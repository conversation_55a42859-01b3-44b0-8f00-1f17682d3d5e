package ins.platform;

import ins.framework.web.exception.DefaultExceptionHelper;
import ins.framework.web.i18n.LocaleMessageService;
import ins.platform.advice.ApiAdvice;
import ins.platform.advice.ExceptionResolver;
import ins.platform.common.ResponseVo;
import ins.platform.common.ScheduleAspect;
import ins.platform.common.TraceLogAspect;
import ins.platform.filter.LogServletFilter;
import ins.platform.plugin.UpdateTimeInterceptor;
import ins.platform.utils.RedisUtils;
import ins.platform.utils.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.JdbcType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import tk.mybatis.mapper.autoconfigure.ConfigurationCustomizer;

/**
 * Created by sino on 2019/9/4.
 */
@Configuration
@Slf4j
public class FrameworkAutoConfiguration {

    @Value("${application.showExceptionStackTrace:false}")
    private boolean showExceptionStackTrace;

    @Bean
    public SpringContextUtils springContextUtils() {
        return new SpringContextUtils();
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate4StrKey(@Autowired RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<String, Object>();
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }

    @Bean
    public RedisUtils redisUtils() {
        return new RedisUtils();
    }

    @Bean
    public ScheduleAspect scheduleAspect() {
        return new ScheduleAspect();
    }

    @Bean
    public TraceLogAspect traceLogAspect() {
        return new TraceLogAspect();
    }

    @Bean
    public LogServletFilter logServletFilter() {
        return new LogServletFilter();
    }

    /*@Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return (configuration) -> configuration.addInterceptor(new UpdateTimeInterceptor());
    }*/

    @Bean
    public UpdateTimeInterceptor updateTimeInterceptor() {
        return new UpdateTimeInterceptor();
    }

    @Bean("payExceptionResolver")
    public ExceptionResolver condition(LocaleMessageService localeMessageService) {
        return (ex) -> {
            int status = ResponseVo.FAIL;
            String messageKey = ResponseVo.FAIL_TEXT;
            if (ex instanceof BindException) {
                status = -3;
                messageKey = ((BindException)ex).getFieldError().getDefaultMessage();
            } else if (ex instanceof MethodArgumentNotValidException) {
                status = -3;
                messageKey = ((MethodArgumentNotValidException)ex).getBindingResult().getFieldError().getDefaultMessage();
            } else {
                status = DefaultExceptionHelper.processStatusCode(ex);
                messageKey = DefaultExceptionHelper.processMessageKey(ex);
            }
            StringBuilder message = new StringBuilder();
            message.append(messageKey);
            try {
                String messageText = localeMessageService.getMessage(messageKey);
                if (StringUtils.isNotBlank(messageText)) {
                    message.append(":").append(messageText);
                }
            } catch (Exception var7) {
                log.error("{}", var7);
            }
            Object data = null;
            if (showExceptionStackTrace) {
                data = DefaultExceptionHelper.processData(ex);
            }
            return new ResponseVo(status, message.toString(), data);
        };
    }

    @Bean
    public ApiAdvice apiAdvice() {
        return new ApiAdvice();
    }


    @Bean
    public ConfigurationCustomizer configurationCustomizer(){
        return new MybatisPlusCustomizers();
    }

    class MybatisPlusCustomizers implements ConfigurationCustomizer {
        @Override
        public void customize(org.apache.ibatis.session.Configuration configuration) {
            configuration.setJdbcTypeForNull(JdbcType.NULL);
        }
    }
}
