package ins.platform.common;

import org.springframework.scheduling.annotation.Scheduled;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 与{@link Scheduled}标注在同一个方法上，表示不控制集群各节点同时分别执行（即不取同步锁）
 */
@Retention(RUNTIME)
@Target(METHOD)
public @interface AllowConcurrentExecution {
}
