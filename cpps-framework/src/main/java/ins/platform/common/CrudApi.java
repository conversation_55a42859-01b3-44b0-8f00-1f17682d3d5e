package ins.platform.common;

import java.io.Serializable;
import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import ins.framework.common.ResultPage;
import io.swagger.annotations.ApiOperation;

/**
 * 提供基于Crud的API实现.<br>
 * 需要子类实现getCrudService()方法
 * 
 * <AUTHOR>
 *
 * @param <V> VO类
 * @param <I> ID类
 */
public interface CrudApi<V extends Serializable, I extends Serializable> {

	public abstract CrudService<V, I> getCrudService();

	@ApiOperation(value = "创建对象")
	@PostMapping(value = "/")
	public default ResponseVo<I> create(@RequestBody V vo) {
		I id = getCrudService().create(vo);
		return ResponseVo.ok(id);
	}

	@ApiOperation(value = "更新对象")
	@PutMapping(value = "/")
	public default ResponseVo<Integer> update(@RequestBody V vo) {
		Integer count = getCrudService().update(vo);
		return ResponseVo.ok(count);
	}

	@ApiOperation(value = "查询对象")
	@GetMapping(value = "/{id}")
	public default ResponseVo<V> select(@PathVariable(value = "id") I id) {
		V result = getCrudService().selectByPrimaryKey(id);
		return ResponseVo.ok(result);
	}

	@ApiOperation(value = "删除对象")
	@DeleteMapping(value = "/{ids}")
	public default ResponseVo<String> delete(@PathVariable(value = "ids") List<I> ids) {
		getCrudService().delete(ids);
		return ResponseVo.ok("");
	}

	@ApiOperation(value = "查找对象")
	@PostMapping(value = "/_search")
	public default ResponseVo<PageResult<V>> search(@ModelAttribute V organizationVo) {
		PageResult<V> result = getCrudService().search(organizationVo);
		return ResponseVo.ok(result);
	}

}
