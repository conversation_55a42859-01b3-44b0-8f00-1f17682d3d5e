package ins.platform.common;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created with IDEA
 * Author:sino
 * Description:
 * Date:2019-11-26
 * Times:14:07
 */
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface IntegerFormat {
    String message() default "数量值错误，数量值应为正整数";

    String valueType() default "BigDecimal";
}
