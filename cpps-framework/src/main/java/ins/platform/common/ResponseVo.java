package ins.platform.common;

import ins.framework.lang.Springs;
import ins.platform.advice.ExceptionResolver;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * Created by sino on 2019/9/9.
 */
@ApiModel("API基础响应对象")
public class ResponseVo<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final int SUCCESS = 0;
    public static final int FAIL = -1;
    public static final int BUSY = -100;
    public static final String SUCCESS_TEXT = "Success";
    public static final String FAIL_TEXT = "Fail";
    public static final String BUSY_TEXT = "Busy";

    @ApiModelProperty("响应状态")
    private int status;
    @ApiModelProperty("状态描述")
    private String statusText;
    @ApiModelProperty("返回数据")
    private T data;

    public ResponseVo() {
        this.status = SUCCESS;
        this.statusText = SUCCESS_TEXT;
    }

    public ResponseVo(int status, String statusText, T data) {
        this.status = status;
        this.statusText = statusText;
        this.data = data;
    }

    public ResponseVo(T data) {
        if(data instanceof Exception) {
            Exception ex = (Exception)data;
            ExceptionResolver resolver = Springs.getBean(ExceptionResolver.class);
            if(resolver == null) {
                this.status = FAIL;
                this.statusText = ex.getLocalizedMessage();
            } else {
                ResponseVo<Object> result = resolver.processException(ex);
                this.status = result.getStatus();
                this.statusText = result.getStatusText();
            }
        } else {
            this.status = SUCCESS;
            this.statusText = SUCCESS_TEXT;
            this.data = data;
        }
    }

    public int getStatus() {
        return this.status;
    }

    public void setStatus(int status) {
        this.status = status;
        if(status == SUCCESS) {
            this.statusText = SUCCESS_TEXT;
        } else if(status == BUSY) {
            this.statusText = BUSY_TEXT;
        } else {
            this.statusText = "";
        }
    }

    public String getStatusText() {
        return this.statusText;
    }

    public void setStatusText(String statusText) {
        this.statusText = statusText;
    }

    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <M> ResponseVo<M> ok() {
        return new ResponseVo();
    }

    public static <T> ResponseVo<T> ok(T data) {
        return new ResponseVo(data);
    }

}
