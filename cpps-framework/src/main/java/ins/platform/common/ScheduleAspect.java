package ins.platform.common;

import ins.platform.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

@Slf4j
@Aspect
public class ScheduleAspect {

	/** 不允许并行的任务，要设置MDC值并Redis取锁 */
	@Around("@annotation(org.springframework.scheduling.annotation.Scheduled) && !@annotation(ins.platform.common.AllowConcurrentExecution)")
	public void aroundScheduled(ProceedingJoinPoint joinPoint) throws Throwable {
		String signature = StringUtils.deleteWhitespace(joinPoint.getSignature().toString());
		String lockKey = "scheduledAspect_" + signature;
		log.debug("getting redis lock for scheduled method call: {}", lockKey);
		RedisUtils.RedisLock lock = RedisUtils.lockNoWait(lockKey, 600000);
		if (lock != null) {
			log.debug("get redis lock succeed for {}", lockKey);
			log.info("[scheduled-start][{}]", signature);
			try {
				joinPoint.proceed();
				log.info("[scheduled-end][{}]", signature);
			} catch (Throwable t) {
				log.error("[scheduled-error][{}]", signature, t);
				throw t;
			} finally {
				log.debug("execution finished, releasing redis lock {}", lockKey);
				lock.unlock();
			}
		} else {
			log.debug("get redis lock failed for {}, ignore execution", lockKey);
		}
	}

}
