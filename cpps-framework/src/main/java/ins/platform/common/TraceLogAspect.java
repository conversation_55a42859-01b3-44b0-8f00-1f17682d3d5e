package ins.platform.common;

import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

import ins.framework.log.AbstractTraceLogAspect;

/**
 * 默认的方法日志拦截器
 * <AUTHOR>
 * 
 */ 
@Aspect
public class TraceLogAspect extends AbstractTraceLogAspect{
	
	@Pointcut("execution(public * ins.pay.robot..*.*(..))")
	public void traceLogPointcut() {
		//本方法的注解表明了拦截的类和方法（用于调试日志TraceLog）
	}
}
