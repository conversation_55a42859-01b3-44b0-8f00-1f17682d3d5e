package ins.platform.filter;

import org.springframework.util.StreamUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;

public class LogHttpServletRequestWrapper extends HttpServletRequestWrapper {

	private final byte[] body;

	public LogHttpServletRequestWrapper(HttpServletRequest request) throws IOException  {
		super(request);
		body = StreamUtils.copyToByteArray(request.getInputStream());
	}

	@Override
	public ServletInputStream getInputStream() throws IOException {
		final ByteArrayInputStream bais = new ByteArrayInputStream(body);
		return new LogServletInputStream(new ServletInputStream() {
			@Override
			public int read() throws IOException {
				return bais.read();
			}

			@Override
			public boolean isFinished() {
				return false;
			}

			@Override
			public boolean isReady() {
				return false;
			}

			@Override
			public void setReadListener(ReadListener readListener) {

			}
		});
	}

	private class LogServletInputStream extends ServletInputStream {
		private ServletInputStream delegate;
		private ByteArrayOutputStream buffer = new ByteArrayOutputStream();

		protected LogServletInputStream(ServletInputStream delegate) {
			this.delegate = delegate;
		}

		@Override
		public int read() throws IOException {
			int b = delegate.read();
			if (b >= 0) {
				buffer.write(b);
			}
			return b;
		}

		@Override
		public int read(byte[] b) throws IOException {
			int readLen = delegate.read(b);
			if (readLen > 0) {
				buffer.write(b, 0, readLen);
			}
			return readLen;
		}

		@Override
		public int read(byte[] b, int off, int len) throws IOException {
			int readLen = delegate.read(b, off, len);
			if (readLen > 0) {
				buffer.write(b, off, readLen);
			}
			return readLen;
		}

		@Override
		public int readLine(byte[] b, int off, int len) throws IOException {
			int readLen = delegate.read(b, off, len);
			if (readLen > 0) {
				buffer.write(b, off, readLen);
			}
			return readLen;
		}

		@Override
		public void close() throws IOException {
			delegate.close();
			ins.platform.filter.LogServletFilter.log.info("[http-request-body][body={}]", new String(buffer.toByteArray(), "UTF-8"));

		}

		@Override
		public boolean isFinished() {
			return delegate.isFinished();
		}

		@Override
		public boolean isReady() {
			return delegate.isReady();
		}

		@Override
		public void setReadListener(ReadListener listener) {
			delegate.setReadListener(listener);
		}

		@Override
		public long skip(long n) throws IOException {
			return delegate.skip(n);
		}

		@Override
		public int available() throws IOException {
			return delegate.available();
		}

	}


}
