package ins.platform.filter;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

public class LogHttpServletResponseWrapper extends HttpServletResponseWrapper {

	private ByteArrayOutputStream buffer = new ByteArrayOutputStream();

	public LogHttpServletResponseWrapper(HttpServletResponse response) {
		super(response);
	}

	public byte[] getResponseBody() {
		return buffer.toByteArray();
	}

	@Override
	public ServletOutputStream getOutputStream() throws IOException {
		return new LogServletOutputStream(super.getOutputStream());
	}

	private class LogServletOutputStream extends ServletOutputStream {

		private ServletOutputStream delegate;

		public LogServletOutputStream(ServletOutputStream delegate) {
			this.delegate = delegate;
		}

		@Override
		public boolean isReady() {
			return delegate.isReady();
		}

		@Override
		public void setWriteListener(WriteListener listener) {
			delegate.setWriteListener(listener);
		}

		@Override
		public void write(int b) throws IOException {
			delegate.write(b);
			buffer.write(b);
		}

		@Override
		public void write(byte[] b) throws IOException {
			delegate.write(b);
			buffer.write(b);
		}

		@Override
		public void write(byte[] b, int off, int len) throws IOException {
			delegate.write(b, off, len);
			buffer.write(b, off, len);
		}

		@Override
		public void flush() throws IOException {
			delegate.flush();

		}

		@Override
		public void close() throws IOException {
			delegate.close();
		}

	}

}
