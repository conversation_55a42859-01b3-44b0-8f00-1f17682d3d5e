package ins.platform.filter;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class LogServletFilter implements Filter {

	protected static final Logger log = LoggerFactory.getLogger(LogServletFilter.class);

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		// do nothing
	}

	@Override
	public void destroy() {
		// do nothing
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		if (!(request instanceof HttpServletRequest)) {
			chain.doFilter(request, response);
			return;
		}
		HttpServletRequest req = (HttpServletRequest) request;
		HttpServletResponse resp = (HttpServletResponse) response;

		// 记录报文日志
		HttpServletRequest requestForChain = req; // 请求需要记录时才包装
		LogHttpServletResponseWrapper responseForChain = new LogHttpServletResponseWrapper(resp);
		String requestUrl = req.getRequestURL().toString();
		String queryStr = req.getQueryString();
		String method = req.getMethod();
		StringBuilder fullUrl = new StringBuilder();
		fullUrl.append(requestUrl);
		if (("GET".equals(method) || "DELETE".equals(method)) && StringUtils.isNotBlank(queryStr)) {
			fullUrl.append("?").append(queryStr);
		}
		String contentType = req.getContentType();
		log.info("[http-request][url={}][method={}]", fullUrl.toString(), method);
		if (("POST".equalsIgnoreCase(method) || "PUT".equals(method))
				&& (StringUtils.isBlank(contentType) || !contentType.toLowerCase().contains("multipart"))) {
			// POST和PUT类型的需要记录请求报文体，等读取body完成时再记录
			requestForChain = new LogHttpServletRequestWrapper(req);
		}
		try {
			chain.doFilter(requestForChain, responseForChain);
			// 记录响应体
			String respContentType = responseForChain.getContentType();
			if (StringUtils.isBlank(respContentType) || respContentType.contains("text")
					|| respContentType.contains("json")) {
				LogServletFilter.log.info("[http-response][contentType={}][body={}]", contentType,
						new String(responseForChain.getResponseBody(), "UTF-8"));
			} else {
				LogServletFilter.log.info("[http-response][contentType={}][body not logged]");
			}
		} catch (Exception e) {
			log.error("[http-error]", e);
			throw e;
		}

	}

}
