package ins.platform.plugin;


import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import ins.platform.common.DateFormat;
import org.apache.commons.lang3.StringUtils;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 解决@JsonFormat注解在把String转化成Date时不会进行格式校验(它会进行字符串截取)问题
 * 该类需要搭配@JsonDeserialize注解以及ins.platform.common.DateFormat注解使用
 */
public class DateFormatSerializer extends JsonDeserializer<Date> implements ContextualDeserializer {

    private DateFormat dateFormat;

    public DateFormatSerializer() {
    }

    public DateFormatSerializer(DateFormat dateFormat) {
        this.dateFormat = dateFormat;
    }

    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
        //获取报文里面属性值
        String data = jsonParser.getText();
        //获取要检验的格式
        String format = dateFormat.format();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        Date result = null;
        String validateFormat;
        try {
            result = simpleDateFormat.parse(data);
            validateFormat = simpleDateFormat.format(result);
        } catch (Exception e) {
            throw new JsonParseException(jsonParser,dateFormat.message());
        }
        //simpleDateFormat的parse默认是弱校验的,需要把由string转成的date再转成string校验一次
        //由于20119-MM-dd会通过校验……增加长度对比
        if ((!StringUtils.equals(data,validateFormat))||(data.length() != format.length())){
            throw new JsonParseException(jsonParser,dateFormat.message());
        }
        return result;
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext deserializationContext, BeanProperty beanProperty) throws JsonMappingException {
        dateFormat = beanProperty.getAnnotation(DateFormat.class);
        return new DateFormatSerializer(dateFormat);
    }
}
