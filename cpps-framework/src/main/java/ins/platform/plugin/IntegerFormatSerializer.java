package ins.platform.plugin;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import ins.platform.common.IntegerFormat;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * Created with IDEA
 * Author:sino
 * Description:
 * Date:2019-11-26
 * Times:11:23
 */
public class IntegerFormatSerializer extends JsonDeserializer<Object> implements ContextualDeserializer {


    private IntegerFormat integerFormat;

    public IntegerFormatSerializer() {
    }

    public IntegerFormatSerializer(IntegerFormat integerFormat) {
        this.integerFormat = integerFormat;
    }

    @Override
    public Object deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
        //获取报文里面属性值
        String data = jsonParser.getText();
        //获取要检验的格式
        Integer result = null;
       try{
           result = Integer.parseInt(data);
       }catch (Exception e){
           throw new JsonParseException(jsonParser,integerFormat.message());
       }
       if(result <= 0){
           throw new JsonParseException(jsonParser, integerFormat.message());
       }
       if("Integer".equals(integerFormat.valueType())){
            return result;
       }else {
           BigDecimal resultEnd = new BigDecimal(result);
           return resultEnd;
       }
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext deserializationContext, BeanProperty beanProperty) throws JsonMappingException {
        integerFormat = beanProperty.getAnnotation(IntegerFormat.class);
        return new IntegerFormatSerializer(integerFormat);
    }
}
