package ins.platform.utils;

import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.impl.DefaultMapperFactory;
import ma.glasnost.orika.metadata.ClassMapBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 对象深层复制工具
 */
public class BeanCopyUtils {

    private static MapperFacade mapper;
    private static MapperFacade notNullMapper;

    /**
     * 默认字段实例集合
     */
    private static Map<String, MapperFacade> CACHE_MAPPER_FACADE_MAP = new ConcurrentHashMap<>();

    static {
        MapperFactory mapperFactory = new DefaultMapperFactory.Builder().build();
        mapper = mapperFactory.getMapperFacade();
        MapperFactory notNullMapperFactory = new DefaultMapperFactory.Builder().mapNulls(false).build();
        notNullMapper = notNullMapperFactory.getMapperFacade();
    }

    /**
     * 复制对象所有属性
     *
     * @param source      源对象
     * @param destination 目标对象
     */
    public static void copy(Object source, Object destination) {
        mapper.map(source, destination);
    }

    /**
     * 复制对象（自定义配置）
     *
     * @param toClass   映射类对象
     * @param data      数据（对象）
     * @param configMap 自定义配置
     * @return 映射类对象
     */
    public static <E, T> E copy(Class<E> toClass, T data, Map<String, String> configMap) {
        MapperFacade mapperFacade = getMapperFacade(toClass, data.getClass(), configMap);
        return mapperFacade.map(data, toClass);
    }

    /**
     * 获取自定义映射
     *
     * @param toClass   映射类
     * @param dataClass 数据映射类
     * @param configMap 自定义配置
     * @return 映射类对象
     */
    private static <E, T> MapperFacade getMapperFacade(Class<E> toClass, Class<T> dataClass, Map<String, String> configMap) {
        String mapKey = dataClass.getCanonicalName() + "_" + toClass.getCanonicalName();
        MapperFacade mapperFacade = CACHE_MAPPER_FACADE_MAP.get(mapKey);
        if (Objects.isNull(mapperFacade)) {
            MapperFactory factory = new DefaultMapperFactory.Builder().build();
            ClassMapBuilder classMapBuilder = factory.classMap(dataClass, toClass);
            configMap.forEach(classMapBuilder::field);
            classMapBuilder.byDefault().register();
            mapperFacade = factory.getMapperFacade();
            CACHE_MAPPER_FACADE_MAP.put(mapKey, mapperFacade);
        }
        return mapperFacade;
    }

    /**
     * 复制对象非null属性
     *
     * @param source      源对象
     * @param destination 目标对象
     */
    public static void copyNotNull(Object source, Object destination) {
        notNullMapper.map(source, destination);
    }

    /**
     * 深度复制对象
     *
     * @param source           源对象
     * @param destinationClass 目标类型
     * @return 复制出的目标对象
     */
    public static <T> T clone(Object source, Class<T> destinationClass) {
        return mapper.map(source, destinationClass);
    }

    /**
     * 复制List
     *
     * @param sourceList       源List
     * @param destinationClass 目标List的元素类型
     * @return 复制出的目标List
     */
    public static <T> List<T> cloneList(List<?> sourceList, Class<T> destinationClass) {
        List<T> destList = new ArrayList<>(sourceList.size());
        for (Object source : sourceList) {
            T destination = clone(source, destinationClass);
            destList.add(destination);
        }
        return destList;
    }

    private BeanCopyUtils() {
    }

}
