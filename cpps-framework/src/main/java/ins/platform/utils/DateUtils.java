package ins.platform.utils;

import ins.framework.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

@Slf4j
public class DateUtils {

    public static final String PATTERN_DATE = "yyyy-MM-dd";
    public static final String PATTERN_DATE_COMPACT = "yyyyMMdd";
    public static final String PATTERN_DATE_TIME = "yyyy-MM-dd HH:mm:ss";
    public static final String PATTERN_DATE_TIME_COMPACT = "yyyyMMddHHmmss";
    // ADD BY LIYUNZHOU BEGIN
    // 京东代付返回的是cst格式日期
    private static final String PATTERN_DATE_CST_UK = "EEE MMM dd HH:mm:ss Z yyyy";
    // 防止返回中文类型的格式
    private static final String PATTERN_DATE_CST_CN = "yyyy'年' MM'月' dd'日' EEE HH:mm:ss Z";
    // ADD BY LIYUNZHOU END

    public static Date parseDate(String str) {
        return parse(str, PATTERN_DATE);
    }

    public static Date parseDateCompact(String str) {
        return parse(str, PATTERN_DATE_COMPACT);
    }

    public static Date parseDatetime(String str) {
        return parse(str, PATTERN_DATE_TIME);
    }

    public static Date parseDatetimeCompact(String str) {
        return parse(str, PATTERN_DATE_TIME_COMPACT);
    }

    public static Date parse(String str, String pattern) {
        Date result;
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            result = sdf.parse(str);
        }catch (Exception e){
            log.error("日期格式解析错误：{}",e);
            throw new BusinessException("日期格式解析错误！");
        }
        return result;
    }

    public static String formatDate(Date date) {
        return format(date, PATTERN_DATE);
    }

    public static String formatDateCompact(Date date) {
        return format(date, PATTERN_DATE_COMPACT);
    }

    public static String formatDatetime(Date date) {
        return format(date, PATTERN_DATE_TIME);
    }

    public static String formatDatetimeCompact(Date date) {
        return format(date, PATTERN_DATE_TIME_COMPACT);
    }

    public static String format(Date date, String pattern) {
        String result;
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            result = sdf.format(date);
        }catch (Exception e){
            log.error("日期格式解析错误：{}",e);
            throw new BusinessException("日期格式解析错误！");
        }
        return result;
    }

    /**
     * 获取第一天日期
     * 1:获取当月第一天的日期
     * 2:获取当年第一天的数据
     * 其他：返回当前时间
     */
    public static Date getFirstDate(int type){
        Date result = new Date();
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH)+1;

        switch (type){
            case 1:
                result = parseDate(year+"-"+formatNumber(month,2)+"-01");
                break;
            case 2:
                result = parseDate(year+"-01-01");
                break;
            default:
                new Date();
        }
        return result;
    }

    /**
     * 将数字前面补0，
     * @param number
     * @param len 想要得到结果长度
     * @return
     */
    private static String formatNumber(int number, int len){
        String result = number+"";
        if(len <= result.length() ){
            return result;
        }else {
            for (int i = 0; i < len - result.length(); i++) {
                result = "0" + result;
            }
        }
        return result;
    }
    
    /**
     * Add by LiyunZhou
     * 京东代付返回的是cst格式的日期格式需要转换类型
     * 将CST格式的日期转换为标准的Date
     * @param cstDateStr
     * @return
     */
	public static Date parseCSTDate(String cstDateStr) {
		try {
			SimpleDateFormat sdf_uk = new SimpleDateFormat(PATTERN_DATE_CST_UK, Locale.UK);
			return sdf_uk.parse(cstDateStr);
		} catch (ParseException e) {
			SimpleDateFormat sdf_cn = new SimpleDateFormat(PATTERN_DATE_CST_CN, Locale.CHINA);
			try {
				return sdf_cn.parse(cstDateStr);
			} catch (ParseException e1) {
				System.out.println("CST日期格式转换出错，原因：" + e.getMessage());
				return null;
			}
		}
	}
}
