package ins.platform.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 用户信息脱敏工具类
 */
public class DesensitizeUtils {

    /**
     * 姓名脱敏
     *
     * @param name 姓名
     * @return 脱敏后姓名
     */
    public static String desensitizeName(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }
        // 目前系统中应该都是中文姓名，名字是两个字的，后面那个变成*，多于两个字的，首尾两字保留中间变*
        if (name.length() > 2) {
            return name.charAt(0) + StringUtils.repeat('*', name.length() - 2) + name.charAt(name.length() - 1);
        } else if (name.length() == 2) {
            return name.charAt(0) + "*";
        } else {
            return name;
        }
    }

    /**
     * 身份证号脱敏
     *
     * @param idCard 身份证号
     * @return 脱敏后身份证号
     */
    public static String desensitizeIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return "";
        }
        if (idCard.length() != 18) {
            throw new IllegalArgumentException("需要脱敏的身份证号必须是18位");
        }
        return idCard.substring(0, 3) + "****" + idCard.substring(14);
    }

    /**
     * 手机号脱敏
     *
     * @param mobile 手机号
     * @return 脱敏后手机号
     */
    public static String desensitizeMobile(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return "";
        }
        if (mobile.length() != 11) {
            throw new IllegalArgumentException("需要脱敏的手机号必须是11位");
        }
        return mobile.substring(0, 3) + "****" + mobile.substring(7);
    }

    /**
     * 证件号码脱敏
     *
     * @param idNo 证件号码
     * @return 脱敏后证件号码
     */
    public static String desensitizeIdNo(String idNo) {
        char[] chars = idNo.toCharArray();
        String no = idNo;
        if (StringUtils.isBlank(idNo)) {
            return "";
        } else if (idNo.length() < 5) {
            no = idNo;
        } else if ((5 <= idNo.length()) && (idNo.length() <= 8)){
            for (int i = 0; i < chars.length; i++) {
                if (i > 2 ) {
                    chars[i] = '*';
                }
            }
            String myCustNo = String.valueOf(chars);
            no=myCustNo;
        }else{
            for (int i = 0; i < chars.length; i++) {
                if (i > 3 && i< chars.length-(chars.length-8)) {
                    chars[i] = '*';
                }
            }
            String myCustNo = String.valueOf(chars);
            no=myCustNo;
        }
        return no;
    }

    /**
     * 证件号码脱敏
     *
     * @param idNo 证件号码
     * @return 脱敏后证件号码
     */
    public static String desensitizeIdNoMessages(String idNo) {
        String no = idNo;
        if (StringUtils.isBlank(idNo)) {
            return "";
        } else if (idNo.length() < 5) {
            no = idNo;
        }else{
            no = no.substring(0,no.length()-4)+"****";
        }
        return no;
    }

    private DesensitizeUtils() {
    }

}
