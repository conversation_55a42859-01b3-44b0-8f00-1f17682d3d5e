package ins.platform.utils;

import java.io.*;

public class FileUtils {
    /**
     * 将原来的文件覆盖掉
     *
     * @param content
     * @param path
     * @throws IOException
     */
    public static void replaceFile(String content, String path) throws IOException {
        File file = new File(path);
        if (file.exists()) {
            FileWriter fw = new FileWriter(file);
            fw.write(content);
            fw.flush();
            fw.close();
        }
    }

    public static void appendContext(String content, String path){
        File file = new File(path);
        FileOutputStream fos = null;
        OutputStreamWriter osw = null;
        try {
            if (!file.exists()) {
                createParentFile(file.getParentFile());
                file.createNewFile();//如果文件不存在，就创建该文件
                fos = new FileOutputStream(file);//首次写入获取
            } else {
                //如果文件已存在，那么就在文件末尾追加写入
                fos = new FileOutputStream(file, true);//这里构造方法多了一个参数true,表示在文件末尾追加写入
            }

            osw = new OutputStreamWriter(fos, "UTF-8");//指定以UTF-8格式写入文件
            osw.write(content);
            osw.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                fos.close();
            }catch (Exception e){
                e.printStackTrace();
            }
            try {
                osw.close();
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

    /**
     * 向上一层一层的创建文件夹
     */
    public static void createParentFile(File file){
        if(!file.exists()){
            createParentFile(file.getParentFile());
            file.mkdirs();
        }
    }

    /**
     * 向下及联的删除文件
     */
    public static boolean deleteFileCascade(String path){
        File file = new File(path);
        if (!file.exists()) {
            return false;
        }

        if (file.isFile()) {
            return file.delete();
        } else {
            File[] files = file.listFiles();
            for (File f : files) {
                deleteFileCascade(f.getAbsolutePath());
            }
            return file.delete();
        }
    }
}
