package ins.platform.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 身份证号解析的工具类
 * <p>
 * 可用于从身份证号中提取省份（因市/区码表较大暂不包含）、生日、性别等信息，并校验身份证格式是否正确。
 */
public class IDCard {

	private static final Map<String, String> provinceMap = new LinkedHashMap<>();
	private static final char[] verifyCodes = { '1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2' };
	private static final int[] weight = { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 };

	static {
		provinceMap.put("11", "北京");
		provinceMap.put("12", "天津");
		provinceMap.put("13", "河北");
		provinceMap.put("14", "山西");
		provinceMap.put("15", "内蒙古");
		provinceMap.put("21", "辽宁");
		provinceMap.put("22", "吉林");
		provinceMap.put("23", "黑龙江");
		provinceMap.put("31", "上海");
		provinceMap.put("32", "江苏");
		provinceMap.put("33", "浙江");
		provinceMap.put("34", "安徽");
		provinceMap.put("35", "福建");
		provinceMap.put("36", "江西");
		provinceMap.put("37", "山东");
		provinceMap.put("41", "河南");
		provinceMap.put("42", "湖北");
		provinceMap.put("43", "湖南");
		provinceMap.put("44", "广东");
		provinceMap.put("45", "广西");
		provinceMap.put("46", "海南");
		provinceMap.put("50", "重庆");
		provinceMap.put("51", "四川");
		provinceMap.put("52", "贵州");
		provinceMap.put("53", "云南");
		provinceMap.put("54", "西藏");
		provinceMap.put("61", "陕西");
		provinceMap.put("62", "甘肃");
		provinceMap.put("63", "青海");
		provinceMap.put("64", "宁夏");
		provinceMap.put("65", "新疆");
		provinceMap.put("71", "台湾");
		provinceMap.put("81", "香港");
		provinceMap.put("82", "澳门");
		provinceMap.put("91", "国外");
	}

	/** 身份证号 */
	private String cardNo;
	/** 省份 */
	private String provinceName;
	/** 出生日期 */
	private Date birthday;
	/** 性别 0-女，1-男 */
	private String gender;
	/** 解析错误时存储错误信息 */
	private String errorMessage;

	/**
	 * 创建身份证号解析对象
	 * <p>
	 * 如果有解析问题，不直接抛出异常，但错误信息会保留在errorMessage属性中供后续获取
	 * 
	 * @param cardNo 身份证号码
	 */
	public IDCard(String cardNo) {
		cardNo = StringUtils.trim(cardNo).toUpperCase();
		this.cardNo = cardNo;
		String cardNo17 = null;
		if (cardNo.length() == 15) {
			if (StringUtils.isNumeric(cardNo)) {
				this.errorMessage = "身份证号码应都为数字";
				return;
			} else {
				cardNo17 = cardNo.substring(0, 6) + "19" + cardNo.substring(6);
			}
		} else if (cardNo.length() == 18) {
			cardNo17 = cardNo.substring(0, 17);
			if (!StringUtils.isNumeric(cardNo17)) {
				this.errorMessage = "身份证号码除最后一位以外应都为数字";
				return;
			}
		} else {
			this.errorMessage = "身份证号长度应为15位或18位";
			return;
		}
		// 省份
		this.provinceName = provinceMap.get(cardNo17.substring(0, 2));
		if (StringUtils.isBlank(provinceName)) {
			this.errorMessage = "身份证号地区编码错误";
		}
		// 生日
		try {
			this.birthday = DateUtils.parseDateStrictly(cardNo17.substring(6, 14), "yyyyMMdd");
		} catch (ParseException e) {
			this.errorMessage = "身份证号日期格式错误";
			return;
		}
		// 性别
		int genderNum = Integer.parseInt(cardNo17.substring(16, 17));
		this.gender = genderNum % 2 == 0 ? "0" : "1";
		// 校验码
		if (cardNo.length() == 18) {
			int total = 0;
			for (int i = 0; i < 17; i++) {
				int num = Integer.parseInt(String.valueOf(cardNo17.charAt(i)));
				total += num * weight[i];
			}
			int mod = total % 11;
			char expectedCheckCode = verifyCodes[mod];
			if (expectedCheckCode != cardNo.charAt(17)) {
				this.errorMessage = "身份证号校验码不正确";
				return;
			}
		}
	}

	/** 获取身份证号 */
	public String getCardNo() {
		return this.cardNo;
	}

	/** 获取省份名称 */
	public String getProvinceName() {
		return this.provinceName;
	}

	/** 获取出生日期 */
	public Date getBirthday() {
		return this.birthday;
	}

	/**
	 * 获取年龄（相对某个时间点）
	 * 
	 * @param date 相对的时间点
	 * @return 年龄
	 */
	public int getAge(Date date) {
		if (this.birthday == null) {
			return -1;
		}
		Calendar c1 = DateUtils.toCalendar(birthday);
		Calendar c2 = DateUtils.toCalendar(date);
		int y1 = c1.get(Calendar.YEAR);
		int y2 = c2.get(Calendar.YEAR);
		int age = y2 - y1;
		c1.add(Calendar.YEAR, age);
		if (c1.after(c2)) {
			age--;
		}
		return age;
	}

	/**
	 * 获取年龄（相对系统当前时间）
	 * 
	 * @return 年龄
	 */
	public int getAge() {
		return getAge(new Date());
	}

	/** 获取性别：0-女，1-男 */
	public String getGender() {
		return this.gender;
	}

	/** 是否有解析错误 */
	public boolean isError() {
		return StringUtils.isNotBlank(errorMessage);
	}

	/** 获取解析错误信息 */
	public String getErrorMessage() {
		return this.errorMessage;
	}

	@Override
	public String toString() {
		return "IDCard [cardNo=" + cardNo + ", provinceName=" + provinceName + ", birthday="
				+ DateFormatUtils.format(birthday, "yyyy-MM-dd") + ", gender=" + gender + ", errorMessage="
				+ errorMessage + "]";
	}

}
