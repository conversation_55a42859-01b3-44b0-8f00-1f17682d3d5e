package ins.platform.utils;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * 密码处理相关工具类
 */
public class PasswordUtils {

	/**
	 * 对密码进行加密处理，目前用加盐MD5方式
	 * 
	 * @param password 密码
	 * @return MD5后的密码
	 */
	public static String encodePassword(String password) {
		return DigestUtils.md5Hex("pay_" + password);
	}

	private PasswordUtils() {
	}

	public static void main(String[] args) {
		System.out.println(encodePassword("000000"));
	}

}
