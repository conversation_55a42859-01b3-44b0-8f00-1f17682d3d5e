package ins.platform.utils;

import ins.platform.utils.RandomGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisStringCommands.SetOption;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/** Redis简单服务的静态包装 */
public class RedisUtils {

	private static RedisTemplate<String, Object> redisTemplate;

	/**
	 * 获取缓存值
	 * 
	 * @param key 缓存键
	 * @return 缓存值
	 */
	public static Object get(String key) {
		return redisTemplate.opsForValue().get(key);
	}

	/**
	 * 获取缓存自动过期时间
	 * 
	 * @param key 缓存键
	 * @return 剩余毫秒数，另外-2表示不存在，-1表示永远不过期
	 */
	public static Long getExpire(String key) {
		return redisTemplate.getExpire(key, TimeUnit.MILLISECONDS);
	}

	/**
	 * 设置缓存值
	 * 
	 * @param key   缓存键
	 * @param value 缓存值
	 */
	public static void set(String key, Object value) {
		redisTemplate.opsForValue().set(key, value);
	}

	/**
	 * 设置缓存值，带自动失效时间
	 * 
	 * @param key    缓存键
	 * @param value  缓存值
	 * @param expire 自动失效的毫秒数
	 */
	public static void set(String key, Object value, long expire) {
		redisTemplate.opsForValue().set(key, value, expire, TimeUnit.MILLISECONDS);
	}

	/**
	 * 如果key不存在，设置缓存值，带自动失效时间
	 * 
	 * @param key    缓存键
	 * @param value  缓存值
	 * @param expire 自动失效的毫秒数
	 * @return 是否设置成功
	 */
	@SuppressWarnings("unchecked")
	public static boolean setIfAbsent(final String key, final Object value, final long expire) {
		RedisSerializer<Object> keySerializer = (RedisSerializer<Object>) redisTemplate.getKeySerializer();
		RedisSerializer<Object> valueSerializer = (RedisSerializer<Object>) redisTemplate.getValueSerializer();
		byte[] rawKey = keySerializer.serialize(key);
		byte[] rawValue = valueSerializer.serialize(value);
		return redisTemplate.execute(new RedisCallback<Boolean>() {
			@Override
			public Boolean doInRedis(RedisConnection connection) throws DataAccessException {
				connection.set(rawKey, rawValue, Expiration.milliseconds(expire), SetOption.SET_IF_ABSENT);
				byte[] rawValue2 = connection.get(rawKey);
				return Arrays.equals(rawValue, rawValue2);
			}
		});
	}

	/**
	 * 删除缓存
	 * 
	 * @param key 缓存键
	 */
	public static void delete(String key) {
		redisTemplate.delete(key);
	}

	/**
	 * 获取一个同步锁，如果已经被锁住则进行等待
	 * 
	 * @param key    同步锁的缓存键
	 * @param expire 同步锁自动失效的毫秒数
	 * @return 返回RedisLock对象，包含unlock方法用于手动解锁。
	 */
	public static RedisLock lockWait(String key, long expire) {
		return lockWait(key, expire, 0);
	}

	/**
	 * 获取一个同步锁，如果已经被锁住则进行等待
	 * 
	 * @param key         同步锁的缓存键
	 * @param expire      同步锁自动失效的毫秒数
	 * @param maxWaitTime 最长等待时间，如果设置为0或者负数则一直等待
	 * @return 如果获取成功，返回RedisLock对象，包含unlock方法用于手动解锁。如果等待锁超时，返回null
	 */
	public static RedisLock lockWait(String key, long expire, long maxWaitTime) {
		if (expire <= 0) {
			throw new RuntimeException("Redis同步锁自动失效时间(expire)必须为正数");
		}
		String value = RandomGenerator.randomString(8);
		long beginTime = System.currentTimeMillis();
		boolean success = setIfAbsent(key, value, expire);
		while (!success) {
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				break;
			}
			success = setIfAbsent(key, value, expire);
			if (maxWaitTime > 0 && System.currentTimeMillis() - beginTime > maxWaitTime) {
				break;
			}
		}
		return success ? new RedisLock(key, value) : null;
	}

	/**
	 * 获取一个同步锁，如果已经被锁住则直接返回false
	 * 
	 * @param key    同步锁的缓存键
	 * @param expire 同步锁自动失效的毫秒数
	 * @return 如果获取成功，返回RedisLock对象，包含unlock方法用于手动解锁。如果获取失败，返回null
	 */
	public static RedisLock lockNoWait(String key, long expire) {
		if (expire <= 0) {
			throw new RuntimeException("Redis同步锁自动失效时间(expire)必须为正数");
		}
		String value = RandomGenerator.randomString(8);
		boolean success = setIfAbsent(key, value, expire);
		return success ? new RedisLock(key, value) : null;
	}

	/**
	 * 解除同步锁
	 * 
	 * @param lock 锁定时返回的对象
	 */
	public static void unlock(RedisLock lock) {
		lock.unlock();
	}

	@Autowired
	public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate4StrKey) {
		RedisUtils.redisTemplate = redisTemplate4StrKey;
	}

	/**
	 * Redis同步锁对象，包含解锁的方法
	 */
	public static class RedisLock {

		private String key;
		private String value;

		private RedisLock(String key, String value) {
			this.key = key;
			this.value = value;
		}

		public void unlock() {
			if (value.equals(get(key))) {
				delete(key);
			}
		}

	}

}
