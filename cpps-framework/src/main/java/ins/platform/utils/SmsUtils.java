package ins.platform.utils;


import com.github.qcloudsms.SmsMultiSender;
import com.github.qcloudsms.SmsMultiSenderResult;
import com.github.qcloudsms.SmsSingleSender;
import com.github.qcloudsms.SmsSingleSenderResult;

/**
 * 发送短信
 */
public class SmsUtils {

    /**
     * 指定模板ID发送短信
     *
     * @param appid:短信应用SDKAppID,以1400开头
     * @param appkey:短信应用SDKAppKey
     * @param phoneNumbers:需要发送短信的手机号码
     * @param templateId:短信模板ID,需要在短信应用中申请
     * @param smsSign:签名,签名参数使用的是`签名内容`,而不是`签名ID`,需要在短信控制台申请
     * @param params:模版内容参数
     * @param nationCode:国家码或地区码(中国大陆为86)
     * @return 是否发送成功
     * int appid = 1400271624;
     * String appkey = c38fb28b90fd236f2087f4930ea9722e；
     */
    public static boolean senderSms(int appid, String appkey, String[] phoneNumbers, int templateId, String smsSign, String[] params, String nationCode) throws Exception {
        boolean result = false;
        try {
            if (phoneNumbers.length == 1) {
                //单发短信
                SmsSingleSender ssender = new SmsSingleSender(appid, appkey);
                SmsSingleSenderResult senderResult = ssender.sendWithParam(nationCode, phoneNumbers[0],
                        templateId, params, smsSign, "", "");
                if (senderResult != null && senderResult.result == 0) {
                    //0表示成功（计费依据），非0表示失败,错误码请参考:https://cloud.tencent.com/document/product/382/3771
                    result = true;
                }else {
                    throw new Exception("单发短信失败:senderResult:"+senderResult);
                }
            }else {
                //群发短信
                SmsMultiSender msender = new SmsMultiSender(appid, appkey);
                SmsMultiSenderResult senderResult =  msender.sendWithParam(nationCode, phoneNumbers,
                        templateId, params, smsSign, "", "");
                if (senderResult != null && senderResult.result == 0) {
                    //0表示成功（计费依据），非0表示失败,错误码请参考:https://cloud.tencent.com/document/product/382/3771
                    result = true;
                }else {
                    throw new Exception("群发短信失败:senderResult:"+senderResult);
                }
            }
            return result;
        } catch (Exception e) {
            throw e;
        }
    }
}
