package ins.channel.gateway;

import ins.channel.base.service.GgcodeService;
import ins.channel.power.service.PowerService;
import ins.channel.utils.CommonUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import tk.mybatis.spring.annotation.MapperScan;

@SpringBootApplication
@EnableAspectJAutoProxy
@MapperScan({"ins.channel.*.dao"})
@Import({
		PowerService.class, GgcodeService.class, CommonUtil.class,})
public class ChannelGatewayApplication {

	public static void main(String[] args) {
		SpringApplication.run(ChannelGatewayApplication.class, args);
	}

}
