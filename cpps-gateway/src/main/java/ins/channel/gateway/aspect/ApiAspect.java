package ins.channel.gateway.aspect;

import com.alibaba.fastjson.JSON;
import ins.channel.currency.dao.GgcurrencyDao;
import ins.channel.gateway.gpsysteminteractivelog.service.GpsysteminteractivelogService;
import ins.platform.common.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created with IDEA
 * Author:sino
 * Description:
 * Date:2019-09-20
 * Times:16:05
 */
@Aspect
@Component
@Slf4j
public class ApiAspect {

    @Autowired
    private GpsysteminteractivelogService gpsysteminteractivelogService;
    @Autowired
    private GgcurrencyDao ggcurrencyDao;

    //controller 切入点
    @Pointcut(value = "execution(* ins.channel.gateway..*Api.*(..))")
    public void pointCut() {
    }
    //统一异常切入点
    @Pointcut(value = "execution(* ins.platform.advice.ApiAdvice.customErrorHandler(..))")
    public void exceptionPointCut() {
    }

    /**
     * 校验报文里面的币种是否是收付费允许接收的币种
     * @param request
     * @throws Exception
     */
//    @Before(value = "(pointCut()||mqPointCut()) && args(request)")
//    public void Before(Object request) throws Exception {
//        if (request instanceof MessageExt){
//            String messageBody = new String(((MessageExt)request).getBody());
//            request = JSON.parseObject(messageBody, Map.class);
//        }
//        Set<String> resultSet = new HashSet<String>();
//        List<GgcurrencySelect> ggcurrencyList = ggcurrencyDao.currencyForSelect();
//        if (ggcurrencyList.size() <= 0){
//            throw new BusinessException("币别代码表中没有配置币种代码或者没有有效币种，请核实");
//        }
//        List<String> currencyList = ggcurrencyList.stream().map(GgcurrencySelect::getCurrencyCode).collect(Collectors.toList());
//        this.object2Set(resultSet, request,currencyList);
//        if (resultSet.size() > 0) {
//            throw new BusinessException("请求报文:" + JSON.toJSONString(request) + "中包含的币种:" + resultSet + "不在收付费系统允许接收的币种:" + currencyList + "中");
//        }
//    }
    /**
     * Restful接口正常日志入库
     * @param request
     * @param response
     */
    @AfterReturning(value = "pointCut() && args(request)", returning = "response", argNames = "request,response")
    public void afterReturning(Object request, ResponseVo response) {

        gpsysteminteractivelogService.insertGpsysteminteractiveLog(String.valueOf(response.getStatus()), JSON.toJSONString(response), null);
    }

    /**
     * MQ接口正常日志入库
     * @param request
     */
//    @AfterReturning(value = "mqPointCut() && args(request)")
//    public void mqafterReturning(Object request) {
//        gpsysteminteractivelogService.insertGpsysteminteractiveLogMQ((MessageExt)request,false,null);
//    }

    /**
     * Restful接口异常日志入库
     * @param response
     */
    @AfterReturning(value = "exceptionPointCut()", returning = "response")
    public void exceptionAfterReturning(ResponseVo response) {

        gpsysteminteractivelogService.insertGpsysteminteractiveLog(String.valueOf(response.getStatus()), JSON.toJSONString(response), null);
    }
    /**
     * MQ接口异常日志入库
     * @param request
     */
//    @AfterThrowing(pointcut = "mqPointCut()&&args(request)", throwing = "e")
//    public void afterThrowing(Object request, Exception e) {
//        gpsysteminteractivelogService.insertGpsysteminteractiveLogMQ((MessageExt)request,true,e);
//    }

    /**
     * 找出请求体中的不合法的币种
     * @param resultSet
     * @param object
     */
    public void object2Set(Set resultSet, Object object,List<String> currencyList) throws Exception{
        Assert.notNull(object, "在找出请求体中的不合法的币种时转换对象不能为空");
        Assert.notNull(resultSet, "在找出请求体中的不合法的币种时结果Set不能为空");
        if (object instanceof Map){
            //针对MQ消息进行特殊处理
            Map<String,Object> requestMap = (Map)object;
            for (Object key : requestMap.keySet()) {
                Object value = requestMap.get(key);
                if (StringUtils.endsWithIgnoreCase((String)key,"currency") && value != null && !currencyList.contains((String) value)) {
                    resultSet.add(value);
                }
                if (value != null) {
                    if (value instanceof List) {
                        List valueList = (List) value;
                        for (int i = 0; i < valueList.size(); i++) {
                            this.object2Set(resultSet, valueList.get(i),currencyList);
                        }
                    }else {
                        this.object2Set(resultSet, value,currencyList);
                    }
                }
            }
        }else {
            //处理restFul请求
            //获取字段集合
            Field[] declaredFields = object.getClass().getDeclaredFields();
            for (Field field : declaredFields) {
                try {
                    //设置允许访问对象的私有属性
                    field.setAccessible(true);
                    Type genericType = field.getGenericType();
                    String fieldName = field.getName();
                    Object fieldValue = field.get(object);
                    if (StringUtils.endsWithIgnoreCase(fieldName, "currency") && fieldValue != null && !currencyList.contains((String) fieldValue)) {
                        resultSet.add(fieldValue);
                    }
                    if ((genericType.toString().startsWith("java.util.List")
                            || StringUtils.endsWithIgnoreCase(fieldName, "RequestBody")) && fieldValue != null) {
                        if (fieldValue instanceof List) {
                            for (int i = 0; i < ((List) fieldValue).size(); i++) {
                                this.object2Set(resultSet, ((List) fieldValue).get(i), currencyList);
                            }
                        } else {
                            this.object2Set(resultSet, fieldValue, currencyList);
                        }
                    }
                } catch (Exception e) {
                    log.error("在找出请求体中的不合法的币种时发送异常", e);
                    throw e;
                }
            }
        }
    }

//    /**
//     * MQ接口正常日志入库
//     * @param request
//     */
//    @AfterReturning(value = "mqPlatformPointCut() && args(request)")
//    public void mqPlatformAfterReturning(org.apache.rocketmq.common.message.MessageExt request) {
//        gpsysteminteractivelogService.insertLogPlatformMQ(request,false,null);
//    }
//
//    /**
//     * MQ接口异常日志入库
//     * @param request
//     */
//    @AfterThrowing(pointcut = "mqPlatformPointCut() && args(request)", throwing = "e")
//    public void afterPlatformThrowing(org.apache.rocketmq.common.message.MessageExt request, Exception e) {
//        gpsysteminteractivelogService.insertLogPlatformMQ(request,true,e);
//    }
}
