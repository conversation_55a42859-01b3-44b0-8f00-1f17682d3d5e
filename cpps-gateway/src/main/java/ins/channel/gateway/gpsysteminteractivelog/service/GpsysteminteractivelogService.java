package ins.channel.gateway.gpsysteminteractivelog.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.common.message.MessageExt;
import ins.framework.exception.BusinessException;
import ins.channel.common.ThirdPlatformMessagePO;
import ins.channel.company.vo.Company;
import ins.channel.systeminteractivelog.dao.GpsysteminteractivelogDao;
import ins.channel.systeminteractivelog.po.Gpsysteminteractivelog;
import ins.channel.user.vo.User;
import ins.platform.common.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Created with IDEA
 * Author:sino
 * Description:
 * Date:2019-09-20
 * Times:15:39
 */
@Service
@Slf4j
public class GpsysteminteractivelogService {

    @Autowired
    private GpsysteminteractivelogDao gpsysteminteractivelogDao;

    @Autowired
    private HttpServletRequest httpServletRequest;

    /**
     * Restful接口请求体相应情况日志送入数据库
     * @param status
     * @param response
     * @param message
     * @return
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public String insertGpsysteminteractiveLog(String status, String response, ThirdPlatformMessagePO message){

        Gpsysteminteractivelog gpsysteminteractivelog = new Gpsysteminteractivelog();
        String requestId = UUID.randomUUID().toString().substring(0, 30);

        String modelName = httpServletRequest.getRequestURI();
        modelName = modelName.substring(modelName.lastIndexOf("/") + 1);

        String requestMessage = this.truncateString(this.getRequestMessage(httpServletRequest, modelName), 3000);
        String responseMessage = this.truncateString(response.toString(), 3000);
        gpsysteminteractivelog.setModelName(modelName);
        gpsysteminteractivelog.setRequestmessage(requestMessage);
        gpsysteminteractivelog.setResponsemessage(responseMessage);
        gpsysteminteractivelog.setResponsestatus(status);


        gpsysteminteractivelog.setRequestid(requestId);
        if (message != null) {
            gpsysteminteractivelog.setThirdintermessage(this.truncateString(message.getRequest() + message.getReponse(), 4000));
        }
        try {
            gpsysteminteractivelogDao.insertSelective(gpsysteminteractivelog);
        } catch (Exception e) {
            if (log.isInfoEnabled()) {
                log.info(modelName + "请求和返回日志存储异常：", e);
            }
        }
        return requestId;
    }

//    /**
//     * MQ接口请求体相应情况日志送入数据库
//     * @param msg
//     * @param isError
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
//    public void insertGpsysteminteractiveLogMQ(MessageExt msg,Boolean isError,Exception e){
//        Gpsysteminteractivelog gpsysteminteractivelog = new Gpsysteminteractivelog();
//        String requestId = UUID.randomUUID().toString().substring(0, 30);
//        String requestMessage = new String(msg.getBody());
//        ResponseVo responseVo = new ResponseVo();
//        if (isError){
//            responseVo.setStatus(ResponseVo.FAIL);
//            responseVo.setStatusText(ResponseVo.FAIL_TEXT);
//            responseVo.setData("Topic:"+msg.getTopic()+",Tags:"+msg.getTags()+",msgId:"+msg.getMsgId()+"消费消息失败:"+e);
//        } else {
//            responseVo.setStatus(ResponseVo.SUCCESS);
//            responseVo.setStatusText(ResponseVo.SUCCESS_TEXT);
//            responseVo.setData("Topic:"+msg.getTopic()+",Tags:"+msg.getTags()+",msgId:"+msg.getMsgId()+"消费消息成功");
//        }
//        String responseMessage = JSON.toJSONString(responseVo);
//        gpsysteminteractivelog.setModelName(msg.getTopic());
//        gpsysteminteractivelog.setRequestmessage(this.truncateString(requestMessage,3000));
//        gpsysteminteractivelog.setResponsemessage(this.truncateString(responseMessage,3000));
//        gpsysteminteractivelog.setResponsestatus(isError == true ? "-2" : "0");
//        gpsysteminteractivelog.setRequestid(requestId);
//        try {
//            gpsysteminteractivelogDao.insertSelective(gpsysteminteractivelog);
//        } catch (Exception exception) {
//            if (log.isInfoEnabled()) {
//                log.info("Topic:"+msg.getTopic()+",Tags:"+msg.getTags()+"请求和返回日志存储异常：", e);
//            }
//        }
//    }

    private String getRequestMessage(HttpServletRequest httpServletRequest, String modelName) {

        String requestMessage = null;
        try {

            BufferedReader reader = new BufferedReader(new InputStreamReader(httpServletRequest.getInputStream(), "UTF-8"));
            requestMessage = reader.lines().map(String::trim).collect(Collectors.joining());
        } catch (IOException e) {
            if (log.isInfoEnabled()) {
                log.info(e.getMessage(), e);
            }
        }

        if (null == requestMessage) {
            requestMessage = "";
        }
        log.info("请求接口名：" + modelName + " 请求报文：" + requestMessage);
        return requestMessage;
    }

    private String truncateString(String message, int iLength) {

        byte[] jsonStrBytes = message.getBytes();
        if (jsonStrBytes.length > iLength) {
            jsonStrBytes = Arrays.copyOfRange(jsonStrBytes, 0, iLength);
        }

        return new String(jsonStrBytes);
    }

    /**
     * MQ接口请求体相应情况日志送入数据库
     * @param msg
     * @param isError
     * @return
     */
//    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
//    public void insertLogPlatformMQ(org.apache.rocketmq.common.message.MessageExt msg, Boolean isError, Exception e){
//        Gpsysteminteractivelog gpsysteminteractivelog = new Gpsysteminteractivelog();
//        String requestId = UUID.randomUUID().toString().substring(0, 30);
//        String mesType = "";
//        String requestMessage = new String(msg.getBody());
//        ResponseVo responseVo = new ResponseVo();
//        if (isError){
//            responseVo.setStatus(ResponseVo.FAIL);
//            responseVo.setStatusText(ResponseVo.FAIL_TEXT);
//            responseVo.setData("Topic:"+msg.getTopic()+",Tags:"+msg.getTags()+",msgId:"+msg.getMsgId()+"消费消息失败:"+e);
//        } else {
//            responseVo.setStatus(ResponseVo.SUCCESS);
//            responseVo.setStatusText(ResponseVo.SUCCESS_TEXT);
//            responseVo.setData("Topic:"+msg.getTopic()+",Tags:"+msg.getTags()+",msgId:"+msg.getMsgId()+"消费消息成功");
//        }
//        String responseMessage = JSON.toJSONString(responseVo);
//        gpsysteminteractivelog.setModelName(msg.getTopic());
//        gpsysteminteractivelog.setRequestmessage(this.truncateString(requestMessage,3000));
//        gpsysteminteractivelog.setResponsemessage(this.truncateString(responseMessage,3000));
//        gpsysteminteractivelog.setResponsestatus(isError == true ? "-2" : "0");
//        gpsysteminteractivelog.setRequestid(requestId);
//        try {
//            gpsysteminteractivelogDao.insertSelective(gpsysteminteractivelog);
//        } catch (Exception exception) {
//            if (log.isInfoEnabled()) {
//                log.info("同步"+mesType+"请求和返回日志存储异常：", e);
//            }
//        }
//    }

}
