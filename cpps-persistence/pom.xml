<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cpps-parent</artifactId>
        <groupId>com.sinosoft</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cpps-persistence</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <!-- 内部 -->
        <dependency>
            <groupId>com.sinosoft</groupId>
            <artifactId>cpps-framework</artifactId>
        </dependency>

        <!-- 代码生成工具 -->
        <dependency>
            <groupId>ins.framework</groupId>
            <artifactId>ins-framework-mybatis-generator</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 好用的预编译工具lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 生成代码，需要使用oracle的jar包-->
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>********-atlassian-hosted</version>
            <scope>test</scope>
        </dependency>
        <!--自定义jackson的反序列化类需要用到的jar-->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.9.8</version>
        </dependency>


    </dependencies>

</project>