package ins.channel.baseCode.po;

import ins.framework.mybatis.annotations.Column;
import lombok.Data;

import java.io.Serializable;

/**
 * @apiNote 基础码表PO对象
 * @author: sino
 * @date: 2019-11-20
 */
@Data
public class BaseCode implements Serializable {
    @Column(name = "CODE_TYPE", description = "基础代码类型")
    private String codeType;

    @Column(name = "CODE_CODE", description = "基础代码")
    private String codeCode;

    @Column(name = "CODE_NAME", description = "基础代码名称")
    private String codeName;

    private static final long serialVersionUID = 1L;
}
