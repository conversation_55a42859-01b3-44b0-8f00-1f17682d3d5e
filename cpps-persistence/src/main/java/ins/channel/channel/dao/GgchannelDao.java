package ins.channel.channel.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.baseCode.po.BaseCode;
import ins.channel.channel.po.Ggchannel;
import ins.channel.channel.po.GgchannelSelect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * 表GGCHANNEL对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GgchannelDao extends MybatisBaseDao<Ggchannel, String> {

    /**
     * 分页模糊查询
     * @param pageParam
     * @param clone
     * @return
     */
    Page<Ggchannel> pageByCondition(PageParam pageParam, Ggchannel clone);

    /**
     * 供下拉框查询使用
     * @param pageParam
     * @param queryInfo
     * @return
     */
    Page<GgchannelSelect> channelForSelectPage(PageParam pageParam, @Param("queryInfo") String queryInfo);

    Ggchannel translate(String channelCode);
    /**
     * Modify By zhaojie 查询所有码表(供基础码表查询使用) 2019/11/20
     * @return
     */
    List<BaseCode> queryAll();
}
