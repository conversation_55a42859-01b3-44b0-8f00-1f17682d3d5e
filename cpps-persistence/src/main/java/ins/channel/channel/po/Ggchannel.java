package ins.channel.channel.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GGCHANNEL的PO对象<br/>
 * 对应表名：GGCHANNEL,备注：收付机构定义表
 *
 */
@Data
@Table(name = "GGCHANNEL")
public class Ggchannel implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CHANNEL_CODE,备注：渠道大类代码 pk */
	@Column(name = "CHANNEL_CODE", description = "渠道大类代码 pk")
	private String channelCode;
	/** 对应字段：CHANNEL_CNAME,备注：渠道大类简体名称 */
	@Column(name = "CHANNEL_CNAME", description = "渠道大类简体名称")
	private String channelCname;
	/** 对应字段：CHANNEL_TNAME,备注：渠道大类繁体名称 */
	@Column(name = "CHANNEL_TNAME", description = "渠道大类繁体名称")
	private String channelTname;
	/** 对应字段：CHANNEL_ENAME,备注：渠道大类英文名称 */
	@Column(name = "CHANNEL_ENAME", description = "渠道大类英文名称")
	private String channelEname;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@Column(name = "CREATE_TIME", description = "创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：修改时间 */
	@Column(name = "MODIFIED_TIME", description = "修改时间")
	private Date modifiedTime;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@Column(name = "VALID_IND", description = "有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：FLAG,备注：预留标志 */
	@Column(name = "FLAG", description = "预留标志")
	private String flag;
}
