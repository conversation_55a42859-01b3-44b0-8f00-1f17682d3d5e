package ins.channel.channel.vo;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GgchannelVo对象.对应实体描述：收付机构定义表
 *
 */
@Data
@ApiModel("GgchannelVo对象")
public class GgchannelVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CHANNEL_CODE,备注：渠道大类代码 pk */
	@ApiModelProperty("渠道大类代码 pk")
	private String channelCode;
	/** 对应字段：CHANNEL_CNAME,备注：渠道大类简体名称 */
	@ApiModelProperty("渠道大类简体名称")
	private String channelCname;
	/** 对应字段：CHANNEL_TNAME,备注：渠道大类繁体名称 */
	@ApiModelProperty("渠道大类繁体名称")
	private String channelTname;
	/** 对应字段：CHANNEL_ENAME,备注：渠道大类英文名称 */
	@ApiModelProperty("渠道大类英文名称")
	private String channelEname;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@ApiModelProperty("创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：修改时间 */
	@ApiModelProperty("修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	private Date modifiedTime;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：预留标志 */
	@ApiModelProperty("预留标志")
	private String flag;
}
