package ins.channel.code.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.baseCode.po.BaseCode;
import ins.channel.code.po.Ggcode;
import ins.channel.code.po.GgcodeSearch;
import ins.channel.code.po.GgcodePayWay;
import ins.channel.code.po.GgcodeSelect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * 表GGCODE对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GgcodeDao extends MybatisBaseDao<Ggcode, String> {

    /**
     * 根据页面条件进行分页查询
     * @param pageParam
     * @param entity
     * @return
     */
    Page<Ggcode> searchPage(PageParam pageParam, GgcodeSearch entity);

    /**
     * 主键无需传入，自动插入
     * @param entity
     * @return
     */
    int insertAuto(Ggcode entity);
    int insertSelectiveAuto(Ggcode entity);

    /**
     * 根据页面条件进行分页查询
     * @param pageParam
     * @param entity
     * @return
     */
    Page<Ggcode> searchCodeByRemark(PageParam pageParam, GgcodePayWay entity);

    /**
     * 供下拉框查询使用
     * @param pageParam
     * @param queryInfo
     * @return
     */
    Page<GgcodeSelect> codeInfoForSelectPage(PageParam pageParam,@Param("queryInfo") String queryInfo);

    List<GgcodeSelect> codeInfoForSelect(@Param("codeType") String codeType);

    Ggcode translate(String codeType,String codeCode);

    /**
     * Modify By Zhoutaoyu 查询所有码表(供基础码表查询使用) 2019/11/20
     * @return
     */
    List<BaseCode> queryAll();
    
    List<BaseCode> queryALLExclusions();

    Ggcode queryByCode(String codeCode);
}
