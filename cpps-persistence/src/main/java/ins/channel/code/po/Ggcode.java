package ins.channel.code.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GGCODE的PO对象<br/>
 * 对应表名：GGCODE,备注：代码配置表
 *
 */
@Data
@Table(name = "GGCODE")
public class Ggcode implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：GID,备注：pk */
	@Column(name = "GID", description = "pk")
	private String gid;
	/** 对应字段：CODE_TYPE,备注：代码类型 */
	@Column(name = "CODE_TYPE", description = "代码类型")
	private String codeType;
	/** 对应字段：COMPANY_CODE,备注：机构代码 */
	@Column(name = "COMPANY_CODE", description = "机构代码")
	private String companyCode;
	/** 对应字段：CODE_CODE,备注：业务代码 */
	@Column(name = "CODE_CODE", description = "业务代码")
	private String codeCode;
	/** 对应字段：CODE_CNAME,备注：业务代码中文名称 */
	@Column(name = "CODE_CNAME", description = "业务代码中文名称")
	private String codeCname;
	/** 对应字段：CODE_TNAME,备注：业务代码繁体名称 */
	@Column(name = "CODE_TNAME", description = "业务代码繁体名称")
	private String codeTname;
	/** 对应字段：CODE_ENAME,备注：业务代码英文名称 */
	@Column(name = "CODE_ENAME", description = "业务代码英文名称")
	private String codeEname;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@Column(name = "VALID_IND", description = "有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@Column(name = "FLAG", description = "标志字段")
	private String flag;
	/** 对应字段：DISPLAY_NO,备注：显示序号 */
	@Column(name = "DISPLAY_NO", description = "显示序号")
	private BigDecimal displayNo;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@Column(name = "CREATE_TIME", description = "创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@Column(name = "MODIFIED_TIME", description = "更新时间")
	private Date modifiedTime;
}
