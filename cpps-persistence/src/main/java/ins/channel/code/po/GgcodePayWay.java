package ins.channel.code.po;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * GgcodeVo对象.对应实体描述：代码配置表
 *
 */
@Data
public class GgcodePayWay implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：COMPANY_CODE,备注：机构代码 */
	private String companyCode;

	/** 对应字段：remark,备注：1 保费 2 出单费 3 手续费 4 赔款 5 再保 6 暂收款 */
	private String remark;

	/** 对应字段：CODE_TYPE,备注：代码类型*/
	private String codeType;

}
