package ins.channel.code.po;

import lombok.Data;

/**
 * 代码类型查询Po
 */
@Data
public class GgcodeSearch {

    /** 对应字段：CODE_TYPE,备注：代码类型 */
    private String codeType;

    /** 对应字段：COMPANY_CODE,备注：机构代码 */
    private String companyCode;

    /** 对应字段：CODE_CODE,备注：业务代码 */
    private String codeCode;

    /** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
    private String validInd;
    /** 职业/工种 */
    private String codeCname;

    private String codeTname;
    /** 对应字段：REMARK,备注：等级 */
    private String remark;

}
