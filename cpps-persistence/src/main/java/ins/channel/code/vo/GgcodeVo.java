package ins.channel.code.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonFormat;
import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GgcodeVo对象.对应实体描述：代码配置表
 *
 */
@Data
@ApiModel("GgcodeVo对象")
public class GgcodeVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：GID,备注：pk */
	@ApiModelProperty("pk")
	private String gid;
	/** 对应字段：CODE_TYPE,备注：代码类型 */
	@ApiModelProperty("代码类型")
	private String codeType;
	/** 对应字段：COMPANY_CODE,备注：机构代码 */
	@ApiModelProperty("机构代码")
	private String companyCode;
	/** 对应字段：CODE_CODE,备注：业务代码 */
	@ApiModelProperty("业务代码")
	private String codeCode;
	/** 对应字段：CODE_CNAME,备注：业务代码中文名称 */
	@ApiModelProperty("业务代码中文名称")
	private String codeCname;
	/** 对应字段：CODE_TNAME,备注：业务代码繁体名称 */
	@ApiModelProperty("业务代码繁体名称")
	private String codeTname;
	/** 对应字段：CODE_ENAME,备注：业务代码英文名称 */
	@ApiModelProperty("业务代码英文名称")
	private String codeEname;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
	/** 对应字段：DISPLAY_NO,备注：显示序号 */
	@ApiModelProperty("显示序号")
	private BigDecimal displayNo;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	private Date modifiedTime;
}
