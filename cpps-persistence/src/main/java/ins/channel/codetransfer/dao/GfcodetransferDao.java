package ins.channel.codetransfer.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.codetransfer.po.Gfcodetransfer;
import ins.channel.codetransfer.po.GfcodetransferKey;
import ins.channel.codetransfer.po.GfcodetransferSearch;
import org.apache.ibatis.annotations.Mapper;

/**
 *
 * 表GFCODETRANSFER对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GfcodetransferDao extends MybatisBaseDao<Gfcodetransfer, GfcodetransferKey> {
    /**
     * 根据页面条件进行分页查询
     * @param pageParam
     * @param entity
     * @return
     */
    Page<Gfcodetransfer> searchPage(PageParam pageParam, GfcodetransferSearch entity);

}
