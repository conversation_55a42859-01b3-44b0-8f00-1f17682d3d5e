package ins.channel.codetransfer.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GFCODETRANSFER的复合主键PO对象<br/>
 * 对应表名：GFCODETRANSFER,备注：转码配置表
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "GFCODETRANSFER")
public class Gfcodetransfer extends GfcodetransferKey implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CODE_NAME,备注：代码名称 */
	@Column(name = "CODE_NAME", description = "代码名称")
	private String codeName;
	/** 对应字段：TRANS_CODE_CODE,备注：转换的代码值 */
	@Column(name = "TRANS_CODE_CODE", description = "转换的代码值")
	private String transCodeCode;
	/** 对应字段：TRANS_CODE_NAME,备注：转换的代码名称 */
	@Column(name = "TRANS_CODE_NAME", description = "转换的代码名称")
	private String transCodeName;
	/** 对应字段：VALID_IND,备注：有效状态 0 无效 1 有效 */
	@Column(name = "VALID_IND", description = "有效状态 0 无效 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：FLAG,备注：预留标志 */
	@Column(name = "FLAG", description = "预留标志")
	private String flag;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@Column(name = "CREATE_TIME", description = "创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@Column(name = "MODIFIED_TIME", description = "更新时间")
	private Date modifiedTime;
	/** 对应字段：UPDATE_TIMES,备注：修改次数 */
	@Column(name = "UPDATE_TIMES", description = "修改次数")
	private BigDecimal updateTimes;
}
