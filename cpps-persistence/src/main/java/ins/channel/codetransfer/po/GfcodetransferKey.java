package ins.channel.codetransfer.po;

import java.io.Serializable;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GFCODETRANSFER的复合主键PO主键对象<br/>
 * 对应表名：GFCODETRANSFER,备注：转码配置表
 *
 */
@Data
@Table(name = "GFCODETRANSFER")
public class GfcodetransferKey implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CODE_CODE,备注：代码值 */
	@Column(name = "CODE_CODE", description = "代码值")
	private String codeCode;
	/** 对应字段：TRANS_TYPE,备注：转码类型 */
	@Column(name = "TRANS_TYPE", description = "转码类型")
	private String transType;
}
