package ins.channel.codetransfer.po;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GFCODETRANSFER的PO对象<br/>
 * 对应表名：GFCODETRANSFER,备注：转码配置表
 *
 */
@Data
public class GfcodetransferSearch implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：TRANS_TYPE,备注：转码类型 */
	private String transType;

	/** 对应字段：CODE_CODE,备注：代码值 */
	private String codeCode;

	/** 对应字段：CODE_NAME,备注：代码名称 */
	private String codeName;

	/** 对应字段：VALID_IND,备注：有效状态 0 无效 1 有效 */
	private String validInd;

	/** 开始时间 */
	private String startTime;

	/** 结束时间 */
	private String endTime;
}
