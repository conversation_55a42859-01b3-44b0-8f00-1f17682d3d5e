package ins.channel.codetransfer.vo;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GfcodetransferKeyVo对象.对应实体描述：转码配置表
 *
 */
@Data
@ApiModel(" GfcodetransferKeyVo对象")
public class GfcodetransferKeyVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CODE_CODE,备注：代码值 */
	@ApiModelProperty("代码值")
	private String codeCode;
	/** 对应字段：TRANS_TYPE,备注：转码类型 */
	@ApiModelProperty("转码类型")
	private String transType;
}
