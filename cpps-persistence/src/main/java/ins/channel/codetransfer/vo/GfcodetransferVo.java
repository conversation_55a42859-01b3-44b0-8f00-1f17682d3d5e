package ins.channel.codetransfer.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * GfcodetransferVo对象.对应实体描述：转码配置表
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("GfcodetransferVo对象")
public class GfcodetransferVo extends GfcodetransferKeyVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CODE_NAME,备注：代码名称 */
	@ApiModelProperty("代码名称")
	private String codeName;
	/** 对应字段：TRANS_CODE_CODE,备注：转换的代码值 */
	@ApiModelProperty("转换的代码值")
	private String transCodeCode;
	/** 对应字段：TRANS_CODE_NAME,备注：转换的代码名称 */
	@ApiModelProperty("转换的代码名称")
	private String transCodeName;
	/** 对应字段：VALID_IND,备注：有效状态 0 无效 1 有效 */
	@ApiModelProperty("有效状态 0 无效 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：预留标志 */
	@ApiModelProperty("预留标志")
	private String flag;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	private Date modifiedTime;
	/** 对应字段：UPDATE_TIMES,备注：修改次数 */
	@ApiModelProperty("修改次数")
	private BigDecimal updateTimes;
}
