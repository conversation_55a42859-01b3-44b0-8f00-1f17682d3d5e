package ins.channel.codetranstype.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.codetranstype.po.Gfcodetranstype;
import ins.channel.codetranstype.po.GfcodetranstypeSearch;
import org.apache.ibatis.annotations.Mapper;

/**
 *
 * 表GFCODETRANSTYPE对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GfcodetranstypeDao extends MybatisBaseDao<Gfcodetranstype, String> {
    /**
     * 根据页面条件进行分页查询
     * @param pageParam
     * @param entity
     * @return
     */
    Page<Gfcodetranstype> searchPage(PageParam pageParam, GfcodetranstypeSearch entity);

    /**
     * 主键无需传入，自动插入
     * @param entity
     * @return
     */
    int insertAuto(Gfcodetranstype entity);
    int insertSelectiveAuto(Gfcodetranstype entity);
}
