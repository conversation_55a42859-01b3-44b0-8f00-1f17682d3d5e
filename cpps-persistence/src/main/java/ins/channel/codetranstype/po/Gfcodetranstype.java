package ins.channel.codetranstype.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GFCODETRANSTYPE的PO对象<br/>
 * 对应表名：GFCODETRANSTYPE,备注：转码类型配置表
 *
 */
@Data
@Table(name = "GFCODETRANSTYPE")
public class Gfcodetranstype implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：GID,备注：pk */
	@Column(name = "GID", description = "pk")
	private String gid;
	/** 对应字段：TRANS_TYPE,备注：转码类型，如：feetypecode收付原因代码转换 ttytype合约类型转换财务代码 */
	@Column(name = "TRANS_TYPE", description = "转码类型，如：feetypecode收付原因代码转换 ttytype合约类型转换财务代码")
	private String transType;
	/** 对应字段：TRANS_TYPE_CDESC,备注：转码类型中文描述 */
	@Column(name = "TRANS_TYPE_CDESC", description = "转码类型中文描述")
	private String transTypeCdesc;
	/** 对应字段：TRANS_TYPE_TDESC,备注：转码类型繁体描述 */
	@Column(name = "TRANS_TYPE_TDESC", description = "转码类型繁体描述")
	private String transTypeTdesc;
	/** 对应字段：TRANS_TYPE_EDESC,备注：转码类型英文描述 */
	@Column(name = "TRANS_TYPE_EDESC", description = "转码类型英文描述")
	private String transTypeEdesc;
	/** 对应字段：VALID_IND,备注：有效状态 0 无效 1 有效 */
	@Column(name = "VALID_IND", description = "有效状态 0 无效 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：FLAG,备注：预留标志 */
	@Column(name = "FLAG", description = "预留标志")
	private String flag;
	/** 对应字段：VAILD_DATE,备注：生效时间 */
	@Column(name = "VAILD_DATE", description = "生效时间")
	private Date vaildDate;
	/** 对应字段：INVALID_DATE,备注：失效时间 */
	@Column(name = "INVALID_DATE", description = "失效时间")
	private Date invalidDate;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@Column(name = "CREATE_TIME", description = "创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@Column(name = "MODIFIED_TIME", description = "更新时间")
	private Date modifiedTime;
}
