package ins.channel.codetranstype.po;

import ins.framework.mybatis.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GFCODETRANSTYPE的PO对象<br/>
 * 对应表名：GFCODETRANSTYPE,备注：转码类型配置表
 *
 */
@Data
@Table(name = "GFCODETRANSTYPE")
public class GfcodetranstypeSearch implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：TRANS_TYPE,备注：转码类型，如：feetypecode收付原因代码转换 ttytype合约类型转换财务代码 */
	private String transType;

	/** 对应字段：TRANS_TYPE_CDESC,备注：转码类型中文描述 */
	private String transTypeCdesc;

	/** 对应字段：VALID_IND,备注：有效状态 0 无效 1 有效 */
	private String validInd;

	/** 开始时间 */
	private String startTime;

	/** 结束时间 */
	private String endTime;
}
