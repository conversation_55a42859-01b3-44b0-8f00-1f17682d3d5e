package ins.channel.codetype.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.codetype.po.Ggcodetype;
import ins.channel.codetype.po.GgcodetypeSearch;
import ins.channel.codetype.po.GgcodetypeSelect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *
 * 表GGCODETYPE对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GgcodetypeDao extends MybatisBaseDao<Ggcodetype, String> {

    /**
     * 根据页面条件进行分页查询
     * @param pageParam
     * @param entity
     * @return
     */
    Page<Ggcodetype> searchPage(PageParam pageParam, GgcodetypeSearch entity);

    /**
     * 主键无需传入，自动插入
     * @param entity
     * @return
     */
    int insertAuto(Ggcodetype entity);
    int insertSelectiveAuto(Ggcodetype entity);

    /**
     * 供下拉框查询使用
     * @param pageParam
     * @param queryInfo
     * @return
     */
    Page<GgcodetypeSelect> codeTypeForSelectPage(PageParam pageParam, @Param("queryInfo") String queryInfo);

}
