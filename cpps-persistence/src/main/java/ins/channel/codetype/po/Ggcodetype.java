package ins.channel.codetype.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GGCODETYPE的PO对象<br/>
 * 对应表名：GGCODETYPE,备注：代码类型配置表
 *
 */
@Data
@Table(name = "GGCODETYPE")
public class Ggcodetype implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：GID,备注：pk */
	@Column(name = "GID", description = "pk")
	private String gid;
	/** 对应字段：CODE_TYPE,备注：代码类型 */
	@Column(name = "CODE_TYPE", description = "代码类型")
	private String codeType;
	/** 对应字段：CODE_TYPE_CDESC,备注：代码类型中文描述 */
	@Column(name = "CODE_TYPE_CDESC", description = "代码类型中文描述")
	private String codeTypeCdesc;
	/** 对应字段：CODE_TYPE_TDESC,备注：代码类型繁体中文描述 */
	@Column(name = "CODE_TYPE_TDESC", description = "代码类型繁体中文描述")
	private String codeTypeTdesc;
	/** 对应字段：CODE_TYPE_EDESC,备注：代码类型英文描述 */
	@Column(name = "CODE_TYPE_EDESC", description = "代码类型英文描述")
	private String codeTypeEdesc;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@Column(name = "VALID_IND", description = "有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@Column(name = "FLAG", description = "标志字段")
	private String flag;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@Column(name = "CREATE_TIME", description = "创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@Column(name = "MODIFIED_TIME", description = "更新时间")
	private Date modifiedTime;
}
