package ins.channel.codetype.vo;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GgcodetypeVo对象.对应实体描述：代码类型配置表
 *
 */
@Data
@ApiModel("GgcodetypeVo对象")
public class GgcodetypeVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：GID,备注：pk */
	@ApiModelProperty("pk")
	private String gid;
	/** 对应字段：CODE_TYPE,备注：代码类型 */
	@ApiModelProperty("代码类型")
	private String codeType;
	/** 对应字段：CODE_TYPE_CDESC,备注：代码类型中文描述 */
	@ApiModelProperty("代码类型中文描述")
	private String codeTypeCdesc;
	/** 对应字段：CODE_TYPE_TDESC,备注：代码类型繁体中文描述 */
	@ApiModelProperty("代码类型繁体中文描述")
	private String codeTypeTdesc;
	/** 对应字段：CODE_TYPE_EDESC,备注：代码类型英文描述 */
	@ApiModelProperty("代码类型英文描述")
	private String codeTypeEdesc;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	private Date modifiedTime;
}
