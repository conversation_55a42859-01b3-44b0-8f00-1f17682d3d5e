package ins.channel.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@ApiModel("RequestHead对象")
public class RequestHeadVO implements Serializable {
    /**
     * 流水号:ESB生成的服务标识号，仅在ESB调用服务提供方时有效，通常为32+4位GUID(xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx)
     */
    @ApiModelProperty("流水号")
    private String seqNo;
    /**
     * 消费方流水号:服务消费方用于唯一标识一次服务交互，建议使用GUID
     */
    @ApiModelProperty("消费方流水号")
    private String consumerSeqNo;
    /**
     * 服务消费方ID
     */
    @ApiModelProperty("服务消费方ID")
    @NotBlank(message = "服务消费方ID不可为空")
    private String consumerID;
    /**
     * 服务提供方ID:仅当消费方在路由服务中显式指定提供方时使用，ESB不会传给服务提供方
     */
    @ApiModelProperty("服务提供方ID")
    private String providerID;
    /**
     * 服务版本:用于同一服务有多个版本时消费方标识用，缺省为空，ESB不会传给服务提供方
     */
    @ApiModelProperty("服务版本")
    private String version;
}
