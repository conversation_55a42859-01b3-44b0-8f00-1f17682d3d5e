package ins.channel.common;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel("Request对象")
public class RequestVO implements Serializable {
    /**
     * 请求消息头
     */
    @ApiModelProperty("请求消息头")
    @Valid
    @NotNull(message = "请求消息头不能为空")
    private RequestHeadVO requestHead;
}
