package ins.channel.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("RequestHeadVO对象")
public class ResponseHeadVO implements Serializable {
    /**
     * 交易流水号（服务消费方）:服务消费方用于唯一标识一次服务交互，服务方提供方在返回给服务消费方时根据请求消息自动填充
     */
    @ApiModelProperty("交易流水号（服务消费方）:服务消费方用于唯一标识一次服务交互，服务方提供方在返回给服务消费方时根据请求消息自动填充")
    private String consumerSeqNo;
    /**
     * 交易流水号（服务提供方）:服务提供方用于唯一标识一次服务交互，建议使用GUID
     */
    @ApiModelProperty("交易流水号（服务提供方）:服务提供方用于唯一标识一次服务交互，建议使用GUID")
    private String providerSeqNo;
    /**
     * 响应状态:1 - 成功, 0-失败
     */
    @ApiModelProperty("响应状态:0 - 成功, 1-失败")
    private String status;
    /**
     * 响应代码:01- 校验失败 02-系统异常
     */
    @ApiModelProperty("响应代码:01- 校验失败 02-系统异常")
    private String appCode;
    /**
     * 响应信息:返回应用响应的描述性信息
     */
    @ApiModelProperty("响应信息:返回应用响应的描述性信息")
    private String appMessage;
}
