package ins.channel.company.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.baseCode.po.BaseCode;
import ins.channel.company.po.Ggcompany;
import ins.channel.company.po.GgcompanySearch;
import ins.channel.company.po.GgcompanySelect;
import ins.channel.power.vo.UserpermitdataVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 表GGCOMPANY对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 */
@Mapper
public interface GgcompanyDao extends MybatisBaseDao<Ggcompany, String> {
    /**
     * 查询上级机构
     *
     * @param comcode
     * @return
     */
    List<Ggcompany> selectUpperComInfo(String comcode);

    /**
     * 查询下级机构
     *
     * @param comcode
     * @return
     */
    List<Ggcompany> selectUnderComInfo(String comcode);

    /**
     * 模糊查询机构信息
     *
     * @param pageParam
     * @param clone
     * @return
     */
    Page<Ggcompany> pageByCondition(PageParam pageParam, Ggcompany clone);

    /**
     * 查询登录用户有权限的关联机构 供下拉框使用
     * @param queryInfo
     * @param permitComs
     * @return
     */
    Page<GgcompanySelect> companyForSelect(
                                               @Param("queryInfo") String queryInfo,
                                               @Param("companyCodes") Set<String> permitComs);



    List<GgcompanySelect> companyForSelectAll();

    /**
     * 查询机构信息
     *
     * @param comcode
     * @return
     */
    Ggcompany selectCompanyInfo(String comcode);

    List<UserpermitdataVo> queryuserpermitdataInfo(@Param("userCode") String userCode);

    List<Ggcompany> queryGgcompanyListByCodeSet(@Param("addSet") Set<String> addSet);

    /**
     * 根据页面条件进行分页查询
     *
     * @param pageParam
     * @param entity
     * @return
     */
//    Page<Ggcompany> searchPage(PageParam pageParam, GgcompanySearch entity);

    /**
     * 根据页面条件进行分页查询
     *
     * @param pageParam
     * @param entity
     * @return
     */
    Page<Ggcompany> searchPage(PageParam pageParam, GgcompanySearch entity);

    /**
     * Modify By Zhoutaoyu 查询所有出单机构信息(供基础码表查询使用) 2019/11/20
     *
     * @return
     */
    List<BaseCode> queryAll();

    List<Ggcompany> selectByCondition(Ggcompany ggcompany);

    Ggcompany selectUpperNameByUpperCode(String upperCompanyCode);
}
