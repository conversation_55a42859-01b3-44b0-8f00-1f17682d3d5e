package ins.channel.company.po;

import ins.framework.mybatis.annotations.Column;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GGCURRENCY的PO对象<br/>
 * 对应表名：GGCURRENCY,备注：GGCurrency-币别代码表
 *
 */
@Data
public class GgcompanySearch implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CODE_TYPE,备注：出单机构代码 */
	private String companyCode;
	/** 对应字段：COMPANY_CODE,备注：出单机构名称 */
	private String companyCname;
	/** 对应字段：CODE_TYPE,备注：收付机构代码 */
//	private String paymentCode;
	/** 对应字段：CODE_CODE,备注：业务代码 */
	private String upperCompanyCode;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	private String validInd;
	/** 对应字段：COM_LEVEL,备注：机构级别 */
	private String comLevel;
	/** 对应字段：TAX_NUMBER,备注：税务号 */
//	private String taxNumber;
	/** 对应字段：,备注：是否赋予收付机构权限 */
//	private String grantFlag;

}
