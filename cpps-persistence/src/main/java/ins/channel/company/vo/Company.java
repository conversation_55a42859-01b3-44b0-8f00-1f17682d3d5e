package ins.channel.company.vo;
import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import ins.platform.common.DateFormat;
import ins.platform.plugin.DateFormatSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 *
 * SysCompanyVo对象.对应实体描述：系统机构表
 *
 */
@Data
@ApiModel("Company请求体")
public class Company implements Serializable {
    private static final long serialVersionUID = 1L;
    @NotBlank(message = "操作类型不能为空")
    @Pattern(regexp = "ADD|DELETE|UPDATE", message = "operType字段只能是ADD|DELETE|UPDATE")
    @ApiModelProperty("操作类型")
    private String operType;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("数据同步时间(发送到队列的时间)")
    private Date operTime;
    @NotBlank(message = "机构代码不能为空")
    @ApiModelProperty("机构代码")
    private String operKey;
    @Valid
    @NotNull(message = "机构数据对象不能为空")
    @ApiModelProperty("机构数据对象对象")
    private SysCompanyVo sysCompany;

}
