package ins.channel.company.vo;

import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GgcompanyVo对象.对应实体描述：机构信息表
 *
 */
@Data
@ApiModel("GgcompanyVo对象")
public class GgcompanyVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：COMPANY_CODE,备注：机构代码 */
	@ApiModelProperty("机构代码")
	private String companyCode;
	/** 对应字段：COMPANY_CNAME,备注：机构中文描述 */
	@ApiModelProperty("机构中文描述")
	private String companyCname;
	/** 对应字段：COMPANY_TNAME,备注：机构繁体描述 */
	@ApiModelProperty("机构繁体描述")
	private String companyTname;
	/** 对应字段：COMPANY_ENAME,备注：机构英文描述 */
	@ApiModelProperty("机构英文描述")
	private String companyEname;
	/** 对应字段：ADDRESS_CNAME,备注：地址中文描述 */
	@ApiModelProperty("地址中文描述")
	private String addressCname;
	/** 对应字段：ADDRESS_TNAME,备注：地址繁体描述 */
	@ApiModelProperty("地址繁体描述")
	private String addressTname;
	/** 对应字段：ADDRESS_ENAME,备注：地址英文描述 */
	@ApiModelProperty("地址英文描述")
	private String addressEname;
	/** 对应字段：INSURER_CNAME,备注：保险人中文名称 */
	@ApiModelProperty("保险人中文名称")
	private String insurerCname;
	/** 对应字段：INSURER_TNAME,备注：保险人繁体名称 */
	@ApiModelProperty("保险人繁体名称")
	private String insurerTname;
	/** 对应字段：INSURER_ENAME,备注：保险人英文名称 */
	@ApiModelProperty("保险人英文名称")
	private String insurerEname;
	/** 对应字段：UPPER_COMPANY_CODE,备注：上级机构代码 */
	@ApiModelProperty("上级机构代码")
	private String upperCompanyCode;
	/** 对应字段：COM_ATTRIBUTE,备注：机构属性 */
	@ApiModelProperty("机构属性")
	private String comAttribute;
	/** 对应字段：COM_TYPE,备注：机构类型 */
	@ApiModelProperty("机构类型")
	private String comType;
	/** 对应字段：CENTER_IND,备注：核算单位，利润中心 */
	@ApiModelProperty("核算单位，利润中心")
	private String centerInd;
	/** 对应字段：COM_LEVEL,备注：机构级别 */
	@ApiModelProperty("机构级别")
	private String comLevel;
	/** 对应字段：POST_CODE,备注：邮政编码 */
	@ApiModelProperty("邮政编码")
	private String postCode;
	/** 对应字段：PHONE_NUMBER,备注：电话号码 */
	@ApiModelProperty("电话号码")
	private String phoneNumber;
	/** 对应字段：FAX_NUMBER,备注：传真电话 */
	@ApiModelProperty("传真电话")
	private String faxNumber;
	/** 对应字段：MANAGER,备注：主管人 */
	@ApiModelProperty("主管人")
	private String manager;
	/** 对应字段：WEB_ADDRESS,备注：网址 */
	@ApiModelProperty("网址")
	private String webAddress;
	/** 对应字段：SERVICE_PHONE,备注：服务电话 */
	@ApiModelProperty("服务电话")
	private String servicePhone;
	/** 对应字段：REPORT_PHONE,备注：报案电话 */
	@ApiModelProperty("报案电话")
	private String reportPhone;
	/** 对应字段：CREATOR_CODE,备注：创建人 */
	@ApiModelProperty("创建人")
	private String creatorCode;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/** 对应字段：UPDATER_CODE,备注：创建人代码 */
	@ApiModelProperty("创建人代码")
	private String updaterCode;
	/** 对应字段：UPDATE_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/** 对应字段：VALID_IND,备注：有效标示 */
	@ApiModelProperty("有效标示")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
	/** 对应字段：SHORT_CNAME,备注：机构简称中文描述 */
	@ApiModelProperty("机构简称中文描述")
	private String shortCname;
	/** 对应字段：SHORT_TNAME,备注：机构简称繁体描述 */
	@ApiModelProperty("机构简称繁体描述")
	private String shortTname;
	/** 对应字段：SHORT_ENAME,备注：机构简称英文描述 */
	@ApiModelProperty("机构简称英文描述")
	private String shortEname;
	/** 对应字段：TAX_NUMBER,备注：税务号 */
	@ApiModelProperty("税务号")
	private String taxNumber;
	/** 对应字段：EMAIL,备注：邮箱 */
	@ApiModelProperty("邮箱")
	private String email;
	/** 对应字段：PRINT_POLICY_COM_CODE,备注：打印保单机构代码 */
	@ApiModelProperty("打印保单机构代码")
	private String printPolicyComCode;
	/** 对应字段：PRINTIN_VOICE_COM_CODE,备注：打印发票机构代码 */
	@ApiModelProperty("打印发票机构代码")
	private String printinVoiceComCode;
	/** 对应字段：CITY_CODE,备注：地市代码 */
	@ApiModelProperty("地市代码")
	private String cityCode;
	/** 对应字段：REQUEST_IND */
	@ApiModelProperty()
	private String requestInd;
	/** 收付机构代码 */
	private String paymentComcode;
}
