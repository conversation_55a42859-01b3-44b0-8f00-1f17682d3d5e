package ins.channel.company.vo;
import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 *
 * SysCompanyVo对象.对应实体描述：系统机构表
 *
 */
@Data
public class SysCompanyVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 对应字段：com_code,备注：机构代码 */
    @NotBlank(message = "机构代码为空")
    @ApiModelProperty("机构代码")
    private String comCode;
    /** 对应字段：com_name,备注：机构名称 */
    @NotBlank(message = "机构名称为空")
    @ApiModelProperty("机构名称")
    private String comName;
    /** 对应字段：upper_com_code,备注：上级机构 */
    @NotBlank(message = "上级机构为空")
    @ApiModelProperty("上级机构")
    private String upperComCode;
    /** 对应字段：com_path,备注：机构路径 */
    @NotBlank(message = "机构路径为空")
    @ApiModelProperty("机构路径")
    private String comPath;
    /** 对应字段：province,备注：省份 */
    @ApiModelProperty("省份")
    private String province;
    /** 对应字段：city,备注：城市 */
    @ApiModelProperty("城市")
    private String city;
    /** 对应字段：county,备注：区县 */
    @ApiModelProperty("区县")
    private String county;
    /** 对应字段：address,备注：地址 */
    @ApiModelProperty("地址")
    private String address;
    /** 对应字段：org_type,备注：类型 */
    @NotBlank(message = "类型为空")
    @ApiModelProperty("类型")
    private String orgType;
    /** 对应字段：manager,备注：负责人 */
    @ApiModelProperty("负责人")
    private String manager;
    /** 对应字段：phone,备注：电话 */
    @ApiModelProperty("电话")
    private String phone;
    /** 对应字段：valid_ind,备注：效力状态 */
    @NotBlank(message = "叶子节点为空")
    @ApiModelProperty("叶子节点")
    private String leafNode;
    @NotBlank(message = "为空")
    @ApiModelProperty("效力状态")
    private String validInd;
    @ApiModelProperty("显示序号")
    private Integer displayNo;

}
