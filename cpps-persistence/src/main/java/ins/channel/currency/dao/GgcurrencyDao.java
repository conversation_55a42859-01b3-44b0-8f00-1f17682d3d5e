package ins.channel.currency.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.code.po.Ggcode;
import ins.channel.currency.po.Ggcurrency;
import ins.channel.currency.po.GgcurrencySearch;
import ins.channel.currency.po.GgcurrencySelect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * 表GGCURRENCY对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GgcurrencyDao extends MybatisBaseDao<Ggcurrency, String> {
    /**
     * 根据页面条件进行分页查询
     * @param pageParam
     * @param entity
     * @return
     */
    Page<Ggcurrency> searchPage(PageParam pageParam, GgcurrencySearch entity);

    /**
     * 主键无需传入，自动插入
     * @param entity
     * @return
     */
    int insertAuto(Ggcurrency entity);
    int insertSelectiveAuto(Ggcurrency entity);

    /**
     * 供下拉框查询使用
     * @param pageParam
     * @param queryInfo
     * @return
     */
    Page<GgcurrencySelect> currencyForSelectPage(PageParam pageParam, @Param("queryInfo") String queryInfo);


    /**
     * 翻译币别代码
     * @param currencyCode
     * @return
     */
    Ggcurrency translate(String currencyCode);

    /**
     * 查询 所有币别
     */
    List<GgcurrencySelect> currencyForSelect();

    /**
     * 根据币种名称查询
     *
     * @param currencyCname
     * @return
     */
    Ggcurrency translateFromCName(String currencyCname);
}
