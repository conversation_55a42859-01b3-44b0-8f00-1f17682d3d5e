package ins.channel.currency.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GGCURRENCY的PO对象<br/>
 * 对应表名：GGCURRENCY,备注：GGCurrency-币别代码表
 *
 */
@Data
@Table(name = "GGCURRENCY")
public class Ggcurrency implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：GID */
	@Column(name = "GID")
	private String gid;
	/** 对应字段：CURRENCY_CODE,备注：币别代码 */
	@Column(name = "CURRENCY_CODE", description = "币别代码")
	private String currencyCode;
	/** 对应字段：CURRENCY_CNAME,备注：币别简体中文名称 */
	@Column(name = "CURRENCY_CNAME", description = "币别简体中文名称")
	private String currencyCname;
	/** 对应字段：CURRENCY_TNAME,备注：币别繁体中文名称 */
	@Column(name = "CURRENCY_TNAME", description = "币别繁体中文名称")
	private String currencyTname;
	/** 对应字段：CURRENCY_ENAME,备注：币别英文名称 */
	@Column(name = "CURRENCY_ENAME", description = "币别英文名称")
	private String currencyEname;
	/** 对应字段：CREATOR_CODE,备注：创建人 */
	@Column(name = "CREATOR_CODE", description = "创建人")
	private String creatorCode;
	/** 对应字段：UPDATER_CODE,备注：最后修改人 */
	@Column(name = "UPDATER_CODE", description = "最后修改人")
	private String updaterCode;
	/** 对应字段：VALID_DATE,备注：生效日期 */
	@Column(name = "VALID_DATE", description = "生效日期")
	private Date validDate;
	/** 对应字段：INVALID_DATE,备注：失效日期 */
	@Column(name = "INVALID_DATE", description = "失效日期")
	private Date invalidDate;
	/** 对应字段：VALID_IND,备注：有效标志 */
	@Column(name = "VALID_IND", description = "有效标志")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@Column(name = "FLAG", description = "标志字段")
	private String flag;
	/** 对应字段：CREATE_BY */
	@Column(name = "CREATE_BY")
	private String createBy;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@Column(name = "CREATE_TIME", description = "创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_BY */
	@Column(name = "MODIFIED_BY")
	private String modifiedBy;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@Column(name = "MODIFIED_TIME", description = "更新时间")
	private Date modifiedTime;
}
