package ins.channel.currency.po;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GGCURRENCY的PO对象<br/>
 * 对应表名：GGCURRENCY,备注：GGCurrency-币别代码表
 *
 */
@Data
public class GgcurrencySearch implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CURRENCYCODE,备注：币别代码 */
	private String currencyCode;
	/** 对应字段：CURRENCYCNAME,备注：币别简体中文名称 */
	private String currencyCname;
	/** 对应字段：VALIDIND,备注：有效标志 */
	private String validind;
	/** 开始时间 */
	private String startTime;
	/** 结束时间 */
	private String endTime;

}
