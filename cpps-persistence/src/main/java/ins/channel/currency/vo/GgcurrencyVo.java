package ins.channel.currency.vo;

import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GgcurrencyVo对象.对应实体描述：GGCurrency-币别代码表
 *
 */
@Data
@ApiModel("GgcurrencyVo对象")
public class GgcurrencyVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：GID */
	@ApiModelProperty()
	private String gid;
	/** 对应字段：CURRENCY_CODE,备注：币别代码 */
	@ApiModelProperty("币别代码")
	private String currencyCode;
	/** 对应字段：CURRENCY_CNAME,备注：币别简体中文名称 */
	@ApiModelProperty("币别简体中文名称")
	private String currencyCname;
	/** 对应字段：CURRENCY_TNAME,备注：币别繁体中文名称 */
	@ApiModelProperty("币别繁体中文名称")
	private String currencyTname;
	/** 对应字段：CURRENCY_ENAME,备注：币别英文名称 */
	@ApiModelProperty("币别英文名称")
	private String currencyEname;
	/** 对应字段：CREATOR_CODE,备注：创建人 */
	@ApiModelProperty("创建人")
	private String creatorCode;
	/** 对应字段：UPDATER_CODE,备注：最后修改人 */
	@ApiModelProperty("最后修改人")
	private String updaterCode;
	/** 对应字段：VALID_DATE,备注：生效日期 */
	@ApiModelProperty("生效日期")
	private Date validDate;
	/** 对应字段：INVALID_DATE,备注：失效日期 */
	@ApiModelProperty("失效日期")
	private Date invalidDate;
	/** 对应字段：VALID_IND,备注：有效标志 */
	@ApiModelProperty("有效标志")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
	/** 对应字段：CREATE_BY */
	@ApiModelProperty()
	private String createBy;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_BY */
	@ApiModelProperty()
	private String modifiedBy;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	private Date modifiedTime;
}
