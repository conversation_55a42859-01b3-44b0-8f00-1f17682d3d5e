package ins.channel.exch.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.exch.po.Gpexch;
import ins.channel.exch.po.GpexchSearch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * 表GPEXCH对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GpexchDao extends MybatisBaseDao<Gpexch, String> {
    /**
     * 根据页面条件进行分页查询
     * @param pageParam
     * @param entity
     * @return
     */
    Page<Gpexch> searchPage(PageParam pageParam, GpexchSearch entity);

    /**
     * 主键无需传入，自动插入
     * @param entity
     * @return
     */
    int insertAuto(Gpexch entity);
    Long insertSelectiveAuto(Gpexch entity);

    /**
     * 查询大于等于总换日期的数据
     * @param gpexch
     * @return
     */
     List<Gpexch> getGtExchDateList(Gpexch gpexch);

    /**
     * 查询所属日期汇率
     */
    BigDecimal queryExchRate(@Param("date") String date,
                            @Param("base") String base,
                            @Param("exch") String exch,
                            @Param("exchType") String exchType);

    /**
     * 更新mq 标志位标志位
     */
    int updateSendMqFlag();
}
