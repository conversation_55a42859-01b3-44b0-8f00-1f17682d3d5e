package ins.channel.exch.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonFormat;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GPEXCH的PO对象<br/>
 * 对应表名：GPEXCH,备注：汇率表
 *
 */
@Data
@Table(name = "GPEXCH")
public class Gpexch implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：GID,备注：pk */
	@Column(name = "GID", description = "pk")
	private String gid;
	/** 对应字段：EXCH_DATE,备注：汇率日期 */
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@Column(name = "EXCH_DATE", description = "汇率日期")
	private Date exchDate;
	/** 对应字段：BASE,备注：基准 */
	@Column(name = "BASE", description = "基准")
	private BigDecimal base;
	/** 对应字段：BASE_CURRENCY,备注：基准币别 */
	@Column(name = "BASE_CURRENCY", description = "基准币别")
	private String baseCurrency;
	/** 对应字段：EXCH_CURRENCY,备注：兑换币别 */
	@Column(name = "EXCH_CURRENCY", description = "兑换币别")
	private String exchCurrency;
	/** 对应字段：EXCH_RATE,备注：兑换汇率 */
	@Column(name = "EXCH_RATE", description = "兑换汇率")
	private BigDecimal exchRate;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@Column(name = "VALID_IND", description = "有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@Column(name = "FLAG", description = "标志字段")
	private String flag;
	/** 对应字段：EXCH_TYPE,备注：年/天兑换率标志 */
	@Column(name = "EXCH_TYPE", description = "年/天兑换率标志")
	private String exchType;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@Column(name = "CREATE_TIME", description = "创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@Column(name = "MODIFIED_TIME", description = "更新时间")
	private Date modifiedTime;
	/** 对应字段：SEND_MQ_STATUS,备注：发送到rocketmq 的标示 */
	@Column(name = "SEND_MQ_STATUS", description = "发送到rocketmq 的标示")
	private String sendMqStatus;
}
