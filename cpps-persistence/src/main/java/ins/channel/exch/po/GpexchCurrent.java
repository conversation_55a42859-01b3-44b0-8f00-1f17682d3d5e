package ins.channel.exch.po;

import ins.framework.mybatis.annotations.Column;
import ins.framework.mybatis.annotations.Table;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GPEXCH的PO对象<br/>
 * 对应表名：GPEXCH,备注：汇率表
 *
 */
@Data
public class GpexchCurrent implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：EXCH_DATE,备注：汇率日期 */
	private Date exchDate;
	/** 对应字段：BASE_CURRENCY,备注：基准币别 */
	private String baseCurrency;
	/** 对应字段：EXCH_CURRENCY,备注：兑换币别 */
	private String exchCurrency;
	/** 对应字段：REMARK,备注：备注 */
}
