package ins.channel.exch.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GPEXCH的PO对象<br/>
 * 对应表名：GPEXCH,备注：汇率表
 *
 */
@Data
public class GpexchSearch implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：BASE_CURRENCY,备注：基准币别 */
	private String baseCurrency;

	/** 对应字段：BASE_CURRENCY,备注：兑换币别 */
	private String exchCurrency;

	/** 兑换汇率 */
	private String exchDate;
	/** DAC转化标志 */
	private String flag;
	/** 有效标志 */
	private String validInd;
	/** 发送消息队列 */
	private String sendMqStatus;

}
