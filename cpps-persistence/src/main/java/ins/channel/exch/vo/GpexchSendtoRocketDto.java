package ins.channel.exch.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * GpexchVo对象.对应实体描述：汇率表
 *
 */
@Data
@ApiModel("GpexchVo对象")
public class GpexchSendtoRocketDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：GID,备注：pk */
	@ApiModelProperty("pk")
	private String gid;
	/** 对应字段：EXCH_DATE,备注：汇率日期 */
	@ApiModelProperty("汇率日期")
	private String exchDate;
	/** 对应字段：BASE,备注：基准 */
	@ApiModelProperty("基准")
	private BigDecimal base;
	/** 对应字段：BASE_CURRENCY,备注：基准币别 */
	@ApiModelProperty("基准币别")
	private String baseCurrency;
	/** 对应字段：EXCH_CURRENCY,备注：兑换币别 */
	@ApiModelProperty("兑换币别")
	private String exchCurrency;
	/** 对应字段：EXCH_RATE,备注：兑换汇率 */
	@ApiModelProperty("兑换汇率")
	private BigDecimal exchRate;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：EXCH_TYPE,备注：年/天兑换率标志 */
	@ApiModelProperty("年/天兑换率标志")
	private String exchType;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	private String createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	private String modifiedTime;
}
