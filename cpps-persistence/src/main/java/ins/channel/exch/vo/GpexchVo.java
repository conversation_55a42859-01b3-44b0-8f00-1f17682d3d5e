package ins.channel.exch.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GpexchVo对象.对应实体描述：汇率表
 *
 */
@Data
@ApiModel("GpexchVo对象")
public class GpexchVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：GID,备注：pk */
	@ApiModelProperty("pk")
	private String gid;
	/** 对应字段：EXCH_DATE,备注：汇率日期 */
	@ApiModelProperty("汇率日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date exchDate;
	/** 对应字段：BASE,备注：基准 */
	@ApiModelProperty("基准")
	private BigDecimal base;
	/** 对应字段：BASE_CURRENCY,备注：基准币别 */
	@ApiModelProperty("基准币别")
	private String baseCurrency;
	/** ,备注：基准币别名称 */
	@ApiModelProperty("基准币别名称名称")
	private String baseCurrencyName;
	/** 对应字段：EXCH_CURRENCY,备注：兑换币别 */
	@ApiModelProperty("兑换币别")
	private String exchCurrency;
	/** 备注：兑换币别名称 */
	@ApiModelProperty("兑换币别")
	private String exchCurrencyName;
	/** 对应字段：EXCH_RATE,备注：兑换汇率 */
	@ApiModelProperty("兑换汇率")
	private BigDecimal exchRate;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
	/** 对应字段：EXCH_TYPE,备注：年/天兑换率标志 */
	@ApiModelProperty("年/天兑换率标志")
	private String exchType;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	private Date modifiedTime;
}
