package ins.channel.feetypecode.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.feetypecode.po.Gpfeetypecode;
import ins.channel.feetypecode.po.GpfeetypecodeSearch;
import org.apache.ibatis.annotations.Mapper;

/**
 *
 * 表GPFEETYPECODE对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GpfeetypecodeDao extends MybatisBaseDao<Gpfeetypecode, String> {

    /**
     * 根据页面条件进行分页查询
     * @param pageParam
     * @param entity
     * @return
     */
    Page<Gpfeetypecode> searchPage(PageParam pageParam, GpfeetypecodeSearch entity);

    /**
     * 主键无需传入，自动插入
     * @param entity
     * @return
     */
    int insertAuto(Gpfeetypecode entity);
}
