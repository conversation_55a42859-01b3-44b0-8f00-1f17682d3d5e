package ins.channel.feetypecode.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GPFEETYPECODE的PO对象<br/>
 * 对应表名：GPFEETYPECODE,备注：费用类型与计算符号对应表
 *
 */
@Data
@Table(name = "GPFEETYPECODE")
public class Gpfeetypecode implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：FEE_TYPE_CODE,备注：费用类型代码 */
	@Column(name = "FEE_TYPE_CODE", description = "费用类型代码")
	private String feeTypeCode;
	/** 对应字段：FEE_TYPE_CODE_CNAME,备注：费用类型简体中文名称 */
	@Column(name = "FEE_TYPE_CODE_CNAME", description = "费用类型简体中文名称")
	private String feeTypeCodeCname;
	/** 对应字段：FEE_TYPE_CODE_TNAME,备注：费用类型繁体中文名称 */
	@Column(name = "FEE_TYPE_CODE_TNAME", description = "费用类型繁体中文名称")
	private String feeTypeCodeTname;
	/** 对应字段：FEE_TYPE_CODE_ENAME,备注：费用类型英文名称 */
	@Column(name = "FEE_TYPE_CODE_ENAME", description = "费用类型英文名称")
	private String feeTypeCodeEname;
	/** 对应字段：CAL_SIGN,备注：计算符号 */
	@Column(name = "CAL_SIGN", description = "计算符号")
	private BigDecimal calSign;
	/** 对应字段：FEE_TYPE_CODE_ACC,备注：凭证费用类型代码 */
	@Column(name = "FEE_TYPE_CODE_ACC", description = "凭证费用类型代码")
	private String feeTypeCodeAcc;
	/** 对应字段：VALIDIND,备注：有效标志 */
	@Column(name = "VALIDIND", description = "有效标志")
	private String validind;
	/** 对应字段：FLAG,备注：预留标志 */
	@Column(name = "FLAG", description = "预留标志")
	private String flag;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@Column(name = "CREATE_TIME", description = "创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@Column(name = "MODIFIED_TIME", description = "更新时间")
	private Date modifiedTime;
}
