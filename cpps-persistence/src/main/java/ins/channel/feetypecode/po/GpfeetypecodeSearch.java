package ins.channel.feetypecode.po;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GPFEETYPECODE的PO对象<br/>
 * 对应表名：GPFEETYPECODE,备注：费用类型与计算符号对应表
 *
 */
@Data
public class GpfeetypecodeSearch implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：FEE_TYPE_CODE,备注：费用类型代码 */
	private String feeTypeCode;

	/** 对应字段：FEE_TYPE_CODE_CNAME,备注：费用类型简体中文名称 */
	private String feeTypeCodeCname;


	/** 对应字段：VALIDIND,备注：有效标志 */
	private String validind;

	/** 开始时间 */
	private String startTime;

	/** 结束时间 */
	private String endTime;
}
