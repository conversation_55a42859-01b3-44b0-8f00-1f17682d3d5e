package ins.channel.feetypecode.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GpfeetypecodeVo对象.对应实体描述：费用类型与计算符号对应表
 *
 */
@Data
@ApiModel("GpfeetypecodeVo对象")
public class GpfeetypecodeVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：FEE_TYPE_CODE,备注：费用类型代码 */
	@ApiModelProperty("费用类型代码")
	private String feeTypeCode;
	/** 对应字段：FEE_TYPE_CODE_CNAME,备注：费用类型简体中文名称 */
	@ApiModelProperty("费用类型简体中文名称")
	private String feeTypeCodeCname;
	/** 对应字段：FEE_TYPE_CODE_TNAME,备注：费用类型繁体中文名称 */
	@ApiModelProperty("费用类型繁体中文名称")
	private String feeTypeCodeTname;
	/** 对应字段：FEE_TYPE_CODE_ENAME,备注：费用类型英文名称 */
	@ApiModelProperty("费用类型英文名称")
	private String feeTypeCodeEname;
	/** 对应字段：CAL_SIGN,备注：计算符号 */
	@ApiModelProperty("计算符号")
	private BigDecimal calSign;
	/** 对应字段：FEE_TYPE_CODE_ACC,备注：凭证费用类型代码 */
	@ApiModelProperty("凭证费用类型代码")
	private String feeTypeCodeAcc;
	/** 对应字段：VALIDIND,备注：有效标志 */
	@ApiModelProperty("有效标志")
	private String validind;
	/** 对应字段：FLAG,备注：预留标志 */
	@ApiModelProperty("预留标志")
	private String flag;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	private Date modifiedTime;
}
