package ins.channel.gsclientmain.dao;

import ins.channel.baseCode.po.BaseCode;
import org.apache.ibatis.annotations.Mapper;

import ins.channel.gsclientmain.po.Gsclientmain;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GSCLIENTMAIN对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GsclientmainDao extends MybatisBaseDao<Gsclientmain, String> {

    //	Modify By Zhoutaoyu 查询所有所属公司信息(供基础码表查询使用) 2021/02/22
    List<BaseCode> queryAll();

    //根据代码翻译所属公司名称
    String translate(String clientCode);

    int batchInsert(List<Gsclientmain> gsclientmainList);

}
