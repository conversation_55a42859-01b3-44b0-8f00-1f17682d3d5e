package ins.channel.gsclientmain.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GSCLIENTMAIN的PO对象<br/>
 * 对应表名：GSCLIENTMAIN
 *
 */
@Data
@Table(name = "GSCLIENTMAIN")
public class Gsclientmain implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CLIENTCODE,备注：客户代码 */
	@Column(name = "CLIENTCODE", description = "客户代码")
	private String clientcode;
	/** 对应字段：CLIENTCNAME,备注：客户中文名称 */
	@Column(name = "CLIENTCNAME", description = "客户中文名称")
	private String clientcname;
	/** 对应字段：CLIENTENAME,备注：客户英文名称 */
	@Column(name = "CLIENTENAME", description = "客户英文名称")
	private String clientename;
	/** 对应字段：CLIENTTYPE,备注：客户类型 1-个人 2-团体 */
	@Column(name = "CLIENTTYPE", description = "客户类型 1-个人 2-团体")
	private String clienttype;
	/** 对应字段：JOINDATE,备注：客户录入时间 */
	@Column(name = "JOINDATE", description = "客户录入时间")
	private Date joindate;
	/** 对应字段：BLACKLISTIND */
	@Column(name = "BLACKLISTIND")
	private String blacklistind;
	/** 对应字段：FORMALIND */
	@Column(name = "FORMALIND")
	private String formalind;
	/** 对应字段：VIPIND */
	@Column(name = "VIPIND")
	private String vipind;
	/** 对应字段：CANCELIND */
	@Column(name = "CANCELIND")
	private String cancelind;
	/** 对应字段：CANCELREASON */
	@Column(name = "CANCELREASON")
	private String cancelReason;
	/** 对应字段：CANCELDATE */
	@Column(name = "CANCELDATE")
	private Date cancelDate;
	/** 对应字段：RELATEDCLIENT */
	@Column(name = "RELATEDCLIENT")
	private String relatedclient;
	/** 对应字段：REMARK */
	@Column(name = "REMARK")
	private String remark;
	/** 对应字段：FLAG */
	@Column(name = "FLAG")
	private String flag;
	/** 对应字段：CONNECTION */
	@Column(name = "CONNECTION")
	private String connection;
	/** 对应字段：POSTCODE */
	@Column(name = "POSTCODE")
	private String postCode;
	/** 对应字段：POSTADDRESS */
	@Column(name = "POSTADDRESS")
	private String postAddress;
	/** 对应字段：QUALITYIND */
	@Column(name = "QUALITYIND")
	private String qualityind;
	/** 对应字段：CLIENTSTATUS */
	@Column(name = "CLIENTSTATUS",description = "有效状态 0-无效 1-有效")
	private String clientstatus;
	/** 对应字段：VISITIND */
	@Column(name = "VISITIND")
	private String visitind;
	/** 对应字段：BANKCODE */
	@Column(name = "BANKCODE")
	private String bankCode;
	/** 对应字段：ACCOUNTNO */
	@Column(name = "ACCOUNTNO")
	private String accountNo;
	/** 对应字段：MONEYLAUNDERINGIND */
	@Column(name = "MONEYLAUNDERINGIND")
	private String moneylaunderingind;
	/** 对应字段：DIRECTCLIENTIND */
	@Column(name = "DIRECTCLIENTIND")
	private String directclientind;
	/** 对应字段：SCANDOCIND */
	@Column(name = "SCANDOCIND")
	private String scandocind;
	/** 对应字段：ORIGINCLIENTCODE */
	@Column(name = "ORIGINCLIENTCODE")
	private String originclientcode;
	/** 对应字段：RELATECLIENTCODE */
	@Column(name = "RELATECLIENTCODE")
	private String relateclientcode;
	/** 对应字段：CREATORCODE */
	@Column(name = "CREATORCODE")
	private String creatorcode;
	/** 对应字段：CREATETIME */
	@Column(name = "CREATETIME")
	private Date createTime;
	/** 对应字段：UPDATERCODE */
	@Column(name = "UPDATERCODE")
	private String updaterCode;
	/** 对应字段：UPDATETIME */
	@Column(name = "UPDATETIME")
	private Date updatetime;
	/** 对应字段：ITEMPROVINCECODE,备注：省份代码 */
	@Column(name = "ITEMPROVINCECODE", description = "省份代码")
	private String itemprovincecode;
	/** 对应字段：ITEMCITYCODE,备注：城市代码 */
	@Column(name = "ITEMCITYCODE", description = "城市代码")
	private String itemcitycode;
	/** 对应字段：ITEMPROVINCECNAME,备注：省份名称 */
	@Column(name = "ITEMPROVINCECNAME", description = "省份名称")
	private String itemprovincecname;
	/** 对应字段：ITEMCITYCNAME,备注：城市名称 */
	@Column(name = "ITEMCITYCNAME", description = "城市名称")
	private String itemcitycname;
	/** 对应字段：STARLEVEL,备注：客户星级 */
	@Column(name = "STARLEVEL", description = "客户星级")
	private String starlevel;
	/** 对应字段：UPDATESYSDATE */
	@Column(name = "UPDATESYSDATE")
	private Date updatesysdate;
	/** 对应字段：BANKNAME */
	@Column(name = "BANKNAME")
	private String bankName;
	/** 对应字段：EMAIL */
	@Column(name = "EMAIL")
	private String email;
	/** 对应字段：INDUSTRYMAINCODE */
	@Column(name = "INDUSTRYMAINCODE")
	private String industrymaincode;
	/** 对应字段：INDUSTRYKINDCODE */
	@Column(name = "INDUSTRYKINDCODE")
	private String industrykindcode;
	/** 对应字段：TALLAGETYPE */
	@Column(name = "TALLAGETYPE")
	private String tallagetype;
	/** 对应字段：TALLAGENO */
	@Column(name = "TALLAGENO")
	private String tallageno;
	/** 对应字段：CORPORATEADDRESS */
	@Column(name = "CORPORATEADDRESS")
	private String corporateaddress;
	/** 对应字段：OPENINGBANK */
	@Column(name = "OPENINGBANK")
	private String openingbank;
	/** 对应字段：TALLAGEBANKCODE */
	@Column(name = "TALLAGEBANKCODE")
	private String tallagebankcode;
	/** 对应字段：CORPORATETEL */
	@Column(name = "CORPORATETEL")
	private String corporatetel;
	/** 对应字段：SENDADDRESS */
	@Column(name = "SENDADDRESS")
	private String sendaddress;
	/** 对应字段：TALLAGEREMARKS */
	@Column(name = "TALLAGEREMARKS")
	private String tallageremarks;
	/** 对应字段：INVOICETYPE */
	@Column(name = "INVOICETYPE")
	private String invoicetype;
	/** 对应字段：REGISTERCODE */
	@Column(name = "REGISTERCODE")
	private String registercode;
}
