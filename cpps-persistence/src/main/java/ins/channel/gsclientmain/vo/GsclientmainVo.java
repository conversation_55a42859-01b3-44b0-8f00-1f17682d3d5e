package ins.channel.gsclientmain.vo;

import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GsclientmainVo对象.
 *
 */
@Data
@ApiModel("GsclientmainVo对象")
public class GsclientmainVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CLIENTCODE,备注：客户代码 */
	@ApiModelProperty("客户代码")
	private String clientcode;
	/** 对应字段：CLIENTCNAME,备注：客户中文名称 */
	@ApiModelProperty("客户中文名称")
	private String clientcname;
	/** 对应字段：CLIENTENAME,备注：客户英文名称 */
	@ApiModelProperty("客户英文名称")
	private String clientename;
	/** 对应字段：CLIENTTYPE,备注：客户类型 1-个人 2-团体 */
	@ApiModelProperty("客户类型 1-个人 2-团体")
	private String clienttype;
	/** 对应字段：JOINDATE,备注：客户录入时间 */
	@ApiModelProperty("客户录入时间")
	private Date joindate;
	/** 对应字段：BLACKLISTIND */
	@ApiModelProperty()
	private String blacklistind;
	/** 对应字段：FORMALIND */
	@ApiModelProperty()
	private String formalind;
	/** 对应字段：VIPIND */
	@ApiModelProperty()
	private String vipind;
	/** 对应字段：CANCELIND */
	@ApiModelProperty()
	private String cancelind;
	/** 对应字段：CANCELREASON */
	@ApiModelProperty()
	private String cancelReason;
	/** 对应字段：CANCELDATE */
	@ApiModelProperty()
	private Date cancelDate;
	/** 对应字段：RELATEDCLIENT */
	@ApiModelProperty()
	private String relatedclient;
	/** 对应字段：REMARK */
	@ApiModelProperty()
	private String remark;
	/** 对应字段：FLAG */
	@ApiModelProperty()
	private String flag;
	/** 对应字段：CONNECTION */
	@ApiModelProperty()
	private String connection;
	/** 对应字段：POSTCODE */
	@ApiModelProperty()
	private String postCode;
	/** 对应字段：POSTADDRESS */
	@ApiModelProperty()
	private String postAddress;
	/** 对应字段：QUALITYIND */
	@ApiModelProperty()
	private String qualityind;
	/** 对应字段：CLIENTSTATUS */
	@ApiModelProperty("有效状态 0-无效 1-有效")
	private String clientstatus;
	/** 对应字段：VISITIND */
	@ApiModelProperty()
	private String visitind;
	/** 对应字段：BANKCODE */
	@ApiModelProperty()
	private String bankCode;
	/** 对应字段：ACCOUNTNO */
	@ApiModelProperty()
	private String accountNo;
	/** 对应字段：MONEYLAUNDERINGIND */
	@ApiModelProperty()
	private String moneylaunderingind;
	/** 对应字段：DIRECTCLIENTIND */
	@ApiModelProperty()
	private String directclientind;
	/** 对应字段：SCANDOCIND */
	@ApiModelProperty()
	private String scandocind;
	/** 对应字段：ORIGINCLIENTCODE */
	@ApiModelProperty()
	private String originclientcode;
	/** 对应字段：RELATECLIENTCODE */
	@ApiModelProperty()
	private String relateclientcode;
	/** 对应字段：CREATORCODE */
	@ApiModelProperty()
	private String creatorcode;
	/** 对应字段：CREATETIME */
	@ApiModelProperty()
	private Date createTime;
	/** 对应字段：UPDATERCODE */
	@ApiModelProperty()
	private String updaterCode;
	/** 对应字段：UPDATETIME */
	@ApiModelProperty()
	private Date updatetime;
	/** 对应字段：ITEMPROVINCECODE,备注：省份代码 */
	@ApiModelProperty("省份代码")
	private String itemprovincecode;
	/** 对应字段：ITEMCITYCODE,备注：城市代码 */
	@ApiModelProperty("城市代码")
	private String itemcitycode;
	/** 对应字段：ITEMPROVINCECNAME,备注：省份名称 */
	@ApiModelProperty("省份名称")
	private String itemprovincecname;
	/** 对应字段：ITEMCITYCNAME,备注：城市名称 */
	@ApiModelProperty("城市名称")
	private String itemcitycname;
	/** 对应字段：STARLEVEL,备注：客户星级 */
	@ApiModelProperty("客户星级")
	private String starlevel;
	/** 对应字段：UPDATESYSDATE */
	@ApiModelProperty()
	private Date updatesysdate;
	/** 对应字段：BANKNAME */
	@ApiModelProperty()
	private String bankName;
	/** 对应字段：EMAIL */
	@ApiModelProperty()
	private String email;
	/** 对应字段：INDUSTRYMAINCODE */
	@ApiModelProperty()
	private String industrymaincode;
	/** 对应字段：INDUSTRYKINDCODE */
	@ApiModelProperty()
	private String industrykindcode;
	/** 对应字段：TALLAGETYPE */
	@ApiModelProperty()
	private String tallagetype;
	/** 对应字段：TALLAGENO */
	@ApiModelProperty()
	private String tallageno;
	/** 对应字段：CORPORATEADDRESS */
	@ApiModelProperty()
	private String corporateaddress;
	/** 对应字段：OPENINGBANK */
	@ApiModelProperty()
	private String openingbank;
	/** 对应字段：TALLAGEBANKCODE */
	@ApiModelProperty()
	private String tallagebankcode;
	/** 对应字段：CORPORATETEL */
	@ApiModelProperty()
	private String corporatetel;
	/** 对应字段：SENDADDRESS */
	@ApiModelProperty()
	private String sendaddress;
	/** 对应字段：TALLAGEREMARKS */
	@ApiModelProperty()
	private String tallageremarks;
	/** 对应字段：INVOICETYPE */
	@ApiModelProperty()
	private String invoicetype;
	/** 对应字段：REGISTERCODE */
	@ApiModelProperty()
	private String registercode;
}
