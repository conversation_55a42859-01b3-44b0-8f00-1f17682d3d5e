package ins.channel.gumaxno.dao;

import ins.channel.gumaxno.vo.GumaxnoVo;
import org.apache.ibatis.annotations.Mapper;

import ins.channel.gumaxno.po.Gumaxno;
import ins.framework.mybatis.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 *
 * 表GUMAXNO对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GumaxnoDao extends MybatisBaseDao<Gumaxno, String> {

    Gumaxno selectByCondition(GumaxnoVo vo);

    Integer updateSelectiveByPolicyNoAndStatus(Map map);
}
