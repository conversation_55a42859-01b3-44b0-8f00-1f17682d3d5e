package ins.channel.gumaxno.po;

import java.io.Serializable;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GUMAXNO的PO对象<br/>
 * 对应表名：GUMAXNO,备注：最新版本号表
 *
 */
@Data
@Table(name = "GUMAXNO")
public class Gumaxno implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键ID */
	@Column(name = "ID", description = "主键ID")
	private String id;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@Column(name = "POLICYNO", description = "保单号码")
	private String policyNo;
	/** 对应字段：VERSIONNO,备注：最新版本号 */
	@Column(name = "VERSIONNO", description = "最新版本号")
	private String versionNo;
	/** 对应字段：STATUS,备注：可修改状态 0-允许修改 1-不允许修改 */
	@Column(name = "STATUS", description = "可修改状态 0-允许修改 1-不允许修改")
	private String status;
	/** 对应字段：FLAG,备注：类型 */
	@Column(name = "FLAG", description = "类型")
	private String flag;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
}
