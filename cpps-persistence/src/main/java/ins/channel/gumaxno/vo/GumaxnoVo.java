package ins.channel.gumaxno.vo;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GumaxnoVo对象.对应实体描述：最新版本号表
 *
 */
@Data
@ApiModel("GumaxnoVo对象")
public class GumaxnoVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键ID */
	@ApiModelProperty("主键ID")
	private String id;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;
	/** 对应字段：VERSIONNO,备注：最新版本号 */
	@ApiModelProperty("最新版本号")
	private String versionNo;
	/** 对应字段：STATUS,备注：可修改状态 0-允许修改 1-不允许修改 */
	@ApiModelProperty("可修改状态 0-允许修改 1-不允许修改")
	private String status;
	/** 对应字段：FLAG,备注：类型 */
	@ApiModelProperty("类型")
	private String flag;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
}
