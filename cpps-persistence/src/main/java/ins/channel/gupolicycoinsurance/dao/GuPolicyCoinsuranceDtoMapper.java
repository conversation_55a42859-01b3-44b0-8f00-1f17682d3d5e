package ins.channel.gupolicycoinsurance.dao;

import ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDto;
import ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDtoExample;
import ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDtoKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface GuPolicyCoinsuranceDtoMapper {
    int countByExample(GuPolicyCoinsuranceDtoExample example);

    int deleteByExample(GuPolicyCoinsuranceDtoExample example);

    int deleteByPrimaryKey(GuPolicyCoinsuranceDtoKey key);

    int insert(GuPolicyCoinsuranceDto record);

    int insertSelective(GuPolicyCoinsuranceDto record);

    List<GuPolicyCoinsuranceDto> selectByExample(GuPolicyCoinsuranceDtoExample example);

    GuPolicyCoinsuranceDto selectByPrimaryKey(GuPolicyCoinsuranceDtoKey key);

    int updateByExampleSelective(@Param("record") GuPolicyCoinsuranceDto record, @Param("example") GuPolicyCoinsuranceDtoExample example);

    int updateByExample(@Param("record") GuPolicyCoinsuranceDto record, @Param("example") GuPolicyCoinsuranceDtoExample example);

    int updateByPrimaryKeySelective(GuPolicyCoinsuranceDto record);

    int updateByPrimaryKey(GuPolicyCoinsuranceDto record);
}