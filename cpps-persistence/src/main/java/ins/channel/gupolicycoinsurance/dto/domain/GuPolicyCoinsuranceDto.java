package ins.channel.gupolicycoinsurance.dto.domain;

import java.math.BigDecimal;

public class GuPolicyCoinsuranceDto extends GuPolicyCoinsuranceDtoKey {
    private String coinspolicyno;

    private String coinsname;

    private String coinstype;

    private BigDecimal coinsrate;

    private String principalind;

    private String leaderind;

    private String chiefadjusterind;

    private String currency;

    private BigDecimal coinsinsured;

    private BigDecimal coinspremium;

    private BigDecimal coinshandlingrate;

    private BigDecimal coinshandlingfee;

    private BigDecimal coinsagencyrate;

    private BigDecimal coinsagencycommission;

    private BigDecimal coinsissuerate;

    private BigDecimal coinsissueexpense;

    private String remark;

    private String flag;

    private String coinspremiumacceptind;

    private String coinspremiumcompose;

    private String coinsagencypayind;

    private BigDecimal coinslevyrate;

    private BigDecimal coinslevy;

    private String coinslevyacceptind;

    private BigDecimal coinsothfeerate;

    private BigDecimal coinsothfee;

    private String coinsothfeeacceptind;

    private BigDecimal coinscompreserverate;

    private BigDecimal coinscompreserve;

    private String coinscompresacceptind;

    private String debitaccepter;

    private String coinsclaimind;

    private String subpolicyno;

    private BigDecimal coinsnotaxpremium;

    private BigDecimal coinstaxamount;

    private String secondlevelcoinsname;

    private String relatedcoinspolicyno;

    private String relatedcoinscode;

    private String relatedcoinscname;

    private String sealsteamcode;

    private String sealsteamcname;

    private String sealsmancode;

    private String sealsmancname;

    private BigDecimal wholerate;

    private String orisingleno;

    private String relatedcoinstype;

    private String channeldetailcode;

    private String channeldetailcname;

    private BigDecimal coinsissueexpensetax;

    private BigDecimal coinsothfeetax;

    private BigDecimal coinsinsuredcny;

    private BigDecimal coinspremiumcny;

    private BigDecimal coinsnotaxpremiumcny;

    private BigDecimal coinstaxamountcny;

    private String currencycny;

    private Integer serialno;

    public String getCoinspolicyno() {
        return coinspolicyno;
    }

    public void setCoinspolicyno(String coinspolicyno) {
        this.coinspolicyno = coinspolicyno == null ? null : coinspolicyno.trim();
    }

    public String getCoinsname() {
        return coinsname;
    }

    public void setCoinsname(String coinsname) {
        this.coinsname = coinsname == null ? null : coinsname.trim();
    }

    public String getCoinstype() {
        return coinstype;
    }

    public void setCoinstype(String coinstype) {
        this.coinstype = coinstype == null ? null : coinstype.trim();
    }

    public BigDecimal getCoinsrate() {
        return coinsrate;
    }

    public void setCoinsrate(BigDecimal coinsrate) {
        this.coinsrate = coinsrate;
    }

    public String getPrincipalind() {
        return principalind;
    }

    public void setPrincipalind(String principalind) {
        this.principalind = principalind == null ? null : principalind.trim();
    }

    public String getLeaderind() {
        return leaderind;
    }

    public void setLeaderind(String leaderind) {
        this.leaderind = leaderind == null ? null : leaderind.trim();
    }

    public String getChiefadjusterind() {
        return chiefadjusterind;
    }

    public void setChiefadjusterind(String chiefadjusterind) {
        this.chiefadjusterind = chiefadjusterind == null ? null : chiefadjusterind.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public BigDecimal getCoinsinsured() {
        return coinsinsured;
    }

    public void setCoinsinsured(BigDecimal coinsinsured) {
        this.coinsinsured = coinsinsured;
    }

    public BigDecimal getCoinspremium() {
        return coinspremium;
    }

    public void setCoinspremium(BigDecimal coinspremium) {
        this.coinspremium = coinspremium;
    }

    public BigDecimal getCoinshandlingrate() {
        return coinshandlingrate;
    }

    public void setCoinshandlingrate(BigDecimal coinshandlingrate) {
        this.coinshandlingrate = coinshandlingrate;
    }

    public BigDecimal getCoinshandlingfee() {
        return coinshandlingfee;
    }

    public void setCoinshandlingfee(BigDecimal coinshandlingfee) {
        this.coinshandlingfee = coinshandlingfee;
    }

    public BigDecimal getCoinsagencyrate() {
        return coinsagencyrate;
    }

    public void setCoinsagencyrate(BigDecimal coinsagencyrate) {
        this.coinsagencyrate = coinsagencyrate;
    }

    public BigDecimal getCoinsagencycommission() {
        return coinsagencycommission;
    }

    public void setCoinsagencycommission(BigDecimal coinsagencycommission) {
        this.coinsagencycommission = coinsagencycommission;
    }

    public BigDecimal getCoinsissuerate() {
        return coinsissuerate;
    }

    public void setCoinsissuerate(BigDecimal coinsissuerate) {
        this.coinsissuerate = coinsissuerate;
    }

    public BigDecimal getCoinsissueexpense() {
        return coinsissueexpense;
    }

    public void setCoinsissueexpense(BigDecimal coinsissueexpense) {
        this.coinsissueexpense = coinsissueexpense;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag == null ? null : flag.trim();
    }

    public String getCoinspremiumacceptind() {
        return coinspremiumacceptind;
    }

    public void setCoinspremiumacceptind(String coinspremiumacceptind) {
        this.coinspremiumacceptind = coinspremiumacceptind == null ? null : coinspremiumacceptind.trim();
    }

    public String getCoinspremiumcompose() {
        return coinspremiumcompose;
    }

    public void setCoinspremiumcompose(String coinspremiumcompose) {
        this.coinspremiumcompose = coinspremiumcompose == null ? null : coinspremiumcompose.trim();
    }

    public String getCoinsagencypayind() {
        return coinsagencypayind;
    }

    public void setCoinsagencypayind(String coinsagencypayind) {
        this.coinsagencypayind = coinsagencypayind == null ? null : coinsagencypayind.trim();
    }

    public BigDecimal getCoinslevyrate() {
        return coinslevyrate;
    }

    public void setCoinslevyrate(BigDecimal coinslevyrate) {
        this.coinslevyrate = coinslevyrate;
    }

    public BigDecimal getCoinslevy() {
        return coinslevy;
    }

    public void setCoinslevy(BigDecimal coinslevy) {
        this.coinslevy = coinslevy;
    }

    public String getCoinslevyacceptind() {
        return coinslevyacceptind;
    }

    public void setCoinslevyacceptind(String coinslevyacceptind) {
        this.coinslevyacceptind = coinslevyacceptind == null ? null : coinslevyacceptind.trim();
    }

    public BigDecimal getCoinsothfeerate() {
        return coinsothfeerate;
    }

    public void setCoinsothfeerate(BigDecimal coinsothfeerate) {
        this.coinsothfeerate = coinsothfeerate;
    }

    public BigDecimal getCoinsothfee() {
        return coinsothfee;
    }

    public void setCoinsothfee(BigDecimal coinsothfee) {
        this.coinsothfee = coinsothfee;
    }

    public String getCoinsothfeeacceptind() {
        return coinsothfeeacceptind;
    }

    public void setCoinsothfeeacceptind(String coinsothfeeacceptind) {
        this.coinsothfeeacceptind = coinsothfeeacceptind == null ? null : coinsothfeeacceptind.trim();
    }

    public BigDecimal getCoinscompreserverate() {
        return coinscompreserverate;
    }

    public void setCoinscompreserverate(BigDecimal coinscompreserverate) {
        this.coinscompreserverate = coinscompreserverate;
    }

    public BigDecimal getCoinscompreserve() {
        return coinscompreserve;
    }

    public void setCoinscompreserve(BigDecimal coinscompreserve) {
        this.coinscompreserve = coinscompreserve;
    }

    public String getCoinscompresacceptind() {
        return coinscompresacceptind;
    }

    public void setCoinscompresacceptind(String coinscompresacceptind) {
        this.coinscompresacceptind = coinscompresacceptind == null ? null : coinscompresacceptind.trim();
    }

    public String getDebitaccepter() {
        return debitaccepter;
    }

    public void setDebitaccepter(String debitaccepter) {
        this.debitaccepter = debitaccepter == null ? null : debitaccepter.trim();
    }

    public String getCoinsclaimind() {
        return coinsclaimind;
    }

    public void setCoinsclaimind(String coinsclaimind) {
        this.coinsclaimind = coinsclaimind == null ? null : coinsclaimind.trim();
    }

    public String getSubpolicyno() {
        return subpolicyno;
    }

    public void setSubpolicyno(String subpolicyno) {
        this.subpolicyno = subpolicyno == null ? null : subpolicyno.trim();
    }

    public BigDecimal getCoinsnotaxpremium() {
        return coinsnotaxpremium;
    }

    public void setCoinsnotaxpremium(BigDecimal coinsnotaxpremium) {
        this.coinsnotaxpremium = coinsnotaxpremium;
    }

    public BigDecimal getCoinstaxamount() {
        return coinstaxamount;
    }

    public void setCoinstaxamount(BigDecimal coinstaxamount) {
        this.coinstaxamount = coinstaxamount;
    }

    public String getSecondlevelcoinsname() {
        return secondlevelcoinsname;
    }

    public void setSecondlevelcoinsname(String secondlevelcoinsname) {
        this.secondlevelcoinsname = secondlevelcoinsname == null ? null : secondlevelcoinsname.trim();
    }

    public String getRelatedcoinspolicyno() {
        return relatedcoinspolicyno;
    }

    public void setRelatedcoinspolicyno(String relatedcoinspolicyno) {
        this.relatedcoinspolicyno = relatedcoinspolicyno == null ? null : relatedcoinspolicyno.trim();
    }

    public String getRelatedcoinscode() {
        return relatedcoinscode;
    }

    public void setRelatedcoinscode(String relatedcoinscode) {
        this.relatedcoinscode = relatedcoinscode == null ? null : relatedcoinscode.trim();
    }

    public String getRelatedcoinscname() {
        return relatedcoinscname;
    }

    public void setRelatedcoinscname(String relatedcoinscname) {
        this.relatedcoinscname = relatedcoinscname == null ? null : relatedcoinscname.trim();
    }

    public String getSealsteamcode() {
        return sealsteamcode;
    }

    public void setSealsteamcode(String sealsteamcode) {
        this.sealsteamcode = sealsteamcode == null ? null : sealsteamcode.trim();
    }

    public String getSealsteamcname() {
        return sealsteamcname;
    }

    public void setSealsteamcname(String sealsteamcname) {
        this.sealsteamcname = sealsteamcname == null ? null : sealsteamcname.trim();
    }

    public String getSealsmancode() {
        return sealsmancode;
    }

    public void setSealsmancode(String sealsmancode) {
        this.sealsmancode = sealsmancode == null ? null : sealsmancode.trim();
    }

    public String getSealsmancname() {
        return sealsmancname;
    }

    public void setSealsmancname(String sealsmancname) {
        this.sealsmancname = sealsmancname == null ? null : sealsmancname.trim();
    }

    public BigDecimal getWholerate() {
        return wholerate;
    }

    public void setWholerate(BigDecimal wholerate) {
        this.wholerate = wholerate;
    }

    public String getOrisingleno() {
        return orisingleno;
    }

    public void setOrisingleno(String orisingleno) {
        this.orisingleno = orisingleno == null ? null : orisingleno.trim();
    }

    public String getRelatedcoinstype() {
        return relatedcoinstype;
    }

    public void setRelatedcoinstype(String relatedcoinstype) {
        this.relatedcoinstype = relatedcoinstype == null ? null : relatedcoinstype.trim();
    }

    public String getChanneldetailcode() {
        return channeldetailcode;
    }

    public void setChanneldetailcode(String channeldetailcode) {
        this.channeldetailcode = channeldetailcode == null ? null : channeldetailcode.trim();
    }

    public String getChanneldetailcname() {
        return channeldetailcname;
    }

    public void setChanneldetailcname(String channeldetailcname) {
        this.channeldetailcname = channeldetailcname == null ? null : channeldetailcname.trim();
    }

    public BigDecimal getCoinsissueexpensetax() {
        return coinsissueexpensetax;
    }

    public void setCoinsissueexpensetax(BigDecimal coinsissueexpensetax) {
        this.coinsissueexpensetax = coinsissueexpensetax;
    }

    public BigDecimal getCoinsothfeetax() {
        return coinsothfeetax;
    }

    public void setCoinsothfeetax(BigDecimal coinsothfeetax) {
        this.coinsothfeetax = coinsothfeetax;
    }

    public BigDecimal getCoinsinsuredcny() {
        return coinsinsuredcny;
    }

    public void setCoinsinsuredcny(BigDecimal coinsinsuredcny) {
        this.coinsinsuredcny = coinsinsuredcny;
    }

    public BigDecimal getCoinspremiumcny() {
        return coinspremiumcny;
    }

    public void setCoinspremiumcny(BigDecimal coinspremiumcny) {
        this.coinspremiumcny = coinspremiumcny;
    }

    public BigDecimal getCoinsnotaxpremiumcny() {
        return coinsnotaxpremiumcny;
    }

    public void setCoinsnotaxpremiumcny(BigDecimal coinsnotaxpremiumcny) {
        this.coinsnotaxpremiumcny = coinsnotaxpremiumcny;
    }

    public BigDecimal getCoinstaxamountcny() {
        return coinstaxamountcny;
    }

    public void setCoinstaxamountcny(BigDecimal coinstaxamountcny) {
        this.coinstaxamountcny = coinstaxamountcny;
    }

    public String getCurrencycny() {
        return currencycny;
    }

    public void setCurrencycny(String currencycny) {
        this.currencycny = currencycny == null ? null : currencycny.trim();
    }

    public Integer getSerialno() {
        return serialno;
    }

    public void setSerialno(Integer serialno) {
        this.serialno = serialno;
    }
}