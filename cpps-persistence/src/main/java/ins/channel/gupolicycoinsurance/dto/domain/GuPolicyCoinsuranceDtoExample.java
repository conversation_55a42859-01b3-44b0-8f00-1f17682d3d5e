package ins.channel.gupolicycoinsurance.dto.domain;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class GuPolicyCoinsuranceDtoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public GuPolicyCoinsuranceDtoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPolicynoIsNull() {
            addCriterion("POLICYNO is null");
            return (Criteria) this;
        }

        public Criteria andPolicynoIsNotNull() {
            addCriterion("POLICYNO is not null");
            return (Criteria) this;
        }

        public Criteria andPolicynoEqualTo(String value) {
            addCriterion("POLICYNO =", value, "policyno");
            return (Criteria) this;
        }

        public Criteria andPolicynoNotEqualTo(String value) {
            addCriterion("POLICYNO <>", value, "policyno");
            return (Criteria) this;
        }

        public Criteria andPolicynoGreaterThan(String value) {
            addCriterion("POLICYNO >", value, "policyno");
            return (Criteria) this;
        }

        public Criteria andPolicynoGreaterThanOrEqualTo(String value) {
            addCriterion("POLICYNO >=", value, "policyno");
            return (Criteria) this;
        }

        public Criteria andPolicynoLessThan(String value) {
            addCriterion("POLICYNO <", value, "policyno");
            return (Criteria) this;
        }

        public Criteria andPolicynoLessThanOrEqualTo(String value) {
            addCriterion("POLICYNO <=", value, "policyno");
            return (Criteria) this;
        }

        public Criteria andPolicynoLike(String value) {
            addCriterion("POLICYNO like", value, "policyno");
            return (Criteria) this;
        }

        public Criteria andPolicynoNotLike(String value) {
            addCriterion("POLICYNO not like", value, "policyno");
            return (Criteria) this;
        }

        public Criteria andPolicynoIn(List<String> values) {
            addCriterion("POLICYNO in", values, "policyno");
            return (Criteria) this;
        }

        public Criteria andPolicynoNotIn(List<String> values) {
            addCriterion("POLICYNO not in", values, "policyno");
            return (Criteria) this;
        }

        public Criteria andPolicynoBetween(String value1, String value2) {
            addCriterion("POLICYNO between", value1, value2, "policyno");
            return (Criteria) this;
        }

        public Criteria andPolicynoNotBetween(String value1, String value2) {
            addCriterion("POLICYNO not between", value1, value2, "policyno");
            return (Criteria) this;
        }

        public Criteria andCoinscodeIsNull() {
            addCriterion("COINSCODE is null");
            return (Criteria) this;
        }

        public Criteria andCoinscodeIsNotNull() {
            addCriterion("COINSCODE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinscodeEqualTo(String value) {
            addCriterion("COINSCODE =", value, "coinscode");
            return (Criteria) this;
        }

        public Criteria andCoinscodeNotEqualTo(String value) {
            addCriterion("COINSCODE <>", value, "coinscode");
            return (Criteria) this;
        }

        public Criteria andCoinscodeGreaterThan(String value) {
            addCriterion("COINSCODE >", value, "coinscode");
            return (Criteria) this;
        }

        public Criteria andCoinscodeGreaterThanOrEqualTo(String value) {
            addCriterion("COINSCODE >=", value, "coinscode");
            return (Criteria) this;
        }

        public Criteria andCoinscodeLessThan(String value) {
            addCriterion("COINSCODE <", value, "coinscode");
            return (Criteria) this;
        }

        public Criteria andCoinscodeLessThanOrEqualTo(String value) {
            addCriterion("COINSCODE <=", value, "coinscode");
            return (Criteria) this;
        }

        public Criteria andCoinscodeLike(String value) {
            addCriterion("COINSCODE like", value, "coinscode");
            return (Criteria) this;
        }

        public Criteria andCoinscodeNotLike(String value) {
            addCriterion("COINSCODE not like", value, "coinscode");
            return (Criteria) this;
        }

        public Criteria andCoinscodeIn(List<String> values) {
            addCriterion("COINSCODE in", values, "coinscode");
            return (Criteria) this;
        }

        public Criteria andCoinscodeNotIn(List<String> values) {
            addCriterion("COINSCODE not in", values, "coinscode");
            return (Criteria) this;
        }

        public Criteria andCoinscodeBetween(String value1, String value2) {
            addCriterion("COINSCODE between", value1, value2, "coinscode");
            return (Criteria) this;
        }

        public Criteria andCoinscodeNotBetween(String value1, String value2) {
            addCriterion("COINSCODE not between", value1, value2, "coinscode");
            return (Criteria) this;
        }

        public Criteria andPlancodeIsNull() {
            addCriterion("PLANCODE is null");
            return (Criteria) this;
        }

        public Criteria andPlancodeIsNotNull() {
            addCriterion("PLANCODE is not null");
            return (Criteria) this;
        }

        public Criteria andPlancodeEqualTo(String value) {
            addCriterion("PLANCODE =", value, "plancode");
            return (Criteria) this;
        }

        public Criteria andPlancodeNotEqualTo(String value) {
            addCriterion("PLANCODE <>", value, "plancode");
            return (Criteria) this;
        }

        public Criteria andPlancodeGreaterThan(String value) {
            addCriterion("PLANCODE >", value, "plancode");
            return (Criteria) this;
        }

        public Criteria andPlancodeGreaterThanOrEqualTo(String value) {
            addCriterion("PLANCODE >=", value, "plancode");
            return (Criteria) this;
        }

        public Criteria andPlancodeLessThan(String value) {
            addCriterion("PLANCODE <", value, "plancode");
            return (Criteria) this;
        }

        public Criteria andPlancodeLessThanOrEqualTo(String value) {
            addCriterion("PLANCODE <=", value, "plancode");
            return (Criteria) this;
        }

        public Criteria andPlancodeLike(String value) {
            addCriterion("PLANCODE like", value, "plancode");
            return (Criteria) this;
        }

        public Criteria andPlancodeNotLike(String value) {
            addCriterion("PLANCODE not like", value, "plancode");
            return (Criteria) this;
        }

        public Criteria andPlancodeIn(List<String> values) {
            addCriterion("PLANCODE in", values, "plancode");
            return (Criteria) this;
        }

        public Criteria andPlancodeNotIn(List<String> values) {
            addCriterion("PLANCODE not in", values, "plancode");
            return (Criteria) this;
        }

        public Criteria andPlancodeBetween(String value1, String value2) {
            addCriterion("PLANCODE between", value1, value2, "plancode");
            return (Criteria) this;
        }

        public Criteria andPlancodeNotBetween(String value1, String value2) {
            addCriterion("PLANCODE not between", value1, value2, "plancode");
            return (Criteria) this;
        }

        public Criteria andRiskcodeIsNull() {
            addCriterion("RISKCODE is null");
            return (Criteria) this;
        }

        public Criteria andRiskcodeIsNotNull() {
            addCriterion("RISKCODE is not null");
            return (Criteria) this;
        }

        public Criteria andRiskcodeEqualTo(String value) {
            addCriterion("RISKCODE =", value, "riskcode");
            return (Criteria) this;
        }

        public Criteria andRiskcodeNotEqualTo(String value) {
            addCriterion("RISKCODE <>", value, "riskcode");
            return (Criteria) this;
        }

        public Criteria andRiskcodeGreaterThan(String value) {
            addCriterion("RISKCODE >", value, "riskcode");
            return (Criteria) this;
        }

        public Criteria andRiskcodeGreaterThanOrEqualTo(String value) {
            addCriterion("RISKCODE >=", value, "riskcode");
            return (Criteria) this;
        }

        public Criteria andRiskcodeLessThan(String value) {
            addCriterion("RISKCODE <", value, "riskcode");
            return (Criteria) this;
        }

        public Criteria andRiskcodeLessThanOrEqualTo(String value) {
            addCriterion("RISKCODE <=", value, "riskcode");
            return (Criteria) this;
        }

        public Criteria andRiskcodeLike(String value) {
            addCriterion("RISKCODE like", value, "riskcode");
            return (Criteria) this;
        }

        public Criteria andRiskcodeNotLike(String value) {
            addCriterion("RISKCODE not like", value, "riskcode");
            return (Criteria) this;
        }

        public Criteria andRiskcodeIn(List<String> values) {
            addCriterion("RISKCODE in", values, "riskcode");
            return (Criteria) this;
        }

        public Criteria andRiskcodeNotIn(List<String> values) {
            addCriterion("RISKCODE not in", values, "riskcode");
            return (Criteria) this;
        }

        public Criteria andRiskcodeBetween(String value1, String value2) {
            addCriterion("RISKCODE between", value1, value2, "riskcode");
            return (Criteria) this;
        }

        public Criteria andRiskcodeNotBetween(String value1, String value2) {
            addCriterion("RISKCODE not between", value1, value2, "riskcode");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeIsNull() {
            addCriterion("SECONDLEVELCOINSCODE is null");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeIsNotNull() {
            addCriterion("SECONDLEVELCOINSCODE is not null");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeEqualTo(String value) {
            addCriterion("SECONDLEVELCOINSCODE =", value, "secondlevelcoinscode");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeNotEqualTo(String value) {
            addCriterion("SECONDLEVELCOINSCODE <>", value, "secondlevelcoinscode");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeGreaterThan(String value) {
            addCriterion("SECONDLEVELCOINSCODE >", value, "secondlevelcoinscode");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeGreaterThanOrEqualTo(String value) {
            addCriterion("SECONDLEVELCOINSCODE >=", value, "secondlevelcoinscode");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeLessThan(String value) {
            addCriterion("SECONDLEVELCOINSCODE <", value, "secondlevelcoinscode");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeLessThanOrEqualTo(String value) {
            addCriterion("SECONDLEVELCOINSCODE <=", value, "secondlevelcoinscode");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeLike(String value) {
            addCriterion("SECONDLEVELCOINSCODE like", value, "secondlevelcoinscode");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeNotLike(String value) {
            addCriterion("SECONDLEVELCOINSCODE not like", value, "secondlevelcoinscode");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeIn(List<String> values) {
            addCriterion("SECONDLEVELCOINSCODE in", values, "secondlevelcoinscode");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeNotIn(List<String> values) {
            addCriterion("SECONDLEVELCOINSCODE not in", values, "secondlevelcoinscode");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeBetween(String value1, String value2) {
            addCriterion("SECONDLEVELCOINSCODE between", value1, value2, "secondlevelcoinscode");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinscodeNotBetween(String value1, String value2) {
            addCriterion("SECONDLEVELCOINSCODE not between", value1, value2, "secondlevelcoinscode");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameIsNull() {
            addCriterion("THIRDLEVELCOINSNAME is null");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameIsNotNull() {
            addCriterion("THIRDLEVELCOINSNAME is not null");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameEqualTo(String value) {
            addCriterion("THIRDLEVELCOINSNAME =", value, "thirdlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameNotEqualTo(String value) {
            addCriterion("THIRDLEVELCOINSNAME <>", value, "thirdlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameGreaterThan(String value) {
            addCriterion("THIRDLEVELCOINSNAME >", value, "thirdlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameGreaterThanOrEqualTo(String value) {
            addCriterion("THIRDLEVELCOINSNAME >=", value, "thirdlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameLessThan(String value) {
            addCriterion("THIRDLEVELCOINSNAME <", value, "thirdlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameLessThanOrEqualTo(String value) {
            addCriterion("THIRDLEVELCOINSNAME <=", value, "thirdlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameLike(String value) {
            addCriterion("THIRDLEVELCOINSNAME like", value, "thirdlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameNotLike(String value) {
            addCriterion("THIRDLEVELCOINSNAME not like", value, "thirdlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameIn(List<String> values) {
            addCriterion("THIRDLEVELCOINSNAME in", values, "thirdlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameNotIn(List<String> values) {
            addCriterion("THIRDLEVELCOINSNAME not in", values, "thirdlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameBetween(String value1, String value2) {
            addCriterion("THIRDLEVELCOINSNAME between", value1, value2, "thirdlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andThirdlevelcoinsnameNotBetween(String value1, String value2) {
            addCriterion("THIRDLEVELCOINSNAME not between", value1, value2, "thirdlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoIsNull() {
            addCriterion("COINSPOLICYNO is null");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoIsNotNull() {
            addCriterion("COINSPOLICYNO is not null");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoEqualTo(String value) {
            addCriterion("COINSPOLICYNO =", value, "coinspolicyno");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoNotEqualTo(String value) {
            addCriterion("COINSPOLICYNO <>", value, "coinspolicyno");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoGreaterThan(String value) {
            addCriterion("COINSPOLICYNO >", value, "coinspolicyno");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoGreaterThanOrEqualTo(String value) {
            addCriterion("COINSPOLICYNO >=", value, "coinspolicyno");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoLessThan(String value) {
            addCriterion("COINSPOLICYNO <", value, "coinspolicyno");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoLessThanOrEqualTo(String value) {
            addCriterion("COINSPOLICYNO <=", value, "coinspolicyno");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoLike(String value) {
            addCriterion("COINSPOLICYNO like", value, "coinspolicyno");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoNotLike(String value) {
            addCriterion("COINSPOLICYNO not like", value, "coinspolicyno");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoIn(List<String> values) {
            addCriterion("COINSPOLICYNO in", values, "coinspolicyno");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoNotIn(List<String> values) {
            addCriterion("COINSPOLICYNO not in", values, "coinspolicyno");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoBetween(String value1, String value2) {
            addCriterion("COINSPOLICYNO between", value1, value2, "coinspolicyno");
            return (Criteria) this;
        }

        public Criteria andCoinspolicynoNotBetween(String value1, String value2) {
            addCriterion("COINSPOLICYNO not between", value1, value2, "coinspolicyno");
            return (Criteria) this;
        }

        public Criteria andCoinsnameIsNull() {
            addCriterion("COINSNAME is null");
            return (Criteria) this;
        }

        public Criteria andCoinsnameIsNotNull() {
            addCriterion("COINSNAME is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsnameEqualTo(String value) {
            addCriterion("COINSNAME =", value, "coinsname");
            return (Criteria) this;
        }

        public Criteria andCoinsnameNotEqualTo(String value) {
            addCriterion("COINSNAME <>", value, "coinsname");
            return (Criteria) this;
        }

        public Criteria andCoinsnameGreaterThan(String value) {
            addCriterion("COINSNAME >", value, "coinsname");
            return (Criteria) this;
        }

        public Criteria andCoinsnameGreaterThanOrEqualTo(String value) {
            addCriterion("COINSNAME >=", value, "coinsname");
            return (Criteria) this;
        }

        public Criteria andCoinsnameLessThan(String value) {
            addCriterion("COINSNAME <", value, "coinsname");
            return (Criteria) this;
        }

        public Criteria andCoinsnameLessThanOrEqualTo(String value) {
            addCriterion("COINSNAME <=", value, "coinsname");
            return (Criteria) this;
        }

        public Criteria andCoinsnameLike(String value) {
            addCriterion("COINSNAME like", value, "coinsname");
            return (Criteria) this;
        }

        public Criteria andCoinsnameNotLike(String value) {
            addCriterion("COINSNAME not like", value, "coinsname");
            return (Criteria) this;
        }

        public Criteria andCoinsnameIn(List<String> values) {
            addCriterion("COINSNAME in", values, "coinsname");
            return (Criteria) this;
        }

        public Criteria andCoinsnameNotIn(List<String> values) {
            addCriterion("COINSNAME not in", values, "coinsname");
            return (Criteria) this;
        }

        public Criteria andCoinsnameBetween(String value1, String value2) {
            addCriterion("COINSNAME between", value1, value2, "coinsname");
            return (Criteria) this;
        }

        public Criteria andCoinsnameNotBetween(String value1, String value2) {
            addCriterion("COINSNAME not between", value1, value2, "coinsname");
            return (Criteria) this;
        }

        public Criteria andCoinstypeIsNull() {
            addCriterion("COINSTYPE is null");
            return (Criteria) this;
        }

        public Criteria andCoinstypeIsNotNull() {
            addCriterion("COINSTYPE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinstypeEqualTo(String value) {
            addCriterion("COINSTYPE =", value, "coinstype");
            return (Criteria) this;
        }

        public Criteria andCoinstypeNotEqualTo(String value) {
            addCriterion("COINSTYPE <>", value, "coinstype");
            return (Criteria) this;
        }

        public Criteria andCoinstypeGreaterThan(String value) {
            addCriterion("COINSTYPE >", value, "coinstype");
            return (Criteria) this;
        }

        public Criteria andCoinstypeGreaterThanOrEqualTo(String value) {
            addCriterion("COINSTYPE >=", value, "coinstype");
            return (Criteria) this;
        }

        public Criteria andCoinstypeLessThan(String value) {
            addCriterion("COINSTYPE <", value, "coinstype");
            return (Criteria) this;
        }

        public Criteria andCoinstypeLessThanOrEqualTo(String value) {
            addCriterion("COINSTYPE <=", value, "coinstype");
            return (Criteria) this;
        }

        public Criteria andCoinstypeLike(String value) {
            addCriterion("COINSTYPE like", value, "coinstype");
            return (Criteria) this;
        }

        public Criteria andCoinstypeNotLike(String value) {
            addCriterion("COINSTYPE not like", value, "coinstype");
            return (Criteria) this;
        }

        public Criteria andCoinstypeIn(List<String> values) {
            addCriterion("COINSTYPE in", values, "coinstype");
            return (Criteria) this;
        }

        public Criteria andCoinstypeNotIn(List<String> values) {
            addCriterion("COINSTYPE not in", values, "coinstype");
            return (Criteria) this;
        }

        public Criteria andCoinstypeBetween(String value1, String value2) {
            addCriterion("COINSTYPE between", value1, value2, "coinstype");
            return (Criteria) this;
        }

        public Criteria andCoinstypeNotBetween(String value1, String value2) {
            addCriterion("COINSTYPE not between", value1, value2, "coinstype");
            return (Criteria) this;
        }

        public Criteria andCoinsrateIsNull() {
            addCriterion("COINSRATE is null");
            return (Criteria) this;
        }

        public Criteria andCoinsrateIsNotNull() {
            addCriterion("COINSRATE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsrateEqualTo(BigDecimal value) {
            addCriterion("COINSRATE =", value, "coinsrate");
            return (Criteria) this;
        }

        public Criteria andCoinsrateNotEqualTo(BigDecimal value) {
            addCriterion("COINSRATE <>", value, "coinsrate");
            return (Criteria) this;
        }

        public Criteria andCoinsrateGreaterThan(BigDecimal value) {
            addCriterion("COINSRATE >", value, "coinsrate");
            return (Criteria) this;
        }

        public Criteria andCoinsrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSRATE >=", value, "coinsrate");
            return (Criteria) this;
        }

        public Criteria andCoinsrateLessThan(BigDecimal value) {
            addCriterion("COINSRATE <", value, "coinsrate");
            return (Criteria) this;
        }

        public Criteria andCoinsrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSRATE <=", value, "coinsrate");
            return (Criteria) this;
        }

        public Criteria andCoinsrateIn(List<BigDecimal> values) {
            addCriterion("COINSRATE in", values, "coinsrate");
            return (Criteria) this;
        }

        public Criteria andCoinsrateNotIn(List<BigDecimal> values) {
            addCriterion("COINSRATE not in", values, "coinsrate");
            return (Criteria) this;
        }

        public Criteria andCoinsrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSRATE between", value1, value2, "coinsrate");
            return (Criteria) this;
        }

        public Criteria andCoinsrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSRATE not between", value1, value2, "coinsrate");
            return (Criteria) this;
        }

        public Criteria andPrincipalindIsNull() {
            addCriterion("PRINCIPALIND is null");
            return (Criteria) this;
        }

        public Criteria andPrincipalindIsNotNull() {
            addCriterion("PRINCIPALIND is not null");
            return (Criteria) this;
        }

        public Criteria andPrincipalindEqualTo(String value) {
            addCriterion("PRINCIPALIND =", value, "principalind");
            return (Criteria) this;
        }

        public Criteria andPrincipalindNotEqualTo(String value) {
            addCriterion("PRINCIPALIND <>", value, "principalind");
            return (Criteria) this;
        }

        public Criteria andPrincipalindGreaterThan(String value) {
            addCriterion("PRINCIPALIND >", value, "principalind");
            return (Criteria) this;
        }

        public Criteria andPrincipalindGreaterThanOrEqualTo(String value) {
            addCriterion("PRINCIPALIND >=", value, "principalind");
            return (Criteria) this;
        }

        public Criteria andPrincipalindLessThan(String value) {
            addCriterion("PRINCIPALIND <", value, "principalind");
            return (Criteria) this;
        }

        public Criteria andPrincipalindLessThanOrEqualTo(String value) {
            addCriterion("PRINCIPALIND <=", value, "principalind");
            return (Criteria) this;
        }

        public Criteria andPrincipalindLike(String value) {
            addCriterion("PRINCIPALIND like", value, "principalind");
            return (Criteria) this;
        }

        public Criteria andPrincipalindNotLike(String value) {
            addCriterion("PRINCIPALIND not like", value, "principalind");
            return (Criteria) this;
        }

        public Criteria andPrincipalindIn(List<String> values) {
            addCriterion("PRINCIPALIND in", values, "principalind");
            return (Criteria) this;
        }

        public Criteria andPrincipalindNotIn(List<String> values) {
            addCriterion("PRINCIPALIND not in", values, "principalind");
            return (Criteria) this;
        }

        public Criteria andPrincipalindBetween(String value1, String value2) {
            addCriterion("PRINCIPALIND between", value1, value2, "principalind");
            return (Criteria) this;
        }

        public Criteria andPrincipalindNotBetween(String value1, String value2) {
            addCriterion("PRINCIPALIND not between", value1, value2, "principalind");
            return (Criteria) this;
        }

        public Criteria andLeaderindIsNull() {
            addCriterion("LEADERIND is null");
            return (Criteria) this;
        }

        public Criteria andLeaderindIsNotNull() {
            addCriterion("LEADERIND is not null");
            return (Criteria) this;
        }

        public Criteria andLeaderindEqualTo(String value) {
            addCriterion("LEADERIND =", value, "leaderind");
            return (Criteria) this;
        }

        public Criteria andLeaderindNotEqualTo(String value) {
            addCriterion("LEADERIND <>", value, "leaderind");
            return (Criteria) this;
        }

        public Criteria andLeaderindGreaterThan(String value) {
            addCriterion("LEADERIND >", value, "leaderind");
            return (Criteria) this;
        }

        public Criteria andLeaderindGreaterThanOrEqualTo(String value) {
            addCriterion("LEADERIND >=", value, "leaderind");
            return (Criteria) this;
        }

        public Criteria andLeaderindLessThan(String value) {
            addCriterion("LEADERIND <", value, "leaderind");
            return (Criteria) this;
        }

        public Criteria andLeaderindLessThanOrEqualTo(String value) {
            addCriterion("LEADERIND <=", value, "leaderind");
            return (Criteria) this;
        }

        public Criteria andLeaderindLike(String value) {
            addCriterion("LEADERIND like", value, "leaderind");
            return (Criteria) this;
        }

        public Criteria andLeaderindNotLike(String value) {
            addCriterion("LEADERIND not like", value, "leaderind");
            return (Criteria) this;
        }

        public Criteria andLeaderindIn(List<String> values) {
            addCriterion("LEADERIND in", values, "leaderind");
            return (Criteria) this;
        }

        public Criteria andLeaderindNotIn(List<String> values) {
            addCriterion("LEADERIND not in", values, "leaderind");
            return (Criteria) this;
        }

        public Criteria andLeaderindBetween(String value1, String value2) {
            addCriterion("LEADERIND between", value1, value2, "leaderind");
            return (Criteria) this;
        }

        public Criteria andLeaderindNotBetween(String value1, String value2) {
            addCriterion("LEADERIND not between", value1, value2, "leaderind");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindIsNull() {
            addCriterion("CHIEFADJUSTERIND is null");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindIsNotNull() {
            addCriterion("CHIEFADJUSTERIND is not null");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindEqualTo(String value) {
            addCriterion("CHIEFADJUSTERIND =", value, "chiefadjusterind");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindNotEqualTo(String value) {
            addCriterion("CHIEFADJUSTERIND <>", value, "chiefadjusterind");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindGreaterThan(String value) {
            addCriterion("CHIEFADJUSTERIND >", value, "chiefadjusterind");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindGreaterThanOrEqualTo(String value) {
            addCriterion("CHIEFADJUSTERIND >=", value, "chiefadjusterind");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindLessThan(String value) {
            addCriterion("CHIEFADJUSTERIND <", value, "chiefadjusterind");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindLessThanOrEqualTo(String value) {
            addCriterion("CHIEFADJUSTERIND <=", value, "chiefadjusterind");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindLike(String value) {
            addCriterion("CHIEFADJUSTERIND like", value, "chiefadjusterind");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindNotLike(String value) {
            addCriterion("CHIEFADJUSTERIND not like", value, "chiefadjusterind");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindIn(List<String> values) {
            addCriterion("CHIEFADJUSTERIND in", values, "chiefadjusterind");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindNotIn(List<String> values) {
            addCriterion("CHIEFADJUSTERIND not in", values, "chiefadjusterind");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindBetween(String value1, String value2) {
            addCriterion("CHIEFADJUSTERIND between", value1, value2, "chiefadjusterind");
            return (Criteria) this;
        }

        public Criteria andChiefadjusterindNotBetween(String value1, String value2) {
            addCriterion("CHIEFADJUSTERIND not between", value1, value2, "chiefadjusterind");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("CURRENCY is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("CURRENCY is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("CURRENCY =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("CURRENCY <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("CURRENCY >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("CURRENCY >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("CURRENCY <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("CURRENCY <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("CURRENCY like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("CURRENCY not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("CURRENCY in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("CURRENCY not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("CURRENCY between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("CURRENCY not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredIsNull() {
            addCriterion("COINSINSURED is null");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredIsNotNull() {
            addCriterion("COINSINSURED is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredEqualTo(BigDecimal value) {
            addCriterion("COINSINSURED =", value, "coinsinsured");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredNotEqualTo(BigDecimal value) {
            addCriterion("COINSINSURED <>", value, "coinsinsured");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredGreaterThan(BigDecimal value) {
            addCriterion("COINSINSURED >", value, "coinsinsured");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSINSURED >=", value, "coinsinsured");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredLessThan(BigDecimal value) {
            addCriterion("COINSINSURED <", value, "coinsinsured");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSINSURED <=", value, "coinsinsured");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredIn(List<BigDecimal> values) {
            addCriterion("COINSINSURED in", values, "coinsinsured");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredNotIn(List<BigDecimal> values) {
            addCriterion("COINSINSURED not in", values, "coinsinsured");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSINSURED between", value1, value2, "coinsinsured");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSINSURED not between", value1, value2, "coinsinsured");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumIsNull() {
            addCriterion("COINSPREMIUM is null");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumIsNotNull() {
            addCriterion("COINSPREMIUM is not null");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumEqualTo(BigDecimal value) {
            addCriterion("COINSPREMIUM =", value, "coinspremium");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumNotEqualTo(BigDecimal value) {
            addCriterion("COINSPREMIUM <>", value, "coinspremium");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumGreaterThan(BigDecimal value) {
            addCriterion("COINSPREMIUM >", value, "coinspremium");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSPREMIUM >=", value, "coinspremium");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumLessThan(BigDecimal value) {
            addCriterion("COINSPREMIUM <", value, "coinspremium");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSPREMIUM <=", value, "coinspremium");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumIn(List<BigDecimal> values) {
            addCriterion("COINSPREMIUM in", values, "coinspremium");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumNotIn(List<BigDecimal> values) {
            addCriterion("COINSPREMIUM not in", values, "coinspremium");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSPREMIUM between", value1, value2, "coinspremium");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSPREMIUM not between", value1, value2, "coinspremium");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingrateIsNull() {
            addCriterion("COINSHANDLINGRATE is null");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingrateIsNotNull() {
            addCriterion("COINSHANDLINGRATE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingrateEqualTo(BigDecimal value) {
            addCriterion("COINSHANDLINGRATE =", value, "coinshandlingrate");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingrateNotEqualTo(BigDecimal value) {
            addCriterion("COINSHANDLINGRATE <>", value, "coinshandlingrate");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingrateGreaterThan(BigDecimal value) {
            addCriterion("COINSHANDLINGRATE >", value, "coinshandlingrate");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSHANDLINGRATE >=", value, "coinshandlingrate");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingrateLessThan(BigDecimal value) {
            addCriterion("COINSHANDLINGRATE <", value, "coinshandlingrate");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSHANDLINGRATE <=", value, "coinshandlingrate");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingrateIn(List<BigDecimal> values) {
            addCriterion("COINSHANDLINGRATE in", values, "coinshandlingrate");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingrateNotIn(List<BigDecimal> values) {
            addCriterion("COINSHANDLINGRATE not in", values, "coinshandlingrate");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSHANDLINGRATE between", value1, value2, "coinshandlingrate");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSHANDLINGRATE not between", value1, value2, "coinshandlingrate");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingfeeIsNull() {
            addCriterion("COINSHANDLINGFEE is null");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingfeeIsNotNull() {
            addCriterion("COINSHANDLINGFEE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingfeeEqualTo(BigDecimal value) {
            addCriterion("COINSHANDLINGFEE =", value, "coinshandlingfee");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingfeeNotEqualTo(BigDecimal value) {
            addCriterion("COINSHANDLINGFEE <>", value, "coinshandlingfee");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingfeeGreaterThan(BigDecimal value) {
            addCriterion("COINSHANDLINGFEE >", value, "coinshandlingfee");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingfeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSHANDLINGFEE >=", value, "coinshandlingfee");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingfeeLessThan(BigDecimal value) {
            addCriterion("COINSHANDLINGFEE <", value, "coinshandlingfee");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingfeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSHANDLINGFEE <=", value, "coinshandlingfee");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingfeeIn(List<BigDecimal> values) {
            addCriterion("COINSHANDLINGFEE in", values, "coinshandlingfee");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingfeeNotIn(List<BigDecimal> values) {
            addCriterion("COINSHANDLINGFEE not in", values, "coinshandlingfee");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingfeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSHANDLINGFEE between", value1, value2, "coinshandlingfee");
            return (Criteria) this;
        }

        public Criteria andCoinshandlingfeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSHANDLINGFEE not between", value1, value2, "coinshandlingfee");
            return (Criteria) this;
        }

        public Criteria andCoinsagencyrateIsNull() {
            addCriterion("COINSAGENCYRATE is null");
            return (Criteria) this;
        }

        public Criteria andCoinsagencyrateIsNotNull() {
            addCriterion("COINSAGENCYRATE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsagencyrateEqualTo(BigDecimal value) {
            addCriterion("COINSAGENCYRATE =", value, "coinsagencyrate");
            return (Criteria) this;
        }

        public Criteria andCoinsagencyrateNotEqualTo(BigDecimal value) {
            addCriterion("COINSAGENCYRATE <>", value, "coinsagencyrate");
            return (Criteria) this;
        }

        public Criteria andCoinsagencyrateGreaterThan(BigDecimal value) {
            addCriterion("COINSAGENCYRATE >", value, "coinsagencyrate");
            return (Criteria) this;
        }

        public Criteria andCoinsagencyrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSAGENCYRATE >=", value, "coinsagencyrate");
            return (Criteria) this;
        }

        public Criteria andCoinsagencyrateLessThan(BigDecimal value) {
            addCriterion("COINSAGENCYRATE <", value, "coinsagencyrate");
            return (Criteria) this;
        }

        public Criteria andCoinsagencyrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSAGENCYRATE <=", value, "coinsagencyrate");
            return (Criteria) this;
        }

        public Criteria andCoinsagencyrateIn(List<BigDecimal> values) {
            addCriterion("COINSAGENCYRATE in", values, "coinsagencyrate");
            return (Criteria) this;
        }

        public Criteria andCoinsagencyrateNotIn(List<BigDecimal> values) {
            addCriterion("COINSAGENCYRATE not in", values, "coinsagencyrate");
            return (Criteria) this;
        }

        public Criteria andCoinsagencyrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSAGENCYRATE between", value1, value2, "coinsagencyrate");
            return (Criteria) this;
        }

        public Criteria andCoinsagencyrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSAGENCYRATE not between", value1, value2, "coinsagencyrate");
            return (Criteria) this;
        }

        public Criteria andCoinsagencycommissionIsNull() {
            addCriterion("COINSAGENCYCOMMISSION is null");
            return (Criteria) this;
        }

        public Criteria andCoinsagencycommissionIsNotNull() {
            addCriterion("COINSAGENCYCOMMISSION is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsagencycommissionEqualTo(BigDecimal value) {
            addCriterion("COINSAGENCYCOMMISSION =", value, "coinsagencycommission");
            return (Criteria) this;
        }

        public Criteria andCoinsagencycommissionNotEqualTo(BigDecimal value) {
            addCriterion("COINSAGENCYCOMMISSION <>", value, "coinsagencycommission");
            return (Criteria) this;
        }

        public Criteria andCoinsagencycommissionGreaterThan(BigDecimal value) {
            addCriterion("COINSAGENCYCOMMISSION >", value, "coinsagencycommission");
            return (Criteria) this;
        }

        public Criteria andCoinsagencycommissionGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSAGENCYCOMMISSION >=", value, "coinsagencycommission");
            return (Criteria) this;
        }

        public Criteria andCoinsagencycommissionLessThan(BigDecimal value) {
            addCriterion("COINSAGENCYCOMMISSION <", value, "coinsagencycommission");
            return (Criteria) this;
        }

        public Criteria andCoinsagencycommissionLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSAGENCYCOMMISSION <=", value, "coinsagencycommission");
            return (Criteria) this;
        }

        public Criteria andCoinsagencycommissionIn(List<BigDecimal> values) {
            addCriterion("COINSAGENCYCOMMISSION in", values, "coinsagencycommission");
            return (Criteria) this;
        }

        public Criteria andCoinsagencycommissionNotIn(List<BigDecimal> values) {
            addCriterion("COINSAGENCYCOMMISSION not in", values, "coinsagencycommission");
            return (Criteria) this;
        }

        public Criteria andCoinsagencycommissionBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSAGENCYCOMMISSION between", value1, value2, "coinsagencycommission");
            return (Criteria) this;
        }

        public Criteria andCoinsagencycommissionNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSAGENCYCOMMISSION not between", value1, value2, "coinsagencycommission");
            return (Criteria) this;
        }

        public Criteria andCoinsissuerateIsNull() {
            addCriterion("COINSISSUERATE is null");
            return (Criteria) this;
        }

        public Criteria andCoinsissuerateIsNotNull() {
            addCriterion("COINSISSUERATE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsissuerateEqualTo(BigDecimal value) {
            addCriterion("COINSISSUERATE =", value, "coinsissuerate");
            return (Criteria) this;
        }

        public Criteria andCoinsissuerateNotEqualTo(BigDecimal value) {
            addCriterion("COINSISSUERATE <>", value, "coinsissuerate");
            return (Criteria) this;
        }

        public Criteria andCoinsissuerateGreaterThan(BigDecimal value) {
            addCriterion("COINSISSUERATE >", value, "coinsissuerate");
            return (Criteria) this;
        }

        public Criteria andCoinsissuerateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSISSUERATE >=", value, "coinsissuerate");
            return (Criteria) this;
        }

        public Criteria andCoinsissuerateLessThan(BigDecimal value) {
            addCriterion("COINSISSUERATE <", value, "coinsissuerate");
            return (Criteria) this;
        }

        public Criteria andCoinsissuerateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSISSUERATE <=", value, "coinsissuerate");
            return (Criteria) this;
        }

        public Criteria andCoinsissuerateIn(List<BigDecimal> values) {
            addCriterion("COINSISSUERATE in", values, "coinsissuerate");
            return (Criteria) this;
        }

        public Criteria andCoinsissuerateNotIn(List<BigDecimal> values) {
            addCriterion("COINSISSUERATE not in", values, "coinsissuerate");
            return (Criteria) this;
        }

        public Criteria andCoinsissuerateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSISSUERATE between", value1, value2, "coinsissuerate");
            return (Criteria) this;
        }

        public Criteria andCoinsissuerateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSISSUERATE not between", value1, value2, "coinsissuerate");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpenseIsNull() {
            addCriterion("COINSISSUEEXPENSE is null");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpenseIsNotNull() {
            addCriterion("COINSISSUEEXPENSE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpenseEqualTo(BigDecimal value) {
            addCriterion("COINSISSUEEXPENSE =", value, "coinsissueexpense");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpenseNotEqualTo(BigDecimal value) {
            addCriterion("COINSISSUEEXPENSE <>", value, "coinsissueexpense");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpenseGreaterThan(BigDecimal value) {
            addCriterion("COINSISSUEEXPENSE >", value, "coinsissueexpense");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpenseGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSISSUEEXPENSE >=", value, "coinsissueexpense");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpenseLessThan(BigDecimal value) {
            addCriterion("COINSISSUEEXPENSE <", value, "coinsissueexpense");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpenseLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSISSUEEXPENSE <=", value, "coinsissueexpense");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpenseIn(List<BigDecimal> values) {
            addCriterion("COINSISSUEEXPENSE in", values, "coinsissueexpense");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpenseNotIn(List<BigDecimal> values) {
            addCriterion("COINSISSUEEXPENSE not in", values, "coinsissueexpense");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpenseBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSISSUEEXPENSE between", value1, value2, "coinsissueexpense");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpenseNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSISSUEEXPENSE not between", value1, value2, "coinsissueexpense");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("REMARK is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("REMARK is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("REMARK =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("REMARK <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("REMARK >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("REMARK >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("REMARK <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("REMARK <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("REMARK like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("REMARK not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("REMARK in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("REMARK not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("REMARK between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("REMARK not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andFlagIsNull() {
            addCriterion("FLAG is null");
            return (Criteria) this;
        }

        public Criteria andFlagIsNotNull() {
            addCriterion("FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andFlagEqualTo(String value) {
            addCriterion("FLAG =", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagNotEqualTo(String value) {
            addCriterion("FLAG <>", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagGreaterThan(String value) {
            addCriterion("FLAG >", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagGreaterThanOrEqualTo(String value) {
            addCriterion("FLAG >=", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagLessThan(String value) {
            addCriterion("FLAG <", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagLessThanOrEqualTo(String value) {
            addCriterion("FLAG <=", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagLike(String value) {
            addCriterion("FLAG like", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagNotLike(String value) {
            addCriterion("FLAG not like", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagIn(List<String> values) {
            addCriterion("FLAG in", values, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagNotIn(List<String> values) {
            addCriterion("FLAG not in", values, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagBetween(String value1, String value2) {
            addCriterion("FLAG between", value1, value2, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagNotBetween(String value1, String value2) {
            addCriterion("FLAG not between", value1, value2, "flag");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindIsNull() {
            addCriterion("COINSPREMIUMACCEPTIND is null");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindIsNotNull() {
            addCriterion("COINSPREMIUMACCEPTIND is not null");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindEqualTo(String value) {
            addCriterion("COINSPREMIUMACCEPTIND =", value, "coinspremiumacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindNotEqualTo(String value) {
            addCriterion("COINSPREMIUMACCEPTIND <>", value, "coinspremiumacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindGreaterThan(String value) {
            addCriterion("COINSPREMIUMACCEPTIND >", value, "coinspremiumacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindGreaterThanOrEqualTo(String value) {
            addCriterion("COINSPREMIUMACCEPTIND >=", value, "coinspremiumacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindLessThan(String value) {
            addCriterion("COINSPREMIUMACCEPTIND <", value, "coinspremiumacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindLessThanOrEqualTo(String value) {
            addCriterion("COINSPREMIUMACCEPTIND <=", value, "coinspremiumacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindLike(String value) {
            addCriterion("COINSPREMIUMACCEPTIND like", value, "coinspremiumacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindNotLike(String value) {
            addCriterion("COINSPREMIUMACCEPTIND not like", value, "coinspremiumacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindIn(List<String> values) {
            addCriterion("COINSPREMIUMACCEPTIND in", values, "coinspremiumacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindNotIn(List<String> values) {
            addCriterion("COINSPREMIUMACCEPTIND not in", values, "coinspremiumacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindBetween(String value1, String value2) {
            addCriterion("COINSPREMIUMACCEPTIND between", value1, value2, "coinspremiumacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumacceptindNotBetween(String value1, String value2) {
            addCriterion("COINSPREMIUMACCEPTIND not between", value1, value2, "coinspremiumacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeIsNull() {
            addCriterion("COINSPREMIUMCOMPOSE is null");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeIsNotNull() {
            addCriterion("COINSPREMIUMCOMPOSE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeEqualTo(String value) {
            addCriterion("COINSPREMIUMCOMPOSE =", value, "coinspremiumcompose");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeNotEqualTo(String value) {
            addCriterion("COINSPREMIUMCOMPOSE <>", value, "coinspremiumcompose");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeGreaterThan(String value) {
            addCriterion("COINSPREMIUMCOMPOSE >", value, "coinspremiumcompose");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeGreaterThanOrEqualTo(String value) {
            addCriterion("COINSPREMIUMCOMPOSE >=", value, "coinspremiumcompose");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeLessThan(String value) {
            addCriterion("COINSPREMIUMCOMPOSE <", value, "coinspremiumcompose");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeLessThanOrEqualTo(String value) {
            addCriterion("COINSPREMIUMCOMPOSE <=", value, "coinspremiumcompose");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeLike(String value) {
            addCriterion("COINSPREMIUMCOMPOSE like", value, "coinspremiumcompose");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeNotLike(String value) {
            addCriterion("COINSPREMIUMCOMPOSE not like", value, "coinspremiumcompose");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeIn(List<String> values) {
            addCriterion("COINSPREMIUMCOMPOSE in", values, "coinspremiumcompose");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeNotIn(List<String> values) {
            addCriterion("COINSPREMIUMCOMPOSE not in", values, "coinspremiumcompose");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeBetween(String value1, String value2) {
            addCriterion("COINSPREMIUMCOMPOSE between", value1, value2, "coinspremiumcompose");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcomposeNotBetween(String value1, String value2) {
            addCriterion("COINSPREMIUMCOMPOSE not between", value1, value2, "coinspremiumcompose");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindIsNull() {
            addCriterion("COINSAGENCYPAYIND is null");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindIsNotNull() {
            addCriterion("COINSAGENCYPAYIND is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindEqualTo(String value) {
            addCriterion("COINSAGENCYPAYIND =", value, "coinsagencypayind");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindNotEqualTo(String value) {
            addCriterion("COINSAGENCYPAYIND <>", value, "coinsagencypayind");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindGreaterThan(String value) {
            addCriterion("COINSAGENCYPAYIND >", value, "coinsagencypayind");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindGreaterThanOrEqualTo(String value) {
            addCriterion("COINSAGENCYPAYIND >=", value, "coinsagencypayind");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindLessThan(String value) {
            addCriterion("COINSAGENCYPAYIND <", value, "coinsagencypayind");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindLessThanOrEqualTo(String value) {
            addCriterion("COINSAGENCYPAYIND <=", value, "coinsagencypayind");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindLike(String value) {
            addCriterion("COINSAGENCYPAYIND like", value, "coinsagencypayind");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindNotLike(String value) {
            addCriterion("COINSAGENCYPAYIND not like", value, "coinsagencypayind");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindIn(List<String> values) {
            addCriterion("COINSAGENCYPAYIND in", values, "coinsagencypayind");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindNotIn(List<String> values) {
            addCriterion("COINSAGENCYPAYIND not in", values, "coinsagencypayind");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindBetween(String value1, String value2) {
            addCriterion("COINSAGENCYPAYIND between", value1, value2, "coinsagencypayind");
            return (Criteria) this;
        }

        public Criteria andCoinsagencypayindNotBetween(String value1, String value2) {
            addCriterion("COINSAGENCYPAYIND not between", value1, value2, "coinsagencypayind");
            return (Criteria) this;
        }

        public Criteria andCoinslevyrateIsNull() {
            addCriterion("COINSLEVYRATE is null");
            return (Criteria) this;
        }

        public Criteria andCoinslevyrateIsNotNull() {
            addCriterion("COINSLEVYRATE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinslevyrateEqualTo(BigDecimal value) {
            addCriterion("COINSLEVYRATE =", value, "coinslevyrate");
            return (Criteria) this;
        }

        public Criteria andCoinslevyrateNotEqualTo(BigDecimal value) {
            addCriterion("COINSLEVYRATE <>", value, "coinslevyrate");
            return (Criteria) this;
        }

        public Criteria andCoinslevyrateGreaterThan(BigDecimal value) {
            addCriterion("COINSLEVYRATE >", value, "coinslevyrate");
            return (Criteria) this;
        }

        public Criteria andCoinslevyrateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSLEVYRATE >=", value, "coinslevyrate");
            return (Criteria) this;
        }

        public Criteria andCoinslevyrateLessThan(BigDecimal value) {
            addCriterion("COINSLEVYRATE <", value, "coinslevyrate");
            return (Criteria) this;
        }

        public Criteria andCoinslevyrateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSLEVYRATE <=", value, "coinslevyrate");
            return (Criteria) this;
        }

        public Criteria andCoinslevyrateIn(List<BigDecimal> values) {
            addCriterion("COINSLEVYRATE in", values, "coinslevyrate");
            return (Criteria) this;
        }

        public Criteria andCoinslevyrateNotIn(List<BigDecimal> values) {
            addCriterion("COINSLEVYRATE not in", values, "coinslevyrate");
            return (Criteria) this;
        }

        public Criteria andCoinslevyrateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSLEVYRATE between", value1, value2, "coinslevyrate");
            return (Criteria) this;
        }

        public Criteria andCoinslevyrateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSLEVYRATE not between", value1, value2, "coinslevyrate");
            return (Criteria) this;
        }

        public Criteria andCoinslevyIsNull() {
            addCriterion("COINSLEVY is null");
            return (Criteria) this;
        }

        public Criteria andCoinslevyIsNotNull() {
            addCriterion("COINSLEVY is not null");
            return (Criteria) this;
        }

        public Criteria andCoinslevyEqualTo(BigDecimal value) {
            addCriterion("COINSLEVY =", value, "coinslevy");
            return (Criteria) this;
        }

        public Criteria andCoinslevyNotEqualTo(BigDecimal value) {
            addCriterion("COINSLEVY <>", value, "coinslevy");
            return (Criteria) this;
        }

        public Criteria andCoinslevyGreaterThan(BigDecimal value) {
            addCriterion("COINSLEVY >", value, "coinslevy");
            return (Criteria) this;
        }

        public Criteria andCoinslevyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSLEVY >=", value, "coinslevy");
            return (Criteria) this;
        }

        public Criteria andCoinslevyLessThan(BigDecimal value) {
            addCriterion("COINSLEVY <", value, "coinslevy");
            return (Criteria) this;
        }

        public Criteria andCoinslevyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSLEVY <=", value, "coinslevy");
            return (Criteria) this;
        }

        public Criteria andCoinslevyIn(List<BigDecimal> values) {
            addCriterion("COINSLEVY in", values, "coinslevy");
            return (Criteria) this;
        }

        public Criteria andCoinslevyNotIn(List<BigDecimal> values) {
            addCriterion("COINSLEVY not in", values, "coinslevy");
            return (Criteria) this;
        }

        public Criteria andCoinslevyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSLEVY between", value1, value2, "coinslevy");
            return (Criteria) this;
        }

        public Criteria andCoinslevyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSLEVY not between", value1, value2, "coinslevy");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindIsNull() {
            addCriterion("COINSLEVYACCEPTIND is null");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindIsNotNull() {
            addCriterion("COINSLEVYACCEPTIND is not null");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindEqualTo(String value) {
            addCriterion("COINSLEVYACCEPTIND =", value, "coinslevyacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindNotEqualTo(String value) {
            addCriterion("COINSLEVYACCEPTIND <>", value, "coinslevyacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindGreaterThan(String value) {
            addCriterion("COINSLEVYACCEPTIND >", value, "coinslevyacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindGreaterThanOrEqualTo(String value) {
            addCriterion("COINSLEVYACCEPTIND >=", value, "coinslevyacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindLessThan(String value) {
            addCriterion("COINSLEVYACCEPTIND <", value, "coinslevyacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindLessThanOrEqualTo(String value) {
            addCriterion("COINSLEVYACCEPTIND <=", value, "coinslevyacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindLike(String value) {
            addCriterion("COINSLEVYACCEPTIND like", value, "coinslevyacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindNotLike(String value) {
            addCriterion("COINSLEVYACCEPTIND not like", value, "coinslevyacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindIn(List<String> values) {
            addCriterion("COINSLEVYACCEPTIND in", values, "coinslevyacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindNotIn(List<String> values) {
            addCriterion("COINSLEVYACCEPTIND not in", values, "coinslevyacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindBetween(String value1, String value2) {
            addCriterion("COINSLEVYACCEPTIND between", value1, value2, "coinslevyacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinslevyacceptindNotBetween(String value1, String value2) {
            addCriterion("COINSLEVYACCEPTIND not between", value1, value2, "coinslevyacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeerateIsNull() {
            addCriterion("COINSOTHFEERATE is null");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeerateIsNotNull() {
            addCriterion("COINSOTHFEERATE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeerateEqualTo(BigDecimal value) {
            addCriterion("COINSOTHFEERATE =", value, "coinsothfeerate");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeerateNotEqualTo(BigDecimal value) {
            addCriterion("COINSOTHFEERATE <>", value, "coinsothfeerate");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeerateGreaterThan(BigDecimal value) {
            addCriterion("COINSOTHFEERATE >", value, "coinsothfeerate");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeerateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSOTHFEERATE >=", value, "coinsothfeerate");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeerateLessThan(BigDecimal value) {
            addCriterion("COINSOTHFEERATE <", value, "coinsothfeerate");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeerateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSOTHFEERATE <=", value, "coinsothfeerate");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeerateIn(List<BigDecimal> values) {
            addCriterion("COINSOTHFEERATE in", values, "coinsothfeerate");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeerateNotIn(List<BigDecimal> values) {
            addCriterion("COINSOTHFEERATE not in", values, "coinsothfeerate");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeerateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSOTHFEERATE between", value1, value2, "coinsothfeerate");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeerateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSOTHFEERATE not between", value1, value2, "coinsothfeerate");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeIsNull() {
            addCriterion("COINSOTHFEE is null");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeIsNotNull() {
            addCriterion("COINSOTHFEE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeEqualTo(BigDecimal value) {
            addCriterion("COINSOTHFEE =", value, "coinsothfee");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeNotEqualTo(BigDecimal value) {
            addCriterion("COINSOTHFEE <>", value, "coinsothfee");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeGreaterThan(BigDecimal value) {
            addCriterion("COINSOTHFEE >", value, "coinsothfee");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSOTHFEE >=", value, "coinsothfee");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeLessThan(BigDecimal value) {
            addCriterion("COINSOTHFEE <", value, "coinsothfee");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSOTHFEE <=", value, "coinsothfee");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeIn(List<BigDecimal> values) {
            addCriterion("COINSOTHFEE in", values, "coinsothfee");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeNotIn(List<BigDecimal> values) {
            addCriterion("COINSOTHFEE not in", values, "coinsothfee");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSOTHFEE between", value1, value2, "coinsothfee");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSOTHFEE not between", value1, value2, "coinsothfee");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindIsNull() {
            addCriterion("COINSOTHFEEACCEPTIND is null");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindIsNotNull() {
            addCriterion("COINSOTHFEEACCEPTIND is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindEqualTo(String value) {
            addCriterion("COINSOTHFEEACCEPTIND =", value, "coinsothfeeacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindNotEqualTo(String value) {
            addCriterion("COINSOTHFEEACCEPTIND <>", value, "coinsothfeeacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindGreaterThan(String value) {
            addCriterion("COINSOTHFEEACCEPTIND >", value, "coinsothfeeacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindGreaterThanOrEqualTo(String value) {
            addCriterion("COINSOTHFEEACCEPTIND >=", value, "coinsothfeeacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindLessThan(String value) {
            addCriterion("COINSOTHFEEACCEPTIND <", value, "coinsothfeeacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindLessThanOrEqualTo(String value) {
            addCriterion("COINSOTHFEEACCEPTIND <=", value, "coinsothfeeacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindLike(String value) {
            addCriterion("COINSOTHFEEACCEPTIND like", value, "coinsothfeeacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindNotLike(String value) {
            addCriterion("COINSOTHFEEACCEPTIND not like", value, "coinsothfeeacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindIn(List<String> values) {
            addCriterion("COINSOTHFEEACCEPTIND in", values, "coinsothfeeacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindNotIn(List<String> values) {
            addCriterion("COINSOTHFEEACCEPTIND not in", values, "coinsothfeeacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindBetween(String value1, String value2) {
            addCriterion("COINSOTHFEEACCEPTIND between", value1, value2, "coinsothfeeacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeeacceptindNotBetween(String value1, String value2) {
            addCriterion("COINSOTHFEEACCEPTIND not between", value1, value2, "coinsothfeeacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserverateIsNull() {
            addCriterion("COINSCOMPRESERVERATE is null");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserverateIsNotNull() {
            addCriterion("COINSCOMPRESERVERATE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserverateEqualTo(BigDecimal value) {
            addCriterion("COINSCOMPRESERVERATE =", value, "coinscompreserverate");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserverateNotEqualTo(BigDecimal value) {
            addCriterion("COINSCOMPRESERVERATE <>", value, "coinscompreserverate");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserverateGreaterThan(BigDecimal value) {
            addCriterion("COINSCOMPRESERVERATE >", value, "coinscompreserverate");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserverateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSCOMPRESERVERATE >=", value, "coinscompreserverate");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserverateLessThan(BigDecimal value) {
            addCriterion("COINSCOMPRESERVERATE <", value, "coinscompreserverate");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserverateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSCOMPRESERVERATE <=", value, "coinscompreserverate");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserverateIn(List<BigDecimal> values) {
            addCriterion("COINSCOMPRESERVERATE in", values, "coinscompreserverate");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserverateNotIn(List<BigDecimal> values) {
            addCriterion("COINSCOMPRESERVERATE not in", values, "coinscompreserverate");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserverateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSCOMPRESERVERATE between", value1, value2, "coinscompreserverate");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserverateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSCOMPRESERVERATE not between", value1, value2, "coinscompreserverate");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserveIsNull() {
            addCriterion("COINSCOMPRESERVE is null");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserveIsNotNull() {
            addCriterion("COINSCOMPRESERVE is not null");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserveEqualTo(BigDecimal value) {
            addCriterion("COINSCOMPRESERVE =", value, "coinscompreserve");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserveNotEqualTo(BigDecimal value) {
            addCriterion("COINSCOMPRESERVE <>", value, "coinscompreserve");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserveGreaterThan(BigDecimal value) {
            addCriterion("COINSCOMPRESERVE >", value, "coinscompreserve");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserveGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSCOMPRESERVE >=", value, "coinscompreserve");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserveLessThan(BigDecimal value) {
            addCriterion("COINSCOMPRESERVE <", value, "coinscompreserve");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserveLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSCOMPRESERVE <=", value, "coinscompreserve");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserveIn(List<BigDecimal> values) {
            addCriterion("COINSCOMPRESERVE in", values, "coinscompreserve");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserveNotIn(List<BigDecimal> values) {
            addCriterion("COINSCOMPRESERVE not in", values, "coinscompreserve");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserveBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSCOMPRESERVE between", value1, value2, "coinscompreserve");
            return (Criteria) this;
        }

        public Criteria andCoinscompreserveNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSCOMPRESERVE not between", value1, value2, "coinscompreserve");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindIsNull() {
            addCriterion("COINSCOMPRESACCEPTIND is null");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindIsNotNull() {
            addCriterion("COINSCOMPRESACCEPTIND is not null");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindEqualTo(String value) {
            addCriterion("COINSCOMPRESACCEPTIND =", value, "coinscompresacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindNotEqualTo(String value) {
            addCriterion("COINSCOMPRESACCEPTIND <>", value, "coinscompresacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindGreaterThan(String value) {
            addCriterion("COINSCOMPRESACCEPTIND >", value, "coinscompresacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindGreaterThanOrEqualTo(String value) {
            addCriterion("COINSCOMPRESACCEPTIND >=", value, "coinscompresacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindLessThan(String value) {
            addCriterion("COINSCOMPRESACCEPTIND <", value, "coinscompresacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindLessThanOrEqualTo(String value) {
            addCriterion("COINSCOMPRESACCEPTIND <=", value, "coinscompresacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindLike(String value) {
            addCriterion("COINSCOMPRESACCEPTIND like", value, "coinscompresacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindNotLike(String value) {
            addCriterion("COINSCOMPRESACCEPTIND not like", value, "coinscompresacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindIn(List<String> values) {
            addCriterion("COINSCOMPRESACCEPTIND in", values, "coinscompresacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindNotIn(List<String> values) {
            addCriterion("COINSCOMPRESACCEPTIND not in", values, "coinscompresacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindBetween(String value1, String value2) {
            addCriterion("COINSCOMPRESACCEPTIND between", value1, value2, "coinscompresacceptind");
            return (Criteria) this;
        }

        public Criteria andCoinscompresacceptindNotBetween(String value1, String value2) {
            addCriterion("COINSCOMPRESACCEPTIND not between", value1, value2, "coinscompresacceptind");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterIsNull() {
            addCriterion("DEBITACCEPTER is null");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterIsNotNull() {
            addCriterion("DEBITACCEPTER is not null");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterEqualTo(String value) {
            addCriterion("DEBITACCEPTER =", value, "debitaccepter");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterNotEqualTo(String value) {
            addCriterion("DEBITACCEPTER <>", value, "debitaccepter");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterGreaterThan(String value) {
            addCriterion("DEBITACCEPTER >", value, "debitaccepter");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterGreaterThanOrEqualTo(String value) {
            addCriterion("DEBITACCEPTER >=", value, "debitaccepter");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterLessThan(String value) {
            addCriterion("DEBITACCEPTER <", value, "debitaccepter");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterLessThanOrEqualTo(String value) {
            addCriterion("DEBITACCEPTER <=", value, "debitaccepter");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterLike(String value) {
            addCriterion("DEBITACCEPTER like", value, "debitaccepter");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterNotLike(String value) {
            addCriterion("DEBITACCEPTER not like", value, "debitaccepter");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterIn(List<String> values) {
            addCriterion("DEBITACCEPTER in", values, "debitaccepter");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterNotIn(List<String> values) {
            addCriterion("DEBITACCEPTER not in", values, "debitaccepter");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterBetween(String value1, String value2) {
            addCriterion("DEBITACCEPTER between", value1, value2, "debitaccepter");
            return (Criteria) this;
        }

        public Criteria andDebitaccepterNotBetween(String value1, String value2) {
            addCriterion("DEBITACCEPTER not between", value1, value2, "debitaccepter");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindIsNull() {
            addCriterion("COINSCLAIMIND is null");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindIsNotNull() {
            addCriterion("COINSCLAIMIND is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindEqualTo(String value) {
            addCriterion("COINSCLAIMIND =", value, "coinsclaimind");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindNotEqualTo(String value) {
            addCriterion("COINSCLAIMIND <>", value, "coinsclaimind");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindGreaterThan(String value) {
            addCriterion("COINSCLAIMIND >", value, "coinsclaimind");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindGreaterThanOrEqualTo(String value) {
            addCriterion("COINSCLAIMIND >=", value, "coinsclaimind");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindLessThan(String value) {
            addCriterion("COINSCLAIMIND <", value, "coinsclaimind");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindLessThanOrEqualTo(String value) {
            addCriterion("COINSCLAIMIND <=", value, "coinsclaimind");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindLike(String value) {
            addCriterion("COINSCLAIMIND like", value, "coinsclaimind");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindNotLike(String value) {
            addCriterion("COINSCLAIMIND not like", value, "coinsclaimind");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindIn(List<String> values) {
            addCriterion("COINSCLAIMIND in", values, "coinsclaimind");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindNotIn(List<String> values) {
            addCriterion("COINSCLAIMIND not in", values, "coinsclaimind");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindBetween(String value1, String value2) {
            addCriterion("COINSCLAIMIND between", value1, value2, "coinsclaimind");
            return (Criteria) this;
        }

        public Criteria andCoinsclaimindNotBetween(String value1, String value2) {
            addCriterion("COINSCLAIMIND not between", value1, value2, "coinsclaimind");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoIsNull() {
            addCriterion("SUBPOLICYNO is null");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoIsNotNull() {
            addCriterion("SUBPOLICYNO is not null");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoEqualTo(String value) {
            addCriterion("SUBPOLICYNO =", value, "subpolicyno");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoNotEqualTo(String value) {
            addCriterion("SUBPOLICYNO <>", value, "subpolicyno");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoGreaterThan(String value) {
            addCriterion("SUBPOLICYNO >", value, "subpolicyno");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoGreaterThanOrEqualTo(String value) {
            addCriterion("SUBPOLICYNO >=", value, "subpolicyno");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoLessThan(String value) {
            addCriterion("SUBPOLICYNO <", value, "subpolicyno");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoLessThanOrEqualTo(String value) {
            addCriterion("SUBPOLICYNO <=", value, "subpolicyno");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoLike(String value) {
            addCriterion("SUBPOLICYNO like", value, "subpolicyno");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoNotLike(String value) {
            addCriterion("SUBPOLICYNO not like", value, "subpolicyno");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoIn(List<String> values) {
            addCriterion("SUBPOLICYNO in", values, "subpolicyno");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoNotIn(List<String> values) {
            addCriterion("SUBPOLICYNO not in", values, "subpolicyno");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoBetween(String value1, String value2) {
            addCriterion("SUBPOLICYNO between", value1, value2, "subpolicyno");
            return (Criteria) this;
        }

        public Criteria andSubpolicynoNotBetween(String value1, String value2) {
            addCriterion("SUBPOLICYNO not between", value1, value2, "subpolicyno");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumIsNull() {
            addCriterion("COINSNOTAXPREMIUM is null");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumIsNotNull() {
            addCriterion("COINSNOTAXPREMIUM is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumEqualTo(BigDecimal value) {
            addCriterion("COINSNOTAXPREMIUM =", value, "coinsnotaxpremium");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumNotEqualTo(BigDecimal value) {
            addCriterion("COINSNOTAXPREMIUM <>", value, "coinsnotaxpremium");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumGreaterThan(BigDecimal value) {
            addCriterion("COINSNOTAXPREMIUM >", value, "coinsnotaxpremium");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSNOTAXPREMIUM >=", value, "coinsnotaxpremium");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumLessThan(BigDecimal value) {
            addCriterion("COINSNOTAXPREMIUM <", value, "coinsnotaxpremium");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSNOTAXPREMIUM <=", value, "coinsnotaxpremium");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumIn(List<BigDecimal> values) {
            addCriterion("COINSNOTAXPREMIUM in", values, "coinsnotaxpremium");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumNotIn(List<BigDecimal> values) {
            addCriterion("COINSNOTAXPREMIUM not in", values, "coinsnotaxpremium");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSNOTAXPREMIUM between", value1, value2, "coinsnotaxpremium");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSNOTAXPREMIUM not between", value1, value2, "coinsnotaxpremium");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountIsNull() {
            addCriterion("COINSTAXAMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountIsNotNull() {
            addCriterion("COINSTAXAMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountEqualTo(BigDecimal value) {
            addCriterion("COINSTAXAMOUNT =", value, "coinstaxamount");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountNotEqualTo(BigDecimal value) {
            addCriterion("COINSTAXAMOUNT <>", value, "coinstaxamount");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountGreaterThan(BigDecimal value) {
            addCriterion("COINSTAXAMOUNT >", value, "coinstaxamount");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSTAXAMOUNT >=", value, "coinstaxamount");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountLessThan(BigDecimal value) {
            addCriterion("COINSTAXAMOUNT <", value, "coinstaxamount");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSTAXAMOUNT <=", value, "coinstaxamount");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountIn(List<BigDecimal> values) {
            addCriterion("COINSTAXAMOUNT in", values, "coinstaxamount");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountNotIn(List<BigDecimal> values) {
            addCriterion("COINSTAXAMOUNT not in", values, "coinstaxamount");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSTAXAMOUNT between", value1, value2, "coinstaxamount");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSTAXAMOUNT not between", value1, value2, "coinstaxamount");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameIsNull() {
            addCriterion("SECONDLEVELCOINSNAME is null");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameIsNotNull() {
            addCriterion("SECONDLEVELCOINSNAME is not null");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameEqualTo(String value) {
            addCriterion("SECONDLEVELCOINSNAME =", value, "secondlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameNotEqualTo(String value) {
            addCriterion("SECONDLEVELCOINSNAME <>", value, "secondlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameGreaterThan(String value) {
            addCriterion("SECONDLEVELCOINSNAME >", value, "secondlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameGreaterThanOrEqualTo(String value) {
            addCriterion("SECONDLEVELCOINSNAME >=", value, "secondlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameLessThan(String value) {
            addCriterion("SECONDLEVELCOINSNAME <", value, "secondlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameLessThanOrEqualTo(String value) {
            addCriterion("SECONDLEVELCOINSNAME <=", value, "secondlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameLike(String value) {
            addCriterion("SECONDLEVELCOINSNAME like", value, "secondlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameNotLike(String value) {
            addCriterion("SECONDLEVELCOINSNAME not like", value, "secondlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameIn(List<String> values) {
            addCriterion("SECONDLEVELCOINSNAME in", values, "secondlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameNotIn(List<String> values) {
            addCriterion("SECONDLEVELCOINSNAME not in", values, "secondlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameBetween(String value1, String value2) {
            addCriterion("SECONDLEVELCOINSNAME between", value1, value2, "secondlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andSecondlevelcoinsnameNotBetween(String value1, String value2) {
            addCriterion("SECONDLEVELCOINSNAME not between", value1, value2, "secondlevelcoinsname");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoIsNull() {
            addCriterion("RELATEDCOINSPOLICYNO is null");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoIsNotNull() {
            addCriterion("RELATEDCOINSPOLICYNO is not null");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoEqualTo(String value) {
            addCriterion("RELATEDCOINSPOLICYNO =", value, "relatedcoinspolicyno");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoNotEqualTo(String value) {
            addCriterion("RELATEDCOINSPOLICYNO <>", value, "relatedcoinspolicyno");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoGreaterThan(String value) {
            addCriterion("RELATEDCOINSPOLICYNO >", value, "relatedcoinspolicyno");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoGreaterThanOrEqualTo(String value) {
            addCriterion("RELATEDCOINSPOLICYNO >=", value, "relatedcoinspolicyno");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoLessThan(String value) {
            addCriterion("RELATEDCOINSPOLICYNO <", value, "relatedcoinspolicyno");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoLessThanOrEqualTo(String value) {
            addCriterion("RELATEDCOINSPOLICYNO <=", value, "relatedcoinspolicyno");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoLike(String value) {
            addCriterion("RELATEDCOINSPOLICYNO like", value, "relatedcoinspolicyno");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoNotLike(String value) {
            addCriterion("RELATEDCOINSPOLICYNO not like", value, "relatedcoinspolicyno");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoIn(List<String> values) {
            addCriterion("RELATEDCOINSPOLICYNO in", values, "relatedcoinspolicyno");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoNotIn(List<String> values) {
            addCriterion("RELATEDCOINSPOLICYNO not in", values, "relatedcoinspolicyno");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoBetween(String value1, String value2) {
            addCriterion("RELATEDCOINSPOLICYNO between", value1, value2, "relatedcoinspolicyno");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinspolicynoNotBetween(String value1, String value2) {
            addCriterion("RELATEDCOINSPOLICYNO not between", value1, value2, "relatedcoinspolicyno");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeIsNull() {
            addCriterion("RELATEDCOINSCODE is null");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeIsNotNull() {
            addCriterion("RELATEDCOINSCODE is not null");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeEqualTo(String value) {
            addCriterion("RELATEDCOINSCODE =", value, "relatedcoinscode");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeNotEqualTo(String value) {
            addCriterion("RELATEDCOINSCODE <>", value, "relatedcoinscode");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeGreaterThan(String value) {
            addCriterion("RELATEDCOINSCODE >", value, "relatedcoinscode");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeGreaterThanOrEqualTo(String value) {
            addCriterion("RELATEDCOINSCODE >=", value, "relatedcoinscode");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeLessThan(String value) {
            addCriterion("RELATEDCOINSCODE <", value, "relatedcoinscode");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeLessThanOrEqualTo(String value) {
            addCriterion("RELATEDCOINSCODE <=", value, "relatedcoinscode");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeLike(String value) {
            addCriterion("RELATEDCOINSCODE like", value, "relatedcoinscode");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeNotLike(String value) {
            addCriterion("RELATEDCOINSCODE not like", value, "relatedcoinscode");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeIn(List<String> values) {
            addCriterion("RELATEDCOINSCODE in", values, "relatedcoinscode");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeNotIn(List<String> values) {
            addCriterion("RELATEDCOINSCODE not in", values, "relatedcoinscode");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeBetween(String value1, String value2) {
            addCriterion("RELATEDCOINSCODE between", value1, value2, "relatedcoinscode");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscodeNotBetween(String value1, String value2) {
            addCriterion("RELATEDCOINSCODE not between", value1, value2, "relatedcoinscode");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameIsNull() {
            addCriterion("RELATEDCOINSCNAME is null");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameIsNotNull() {
            addCriterion("RELATEDCOINSCNAME is not null");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameEqualTo(String value) {
            addCriterion("RELATEDCOINSCNAME =", value, "relatedcoinscname");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameNotEqualTo(String value) {
            addCriterion("RELATEDCOINSCNAME <>", value, "relatedcoinscname");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameGreaterThan(String value) {
            addCriterion("RELATEDCOINSCNAME >", value, "relatedcoinscname");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameGreaterThanOrEqualTo(String value) {
            addCriterion("RELATEDCOINSCNAME >=", value, "relatedcoinscname");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameLessThan(String value) {
            addCriterion("RELATEDCOINSCNAME <", value, "relatedcoinscname");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameLessThanOrEqualTo(String value) {
            addCriterion("RELATEDCOINSCNAME <=", value, "relatedcoinscname");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameLike(String value) {
            addCriterion("RELATEDCOINSCNAME like", value, "relatedcoinscname");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameNotLike(String value) {
            addCriterion("RELATEDCOINSCNAME not like", value, "relatedcoinscname");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameIn(List<String> values) {
            addCriterion("RELATEDCOINSCNAME in", values, "relatedcoinscname");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameNotIn(List<String> values) {
            addCriterion("RELATEDCOINSCNAME not in", values, "relatedcoinscname");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameBetween(String value1, String value2) {
            addCriterion("RELATEDCOINSCNAME between", value1, value2, "relatedcoinscname");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinscnameNotBetween(String value1, String value2) {
            addCriterion("RELATEDCOINSCNAME not between", value1, value2, "relatedcoinscname");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeIsNull() {
            addCriterion("SEALSTEAMCODE is null");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeIsNotNull() {
            addCriterion("SEALSTEAMCODE is not null");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeEqualTo(String value) {
            addCriterion("SEALSTEAMCODE =", value, "sealsteamcode");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeNotEqualTo(String value) {
            addCriterion("SEALSTEAMCODE <>", value, "sealsteamcode");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeGreaterThan(String value) {
            addCriterion("SEALSTEAMCODE >", value, "sealsteamcode");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeGreaterThanOrEqualTo(String value) {
            addCriterion("SEALSTEAMCODE >=", value, "sealsteamcode");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeLessThan(String value) {
            addCriterion("SEALSTEAMCODE <", value, "sealsteamcode");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeLessThanOrEqualTo(String value) {
            addCriterion("SEALSTEAMCODE <=", value, "sealsteamcode");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeLike(String value) {
            addCriterion("SEALSTEAMCODE like", value, "sealsteamcode");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeNotLike(String value) {
            addCriterion("SEALSTEAMCODE not like", value, "sealsteamcode");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeIn(List<String> values) {
            addCriterion("SEALSTEAMCODE in", values, "sealsteamcode");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeNotIn(List<String> values) {
            addCriterion("SEALSTEAMCODE not in", values, "sealsteamcode");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeBetween(String value1, String value2) {
            addCriterion("SEALSTEAMCODE between", value1, value2, "sealsteamcode");
            return (Criteria) this;
        }

        public Criteria andSealsteamcodeNotBetween(String value1, String value2) {
            addCriterion("SEALSTEAMCODE not between", value1, value2, "sealsteamcode");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameIsNull() {
            addCriterion("SEALSTEAMCNAME is null");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameIsNotNull() {
            addCriterion("SEALSTEAMCNAME is not null");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameEqualTo(String value) {
            addCriterion("SEALSTEAMCNAME =", value, "sealsteamcname");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameNotEqualTo(String value) {
            addCriterion("SEALSTEAMCNAME <>", value, "sealsteamcname");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameGreaterThan(String value) {
            addCriterion("SEALSTEAMCNAME >", value, "sealsteamcname");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameGreaterThanOrEqualTo(String value) {
            addCriterion("SEALSTEAMCNAME >=", value, "sealsteamcname");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameLessThan(String value) {
            addCriterion("SEALSTEAMCNAME <", value, "sealsteamcname");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameLessThanOrEqualTo(String value) {
            addCriterion("SEALSTEAMCNAME <=", value, "sealsteamcname");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameLike(String value) {
            addCriterion("SEALSTEAMCNAME like", value, "sealsteamcname");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameNotLike(String value) {
            addCriterion("SEALSTEAMCNAME not like", value, "sealsteamcname");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameIn(List<String> values) {
            addCriterion("SEALSTEAMCNAME in", values, "sealsteamcname");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameNotIn(List<String> values) {
            addCriterion("SEALSTEAMCNAME not in", values, "sealsteamcname");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameBetween(String value1, String value2) {
            addCriterion("SEALSTEAMCNAME between", value1, value2, "sealsteamcname");
            return (Criteria) this;
        }

        public Criteria andSealsteamcnameNotBetween(String value1, String value2) {
            addCriterion("SEALSTEAMCNAME not between", value1, value2, "sealsteamcname");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeIsNull() {
            addCriterion("SEALSMANCODE is null");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeIsNotNull() {
            addCriterion("SEALSMANCODE is not null");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeEqualTo(String value) {
            addCriterion("SEALSMANCODE =", value, "sealsmancode");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeNotEqualTo(String value) {
            addCriterion("SEALSMANCODE <>", value, "sealsmancode");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeGreaterThan(String value) {
            addCriterion("SEALSMANCODE >", value, "sealsmancode");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeGreaterThanOrEqualTo(String value) {
            addCriterion("SEALSMANCODE >=", value, "sealsmancode");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeLessThan(String value) {
            addCriterion("SEALSMANCODE <", value, "sealsmancode");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeLessThanOrEqualTo(String value) {
            addCriterion("SEALSMANCODE <=", value, "sealsmancode");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeLike(String value) {
            addCriterion("SEALSMANCODE like", value, "sealsmancode");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeNotLike(String value) {
            addCriterion("SEALSMANCODE not like", value, "sealsmancode");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeIn(List<String> values) {
            addCriterion("SEALSMANCODE in", values, "sealsmancode");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeNotIn(List<String> values) {
            addCriterion("SEALSMANCODE not in", values, "sealsmancode");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeBetween(String value1, String value2) {
            addCriterion("SEALSMANCODE between", value1, value2, "sealsmancode");
            return (Criteria) this;
        }

        public Criteria andSealsmancodeNotBetween(String value1, String value2) {
            addCriterion("SEALSMANCODE not between", value1, value2, "sealsmancode");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameIsNull() {
            addCriterion("SEALSMANCNAME is null");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameIsNotNull() {
            addCriterion("SEALSMANCNAME is not null");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameEqualTo(String value) {
            addCriterion("SEALSMANCNAME =", value, "sealsmancname");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameNotEqualTo(String value) {
            addCriterion("SEALSMANCNAME <>", value, "sealsmancname");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameGreaterThan(String value) {
            addCriterion("SEALSMANCNAME >", value, "sealsmancname");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameGreaterThanOrEqualTo(String value) {
            addCriterion("SEALSMANCNAME >=", value, "sealsmancname");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameLessThan(String value) {
            addCriterion("SEALSMANCNAME <", value, "sealsmancname");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameLessThanOrEqualTo(String value) {
            addCriterion("SEALSMANCNAME <=", value, "sealsmancname");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameLike(String value) {
            addCriterion("SEALSMANCNAME like", value, "sealsmancname");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameNotLike(String value) {
            addCriterion("SEALSMANCNAME not like", value, "sealsmancname");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameIn(List<String> values) {
            addCriterion("SEALSMANCNAME in", values, "sealsmancname");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameNotIn(List<String> values) {
            addCriterion("SEALSMANCNAME not in", values, "sealsmancname");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameBetween(String value1, String value2) {
            addCriterion("SEALSMANCNAME between", value1, value2, "sealsmancname");
            return (Criteria) this;
        }

        public Criteria andSealsmancnameNotBetween(String value1, String value2) {
            addCriterion("SEALSMANCNAME not between", value1, value2, "sealsmancname");
            return (Criteria) this;
        }

        public Criteria andWholerateIsNull() {
            addCriterion("WHOLERATE is null");
            return (Criteria) this;
        }

        public Criteria andWholerateIsNotNull() {
            addCriterion("WHOLERATE is not null");
            return (Criteria) this;
        }

        public Criteria andWholerateEqualTo(BigDecimal value) {
            addCriterion("WHOLERATE =", value, "wholerate");
            return (Criteria) this;
        }

        public Criteria andWholerateNotEqualTo(BigDecimal value) {
            addCriterion("WHOLERATE <>", value, "wholerate");
            return (Criteria) this;
        }

        public Criteria andWholerateGreaterThan(BigDecimal value) {
            addCriterion("WHOLERATE >", value, "wholerate");
            return (Criteria) this;
        }

        public Criteria andWholerateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("WHOLERATE >=", value, "wholerate");
            return (Criteria) this;
        }

        public Criteria andWholerateLessThan(BigDecimal value) {
            addCriterion("WHOLERATE <", value, "wholerate");
            return (Criteria) this;
        }

        public Criteria andWholerateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("WHOLERATE <=", value, "wholerate");
            return (Criteria) this;
        }

        public Criteria andWholerateIn(List<BigDecimal> values) {
            addCriterion("WHOLERATE in", values, "wholerate");
            return (Criteria) this;
        }

        public Criteria andWholerateNotIn(List<BigDecimal> values) {
            addCriterion("WHOLERATE not in", values, "wholerate");
            return (Criteria) this;
        }

        public Criteria andWholerateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("WHOLERATE between", value1, value2, "wholerate");
            return (Criteria) this;
        }

        public Criteria andWholerateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("WHOLERATE not between", value1, value2, "wholerate");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoIsNull() {
            addCriterion("ORISINGLENO is null");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoIsNotNull() {
            addCriterion("ORISINGLENO is not null");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoEqualTo(String value) {
            addCriterion("ORISINGLENO =", value, "orisingleno");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoNotEqualTo(String value) {
            addCriterion("ORISINGLENO <>", value, "orisingleno");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoGreaterThan(String value) {
            addCriterion("ORISINGLENO >", value, "orisingleno");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoGreaterThanOrEqualTo(String value) {
            addCriterion("ORISINGLENO >=", value, "orisingleno");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoLessThan(String value) {
            addCriterion("ORISINGLENO <", value, "orisingleno");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoLessThanOrEqualTo(String value) {
            addCriterion("ORISINGLENO <=", value, "orisingleno");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoLike(String value) {
            addCriterion("ORISINGLENO like", value, "orisingleno");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoNotLike(String value) {
            addCriterion("ORISINGLENO not like", value, "orisingleno");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoIn(List<String> values) {
            addCriterion("ORISINGLENO in", values, "orisingleno");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoNotIn(List<String> values) {
            addCriterion("ORISINGLENO not in", values, "orisingleno");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoBetween(String value1, String value2) {
            addCriterion("ORISINGLENO between", value1, value2, "orisingleno");
            return (Criteria) this;
        }

        public Criteria andOrisinglenoNotBetween(String value1, String value2) {
            addCriterion("ORISINGLENO not between", value1, value2, "orisingleno");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeIsNull() {
            addCriterion("RELATEDCOINSTYPE is null");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeIsNotNull() {
            addCriterion("RELATEDCOINSTYPE is not null");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeEqualTo(String value) {
            addCriterion("RELATEDCOINSTYPE =", value, "relatedcoinstype");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeNotEqualTo(String value) {
            addCriterion("RELATEDCOINSTYPE <>", value, "relatedcoinstype");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeGreaterThan(String value) {
            addCriterion("RELATEDCOINSTYPE >", value, "relatedcoinstype");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeGreaterThanOrEqualTo(String value) {
            addCriterion("RELATEDCOINSTYPE >=", value, "relatedcoinstype");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeLessThan(String value) {
            addCriterion("RELATEDCOINSTYPE <", value, "relatedcoinstype");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeLessThanOrEqualTo(String value) {
            addCriterion("RELATEDCOINSTYPE <=", value, "relatedcoinstype");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeLike(String value) {
            addCriterion("RELATEDCOINSTYPE like", value, "relatedcoinstype");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeNotLike(String value) {
            addCriterion("RELATEDCOINSTYPE not like", value, "relatedcoinstype");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeIn(List<String> values) {
            addCriterion("RELATEDCOINSTYPE in", values, "relatedcoinstype");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeNotIn(List<String> values) {
            addCriterion("RELATEDCOINSTYPE not in", values, "relatedcoinstype");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeBetween(String value1, String value2) {
            addCriterion("RELATEDCOINSTYPE between", value1, value2, "relatedcoinstype");
            return (Criteria) this;
        }

        public Criteria andRelatedcoinstypeNotBetween(String value1, String value2) {
            addCriterion("RELATEDCOINSTYPE not between", value1, value2, "relatedcoinstype");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeIsNull() {
            addCriterion("CHANNELDETAILCODE is null");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeIsNotNull() {
            addCriterion("CHANNELDETAILCODE is not null");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeEqualTo(String value) {
            addCriterion("CHANNELDETAILCODE =", value, "channeldetailcode");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeNotEqualTo(String value) {
            addCriterion("CHANNELDETAILCODE <>", value, "channeldetailcode");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeGreaterThan(String value) {
            addCriterion("CHANNELDETAILCODE >", value, "channeldetailcode");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeGreaterThanOrEqualTo(String value) {
            addCriterion("CHANNELDETAILCODE >=", value, "channeldetailcode");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeLessThan(String value) {
            addCriterion("CHANNELDETAILCODE <", value, "channeldetailcode");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeLessThanOrEqualTo(String value) {
            addCriterion("CHANNELDETAILCODE <=", value, "channeldetailcode");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeLike(String value) {
            addCriterion("CHANNELDETAILCODE like", value, "channeldetailcode");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeNotLike(String value) {
            addCriterion("CHANNELDETAILCODE not like", value, "channeldetailcode");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeIn(List<String> values) {
            addCriterion("CHANNELDETAILCODE in", values, "channeldetailcode");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeNotIn(List<String> values) {
            addCriterion("CHANNELDETAILCODE not in", values, "channeldetailcode");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeBetween(String value1, String value2) {
            addCriterion("CHANNELDETAILCODE between", value1, value2, "channeldetailcode");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcodeNotBetween(String value1, String value2) {
            addCriterion("CHANNELDETAILCODE not between", value1, value2, "channeldetailcode");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameIsNull() {
            addCriterion("CHANNELDETAILCNAME is null");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameIsNotNull() {
            addCriterion("CHANNELDETAILCNAME is not null");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameEqualTo(String value) {
            addCriterion("CHANNELDETAILCNAME =", value, "channeldetailcname");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameNotEqualTo(String value) {
            addCriterion("CHANNELDETAILCNAME <>", value, "channeldetailcname");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameGreaterThan(String value) {
            addCriterion("CHANNELDETAILCNAME >", value, "channeldetailcname");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameGreaterThanOrEqualTo(String value) {
            addCriterion("CHANNELDETAILCNAME >=", value, "channeldetailcname");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameLessThan(String value) {
            addCriterion("CHANNELDETAILCNAME <", value, "channeldetailcname");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameLessThanOrEqualTo(String value) {
            addCriterion("CHANNELDETAILCNAME <=", value, "channeldetailcname");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameLike(String value) {
            addCriterion("CHANNELDETAILCNAME like", value, "channeldetailcname");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameNotLike(String value) {
            addCriterion("CHANNELDETAILCNAME not like", value, "channeldetailcname");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameIn(List<String> values) {
            addCriterion("CHANNELDETAILCNAME in", values, "channeldetailcname");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameNotIn(List<String> values) {
            addCriterion("CHANNELDETAILCNAME not in", values, "channeldetailcname");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameBetween(String value1, String value2) {
            addCriterion("CHANNELDETAILCNAME between", value1, value2, "channeldetailcname");
            return (Criteria) this;
        }

        public Criteria andChanneldetailcnameNotBetween(String value1, String value2) {
            addCriterion("CHANNELDETAILCNAME not between", value1, value2, "channeldetailcname");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpensetaxIsNull() {
            addCriterion("COINSISSUEEXPENSETAX is null");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpensetaxIsNotNull() {
            addCriterion("COINSISSUEEXPENSETAX is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpensetaxEqualTo(BigDecimal value) {
            addCriterion("COINSISSUEEXPENSETAX =", value, "coinsissueexpensetax");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpensetaxNotEqualTo(BigDecimal value) {
            addCriterion("COINSISSUEEXPENSETAX <>", value, "coinsissueexpensetax");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpensetaxGreaterThan(BigDecimal value) {
            addCriterion("COINSISSUEEXPENSETAX >", value, "coinsissueexpensetax");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpensetaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSISSUEEXPENSETAX >=", value, "coinsissueexpensetax");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpensetaxLessThan(BigDecimal value) {
            addCriterion("COINSISSUEEXPENSETAX <", value, "coinsissueexpensetax");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpensetaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSISSUEEXPENSETAX <=", value, "coinsissueexpensetax");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpensetaxIn(List<BigDecimal> values) {
            addCriterion("COINSISSUEEXPENSETAX in", values, "coinsissueexpensetax");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpensetaxNotIn(List<BigDecimal> values) {
            addCriterion("COINSISSUEEXPENSETAX not in", values, "coinsissueexpensetax");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpensetaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSISSUEEXPENSETAX between", value1, value2, "coinsissueexpensetax");
            return (Criteria) this;
        }

        public Criteria andCoinsissueexpensetaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSISSUEEXPENSETAX not between", value1, value2, "coinsissueexpensetax");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeetaxIsNull() {
            addCriterion("COINSOTHFEETAX is null");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeetaxIsNotNull() {
            addCriterion("COINSOTHFEETAX is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeetaxEqualTo(BigDecimal value) {
            addCriterion("COINSOTHFEETAX =", value, "coinsothfeetax");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeetaxNotEqualTo(BigDecimal value) {
            addCriterion("COINSOTHFEETAX <>", value, "coinsothfeetax");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeetaxGreaterThan(BigDecimal value) {
            addCriterion("COINSOTHFEETAX >", value, "coinsothfeetax");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeetaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSOTHFEETAX >=", value, "coinsothfeetax");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeetaxLessThan(BigDecimal value) {
            addCriterion("COINSOTHFEETAX <", value, "coinsothfeetax");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeetaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSOTHFEETAX <=", value, "coinsothfeetax");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeetaxIn(List<BigDecimal> values) {
            addCriterion("COINSOTHFEETAX in", values, "coinsothfeetax");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeetaxNotIn(List<BigDecimal> values) {
            addCriterion("COINSOTHFEETAX not in", values, "coinsothfeetax");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeetaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSOTHFEETAX between", value1, value2, "coinsothfeetax");
            return (Criteria) this;
        }

        public Criteria andCoinsothfeetaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSOTHFEETAX not between", value1, value2, "coinsothfeetax");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredcnyIsNull() {
            addCriterion("COINSINSUREDCNY is null");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredcnyIsNotNull() {
            addCriterion("COINSINSUREDCNY is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredcnyEqualTo(BigDecimal value) {
            addCriterion("COINSINSUREDCNY =", value, "coinsinsuredcny");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredcnyNotEqualTo(BigDecimal value) {
            addCriterion("COINSINSUREDCNY <>", value, "coinsinsuredcny");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredcnyGreaterThan(BigDecimal value) {
            addCriterion("COINSINSUREDCNY >", value, "coinsinsuredcny");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredcnyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSINSUREDCNY >=", value, "coinsinsuredcny");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredcnyLessThan(BigDecimal value) {
            addCriterion("COINSINSUREDCNY <", value, "coinsinsuredcny");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredcnyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSINSUREDCNY <=", value, "coinsinsuredcny");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredcnyIn(List<BigDecimal> values) {
            addCriterion("COINSINSUREDCNY in", values, "coinsinsuredcny");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredcnyNotIn(List<BigDecimal> values) {
            addCriterion("COINSINSUREDCNY not in", values, "coinsinsuredcny");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredcnyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSINSUREDCNY between", value1, value2, "coinsinsuredcny");
            return (Criteria) this;
        }

        public Criteria andCoinsinsuredcnyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSINSUREDCNY not between", value1, value2, "coinsinsuredcny");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcnyIsNull() {
            addCriterion("COINSPREMIUMCNY is null");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcnyIsNotNull() {
            addCriterion("COINSPREMIUMCNY is not null");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcnyEqualTo(BigDecimal value) {
            addCriterion("COINSPREMIUMCNY =", value, "coinspremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcnyNotEqualTo(BigDecimal value) {
            addCriterion("COINSPREMIUMCNY <>", value, "coinspremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcnyGreaterThan(BigDecimal value) {
            addCriterion("COINSPREMIUMCNY >", value, "coinspremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcnyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSPREMIUMCNY >=", value, "coinspremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcnyLessThan(BigDecimal value) {
            addCriterion("COINSPREMIUMCNY <", value, "coinspremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcnyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSPREMIUMCNY <=", value, "coinspremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcnyIn(List<BigDecimal> values) {
            addCriterion("COINSPREMIUMCNY in", values, "coinspremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcnyNotIn(List<BigDecimal> values) {
            addCriterion("COINSPREMIUMCNY not in", values, "coinspremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcnyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSPREMIUMCNY between", value1, value2, "coinspremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinspremiumcnyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSPREMIUMCNY not between", value1, value2, "coinspremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumcnyIsNull() {
            addCriterion("COINSNOTAXPREMIUMCNY is null");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumcnyIsNotNull() {
            addCriterion("COINSNOTAXPREMIUMCNY is not null");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumcnyEqualTo(BigDecimal value) {
            addCriterion("COINSNOTAXPREMIUMCNY =", value, "coinsnotaxpremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumcnyNotEqualTo(BigDecimal value) {
            addCriterion("COINSNOTAXPREMIUMCNY <>", value, "coinsnotaxpremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumcnyGreaterThan(BigDecimal value) {
            addCriterion("COINSNOTAXPREMIUMCNY >", value, "coinsnotaxpremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumcnyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSNOTAXPREMIUMCNY >=", value, "coinsnotaxpremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumcnyLessThan(BigDecimal value) {
            addCriterion("COINSNOTAXPREMIUMCNY <", value, "coinsnotaxpremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumcnyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSNOTAXPREMIUMCNY <=", value, "coinsnotaxpremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumcnyIn(List<BigDecimal> values) {
            addCriterion("COINSNOTAXPREMIUMCNY in", values, "coinsnotaxpremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumcnyNotIn(List<BigDecimal> values) {
            addCriterion("COINSNOTAXPREMIUMCNY not in", values, "coinsnotaxpremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumcnyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSNOTAXPREMIUMCNY between", value1, value2, "coinsnotaxpremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinsnotaxpremiumcnyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSNOTAXPREMIUMCNY not between", value1, value2, "coinsnotaxpremiumcny");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountcnyIsNull() {
            addCriterion("COINSTAXAMOUNTCNY is null");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountcnyIsNotNull() {
            addCriterion("COINSTAXAMOUNTCNY is not null");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountcnyEqualTo(BigDecimal value) {
            addCriterion("COINSTAXAMOUNTCNY =", value, "coinstaxamountcny");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountcnyNotEqualTo(BigDecimal value) {
            addCriterion("COINSTAXAMOUNTCNY <>", value, "coinstaxamountcny");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountcnyGreaterThan(BigDecimal value) {
            addCriterion("COINSTAXAMOUNTCNY >", value, "coinstaxamountcny");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountcnyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSTAXAMOUNTCNY >=", value, "coinstaxamountcny");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountcnyLessThan(BigDecimal value) {
            addCriterion("COINSTAXAMOUNTCNY <", value, "coinstaxamountcny");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountcnyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COINSTAXAMOUNTCNY <=", value, "coinstaxamountcny");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountcnyIn(List<BigDecimal> values) {
            addCriterion("COINSTAXAMOUNTCNY in", values, "coinstaxamountcny");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountcnyNotIn(List<BigDecimal> values) {
            addCriterion("COINSTAXAMOUNTCNY not in", values, "coinstaxamountcny");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountcnyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSTAXAMOUNTCNY between", value1, value2, "coinstaxamountcny");
            return (Criteria) this;
        }

        public Criteria andCoinstaxamountcnyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COINSTAXAMOUNTCNY not between", value1, value2, "coinstaxamountcny");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyIsNull() {
            addCriterion("CURRENCYCNY is null");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyIsNotNull() {
            addCriterion("CURRENCYCNY is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyEqualTo(String value) {
            addCriterion("CURRENCYCNY =", value, "currencycny");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyNotEqualTo(String value) {
            addCriterion("CURRENCYCNY <>", value, "currencycny");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyGreaterThan(String value) {
            addCriterion("CURRENCYCNY >", value, "currencycny");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyGreaterThanOrEqualTo(String value) {
            addCriterion("CURRENCYCNY >=", value, "currencycny");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyLessThan(String value) {
            addCriterion("CURRENCYCNY <", value, "currencycny");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyLessThanOrEqualTo(String value) {
            addCriterion("CURRENCYCNY <=", value, "currencycny");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyLike(String value) {
            addCriterion("CURRENCYCNY like", value, "currencycny");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyNotLike(String value) {
            addCriterion("CURRENCYCNY not like", value, "currencycny");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyIn(List<String> values) {
            addCriterion("CURRENCYCNY in", values, "currencycny");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyNotIn(List<String> values) {
            addCriterion("CURRENCYCNY not in", values, "currencycny");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyBetween(String value1, String value2) {
            addCriterion("CURRENCYCNY between", value1, value2, "currencycny");
            return (Criteria) this;
        }

        public Criteria andCurrencycnyNotBetween(String value1, String value2) {
            addCriterion("CURRENCYCNY not between", value1, value2, "currencycny");
            return (Criteria) this;
        }

        public Criteria andSerialnoIsNull() {
            addCriterion("SERIALNO is null");
            return (Criteria) this;
        }

        public Criteria andSerialnoIsNotNull() {
            addCriterion("SERIALNO is not null");
            return (Criteria) this;
        }

        public Criteria andSerialnoEqualTo(Integer value) {
            addCriterion("SERIALNO =", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoNotEqualTo(Integer value) {
            addCriterion("SERIALNO <>", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoGreaterThan(Integer value) {
            addCriterion("SERIALNO >", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoGreaterThanOrEqualTo(Integer value) {
            addCriterion("SERIALNO >=", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoLessThan(Integer value) {
            addCriterion("SERIALNO <", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoLessThanOrEqualTo(Integer value) {
            addCriterion("SERIALNO <=", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoIn(List<Integer> values) {
            addCriterion("SERIALNO in", values, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoNotIn(List<Integer> values) {
            addCriterion("SERIALNO not in", values, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoBetween(Integer value1, Integer value2) {
            addCriterion("SERIALNO between", value1, value2, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoNotBetween(Integer value1, Integer value2) {
            addCriterion("SERIALNO not between", value1, value2, "serialno");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}