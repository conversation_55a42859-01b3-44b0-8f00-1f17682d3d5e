package ins.channel.gupolicycoinsurance.dto.domain;

public class GuPolicyCoinsuranceDtoKey {
    private String policyno;

    private String coinscode;

    private String plancode;

    private String riskcode;

    private String secondlevelcoinscode;

    private String thirdlevelcoinsname;

    public String getPolicyno() {
        return policyno;
    }

    public void setPolicyno(String policyno) {
        this.policyno = policyno == null ? null : policyno.trim();
    }

    public String getCoinscode() {
        return coinscode;
    }

    public void setCoinscode(String coinscode) {
        this.coinscode = coinscode == null ? null : coinscode.trim();
    }

    public String getPlancode() {
        return plancode;
    }

    public void setPlancode(String plancode) {
        this.plancode = plancode == null ? null : plancode.trim();
    }

    public String getRiskcode() {
        return riskcode;
    }

    public void setRiskcode(String riskcode) {
        this.riskcode = riskcode == null ? null : riskcode.trim();
    }

    public String getSecondlevelcoinscode() {
        return secondlevelcoinscode;
    }

    public void setSecondlevelcoinscode(String secondlevelcoinscode) {
        this.secondlevelcoinscode = secondlevelcoinscode == null ? null : secondlevelcoinscode.trim();
    }

    public String getThirdlevelcoinsname() {
        return thirdlevelcoinsname;
    }

    public void setThirdlevelcoinsname(String thirdlevelcoinsname) {
        this.thirdlevelcoinsname = thirdlevelcoinsname == null ? null : thirdlevelcoinsname.trim();
    }
}