package ins.channel.gupolicycopycoinsfee.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GupolicycopycoinsfeeVo对象.对应实体描述：最新保单共保份额表
 *
 */
@Data
@ApiModel("GupolicycopycoinsfeeVo对象")
public class GupolicycopycoinsfeeVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@ApiModelProperty("主键")
	private String id;
	/** 对应字段：POLICYNO,备注：保单号 */
	@ApiModelProperty("保单号")
	private String policyNo;
	/** 对应字段：ENDORNO,备注：批改申请号/申报编号 */
	@ApiModelProperty("批改申请号/申报编号")
	private String endorNo;
	/** 对应字段：SERIALNO,备注：序号 */
	@ApiModelProperty("序号")
	private BigDecimal serialNo;
	/** 对应字段：COINSPOLICYNO,备注：共保保单号 */
	@ApiModelProperty("共保保单号")
	private String coinspolicyno;
	/** 对应字段：COINSCODE,备注：共保人代码 */
	@ApiModelProperty("共保人代码")
	private String coinsCode;
	/** 对应字段：COINSNAME,备注：共保人名称 */
	@ApiModelProperty("共保人名称")
	private String coinsName;
	/** 对应字段：COINSTYPE,备注：共保类型 1：我方；2：系统内他方；3：系统外他方；代码对应数据表 GgCode.Code（CodeType=CoinsType） */
	@ApiModelProperty("共保类型 1：我方；2：系统内他方；3：系统外他方；代码对应数据表 GgCode.Code（CodeType=CoinsType）")
	private String coinsType;
	/** 对应字段：COINSRATE,备注：共保份额 */
	@ApiModelProperty("共保份额")
	private BigDecimal coinsRate;
	/** 对应字段：PRINCIPALIND,备注：共保人类型 1 主方/2 从方；代码对应数据表GgCode.CodeCode（CodeType=PrincipalInd） */
	@ApiModelProperty("共保人类型 1 主方/2 从方；代码对应数据表GgCode.CodeCode（CodeType=PrincipalInd）")
	private String principalind;
	/** 对应字段：LEADERIND,备注：是否首席承保人 0 否 1 是代码对应数据表GgCode.CodeCode（CodeType=LeaderInd） */
	@ApiModelProperty("是否首席承保人 0 否 1 是代码对应数据表GgCode.CodeCode（CodeType=LeaderInd）")
	private String leaderind;
	/** 对应字段：CHIEFADJUSTERIND,备注：是否首席理赔人 0 否/1 是；代码对应数据表GgCode.CodeCode（CodeType=ChiefAdjusterInd） */
	@ApiModelProperty("是否首席理赔人 0 否/1 是；代码对应数据表GgCode.CodeCode（CodeType=ChiefAdjusterInd）")
	private String chiefadjusterind;
	/** 对应字段：CURRENCY,备注：币别 */
	@ApiModelProperty("币别")
	private String currency;
	/** 对应字段：COINSINSURED,备注：共保保额 */
	@ApiModelProperty("共保保额")
	private BigDecimal coinsinsured;
	/** 对应字段：COINSPREMIUM,备注：共保含税保费 */
	@ApiModelProperty("共保含税保费")
	private BigDecimal coinsPremium;
	/** 对应字段：CHANGECOINSINSURED,备注：共保保额变化量 */
	@ApiModelProperty("共保保额变化量")
	private BigDecimal changecoinsinsured;
	/** 对应字段：CHANGECOINSPREMIUM,备注：共保含税保费变化量 */
	@ApiModelProperty("共保含税保费变化量")
	private BigDecimal changecoinspremium;
	/** 对应字段：COINSHANDLINGRATE,备注：共保手续费比例 */
	@ApiModelProperty("共保手续费比例")
	private BigDecimal coinshandlingrate;
	/** 对应字段：COINSHANDLINGFEE,备注：共保手续费 */
	@ApiModelProperty("共保手续费")
	private BigDecimal coinshandlingfee;
	/** 对应字段：CHANGECOINSHANDLINGFEE,备注：共保手续费变化量 */
	@ApiModelProperty("共保手续费变化量")
	private BigDecimal changecoinshandlingfee;
	/** 对应字段：COINSAGENCYRATE,备注：共保代理佣金比例 */
	@ApiModelProperty("共保代理佣金比例")
	private BigDecimal coinsagencyrate;
	/** 对应字段：COINSAGENCYCOMMISSION,备注：共保代理佣金 */
	@ApiModelProperty("共保代理佣金")
	private BigDecimal coinsagencycommission;
	/** 对应字段：CHANGECOINSAGENCYCOMMISSION,备注：共保代理佣金变化量 */
	@ApiModelProperty("共保代理佣金变化量")
	private BigDecimal changecoinsagencycommission;
	/** 对应字段：COINSISSUERATE,备注：共保出单费比例 */
	@ApiModelProperty("共保出单费比例")
	private BigDecimal coinsissuerate;
	/** 对应字段：COINSISSUEEXPENSE,备注：共保出单费 */
	@ApiModelProperty("共保出单费")
	private BigDecimal coinsissueexpense;
	/** 对应字段：CHANGECOINSISSUEEXPENSE,备注：共保出单费变化量 */
	@ApiModelProperty("共保出单费变化量")
	private BigDecimal changecoinsissueexpense;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
	/** 对应字段：COINSPREMIUMACCEPTIND,备注：共保保费收取形式 1 从客户处收取/2 从主方处收取； */
	@ApiModelProperty("共保保费收取形式 1 从客户处收取/2 从主方处收取；")
	private String coinspremiumacceptind;
	/** 对应字段：COINSPREMIUMCOMPOSE,备注：共保保费组成,：第一位 净手续费；第二位 净代理佣金；第三位 净出单费；第四位 净政府徴费；第五位 净賠償儲備金；第六位 净其他费用；0 否 1 是 默认6个0 */
	@ApiModelProperty("共保保费组成,：第一位 净手续费；第二位 净代理佣金；第三位 净出单费；第四位 净政府徴费；第五位 净賠償儲備金；第六位 净其他费用；0 否 1 是 默认6个0")
	private String coinspremiumcompose;
	/** 对应字段：COINSAGENCYPAYIND,备注：共保代理佣金支付形式 0 付给代理 1 -付给主方； */
	@ApiModelProperty("共保代理佣金支付形式 0 付给代理 1 -付给主方；")
	private String coinsagencypayind;
	/** 对应字段：COINSCLAIMIND,备注：支付方式 0：我方代支付；1：按比例支付 */
	@ApiModelProperty("支付方式 0：我方代支付；1：按比例支付")
	private String coinsclaimind;
	/** 对应字段：PLANCODE,备注：计划代码 */
	@ApiModelProperty("计划代码")
	private String plancode;
	/** 对应字段：RISKCODE,备注：险种代码 */
	@ApiModelProperty("险种代码")
	private String riskCode;
	/** 对应字段：SUBPOLICYNO,备注：分单号 */
	@ApiModelProperty("分单号")
	private String subpolicyno;
	/** 对应字段：COINSNOTAXPREMIUM,备注：不含税保费 */
	@ApiModelProperty("不含税保费")
	private BigDecimal coinsnotaxpremium;
	/** 对应字段：COINSTAXAMOUNT,备注：增值税额 */
	@ApiModelProperty("增值税额")
	private BigDecimal coinstaxamount;
	/** 对应字段：CHANGECOINSNOTAXPREMIUM,备注：不含税保费变化量 */
	@ApiModelProperty("不含税保费变化量")
	private BigDecimal changecoinsnotaxpremium;
	/** 对应字段：CHANGECOINSTAXAMOUNT,备注：增值税额变化量 */
	@ApiModelProperty("增值税额变化量")
	private BigDecimal changecoinstaxamount;
	/** 对应字段：SECONDLEVELCOINSCODE,备注：共保人二级代码 */
	@ApiModelProperty("共保人二级代码")
	private String secondlevelcoinscode;
	/** 对应字段：SECONDLEVELCOINSNAME,备注：共保人二级名称 */
	@ApiModelProperty("共保人二级名称")
	private String secondlevelcoinsname;
	/** 对应字段：THIRDLEVELCOINSNAME,备注：共保人三级名称 */
	@ApiModelProperty("共保人三级名称")
	private String thirdlevelcoinsname;
	/** 对应字段：SEALSTEAMCODE,备注：销售团队代码 */
	@ApiModelProperty("销售团队代码")
	private String sealsteamcode;
	/** 对应字段：SEALSTEAMCNAME,备注：销售团队名称 */
	@ApiModelProperty("销售团队名称")
	private String sealsteamcname;
	/** 对应字段：SEALSMANCODE,备注：业务员代码 */
	@ApiModelProperty("业务员代码")
	private String sealsmancode;
	/** 对应字段：SEALSMANCNAME,备注：业务员名称 */
	@ApiModelProperty("业务员名称")
	private String sealsmancname;
	/** 对应字段：CHANNELDETAILCODE,备注：渠道代码 */
	@ApiModelProperty("渠道代码")
	private String channeldetailcode;
	/** 对应字段：CHANNELDETAILCNAME,备注：渠道名称 */
	@ApiModelProperty("渠道名称")
	private String channeldetailcname;
	/** 对应字段：CURRENCYCNY,备注：本位币币别 */
	@ApiModelProperty("本位币币别")
	private String currencycny;
	/** 对应字段：COINSINSUREDCNY,备注：共保保额本位币金额 */
	@ApiModelProperty("共保保额本位币金额")
	private BigDecimal coinsinsuredcny;
	/** 对应字段：COINSPREMIUMCNY,备注：共保保费本位币金额 */
	@ApiModelProperty("共保保费本位币金额")
	private BigDecimal coinspremiumcny;
	/** 对应字段：COINSNOTAXPREMIUMCNY,备注：共保不含税保费本位币金额 */
	@ApiModelProperty("共保不含税保费本位币金额")
	private BigDecimal coinsnotaxpremiumcny;
	/** 对应字段：COINSTAXAMOUNTCNY,备注：共保税额本位币金额 */
	@ApiModelProperty("共保税额本位币金额")
	private BigDecimal coinstaxamountcny;
	/** 对应字段：CHANGECOINSINSUREDCNY,备注：共保保额本位币金额变化量 */
	@ApiModelProperty("共保保额本位币金额变化量")
	private BigDecimal changecoinsinsuredcny;
	/** 对应字段：CHANGECOINSPREMIUMCNY,备注：共保保费本位币金额变化量 */
	@ApiModelProperty("共保保费本位币金额变化量")
	private BigDecimal changecoinspremiumcny;
	/** 对应字段：CHANGECOINSNOTAXPREMIUMCNY,备注：共保不含税保费本位币金额变化量 */
	@ApiModelProperty("共保不含税保费本位币金额变化量")
	private BigDecimal changecoinsnotaxpremiumcny;
	/** 对应字段：CHANGECOINSTAXAMOUNTCNY,备注：共保税额本位币金额变化量 */
	@ApiModelProperty("共保税额本位币金额变化量")
	private BigDecimal changecoinstaxamountcny;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@ApiModelProperty("入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@ApiModelProperty("修改日期")
	private Date updatesysdate;
}
