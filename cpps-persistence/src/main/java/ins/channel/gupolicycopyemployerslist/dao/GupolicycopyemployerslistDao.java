package ins.channel.gupolicycopyemployerslist.dao;

import ins.channel.gupolicymain.po.Gupolicymain;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicycopyemployerslist.po.Gupolicycopyemployerslist;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYCOPYEMPLOYERSLIST对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicycopyemployerslistDao extends MybatisBaseDao<Gupolicycopyemployerslist, String> {

    List<Gupolicycopyemployerslist> selectByCondition(Gupolicycopyemployerslist copyemployerslistCondition);
    List<Gupolicycopyemployerslist> searchBypolicyNo(Gupolicycopyemployerslist copyemployerslistCondition);

    Page<Gupolicycopyemployerslist> selectByConditionForPage(PageParam pageParam, Gupolicycopyemployerslist copyemployerslistCondition);

    Gupolicycopyemployerslist selectByEditType(Gupolicycopyemployerslist copyemployerslistCondition);

    int batchUpdate(List<Gupolicycopyemployerslist> list);

    int batchUpdateSelective(List<Gupolicycopyemployerslist> list);

    int batchInsert(List<Gupolicycopyemployerslist> list);

    int deleteByEndorNo(String endorNo);

    //根据申报号修改信息
    int updateEndorNo(String endorNo);

    void deleteByPolicyNo(String policyNo);
}
