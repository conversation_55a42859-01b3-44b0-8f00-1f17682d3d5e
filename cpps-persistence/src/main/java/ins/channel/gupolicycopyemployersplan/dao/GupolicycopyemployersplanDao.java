package ins.channel.gupolicycopyemployersplan.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicycopyemployersplan.po.Gupolicycopyemployersplan;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYCOPYEMPLOYERSPLAN对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicycopyemployersplanDao extends MybatisBaseDao<Gupolicycopyemployersplan, String> {

    Gupolicycopyemployersplan selectByCondition(Gupolicycopyemployersplan policycopyemployersplanCondition);

    int batchInsert(List<Gupolicycopyemployersplan> gupolicycopyemployersplanList);

    int deleteByEndorNo(String endorNo);

    void deleteByPolicyNo(String policyNo);
}
