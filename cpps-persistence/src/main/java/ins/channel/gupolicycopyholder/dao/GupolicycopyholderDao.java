package ins.channel.gupolicycopyholder.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicycopyholder.po.Gupolicycopyholder;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYCOPYHOLDER对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicycopyholderDao extends MybatisBaseDao<Gupolicycopyholder, String> {

    //批量插入
    int batchInsert(List<Gupolicycopyholder> gupolicycopyholderList);

    void deleteByPolicyNo(String policyNo);
}
