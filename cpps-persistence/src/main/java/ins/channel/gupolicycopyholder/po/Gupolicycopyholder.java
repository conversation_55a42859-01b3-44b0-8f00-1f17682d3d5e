package ins.channel.gupolicycopyholder.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GUPOLICYCOPYHOLDER的PO对象<br/>
 * 对应表名：GUPOLICYCOPYHOLDER,备注：保单投保人信息轨迹表
 *
 */
@Data
@Table(name = "GUPOLICYCOPYHOLDER")
public class Gupolicycopyholder implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@Column(name = "ID", description = "主键")
	private String id;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@Column(name = "POLICYNO", description = "保单号码")
	private String policyNo;
	/** 对应字段：ENDORNO,备注：批改申请号/申报编号 */
	@Column(name = "ENDORNO", description = "批改申请号/申报编号")
	private String endorNo;
	/** 对应字段：PRODUCTCODE,备注：产品代码 */
	@Column(name = "PRODUCTCODE", description = "产品代码")
	private String productcode;
	/** 对应字段：INSURANCECOMPANYCODE,备注：保险公司归属机构 */
	@Column(name = "INSURANCECOMPANYCODE", description = "保险公司归属机构")
	private String insurancecompanycode;
	/** 对应字段：INSUREDTYPE,备注：投保人类型 1-个人 2-企业 */
	@Column(name = "INSUREDTYPE", description = "投保人类型 1-个人 2-企业")
	private String insuredType;
	/** 对应字段：VIPIND,备注：VIP客户分类 0-普通客户1-集团内公车 2-集团内员工车 3-VIP团体客户 4-其他VIP客户 */
	@Column(name = "VIPIND", description = "VIP客户分类 0-普通客户1-集团内公车 2-集团内员工车 3-VIP团体客户 4-其他VIP客户")
	private String vipind;
	/** 对应字段：INSUREDCODE,备注：投保人代码 */
	@Column(name = "INSUREDCODE", description = "投保人代码")
	private String insuredCode;
	/** 对应字段：INSUREDNAME,备注：投保人名称 */
	@Column(name = "INSUREDNAME", description = "投保人名称")
	private String insuredName;
	/** 对应字段：INSUREDADDRESS,备注：投保人地址 */
	@Column(name = "INSUREDADDRESS", description = "投保人地址")
	private String insuredAddress;
	/** 对应字段：IDENTIFYTYPE,备注：投保人证件类型 02-身份证 02-护照03-军官 04-驾驶证 05-港澳台同胞证 06-港澳台居民居住证 07-中国护照 08-外国人永久居留身份证99-其他 */
	@Column(name = "IDENTIFYTYPE", description = "投保人证件类型 02-身份证 02-护照03-军官 04-驾驶证 05-港澳台同胞证 06-港澳台居民居住证 07-中国护照 08-外国人永久居留身份证99-其他")
	private String identifyType;
	/** 对应字段：IDENTIFYNUMBER,备注：证件号码 */
	@Column(name = "IDENTIFYNUMBER", description = "证件号码")
	private String identifyNumber;
	/** 对应字段：SEX,备注：性别 1-男 2-女 */
	@Column(name = "SEX", description = "性别 1-男 2-女")
	private String sex;
	/** 对应字段：BIRTHDATE,备注：出生日期 */
	@Column(name = "BIRTHDATE", description = "出生日期")
	private Date birthDate;
	/** 对应字段：STARLEVEL,备注：增值服务类型 0-普通客户 1-钻石VIP客户 2-铂金VIP客户 3-黄金VIP客户 */
	@Column(name = "STARLEVEL", description = "增值服务类型 0-普通客户 1-钻石VIP客户 2-铂金VIP客户 3-黄金VIP客户")
	private String starlevel;
	/** 对应字段：SOCIALSECURITYNO,备注：社保保险登记证号 */
	@Column(name = "SOCIALSECURITYNO", description = "社保保险登记证号")
	private String socialsecurityno;
	/** 对应字段：ITEMPROVINCECODE,备注：省份代码 */
	@Column(name = "ITEMPROVINCECODE", description = "省份代码")
	private String itemprovincecode;
	/** 对应字段：ITEMPROVINCECNAME,备注：省份名称 */
	@Column(name = "ITEMPROVINCECNAME", description = "省份名称")
	private String itemprovincecname;
	/** 对应字段：ITEMCITYCODE,备注：城市代码 */
	@Column(name = "ITEMCITYCODE", description = "城市代码")
	private String itemcitycode;
	/** 对应字段：ITEMCITYCNAME,备注：城市名称 */
	@Column(name = "ITEMCITYCNAME", description = "城市名称")
	private String itemcitycname;
	/** 对应字段：COUNTYCODE,备注：区（县级）代码 */
	@Column(name = "COUNTYCODE", description = "区（县级）代码")
	private String countycode;
	/** 对应字段：COUNTYCNAME,备注：区（县级）名称 */
	@Column(name = "COUNTYCNAME", description = "区（县级）名称")
	private String countycname;
	/** 对应字段：POSTCODE,备注：邮政编码 */
	@Column(name = "POSTCODE", description = "邮政编码")
	private String postCode;
	/** 对应字段：MOBILEPHONE,备注：投保人手机号 */
	@Column(name = "MOBILEPHONE", description = "投保人手机号")
	private String mobilephone;
	/** 对应字段：OFFICEPHONE,备注：办公电话(个人)/单位电话(团体) */
	@Column(name = "OFFICEPHONE", description = "办公电话(个人)/单位电话(团体)")
	private String officephone;
	/** 对应字段：HOMEPHONE,备注：家庭电话 */
	@Column(name = "HOMEPHONE", description = "家庭电话")
	private String homePhone;
	/** 对应字段：BANKNAME,备注：开户行名称 */
	@Column(name = "BANKNAME", description = "开户行名称")
	private String bankName;
	/** 对应字段：BANKNUMBER,备注：银行帐号 */
	@Column(name = "BANKNUMBER", description = "银行帐号")
	private String banknumber;
	/** 对应字段：OCCUPATIONCODE,备注：职业代码,来源ggcode.00-未知 10-公务员 20-金融单位员工 30-事业单位员工 40-律师或医生 50-企业高管人员 60-企业职员 70-自由职业者 80-客户或其实际受益人、实际控制人、亲属、关系密切人等属于外国政要 99-其他 */
	@Column(name = "OCCUPATIONCODE", description = "职业代码,来源ggcode.00-未知 10-公务员 20-金融单位员工 30-事业单位员工 40-律师或医生 50-企业高管人员 60-企业职员 70-自由职业者 80-客户或其实际受益人、实际控制人、亲属、关系密切人等属于外国政要 99-其他")
	private String occupationCode;
	/** 对应字段：COMPANYNATURE,备注：单位性质 来源:ggode表,codetype =&#39;CompanyNature&#39;  */
	@Column(name = "COMPANYNATURE", description = "单位性质 来源:ggode表,codetype =&#39;CompanyNature&#39; ")
	private String companynature;
	/** 对应字段：NUMBEROFUNITS,备注：单位总人数 */
	@Column(name = "NUMBEROFUNITS", description = "单位总人数")
	private BigDecimal numberofunits;
	/** 对应字段：EMAIL,备注：投保人邮箱 */
	@Column(name = "EMAIL", description = "投保人邮箱")
	private String email;
	/** 对应字段：INDUSTRYMAINCODE,备注：行业大类 */
	@Column(name = "INDUSTRYMAINCODE", description = "行业大类")
	private String industrymaincode;
	/** 对应字段：INDUSTRYKINDCODE,备注：行业小类 */
	@Column(name = "INDUSTRYKINDCODE", description = "行业小类")
	private String industrykindcode;
	/** 对应字段：CONTACTNAME,备注：联系人 */
	@Column(name = "CONTACTNAME", description = "联系人")
	private String contactname;
	/** 对应字段：CONTACTSEX,备注：联系人性别 */
	@Column(name = "CONTACTSEX", description = "联系人性别")
	private String contactsex;
	/** 对应字段：CONTACTPOSITION,备注：联系人职务 */
	@Column(name = "CONTACTPOSITION", description = "联系人职务")
	private String contactposition;
	/** 对应字段：CONTACTDEPARTMENT,备注：联系人部门 */
	@Column(name = "CONTACTDEPARTMENT", description = "联系人部门")
	private String contactdepartment;
	/** 对应字段：CONTACTOFFICENUMBER,备注：联系人个人办公电话 */
	@Column(name = "CONTACTOFFICENUMBER", description = "联系人个人办公电话")
	private String contactofficenumber;
	/** 对应字段：CONTACTMOBILE,备注：联系人手机 */
	@Column(name = "CONTACTMOBILE", description = "联系人手机")
	private String contactmobile;
	/** 对应字段：CONTACTPHONE,备注：联系人家庭电话 */
	@Column(name = "CONTACTPHONE", description = "联系人家庭电话")
	private String contactphone;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@Column(name = "INPUTDATE", description = "入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@Column(name = "UPDATESYSDATE", description = "修改日期")
	private Date updatesysdate;
}
