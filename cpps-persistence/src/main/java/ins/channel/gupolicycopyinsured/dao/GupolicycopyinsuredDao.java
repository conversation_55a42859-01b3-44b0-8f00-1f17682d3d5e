package ins.channel.gupolicycopyinsured.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicycopyinsured.po.Gupolicycopyinsured;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYCOPYINSURED对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicycopyinsuredDao extends MybatisBaseDao<Gupolicycopyinsured, String> {

    //批量插入
    int batchInsert(List<Gupolicycopyinsured> gupolicycopyinsuredList);

    void deleteByPolicyNo(String policyNo);
}
