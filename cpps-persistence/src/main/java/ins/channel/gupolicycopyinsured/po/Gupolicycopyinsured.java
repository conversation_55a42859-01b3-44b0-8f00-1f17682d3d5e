package ins.channel.gupolicycopyinsured.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GUPOLICYCOPYINSURED的PO对象<br/>
 * 对应表名：GUPOLICYCOPYINSURED,备注：保单被保险人信息轨迹表
 *
 */
@Data
@Table(name = "GUPOLICYCOPYINSURED")
public class Gupolicycopyinsured implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@Column(name = "ID", description = "主键")
	private String id;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@Column(name = "POLICYNO", description = "保单号码")
	private String policyNo;
	/** 对应字段：ENDORNO,备注：批改申请号/申报编号 */
	@Column(name = "ENDORNO", description = "批改申请号/申报编号")
	private String endorNo;
	/** 对应字段：PLANCODE,备注：计划代码 0000表示没有计划 */
	@Column(name = "PLANCODE", description = "计划代码 0000表示没有计划")
	private String plancode;
	/** 对应字段：RISKCODE,备注：险种代码 */
	@Column(name = "RISKCODE", description = "险种代码")
	private String riskCode;
	/** 对应字段：SUBPOLICYNO,备注：分单号 */
	@Column(name = "SUBPOLICYNO", description = "分单号")
	private String subpolicyno;
	/** 对应字段：SERIALNO,备注：由关系人类型、关系人代码决定; */
	@Column(name = "SERIALNO", description = "由关系人类型、关系人代码决定;")
	private BigDecimal serialNo;
	/** 对应字段：INSUREDTYPE,备注：被保人类型  1 个人/2 单位； */
	@Column(name = "INSUREDTYPE", description = "被保人类型  1 个人/2 单位；")
	private String insuredType;
	/** 对应字段：INSUREDCODE,备注：关系人代码 */
	@Column(name = "INSUREDCODE", description = "关系人代码")
	private String insuredCode;
	/** 对应字段：INSUREDNAME,备注：关系人名称 */
	@Column(name = "INSUREDNAME", description = "关系人名称")
	private String insuredName;
	/** 对应字段：INSUREDADDRESS,备注：关系人地址 */
	@Column(name = "INSUREDADDRESS", description = "关系人地址")
	private String insuredAddress;
	/** 对应字段：INSUREDBUSINESSSOURCE,备注：关系人营业性质 */
	@Column(name = "INSUREDBUSINESSSOURCE", description = "关系人营业性质")
	private String insuredbusinesssource;
	/** 对应字段：INSUREDFLAG,备注：2 被保险人 4 受益人 */
	@Column(name = "INSUREDFLAG", description = "2 被保险人 4 受益人")
	private String insuredFlag;
	/** 对应字段：INSUREDROLE,备注：0 次承包商/1 主承包商； */
	@Column(name = "INSUREDROLE", description = "0 次承包商/1 主承包商；")
	private String insuredrole;
	/** 对应字段：IDENTIFYTYPE,备注：证件类型   01 身份证/02 户口簿/03 护照/04 军官证/05 驾驶执照/06 返乡证/99 其他 */
	@Column(name = "IDENTIFYTYPE", description = "证件类型   01 身份证/02 户口簿/03 护照/04 军官证/05 驾驶执照/06 返乡证/99 其他")
	private String identifyType;
	/** 对应字段：IDENTIFYNUMBER,备注：个人证件号码/法人组织机构代码 */
	@Column(name = "IDENTIFYNUMBER", description = "个人证件号码/法人组织机构代码")
	private String identifyNumber;
	/** 对应字段：INSUREDRELATION,备注：代码维护字段 */
	@Column(name = "INSUREDRELATION", description = "代码维护字段")
	private String insuredrelation;
	/** 对应字段：RELATESERIALNO,备注：关联人序号 */
	@Column(name = "RELATESERIALNO", description = "关联人序号")
	private BigDecimal relateSerialNo;
	/** 对应字段：INSUREDIND,备注：0 否/1 是； */
	@Column(name = "INSUREDIND", description = "0 否/1 是；")
	private String insuredind;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@Column(name = "FLAG", description = "标志字段")
	private String flag;
	/** 对应字段：INSUREDSEX,备注：性别 */
	@Column(name = "INSUREDSEX", description = "性别")
	private String insuredsex;
	/** 对应字段：INSUREDBIRTHDATE,备注：出生日期 */
	@Column(name = "INSUREDBIRTHDATE", description = "出生日期")
	private Date insuredbirthdate;
	/** 对应字段：INSUREDPOSTCODE,备注：邮政编码 */
	@Column(name = "INSUREDPOSTCODE", description = "邮政编码")
	private String insuredpostcode;
	/** 对应字段：INSUREDOFFICEPHONE,备注：办公电话 */
	@Column(name = "INSUREDOFFICEPHONE", description = "办公电话")
	private String insuredofficephone;
	/** 对应字段：INSUREDMOBILEPHONE,备注：手机号 */
	@Column(name = "INSUREDMOBILEPHONE", description = "手机号")
	private String insuredmobilephone;
	/** 对应字段：INSUREDHOMEPHONE,备注：家庭电话 */
	@Column(name = "INSUREDHOMEPHONE", description = "家庭电话")
	private String insuredhomephone;
	/** 对应字段：CONTACTNAME,备注：联系人 */
	@Column(name = "CONTACTNAME", description = "联系人")
	private String contactname;
	/** 对应字段：CONTACTPHONE,备注：联系人电话 */
	@Column(name = "CONTACTPHONE", description = "联系人电话")
	private String contactphone;
	/** 对应字段：MARRIAGESTATUS,备注：婚姻状况 */
	@Column(name = "MARRIAGESTATUS", description = "婚姻状况")
	private String marriagestatus;
	/** 对应字段：EDUCATIONBACKGROUND,备注：学历 */
	@Column(name = "EDUCATIONBACKGROUND", description = "学历")
	private String educationbackground;
	/** 对应字段：RELATIONWITHHOLDER,备注：被保险人与投保人关系 */
	@Column(name = "RELATIONWITHHOLDER", description = "被保险人与投保人关系")
	private String relationwithholder;
	/** 对应字段：CONTACTSEX,备注：联系人性别 */
	@Column(name = "CONTACTSEX", description = "联系人性别")
	private String contactsex;
	/** 对应字段：CONTACTTYPE,备注：联系人类型 */
	@Column(name = "CONTACTTYPE", description = "联系人类型")
	private String contacttype;
	/** 对应字段：CONTACTDEPARTMENT,备注：联系人部门 */
	@Column(name = "CONTACTDEPARTMENT", description = "联系人部门")
	private String contactdepartment;
	/** 对应字段：CONTACTPOSITION,备注：联系人职务 */
	@Column(name = "CONTACTPOSITION", description = "联系人职务")
	private String contactposition;
	/** 对应字段：CONTACTOFFICENUMBER,备注：联系人个人办公电话 */
	@Column(name = "CONTACTOFFICENUMBER", description = "联系人个人办公电话")
	private String contactofficenumber;
	/** 对应字段：CONTACTMOBILE,备注：联系人手机 */
	@Column(name = "CONTACTMOBILE", description = "联系人手机")
	private String contactmobile;
	/** 对应字段：RELATEDTYPE,备注：关系人类型 */
	@Column(name = "RELATEDTYPE", description = "关系人类型")
	private String relatedtype;
	/** 对应字段：ITEMPROVINCECODE,备注：省份代码 */
	@Column(name = "ITEMPROVINCECODE", description = "省份代码")
	private String itemprovincecode;
	/** 对应字段：ITEMCITYCODE,备注：城市代码 */
	@Column(name = "ITEMCITYCODE", description = "城市代码")
	private String itemcitycode;
	/** 对应字段：ITEMPROVINCECNAME,备注：省份名称 */
	@Column(name = "ITEMPROVINCECNAME", description = "省份名称")
	private String itemprovincecname;
	/** 对应字段：ITEMCITYCNAME,备注：城市名称 */
	@Column(name = "ITEMCITYCNAME", description = "城市名称")
	private String itemcitycname;
	/** 对应字段：MONEYLAUNDERINGIND,备注：洗钱风险 1-高，2-中，3-低； ggcode.codecode where codetype=“moneylaunderingind” */
	@Column(name = "MONEYLAUNDERINGIND", description = "洗钱风险 1-高，2-中，3-低； ggcode.codecode where codetype=“moneylaunderingind”")
	private String moneylaunderingind;
	/** 对应字段：STARLEVEL,备注：增值服务类型 */
	@Column(name = "STARLEVEL", description = "增值服务类型")
	private String starlevel;
	/** 对应字段：VIPIND,备注：vip客户分类 0-普通客户1-集团内公车 2-集团内员工车 3-vip团体客户 4-其他vip客户 */
	@Column(name = "VIPIND", description = "vip客户分类 0-普通客户1-集团内公车 2-集团内员工车 3-vip团体客户 4-其他vip客户")
	private String vipind;
	/** 对应字段：MACHINECODE,备注：身份证采集器编码 */
	@Column(name = "MACHINECODE", description = "身份证采集器编码")
	private String machinecode;
	/** 对应字段：IDCOLLECTIND,备注：身份证采集标识 */
	@Column(name = "IDCOLLECTIND", description = "身份证采集标识")
	private String idcollectind;
	/** 对应字段：COUNTYCODE,备注：联系地址区（县级） */
	@Column(name = "COUNTYCODE", description = "联系地址区（县级）")
	private String countycode;
	/** 对应字段：EMAIL,备注：邮箱地址 */
	@Column(name = "EMAIL", description = "邮箱地址")
	private String email;
	/** 对应字段：INDUSTRYMAINCODE,备注：行业主代码 */
	@Column(name = "INDUSTRYMAINCODE", description = "行业主代码")
	private String industrymaincode;
	/** 对应字段：INDUSTRYKINDCODE,备注：行业类别代码 */
	@Column(name = "INDUSTRYKINDCODE", description = "行业类别代码")
	private String industrykindcode;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@Column(name = "INPUTDATE", description = "入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@Column(name = "UPDATESYSDATE", description = "修改日期")
	private Date updatesysdate;
}
