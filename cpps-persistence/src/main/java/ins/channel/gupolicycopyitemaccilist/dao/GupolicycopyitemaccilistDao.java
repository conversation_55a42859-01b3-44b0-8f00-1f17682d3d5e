package ins.channel.gupolicycopyitemaccilist.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicycopyitemaccilist.po.Gupolicycopyitemaccilist;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYCOPYITEMACCILIST对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicycopyitemaccilistDao extends MybatisBaseDao<Gupolicycopyitemaccilist, String> {

    void deleteByPolicyNo(String policyNo);

    int batchInsert(List<Gupolicycopyitemaccilist> gupolicyitemaccilistList);
}
