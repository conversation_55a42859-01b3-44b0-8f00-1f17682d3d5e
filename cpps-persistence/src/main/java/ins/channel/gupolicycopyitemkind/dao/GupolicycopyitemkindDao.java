package ins.channel.gupolicycopyitemkind.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicycopyitemkind.po.Gupolicycopyitemkind;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYCOPYITEMKIND对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicycopyitemkindDao extends MybatisBaseDao<Gupolicycopyitemkind, String> {

    List<Gupolicycopyitemkind> selectByCondition(Gupolicycopyitemkind copyitemkindCondition);

    //批量插入
    int batchInsert(List<Gupolicycopyitemkind> gupolicycopyitemkindList);

    int deleteByEndorNo(String endorNo);

    void deleteByPolicyNo(String policyNo);
}
