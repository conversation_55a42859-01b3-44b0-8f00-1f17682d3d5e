package ins.channel.gupolicycopyitemkind.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GupolicycopyitemkindVo对象.对应实体描述：保单计划险别轨迹表
 *
 */
@Data
@ApiModel("GupolicycopyitemkindVo对象")
public class GupolicycopyitemkindVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@ApiModelProperty("主键")
	private String id;
	/** 对应字段：POLICYNO,备注：保单号 */
	@ApiModelProperty("保单号")
	private String policyNo;
	/** 对应字段：ENDORNO,备注：批改申请号/申报编号 */
	@ApiModelProperty("批改申请号/申报编号")
	private String endorNo;
	/** 对应字段：SUBPOLICYNO,备注：分单号 */
	@ApiModelProperty("分单号")
	private String subpolicyno;
	/** 对应字段：PLANCODE,备注：计划代码 */
	@ApiModelProperty("计划代码")
	private String plancode;
	/** 对应字段：RISKCODE,备注：险种代码 */
	@ApiModelProperty("险种代码")
	private String riskCode;
	/** 对应字段：ITEMKINDNO,备注：险别序号 */
	@ApiModelProperty("险别序号")
	private BigDecimal itemKindNo;
	/** 对应字段：ITEMNO,备注：标的序号； */
	@ApiModelProperty("标的序号；")
	private BigDecimal itemNo;
	/** 对应字段：ITEMCODE,备注：标的代码； */
	@ApiModelProperty("标的代码；")
	private String itemCode;
	/** 对应字段：ITEMDETAILNO,备注：如果是标的物的险别，记录险别对应的标的物序号，否则为0 ； */
	@ApiModelProperty("如果是标的物的险别，记录险别对应的标的物序号，否则为0 ；")
	private BigDecimal itemdetailno;
	/** 对应字段：ITEMDETAILCODE,备注：如果是标的物的险别，记录险别对应的标的物代码；GgRiskItemDetail.ItemDetailCode */
	@ApiModelProperty("如果是标的物的险别，记录险别对应的标的物代码；GgRiskItemDetail.ItemDetailCode")
	private String itemdetailcode;
	/** 对应字段：ITEMDETAILLIST,备注：如果是标的物的险别，记录险别对应的标的物的明细信息；GGItemDetail.ItemDetailCName */
	@ApiModelProperty("如果是标的物的险别，记录险别对应的标的物的明细信息；GGItemDetail.ItemDetailCName")
	private String itemdetaillist;
	/** 对应字段：PLANID,备注：计划编码 */
	@ApiModelProperty("计划编码")
	private String planid;
	/** 对应字段：KINDIND,备注：1：主险标志 2：附加险标志 */
	@ApiModelProperty("1：主险标志 2：附加险标志")
	private String kindind;
	/** 对应字段：KINDCODE,备注：险别代码 */
	@ApiModelProperty("险别代码")
	private String kindCode;
	/** 对应字段：KINDNAME,备注：险别名称 */
	@ApiModelProperty("险别名称")
	private String kindName;
	/** 对应字段：MODECODE,备注：投保方式代码 */
	@ApiModelProperty("投保方式代码")
	private String modeCode;
	/** 对应字段：MODENAME,备注：投保方式名称 */
	@ApiModelProperty("投保方式名称")
	private String modeName;
	/** 对应字段：STARTDATE,备注：险别的起保日期 */
	@ApiModelProperty("险别的起保日期")
	private Date startDate;
	/** 对应字段：ENDDATE,备注：险别的终保日期 */
	@ApiModelProperty("险别的终保日期")
	private Date endDate;
	/** 对应字段：CALCULATEIND,备注：是否计算保额标志,代码对应数据表GgKind.CalculateInd； */
	@ApiModelProperty("是否计算保额标志,代码对应数据表GgKind.CalculateInd；")
	private String calculateind;
	/** 对应字段：CURRENCY,备注：原币币别 */
	@ApiModelProperty("原币币别")
	private String currency;
	/** 对应字段：UNIT,备注：单位 */
	@ApiModelProperty("单位")
	private String unit;
	/** 对应字段：SUMVALUE,备注：保险价值 */
	@ApiModelProperty("保险价值")
	private BigDecimal sumValue;
	/** 对应字段：SUMINSURED,备注：险别对应的保额 */
	@ApiModelProperty("险别对应的保额")
	private BigDecimal suminsured;
	/** 对应字段：CHANGEINSURED,备注：险别对应的保额变化量 */
	@ApiModelProperty("险别对应的保额变化量")
	private BigDecimal changeinsured;
	/** 对应字段：RATEPERIOD,备注：适应费率期数 */
	@ApiModelProperty("适应费率期数")
	private BigDecimal ratePeriod;
	/** 对应字段：RATE,备注：费率 */
	@ApiModelProperty("费率")
	private BigDecimal rate;
	/** 对应字段：SHORTRATEFLAG,备注：短期费率标志 1-日比例 2-月比例 3-短期费率表 4-不计 5-趸交保费系数表  代码对应数据表 GgCode.CodeType=ShortRateFlag */
	@ApiModelProperty("短期费率标志 1-日比例 2-月比例 3-短期费率表 4-不计 5-趸交保费系数表  代码对应数据表 GgCode.CodeType=ShortRateFlag")
	private String shortrateFlag;
	/** 对应字段：SHORTRATE,备注：短期费率 */
	@ApiModelProperty("短期费率")
	private BigDecimal shortRate;
	/** 对应字段：SHORTRATEDENOMINATOR,备注：短期费率分母 */
	@ApiModelProperty("短期费率分母")
	private BigDecimal shortratedenominator;
	/** 对应字段：LOADING,备注：加减费 */
	@ApiModelProperty("加减费")
	private BigDecimal loading;
	/** 对应字段：GROSSPREMIUM,备注：险别的毛保费 */
	@ApiModelProperty("险别的毛保费")
	private BigDecimal grosspremium;
	/** 对应字段：CHANGEGROSSPREMIUM,备注：险别的毛保费变化量 */
	@ApiModelProperty("险别的毛保费变化量")
	private BigDecimal changegrosspremium;
	/** 对应字段：NOTAXPREMIUM,备注：营改增-不含税保费 */
	@ApiModelProperty("营改增-不含税保费")
	private BigDecimal notaxpremium;
	/** 对应字段：TAXAMOUNT,备注：营改增-税额 */
	@ApiModelProperty("营改增-税额")
	private BigDecimal taxamount;
	/** 对应字段：CHANGENOTAXPREMIUM,备注：营改增-不含税保费变化量 */
	@ApiModelProperty("营改增-不含税保费变化量")
	private BigDecimal changenotaxpremium;
	/** 对应字段：CHANGETAXAMOUNT,备注：营改增-税额变化量 */
	@ApiModelProperty("营改增-税额变化量")
	private BigDecimal changetaxamount;
	/** 对应字段：NETPREMIUM,备注：险别净保费 */
	@ApiModelProperty("险别净保费")
	private BigDecimal netPremium;
	/** 对应字段：CHANGENETPREMIUM,备注：险别净保费变化量 */
	@ApiModelProperty("险别净保费变化量")
	private BigDecimal changenetpremium;
	/** 对应字段：DEDUCTIBLERATE,备注：免赔费率 */
	@ApiModelProperty("免赔费率")
	private BigDecimal deductibleRate;
	/** 对应字段：DEDUCTIBLE,备注：免赔额 */
	@ApiModelProperty("免赔额")
	private BigDecimal deductible;
	/** 对应字段：YEARPREMIUM,备注：年保费 */
	@ApiModelProperty("年保费")
	private BigDecimal yearpremium;
	/** 对应字段：SURRENDERIND,备注：险别是否有效 0-有效 */
	@ApiModelProperty("险别是否有效 0-有效")
	private String surrenderind;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 U I D */
	@ApiModelProperty("标志字段 U I D")
	private String flag;
	/** 对应字段：LIABCODE,备注：意健险作“责任代码”使用 */
	@ApiModelProperty("意健险作“责任代码”使用")
	private String liabCode;
	/** 对应字段：UNITPREMIUM,备注：意健险作“每人保保费”使用 */
	@ApiModelProperty("意健险作“每人保保费”使用")
	private BigDecimal unitPremium;
	/** 对应字段：DISCOUNT,备注：意健险作“折扣率”使用 */
	@ApiModelProperty("意健险作“折扣率”使用")
	private BigDecimal discount;
	/** 对应字段：QUANTITY,备注：意健险作“人数”使用 */
	@ApiModelProperty("意健险作“人数”使用")
	private BigDecimal quantity;
	/** 对应字段：UWCOUNT,备注：意健险作“投保份数”使用 */
	@ApiModelProperty("意健险作“投保份数”使用")
	private BigDecimal uwcount;
	/** 对应字段：UWPREMIUM,备注：承保保费 */
	@ApiModelProperty("承保保费")
	private BigDecimal uwpremium;
	/** 对应字段：CHANGEUWPREMIUM,备注：承保保费变化量 */
	@ApiModelProperty("承保保费变化量")
	private BigDecimal changeuwpremium;
	/** 对应字段：ORIGINUWPREMIUM,备注：改前承保保费 */
	@ApiModelProperty("改前承保保费")
	private BigDecimal originuwpremium;
	/** 对应字段：ORIGINGROSSPREMIUM,备注：改前应收保费 */
	@ApiModelProperty("改前应收保费")
	private BigDecimal origingrosspremium;
	/** 对应字段：COMMISSION,备注：手续费金额 */
	@ApiModelProperty("手续费金额")
	private BigDecimal commission;
	/** 对应字段：CHANGECOMMISSION,备注：手续费金额变化量 */
	@ApiModelProperty("手续费金额变化量")
	private BigDecimal changecommission;
	/** 对应字段：AGENTRATE,备注：手续费比例（含税） */
	@ApiModelProperty("手续费比例（含税）")
	private BigDecimal agentrate;
	/** 对应字段：PUBINSUREDIND,备注：公共保额标志,0:否;1:是 */
	@ApiModelProperty("公共保额标志,0:否;1:是")
	private String pubinsuredind;
	/** 对应字段：SPECIALIND,备注：0-普通险别 1-特殊险别 */
	@ApiModelProperty("0-普通险别 1-特殊险别")
	private String specialind;
	/** 对应字段：MUSTINSUREIND,备注：是否必保标识：1－是 0－否 */
	@ApiModelProperty("是否必保标识：1－是 0－否")
	private String mustinsureind;
	/** 对应字段：OURAMOUNT,备注：我司保额 */
	@ApiModelProperty("我司保额")
	private BigDecimal ouramount;
	/** 对应字段：OURPREMIUM,备注：我司含税保费 */
	@ApiModelProperty("我司含税保费")
	private BigDecimal ourpremium;
	/** 对应字段：OURNOTTAXPREMIUM,备注：我司不含税保费 */
	@ApiModelProperty("我司不含税保费")
	private BigDecimal ournottaxpremium;
	/** 对应字段：OURTAXAMOUNT,备注：我司税额 */
	@ApiModelProperty("我司税额")
	private BigDecimal ourtaxamount;
	/** 对应字段：CHANGEOURAMOUNT,备注：我司保额变化量 */
	@ApiModelProperty("我司保额变化量")
	private BigDecimal changeouramount;
	/** 对应字段：CHANGEOURPREMIUM,备注：我司含税保费变化量 */
	@ApiModelProperty("我司含税保费变化量")
	private BigDecimal changeourpremium;
	/** 对应字段：CHANGEOURNOTTAXPREMIUM,备注：我司不含税保费变化量 */
	@ApiModelProperty("我司不含税保费变化量")
	private BigDecimal changeournottaxpremium;
	/** 对应字段：CHANGEOURTAXAMOUNT,备注：我司税额变化量 */
	@ApiModelProperty("我司税额变化量")
	private BigDecimal changeourtaxamount;
	/** 对应字段：CURRENCYCNY,备注：本位币币别 */
	@ApiModelProperty("本位币币别")
	private String currencycny;
	/** 对应字段：SUMINSUREDCNY,备注：总保额本位币金额 */
	@ApiModelProperty("总保额本位币金额")
	private BigDecimal suminsuredcny;
	/** 对应字段：UWPREMIUMCNY,备注：总签单保费本位币金额 */
	@ApiModelProperty("总签单保费本位币金额")
	private BigDecimal uwpremiumcny;
	/** 对应字段：NOTAXPREMIUMCNY,备注：不含税保费本位币金额 */
	@ApiModelProperty("不含税保费本位币金额")
	private BigDecimal notaxpremiumcny;
	/** 对应字段：TAXAMOUNTCNY,备注：税额本位币金额 */
	@ApiModelProperty("税额本位币金额")
	private BigDecimal taxamountcny;
	/** 对应字段：CHANGEINSUREDCNY,备注：总保额本位币金额变化量 */
	@ApiModelProperty("总保额本位币金额变化量")
	private BigDecimal changeinsuredcny;
	/** 对应字段：CHANGEUWPREMIUMCNY,备注：总签单保费本位币金额变化量 */
	@ApiModelProperty("总签单保费本位币金额变化量")
	private BigDecimal changeuwpremiumcny;
	/** 对应字段：CHANGENOTAXPREMIUMCNY,备注：不含税保费本位币金额变化量 */
	@ApiModelProperty("不含税保费本位币金额变化量")
	private BigDecimal changenotaxpremiumcny;
	/** 对应字段：CHANGETAXAMOUNTCNY,备注：税额本位币金额变化量 */
	@ApiModelProperty("税额本位币金额变化量")
	private BigDecimal changetaxamountcny;
	/** 对应字段：OURAMOUNTCNY,备注：我司保额本位币金额 */
	@ApiModelProperty("我司保额本位币金额")
	private BigDecimal ouramountcny;
	/** 对应字段：OURPREMIUMCNY,备注：我司含税保费本位币金额 */
	@ApiModelProperty("我司含税保费本位币金额")
	private BigDecimal ourpremiumcny;
	/** 对应字段：OURNOTTAXPREMIUMCNY,备注：我司不含税保费本位币金额 */
	@ApiModelProperty("我司不含税保费本位币金额")
	private BigDecimal ournottaxpremiumcny;
	/** 对应字段：OURTAXAMOUNTCNY,备注：我司税额本位币金额 */
	@ApiModelProperty("我司税额本位币金额")
	private BigDecimal ourtaxamountcny;
	/** 对应字段：CHANGEOURAMOUNTCNY,备注：我司保额本位币金额变化量 */
	@ApiModelProperty("我司保额本位币金额变化量")
	private BigDecimal changeouramountcny;
	/** 对应字段：CHANGEOURPREMIUMCNY,备注：我司含税保费本位币金额变化量 */
	@ApiModelProperty("我司含税保费本位币金额变化量")
	private BigDecimal changeourpremiumcny;
	/** 对应字段：CHANGEOURNOTTAXPREMIUMCNY,备注：我司不含税保费本位币金额变化量 */
	@ApiModelProperty("我司不含税保费本位币金额变化量")
	private BigDecimal changeournottaxpremiumcny;
	/** 对应字段：CHANGEOURTAXAMOUNTCNY,备注：我司税额本位币金额变化量 */
	@ApiModelProperty("我司税额本位币金额变化量")
	private BigDecimal changeourtaxamountcny;
	/** 对应字段：CLAIMCOUNTLIMIT,备注：理赔次数限制 */
	@ApiModelProperty("理赔次数限制")
	private String claimcountlimit;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@ApiModelProperty("入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@ApiModelProperty("修改日期")
	private Date updatesysdate;
}
