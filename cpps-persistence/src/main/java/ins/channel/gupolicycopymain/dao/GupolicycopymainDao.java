package ins.channel.gupolicycopymain.dao;

import ins.channel.gupolicycopymain.vo.DeclarationReqVo;
import ins.channel.gupolicycopymain.vo.DeclarationRespVo;
import ins.channel.gupolicycopymain.vo.SelectSettlementVo;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicycopymain.po.Gupolicycopymain;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;


/**
 *
 * 表GUPOLICYCOPYMAIN对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicycopymainDao extends MybatisBaseDao<Gupolicycopymain, String> {

    Page<DeclarationRespVo> pageQueryDeclarationByCondition(PageParam pageParam, DeclarationReqVo vo);

    //申报查询导出查询结果集
    List<DeclarationRespVo> selectByAllDeclarationRespVo(DeclarationReqVo vo);

    List<Gupolicycopymain> selectByCondition(Gupolicycopymain gupolicycopymain);

    //批量插入
    int batchInsert(List<Gupolicycopymain> gupolicycopymainList);

    //根据申报单号删除
    int deleteByEndorNo(String endorNo);

    void insertBatchDeclarationBatchQueryTemp(List<DeclarationReqVo> list);
    //修改
    int  SettlementUpdate(SelectSettlementVo selectSettlementVo);
    //根据申报号查询金额
    SelectSettlementVo selectGupolicycopymain(String endorno);
    //结算页面查询结果
    Page<SelectSettlementVo> shouByAll(PageParam pageParam, SelectSettlementVo SSV);
    //保单号，批改申请号，批单序号
    List<Gupolicycopymain>  ShowEndorseqnoPolicynoEndorno();
    //修改状态为2
    int GupolicycopymainUpd(Gupolicycopymain gupolicycopymain);
    //查询
    List<Gupolicycopymain> selectByStell(SelectSettlementVo SSV);
    //修改信息轨迹表关联机构代码
    int updateCompanyCodeAndCompanyName(Gupolicycopymain gupolicycopymain);

    int updateNullValues(Gupolicycopymain gupolicycopymain);


    void deleteByPolicyNo(String policyNo);
}
