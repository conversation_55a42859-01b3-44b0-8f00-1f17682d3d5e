package ins.channel.gupolicycopymain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 申报信息查询条件载体
 * <AUTHOR>
 * @date 2021年02月07日 17:39:22
 */
@Data
@ApiModel("DeclarationReqVo")
public class DeclarationReqVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;
	
	/** 对应字段：APPLICODE,备注：投保人代码-投保公司code */
	@ApiModelProperty("投保人代码-投保公司code")
	private String appliCode;
	/** 对应字段：APPLINAME,备注：投保人名称 */
	@ApiModelProperty("投保人名称")
	private String appliName;
	/** 对应字段：COMPANYCODE,备注：关联机构代码 */
	@ApiModelProperty("关联机构代码")
	private String companyCode;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;
	/** 对应字段：PERSONCASUALTIESLIMIT,备注：计划名称-责任限额 */
	@ApiModelProperty("计划代码")
	private String personcasualtieslimit;
	/** 对应字段：EMPNAME,备注：被保险人名称 */
	@ApiModelProperty("被保险人名称")
	private String empName;
	/** 对应字段：EMPIDENTIFYTYPE,备注：证件类型 */
	@ApiModelProperty("证件类型")
	private String empIdentifyType;
	/** 对应字段：EMPIDENTIFYNUMBER,备注：证件号码 */
	@ApiModelProperty("证件号码")
	private String empIdentifyNumber;
	/** 对应字段：LISTSEQNO,备注：凭证号码 */
	@ApiModelProperty("凭证号码")
	private String listSeqno;
	/** 对应字段：TARGETFLAG,备注：操作类型 */
	@ApiModelProperty("操作类型")
	private String targetFlag;
	/** 对应字段：ENDORNO,备注：申报单号 */
	@ApiModelProperty("申报单号")
	private String endorNo;
	/** 对应字段：PROJECTMANAGERNAME,备注：归属业务员名称 */
	@ApiModelProperty("归属业务员名称")
	private String projectManagerName;

	/** 对应字段：EFFECTIVEDATE,备注：生效日期查询区间开始 */
	@ApiModelProperty("生效日期查询区间开始")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date effectiveDateStart;
	/** 对应字段：EFFECTIVEDATE,备注：生效日期查询区间结束 */
	@ApiModelProperty("生效日期查询区间结束")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date effectiveDateEnd;
	/** 对应字段：ENDDATE,备注：到期日期查询区间开始 */
	@ApiModelProperty("到期日期查询区间开始")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date endDateStart;
	/** 对应字段：ENDDATE,备注：到期日期查询区间结束 */
	@ApiModelProperty("到期日期查询区间结束")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date endDateEnd;
	/** 对应字段：ACCEPTDATE,备注：申报日期查询区间开始 */
	@ApiModelProperty("申报日期查询区间开始")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date acceptDateStart;
	/** 对应字段：ACCEPTDATE,备注：申报日期查询区间结束 */
	@ApiModelProperty("申报日期查询区间结束")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date acceptDateEnd;
	/** 对应字段：PRODUCTCODE,备注：保险险种 */
	@ApiModelProperty("保险险种")
	private String productcode;
	
	/** 对应字段：ORDERCOLUMN,备注：排序字段 */
	@ApiModelProperty("排序字段")
	private String orderColumn;
	/** 对应字段：ORDERTYPE,备注：asc,desc */
	@ApiModelProperty("排序类型:升序降序")
	private String orderType;

	//当前登录人员关联机构权限集合
	@JsonIgnore
	private Set<String> companyCodes;

	/** 是否为excel导入批量查询1是0否 */
	@JsonIgnore
	private int isExcelBatchQueryFlag;

	/** 批次查询标识UUID */
	@JsonIgnore
	private String uuid;
	
	/** 业务员代码 */
	@JsonIgnore
	private String projectManagerCode;

	@ApiModelProperty("所属部门")
	private String department;
}
