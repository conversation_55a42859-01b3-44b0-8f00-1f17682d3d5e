package ins.channel.gupolicycopymain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 申报信息查询结果集载体
 * <AUTHOR>
 * @date 2021年02月07日 17:39:22
 */
@Data
@ApiModel("DeclarationRespVo")
public class DeclarationRespVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;
	/** 对应字段：LISTSEQNO,备注：凭证号码 */
	@ApiModelProperty("凭证号码")
	private String listSeqno;
	/** 对应字段：EMPNAME,备注：被保险人名称 */
	@ApiModelProperty("被保险人名称")
	private String empName;
	/** 对应字段：EMPIDENTIFYTYPE,备注：证件类型 */
	@ApiModelProperty("证件类型")
	private String empIdentifyType;
	/** 对应字段：EMPIDENTIFYNUMBER,备注：证件号码 */
	@ApiModelProperty("证件号码")
	private String empIdentifyNumber;
	/** 对应字段：PLANNAME,备注：通过方案进行生成传给前端,先前端固定,后端根据每人伤亡责任限额(fieldaf)查询.
	 * 保障项目			计划一	计划二	计划三	计划四	计划五	计划六
	 * 意外身故、残疾	300000	500000	600000	700000	800000	1000000
	 * */
	@ApiModelProperty("计划名称")
	private String personcasualtieslimit;
	/** 对应字段：TARGETFLAG,备注：操作类型 */
	@ApiModelProperty("操作类型")
	private String targetFlag;
	/** 对应字段：ENTRYDATE,备注：生效日期 */
	@ApiModelProperty("入职日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date entryDate;
	/** 对应字段：EFFECTIVEDATE,备注：生效日期 */
	@ApiModelProperty("生效日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date effectiveDate;
	/** 对应字段：ENDDATE,备注：到期日期 */
	@ApiModelProperty("到期日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date endDate;
	/** 对应字段：ACCEPTDATE,备注：申报日期 */
	@ApiModelProperty("申报日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date acceptDate;
	/** 对应字段：MONTHPAY,备注：约定月薪 */
	@ApiModelProperty("约定月薪")
	private BigDecimal monthPay;
	/** 对应字段：EMPPREMIUM,备注：保费 */
	@ApiModelProperty("保费")
	private BigDecimal empPremium;
	/** 对应字段：APPLINAME,备注：投保人名称-投保公司 */
	@ApiModelProperty("投保人名称-投保公司")
	private String appliName;
	/** 对应字段：COMPANYNAME,备注：关联机构名称 */
	@ApiModelProperty("关联机构名称")
	private String companyName;
	/** 对应字段：UNDERWRITEIND,备注：申报状态 */
	@ApiModelProperty("申报状态")
	private String underWriteInd;
	/** 对应字段：ENDORNO,备注：申报单号 */
	@ApiModelProperty("申报单号")
	private String endorNo;
	/** 对应字段：PROJECTMANAGERNAME,备注：归属业务员名称 */
	@ApiModelProperty("归属业务员名称")
	private String projectManagerName;
	/** 对应字段：RESIGNATIONDATE,备注：离职日期 */
	@ApiModelProperty("离职日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date resignationDate;
	/** 对应字段：PROJECT,备注：项目 */
	@ApiModelProperty("项目")
	private String project;
	@ApiModelProperty("操作员")
	private String lastModifyManagerName;
	@ApiModelProperty("应收保费")
	private BigDecimal changeemppremium;
}
