package ins.channel.gupolicycopymain.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import ins.framework.mybatis.annotations.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GupolicycopymainVo对象.对应实体描述：保单主信息轨迹表
 *
 */
@Data
@ApiModel("GupolicycopymainVo对象")
public class GupolicycopymainVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键（创新业务标识3+险种4+年月日8+15位序列） */
	@ApiModelProperty("主键（创新业务标识3+险种4+年月日8+15位序列）")
	private String id;
	/** 对应字段：PROPOSALNO,备注：投保单号码 */
	@ApiModelProperty("投保单号码")
	private String proposalNo;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;
	/** 对应字段：ENDORSERIALNO,备注：批单编号 */
	@ApiModelProperty("批单编号")
	private BigDecimal endorserialno;
	/** 对应字段：ENDORSEQNO,备注：批单序号（核心） */
	@ApiModelProperty("批单序号（核心）")
	private String endorseqno;
	/** 对应字段：ENDORNO,备注：批改申请号/申报编号 */
	@ApiModelProperty("批改申请号/申报编号")
	private String endorNo;
	/** 对应字段：LANGUAGE,备注：保单语种 */
	@ApiModelProperty("保单语种")
	private String language;
	/** 对应字段：PRODUCTCODE,备注：产品代码 */
	@ApiModelProperty("产品代码")
	private String productcode;
	/** 对应字段：GROUPIND,备注：团体标志 1-个单 2-团单 */
	@ApiModelProperty("团体标志 1-个单 2-团单")
	private String groupind;
	/** 对应字段：INSURANCECOMPANYCODE,备注：保险公司归属机构 */
	@ApiModelProperty("保险公司归属机构")
	private String insurancecompanycode;
	/** 对应字段：SURVEYIND,备注：创新业务标识 */
	@ApiModelProperty("创新业务标识")
	private String surveyind;
	/** 对应字段：FLOWID,备注：产品方案代码 */
	@ApiModelProperty("产品方案代码")
	private String flowid;
	/** 对应字段：COMPANYCODE,备注：关联机构代码 */
	@ApiModelProperty("关联机构代码")
	private String companycode;
	/** 对应字段：COMPANYNAME,备注：关联机构名称 */
	@ApiModelProperty("关联机构名称")
	private String companyname;
	/** 对应字段：PROJECTMANAGERCODE,备注：PM代码 */
	@ApiModelProperty("PM代码")
	private String projectmanagercode;
	/** 对应字段：PROJECTMANAGERNAME,备注：PM名称 */
	@ApiModelProperty("PM名称")
	private String projectmanagername;
	/** 对应字段：STARTDATE,备注：起保日期 */
	@ApiModelProperty("起保日期")
	private Date startDate;
	/** 对应字段：ENDDATE,备注：终保日期 */
	@ApiModelProperty("终保日期")
	private Date endDate;
	/** 对应字段：VALIDDATE,备注：生效日期 */
	@ApiModelProperty("生效日期")
	private Date validDate;
	/** 对应字段：APPLICODE,备注：投保人代码 */
	@ApiModelProperty("投保人代码")
	private String appliCode;
	/** 对应字段：APPLINAME,备注：投保人名称 */
	@ApiModelProperty("投保人名称")
	private String appliName;
	/** 对应字段：INSUREDCODE,备注：被保险人代码 */
	@ApiModelProperty("被保险人代码")
	private String insuredCode;
	/** 对应字段：INSUREDNAME,备注：被保险人名称 */
	@ApiModelProperty("被保险人名称")
	private String insuredName;
	/** 对应字段：CURRENCY,备注：保单币别 */
	@ApiModelProperty("保单币别")
	private String currency;
	/** 对应字段：YEARPREMIUM,备注：年保费 */
	@ApiModelProperty("年保费")
	private BigDecimal yearpremium;
	/** 对应字段：SUMINSURED,备注：总保额 */
	@ApiModelProperty("总保额")
	private BigDecimal suminsured;
	/** 对应字段：SUMGROSSPREMIUM,备注：总毛保费 */
	@ApiModelProperty("总毛保费")
	private BigDecimal sumgrosspremium;
	/** 对应字段：SUMNETPREMIUM,备注：总净保费 */
	@ApiModelProperty("总净保费")
	private BigDecimal sumnetpremium;
	/** 对应字段：SUMUWPREMIUM,备注：总承保保费 */
	@ApiModelProperty("总承保保费")
	private BigDecimal sumuwpremium;
	/** 对应字段：NOTAXPREMIUM,备注：总不含税保费 */
	@ApiModelProperty("总不含税保费")
	private BigDecimal notaxpremium;
	/** 对应字段：TAXAMOUNT,备注：总税额 */
	@ApiModelProperty("总税额")
	private BigDecimal taxamount;
	/** 对应字段：CHANGEINSURED,备注：总保额变化量 */
	@ApiModelProperty("总保额变化量")
	private BigDecimal changeinsured;
	/** 对应字段：CHANGEGROSSPREMIUM,备注：总毛保费变化量 */
	@ApiModelProperty("总毛保费变化量")
	private BigDecimal changegrosspremium;
	/** 对应字段：CHANGENETPREMIUM,备注：总净保费变化量 */
	@ApiModelProperty("总净保费变化量")
	private BigDecimal changenetpremium;
	/** 对应字段：CHANGEUWPREMIUM,备注：总承保保费变化量 */
	@ApiModelProperty("总承保保费变化量")
	private BigDecimal changeuwpremium;
	/** 对应字段：CHANGENOTAXPREMIUM,备注：总不含税保费变化量 */
	@ApiModelProperty("总不含税保费变化量")
	private BigDecimal changenotaxpremium;
	/** 对应字段：CHANGETAXAMOUNT,备注：总税额变化量 */
	@ApiModelProperty("总税额变化量")
	private BigDecimal changetaxamount;
	/** 对应字段：CURRENCYCNY,备注：币种 */
	@ApiModelProperty("币种")
	private String currencycny;
	/** 对应字段：SUMINSUREDCNY,备注：总保额 */
	@ApiModelProperty("总保额")
	private BigDecimal suminsuredcny;
	/** 对应字段：SUMGROSSPREMIUMCNY,备注：总毛保费 */
	@ApiModelProperty("总毛保费")
	private BigDecimal sumgrosspremiumcny;
	/** 对应字段：SUMUWPREMIUMCNY,备注：总承保保费 */
	@ApiModelProperty("总承保保费")
	private BigDecimal sumuwpremiumcny;
	/** 对应字段：NOTAXPREMIUMCNY,备注：不含税保费 */
	@ApiModelProperty("不含税保费")
	private BigDecimal notaxpremiumcny;
	/** 对应字段：TAXAMOUNTCNY,备注：总税额 */
	@ApiModelProperty("总税额")
	private BigDecimal taxamountcny;
	/** 对应字段：CHANGEINSUREDCNY,备注：总保额变化量 */
	@ApiModelProperty("总保额变化量")
	private BigDecimal changeinsuredcny;
	/** 对应字段：CHANGEGROSSPREMIUMCNY,备注：总毛保费变化量 */
	@ApiModelProperty("总毛保费变化量")
	private BigDecimal changegrosspremiumcny;
	/** 对应字段：CHANGEUWPREMIUMCNY,备注：总承保保费变化量 */
	@ApiModelProperty("总承保保费变化量")
	private BigDecimal changeuwpremiumcny;
	/** 对应字段：CHANGENOTAXPREMIUMCNY,备注：不含税保费变化量 */
	@ApiModelProperty("不含税保费变化量")
	private BigDecimal changenotaxpremiumcny;
	/** 对应字段：CHANGETAXAMOUNTCNY,备注：总税额变化量 */
	@ApiModelProperty("总税额变化量")
	private BigDecimal changetaxamountcny;
	/** 对应字段：UWYEAR,备注：承保年度 */
	@ApiModelProperty("承保年度")
	private String uwYear;
	/** 对应字段：ACCEPTDATE,备注：承保确认时间/批单申报日期 */
	@ApiModelProperty("承保确认时间/批单申报日期")
	private Date acceptdate;
	/** 对应字段：UNDERWRITEIND,备注：核保标志 */
	@ApiModelProperty("核保标志")
	private String underwriteind;
	/** 对应字段：UNDERWRITEENDDATE,备注：核保完成日期 */
	@ApiModelProperty("核保完成日期")
	private Date underWriteEndDate;
	/** 对应字段：SURRENDERIND,备注：退保标志 */
	@ApiModelProperty("退保标志")
	private String surrenderind;
	/** 对应字段：CANCELIND,备注：注销标志 */
	@ApiModelProperty("注销标志")
	private String cancelind;
	/** 对应字段：ENDIND,备注：保险合同终止标志 */
	@ApiModelProperty("保险合同终止标志")
	private String endind;
	/** 对应字段：CODIND,备注：见费出单COD标志 */
	@ApiModelProperty("见费出单COD标志")
	private String codind;
	/** 对应字段：CALCULATETYPE,备注：保费计算方式 */
	@ApiModelProperty("保费计算方式")
	private String calculatetype;
	/** 对应字段：COINSIND,备注：联共保标识 */
	@ApiModelProperty("联共保标识")
	private String coinsind;
	/** 对应字段：AGENTRATE,备注：手续费比例（含税） */
	@ApiModelProperty("手续费比例（含税）")
	private BigDecimal agentrate;
	/** 对应字段：NOTAXAGENTRATE,备注：手续费比例（不含税） */
	@ApiModelProperty("手续费比例（不含税）")
	private BigDecimal notaxagentrate;
	/** 对应字段：COMMISSION,备注：手续费金额 */
	@ApiModelProperty("手续费金额")
	private BigDecimal commission;
	/** 对应字段：CHANGECOMMISSION,备注：手续费金额变化量 */
	@ApiModelProperty("手续费金额变化量")
	private BigDecimal changecommission;
	/** 对应字段：FSH,备注：FSH%值 */
	@ApiModelProperty("FSH%值")
	private BigDecimal fsh;
	/** 对应字段：XSF,备注：XSF%调整系数值 */
	@ApiModelProperty("XSF%调整系数值")
	private BigDecimal xsf;
	/** 对应字段：XSFIND,备注：XSF等级  */
	@ApiModelProperty("XSF等级 ")
	private String xsfind;
	/** 对应字段：INSTALLMENTNO,备注：约定分期交费次数 */
	@ApiModelProperty("约定分期交费次数")
	private BigDecimal installmentno;
	/** 对应字段：ENDORSETIMES,备注：批改次数 */
	@ApiModelProperty("批改次数")
	private BigDecimal endorseTimes;
	/** 对应字段：RENEWEDTIME,备注：续保次数 */
	@ApiModelProperty("续保次数")
	private BigDecimal renewedtime;
	/** 对应字段：REGISTTIMES,备注：报案次数 */
	@ApiModelProperty("报案次数")
	private BigDecimal registTimes;
	/** 对应字段：CLAIMSTIMES,备注：理赔次数 */
	@ApiModelProperty("理赔次数")
	private BigDecimal claimstimes;
	/** 对应字段：PRINTTIMES,备注：打印次数 */
	@ApiModelProperty("打印次数")
	private BigDecimal printtimes;
	/** 对应字段：ISSENDSMS,备注：是否发送短信通知 */
	@ApiModelProperty("是否发送短信通知")
	private String issendsms;
	/** 对应字段：ISSENDEMAIL,备注：是否发送邮件 */
	@ApiModelProperty("是否发送邮件")
	private String issendemail;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：VALIDIND,备注：有效标志 0-无效 1-有效 */
	@ApiModelProperty("有效标志 0-无效 1-有效")
	private String validind;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@ApiModelProperty("入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@ApiModelProperty("修改日期")
	private Date updatesysdate;
	/** 对应字段：SETTLESTATUS,备注：结算状态 0-未结算 1-已生成结算单 2-已结算 */
	@ApiModelProperty("结算状态 0-未结算 1-已生成结算单 2-已结算")
	private String settleStatus;
	/** 对应字段：SETTLEFEE,备注：已结算金额 */
	@ApiModelProperty("已结算金额")
	private BigDecimal settlefee;
	/** 对应字段：SETTLENO,备注：结算单号 */
	@ApiModelProperty("结算单号")
	private String settleNo;
}
