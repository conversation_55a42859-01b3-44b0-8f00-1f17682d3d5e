package ins.channel.gupolicycopymain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

/**
 * 结算信息页面载体
 */
@Data
@ApiModel("SelectSettlementVo")
public class SelectSettlementVo extends PageBaseDto implements Serializable{
        private static final long serialVersionUID = 1L;

        /** 对应字段：POLICYNO,备注：保单号 */
        @ApiModelProperty("保单号")
        private String policyno;
        /** 对应字段：ENDORNO,备注：批改申请号/申报编号*/
        @ApiModelProperty("申报单号")
        private String endorno;
        @ApiModelProperty("结算单号")
        private String settleno;
        @ApiModelProperty("被保险人")
        private String empname;
        @ApiModelProperty("计划名称-外表")
        private String personcasualtieslimit;
        @ApiModelProperty("生效日期")
        @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date validdate;
        @ApiModelProperty("到期日期")
        @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date enddate;
        @ApiModelProperty("申报日期")
        @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date acceptdate;
        @ApiModelProperty("申报金额")
        private BigDecimal changegrosspremium;
        @ApiModelProperty("申报雇员数-外表")
        private String employeesthischange;
        @ApiModelProperty("申报状态")
        private String underwriteind;
        @ApiModelProperty("结算状态")
        private String settlestatus;
        @ApiModelProperty("已结算金额")
        private BigDecimal settlefee;
        @ApiModelProperty("关联机构名字")
        private String companyname;
        @ApiModelProperty("关联机构代码")
        private String companyCode;


        @ApiModelProperty("业务员")
        private String projectmanagername;
        @ApiModelProperty("保险编码")
        private String productcode;
        @ApiModelProperty("生效日期结束")
        @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date validdateEnd;
        @ApiModelProperty("到期日期结束")
        @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date startdate;
        @ApiModelProperty("申报日期结束")
        @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date acceptdateEnd;



        //当前登录人员关联机构权限集合
        @JsonIgnore
        private Set<String> companyCodes;

        private BigDecimal sumgrosspremium;

        @ApiModelProperty("所属部门")
        private String department;

}
