package ins.channel.gupolicycopymainbasic.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicycopymainbasic.po.Gupolicycopymainbasic;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYCOPYMAINBASIC对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicycopymainbasicDao extends MybatisBaseDao<Gupolicycopymainbasic, String> {

    //批量插入
    int batchInsert(List<Gupolicycopymainbasic> gupolicycopymainbasicList);

    void deleteByPolicyNo(String policyNo);
}
