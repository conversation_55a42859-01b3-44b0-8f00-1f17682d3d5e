package ins.channel.gupolicyemployerslist.dao;

import ins.channel.gupolicyemployerslist.vo.EmployerslistQueryReqVo;
import ins.channel.gupolicyemployerslist.vo.EmployerslistQueryRespVo;
import ins.channel.gupolicymain.po.Gupolicymain;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicyemployerslist.po.Gupolicyemployerslist;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYEMPLOYERSLIST对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicyemployerslistDao extends MybatisBaseDao<Gupolicyemployerslist, String> {

    int batchUpdate(List<Gupolicyemployerslist> list);
    //批量更新
    int batchUpdateSelective(List<Gupolicyemployerslist> list);

    int batchInsert(List<Gupolicyemployerslist> list);

    String selectAllOccupationNamesByPolicyNo(String policyNo);

    List<Gupolicyemployerslist> selectByCondition(Gupolicyemployerslist employerslistCondition);

    List<Gupolicyemployerslist> searchByPolicyNo(Gupolicyemployerslist employerslistCondition);

    Page<EmployerslistQueryRespVo> queryPolicyEmployersListByPage(PageParam pageParam, EmployerslistQueryReqVo vo);

    void deleteByPolicyNo(String policyNo);
}
