package ins.channel.gupolicyemployerslist.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GUPOLICYEMPLOYERSLIST的PO对象<br/>
 * 对应表名：GUPOLICYEMPLOYERSLIST,备注：最新保单雇员清单表
 *
 */
@Data
@Table(name = "GUPOLICYEMPLOYERSLIST")
public class Gupolicyemployerslist implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@Column(name = "ID", description = "主键")
	private String id;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@Column(name = "POLICYNO", description = "保单号码")
	private String policyNo;
	/** 对应字段：SUBPOLICYNO,备注：子保单号 */
	@Column(name = "SUBPOLICYNO", description = "子保单号")
	private String subpolicyno;
	/** 对应字段：PLANID,备注：计划编码 fieldaa */
	@Column(name = "PLANID", description = "计划编码 fieldaa")
	private String planid;
	/** 对应字段：ITEMNO,备注：标的序号 */
	@Column(name = "ITEMNO", description = "标的序号")
	private BigDecimal itemNo;
	/** 对应字段：ITEMDETAILNO,备注：明细序号 */
	@Column(name = "ITEMDETAILNO", description = "明细序号")
	private String itemdetailno;
	/** 对应字段：DYNAMICTARGETTYPE,备注：动态标的类型 见ggcode类型dynamiclisttype 默认存5 */
	@Column(name = "DYNAMICTARGETTYPE", description = "动态标的类型 见ggcode类型dynamiclisttype 默认存5")
	private String dynamictargettype;
	/** 对应字段：LISTSEQNO,备注：标的物方案号 */
	@Column(name = "LISTSEQNO", description = "标的物方案号")
	private BigDecimal listseqno;
	/** 对应字段：TARGETFLAG,备注：标的标识 U-更新 I-插入 D-删除 */
	@Column(name = "TARGETFLAG", description = "标的标识 U-更新 I-插入 D-删除")
	private String targetflag;
	/** 对应字段：EMPNAME,备注：雇员姓名 */
	@Column(name = "EMPNAME", description = "雇员姓名")
	private String empname;
	/** 对应字段：EMPSEX,备注：雇员性别 */
	@Column(name = "EMPSEX", description = "雇员性别")
	private String empsex;
	/** 对应字段：EMPBIRTHDAY,备注：雇员生日 */
	@Column(name = "EMPBIRTHDAY", description = "雇员生日")
	private String empbirthday;
	/** 对应字段：EMPIDENTIFYTYPE,备注：雇员证件类型 */
	@Column(name = "EMPIDENTIFYTYPE", description = "雇员证件类型")
	private String empidentifytype;
	/** 对应字段：EMPIDENTIFYNUMBER,备注：雇员证件号 */
	@Column(name = "EMPIDENTIFYNUMBER", description = "雇员证件号")
	private String empidentifynumber;
	/** 对应字段：OCCUPATIONCODE,备注：职业代码 */
	@Column(name = "OCCUPATIONCODE", description = "职业代码")
	private String occupationCode;
	/** 对应字段：OCCUPATIONNAME,备注：职业名称 */
	@Column(name = "OCCUPATIONNAME", description = "职业名称")
	private String occupationname;
	/** 对应字段：OCCUPATIONLEVEL,备注：职业等级 */
	@Column(name = "OCCUPATIONLEVEL", description = "职业等级")
	private String occupationlevel;
	/** 对应字段：ENTRYDATE,备注：入职日期 */
	@Column(name = "ENTRYDATE", description = "入职日期")
	private Date entrydate;
	/** 对应字段：RESIGNATIONDATE,备注：离职日期 */
	@Column(name = "RESIGNATIONDATE", description = "离职日期")
	private Date resignationdate;
	/** 对应字段：EFFECTIVEDATE,备注：生效日期 */
	@Column(name = "EFFECTIVEDATE", description = "生效日期")
	private Date effectivedate;
	/** 对应字段：ENDDATE,备注：终保日期 */
	@Column(name = "ENDDATE", description = "终保日期")
	private Date endDate;
	/** 对应字段：CURRENCY,备注：保单币别 */
	@Column(name = "CURRENCY", description = "保单币别")
	private String currency;
	/** 对应字段：MONTHPAY,备注：约定月薪 */
	@Column(name = "MONTHPAY", description = "约定月薪")
	private BigDecimal monthPay;
	/** 对应字段：EMPINSURED,备注：每人保额 */
	@Column(name = "EMPINSURED", description = "每人保额")
	private BigDecimal empinsured;
	/** 对应字段：EMPPREMIUM,备注：每人毛保费 */
	@Column(name = "EMPPREMIUM", description = "每人毛保费")
	private BigDecimal emppremium;
	/** 对应字段：PROVINCECODE,备注：省代码 */
	@Column(name = "PROVINCECODE", description = "省代码")
	private String provincecode;
	/** 对应字段：CITYCODE,备注：市代码 */
	@Column(name = "CITYCODE", description = "市代码")
	private String citycode;
	/** 对应字段：COUNTYCODE,备注：县代码 */
	@Column(name = "COUNTYCODE", description = "县代码")
	private String countycode;
	/** 对应字段：MONEYLAUNDERINGIND,备注：洗钱风险 1-高，2-中，3-低； ggcode.codecode where codetype=“moneylaunderingind” */
	@Column(name = "MONEYLAUNDERINGIND", description = "洗钱风险 1-高，2-中，3-低； ggcode.codecode where codetype=“moneylaunderingind”")
	private String moneylaunderingind;
	/** 对应字段：LISTBELONGIND,备注：默认0 */
	@Column(name = "LISTBELONGIND", description = "默认0")
	private String listbelongind;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@Column(name = "INPUTDATE", description = "入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@Column(name = "UPDATESYSDATE", description = "修改日期")
	private Date updatesysdate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@Column(name = "PROJECT", description = "项目")
	private String project;
	/** 对应字段：HOMEADDRESS,备注：修改日期 */
	@Column(name = "HOMEADDRESS", description = "详细地址")
	private String homeaddress;
	/** 对应字段：HOMETEL,备注：修改日期 */
	@Column(name = "HOMETEL", description = "电话")
	private String hometel;
	/** 对应字段：DEPARTMENT,备注：所属部门 */
	@Column(name = "DEPARTMENT", description = "所属部门")
	private String department;
}
