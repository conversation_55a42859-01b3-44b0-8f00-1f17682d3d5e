package ins.channel.gupolicyemployerslist.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import ins.framework.mybatis.annotations.Column;
import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 *EmployerslistQueryReqVo 对象.对应实体描述：最新保单雇员清单表分页模糊查询入参Vo对象
 *
 */
@Data
@ApiModel("EmployerslistQueryReqVo 对象")
public class EmployerslistQueryReqVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;

	/** 对应字段：EMPNAME,备注：雇员姓名 */
	@ApiModelProperty("雇员姓名")
	private String empname;
	/** 对应字段：EMPIDENTIFYTYPE,备注：雇员证件类型 */
	@ApiModelProperty("雇员证件类型")
	private String empidentifytype;
	/** 对应字段：EMPIDENTIFYNUMBER,备注：雇员证件号 */
	@ApiModelProperty("雇员证件号")
	private String empidentifynumber;
	/** 对应字段：ENTRYDATE,备注：入职日期 */
	@ApiModelProperty("入职日期起")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date entrydatestart;
	/** 对应字段：ENTRYDATE,备注：入职日期 */
	@ApiModelProperty("入职日期止")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date entrydateend;
	/** 对应字段：EFFECTIVEDATE,备注：生效日期 */
	@ApiModelProperty("生效日期起")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date effectivedatestart;
	/** 对应字段：EFFECTIVEDATE,备注：生效日期 */
	@ApiModelProperty("生效日期止")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date effectivedateend;
	/** 对应字段：ENDDATE,备注：终保日期 */
	@ApiModelProperty("终保日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date endDatestart;
	/** 对应字段：ENDDATE,备注：终保日期 */
	@ApiModelProperty("终保日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date endDateend;
	/** 对应字段：ACCEPTDATE,备注：承保确认时间/批单申报日期 */
	@ApiModelProperty("申报日期起")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date acceptdatestart;
	/** 对应字段：ACCEPTDATE,备注：承保确认时间/批单申报日期 */
/*	@ApiModelProperty("申报日期止")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date acceptdateend;*/
	/** 对应字段：DEPARTMENT,备注：所属部门 */
	@ApiModelProperty("所属部门")
	private String department;
	@ApiModelProperty("保险计划")
	private String itemNo;

}
