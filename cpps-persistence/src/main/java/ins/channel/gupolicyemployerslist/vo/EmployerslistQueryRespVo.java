package ins.channel.gupolicyemployerslist.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import ins.framework.mybatis.annotations.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 *EmployerslistQueryReqVo 对象.对应实体描述：最新保单雇员清单表分页模糊查询入参Vo对象
 *
 */
@Data
@ApiModel("EmployerslistQueryReqVo 对象")
public class EmployerslistQueryRespVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;
	@ApiModelProperty("计划编码")
	private String planName;
	/** 对应字段：PERSONCASUALTIESLIMIT,备注：每人伤亡责任限额 */
	@ApiModelProperty("每人伤亡责任限额")
	private BigDecimal personcasualtieslimit;
	/** 对应字段：ITEMNO,备注：人员编号 */
	@ApiModelProperty("人员编号")
	private BigDecimal listseqno;
	/** 对应字段：EMPNAME,备注：雇员姓名 */
	@ApiModelProperty("雇员姓名")
	private String empname;
	@ApiModelProperty("雇员性别")
	private String empsex;
	/** 对应字段：EMPBIRTHDAY,备注：雇员生日 */
	@ApiModelProperty("雇员生日")
	private String empbirthday;
	/** 对应字段：EMPIDENTIFYTYPE,备注：雇员证件类型 */
	@ApiModelProperty("雇员证件类型")
	private String empidentifytype;
	/** 对应字段：EMPIDENTIFYNUMBER,备注：雇员证件号 */
	@ApiModelProperty("雇员证件号")
	private String empidentifynumber;
	/** 对应字段：OCCUPATIONCODE,备注：职业代码 */
	@ApiModelProperty("职业代码")
	private String occupationCode;
	/** 对应字段：OCCUPATIONNAME,备注：职业名称 */
	@ApiModelProperty("职业名称")
	private String occupationname;
	/** 对应字段：OCCUPATIONLEVEL,备注：职业等级 */
	@ApiModelProperty("职业等级")
	private String occupationlevel;
	/** 对应字段：ENTRYDATE,备注：入职日期 */
	@ApiModelProperty("入职日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date entrydate;
	/** 对应字段：RESIGNATIONDATE,备注：离职日期 */
	@ApiModelProperty("离职日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date resignationdate;
	/** 对应字段：EFFECTIVEDATE,备注：生效日期 */
	@ApiModelProperty("生效日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date effectivedate;
	/** 对应字段：ENDDATE,备注：终保日期 */
	@ApiModelProperty("终保日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date endDate;
	/** 对应字段：MONTHPAY,备注：约定月薪 */
	@ApiModelProperty( "约定月薪")
	private BigDecimal monthPay;
	/** 对应字段：PROJECT,备注：项目 */
	@ApiModelProperty( "约定月薪")
	private String project;
	/** 对应字段：HOMEADDRESS,备注：详细地址 */
	@ApiModelProperty( "详细地址")
	private String homeaddress;
	/** 对应字段：HOMETEL,备注：电话 */
	@ApiModelProperty( "电话")
	private String hometel;
	@ApiModelProperty( "保险计划")
	private String itemNo;

}
