package ins.channel.gupolicyemployerslist.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import ins.framework.mybatis.annotations.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GupolicyemployerslistVo对象.对应实体描述：最新保单雇员清单表
 *
 */
@Data
@ApiModel("GupolicyemployerslistVo对象")
public class GupolicyemployerslistVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@ApiModelProperty("主键")
	private String id;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;
	/** 对应字段：SUBPOLICYNO,备注：子保单号 */
	@ApiModelProperty("子保单号")
	private String subpolicyno;
	/** 对应字段：PLANID,备注：计划编码 fieldaa */
	@ApiModelProperty("计划编码 fieldaa")
	private String planid;
	/** 对应字段：ITEMNO,备注：标的序号 */
	@ApiModelProperty("标的序号")
	private BigDecimal itemNo;
	/** 对应字段：ITEMDETAILNO,备注：明细序号 */
	@ApiModelProperty("明细序号")
	private String itemdetailno;
	/** 对应字段：DYNAMICTARGETTYPE,备注：动态标的类型 见ggcode类型dynamiclisttype 默认存5 */
	@ApiModelProperty("动态标的类型 见ggcode类型dynamiclisttype 默认存5")
	private String dynamictargettype;
	/** 对应字段：LISTSEQNO,备注：标的物方案号 */
	@ApiModelProperty("标的物方案号")
	private BigDecimal listseqno;
	/** 对应字段：TARGETFLAG,备注：标的标识 U-更新 I-插入 D-删除 */
	@ApiModelProperty("标的标识 U-更新 I-插入 D-删除")
	private String targetflag;
	/** 对应字段：EMPNAME,备注：雇员姓名 */
	@ApiModelProperty("雇员姓名")
	private String empname;
	/** 对应字段：EMPSEX,备注：雇员性别 */
	@ApiModelProperty("雇员性别")
	private String empsex;
	/** 对应字段：EMPBIRTHDAY,备注：雇员生日 */
	@ApiModelProperty("雇员生日")
	private String empbirthday;
	/** 对应字段：EMPIDENTIFYTYPE,备注：雇员证件类型 */
	@ApiModelProperty("雇员证件类型")
	private String empidentifytype;
	/** 对应字段：EMPIDENTIFYNUMBER,备注：雇员证件号 */
	@ApiModelProperty("雇员证件号")
	private String empidentifynumber;
	/** 对应字段：OCCUPATIONCODE,备注：职业代码 */
	@ApiModelProperty("职业代码")
	private String occupationCode;
	/** 对应字段：OCCUPATIONNAME,备注：职业名称 */
	@ApiModelProperty("职业名称")
	private String occupationname;
	/** 对应字段：OCCUPATIONLEVEL,备注：职业等级 */
	@ApiModelProperty("职业等级")
	private String occupationlevel;
	/** 对应字段：ENTRYDATE,备注：入职日期 */
	@ApiModelProperty("入职日期")
	private Date entrydate;
	/** 对应字段：RESIGNATIONDATE,备注：离职日期 */
	@ApiModelProperty("离职日期")
	private Date resignationdate;
	/** 对应字段：EFFECTIVEDATE,备注：生效日期 */
	@ApiModelProperty("生效日期")
	private Date effectivedate;
	/** 对应字段：ENDDATE,备注：终保日期 */
	@ApiModelProperty("终保日期")
	private Date endDate;
	/** 对应字段：CURRENCY,备注：保单币别 */
	@ApiModelProperty("保单币别")
	private String currency;
	/** 对应字段：MONTHPAY,备注：约定月薪 */
	@ApiModelProperty( "约定月薪")
	private BigDecimal monthPay;
	/** 对应字段：EMPINSURED,备注：每人保额 */
	@ApiModelProperty("每人保额")
	private BigDecimal empinsured;
	/** 对应字段：EMPPREMIUM,备注：每人毛保费 */
	@ApiModelProperty("每人毛保费")
	private BigDecimal emppremium;
	/** 对应字段：PROVINCECODE,备注：省代码 */
	@ApiModelProperty("省代码")
	private String provincecode;
	/** 对应字段：CITYCODE,备注：市代码 */
	@ApiModelProperty("市代码")
	private String citycode;
	/** 对应字段：COUNTYCODE,备注：县代码 */
	@ApiModelProperty("县代码")
	private String countycode;
	/** 对应字段：MONEYLAUNDERINGIND,备注：洗钱风险 1-高，2-中，3-低； ggcode.codecode where codetype=“moneylaunderingind” */
	@ApiModelProperty("洗钱风险 1-高，2-中，3-低； ggcode.codecode where codetype=“moneylaunderingind”")
	private String moneylaunderingind;
	/** 对应字段：LISTBELONGIND,备注：默认0 */
	@ApiModelProperty("默认0")
	private String listbelongind;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@ApiModelProperty("入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@ApiModelProperty("修改日期")
	private Date updatesysdate;
}
