package ins.channel.gupolicyemployersplan.dao;

import ins.channel.gupolicymain.po.Gupolicymain;
import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicyemployersplan.po.Gupolicyemployersplan;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYEMPLOYERSPLAN对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicyemployersplanDao extends MybatisBaseDao<Gupolicyemployersplan, String> {
    int batchUpdate(List<Gupolicyemployersplan> list);

    int batchUpdateSelective(List<Gupolicyemployersplan> list);

    int batchInsert(List<Gupolicyemployersplan> list);

    List<Gupolicyemployersplan> selectByCondition(Gupolicyemployersplan employersplanCondition);

    void deleteByPolicyNo(String policyNo);
}
