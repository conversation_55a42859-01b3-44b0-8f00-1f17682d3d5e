package ins.channel.gupolicyemployersplan.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GUPOLICYEMPLOYERSPLAN的PO对象<br/>
 * 对应表名：GUPOLICYEMPLOYERSPLAN,备注：最新保单雇主计划表
 *
 */
@Data
@Table(name = "GUPOLICYEMPLOYERSPLAN")
public class Gupolicyemployersplan implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@Column(name = "ID", description = "主键")
	private String id;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@Column(name = "POLICYNO", description = "保单号码")
	private String policyNo;
	/** 对应字段：SUBPOLICYNO,备注：子保单号 */
	@Column(name = "SUBPOLICYNO", description = "子保单号")
	private String subpolicyno;
	/** 对应字段：PLANID,备注：计划编码 */
	@Column(name = "PLANID", description = "计划编码")
	private String planid;
	/** 对应字段：PLANCODE,备注：计划代码 */
	@Column(name = "PLANCODE", description = "计划代码")
	private String plancode;
	/** 对应字段：RISKCODE,备注：险种代码 */
	@Column(name = "RISKCODE", description = "险种代码")
	private String riskCode;
	/** 对应字段：ITEMNO,备注：标的序号 */
	@Column(name = "ITEMNO", description = "标的序号")
	private BigDecimal itemNo;
	/** 对应字段：ITEMCODE,备注：标的代码 */
	@Column(name = "ITEMCODE", description = "标的代码")
	private String itemCode;
	/** 对应字段：ITEMDETAILNO,备注：明细序号 */
	@Column(name = "ITEMDETAILNO", description = "明细序号")
	private String itemdetailno;
	/** 对应字段：CURRENCY,备注：币别 */
	@Column(name = "CURRENCY", description = "币别")
	private String currency;
	/** 对应字段：INDUSTRYTYPE,备注：行业类型 */
	@Column(name = "INDUSTRYTYPE", description = "行业类型")
	private String industrytype;
	/** 对应字段：INDUSTRYDETAIL,备注：行业细类 */
	@Column(name = "INDUSTRYDETAIL", description = "行业细类")
	private String industrydetail;
	/** 对应字段：SCHEMECODE,备注：方案代码 */
	@Column(name = "SCHEMECODE", description = "方案代码")
	private String schemecode;
	/** 对应字段：SCHEMENAME,备注：方案名称 */
	@Column(name = "SCHEMENAME", description = "方案名称")
	private String schemename;
	/** 对应字段：PERSONNELTYPE,备注：人员分类 */
	@Column(name = "PERSONNELTYPE", description = "人员分类")
	private String personneltype;
	/** 对应字段：DYNAMICTARGETTYPE,备注：动态标的类型 见ggcode类型dynamiclisttype 默认存10 */
	@Column(name = "DYNAMICTARGETTYPE", description = "动态标的类型 见ggcode类型dynamiclisttype 默认存10")
	private String dynamictargettype;
	/** 对应字段：LISTSEQNO,备注：标的物方案号 */
	@Column(name = "LISTSEQNO", description = "标的物方案号")
	private BigDecimal listseqno;
	/** 对应字段：TARGETFLAG,备注：标的标识 U-更新 I-插入 D-删除 */
	@Column(name = "TARGETFLAG", description = "标的标识 U-更新 I-插入 D-删除")
	private String targetflag;
	/** 对应字段：TARGETCALCULATE,备注：标的计算 0-不计算 1-计算 */
	@Column(name = "TARGETCALCULATE", description = "标的计算 0-不计算 1-计算")
	private String targetcalculate;
	/** 对应字段：EMPLOYEES,备注：投保雇员人数 */
	@Column(name = "EMPLOYEES", description = "投保雇员人数")
	private String employees;
	/** 对应字段：EMPLOYEESTHISCHANGE,备注：本次变更投保雇员人数 */
	@Column(name = "EMPLOYEESTHISCHANGE", description = "本次变更投保雇员人数")
	private String employeesthischange;
	/** 对应字段：LEGALFEESLIMIT,备注：法律费用责任限额 */
	@Column(name = "LEGALFEESLIMIT", description = "法律费用责任限额")
	private BigDecimal legalfeeslimit;
	/** 对应字段：LEGALFEESRATE,备注：法律费用责任费率‰ */
	@Column(name = "LEGALFEESRATE", description = "法律费用责任费率‰")
	private BigDecimal legalfeesrate;
	/** 对应字段：LEGALSUMPREMIUM,备注：法律合计费用 */
	@Column(name = "LEGALSUMPREMIUM", description = "法律合计费用")
	private BigDecimal legalsumpremium;
	/** 对应字段：PERSONCASUALTIESLIMIT,备注：每人伤亡责任限额 */
	@Column(name = "PERSONCASUALTIESLIMIT", description = "每人伤亡责任限额")
	private BigDecimal personcasualtieslimit;
	/** 对应字段：PERSONCASUALTIESRATE,备注：每人伤亡责任费率‰ */
	@Column(name = "PERSONCASUALTIESRATE", description = "每人伤亡责任费率‰")
	private BigDecimal personcasualtiesrate;
	/** 对应字段：PERSONMEDICALLIMIT,备注：每人医疗费用责任限额 */
	@Column(name = "PERSONMEDICALLIMIT", description = "每人医疗费用责任限额")
	private BigDecimal personmedicallimit;
	/** 对应字段：PERSONMEDICALRATE,备注：每人医疗费用责任费率‰ */
	@Column(name = "PERSONMEDICALRATE", description = "每人医疗费用责任费率‰")
	private BigDecimal personmedicalrate;
	/** 对应字段：FIELDALIMIT,备注：限额 */
	@Column(name = "FIELDALIMIT", description = "限额")
	private BigDecimal fieldalimit;
	/** 对应字段：FIELDARATE,备注：费率‰ */
	@Column(name = "FIELDARATE", description = "费率‰")
	private BigDecimal fieldarate;
	/** 对应字段：FIELDBLIMIT,备注：限额 */
	@Column(name = "FIELDBLIMIT", description = "限额")
	private BigDecimal fieldblimit;
	/** 对应字段：FIELDBRATE,备注：费率‰ */
	@Column(name = "FIELDBRATE", description = "费率‰")
	private BigDecimal fieldbrate;
	/** 对应字段：FIELDC,备注：备用 */
	@Column(name = "FIELDC", description = "备用")
	private String fieldc;
	/** 对应字段：FIELD,备注：备用 */
	@Column(name = "FIELD", description = "备用")
	private String field;
	/** 对应字段：PERSONSUMPREMIUM,备注：伤亡医疗合计费用 */
	@Column(name = "PERSONSUMPREMIUM", description = "伤亡医疗合计费用")
	private BigDecimal personsumpremium;
	/** 对应字段：LISTBELONGIND,备注：默认0 */
	@Column(name = "LISTBELONGIND", description = "默认0")
	private String listbelongind;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@Column(name = "INPUTDATE", description = "入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@Column(name = "UPDATESYSDATE", description = "修改日期")
	private Date updatesysdate;
}
