package ins.channel.gupolicyemployersplan.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GupolicyemployersplanVo对象.对应实体描述：最新保单雇主计划表
 *
 */
@Data
@ApiModel("GupolicyemployersplanVo对象")
public class GupolicyemployersplanVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@ApiModelProperty("主键")
	private String id;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;
	/** 对应字段：SUBPOLICYNO,备注：子保单号 */
	@ApiModelProperty("子保单号")
	private String subpolicyno;
	/** 对应字段：PLANID,备注：计划编码 */
	@ApiModelProperty("计划编码")
	private String planid;
	/** 对应字段：PLANCODE,备注：计划代码 */
	@ApiModelProperty("计划代码")
	private String plancode;
	/** 对应字段：RISKCODE,备注：险种代码 */
	@ApiModelProperty("险种代码")
	private String riskCode;
	/** 对应字段：ITEMNO,备注：标的序号 */
	@ApiModelProperty("标的序号")
	private BigDecimal itemNo;
	/** 对应字段：ITEMCODE,备注：标的代码 */
	@ApiModelProperty("标的代码")
	private String itemCode;
	/** 对应字段：ITEMDETAILNO,备注：明细序号 */
	@ApiModelProperty("明细序号")
	private String itemdetailno;
	/** 对应字段：CURRENCY,备注：币别 */
	@ApiModelProperty("币别")
	private String currency;
	/** 对应字段：INDUSTRYTYPE,备注：行业类型 */
	@ApiModelProperty("行业类型")
	private String industrytype;
	/** 对应字段：INDUSTRYDETAIL,备注：行业细类 */
	@ApiModelProperty("行业细类")
	private String industrydetail;
	/** 对应字段：SCHEMECODE,备注：方案代码 */
	@ApiModelProperty("方案代码")
	private String schemecode;
	/** 对应字段：SCHEMENAME,备注：方案名称 */
	@ApiModelProperty("方案名称")
	private String schemename;
	/** 对应字段：PERSONNELTYPE,备注：人员分类 */
	@ApiModelProperty("人员分类")
	private String personneltype;
	/** 对应字段：DYNAMICTARGETTYPE,备注：动态标的类型 见ggcode类型dynamiclisttype 默认存10 */
	@ApiModelProperty("动态标的类型 见ggcode类型dynamiclisttype 默认存10")
	private String dynamictargettype;
	/** 对应字段：LISTSEQNO,备注：标的物方案号 */
	@ApiModelProperty("标的物方案号")
	private BigDecimal listseqno;
	/** 对应字段：TARGETFLAG,备注：标的标识 U-更新 I-插入 D-删除 */
	@ApiModelProperty("标的标识 U-更新 I-插入 D-删除")
	private String targetflag;
	/** 对应字段：TARGETCALCULATE,备注：标的计算 0-不计算 1-计算 */
	@ApiModelProperty("标的计算 0-不计算 1-计算")
	private String targetcalculate;
	/** 对应字段：EMPLOYEES,备注：投保雇员人数 */
	@ApiModelProperty("投保雇员人数")
	private String employees;
	/** 对应字段：EMPLOYEESTHISCHANGE,备注：本次变更投保雇员人数 */
	@ApiModelProperty("本次变更投保雇员人数")
	private String employeesthischange;
	/** 对应字段：LEGALFEESLIMIT,备注：法律费用责任限额 */
	@ApiModelProperty("法律费用责任限额")
	private BigDecimal legalfeeslimit;
	/** 对应字段：LEGALFEESRATE,备注：法律费用责任费率‰ */
	@ApiModelProperty("法律费用责任费率‰")
	private BigDecimal legalfeesrate;
	/** 对应字段：LEGALSUMPREMIUM,备注：法律合计费用 */
	@ApiModelProperty("法律合计费用")
	private BigDecimal legalsumpremium;
	/** 对应字段：PERSONCASUALTIESLIMIT,备注：每人伤亡责任限额 */
	@ApiModelProperty("每人伤亡责任限额")
	private BigDecimal personcasualtieslimit;
	/** 对应字段：PERSONCASUALTIESRATE,备注：每人伤亡责任费率‰ */
	@ApiModelProperty("每人伤亡责任费率‰")
	private BigDecimal personcasualtiesrate;
	/** 对应字段：PERSONMEDICALLIMIT,备注：每人医疗费用责任限额 */
	@ApiModelProperty("每人医疗费用责任限额")
	private BigDecimal personmedicallimit;
	/** 对应字段：PERSONMEDICALRATE,备注：每人医疗费用责任费率‰ */
	@ApiModelProperty("每人医疗费用责任费率‰")
	private BigDecimal personmedicalrate;
	/** 对应字段：FIELDALIMIT,备注：限额 */
	@ApiModelProperty("限额")
	private BigDecimal fieldalimit;
	/** 对应字段：FIELDARATE,备注：费率‰ */
	@ApiModelProperty("费率‰")
	private BigDecimal fieldarate;
	/** 对应字段：FIELDBLIMIT,备注：限额 */
	@ApiModelProperty("限额")
	private BigDecimal fieldblimit;
	/** 对应字段：FIELDBRATE,备注：费率‰ */
	@ApiModelProperty("费率‰")
	private BigDecimal fieldbrate;
	/** 对应字段：FIELDC,备注：备用 */
	@ApiModelProperty("备用")
	private String fieldc;
	/** 对应字段：FIELD,备注：备用 */
	@ApiModelProperty("备用")
	private String field;
	/** 对应字段：PERSONSUMPREMIUM,备注：伤亡医疗合计费用 */
	@ApiModelProperty("伤亡医疗合计费用")
	private BigDecimal personsumpremium;
	/** 对应字段：LISTBELONGIND,备注：默认0 */
	@ApiModelProperty("默认0")
	private String listbelongind;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@ApiModelProperty("入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@ApiModelProperty("修改日期")
	private Date updatesysdate;
}
