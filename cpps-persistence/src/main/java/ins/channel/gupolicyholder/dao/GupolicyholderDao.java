package ins.channel.gupolicyholder.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicyholder.po.Gupolicyholder;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYHOLDER对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicyholderDao extends MybatisBaseDao<Gupolicyholder, String> {

    int batchUpdate(List<Gupolicyholder> list);

    int batchUpdateSelective(List<Gupolicyholder> list);

    int batchInsert(List<Gupolicyholder> list);

    void deleteByPolicyNo(String policyNo);
}
