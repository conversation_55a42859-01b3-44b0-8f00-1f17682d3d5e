package ins.channel.gupolicyinsured.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicyinsured.po.Gupolicyinsured;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYINSURED对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicyinsuredDao extends MybatisBaseDao<Gupolicyinsured, String> {
    int batchUpdate(List<Gupolicyinsured> list);

    int batchUpdateSelective(List<Gupolicyinsured> list);

    int batchInsert(List<Gupolicyinsured> list);

    void deleteByPolicyNo(String policyNo);
}
