package ins.channel.gupolicyitemaccilist.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicyitemaccilist.po.Gupolicyitemaccilist;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYITEMACCILIST对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicyitemaccilistDao extends MybatisBaseDao<Gupolicyitemaccilist,String> {

    void deleteByPolicyNo(String policyNo);

    int batchInsert(List<Gupolicyitemaccilist> gupolicyitemaccilistList);
}
