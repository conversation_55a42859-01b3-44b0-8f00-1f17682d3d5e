package ins.channel.gupolicyitemaccilist.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GUPOLICYITEMACCILIST的复合主键PO对象<br/>
 * 对应表名：GUPOLICYITEMACCILIST
 *
 */
@Data
@Table(name = "GUPOLICYITEMACCILIST")
public class Gupolicyitemaccilist  implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：ID,备注：主键 */
	@Column(name = "ID", description = "主键")
	private String id;
	/** 对应字段：CLIENTNO */
	@Column(name = "CLIENTNO")
	private BigDecimal clientno;
	/** 对应字段：ITEMNO,备注：意健险只有1个标的，所以 此序号始终为1 */
	@Column(name = "ITEMNO", description = "意健险只有1个标的，所以 此序号始终为1")
	private BigDecimal itemNo;
	/** 对应字段：PLANCODE */
	@Column(name = "PLANCODE")
	private String plancode;
	/** 对应字段：POLICYNO */
	@Column(name = "POLICYNO")
	private String policyNo;
	/** 对应字段：RISKCODE */
	@Column(name = "RISKCODE")
	private String riskCode;

	/** 对应字段：ITEMCODE,备注：对应GGITEM中代码：05 */
	@Column(name = "ITEMCODE", description = "对应GGITEM中代码：05")
	private String itemCode;
	/** 对应字段：ITEMNAME,备注：人员 */
	@Column(name = "ITEMNAME", description = "人员")
	private String itemName;
	/** 对应字段：ITEMDETAILNO,备注：对应分类序号 */
	@Column(name = "ITEMDETAILNO", description = "对应分类序号")
	private BigDecimal itemdetailno;
	/** 对应字段：ITEMDETAILCODE */
	@Column(name = "ITEMDETAILCODE")
	private String itemdetailcode;
	/** 对应字段：ITEMDETAILNAME */
	@Column(name = "ITEMDETAILNAME")
	private String itemDetailName;
	/** 对应字段：CLIENTCODE,备注：▲来源于gsclientmain */
	@Column(name = "CLIENTCODE", description = "▲来源于gsclientmain")
	private String clientcode;
	/** 对应字段：MEMBERNO,备注：被保人所属公司内部编号 */
	@Column(name = "MEMBERNO", description = "被保人所属公司内部编号")
	private String memberno;
	/** 对应字段：CLIENTCNAME */
	@Column(name = "CLIENTCNAME")
	private String clientcname;
	/** 对应字段：CLIENTENAME */
	@Column(name = "CLIENTENAME")
	private String clientename;
	/** 对应字段：SEX,备注：▲来源于GGCODE */
	@Column(name = "SEX", description = "▲来源于GGCODE")
	private String sex;
	/** 对应字段：BIRTHDAY */
	@Column(name = "BIRTHDAY")
	private Date birthday;
	/** 对应字段：AGE */
	@Column(name = "AGE")
	private BigDecimal age;
	/** 对应字段：IDENTIFYTYPEA,备注：▲来源于GGCODE */
	@Column(name = "IDENTIFYTYPEA", description = "▲来源于GGCODE")
	private String identifytypea;
	/** 对应字段：IDENTIFYNOA */
	@Column(name = "IDENTIFYNOA")
	private String identifynoa;
	/** 对应字段：IDENTIFYTYPEB,备注：▲来源于GGCODE */
	@Column(name = "IDENTIFYTYPEB", description = "▲来源于GGCODE")
	private String identifytypeb;
	/** 对应字段：IDENTIFYNOB */
	@Column(name = "IDENTIFYNOB")
	private String identifynob;
	/** 对应字段：ENROLLMENTDATE */
	@Column(name = "ENROLLMENTDATE")
	private Date enrollmentdate;
	/** 对应字段：STARTDATE */
	@Column(name = "STARTDATE")
	private Date startDate;
	/** 对应字段：ENDDATE */
	@Column(name = "ENDDATE")
	private Date endDate;
	/** 对应字段：BANKNAME,备注：标的地址 */
	@Column(name = "BANKNAME", description = "标的地址")
	private String bankName;
	/** 对应字段：BANKACCOUNTNO */
	@Column(name = "BANKACCOUNTNO")
	private String bankaccountno;
	/** 对应字段：CREDITNO */
	@Column(name = "CREDITNO")
	private String creditNo;
	/** 对应字段：CREDITEXPIRY */
	@Column(name = "CREDITEXPIRY")
	private Date creditexpiry;
	/** 对应字段：OCCUPATIONTYPE,备注：对应职业等级，目前总共4类 */
	@Column(name = "OCCUPATIONTYPE", description = "对应职业等级，目前总共4类")
	private String occupationtype;
	/** 对应字段：OCCUPATION */
	@Column(name = "OCCUPATION")
	private String occupation;
	/** 对应字段：OCCUPATIONCODE,备注：来源于GGCODE */
	@Column(name = "OCCUPATIONCODE", description = "来源于GGCODE")
	private String occupationCode;
	/** 对应字段：JOBTITLE */
	@Column(name = "JOBTITLE")
	private String jobTitle;
	/** 对应字段：HOMEADDRESS */
	@Column(name = "HOMEADDRESS")
	private String homeAddress;
	/** 对应字段：HOMETEL */
	@Column(name = "HOMETEL")
	private String hometel;
	/** 对应字段：BUSINESSNATURE */
	@Column(name = "BUSINESSNATURE")
	private String businessNature;
	/** 对应字段：JOBUNITCODE */
	@Column(name = "JOBUNITCODE")
	private String jobunitcode;
	/** 对应字段：JOBUNITNAME */
	@Column(name = "JOBUNITNAME")
	private String jobunitname;
	/** 对应字段：OFFICETEL */
	@Column(name = "OFFICETEL")
	private String officetel;
	/** 对应字段：EMPLOYERNAME */
	@Column(name = "EMPLOYERNAME")
	private String employername;
	/** 对应字段：EMPLOYEEIND */
	@Column(name = "EMPLOYEEIND")
	private String employeeind;
	/** 对应字段：MATERNITYIND */
	@Column(name = "MATERNITYIND")
	private String maternityind;
	/** 对应字段：AUTOPAYIND */
	@Column(name = "AUTOPAYIND")
	private String autopayind;
	/** 对应字段：RELATIONCODE */
	@Column(name = "RELATIONCODE")
	private String relationCode;
	/** 对应字段：RELATIONSHIP,备注：▲来源于GGCODE */
	@Column(name = "RELATIONSHIP", description = "▲来源于GGCODE")
	private String relationship;
	/** 对应字段：PROJECTCODE */
	@Column(name = "PROJECTCODE")
	private String projectcode;
	/** 对应字段：UWCOUNT */
	@Column(name = "UWCOUNT")
	private BigDecimal uwcount;
	/** 对应字段：SUMINSURED */
	@Column(name = "SUMINSURED")
	private BigDecimal suminsured;
	/** 对应字段：BASEPREMIUM */
	@Column(name = "BASEPREMIUM")
	private BigDecimal basePremium;
	/** 对应字段：NETPREMIUM */
	@Column(name = "NETPREMIUM")
	private BigDecimal netPremium;
	/** 对应字段：GROSSPREMIUM */
	@Column(name = "GROSSPREMIUM")
	private BigDecimal grosspremium;
	/** 对应字段：ANNUALPREMIUM */
	@Column(name = "ANNUALPREMIUM")
	private BigDecimal annualpremium;
	/** 对应字段：PRORATAPREMIUM */
	@Column(name = "PRORATAPREMIUM")
	private BigDecimal proratapremium;
	/** 对应字段：EXPRIREFUND,备注：0：否；1:是 */
	@Column(name = "EXPRIREFUND", description = "0：否；1:是")
	private String exprirefund;
	/** 对应字段：PREEXISTIND,备注：0：否；1:是 */
	@Column(name = "PREEXISTIND", description = "0：否；1:是")
	private String preexistind;
	/** 对应字段：ACTIVEIND,备注：0：否；1:是 */
	@Column(name = "ACTIVEIND", description = "0：否；1:是")
	private String activeind;
	/** 对应字段：COMMENCEDATE,备注：历次投保记录中最早一张保单的起保日期 */
	@Column(name = "COMMENCEDATE", description = "历次投保记录中最早一张保单的起保日期")
	private Date commencedate;
	/** 对应字段：EMAIL */
	@Column(name = "EMAIL")
	private String email;
	/** 对应字段：DISTRICT */
	@Column(name = "DISTRICT")
	private String district;
	/** 对应字段：IPASERVICE,备注：0,否，1,是 */
	@Column(name = "IPASERVICE", description = "0,否，1,是")
	private String ipaservice;
	/** 对应字段：COUNTRYCODE,备注：▲来源于ggcountry */
	@Column(name = "COUNTRYCODE", description = "▲来源于ggcountry")
	private String countryCode;
	/** 对应字段：REGISTADDRESS */
	@Column(name = "REGISTADDRESS")
	private String registaddress;
	/** 对应字段：MEMBERREFRENCE */
	@Column(name = "MEMBERREFRENCE")
	private String memberrefrence;
	/** 对应字段：DISCOUNT */
	@Column(name = "DISCOUNT")
	private BigDecimal discount;
	/** 对应字段：SPECIALCLAUSE */
	@Column(name = "SPECIALCLAUSE")
	private String specialclause;
	/** 对应字段：REMARK */
	@Column(name = "REMARK")
	private String remark;
	/** 对应字段：FLAG */
	@Column(name = "FLAG")
	private String flag;
	/** 对应字段：JOURNEYSTART */
	@Column(name = "JOURNEYSTART")
	private String journeystart;
	/** 对应字段：JOURNEYEND */
	@Column(name = "JOURNEYEND")
	private String journeyend;
	/** 对应字段：JOURNEYBACK */
	@Column(name = "JOURNEYBACK")
	private String journeyback;
	/** 对应字段：APPLIRELATION,备注：参考ggCode表中codetype为“InsuredIdentity”的数据 */
	@Column(name = "APPLIRELATION", description = "参考ggCode表中codetype为“InsuredIdentity”的数据")
	private String applirelation;
	/** 对应字段：LINKERNAME */
	@Column(name = "LINKERNAME")
	private String linkerName;
	/** 对应字段：LINKERPHONE */
	@Column(name = "LINKERPHONE")
	private String linkerphone;
	/** 对应字段：INNERREMARK,备注：内部备注 */
	@Column(name = "INNERREMARK", description = "内部备注")
	private String innerremark;
	/** 对应字段：PLEDGED,备注：借款金额 */
	@Column(name = "PLEDGED", description = "借款金额")
	private BigDecimal pledged;
	/** 对应字段：CLAIMPAYWAY,备注：数据来源于ggcode.codetype=ClaimPayWay */
	@Column(name = "CLAIMPAYWAY", description = "数据来源于ggcode.codetype=ClaimPayWay")
	private String claimpayway;
	/** 对应字段：CLIENTTYPE,备注：客户类型，来源ggcode.codetype= CustomerType */
	@Column(name = "CLIENTTYPE", description = "客户类型，来源ggcode.codetype= CustomerType")
	private String clienttype;
	/** 对应字段：OCCUPATIONTYPENAME,备注：职业大类名称 */
	@Column(name = "OCCUPATIONTYPENAME", description = "职业大类名称")
	private String occupationtypename;
	/** 对应字段：OCCUPATIONLEVEL,备注：职业等级 */
	@Column(name = "OCCUPATIONLEVEL", description = "职业等级")
	private String occupationlevel;
	/** 对应字段：MAININSUREDIND,备注：是否主被保险人 */
	@Column(name = "MAININSUREDIND", description = "是否主被保险人")
	private String maininsuredind;
	/** 对应字段：SURNAME,备注：姓氏 */
	@Column(name = "SURNAME", description = "姓氏")
	private String surname;
	/** 对应字段：MONIKER,备注：名字 */
	@Column(name = "MONIKER", description = "名字")
	private String moniker;
	/** 对应字段：FIRSTNAME,备注：FirstName英文名 */
	@Column(name = "FIRSTNAME", description = "FirstName英文名")
	private String firstname;
	/** 对应字段：LASTNAME,备注：LastName英文姓氏 */
	@Column(name = "LASTNAME", description = "LastName英文姓氏")
	private String lastname;
	/** 对应字段：PROVINCECODE,备注：联系地址（省级） */
	@Column(name = "PROVINCECODE", description = "联系地址（省级）")
	private String provincecode;
	/** 对应字段：CITYCODE,备注：联系地址（地级） */
	@Column(name = "CITYCODE", description = "联系地址（地级）")
	private String citycode;
	/** 对应字段：COUNTYCODE,备注：联系地址区（县级） */
	@Column(name = "COUNTYCODE", description = "联系地址区（县级）")
	private String countycode;
	/** 对应字段：DISPLAYNO,备注：序号 */
	@Column(name = "DISPLAYNO", description = "序号")
	private BigDecimal displayNo;
	/** 对应字段：UPDATESYSDATE,备注：系统更新时间 */
	@Column(name = "UPDATESYSDATE", description = "系统更新时间")
	private Date updatesysdate;
	/** 对应字段：inputDate,备注：入机时间 */
	@Column(name = "INPUTDATE", description = "入机时间")
	private Date inputDate;
	/** 对应字段：ENDORFLAG,备注：批改标志 */
	@Column(name = "ENDORFLAG", description = "批改标志")
	private String endorflag;
	/** 对应字段：GROUPTYPE,备注：分组组别 */
	@Column(name = "GROUPTYPE", description = "分组组别")
	private String groupType;
	/** 对应字段：SIGNATURE,备注：被保险人签字 */
	@Column(name = "SIGNATURE", description = "被保险人签字")
	private String signature;
	/** 对应字段：ITEMADDRESS */
	@Column(name = "ITEMADDRESS")
	private String itemAddress;
	/** 对应字段：USENATURECODE,备注：使用性质,来源ggcode.codetype=UseNatureCode */
	@Column(name = "USENATURECODE", description = "使用性质,来源ggcode.codetype=UseNatureCode")
	private String useNatureCode;
	/** 对应字段：DRIVERTYPE,备注：驾驶人类别,来源于ggcode.codetype=DriverType */
	@Column(name = "DRIVERTYPE", description = "驾驶人类别,来源于ggcode.codetype=DriverType")
	private String drivertype;
	/** 对应字段：DRIVERLICENSENOOLD */
	@Column(name = "DRIVERLICENSENOOLD")
	private BigDecimal driverlicensenoold;
	/** 对应字段：DRIVERLICENSEEXPIRATIONDATE,备注：驾照有效期 */
	@Column(name = "DRIVERLICENSEEXPIRATIONDATE", description = "驾照有效期")
	private Date driverlicenseexpirationdate;
	/** 对应字段：BENEFITPROJECT,备注：其他福利计划/医疗福利计划,来源ggcode.codetype=BenefitProject */
	@Column(name = "BENEFITPROJECT", description = "其他福利计划/医疗福利计划,来源ggcode.codetype=BenefitProject")
	private String benefitproject;
	/** 对应字段：SOCIALSECURITYINFO */
	@Column(name = "SOCIALSECURITYINFO")
	private String socialsecurityinfo;
	/** 对应字段：DRIVINGMODEL */
	@Column(name = "DRIVINGMODEL")
	private String drivingmodel;
	/** 对应字段：DRIVERLICENSENO,备注：机动车驾驶执照号 */
	@Column(name = "DRIVERLICENSENO", description = "机动车驾驶执照号")
	private String driverlicenseno;
	/** 对应字段：ONTHEJOBSTATUS */
	@Column(name = "ONTHEJOBSTATUS")
	private String onthejobstatus; //在职状态（01：在职；02：退保；09：其他）1144
	/** 对应字段：PLATCUSTOMNO,备注：意健险平台客户代码 */
	@Column(name = "PLATCUSTOMNO", description = "意健险平台客户代码")
	private String platcustomno;
	/** 对应字段：BEIFENDESCRIPTION,备注：北分描述 */
	@Column(name = "BEIFENDESCRIPTION", description = "北分描述")
	private String beifendescription;
	/** 对应字段：INSUREDNAME,备注：投保人姓名 */
	@Column(name = "INSUREDNAME", description = "投保人姓名")
	private String insuredName;
	/** 对应字段：INSUREDSEX,备注：投保人性别 */
	@Column(name = "INSUREDSEX", description = "投保人性别")
	private String insuredsex;
	/** 对应字段：INSUREDBIRTHDAY,备注：投保人出生日期 */
	@Column(name = "INSUREDBIRTHDAY", description = "投保人出生日期")
	private Date insuredbirthday;
	/** 对应字段：IDENTIFYTYPEC,备注：证件类型C */
	@Column(name = "IDENTIFYTYPEC", description = "证件类型C")
	private String identifytypec;
	/** 对应字段：IDENTIFYNOC,备注：证件号码C */
	@Column(name = "IDENTIFYNOC", description = "证件号码C")
	private String identifynoc;
	/** 对应字段：INSUREDHOMEADDRESS,备注：投保人地址 */
	@Column(name = "INSUREDHOMEADDRESS", description = "投保人地址")
	private String insuredhomeaddress;
	/** 对应字段：INSUREDHOMETEL,备注：投保人手机号 */
	@Column(name = "INSUREDHOMETEL", description = "投保人手机号")
	private String insuredhometel;
	/** 对应字段：INSUREDPHONE,备注：投保人与其他联系人电话 */
	@Column(name = "INSUREDPHONE", description = "投保人与其他联系人电话")
	private String insuredPhone;
	/** 对应字段：INSUREDEMAIL,备注：投保人电子邮箱 */
	@Column(name = "INSUREDEMAIL", description = "投保人电子邮箱")
	private String insuredemail;
	/** 对应字段：INSUREDCLASS,备注：被保人班级 */
	@Column(name = "INSUREDCLASS", description = "被保人班级")
	private String insuredclass;
	/** 对应字段：SCHOOL,备注：被保人学校 */
	@Column(name = "SCHOOL", description = "被保人学校")
	private String school;
	/** 对应字段：JOINTINSUREDFLAG */
	@Column(name = "JOINTINSUREDFLAG")
	private String jointinsuredflag;
	/** 对应字段：VACCINATION,备注：接种疫苗 */
	@Column(name = "VACCINATION", description = "接种疫苗")
	private String vaccination;
	/** 对应字段：INOCULABILITYTIME,备注：接种时间 */
	@Column(name = "INOCULABILITYTIME", description = "接种时间")
	private Date inoculabilitytime;
	/** 对应字段：PORTABLETYPE,备注：便携式电子产品类型 */
	@Column(name = "PORTABLETYPE", description = "便携式电子产品类型")
	private String portabletype;
	/** 对应字段：BRAND,备注：品牌 */
	@Column(name = "BRAND", description = "品牌")
	private String brand;
	/** 对应字段：MODEL,备注：型号 */
	@Column(name = "MODEL", description = "型号")
	private String model;
	/** 对应字段：UNIQUEENCODING,备注：唯一编码 */
	@Column(name = "UNIQUEENCODING", description = "唯一编码")
	private String uniqueencoding;
	/** 对应字段：UPLOADTYPE,备注：清单导入类型 */
	@Column(name = "UPLOADTYPE", description = "清单导入类型")
	private String uploadtype;
}
