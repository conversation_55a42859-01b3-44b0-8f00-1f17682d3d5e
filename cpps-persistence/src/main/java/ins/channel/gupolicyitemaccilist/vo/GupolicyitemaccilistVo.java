package ins.channel.gupolicyitemaccilist.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * GupolicyitemaccilistVo对象.
 *
 */
@Data
@ApiModel("GupolicyitemaccilistVo对象")
public class GupolicyitemaccilistVo  implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CLIENTNO */
	@ApiModelProperty()
	private BigDecimal clientno;
	/** 对应字段：ITEMNO,备注：意健险只有1个标的，所以 此序号始终为1 */
	@ApiModelProperty("意健险只有1个标的，所以 此序号始终为1")
	private BigDecimal itemNo;
	/** 对应字段：PLANCODE */
	@ApiModelProperty()
	private String plancode;
	/** 对应字段：POLICYNO */
	@ApiModelProperty()
	private String policyNo;
	/** 对应字段：RISKCODE */
	@ApiModelProperty()
	private String riskCode;
	/** 对应字段：ITEMCODE,备注：对应GGITEM中代码：05 */
	@ApiModelProperty("对应GGITEM中代码：05")
	private String itemCode;
	/** 对应字段：ITEMNAME,备注：人员 */
	@ApiModelProperty("人员")
	private String itemName;
	/** 对应字段：ITEMDETAILNO,备注：对应分类序号 */
	@ApiModelProperty("对应分类序号")
	private BigDecimal itemdetailno;
	/** 对应字段：ITEMDETAILCODE */
	@ApiModelProperty()
	private String itemdetailcode;
	/** 对应字段：ITEMDETAILNAME */
	@ApiModelProperty()
	private String itemDetailName;
	/** 对应字段：CLIENTCODE,备注：▲来源于gsclientmain */
	@ApiModelProperty("▲来源于gsclientmain")
	private String clientcode;
	/** 对应字段：MEMBERNO,备注：被保人所属公司内部编号 */
	@ApiModelProperty("被保人所属公司内部编号")
	private String memberno;
	/** 对应字段：CLIENTCNAME */
	@ApiModelProperty()
	private String clientcname;
	/** 对应字段：CLIENTENAME */
	@ApiModelProperty()
	private String clientename;
	/** 对应字段：SEX,备注：▲来源于GGCODE */
	@ApiModelProperty("▲来源于GGCODE")
	private String sex;
	/** 对应字段：BIRTHDAY */
	@ApiModelProperty()
	private Date birthday;
	/** 对应字段：AGE */
	@ApiModelProperty()
	private BigDecimal age;
	/** 对应字段：IDENTIFYTYPEA,备注：▲来源于GGCODE */
	@ApiModelProperty("▲来源于GGCODE")
	private String identifytypea;
	/** 对应字段：IDENTIFYNOA */
	@ApiModelProperty()
	private String identifynoa;
	/** 对应字段：IDENTIFYTYPEB,备注：▲来源于GGCODE */
	@ApiModelProperty("▲来源于GGCODE")
	private String identifytypeb;
	/** 对应字段：IDENTIFYNOB */
	@ApiModelProperty()
	private String identifynob;
	/** 对应字段：ENROLLMENTDATE */
	@ApiModelProperty()
	private Date enrollmentdate;
	/** 对应字段：STARTDATE */
	@ApiModelProperty()
	private Date startDate;
	/** 对应字段：ENDDATE */
	@ApiModelProperty()
	private Date endDate;
	/** 对应字段：BANKNAME,备注：标的地址 */
	@ApiModelProperty("标的地址")
	private String bankName;
	/** 对应字段：BANKACCOUNTNO */
	@ApiModelProperty()
	private String bankaccountno;
	/** 对应字段：CREDITNO */
	@ApiModelProperty()
	private String creditNo;
	/** 对应字段：CREDITEXPIRY */
	@ApiModelProperty()
	private Date creditexpiry;
	/** 对应字段：OCCUPATIONTYPE,备注：对应职业等级，目前总共4类 */
	@ApiModelProperty("对应职业等级，目前总共4类")
	private String occupationtype;
	/** 对应字段：OCCUPATION */
	@ApiModelProperty()
	private String occupation;
	/** 对应字段：OCCUPATIONCODE,备注：来源于GGCODE */
	@ApiModelProperty("来源于GGCODE")
	private String occupationCode;
	/** 对应字段：JOBTITLE */
	@ApiModelProperty()
	private String jobTitle;
	/** 对应字段：HOMEADDRESS */
	@ApiModelProperty()
	private String homeAddress;
	/** 对应字段：HOMETEL */
	@ApiModelProperty()
	private String hometel;
	/** 对应字段：BUSINESSNATURE */
	@ApiModelProperty()
	private String businessNature;
	/** 对应字段：JOBUNITCODE */
	@ApiModelProperty()
	private String jobunitcode;
	/** 对应字段：JOBUNITNAME */
	@ApiModelProperty()
	private String jobunitname;
	/** 对应字段：OFFICETEL */
	@ApiModelProperty()
	private String officetel;
	/** 对应字段：EMPLOYERNAME */
	@ApiModelProperty()
	private String employername;
	/** 对应字段：EMPLOYEEIND */
	@ApiModelProperty()
	private String employeeind;
	/** 对应字段：MATERNITYIND */
	@ApiModelProperty()
	private String maternityind;
	/** 对应字段：AUTOPAYIND */
	@ApiModelProperty()
	private String autopayind;
	/** 对应字段：RELATIONCODE */
	@ApiModelProperty()
	private String relationCode;
	/** 对应字段：RELATIONSHIP,备注：▲来源于GGCODE */
	@ApiModelProperty("▲来源于GGCODE")
	private String relationship;
	/** 对应字段：PROJECTCODE */
	@ApiModelProperty()
	private String projectcode;
	/** 对应字段：UWCOUNT */
	@ApiModelProperty()
	private BigDecimal uwcount;
	/** 对应字段：SUMINSURED */
	@ApiModelProperty()
	private BigDecimal suminsured;
	/** 对应字段：BASEPREMIUM */
	@ApiModelProperty()
	private BigDecimal basePremium;
	/** 对应字段：NETPREMIUM */
	@ApiModelProperty()
	private BigDecimal netPremium;
	/** 对应字段：GROSSPREMIUM */
	@ApiModelProperty()
	private BigDecimal grosspremium;
	/** 对应字段：ANNUALPREMIUM */
	@ApiModelProperty()
	private BigDecimal annualpremium;
	/** 对应字段：PRORATAPREMIUM */
	@ApiModelProperty()
	private BigDecimal proratapremium;
	/** 对应字段：EXPRIREFUND,备注：0：否；1:是 */
	@ApiModelProperty("0：否；1:是")
	private String exprirefund;
	/** 对应字段：PREEXISTIND,备注：0：否；1:是 */
	@ApiModelProperty("0：否；1:是")
	private String preexistind;
	/** 对应字段：ACTIVEIND,备注：0：否；1:是 */
	@ApiModelProperty("0：否；1:是")
	private String activeind;
	/** 对应字段：COMMENCEDATE,备注：历次投保记录中最早一张保单的起保日期 */
	@ApiModelProperty("历次投保记录中最早一张保单的起保日期")
	private Date commencedate;
	/** 对应字段：EMAIL */
	@ApiModelProperty()
	private String email;
	/** 对应字段：DISTRICT */
	@ApiModelProperty()
	private String district;
	/** 对应字段：IPASERVICE,备注：0,否，1,是 */
	@ApiModelProperty("0,否，1,是")
	private String ipaservice;
	/** 对应字段：COUNTRYCODE,备注：▲来源于ggcountry */
	@ApiModelProperty("▲来源于ggcountry")
	private String countryCode;
	/** 对应字段：REGISTADDRESS */
	@ApiModelProperty()
	private String registaddress;
	/** 对应字段：MEMBERREFRENCE */
	@ApiModelProperty()
	private String memberrefrence;
	/** 对应字段：DISCOUNT */
	@ApiModelProperty()
	private BigDecimal discount;
	/** 对应字段：SPECIALCLAUSE */
	@ApiModelProperty()
	private String specialclause;
	/** 对应字段：REMARK */
	@ApiModelProperty()
	private String remark;
	/** 对应字段：FLAG */
	@ApiModelProperty()
	private String flag;
	/** 对应字段：JOURNEYSTART */
	@ApiModelProperty()
	private String journeystart;
	/** 对应字段：JOURNEYEND */
	@ApiModelProperty()
	private String journeyend;
	/** 对应字段：JOURNEYBACK */
	@ApiModelProperty()
	private String journeyback;
	/** 对应字段：APPLIRELATION,备注：参考ggCode表中codetype为“InsuredIdentity”的数据 */
	@ApiModelProperty("参考ggCode表中codetype为“InsuredIdentity”的数据")
	private String applirelation;
	/** 对应字段：LINKERNAME */
	@ApiModelProperty()
	private String linkerName;
	/** 对应字段：LINKERPHONE */
	@ApiModelProperty()
	private String linkerphone;
	/** 对应字段：INNERREMARK,备注：内部备注 */
	@ApiModelProperty("内部备注")
	private String innerremark;
	/** 对应字段：PLEDGED,备注：借款金额 */
	@ApiModelProperty("借款金额")
	private BigDecimal pledged;
	/** 对应字段：CLAIMPAYWAY,备注：数据来源于ggcode.codetype=ClaimPayWay */
	@ApiModelProperty("数据来源于ggcode.codetype=ClaimPayWay")
	private String claimpayway;
	/** 对应字段：CLIENTTYPE,备注：客户类型，来源ggcode.codetype= CustomerType */
	@ApiModelProperty("客户类型，来源ggcode.codetype= CustomerType")
	private String clienttype;
	/** 对应字段：OCCUPATIONTYPENAME,备注：职业大类名称 */
	@ApiModelProperty("职业大类名称")
	private String occupationtypename;
	/** 对应字段：OCCUPATIONLEVEL,备注：职业等级 */
	@ApiModelProperty("职业等级")
	private String occupationlevel;
	/** 对应字段：MAININSUREDIND,备注：是否主被保险人 */
	@ApiModelProperty("是否主被保险人")
	private String maininsuredind;
	/** 对应字段：SURNAME,备注：姓氏 */
	@ApiModelProperty("姓氏")
	private String surname;
	/** 对应字段：MONIKER,备注：名字 */
	@ApiModelProperty("名字")
	private String moniker;
	/** 对应字段：FIRSTNAME,备注：FirstName英文名 */
	@ApiModelProperty("FirstName英文名")
	private String firstname;
	/** 对应字段：LASTNAME,备注：LastName英文姓氏 */
	@ApiModelProperty("LastName英文姓氏")
	private String lastname;
	/** 对应字段：PROVINCECODE,备注：联系地址（省级） */
	@ApiModelProperty("联系地址（省级）")
	private String provincecode;
	/** 对应字段：CITYCODE,备注：联系地址（地级） */
	@ApiModelProperty("联系地址（地级）")
	private String citycode;
	/** 对应字段：COUNTYCODE,备注：联系地址区（县级） */
	@ApiModelProperty("联系地址区（县级）")
	private String countycode;
	/** 对应字段：DISPLAYNO,备注：序号 */
	@ApiModelProperty("序号")
	private BigDecimal displayNo;
	/** 对应字段：UPDATESYSDATE,备注：系统更新时间 */
	@ApiModelProperty("系统更新时间")
	private Date updatesysdate;
	/** 对应字段：ENDORFLAG,备注：批改标志 */
	@ApiModelProperty("批改标志")
	private String endorflag;
	/** 对应字段：GROUPTYPE,备注：分组组别 */
	@ApiModelProperty("分组组别")
	private String groupType;
	/** 对应字段：SIGNATURE,备注：被保险人签字 */
	@ApiModelProperty("被保险人签字")
	private String signature;
	/** 对应字段：ITEMADDRESS */
	@ApiModelProperty()
	private String itemAddress;
	/** 对应字段：USENATURECODE,备注：使用性质,来源ggcode.codetype=UseNatureCode */
	@ApiModelProperty("使用性质,来源ggcode.codetype=UseNatureCode")
	private String useNatureCode;
	/** 对应字段：DRIVERTYPE,备注：驾驶人类别,来源于ggcode.codetype=DriverType */
	@ApiModelProperty("驾驶人类别,来源于ggcode.codetype=DriverType")
	private String drivertype;
	/** 对应字段：DRIVERLICENSENOOLD */
	@ApiModelProperty()
	private BigDecimal driverlicensenoold;
	/** 对应字段：DRIVERLICENSEEXPIRATIONDATE,备注：驾照有效期 */
	@ApiModelProperty("驾照有效期")
	private Date driverlicenseexpirationdate;
	/** 对应字段：BENEFITPROJECT,备注：其他福利计划/医疗福利计划,来源ggcode.codetype=BenefitProject */
	@ApiModelProperty("其他福利计划/医疗福利计划,来源ggcode.codetype=BenefitProject")
	private String benefitproject;
	/** 对应字段：SOCIALSECURITYINFO */
	@ApiModelProperty()
	private String socialsecurityinfo;
	/** 对应字段：DRIVINGMODEL */
	@ApiModelProperty()
	private String drivingmodel;
	/** 对应字段：DRIVERLICENSENO,备注：机动车驾驶执照号 */
	@ApiModelProperty("机动车驾驶执照号")
	private String driverlicenseno;
	/** 对应字段：ONTHEJOBSTATUS */
	@ApiModelProperty()
	private String onthejobstatus;
	/** 对应字段：PLATCUSTOMNO,备注：意健险平台客户代码 */
	@ApiModelProperty("意健险平台客户代码")
	private String platcustomno;
	/** 对应字段：BEIFENDESCRIPTION,备注：北分描述 */
	@ApiModelProperty("北分描述")
	private String beifendescription;
	/** 对应字段：INSUREDNAME,备注：投保人姓名 */
	@ApiModelProperty("投保人姓名")
	private String insuredName;
	/** 对应字段：INSUREDSEX,备注：投保人性别 */
	@ApiModelProperty("投保人性别")
	private String insuredsex;
	/** 对应字段：INSUREDBIRTHDAY,备注：投保人出生日期 */
	@ApiModelProperty("投保人出生日期")
	private Date insuredbirthday;
	/** 对应字段：IDENTIFYTYPEC,备注：证件类型C */
	@ApiModelProperty("证件类型C")
	private String identifytypec;
	/** 对应字段：IDENTIFYNOC,备注：证件号码C */
	@ApiModelProperty("证件号码C")
	private String identifynoc;
	/** 对应字段：INSUREDHOMEADDRESS,备注：投保人地址 */
	@ApiModelProperty("投保人地址")
	private String insuredhomeaddress;
	/** 对应字段：INSUREDHOMETEL,备注：投保人手机号 */
	@ApiModelProperty("投保人手机号")
	private String insuredhometel;
	/** 对应字段：INSUREDPHONE,备注：投保人与其他联系人电话 */
	@ApiModelProperty("投保人与其他联系人电话")
	private String insuredPhone;
	/** 对应字段：INSUREDEMAIL,备注：投保人电子邮箱 */
	@ApiModelProperty("投保人电子邮箱")
	private String insuredemail;
	/** 对应字段：INSUREDCLASS,备注：被保人班级 */
	@ApiModelProperty("被保人班级")
	private String insuredclass;
	/** 对应字段：SCHOOL,备注：被保人学校 */
	@ApiModelProperty("被保人学校")
	private String school;
	/** 对应字段：JOINTINSUREDFLAG */
	@ApiModelProperty()
	private String jointinsuredflag;
	/** 对应字段：VACCINATION,备注：接种疫苗 */
	@ApiModelProperty("接种疫苗")
	private String vaccination;
	/** 对应字段：INOCULABILITYTIME,备注：接种时间 */
	@ApiModelProperty("接种时间")
	private Date inoculabilitytime;
	/** 对应字段：PORTABLETYPE,备注：便携式电子产品类型 */
	@ApiModelProperty("便携式电子产品类型")
	private String portabletype;
	/** 对应字段：BRAND,备注：品牌 */
	@ApiModelProperty("品牌")
	private String brand;
	/** 对应字段：MODEL,备注：型号 */
	@ApiModelProperty("型号")
	private String model;
	/** 对应字段：UNIQUEENCODING,备注：唯一编码 */
	@ApiModelProperty("唯一编码")
	private String uniqueencoding;
	/** 对应字段：UPLOADTYPE,备注：清单导入类型 */
	@ApiModelProperty("清单导入类型")
	private String uploadtype;
}
