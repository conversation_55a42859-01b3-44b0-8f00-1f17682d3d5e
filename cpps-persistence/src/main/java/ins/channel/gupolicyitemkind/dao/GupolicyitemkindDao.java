package ins.channel.gupolicyitemkind.dao;

import ins.channel.gupolicymain.po.Gupolicymain;
import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicyitemkind.po.Gupolicyitemkind;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYITEMKIND对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicyitemkindDao extends MybatisBaseDao<Gupolicyitemkind, String> {
    int batchUpdate(List<Gupolicyitemkind> list);

    int batchUpdateSelective(List<Gupolicyitemkind> list);

    int batchInsert(List<Gupolicyitemkind> list);

    List<Gupolicyitemkind> selectByCondition(Gupolicyitemkind itemkindCondition);

    void deleteByPolicyNo(String policyNo);
}
