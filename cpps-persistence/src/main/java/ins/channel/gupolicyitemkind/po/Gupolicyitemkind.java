package ins.channel.gupolicyitemkind.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GUPOLICYITEMKIND的PO对象<br/>
 * 对应表名：GUPOLICYITEMKIND,备注：最新保单计划险别表
 *
 */
@Data
@Table(name = "GUPOLICYITEMKIND")
public class Gupolicyitemkind implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@Column(name = "ID", description = "主键")
	private String id;
	/** 对应字段：POLICYNO,备注：保单号 */
	@Column(name = "POLICYNO", description = "保单号")
	private String policyNo;
	/** 对应字段：SUBPOLICYNO,备注：分单号 */
	@Column(name = "SUBPOLICYNO", description = "分单号")
	private String subpolicyno;
	/** 对应字段：PLANCODE,备注：计划代码 */
	@Column(name = "PLANCODE", description = "计划代码")
	private String plancode;
	/** 对应字段：RISKCODE,备注：险种代码 */
	@Column(name = "RISKCODE", description = "险种代码")
	private String riskCode;
	/** 对应字段：ITEMKINDNO,备注：险别序号 */
	@Column(name = "ITEMKINDNO", description = "险别序号")
	private BigDecimal itemKindNo;
	/** 对应字段：ITEMNO,备注：标的序号； */
	@Column(name = "ITEMNO", description = "标的序号；")
	private BigDecimal itemNo;
	/** 对应字段：ITEMCODE,备注：标的代码； */
	@Column(name = "ITEMCODE", description = "标的代码；")
	private String itemCode;
	/** 对应字段：ITEMDETAILNO,备注：如果是标的物的险别，记录险别对应的标的物序号，否则为0 ； */
	@Column(name = "ITEMDETAILNO", description = "如果是标的物的险别，记录险别对应的标的物序号，否则为0 ；")
	private BigDecimal itemdetailno;
	/** 对应字段：ITEMDETAILCODE,备注：如果是标的物的险别，记录险别对应的标的物代码；GgRiskItemDetail.ItemDetailCode */
	@Column(name = "ITEMDETAILCODE", description = "如果是标的物的险别，记录险别对应的标的物代码；GgRiskItemDetail.ItemDetailCode")
	private String itemdetailcode;
	/** 对应字段：ITEMDETAILLIST,备注：如果是标的物的险别，记录险别对应的标的物的明细信息；GGItemDetail.ItemDetailCName */
	@Column(name = "ITEMDETAILLIST", description = "如果是标的物的险别，记录险别对应的标的物的明细信息；GGItemDetail.ItemDetailCName")
	private String itemdetaillist;
	/** 对应字段：PLANID,备注：计划编码 */
	@Column(name = "PLANID", description = "计划编码")
	private String planid;
	/** 对应字段：KINDIND,备注：1：主险标志 2：附加险标志 */
	@Column(name = "KINDIND", description = "1：主险标志 2：附加险标志")
	private String kindind;
	/** 对应字段：KINDCODE,备注：险别代码 */
	@Column(name = "KINDCODE", description = "险别代码")
	private String kindCode;
	/** 对应字段：KINDNAME,备注：险别名称 */
	@Column(name = "KINDNAME", description = "险别名称")
	private String kindName;
	/** 对应字段：MODECODE,备注：投保方式代码 */
	@Column(name = "MODECODE", description = "投保方式代码")
	private String modeCode;
	/** 对应字段：MODENAME,备注：投保方式名称 */
	@Column(name = "MODENAME", description = "投保方式名称")
	private String modeName;
	/** 对应字段：STARTDATE,备注：险别的起保日期 */
	@Column(name = "STARTDATE", description = "险别的起保日期")
	private Date startDate;
	/** 对应字段：ENDDATE,备注：险别的终保日期 */
	@Column(name = "ENDDATE", description = "险别的终保日期")
	private Date endDate;
	/** 对应字段：CALCULATEIND,备注：是否计算保额标志,代码对应数据表GgKind.CalculateInd； */
	@Column(name = "CALCULATEIND", description = "是否计算保额标志,代码对应数据表GgKind.CalculateInd；")
	private String calculateind;
	/** 对应字段：CURRENCY,备注：原币币别 */
	@Column(name = "CURRENCY", description = "原币币别")
	private String currency;
	/** 对应字段：UNIT,备注：单位 */
	@Column(name = "UNIT", description = "单位")
	private String unit;
	/** 对应字段：SUMVALUE,备注：保险价值 */
	@Column(name = "SUMVALUE", description = "保险价值")
	private BigDecimal sumValue;
	/** 对应字段：SUMINSURED,备注：险别对应的保额； */
	@Column(name = "SUMINSURED", description = "险别对应的保额；")
	private BigDecimal suminsured;
	/** 对应字段：RATEPERIOD,备注：适应费率期数 */
	@Column(name = "RATEPERIOD", description = "适应费率期数")
	private BigDecimal ratePeriod;
	/** 对应字段：RATE,备注：费率 */
	@Column(name = "RATE", description = "费率")
	private BigDecimal rate;
	/** 对应字段：SHORTRATEFLAG,备注：短期费率标志 1-日比例 2-月比例 3-短期费率表 4-不计 5-趸交保费系数表  代码对应数据表 GgCode.CodeType=ShortRateFlag */
	@Column(name = "SHORTRATEFLAG", description = "短期费率标志 1-日比例 2-月比例 3-短期费率表 4-不计 5-趸交保费系数表  代码对应数据表 GgCode.CodeType=ShortRateFlag")
	private String shortrateFlag;
	/** 对应字段：SHORTRATE,备注：短期费率 */
	@Column(name = "SHORTRATE", description = "短期费率")
	private BigDecimal shortRate;
	/** 对应字段：SHORTRATEDENOMINATOR,备注：短期费率分母 */
	@Column(name = "SHORTRATEDENOMINATOR", description = "短期费率分母")
	private BigDecimal shortratedenominator;
	/** 对应字段：LOADING,备注：加减费 */
	@Column(name = "LOADING", description = "加减费")
	private BigDecimal loading;
	/** 对应字段：GROSSPREMIUM,备注：险别的毛保费； */
	@Column(name = "GROSSPREMIUM", description = "险别的毛保费；")
	private BigDecimal grosspremium;
	/** 对应字段：NOTAXPREMIUM,备注：营改增-不含税保费 */
	@Column(name = "NOTAXPREMIUM", description = "营改增-不含税保费")
	private BigDecimal notaxpremium;
	/** 对应字段：TAXAMOUNT,备注：营改增-税额 */
	@Column(name = "TAXAMOUNT", description = "营改增-税额")
	private BigDecimal taxamount;
	/** 对应字段：NETPREMIUM,备注：险别净保费 */
	@Column(name = "NETPREMIUM", description = "险别净保费")
	private BigDecimal netPremium;
	/** 对应字段：DEDUCTIBLERATE,备注：免赔费率 */
	@Column(name = "DEDUCTIBLERATE", description = "免赔费率")
	private BigDecimal deductibleRate;
	/** 对应字段：DEDUCTIBLE,备注：免赔额 */
	@Column(name = "DEDUCTIBLE", description = "免赔额")
	private BigDecimal deductible;
	/** 对应字段：YEARPREMIUM,备注：年保费 */
	@Column(name = "YEARPREMIUM", description = "年保费")
	private BigDecimal yearpremium;
	/** 对应字段：SURRENDERIND,备注：险别是否有效 0-有效 */
	@Column(name = "SURRENDERIND", description = "险别是否有效 0-有效")
	private String surrenderind;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 U I D */
	@Column(name = "FLAG", description = "标志字段 U I D")
	private String flag;
	/** 对应字段：LIABCODE,备注：意健险作“责任代码”使用 */
	@Column(name = "LIABCODE", description = "意健险作“责任代码”使用")
	private String liabCode;
	/** 对应字段：UNITPREMIUM,备注：意健险作“每人保保费”使用 */
	@Column(name = "UNITPREMIUM", description = "意健险作“每人保保费”使用")
	private BigDecimal unitPremium;
	/** 对应字段：DISCOUNT,备注：意健险作“折扣率”使用 */
	@Column(name = "DISCOUNT", description = "意健险作“折扣率”使用")
	private BigDecimal discount;
	/** 对应字段：QUANTITY,备注：意健险作“人数”使用 */
	@Column(name = "QUANTITY", description = "意健险作“人数”使用")
	private BigDecimal quantity;
	/** 对应字段：UWCOUNT,备注：意健险作“投保份数”使用 */
	@Column(name = "UWCOUNT", description = "意健险作“投保份数”使用")
	private BigDecimal uwcount;
	/** 对应字段：UWPREMIUM,备注：承保保费 */
	@Column(name = "UWPREMIUM", description = "承保保费")
	private BigDecimal uwpremium;
	/** 对应字段：ORIGINUWPREMIUM,备注：改前承保保费 */
	@Column(name = "ORIGINUWPREMIUM", description = "改前承保保费")
	private BigDecimal originuwpremium;
	/** 对应字段：ORIGINGROSSPREMIUM,备注：改前应收保费 */
	@Column(name = "ORIGINGROSSPREMIUM", description = "改前应收保费")
	private BigDecimal origingrosspremium;
	/** 对应字段：COMMISSION,备注：手续费金额 */
	@Column(name = "COMMISSION", description = "手续费金额")
	private BigDecimal commission;
	/** 对应字段：AGENTRATE,备注：手续费比例（含税） */
	@Column(name = "AGENTRATE", description = "手续费比例（含税）")
	private BigDecimal agentrate;
	/** 对应字段：PUBINSUREDIND,备注：公共保额标志,0:否;1:是 */
	@Column(name = "PUBINSUREDIND", description = "公共保额标志,0:否;1:是")
	private String pubinsuredind;
	/** 对应字段：SPECIALIND,备注：0-普通险别 1-特殊险别 */
	@Column(name = "SPECIALIND", description = "0-普通险别 1-特殊险别")
	private String specialind;
	/** 对应字段：MUSTINSUREIND,备注：是否必保标识：1－是 0－否 */
	@Column(name = "MUSTINSUREIND", description = "是否必保标识：1－是 0－否")
	private String mustinsureind;
	/** 对应字段：OURAMOUNT,备注：我司保额 */
	@Column(name = "OURAMOUNT", description = "我司保额")
	private BigDecimal ouramount;
	/** 对应字段：OURPREMIUM,备注：我司含税保费 */
	@Column(name = "OURPREMIUM", description = "我司含税保费")
	private BigDecimal ourpremium;
	/** 对应字段：OURNOTTAXPREMIUM,备注：我司不含税保费 */
	@Column(name = "OURNOTTAXPREMIUM", description = "我司不含税保费")
	private BigDecimal ournottaxpremium;
	/** 对应字段：OURTAXAMOUNT,备注：我司税额 */
	@Column(name = "OURTAXAMOUNT", description = "我司税额")
	private BigDecimal ourtaxamount;
	/** 对应字段：CURRENCYCNY,备注：本位币币别 */
	@Column(name = "CURRENCYCNY", description = "本位币币别")
	private String currencycny;
	/** 对应字段：SUMINSUREDCNY,备注：总保额本位币金额 */
	@Column(name = "SUMINSUREDCNY", description = "总保额本位币金额")
	private BigDecimal suminsuredcny;
	/** 对应字段：UWPREMIUMCNY,备注：总签单保费本位币金额 */
	@Column(name = "UWPREMIUMCNY", description = "总签单保费本位币金额")
	private BigDecimal uwpremiumcny;
	/** 对应字段：NOTAXPREMIUMCNY,备注：不含税保费本位币金额 */
	@Column(name = "NOTAXPREMIUMCNY", description = "不含税保费本位币金额")
	private BigDecimal notaxpremiumcny;
	/** 对应字段：TAXAMOUNTCNY,备注：税额本位币金额 */
	@Column(name = "TAXAMOUNTCNY", description = "税额本位币金额")
	private BigDecimal taxamountcny;
	/** 对应字段：OURAMOUNTCNY,备注：我司保额本位币金额 */
	@Column(name = "OURAMOUNTCNY", description = "我司保额本位币金额")
	private BigDecimal ouramountcny;
	/** 对应字段：OURPREMIUMCNY,备注：我司含税保费本位币金额 */
	@Column(name = "OURPREMIUMCNY", description = "我司含税保费本位币金额")
	private BigDecimal ourpremiumcny;
	/** 对应字段：OURNOTTAXPREMIUMCNY,备注：我司不含税保费本位币金额 */
	@Column(name = "OURNOTTAXPREMIUMCNY", description = "我司不含税保费本位币金额")
	private BigDecimal ournottaxpremiumcny;
	/** 对应字段：OURTAXAMOUNTCNY,备注：我司税额本位币金额 */
	@Column(name = "OURTAXAMOUNTCNY", description = "我司税额本位币金额")
	private BigDecimal ourtaxamountcny;
	/** 对应字段：CLAIMCOUNTLIMIT,备注：理赔次数限制 */
	@Column(name = "CLAIMCOUNTLIMIT", description = "理赔次数限制")
	private String claimcountlimit;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@Column(name = "INPUTDATE", description = "入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@Column(name = "UPDATESYSDATE", description = "修改日期")
	private Date updatesysdate;
}
