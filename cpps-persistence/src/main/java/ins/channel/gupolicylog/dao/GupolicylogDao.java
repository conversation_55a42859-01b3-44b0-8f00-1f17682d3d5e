package ins.channel.gupolicylog.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicylog.po.Gupolicylog;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYLOG对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicylogDao extends MybatisBaseDao<Gupolicylog, String> {

    /**
     * @description: 根据条件,按照请求时间倒序排列,取前10条日志记录
     * @param: policylogCondition
     * @author: zhoutaoyu
     * @date: 2021/3/12
     * @return:
     **/
    List<Gupolicylog> selectByCondition(Gupolicylog policylogCondition);
}
