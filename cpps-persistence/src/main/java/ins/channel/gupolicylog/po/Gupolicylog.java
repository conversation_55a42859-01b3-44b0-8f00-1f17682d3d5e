package ins.channel.gupolicylog.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GUPOLICYLOG的PO对象<br/>
 * 对应表名：GUPOLICYLOG,备注：任务处理存储日志表
 *
 */
@Data
@Table(name = "GUPOLICYLOG")
public class Gupolicylog implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@Column(name = "ID", description = "主键")
	private String id;
	/** 对应字段：REQUESTCONTENT,备注：请求内容 */
	@Column(name = "REQUESTCONTENT", description = "请求内容")
	private String requestcontent;
	/** 对应字段：RESPONSECONTENT,备注：响应内容 */
	@Column(name = "RESPONSECONTENT", description = "响应内容")
	private String responsecontent;
	/** 对应字段：RESPONSESTATUS,备注：响应状态码：0- 成功, 1- 失败 */
	@Column(name = "RESPONSESTATUS", description = "响应状态码：0- 成功, 1- 失败")
	private String responsestatus;
	/** 对应字段：REQUESETDATE,备注：请求日期 */
	@Column(name = "REQUESETDATE", description = "请求日期")
	private Date requesetdate;
	/** 对应字段：OPERATORCODE,备注：操作人员代码 */
	@Column(name = "OPERATORCODE", description = "操作人员代码")
	private String operatorcode;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@Column(name = "FLAG", description = "标志字段")
	private String flag;
}
