package ins.channel.gupolicylog.vo;

import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GupolicylogVo对象.对应实体描述：任务处理存储日志表
 *
 */
@Data
@ApiModel("GupolicylogVo对象")
public class GupolicylogVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@ApiModelProperty("主键")
	private String id;
	/** 对应字段：REQUESTCONTENT,备注：请求内容 */
	@ApiModelProperty("请求内容")
	private String requestcontent;
	/** 对应字段：RESPONSECONTENT,备注：响应内容 */
	@ApiModelProperty("响应内容")
	private String responsecontent;
	/** 对应字段：RESPONSESTATUS,备注：响应状态码：0- 成功, 1- 失败 */
	@ApiModelProperty("响应状态码：0- 成功, 1- 失败")
	private String responsestatus;
	/** 对应字段：REQUESETDATE,备注：请求日期 */
	@ApiModelProperty("请求日期")
	private Date requesetdate;
	/** 对应字段：OPERATORCODE,备注：操作人员代码 */
	@ApiModelProperty("操作人员代码")
	private String operatorcode;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
}
