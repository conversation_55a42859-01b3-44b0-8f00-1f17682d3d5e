package ins.channel.gupolicymain.dao;

import ins.channel.gupolicycopymain.vo.DeclarationReqVo;
import ins.channel.gupolicycopymain.vo.DeclarationRespVo;
import ins.channel.gupolicymain.vo.PolicyMainReqVo;
import ins.channel.gupolicymain.vo.PolicyMainRespVo;
import ins.channel.gupolicymain.vo.PolicyVoucherInfoVo;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicymain.po.Gupolicymain;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYMAIN对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicymainDao extends MybatisBaseDao<Gupolicymain, String> {

    List<Gupolicymain> selectByCondition(Gupolicymain gupolicyMain);

    //取消保单授权
    Integer invalidMainCompany (Gupolicymain gupolicymain);

    List<String> selectAllPolicyNo();

    //查询待更新保单号是否已存在
    Integer selectCountByPolicyNo(String policyno);

    //批量更新全部字段
    int batchUpdate(List<Gupolicymain> list);

    //批量更新非空字段
    int batchUpdateSelective(List<Gupolicymain> list);

    //批量插入
    int batchInsert(List<Gupolicymain> list);

    Page<PolicyMainRespVo> pageQueryPolicyByCondition(PageParam pageParam, PolicyMainReqVo vo);

    Page<PolicyMainRespVo> queryPolicyByPage(PageParam pageParam, PolicyMainReqVo vo);

    List<PolicyMainRespVo> selectForDeclarationAdd(PolicyMainReqVo vo);

    PolicyVoucherInfoVo queryPolicyVoucherInfo(String policyNo);

    //修改结算金额
    int UpdateSettlefee(Gupolicymain gupolicymain);

    //申报查询默认保单号
    List<DeclarationRespVo> searchPolicyNo(DeclarationReqVo vo);

    void deleteByPolicyNo(String policyNo);
}
