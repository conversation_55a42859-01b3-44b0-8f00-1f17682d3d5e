package ins.channel.gupolicymain.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GUPOLICYMAIN的PO对象<br/>
 * 对应表名：GUPOLICYMAIN,备注：最新保单主信息表
 *
 */
@Data
@Table(name = "GUPOLICYMAIN")
public class Gupolicymain implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键（创新业务标识3+险种4+年月日8+15位序列） */
	@Column(name = "ID", description = "主键（创新业务标识3+险种4+年月日8+15位序列）")
	private String id;
	/** 对应字段：PROPOSALNO,备注：投保单号码 */
	@Column(name = "PROPOSALNO", description = "投保单号码")
	private String proposalNo;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@Column(name = "POLICYNO", description = "保单号码")
	private String policyNo;
	/** 对应字段：LANGUAGE,备注：保单语种 */
	@Column(name = "LANGUAGE", description = "保单语种")
	private String language;
	/** 对应字段：PRODUCTCODE,备注：产品代码 */
	@Column(name = "PRODUCTCODE", description = "产品代码")
	private String productcode;
	/** 对应字段：GROUPIND,备注：团体标志 1-个单 2-团单 */
	@Column(name = "GROUPIND", description = "团体标志 1-个单 2-团单")
	private String groupind;
	/** 对应字段：INSURANCECOMPANYCODE,备注：保险公司归属机构 */
	@Column(name = "INSURANCECOMPANYCODE", description = "保险公司归属机构")
	private String insurancecompanycode;
	/** 对应字段：SURVEYIND,备注：创新业务标识 */
	@Column(name = "SURVEYIND", description = "创新业务标识")
	private String surveyind;
	/** 对应字段：FLOWID,备注：产品方案代码 */
	@Column(name = "FLOWID", description = "产品方案代码")
	private String flowid;
	/** 对应字段：COMPANYCODE,备注：关联机构代码 */
	@Column(name = "COMPANYCODE", description = "关联机构代码")
	private String companycode;
	/** 对应字段：COMPANYNAME,备注：关联机构名称 */
	@Column(name = "COMPANYNAME", description = "关联机构名称")
	private String companyname;
	/** 对应字段：PROJECTMANAGERCODE,备注：PM代码 */
	@Column(name = "PROJECTMANAGERCODE", description = "PM代码")
	private String projectmanagercode;
	/** 对应字段：PROJECTMANAGERNAME,备注：PM名称 */
	@Column(name = "PROJECTMANAGERNAME", description = "PM名称")
	private String projectmanagername;
	/** 对应字段：STARTDATE,备注：起保日期 */
	@Column(name = "STARTDATE", description = "起保日期")
	private Date startDate;
	/** 对应字段：ENDDATE,备注：终保日期 */
	@Column(name = "ENDDATE", description = "终保日期")
	private Date endDate;
	/** 对应字段：APPLICODE,备注：投保人代码 */
	@Column(name = "APPLICODE", description = "投保人代码")
	private String appliCode;
	/** 对应字段：APPLINAME,备注：投保人名称 */
	@Column(name = "APPLINAME", description = "投保人名称")
	private String appliName;
	/** 对应字段：INSUREDCODE,备注：被保险人代码 */
	@Column(name = "INSUREDCODE", description = "被保险人代码")
	private String insuredCode;
	/** 对应字段：INSUREDNAME,备注：被保险人名称 */
	@Column(name = "INSUREDNAME", description = "被保险人名称")
	private String insuredName;
	/** 对应字段：CURRENCY,备注：保单币别 */
	@Column(name = "CURRENCY", description = "保单币别")
	private String currency;
	/** 对应字段：SUMINSURED,备注：总保额 */
	@Column(name = "SUMINSURED", description = "总保额")
	private BigDecimal suminsured;
	/** 对应字段：SUMGROSSPREMIUM,备注：总毛保费 */
	@Column(name = "SUMGROSSPREMIUM", description = "总毛保费")
	private BigDecimal sumgrosspremium;
	/** 对应字段：SUMNETPREMIUM,备注：总净保费 */
	@Column(name = "SUMNETPREMIUM", description = "总净保费")
	private BigDecimal sumnetpremium;
	/** 对应字段：YEARPREMIUM,备注：年保费 */
	@Column(name = "YEARPREMIUM", description = "年保费")
	private BigDecimal yearpremium;
	/** 对应字段：SUMUWPREMIUM,备注：总承保保费 */
	@Column(name = "SUMUWPREMIUM", description = "总承保保费")
	private BigDecimal sumuwpremium;
	/** 对应字段：NOTAXPREMIUM,备注：总净保费 */
	@Column(name = "NOTAXPREMIUM", description = "总净保费")
	private BigDecimal notaxpremium;
	/** 对应字段：TAXAMOUNT,备注：总税额 */
	@Column(name = "TAXAMOUNT", description = "总税额")
	private BigDecimal taxamount;
	/** 对应字段：CURRENCYCNY,备注：币种 */
	@Column(name = "CURRENCYCNY", description = "币种")
	private String currencycny;
	/** 对应字段：SUMINSUREDCNY,备注：总保额 */
	@Column(name = "SUMINSUREDCNY", description = "总保额")
	private BigDecimal suminsuredcny;
	/** 对应字段：SUMGROSSPREMIUMCNY,备注：总毛保费 */
	@Column(name = "SUMGROSSPREMIUMCNY", description = "总毛保费")
	private BigDecimal sumgrosspremiumcny;
	/** 对应字段：SUMUWPREMIUMCNY,备注：总承保保费 */
	@Column(name = "SUMUWPREMIUMCNY", description = "总承保保费")
	private BigDecimal sumuwpremiumcny;
	/** 对应字段：NOTAXPREMIUMCNY,备注：净保费 */
	@Column(name = "NOTAXPREMIUMCNY", description = "净保费")
	private BigDecimal notaxpremiumcny;
	/** 对应字段：TAXAMOUNTCNY,备注：总税额 */
	@Column(name = "TAXAMOUNTCNY", description = "总税额")
	private BigDecimal taxamountcny;
	/** 对应字段：UWYEAR,备注：承保年度 */
	@Column(name = "UWYEAR", description = "承保年度")
	private String uwYear;
	/** 对应字段：ACCEPTDATE,备注：承保确认时间 */
	@Column(name = "ACCEPTDATE", description = "承保确认时间")
	private Date acceptdate;
	/** 对应字段：UNDERWRITEIND,备注：核保标志 */
	@Column(name = "UNDERWRITEIND", description = "核保标志")
	private String underwriteind;
	/** 对应字段：UNDERWRITEENDDATE,备注：核保完成日期 */
	@Column(name = "UNDERWRITEENDDATE", description = "核保完成日期")
	private Date underWriteEndDate;
	/** 对应字段：SURRENDERIND,备注：退保标志 */
	@Column(name = "SURRENDERIND", description = "退保标志")
	private String surrenderind;
	/** 对应字段：CANCELIND,备注：注销标志 */
	@Column(name = "CANCELIND", description = "注销标志")
	private String cancelind;
	/** 对应字段：ENDIND,备注：保险合同终止标志 */
	@Column(name = "ENDIND", description = "保险合同终止标志")
	private String endind;
	/** 对应字段：CODIND,备注：见费出单COD标志 */
	@Column(name = "CODIND", description = "见费出单COD标志")
	private String codind;
	/** 对应字段：CALCULATETYPE,备注：保费计算方式 */
	@Column(name = "CALCULATETYPE", description = "保费计算方式")
	private String calculatetype;
	/** 对应字段：COINSIND,备注：联共保标识 */
	@Column(name = "COINSIND", description = "联共保标识")
	private String coinsind;
	/** 对应字段：AGENTRATE,备注：手续费比例（含税） */
	@Column(name = "AGENTRATE", description = "手续费比例（含税）")
	private BigDecimal agentrate;
	/** 对应字段：NOTAXAGENTRATE,备注：手续费比例（不含税） */
	@Column(name = "NOTAXAGENTRATE", description = "手续费比例（不含税）")
	private BigDecimal notaxagentrate;
	/** 对应字段：COMMISSION,备注：手续费金额 */
	@Column(name = "COMMISSION", description = "手续费金额")
	private BigDecimal commission;
	/** 对应字段：FSH,备注：FSH%值 */
	@Column(name = "FSH", description = "FSH%值")
	private BigDecimal fsh;
	/** 对应字段：XSF,备注：XSF%调整系数值 */
	@Column(name = "XSF", description = "XSF%调整系数值")
	private BigDecimal xsf;
	/** 对应字段：XSFIND,备注：XSF等级  */
	@Column(name = "XSFIND", description = "XSF等级 ")
	private String xsfind;
	/** 对应字段：INSTALLMENTNO,备注：约定分期交费次数 */
	@Column(name = "INSTALLMENTNO", description = "约定分期交费次数")
	private BigDecimal installmentno;
	/** 对应字段：ENDORSETIMES,备注：批改次数 */
	@Column(name = "ENDORSETIMES", description = "批改次数")
	private BigDecimal endorseTimes;
	/** 对应字段：RENEWEDTIME,备注：续保次数 */
	@Column(name = "RENEWEDTIME", description = "续保次数")
	private BigDecimal renewedtime;
	/** 对应字段：REGISTTIMES,备注：报案次数 */
	@Column(name = "REGISTTIMES", description = "报案次数")
	private BigDecimal registTimes;
	/** 对应字段：CLAIMSTIMES,备注：理赔次数 */
	@Column(name = "CLAIMSTIMES", description = "理赔次数")
	private BigDecimal claimstimes;
	/** 对应字段：PRINTTIMES,备注：打印次数 */
	@Column(name = "PRINTTIMES", description = "打印次数")
	private BigDecimal printtimes;
	/** 对应字段：ISSENDSMS,备注：是否发送短信通知 */
	@Column(name = "ISSENDSMS", description = "是否发送短信通知")
	private String issendsms;
	/** 对应字段：ISSENDEMAIL,备注：是否发送邮件 */
	@Column(name = "ISSENDEMAIL", description = "是否发送邮件")
	private String issendemail;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：VALIDIND,备注：有效标志 0-无效 1-有效 */
	@Column(name = "VALIDIND", description = "有效标志 0-无效 1-有效")
	private String validind;
	/** 对应字段：FLAG,备注：标志字段 */
	@Column(name = "FLAG", description = "标志字段")
	private String flag;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@Column(name = "INPUTDATE", description = "入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@Column(name = "UPDATESYSDATE", description = "修改日期")
	private Date updatesysdate;
	/** 对应字段：SETTLEFEE,备注：已结算金额 */
	@Column(name = "SETTLEFEE", description = "已结算金额")
	private BigDecimal settlefee;
}
