package ins.channel.gupolicymain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 保单信息查询条件载体
 * <AUTHOR>
 * @date 2021年02月07日 17:39:22
 */
@Data
@ApiModel("PolicyMainReqVo")
public class PolicyMainReqVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：APPLICODE,备注：投保人代码-投保公司code */
	@ApiModelProperty("投保人代码-投保公司code")
	private String appliCode;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;
	/** 对应字段：COMPANYCODE,备注：关联机构代码 */
	@ApiModelProperty("关联机构代码")
	private String companyCode;
	/** 对应字段：EFFECTIVEDATE,备注：生效日期查询区间开始 */
	@ApiModelProperty("生效日期查询区间开始")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date effectiveDateStart;
	/** 对应字段：EFFECTIVEDATE,备注：生效日期查询区间结束 */
	@ApiModelProperty("生效日期查询区间结束")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date effectiveDateEnd;
	//申报页面查询入参 zhoutaoyu begin=======
	/** 对应字段：PRODUCTCODE,备注：产品代码 */
	@ApiModelProperty("保险险种")
	private String productcode;
	/** 对应字段：STARTDATE,备注：起保日期 */
	@ApiModelProperty("起保日期起期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date startDateStart;
	/** 对应字段：STARTDATE,备注：起保日期 */
	@ApiModelProperty("起保日期止期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date startDateEnd;
	/** 对应字段：ENDDATE,备注：终保日期 */
	@ApiModelProperty("终保日期起期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date endDateStrat;
	/** 对应字段：ENDDATE,备注：终保日期 */
	@ApiModelProperty("终保日期止期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date endDateEnd;
	//当前登录人员关联机构权限集合
	@JsonIgnore
	private Set<String> companyCodes;
	//申报页面查询入参 zhoutaoyu end=======
	/** 业务员代码 */
	@JsonIgnore
	private String projectManagerCode;

	@ApiModelProperty("所属机构")
	private String department;

	@ApiModelProperty("保险计划")
	private String itemNo;
}
