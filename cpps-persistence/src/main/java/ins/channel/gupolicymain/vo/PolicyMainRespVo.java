package ins.channel.gupolicymain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 保单信息查询结果集载体
 * <AUTHOR>
 * @date 2021年02月07日 17:39:22
 */
@Data
@ApiModel("PolicyMainRespVo")
public class PolicyMainRespVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;
	/** 对应字段：APPLINAME,备注：投保人名称-投保公司 */
	@ApiModelProperty("投保人名称-投保公司")
	private String appliName;
	/** 对应字段：INSUREDNAME,备注：被保险人姓名 */
	@ApiModelProperty("被保险人姓名")
	private String insuredname;
	/** 对应字段：COMPANYNAME,备注：关联机构名称 */
	@ApiModelProperty("关联机构名称")
	private String companyName;
	/** 对应字段：SALESMANNAME,备注：业务员 */
	@ApiModelProperty("业务员")
	private String salesManName;
	/** 对应字段：EFFECTIVEDATE,备注：生效日期 */
	@ApiModelProperty("生效日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date effectiveDate;
	/** 对应字段：ENDDATE,备注：到期日期 */
	@ApiModelProperty("到期日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date endDate;
	/** 对应字段：VALIDIND,备注：保单状态 */
	@ApiModelProperty("保单状态 0-有效 1-无效")
	private BigDecimal validind;

	//申报页面分页查询结果 zhoutaoyu begin=======
	@ApiModelProperty("计划名称")
	private String planName;
	@ApiModelProperty("计划编码")
	private String itemNo;
	/** 对应字段：STARTDATE,备注：起保日期 */
	@ApiModelProperty("起保日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date startDate;
	/**Gupolicyemployersplan表 对应字段：PERSONCASUALTIESLIMIT,备注：每人伤亡责任限额 */
	@ApiModelProperty("每人伤亡责任限额")
	private BigDecimal personcasualtieslimit;
	/**Gupolicyemployersplan表 对应字段：PERSONMEDICALLIMIT,备注：每人医疗费用责任限额 */
	@ApiModelProperty("每人医疗费用责任限额")
	private BigDecimal personmedicallimit;
	//申报页面分页查询结果 zhoutaoyu end========
	//申报页面待申报保单查询结果 zhoutaoyu begin=======
	/** 对应字段：PRODUCTCODE,备注：产品代码 */
	@ApiModelProperty("保险险种")
	private String productcode;
    /**Gupolicyemployerslist表  对应字段：OCCUPATIONNAME,备注：职业名称 */
    @ApiModelProperty("职业名称")
    private String occupationName;
	@ApiModelProperty("方案代码")
	private String schemecode;
	//申报页面待申报保单查询结果 zhoutaoyu end=======

	/** 对应字段：SURVEYIND,备注：创新业务标识 */
	@ApiModelProperty("创新业务标识")
	private String surveyind;
}
