package ins.channel.gupolicymain.vo;

import ins.framework.mybatis.annotations.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel ("PolicyVoucherVo")
public class PolicyVoucherInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty ("被保险公司")
    private String insuredCompany;
    @ApiModelProperty ("险种名称")
    private String kindName;
    @ApiModelProperty ("保单号码")
    private String policyNo;
    @ApiModelProperty ("被保险人名称")
    private String appliName;
    @ApiModelProperty ("被保险人地址")
    private String insuredAddress;
    @ApiModelProperty ("开始时间（年月日）")
    private String startDate;
    @ApiModelProperty ("开始时间（时）")
    private String startDateHour;
    @ApiModelProperty ("结束时间（年月日）")
    private String endDate;
    @ApiModelProperty ("结束时间（时）")
    private String endDateHour;
    @ApiModelProperty("保险险种")
    private String productcode;
    @ApiModelProperty("保险公司归属机构")
    private String insurancecompanycode;

}
