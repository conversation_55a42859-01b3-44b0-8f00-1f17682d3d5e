package ins.channel.gupolicymainbasic.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicymainbasic.po.Gupolicymainbasic;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYMAINBASIC对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicymainbasicDao extends MybatisBaseDao<Gupolicymainbasic, String> {
    List<Gupolicymainbasic> selectByCondition(Gupolicymainbasic gupolicyMain);

    int batchUpdate(List<Gupolicymainbasic> list);

    int batchUpdateSelective(List<Gupolicymainbasic> list);

    int batchInsert(List<Gupolicymainbasic> list);

    void deleteByPolicyNo(String policyNo);
}
