package ins.channel.gupolicymainbasic.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GUPOLICYMAINBASIC的PO对象<br/>
 * 对应表名：GUPOLICYMAINBASIC,备注：最新保单基础信息表
 *
 */
@Data
@Table(name = "GUPOLICYMAINBASIC")
public class Gupolicymainbasic implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@Column(name = "ID", description = "主键")
	private String id;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@Column(name = "POLICYNO", description = "保单号码")
	private String policyNo;
	/** 对应字段：PRODUCTCODE,备注：产品代码 */
	@Column(name = "PRODUCTCODE", description = "产品代码")
	private String productcode;
	/** 对应字段：SHAREHOLDERFLAG,备注：股东标识 */
	@Column(name = "SHAREHOLDERFLAG", description = "股东标识")
	private String shareHolderFlag;
	/** 对应字段：INSURANCECOMPANYCODE,备注：保险公司归属机构 */
	@Column(name = "INSURANCECOMPANYCODE", description = "保险公司归属机构")
	private String insurancecompanycode;
	/** 对应字段：BUSINESSCHANNEL,备注：渠道大类 */
	@Column(name = "BUSINESSCHANNEL", description = "渠道大类")
	private String businesschannel;
	/** 对应字段：CHANNELDETAILCODE,备注：渠道类型 */
	@Column(name = "CHANNELDETAILCODE", description = "渠道类型")
	private String channeldetailcode;
	/** 对应字段：CHANNELTIP,备注：渠道小类 */
	@Column(name = "CHANNELTIP", description = "渠道小类")
	private String channeltip;
	/** 对应字段：CHANNELFLAG,备注：保单客户类型 */
	@Column(name = "CHANNELFLAG", description = "保单客户类型")
	private String channelflag;
	/** 对应字段：INTERMEDIARYCODE,备注：中介代码 */
	@Column(name = "INTERMEDIARYCODE", description = "中介代码")
	private String intermediarycode;
	/** 对应字段：AGREEMENTNO,备注：中介协议号 */
	@Column(name = "AGREEMENTNO", description = "中介协议号")
	private String agreementNo;
	/** 对应字段：BUSINESSSOURCE,备注：业务来源 */
	@Column(name = "BUSINESSSOURCE", description = "业务来源")
	private String businessSource;
	/** 对应字段：BUSINESSTYPE,备注：业务类别 1-直保 2-分入 */
	@Column(name = "BUSINESSTYPE", description = "业务类别 1-直保 2-分入")
	private String businessType;
	/** 对应字段：ISSUEDATE,备注：录单/签单日期 */
	@Column(name = "ISSUEDATE", description = "录单/签单日期")
	private Date issueDate;
	/** 对应字段：ISSUECOMPANY,备注：出单机构 */
	@Column(name = "ISSUECOMPANY", description = "出单机构")
	private String issuecompany;
	/** 对应字段：ISSUEPLACE,备注：签单地点 */
	@Column(name = "ISSUEPLACE", description = "签单地点")
	private String issueplace;
	/** 对应字段：OPERATORCODE,备注：操作员代码 */
	@Column(name = "OPERATORCODE", description = "操作员代码")
	private String operatorcode;
	/** 对应字段：OPERATEDATE,备注：投保日期 */
	@Column(name = "OPERATEDATE", description = "投保日期")
	private Date operateDate;
	/** 对应字段：ARGUESOLUTION,备注：争议解决方式 */
	@Column(name = "ARGUESOLUTION", description = "争议解决方式")
	private String argueSolution;
	/** 对应字段：ARBITORYNAME,备注：仲裁委员会名称 */
	@Column(name = "ARBITORYNAME", description = "仲裁委员会名称")
	private String arbitoryname;
	/** 对应字段：DOMESTICIND,备注：境内外标志 */
	@Column(name = "DOMESTICIND", description = "境内外标志")
	private String domesticind;
	/** 对应字段：AGRICULTUREFLAG,备注：涉农标志 */
	@Column(name = "AGRICULTUREFLAG", description = "涉农标志")
	private String agricultureflag;
	/** 对应字段：MONEYSUSPICIOUSIND,备注：反洗钱可疑交易特征 */
	@Column(name = "MONEYSUSPICIOUSIND", description = "反洗钱可疑交易特征")
	private String moneysuspiciousind;
	/** 对应字段：COMINSUREIND,备注：是否统括保单 */
	@Column(name = "COMINSUREIND", description = "是否统括保单")
	private String cominsureind;
	/** 对应字段：ISDIFFERENTPLACE,备注：是否异地业务 */
	@Column(name = "ISDIFFERENTPLACE", description = "是否异地业务")
	private String isdifferentplace;
	/** 对应字段：POLICYSTYLE,备注：是否电子保单 */
	@Column(name = "POLICYSTYLE", description = "是否电子保单")
	private String policystyle;
	/** 对应字段：ISFARMING,备注：是否涉农 */
	@Column(name = "ISFARMING", description = "是否涉农")
	private String isfarming;
	/** 对应字段：RENEWIND,备注：新/续保标志 */
	@Column(name = "RENEWIND", description = "新/续保标志")
	private String renewind;
	/** 对应字段：RENEWEDIND,备注：是否续保标志 */
	@Column(name = "RENEWEDIND", description = "是否续保标志")
	private String renewedind;
	/** 对应字段：RENEWEDTIME,备注：续保次数 */
	@Column(name = "RENEWEDTIME", description = "续保次数")
	private BigDecimal renewedtime;
	/** 对应字段：RENEWALNO,备注：续保旧单号码 */
	@Column(name = "RENEWALNO", description = "续保旧单号码")
	private String renewalno;
	/** 对应字段：REPLACEDPOLICYNO,备注：续保新单号码 */
	@Column(name = "REPLACEDPOLICYNO", description = "续保新单号码")
	private String replacedpolicyno;
	/** 对应字段：AUTORENEWIND,备注：自动续保标志 */
	@Column(name = "AUTORENEWIND", description = "自动续保标志")
	private String autorenewind;
	/** 对应字段：MULTIRISKIND,备注：多险种保单标志 */
	@Column(name = "MULTIRISKIND", description = "多险种保单标志")
	private String multiriskind;
	/** 对应字段：RISKAPPLYTYPE,备注：险种适用范围 */
	@Column(name = "RISKAPPLYTYPE", description = "险种适用范围")
	private String riskapplytype;
	/** 对应字段：PRODUCTEDITION,备注：条款代码 */
	@Column(name = "PRODUCTEDITION", description = "条款代码")
	private String productedition;
	/** 对应字段：PRODUCTEDITIONNAME,备注：条款名称 */
	@Column(name = "PRODUCTEDITIONNAME", description = "条款名称")
	private String producteditionname;
	/** 对应字段：COMPENSATIONTYPE,备注：责任险保单基础 */
	@Column(name = "COMPENSATIONTYPE", description = "责任险保单基础")
	private String compensationtype;
	/** 对应字段：DISCOVERSTARTDATE,备注：追溯起始日期 */
	@Column(name = "DISCOVERSTARTDATE", description = "追溯起始日期")
	private Date discoverstartdate;
	/** 对应字段：DISCOVERENDDATE,备注：发现终止日期 */
	@Column(name = "DISCOVERENDDATE", description = "发现终止日期")
	private Date discoverenddate;
	/** 对应字段：JUDICALCODE,备注：司法管辖代码 */
	@Column(name = "JUDICALCODE", description = "司法管辖代码")
	private String judicalCode;
	/** 对应字段：GEOGRAPHICALAREA,备注：承保地区代码 */
	@Column(name = "GEOGRAPHICALAREA", description = "承保地区代码")
	private String geographicalarea;
	/** 对应字段：GEOGRAPHICALAREADESC,备注：承保地区名称 */
	@Column(name = "GEOGRAPHICALAREADESC", description = "承保地区名称")
	private String geographicalareadesc;
	/** 对应字段：UPLOADIND,备注：导入标志（riskdynamic） */
	@Column(name = "UPLOADIND", description = "导入标志（riskdynamic）")
	private String uploadind;
	/** 对应字段：NOMINATIVEIND,备注：记名标志（riskdynamic） */
	@Column(name = "NOMINATIVEIND", description = "记名标志（riskdynamic）")
	private String nominativeind;
	/** 对应字段：BUSINESSIND,备注：业务类型 */
	@Column(name = "BUSINESSIND", description = "业务类型")
	private String businessind;
	/** 对应字段：BUSINESSMODE,备注：业务方式 */
	@Column(name = "BUSINESSMODE", description = "业务方式")
	private String businessmode;
	/** 对应字段：SALESTEAMCODE,备注：业务团队代码 */
	@Column(name = "SALESTEAMCODE", description = "业务团队代码")
	private String salesteamcode;
	/** 对应字段：SALESTEAMNAME,备注：业务团队名称 */
	@Column(name = "SALESTEAMNAME", description = "业务团队名称")
	private String salesteamname;
	/** 对应字段：SALESMANCODE,备注：归属业务员代码 */
	@Column(name = "SALESMANCODE", description = "归属业务员代码")
	private String salesmancode;
	/** 对应字段：SALESMANNAME,备注：归属业务员姓名 */
	@Column(name = "SALESMANNAME", description = "归属业务员姓名")
	private String salesmanname;
	/** 对应字段：SALESMANREGISTERNO,备注：归属业务员职业证号 */
	@Column(name = "SALESMANREGISTERNO", description = "归属业务员职业证号")
	private String salesmanregisterno;
	/** 对应字段：SELLERNAME,备注：销售人员姓名 */
	@Column(name = "SELLERNAME", description = "销售人员姓名")
	private String sellername;
	/** 对应字段：SELLERREGISTERNO,备注：销售人员职业证号 */
	@Column(name = "SELLERREGISTERNO", description = "销售人员职业证号")
	private String sellerregisterno;
	/** 对应字段：INTERSALESMANREGISTERNO,备注：中介销售人员 */
	@Column(name = "INTERSALESMANREGISTERNO", description = "中介销售人员")
	private String intersalesmanregisterno;
	/** 对应字段：INTERSALESMANCODE,备注：中介销售人员职业证号 */
	@Column(name = "INTERSALESMANCODE", description = "中介销售人员职业证号")
	private String intersalesmancode;
	/** 对应字段：TEAMMANAGERCODE,备注：客户经理代码 */
	@Column(name = "TEAMMANAGERCODE", description = "客户经理代码")
	private String teammanagercode;
	/** 对应字段：TEAMMANAGERNAME,备注：客户经理名称 */
	@Column(name = "TEAMMANAGERNAME", description = "客户经理名称")
	private String teammanagername;
	/** 对应字段：UNDERWRITECODE,备注：最终核保人代码 */
	@Column(name = "UNDERWRITECODE", description = "最终核保人代码")
	private String underWriteCode;
	/** 对应字段：UNDERWRITENAME,备注：最终核保人名称 */
	@Column(name = "UNDERWRITENAME", description = "最终核保人名称")
	private String underWriteName;
	/** 对应字段：LASTMODIFIERCODE,备注：最后一次修改人员代码 */
	@Column(name = "LASTMODIFIERCODE", description = "最后一次修改人员代码")
	private String lastmodifiercode;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@Column(name = "INPUTDATE", description = "入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@Column(name = "UPDATESYSDATE", description = "修改日期")
	private Date updatesysdate;
}
