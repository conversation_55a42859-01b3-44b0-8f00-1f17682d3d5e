package ins.channel.gupolicytodolist.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GUPOLICYTODOLIST的PO对象<br/>
 * 对应表名：GUPOLICYTODOLIST,备注：待处理保单清单表
 *
 */
@Data
@Table(name = "GUPOLICYTODOLIST")
public class Gupolicytodolist implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@Column(name = "ID", description = "主键")
	private String id;
	/** 对应字段：ENDORNO,备注：申报单号(当投保时,申报单号为保单号当批改时,申报单号为批改申请号) */
	@Column(name = "ENDORNO", description = "申报单号(当投保时,申报单号为保单号当批改时,申报单号为批改申请号)")
	private String endorNo;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@Column(name = "POLICYNO", description = "保单号码")
	private String policyNo;
	/** 对应字段：DECLARATIONDATE,备注：申报日期 */
	@Column(name = "DECLARATIONDATE", description = "申报日期")
	private Date declarationdate;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@Column(name = "FLAG", description = "标志字段")
	private String flag;
}
