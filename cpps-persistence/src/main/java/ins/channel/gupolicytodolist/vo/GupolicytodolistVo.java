package ins.channel.gupolicytodolist.vo;

import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GupolicytodolistVo对象.对应实体描述：待处理保单清单表
 *
 */
@Data
@ApiModel("GupolicytodolistVo对象")
public class GupolicytodolistVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@ApiModelProperty("主键")
	private String id;
	/** 对应字段：ENDORNO,备注：申报单号(当投保时,申报单号为保单号当批改时,申报单号为批改申请号) */
	@ApiModelProperty("申报单号(当投保时,申报单号为保单号当批改时,申报单号为批改申请号)")
	private String endorNo;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;
	/** 对应字段：DECLARATIONDATE,备注：申报日期 */
	@ApiModelProperty("申报日期")
	private Date declarationdate;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
}
