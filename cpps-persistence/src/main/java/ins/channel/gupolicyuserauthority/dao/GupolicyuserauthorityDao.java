package ins.channel.gupolicyuserauthority.dao;

import ins.channel.gupolicyuserauthority.vo.PolicyuserauthorityReqVo;
import ins.channel.gupolicyuserauthority.vo.PolicyuserauthorityRespVo;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import org.apache.ibatis.annotations.Mapper;

import ins.channel.gupolicyuserauthority.po.Gupolicyuserauthority;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GUPOLICYUSERAUTHORITY对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GupolicyuserauthorityDao extends MybatisBaseDao<Gupolicyuserauthority, String> {

    Page<PolicyuserauthorityRespVo> pageByCondition(PageParam pageParam, PolicyuserauthorityReqVo vo);

    List<Gupolicyuserauthority> selectByCondition(PolicyuserauthorityReqVo vo);

}
