package ins.channel.gupolicyuserauthority.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GUPOLICYUSERAUTHORITY的PO对象<br/>
 * 对应表名：GUPOLICYUSERAUTHORITY,备注：用户保单权限表
 *
 */
@Data
@Table(name = "GUPOLICYUSERAUTHORITY")
public class Gupolicyuserauthority implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@Column(name = "ID", description = "主键")
	private String id;
	/** 对应字段：USER_CODE,备注：用户账号 */
	@Column(name = "USER_CODE", description = "用户账号")
	private String userCode;
	/** 对应字段：COMPANY_CODE,备注：关联机构代码 */
	@Column(name = "COMPANY_CODE", description = "关联机构代码")
	private String companyCode;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@Column(name = "POLICYNO", description = "保单号码")
	private String policyNo;
	/** 对应字段：RISKCODE,备注：险种代码 */
	@Column(name = "RISKCODE", description = "险种代码")
	private String riskCode;
	/** 对应字段：CHANNELCODE,备注：渠道代码 */
	@Column(name = "CHANNELCODE", description = "渠道代码")
	private String channelcode;
	/** 对应字段：INSURANCECOMPANYCODE,备注：保险公司归属机构 */
	@Column(name = "INSURANCECOMPANYCODE", description = "保险公司归属机构")
	private String insurancecompanycode;
	/** 对应字段：SURVEYIND,备注：创新业务标识 */
	@Column(name = "SURVEYIND", description = "创新业务标识")
	private String surveyind;
	/** 对应字段：VALIDIND,备注：有效标志0-无效1-有效 */
	@Column(name = "VALIDIND", description = "有效标志0-无效1-有效")
	private String validind;
	/** 对应字段：FLAG,备注：标志字段 */
	@Column(name = "FLAG", description = "标志字段")
	private String flag;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@Column(name = "INPUTDATE", description = "入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@Column(name = "UPDATESYSDATE", description = "修改日期")
	private Date updatesysdate;
}
