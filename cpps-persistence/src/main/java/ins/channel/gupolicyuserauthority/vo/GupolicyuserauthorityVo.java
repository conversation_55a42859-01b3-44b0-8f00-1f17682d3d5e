package ins.channel.gupolicyuserauthority.vo;

import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GupolicyuserauthorityVo对象.对应实体描述：用户保单权限表
 *
 */
@Data
@ApiModel("GupolicyuserauthorityVo对象")
public class GupolicyuserauthorityVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 */
	@ApiModelProperty("主键")
	private String id;
	/** 对应字段：USER_CODE,备注：用户账号 */
	@ApiModelProperty("用户账号")
	private String userCode;
	/** 对应字段：COMPANY_CODE,备注：关联机构代码 */
	@ApiModelProperty("关联机构代码")
	private String companyCode;
	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;
	/** 对应字段：RISKCODE,备注：险种代码 */
	@ApiModelProperty("险种代码")
	private String riskCode;
	/** 对应字段：CHANNELCODE,备注：渠道代码 */
	@ApiModelProperty("渠道代码")
	private String channelcode;
	/** 对应字段：INSURANCECOMPANYCODE,备注：保险公司归属机构 */
	@ApiModelProperty("保险公司归属机构")
	private String insurancecompanycode;
	/** 对应字段：SURVEYIND,备注：创新业务标识 */
	@ApiModelProperty("创新业务标识")
	private String surveyind;
	/** 对应字段：VALIDIND,备注：有效标志0-无效1-有效 */
	@ApiModelProperty("有效标志0-无效1-有效")
	private String validind;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：INPUTDATE,备注：入机日期 */
	@ApiModelProperty("入机日期")
	private Date inputDate;
	/** 对应字段：UPDATESYSDATE,备注：修改日期 */
	@ApiModelProperty("修改日期")
	private Date updatesysdate;
}
