package ins.channel.gupolicyuserauthority.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import ins.framework.mybatis.annotations.Column;
import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 *
 * PolicyuserauthorityReqVo对象.对应实体描述：用户保单权限查询入参
 *
 */
@Data
@ApiModel("PolicyuserauthorityReqVo对象")
public class PolicyuserauthorityReqVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;
	/** 对应字段：APPLINAME,备注：投保人名称 */
	@ApiModelProperty("投保人名称")
	private String appliName;
	//@ApiModelProperty("起保日期起")
	@JsonIgnore
	private Date startDatestart;
	//@ApiModelProperty("起保日期止")
	@JsonIgnore
	private Date startDateend;
	//@ApiModelProperty("终保日期起")
	@JsonIgnore
	private Date endDatestart;
	//@ApiModelProperty("终保日期止")
	@JsonIgnore
	private Date endDateend;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;

	/** 对应字段：AUTHSTATUS,备注：授权状态 0-未授权 1-已授权 */
	@ApiModelProperty("授权状态 0-未授权 1-已授权")
	private String authStatus;

	@ApiModelProperty("待授权 保单集合")
	private Set<String> policynoList;
	@ApiModelProperty("关联机构代码")
	private String companyCode;

	@ApiModelProperty("起保日期查询区间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private List<Date> startDateList;

	@ApiModelProperty("终保日期查询区间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private List<Date> endDateList;

	/** 对应字段：USER_CODE,备注：用户代码 */
	@Column(name = "USER_CODE", description = "用户代码")
	private String userCode;
	/** 对应字段：USER_CNAME,备注：用户姓名 */
	@Column(name = "USER_CNAME", description = "用户姓名")
	private String userCname;
}
