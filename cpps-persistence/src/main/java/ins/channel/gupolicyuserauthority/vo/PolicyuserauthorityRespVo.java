package ins.channel.gupolicyuserauthority.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * PolicyuserauthorityReqVo对象.对应实体描述：用户保单权限查询入参
 *
 */
@Data
@ApiModel("GupolicyuserauthorityVo对象")
public class PolicyuserauthorityRespVo implements Serializable {
	private static final long serialVersionUID = 1L;


	//Gupolicymain 表字段
	/** 对应字段：POLICYNO,备注：保单号码 */
	@ApiModelProperty("保单号码")
	private String policyNo;
	/** 对应字段：APPLINAME,备注：投保人名称 */
	@ApiModelProperty("投保人名称")
	private String appliName;
	/** 对应字段：COMPANY_CODE,备注：关联机构代码 */
	@ApiModelProperty("关联机构代码")
	private String companyCode;
	@ApiModelProperty("起保日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date startDate;
	/** 对应字段：ENDDATE,备注：终保日期 */
	@ApiModelProperty("终保日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date endDate;

	/** 对应字段：AUTHSTATUS,备注：授权状态 0-未授权 1-已授权 */
	@ApiModelProperty("授权状态 0-未授权 1-已授权")
	private String authStatus;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
}
