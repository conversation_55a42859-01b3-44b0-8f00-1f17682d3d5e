package ins.channel.gzendorrealated.dao;

import ins.channel.gzendorrealated.dto.domain.GzEndorRealatedDto;
import ins.channel.gzendorrealated.dto.domain.GzEndorRealatedDtoExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface GzEndorRealatedDtoMapper {
    int countByExample(GzEndorRealatedDtoExample example);

    int deleteByExample(GzEndorRealatedDtoExample example);

    int insert(GzEndorRealatedDto record);

    int insertSelective(GzEndorRealatedDto record);

    List<GzEndorRealatedDto> selectByExample(GzEndorRealatedDtoExample example);

    int updateByExampleSelective(@Param("record") GzEndorRealatedDto record, @Param("example") GzEndorRealatedDtoExample example);

    int updateByExample(@Param("record") GzEndorRealatedDto record, @Param("example") GzEndorRealatedDtoExample example);
}