package ins.channel.gztemplatesave.po;

import ins.framework.mybatis.annotations.Column;
import ins.framework.mybatis.annotations.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GZTEMPLATESAVE的PO对象<br/>
 * 对应表名：GZTEMPLATESAVE,备注：上传模板数据暂存
 *
 */
@Data
@Table(name = "GZTEMPLATESAVE")
public class Gztemplatesave implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：UUID,备注：唯一值 */
	@Column(name = "UUID", description = "唯一值")
	private String uuid;
	/** 对应字段：TEMPLATEDATA,备注：模板数据 */
	@Column(name = "TEMPLATEDATA", description = "模板数据")
	private String templatedata;
	/** 对应字段：CREATEDATE */
	@Column(name = "CREATEDATE")
	private Date createDate;
	/** 对应字段：USERCODE */
	@Column(name = "USERCODE")
	private String userCode;
	/** 对应字段：REMARK */
	@Column(name = "REMARK")
	private String remark;
}
