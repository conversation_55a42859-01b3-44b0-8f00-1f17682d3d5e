package ins.channel.gztemplatesave.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * GztemplatesaveVo对象.对应实体描述：上传模板数据暂存
 *
 */
@Data
@ApiModel("GztemplatesaveVo对象")
public class GztemplatesaveVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：UUID,备注：唯一值 */
	@ApiModelProperty("唯一值")
	private String uuid;
	/** 对应字段：TEMPLATEDATA,备注：模板数据 */
	@ApiModelProperty("模板数据")
	private String templatedata;
	/** 对应字段：CREATEDATE */
	@ApiModelProperty()
	private Date createDate;
	/** 对应字段：USERCODE */
	@ApiModelProperty()
	private String userCode;
	/** 对应字段：REMARK */
	@ApiModelProperty()
	private String remark;
}
