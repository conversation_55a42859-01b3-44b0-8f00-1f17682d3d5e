package ins.channel.paycomcodedefine.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.baseCode.po.BaseCode;
import ins.channel.paycomcodedefine.po.GgpaycomcodedefineSelect;
import ins.channel.paycomcodedefine.po.Gppaycomcodedefine;
import ins.channel.paycomcodedefine.po.GppaycomcodedefineSearch;
import ins.channel.power.vo.UserpaymentcompanyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 表GPPAYCOMCODEDEFINE对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 */
@Mapper
public interface GppaycomcodedefineDao extends MybatisBaseDao<Gppaycomcodedefine, String> {

    /**
     * 根据页面条件进行分页查询
     *
     * @param pageParam
     * @param entity
     * @return
     */
    Page<Gppaycomcodedefine> searchPage(PageParam pageParam, GppaycomcodedefineSearch entity);

    /**
     * 供下拉框查询使用
     *
     * @param pageParam
     * @param queryInfo
     * @return
     */
    Page<GgpaycomcodedefineSelect> paycomcodeForSelectPage(PageParam pageParam, @Param("queryInfo") String queryInfo,
                                                           @Param("paymentCodes") Set<String> paymentCodes);

    Page<GgpaycomcodedefineSelect> paycomcodeForSelectNoLimit();

    List<UserpaymentcompanyVo> queryuserpaymentcompanyInfo(@Param("userCode") String userCode);

    List<Gppaycomcodedefine> queryPaymentComListByCodeSet(@Param("addSet") Set<String> addSet);

    List<String> selectByDeparmentCode(@Param("companyCode") List<String> companyCode);

    /**
     * Modify By sino 查询所有收付机构信息(供基础码表查询使用) 2019/11/20
     *
     * @return
     */
    List<BaseCode> queryAll();
}
