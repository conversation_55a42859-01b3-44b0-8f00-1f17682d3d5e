package ins.channel.paycomcodedefine.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GPPAYCOMCODEDEFINE的PO对象<br/>
 * 对应表名：GPPAYCOMCODEDEFINE,备注：收付机构定义表
 *
 */
@Data
@Table(name = "GPPAYCOMCODEDEFINE")
public class Gppaycomcodedefine implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：PAYMENT_COMCODE,备注：收付机构 */
	@Column(name = "PAYMENT_COMCODE", description = "收付机构")
	private String paymentComcode;
	/** 对应字段：DEPARTMENT_CODE,备注：出单机构 */
	@Column(name = "DEPARTMENT_CODE", description = "出单机构")
	private String departmentCode;
	/** 对应字段：OFCENTER_CNAME,备注：收付机构中文名称 */
	@Column(name = "OFCENTER_CNAME", description = "收付机构中文名称")
	private String ofcenterCname;
	/** 对应字段：OFCENTER_TNAME,备注：收付机构繁体名称 */
	@Column(name = "OFCENTER_TNAME", description = "收付机构繁体名称")
	private String ofcenterTname;
	/** 对应字段：OFCENTER_ENAME,备注：收付机构英文名称 */
	@Column(name = "OFCENTER_ENAME", description = "收付机构英文名称")
	private String ofcenterEname;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@Column(name = "VALID_IND", description = "有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@Column(name = "FLAG", description = "标志字段")
	private String flag;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@Column(name = "CREATE_TIME", description = "创建时间")
	private Date createTime;
	/** 对应字段：CREATE_CODE,备注：创建人 */
	@Column(name = "CREATE_CODE", description = "创建人")
	private String createCode;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@Column(name = "MODIFIED_TIME", description = "更新时间")
	private Date modifiedTime;
	/** 对应字段：MODIFIED_CODE,备注：修改人 */
	@Column(name = "MODIFIED_CODE", description = "修改人")
	private String modifiedCode;
}
