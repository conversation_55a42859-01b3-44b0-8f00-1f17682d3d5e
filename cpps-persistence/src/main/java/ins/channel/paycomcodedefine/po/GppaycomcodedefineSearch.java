package ins.channel.paycomcodedefine.po;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GPPAYCOMCODEDEFINE的PO对象<br/>
 * 对应表名：GPPAYCOMCODEDEFINE,备注：收付机构定义表
 *
 */
@Data
public class GppaycomcodedefineSearch implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：PAYMENT_COMCODE,备注：收付机构 */
	private String paymentComcode;

	/** 对应字段：DEPARTMENT_CODE,备注：出单机构 */
	private String departmentCode;

	/** 对应字段：OFCENTER_CNAME,备注：收付机构中文名称 */
	private String ofcenterCname;

	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	private String validInd;

	/** 开始时间 */
	private String startTime;
	/** 结束时间 */
	private String endTime;
}
