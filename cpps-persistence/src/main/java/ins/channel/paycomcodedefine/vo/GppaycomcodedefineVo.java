package ins.channel.paycomcodedefine.vo;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GppaycomcodedefineVo对象.对应实体描述：收付机构定义表
 *
 */
@Data
@ApiModel("GppaycomcodedefineVo对象")
public class GppaycomcodedefineVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：PAYMENT_COMCODE,备注：收付机构 */
	@ApiModelProperty("收付机构")
	private String paymentComcode;
	/** 对应字段：DEPARTMENT_CODE,备注：出单机构 */
	@ApiModelProperty("出单机构")
	private String departmentCode;
	/** 对应字段：OFCENTER_CNAME,备注：收付机构中文名称 */
	@ApiModelProperty("收付机构中文名称")
	private String ofcenterCname;
	/** 对应字段：OFCENTER_TNAME,备注：收付机构繁体名称 */
	@ApiModelProperty("收付机构繁体名称")
	private String ofcenterTname;
	/** 对应字段：OFCENTER_ENAME,备注：收付机构英文名称 */
	@ApiModelProperty("收付机构英文名称")
	private String ofcenterEname;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	private Date createTime;
	/** 对应字段：CREATE_CODE,备注：创建人 */
	@ApiModelProperty("创建人")
	private String createCode;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	private Date modifiedTime;
	/** 对应字段：MODIFIED_CODE,备注：修改人 */
	@ApiModelProperty("修改人")
	private String modifiedCode;
}
