package ins.channel.paycomref.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.paycomref.po.Gppaycomref;
import ins.channel.paycomref.po.GppaycomrefKey;
import ins.channel.paycomref.po.GppaycomrefSearch;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 *
 * 表GPPAYCOMREF对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GppaycomrefDao extends MybatisBaseDao<Gppaycomref, GppaycomrefKey> {

    /**
     * 根据页面条件进行分页查询
     * @param pageParam
     * @param entity
     * @return
     */
    Page<Gppaycomref> searchPage(PageParam pageParam, GppaycomrefSearch entity);

    Gppaycomref selectByDepartmentCode(Gppaycomref gppaycomref);

    /**
     * 根据出单机构删除关联关系
     * @param departmentCodes
     * @return
     */
    int deleteByDepartMentCodes(List<String> departmentCodes);

    /**
     * 根据出单机构查询关联关系
     * @param departmentCode
     * @return
     */
    List<Gppaycomref> selectByCompanyCode(String departmentCode);

    /**
     * 根据收付机构机构查询关联关系
     * @param paymentCode
     * @return
     */
    List<Gppaycomref> selectByPaymentCode(String paymentCode);
}
