package ins.channel.paycomref.po;

import java.io.Serializable;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GPPAYCOMREF的复合主键PO对象<br/>
 * 对应表名：GPPAYCOMREF,备注：收付机构和机构映射表
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "GPPAYCOMREF")
public class Gppaycomref extends GppaycomrefKey implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：PAYMENT_COMCODE,备注：Δ业务归属机构 来源于ggcompany */
	@Column(name = "PAYMENT_COMCODE", description = "Δ业务归属机构 来源于ggcompany")
	private String paymentComcode;
}
