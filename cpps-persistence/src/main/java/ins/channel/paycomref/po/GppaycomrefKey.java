package ins.channel.paycomref.po;

import java.io.Serializable;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GPPAYCOMREF的复合主键PO主键对象<br/>
 * 对应表名：GPPAYCOMREF,备注：收付机构和机构映射表
 *
 */
@Data
@Table(name = "GPPAYCOMREF")
public class GppaycomrefKey implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：DEPARTMENT_CODE,备注：Δ业务归属机构 来源于ggcompany */
	@Column(name = "DEPARTMENT_CODE", description = "Δ业务归属机构 来源于ggcompany")
	private String departmentCode;
	/** 对应字段：USER_TYPE,备注：1:保费;2:佣金;3:赔款;4:再保;5:见费出单 */
	@Column(name = "USER_TYPE", description = "1:保费;2:佣金;3:赔款;4:再保;5:见费出单")
	private String userType;
}
