package ins.channel.paycomref.po;

import ins.framework.mybatis.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GPPAYCOMREF的PO对象<br/>
 * 对应表名：GPPAYCOMREF,备注：归属机构与收付机构关联表
 *
 */
@Data
@Table(name = "GPPAYCOMREF")
public class GppaycomrefSearch implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 对应字段：DEPARTMENT_CODE,备注：业务归属机构 */
    private String departmentCode;

    /** 对应字段：USER_TYPE,备注：1:保费;2:佣金;3:赔款;4:再保 */
    private String userType;

    /** 对应字段：PAYMENT_COMCODE,备注：收付机构 */
    private String paymentComcode;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;
}
