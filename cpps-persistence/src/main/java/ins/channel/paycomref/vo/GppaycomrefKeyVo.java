package ins.channel.paycomref.vo;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GppaycomrefKeyVo对象.对应实体描述：收付机构和机构映射表
 *
 */
@Data
@ApiModel(" GppaycomrefKeyVo对象")
public class GppaycomrefKeyVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：DEPARTMENTCODE,备注：Δ业务归属机构 来源于ggcompany */
	@ApiModelProperty("Δ业务归属机构 来源于ggcompany")
	private String departmentCode;
	/** 对应字段：USETYPE,备注：1:保费;2:佣金;3:赔款;4:再保;5:见费出单 */
	@ApiModelProperty("1:保费;2:佣金;3:赔款;4:再保;5:见费出单")
	private String userType;
}
