package ins.channel.paycomref.vo;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * GppaycomrefVo对象.对应实体描述：收付机构和机构映射表
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("GppaycomrefVo对象")
public class GppaycomrefVo extends GppaycomrefKeyVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：PAYMENTCOMCODE,备注：Δ业务归属机构 来源于ggcompany */
	@ApiModelProperty("Δ业务归属机构 来源于ggcompany")
	private String paymentComcode;
}
