package ins.channel.power.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.channel.power.po.SaaGrade;
import ins.channel.power.vo.SaagradetaskVo;
import ins.channel.power.vo.SaausergradeVo;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 角色表DAO接口
 * <AUTHOR>
 * @version 2019-06-03
 */
//public interface SaaGradeDao extends Mapper<SaaGrade> {
public interface SaaGradeDao extends MybatisBaseDao<SaaGrade,Long> {

    public Long selectMaxId();

    public List<SaagradetaskVo> queryGradeInfo(Long id);

    public List<SaaGrade> querySubGrade(@Param("gradeLevel") String gradeLevel);

    public String selectNameById(Long id);

    List<SaaGrade> selectAll();

    List<SaaGrade> selectByCondition(SaaGrade saaGrade);

    List<SaaGrade> querySaaGradeListBySet(@Param("addSet") Set<Long> addSet);

    List<SaausergradeVo> queryUserInfo(String userCode,String userInd);

    Integer selectNotDiff(SaaGrade saaGrade);

    Page<SaaGrade> searchPage(PageParam pageParam, SaaGrade saaGrade);
}
