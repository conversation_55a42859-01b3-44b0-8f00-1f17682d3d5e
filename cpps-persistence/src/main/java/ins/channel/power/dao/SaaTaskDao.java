package ins.channel.power.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.channel.power.po.SaaTask;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Set;

/**
 * 权限表DAO接口
 * <AUTHOR>
 * @version 2019-06-03
 */
public interface SaaTaskDao extends MybatisBaseDao<SaaTask,Long> {

    Set<String> queryTaskCodeSetByTaskIdSet(@Param("taskIdSet") Set<Long> taskIdSet);

    List<SaaTask> querySaaTaskListByTaskCodeSet(@Param("taskCodeSet") Set<String> taskCodeSet);

    List<SaaTask> selectByCondition(SaaTask saaTask);

    List<SaaTask> selectAll();

    Long selectMaxId();

    String selectParentNameByCode(String parentcode);

    List<String> selectAllTaskCode();

    Integer selectNotDiff(SaaTask saaTask);
}
