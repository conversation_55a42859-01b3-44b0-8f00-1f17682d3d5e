package ins.channel.power.dao;

import ins.channel.power.po.SaaUserGradeTask;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.Set;

/**
 * 用户角色权限表DAO接口
 * <AUTHOR>
 * @version 2019-06-03
 */
public interface SaaUserGradeTaskDao extends Mapper<SaaUserGradeTask> {

    void validGradeTaskByGradeAndTask(@Param("gradeId") Long gradeId, @Param("taskCodeSet") Set<String> taskCodeSet);

    void invalidGradeTaskByGradeAndTask(@Param("gradeId") Long gradeId, @Param("taskCodeSet") Set<String> taskCodeSet);
}
