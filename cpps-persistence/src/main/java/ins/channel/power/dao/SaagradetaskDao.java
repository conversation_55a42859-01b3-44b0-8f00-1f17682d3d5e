package ins.channel.power.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import ins.framework.mybatis.MybatisBaseDao;
import ins.channel.power.po.Saagradetask;

/**
 * 角色权限表DAO接口
 * <AUTHOR>
 * @version 2019-06-03
 */
public interface SaagradetaskDao extends MybatisBaseDao<Saagradetask,Long> {

    public Long selectMaxId();

    List<String> querySaaTaskCodeListByGradeId(@Param("gradeId") Long gradeId);

    Saagradetask queryAllGradeTaskByGradeIdAndTaskId(@Param("gradeId") Long gradeId, @Param("taskId") Long taskId);

//    void validSingleGradeTask(@Param("gradeId") Long gradeId, @Param("taskId") Long taskId);
    void validSingleGradeTask(Map map);

//    void invalidGradeTask(@Param("gradeId") Long gradeId, @Param("taskIdSet") Set<Long> taskIdSet);
    void invalidGradeTask(Map map);

    void invalidGradeTaskByTaskId(Saagradetask saagradetask);

    void updateSelectiveByGradeId(Saagradetask saagradetask);
}
