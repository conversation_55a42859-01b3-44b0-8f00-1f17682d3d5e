package ins.channel.power.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.channel.power.po.Saausergrade;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户角色权限表DAO接口
 *
 * <AUTHOR>
 * @version 2019-06-03
 */
//public interface SaaUserGradeDao extends Mapper<SaaUserGrade> {
public interface SaausergradeDao extends MybatisBaseDao<Saausergrade,Long> {

    List<String> queryAllUserCode(@Param("list") List list);

    List<String> queryRoleList(@Param("userCode") String userCode);

    void invalidGradeByUserCode(Map map);

    List<String> queryEmail(@Param("gradeId") Long gradeId);

    /**
     * @param comcode 必须传入权限机构，可取T_ORG_INFO 的maprebranchcom
     * @return
     */
    List<Map<String,String>> getNotifyToByComcode(@Param("comcode") String comcode);

    List<Map<String,String>> getNotifyCCByComcode(@Param("comcode") String comcode);

    int updateSelectiveByGradeId(Saausergrade saaUserGrade);

    List<Long> queryGradeIdListByUserCode(@Param("userCode") String usercode);

    Saausergrade queryUserGradeByUserCodeAndGradeId(@Param("userCode") String usercode, @Param("gradeid") Long gradeid);

    Long selectMaxId();

    void validSingleGradeTask(Map map);

    void invalidGradeTask(Map map);

    void deleteDataByUserCode(String userCode);
}
