package ins.channel.power.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.channel.power.po.Saauserpaymentcompany;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 表SAAUSERPAYMENTCOMPANY对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 */
@Mapper
public interface SaauserpaymentcompanyDao extends MybatisBaseDao<Saauserpaymentcompany, String> {

    public List<Saauserpaymentcompany> queryByUserCode(Saauserpaymentcompany saauserpaymentcompany);

    List<String> queryPaymentComcodeListByUserCode(@Param("userCode") String userCode);

    Saauserpaymentcompany queryByUserCodeAndPaymentComcode(@Param("userCode") String userCode, @Param("paymentComcode") String paymentComcode);

    void updateOldDefaultCom(String userCode);

    Long selectMaxId();

    void updatePaymentCom(Map<String, Object> map);

    List<Saauserpaymentcompany> queryuserpaymentComListBySet(@Param("plusSet") Set<String> plusSet,@Param("userCode") String userCode);

    void deleteDataByUserCode(String userCode);
}
