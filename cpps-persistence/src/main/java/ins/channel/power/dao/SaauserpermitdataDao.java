package ins.channel.power.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.channel.company.po.Ggcompany;
import ins.channel.power.po.Saausergrade;
import ins.channel.power.po.Saauserpermitdata;
import ins.channel.power.vo.SaausergradeVo;
import ins.channel.power.vo.UserpermitdataVo;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 表SAAUSERPERMITDATA对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 */

public interface SaauserpermitdataDao extends MybatisBaseDao<Saauserpermitdata,String> {
    List<String> queryAllUserCode(@Param("list") List<String> list);

    List<String> queryAllValue2(@Param("list") List<String> list);

    List<String> queryValue2(@Param("userCode") String userCode);

    List<String> queryUserCode(@Param("roleLists") List<String> roleLists,
                               @Param("stringList") List<String> stringList);

    void invalidDataByUserCode(Map map);

    List<Ggcompany> queryUserGradeOrgInfo(@Param("userCode") String userCode, @Param("gradeid") Long gradeid);

    List<String> queryComcodeListByUserCode(@Param("userCode") String userCode);

    Saauserpermitdata queryUserPermitdataByUserCodeAndComCode(@Param("userCode")String userCode,@Param("comCode") String companyCode);

    Long selectMaxId();

    void validSingleUserpermitdata(Map<String, Object> map);

    void invalidvalidSingleUserpermitdata(Map<String, Object> map);

    List<UserpermitdataVo> queryGgCompanyByLoginUser(String userCode);

}