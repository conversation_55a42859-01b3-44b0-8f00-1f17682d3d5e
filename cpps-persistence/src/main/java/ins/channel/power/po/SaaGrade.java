package ins.channel.power.po;

import ins.platform.common.CreateTime;
import ins.platform.common.ModifiedTime;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@SuppressWarnings("serial")
@Table(name = "SAAGRADE")
public class SaaGrade implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 对应字段：ID */
    private Long id;
    /** 对应字段：GRADECNAME */
    private String gradecname;
    /** 对应字段：GRADEENAME */
    private String gradeename;
    /** 对应字段：COMCODE */
    private String comcode;
    /** 对应字段：CREATORCODE */
    private String creatorcode;
    /** 对应字段：UPDATERCODE */
    private String updatercode;
    /** 对应字段：VALIDSTATUS */
    private String validstatus;
    /** 对应字段：INSERTTIMEFORHIS */
    @CreateTime
    private Date inserttimeforhis;
    /** 对应字段：OPERATETIMEFORHIS */
    @ModifiedTime
    private Date operatetimeforhis;
    /** 开关 */
    private String numflag;
    /** 职级 */
    private String userInd;

}

