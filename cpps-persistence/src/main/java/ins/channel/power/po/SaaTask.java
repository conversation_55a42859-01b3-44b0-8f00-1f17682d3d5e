package ins.channel.power.po;

import ins.framework.mybatis.annotations.Column;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@SuppressWarnings("serial")
@Table(name = "SAATASK")
public class SaaTask implements Serializable {
    /**
     * 对应字段：ID,备注：主键ID PK,创建序列号自增
     */
    @Column(name = "ID", description = "主键ID PK,创建序列号自增")
    private Long id;
    /**
     * 对应字段：TASKCODE,备注：功能代码
     */
    @Column(name = "TASKCODE", description = "功能代码")
    private String taskcode;
    /**
     * 对应字段：PARENTCODE,备注：父级代码
     */
    @Column(name = "PARENTCODE", description = "父级代码")
    private String parentcode;
    /**
     * 对应字段：TASKCNAME,备注：功能中文名称
     */
    @Column(name = "TASKCNAME", description = "功能中文名称")
    private String taskcname;

    /**
     * 对应字段：TASKENAME,备注：功能英文名称
     */
    @Column(name = "TASKENAME", description = "功能英文名称")
    private String taskename;
    /**
     * 对应字段：URL,备注：路径
     */
    @Column(name = "URL", description = "路径")
    private String url;
    /**
     * 对应字段：SYSTEM,备注：系统
     */
    @Column(name = "SYSTEM", description = "系统")
    private String system;
    /**
     * 对应字段：CREATORCODE,备注：创建人
     */
    @Column(name = "CREATORCODE", description = "创建人")
    private String creatorcode;
    /**
     * 对应字段：CREATETIME,备注：创建时间
     */
    @Column(name = "CREATETIME", description = "创建时间")
    private Date createtime;
    /**
     * 对应字段：VALIDFLAG,备注：有效标志 1:有效 0:无效
     */
    @Column(name = "VALIDFLAG", description = "有效标志 1:有效 0:无效")
    private String validflag;
    /**
     * 对应字段：REMARK,备注：备注
     */
    @Column(name = "REMARK", description = "备注")
    private String remark;
    /**
     * 对应字段：FLAG,备注：标志字段
     */
    @Column(name = "FLAG", description = "标志字段")
    private String flag;
    /**
     * 对应字段：SYNFLAG
     */
    @Column(name = "SYNFLAG")
    private String synflag;
    /**
     * 对应字段：INSERTTIMEFORHIS,备注：插入时间
     */
    @Column(name = "INSERTTIMEFORHIS", description = "插入时间")
    private Date inserttimeforhis;
    /**
     * 对应字段：OPERATETIMEFORHIS,备注：修改时间
     */
    @Column(name = "OPERATETIMEFORHIS", description = "修改时间")
    private Date operatetimeforhis;
    /**
     * 对应字段：TASK_NUM,备注：菜单顺序
     */
    @Column(name = "TASK_NUM", description = "菜单顺序")
    private BigDecimal taskNum;

    private static final long serialVersionUID = 1L;
}

