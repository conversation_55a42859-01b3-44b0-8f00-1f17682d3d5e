package ins.channel.power.po;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;
import tk.mybatis.mapper.code.ORDER;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@SuppressWarnings("serial")
@Table(name = "SAAUSERGRADETASK")
public class SaaUserGradeTask implements Serializable {
    /**
     *
     */
    @Id
    @KeySql(sql = "select SEQ_SAAUSERGRADETASK.nextval from dual", order = ORDER.BEFORE)
    private Long id;

    /**
     *
     */
    private String systemcode;

    /**
     *
     */
    private String usercode;

    /**
     *
     */
    private Long gradeid;

    /**
     *
     */
    private String taskcode;

    /**
     *
     */
    private String creatorcode;

    /**
     *
     */
    private String updatercode;

    /**
     *
     */
    private String validstatus;

    /**
     *
     */
    private Date inserttimeforhis;

    /**
     *
     */
    private Date operatetimeforhis;

    private static final long serialVersionUID = 1L;
}

