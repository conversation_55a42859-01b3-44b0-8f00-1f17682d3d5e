package ins.channel.power.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表SAAGRADETASK的PO对象<br/>
 * 对应表名：SAAGRADETASK
 *
 */
@Data
@Table(name = "SAAGRADETASK")
public class Saagradetask implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键ID PK,创建序列号自增 */
	@Column(name = "ID", description = "主键ID PK,创建序列号自增")
	private Long id;
	/** 对应字段：GRADEID,备注：等级表ID */
	@Column(name = "GRADEID", description = "等级表ID")
	private Long gradeid;
	/** 对应字段：TASKID,备注：任务ID */
	@Column(name = "TASKID", description = "任务ID")
	private String taskid;
	/** 对应字段：CREATORCODE,备注：创建人 */
	@Column(name = "CREATORCODE", description = "创建人")
	private String creatorcode;
	/** 对应字段：UPDATERCODE,备注：最后修改人 */
	@Column(name = "UPDATERCODE", description = "最后修改人")
	private String updaterCode;
	/** 对应字段：VALIDSTATUS,备注：效力状态 1:有效 0:无效 */
	@Column(name = "VALIDSTATUS", description = "效力状态 1:有效 0:无效")
	private String validStatus;
	/** 对应字段：INSERTTIMEFORHIS,备注：插入时间 */
	@Column(name = "INSERTTIMEFORHIS", description = "插入时间")
	private Date inserttimeforhis;
	/** 对应字段：OPERATETIMEFORHIS,备注：修改时间 */
	@Column(name = "OPERATETIMEFORHIS", description = "修改时间")
	private Date operatetimeforhis;
}
