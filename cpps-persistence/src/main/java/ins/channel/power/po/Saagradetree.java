package ins.channel.power.po;

import lombok.Data;

import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;

@Data
public class Saagradetree implements Serializable {
    /**
     * 角色ID
     */
    @Id
    private Long gradeid;

    /**
     * 可以操作的角色ID（多个角色中间以,分隔）
     */
    private String gradeidOpr;

    /**
     * 备注
     */
    private String remark;

    /**
     * 插入时间
     */
    private Date inputTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}

