package ins.channel.power.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表SAAUSERPAYMENTCOMPANY的PO对象<br/>
 * 对应表名：SAAUSERPAYMENTCOMPANY
 *
 */
@Data
@Table(name = "SAAUSERPAYMENTCOMPANY")
public class Saauserpaymentcompany implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：PK,创建序列号自增 */
	@Column(name = "ID", description = "PK,创建序列号自增")
	private String id;
	/** 对应字段：USER_CODE,备注：用户代码 */
	@Column(name = "USER_CODE", description = "用户代码")
	private String userCode;
	/** 对应字段：USER_CNAME,备注：用户姓名 */
	@Column(name = "USER_CNAME", description = "用户姓名")
	private String userCname;
	/** 对应字段：COM_CODE,备注：人员归属机构 */
	@Column(name = "COM_CODE", description = "人员归属机构")
	private String comCode;
	/** 对应字段：PAYMENT_COMCODE,备注：收付机构 */
	@Column(name = "PAYMENT_COMCODE", description = "收付机构")
	private String paymentComcode;
	/** 对应字段：PAYMENT_COMPANYNAME,备注：收付机构 名称*/
	@Column(name = "PAYMENT_COMPANYNAME", description = "收付机构名称")
	private String paymentCompanyname;
	/** 对应字段：CREATE_CODE,备注：创建人 */
	@Column(name = "CREATE_CODE", description = "创建人")
	private String createCode;
	/** 对应字段：CREATE_TIME,备注：创建日期 */
	@Column(name = "CREATE_TIME", description = "创建日期")
	private Date createTime;
	/** 对应字段：DEFAULT_IND,备注：修改时间 */
	@Column(name = "MODIFIED_TIME", description = "修改时间")
	private Date modifiedTime;
	/** 对应字段：MODIFIED_TIME,备注：是否默认: 1-默认 */
	@Column(name = "DEFAULT_IND", description = "是否默认: 1-默认")
	private String defaultInd;
	/** 对应字段：VALID_IND,备注：是否有效: 1-有效；0-无效 */
	@Column(name = "VALID_IND", description = "是否有效: 1-有效；0-无效")
	private String validInd;
}
