package ins.channel.power.po;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;
import tk.mybatis.mapper.code.ORDER;

import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;


@Data
public class TSaagradetrace implements Serializable {
    /**
     * ID
     */
    @Id
    @KeySql(sql = "select SEQ_T_SAAGRADETRACE.nextval from dual", order = ORDER.BEFORE)
    private Long id;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 角色代码
     */
    private Long gradeid;

    /**
     * 角色中文名称
     */
    private String gradecname;

    /**
     * 角色英文名称
     */
    private String gradeename;

    /**
     * 机构代码集合
     */
    private String taskcode;

    /**
     * 操作人代码
     */
    private String usercode;

    /**
     * 插入时间
     */
    private Date inserttimeforhis;

    private static final long serialVersionUID = 1L;
}

