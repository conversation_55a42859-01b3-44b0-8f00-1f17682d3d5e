package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "用户详细信息")
@Data
@SuppressWarnings("serial")
public class GguserInfoVo implements Serializable {
    /**
     * 用户代码
     */
    @ApiModelProperty(value = "用户代码")
    private String userCode;

    /**
     * 岗位中文名称
     */
    @ApiModelProperty(value = "用户中文名称")
    private String userCname;

    @ApiModelProperty(value = "角色查询集合")
    private List<SaausergradeVo> usergradelist;

    @ApiModelProperty(value = "角色保存集合")
    private List<Long> gradeid;

    private static final long serialVersionUID = 1L;
}

