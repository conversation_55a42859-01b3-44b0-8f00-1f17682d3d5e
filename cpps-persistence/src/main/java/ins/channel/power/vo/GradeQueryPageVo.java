package ins.channel.power.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "角色分页模糊查询信息")
@Data
@SuppressWarnings("serial")
public class GradeQueryPageVo extends PageBaseDto implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "角色代码")
    private Long id;

    /**
     * 岗位中文名称
     */
    @ApiModelProperty(value = "角色中文名称")
    private String gradecname;

    //modify By Zhoutaoyu 新增前端查询条件(validStatus) 2019/10/04
    /**
     * 是否有效 1-有效
     */
    @ApiModelProperty(value = "是否有效 1-有效")
    private String validstatus;

    @ApiModelProperty("职级 1:管理员 2:业务管理岗 3:业务员 codeType ='UserRank'")
    private String userInd;

    private static final long serialVersionUID = 1L;
}

