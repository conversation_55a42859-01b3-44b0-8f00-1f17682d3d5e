package ins.channel.power.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "付款审核等级查询条件对象")
@Data
@SuppressWarnings("serial")
public class PaymentgradeQueryVo extends PageBaseDto {

	/** 对应字段：PAYMENT_GRADE_TYPE,备注：审核等级类型 0-通用 1-保费 2-手续费 3-赔款 4-再保 5-暂收款 6-共保出单费 */
//	@ApiModelProperty("审核等级类型 0-通用 1-保费 2-手续费 3-赔款 4-再保 5-暂收款 6-共保出单费")
//	private String paymentGradeType;
	/** 对应字段：PAYMENT_GRADE_CODE,备注：审核等级代码 */
	@ApiModelProperty("审核等级代码")
	private String paymentGradeCode;
	/** 对应字段：PAYMENT_GRADE_NAME,备注：审核等级名称 */
	@ApiModelProperty("审核等级名称")
	private String paymentGradeName;
	/** 对应字段：VALID_IND,备注：有效标识 0-无效 1-有效 */
	@ApiModelProperty("有效标识 0-无效 1-有效")
	private String validInd;
}
