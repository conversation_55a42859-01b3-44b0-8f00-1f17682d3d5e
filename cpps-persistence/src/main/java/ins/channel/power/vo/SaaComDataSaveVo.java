package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2018-08-30 19:32
 */
@Data
@ApiModel(value = "用户角色机构信息")
@SuppressWarnings("serial")
public class SaaComDataSaveVo implements Serializable {

    @ApiModelProperty(value = "用户代码")
    private String userCode;

    @ApiModelProperty(value = "角色代码")
    private Long gradeId;

    @ApiModelProperty(value = "归属机构")
    private String makeCom;

    @ApiModelProperty(value = "操作机构 逗号分割")
    private String comCodeList;

    @ApiModelProperty(value = "当前用户代码")
    private String currentUserCode;

    @ApiModelProperty(value = "默认操作机构")
    private String defaultComCode;

}