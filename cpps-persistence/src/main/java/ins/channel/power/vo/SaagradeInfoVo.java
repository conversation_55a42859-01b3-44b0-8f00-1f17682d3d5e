package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "角色详细信息")
@Data
@SuppressWarnings("serial")
public class SaagradeInfoVo implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "角色代码")
    private Long id;

    /**
     * 岗位中文名称
     */
    @ApiModelProperty(value = "角色中文名称")
    private String gradecname;

    @ApiModelProperty(value = "权限查询集合")
    private List<SaagradetaskVo> gradetasklist;

    @ApiModelProperty(value = "权限保存集合")
    private List<Long> taskid;

    /**
     * 用户代码
     */
    @ApiModelProperty(value = "用户代码")
    private String usercode;

    @ApiModelProperty("职级 1:管理员 2:业务管理岗 3:业务员 codeType ='UserRank'")
    private String userInd;

    private static final long serialVersionUID = 1L;
}

