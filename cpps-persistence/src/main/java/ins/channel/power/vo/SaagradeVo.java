package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "角色表")
@Data
@SuppressWarnings("serial")
public class SaagradeVo implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "角色代码")
    private Long id;

    /**
     * 岗位中文名称
     */
    @ApiModelProperty(value = "角色中文名称")
    private String gradecname;

    @ApiModelProperty("职级 1:管理员 2:业务管理岗 3:业务员 codeType ='UserRank'")
    private String userInd;


    /**
     * 角色级别 供创建角色使用
     */
    /*@ApiModelProperty(value = "角色级别 供创建角色使用 1总公司 2分公司 3 中支公司 4 支公司(默认为总公司,前端不展示)")
    private String gradelevel;*/

    //add By <PERSON><PERSON><PERSON>u 将是否有效返回给前端
    /**
     * 是否有效 1-有效
     */
    @ApiModelProperty(value = "是否有效 1-有效")
    private String validstatus;

    private static final long serialVersionUID = 1L;
}

