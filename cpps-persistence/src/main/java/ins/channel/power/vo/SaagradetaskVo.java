package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "角色权限信息")
@Data
@SuppressWarnings("serial")
public class SaagradetaskVo implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "权限ID  ")
    private Long taskid;

    /**
     * 权限代码
     */
    @ApiModelProperty(value = "权限代码")
    private String taskcode;

    /**
     * 父级代码
     */
    @ApiModelProperty(value = "父级代码")
    private String parentcode;

    /**
     * 权限中文名称
     */
    @ApiModelProperty(value = "权限中文名称")
    private String taskcname;

    /**
     * 选中标志
     */
    @ApiModelProperty(value = "是否选中 1 选中 0 未选中 ")
    private String flag;




    private static final long serialVersionUID = 1L;
}

