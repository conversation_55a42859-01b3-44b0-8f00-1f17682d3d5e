package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "ins.aml.power.vo.SaagradetreeVo")
@Data
public class SaagradetreeVo implements Serializable {
    /**
     * 角色ID
     */
    @ApiModelProperty(value = "角色ID")
    private Long gradeid;

    /**
     * 可以操作的角色ID（多个角色中间以,分隔）
     */
    @ApiModelProperty(value = "可以操作的角色ID（多个角色中间以,分隔）")
    private String gradeidOpr;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 插入时间
     */
    @ApiModelProperty(value = "插入时间")
    private Date inputTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}

