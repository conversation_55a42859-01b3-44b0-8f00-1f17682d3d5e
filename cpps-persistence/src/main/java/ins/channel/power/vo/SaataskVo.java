package ins.channel.power.vo;

import ins.framework.mybatis.annotations.Column;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@SuppressWarnings("serial")
@Table(name = "SAATASK")
public class SaataskVo implements Serializable {
    /**
     *主键ID PK
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     *功能代码
     */
    @ApiModelProperty(value = "功能代码")
    private String taskcode;

    /**
     *父级代码
     */
    @ApiModelProperty(value = "父级代码")
    private String parentcode;

    /**
     *功能中文名称
     */
    @ApiModelProperty(value = "功能中文名称")
    private String taskcname;

    /**
     *功能英文名称
     */
    @ApiModelProperty(value = "功能英文名称")
    private String taskename;

    /**
     *路径
     */
    @ApiModelProperty(value = "路径")
    private String url;

    /**
     *系统
     */
    @ApiModelProperty(value = "系统")
    private String system;

    /**
     *创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creatorcode;

    /**
     *创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createtime;

    /**
     *有效标志 1:有效 0:无效
     */
    @ApiModelProperty(value = "有效标志 1:有效 0:无效")
    private String validflag;

    /**
     *备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     *标志字段
     */
    @ApiModelProperty(value = "标志字段")
    private String flag;

    /**
     *
     */
    private String synflag;

    /**
     *插入时间
     */
    @ApiModelProperty(value = "插入时间")
    private Date inserttimeforhis;

    /**
     *修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date operatetimeforhis;
    /**
     * 对应字段：TASK_NUM,备注：菜单顺序
     */
    @ApiModelProperty(value = "功能序号")
    private BigDecimal taskNum;

    private static final long serialVersionUID = 1L;
}

