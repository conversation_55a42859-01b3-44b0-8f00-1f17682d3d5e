package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "用户角色信息")
@Data
@SuppressWarnings("serial")
public class SaausergradeVo implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "角色ID  ")
    private Long gradeid;

    /**
     * 权限中文名称
     */
    @ApiModelProperty(value = "权限中文名称")
    private String gradecname;

    /**
     * 选中标志
     */
    @ApiModelProperty(value = "是否选中 1 选中 0 未选中 ")
    private String flag;



    private static final long serialVersionUID = 1L;
}

