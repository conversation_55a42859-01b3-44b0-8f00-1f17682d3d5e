package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@ApiModel(value = "用户收付机构详细信息")
@Data
@SuppressWarnings("serial")
public class SaauserpaymentcompanyInfoVo implements Serializable {
    /**
     * 用户代码
     */
    @ApiModelProperty(value = "用户代码")
    private String userCode;

    /**
     * 岗位中文名称
     */
    @ApiModelProperty(value = "用户中文名称")
    private String userCname;

    @ApiModelProperty(value = "收付机构查询集合")
    private List<UserpaymentcompanyVo> userpaymentcompanyList;

    @ApiModelProperty(value = "收付机构更新集合")
    private List<UserpaymentcompanyVo> updatePaymentComList;

    private static final long serialVersionUID = 1L;
}

