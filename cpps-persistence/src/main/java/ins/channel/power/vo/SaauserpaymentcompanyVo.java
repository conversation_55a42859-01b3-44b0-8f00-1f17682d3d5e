package ins.channel.power.vo;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnore;

import ins.framework.mybatis.annotations.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * SaauserpaymentcompanyVo对象.
 *
 */
@Data
@ApiModel("SaauserpaymentcompanyVo对象")
public class SaauserpaymentcompanyVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：PK,创建序列号自增 */
	@ApiModelProperty("PK,创建序列号自增")
	@JsonIgnore
	private String id;
	/** 对应字段：USER_CODE,备注：用户代码 */
	@ApiModelProperty("用户代码")
	@JsonIgnore
	private String userCode;
	/** 对应字段：USER_CNAME,备注：用户姓名 */
	@ApiModelProperty("用户姓名")
	@JsonIgnore
	private String userCname;
	/** 对应字段：COM_CODE,备注：人员归属机构 */
	@ApiModelProperty("人员归属机构")
	@JsonIgnore
	private String comCode;
	/** 对应字段：PAYMENT_COMCODE,备注：收付机构 */
	@ApiModelProperty("收付机构")
	private String paymentComcode;
	@ApiModelProperty(name="收付机构名称")
	private String paymentCompanyname;
	/** 对应字段：CREATE_CODE,备注：创建人 */
	@ApiModelProperty(name = "创建人")
	@JsonIgnore
	private String createCode;
	/** 对应字段：CREATE_TIME,备注：创建日期 */
	@ApiModelProperty("创建日期")
	@JsonIgnore
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：修改时间 */
	@ApiModelProperty("修改时间")
	@JsonIgnore
	private Date modifiedTime;
	/** 对应字段：MODIFIED_TIME,备注：是否默认: 1-默认 */
	@ApiModelProperty("是否默认: 1-默认")
	@JsonIgnore
	private String defaultInd;
	/** 对应字段：VALID_IND,备注：是否有效: 1-有效；0-无效 */
	@ApiModelProperty("是否有效: 1-有效；0-无效")
	@JsonIgnore
	private String validInd;
}
