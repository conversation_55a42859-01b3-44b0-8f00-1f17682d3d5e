package ins.channel.power.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * SaauserpaymentgradeVo对象.对应实体描述：用户付款审核表
 *
 */
@Data
@ApiModel("SaauserpaymentgradeVo对象")
public class SaauserpaymentgradeVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 PK */
	@ApiModelProperty("主键 PK")
	private BigDecimal id;
	/** 对应字段：USER_CODE,备注：用户代码 */
	@ApiModelProperty("用户代码")
	private String userCode;
	/** 对应字段：PAYMENT_GRADE_TYPE,备注：审核等级类型 0-通用 1-保费 2-手续费 3-赔款 4-再保 5-暂收款 6-共保出单费 */
	@ApiModelProperty("审核等级类型 0-通用 1-保费 2-手续费 3-赔款 4-再保 5-暂收款 6-共保出单费")
	private String paymentGradeType;
	/** 对应字段：PAYMENT_GRADE_CODE,备注：审核等级代码 */
	@ApiModelProperty("审核等级代码")
	private String paymentGradeCode;
	/** 对应字段：PAYMENT_GRADE_NAME,备注：审核等级名称 */
	@ApiModelProperty("审核等级名称")
	private String paymentGradeName;
	/** 对应字段：START_FEE,备注：起始金额 */
	@ApiModelProperty("起始金额")
	private BigDecimal startFee;
	/** 对应字段：END_FEE,备注：最大金额 */
	@ApiModelProperty("最大金额")
	private BigDecimal endFee;
	/** 对应字段：VALID_IND,备注：有效标识 0-无效 1-有效 */
	@ApiModelProperty("有效标识 0-无效 1-有效")
	private String validInd;
	/** 对应字段：DEFAULT_FLAG,备注：默认标识 0-无效 1-默认 */
	@ApiModelProperty("默认标识 0-无效 1-默认")
	private String defaultFlag;
	/** 对应字段：FLAG,备注：标识 */
	@ApiModelProperty("标识")
	private String flag;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：CREATOR_CODE,备注：创建人 */
	@ApiModelProperty("创建人")
	private String creatorCode;
	/** 对应字段：UPDATER_CODE,备注：修改人 */
	@ApiModelProperty("修改人")
	private String updaterCode;
	/** 对应字段：MODIFY_TIME,备注：修改时间 */
	@ApiModelProperty("修改时间")
	private Date modifyTime;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	private Date createTime;
}
