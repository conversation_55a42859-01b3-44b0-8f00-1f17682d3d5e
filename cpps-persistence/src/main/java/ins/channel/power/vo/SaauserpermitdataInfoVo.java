package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "用户操作机构详细信息")
@Data
@SuppressWarnings("serial")
public class SaauserpermitdataInfoVo implements Serializable {
    /**
     * 用户代码
     */
    @ApiModelProperty(value = "用户代码")
    private String userCode;

    /**
     * 岗位中文名称
     */
    @ApiModelProperty(value = "用户中文名称")
    private String userCname;

    @ApiModelProperty(value = "操作机构查询集合")
    private List<UserpermitdataVo> userpermitdatalist;

    @ApiModelProperty(value = "操作机构更新ID集合")
    private List<String> comCodeList;

    private static final long serialVersionUID = 1L;
}

