package ins.channel.power.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，表SAAUSERPERMITDATA的VO对象<br/>
 * 对应表名：SAAUSERPERMITDATA,备注：用户允许操作数据表
 *
 */
@Data
public class SaauserpermitdataVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：ID,备注：ID */
	private BigDecimal id;
	/** 对应字段：SYSTEMCODE,备注：待取名 */
	private String systemCode;
	/** 对应字段：USERCODE,备注：用户代码 */
	private String userCode;
	/** 对应字段：COMCODE,备注：机构代码 */
	private String comCode;
	/** 对应字段：USERGRADEID,备注：用户等级表ID */
	private BigDecimal usergradeid;
	/** 对应字段：FACTORGROUP,备注：待取名 */
	private BigDecimal factorgroup;
	/** 对应字段：FACTORFIELDID,备注：待取名 */
	private BigDecimal factorfieldid;
	/** 对应字段：FACTORCODE,备注：待取名 */
	private String factorCode;
	/** 对应字段：DATAOPER,备注：待取名 */
	private String dataoper;
	/** 对应字段：DATAVALUE1,备注：待取名 */
	private String datavalue1;
	/** 对应字段：DATAVALUE2,备注：待取名 */
	private String datavalue2;
	/** 对应字段：CREATECODE,备注：创建人员 */
	private String createcode;
	/** 对应字段：CREATETIME,备注：创建时间 */
	private Date createTime;
	/** 对应字段：UPDATECODE,备注：更新人员 */
	private String updateCode;
	/** 对应字段：UPDATETIME,备注：更新时间 */
	private Date updatetime;
	/** 对应字段：VALIDFLAG,备注：有效标志 */
	private String validflag;
	/** 对应字段：REMARK,备注：备注 */
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	private String flag;
	/** 对应字段：SYNFLAG,备注：待取名 */
	private String synflag;
	/** 对应字段：INSERTTIMEFORHIS,备注：系统插入时间 */
	private Date inserttimeforhis;
	/** 对应字段：OPERATETIMEFORHIS,备注：系统更新时间 */
	private Date operatetimeforhis;

}
