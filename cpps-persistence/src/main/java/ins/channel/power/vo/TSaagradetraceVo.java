package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "角色日志表")
@Data
public class TSaagradetraceVo implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operateType;

    /**
     * 角色代码
     */
    @ApiModelProperty(value = "角色代码")
    private Long gradeid;

    /**
     * 角色中文名称
     */
    @ApiModelProperty(value = "角色中文名称")
    private String gradecname;

    /**
     * 角色英文名称
     */
    @ApiModelProperty(value = "角色英文名称")
    private String gradeename;

    /**
     * 机构代码集合
     */
    @ApiModelProperty(value = "机构代码集合")
    private String taskcode;

    /**
     * 操作人代码
     */
    @ApiModelProperty(value = "操作人代码")
    private String usercode;

    /**
     * 插入时间
     */
    @ApiModelProperty(value = "插入时间")
    private Date inserttimeforhis;

    private static final long serialVersionUID = 1L;
}

