package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value = "功能查询信息")
@Data
@SuppressWarnings("serial")
public class TaskQueryVo implements Serializable {
    /**
     *ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     *功能代码
     */
    @ApiModelProperty(value = "功能代码")
    private String taskcode;

    /**
     *功能中文名称
     */
    @ApiModelProperty(value = "功能中文名称")
    private String taskcname;

    /**
     *功能英文名称
     */
    @ApiModelProperty(value = "功能英文名称")
    private String taskename;

    /**
     *父级代码
     */
    @ApiModelProperty(value = "父级代码")
    private String parentcode;

    /**
     *父级功能名称
     */
    @ApiModelProperty(value = "父级功能名称")
    private String parentname;

    /**
     *创建人用户代码
     */
    @ApiModelProperty(value = "创建人用户代码")
    private String creatorcode;

    /**
     *创建人
     */
    @ApiModelProperty(value = "创建人")
    private String userCname;

    /**
     *系统
     */
    @ApiModelProperty(value = "系统")
    private String system;

    /**
     * 对应字段：TASK_NUM,备注：菜单顺序
     */
    @ApiModelProperty(value = "功能序号")
    private BigDecimal taskNum;

    /**
     * 是否有效 1-有效
     */
    @ApiModelProperty(value = "是否有效 1-有效")
    private String validflag;

    private static final long serialVersionUID = 1L;
}

