package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "用户收付机构信息")
@Data
@SuppressWarnings("serial")
public class UserpaymentcompanyVo implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "收付机构代码")
    private String paymentComcode;

    /**
     * 权限中文名称
     */
    @ApiModelProperty(value = "收付机构代码中文名称")
    private String paymentCompanyname;

    /**
     * 权限中文名称
     */
    @ApiModelProperty(value = "是否默认: 1-默认")
    private String defaultInd;

    /**
     * 选中标志
     */
    @ApiModelProperty(value = "是否选中 1 选中 0 未选中 ")
    private String flag;



    private static final long serialVersionUID = 1L;
}

