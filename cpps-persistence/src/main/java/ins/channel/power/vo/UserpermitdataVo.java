package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "用户操作机构信息")
@Data
@SuppressWarnings("serial")
public class UserpermitdataVo implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "操作机构代码")
    private String comCode;

    /**
     * 权限中文名称
     */
    @ApiModelProperty(value = "操作机构代码中文名称")
    private String companyCname;

    /**
     * 权限中文名称
     */
    @ApiModelProperty(value = "上级机构代码")
    private String upperCompanyCode;

    /**
     * 选中标志
     */
    @ApiModelProperty(value = "是否选中 1 选中 0 未选中 ")
    private String flag;



    private static final long serialVersionUID = 1L;
}

