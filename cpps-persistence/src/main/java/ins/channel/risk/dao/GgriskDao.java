package ins.channel.risk.dao;

import ins.channel.code.po.GgcodeSelect;
import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.baseCode.po.BaseCode;
import ins.channel.currency.po.Ggcurrency;
import ins.channel.risk.po.Ggrisk;
import ins.channel.risk.po.GgriskSearch;
import ins.channel.risk.po.GgriskSelect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * 表GGRISK对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GgriskDao extends MybatisBaseDao<Ggrisk, String> {
    /**
     * 根据页面条件进行分页查询
     * @param pageParam
     * @param entity
     * @return
     */
    Page<Ggrisk> searchPage(PageParam pageParam, GgriskSearch entity);

    /**
     * 主键无需传入，自动插入
     * @param entity
     * @return
     */
    int insertAuto(Ggrisk entity);
    int insertSelectiveAuto(Ggrisk entity);

    /**
     * 供下拉框查询使用
     * @param pageParam
     * @param queryInfo
     * @return
     */
    Page<GgriskSelect> riskInfoForSelectPage(PageParam pageParam, @Param("queryInfo") String queryInfo);

    /**
     * 翻译险种代码
     * @param riskCode
     * @return
     */
    Ggrisk translate(String riskCode);

    /**
     * 供下拉框查询使用
     * @param riskCode
     * @param riskCname
     * @return
     */
    Page<GgriskSelect> riskInfoForSelect( @Param("riskCode") String riskCode,@Param("riskCname") String riskCname);

    /**
     * Modify By Zhoutaoyu 查询所有险种信息(供基础码表查询使用) 2019/11/23
     *
     * @return
     */
    List<BaseCode> queryAll();

    List<GgcodeSelect> riskForSelectAll();
}
