package ins.channel.risk.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GGRISK的PO对象<br/>
 * 对应表名：GGRISK,备注：险种表
 *
 */
@Data
@Table(name = "GGRISK")
public class Ggrisk implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：RISK_CODE,备注：险种代码 */
	@Column(name = "RISK_CODE", description = "险种代码")
	private String riskCode;
	/** 对应字段：RISK_CNAME,备注：险种中文名称 */
	@Column(name = "RISK_CNAME", description = "险种中文名称")
	private String riskCname;
	/** 对应字段：RISK_TNAME,备注：险种繁体名称 */
	@Column(name = "RISK_TNAME", description = "险种繁体名称")
	private String riskTname;
	/** 对应字段：RISK_ENAME,备注：险种英文名称 */
	@Column(name = "RISK_ENAME", description = "险种英文名称")
	private String riskEname;
	/** 对应字段：RISK_CLASS,备注：险类 */
	@Column(name = "RISK_CLASS", description = "险类")
	private String riskClass;
	/** 对应字段：OPENCOVER_IND,备注：是否预约协议 */
	@Column(name = "OPENCOVER_IND", description = "是否预约协议")
	private String opencoverInd;
	/** 对应字段：VALIDDATE,备注：有效日期 */
	@Column(name = "VALIDDATE", description = "有效日期")
	private Date validDate;
	/** 对应字段：INVALIDDATE,备注：失效日期 */
	@Column(name = "INVALIDDATE", description = "失效日期")
	private Date invalidDate;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@Column(name = "VALID_IND", description = "有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@Column(name = "FLAG", description = "标志字段")
	private String flag;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@Column(name = "CREATE_TIME", description = "创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@Column(name = "MODIFIED_TIME", description = "更新时间")
	private Date modifiedTime;
}
