package ins.channel.risk.po;

import lombok.Data;

/**
 * 代码类型查询Po
 */
@Data
public class GgriskSearch {

    /** 对应字段：CODE_TYPE,备注：险种代码 */
    private String riskCode;

    /** 对应字段：COMPANY_CODE,备注：险类代码 */
    private String riskClass;

    /** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
    private String validInd;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;


}
