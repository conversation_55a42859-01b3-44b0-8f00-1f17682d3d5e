package ins.channel.risk.po;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GGCODE的PO对象<br/>
 * 对应表名：GGCODE,备注：代码配置表
 *
 */
@Data
public class GgriskSelect implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：RISK_CODE,备注：险种代码 */
	private String riskCode;
	/** 对应字段：RISK_CNAME,备注：险种中文名称 */
	private String riskCname;
}
