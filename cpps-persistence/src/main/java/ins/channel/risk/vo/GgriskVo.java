package ins.channel.risk.vo;

import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GgriskVo对象.对应实体描述：险种表
 *
 */
@Data
@ApiModel("GgriskVo对象")
public class GgriskVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：RISK_CODE,备注：险种代码 */
	@ApiModelProperty("险种代码")
	private String riskCode;
	/** 对应字段：RISK_CNAME,备注：险种中文名称 */
	@ApiModelProperty("险种中文名称")
	private String riskCname;
	/** 对应字段：RISK_TNAME,备注：险种繁体名称 */
	@ApiModelProperty("险种繁体名称")
	private String riskTname;
	/** 对应字段：RISK_ENAME,备注：险种英文名称 */
	@ApiModelProperty("险种英文名称")
	private String riskEname;
	/** 对应字段：RISK_CLASS,备注：险类 */
	@ApiModelProperty("险类")
	private String riskClass;
	/** 对应字段：OPENCOVER_IND,备注：是否预约协议 */
	@ApiModelProperty("是否预约协议")
	private String opencoverInd;
	/** 对应字段：VALIDDATE,备注：有效日期 */
	@ApiModelProperty("有效日期")
	private Date validDate;
	/** 对应字段：INVALIDDATE,备注：失效日期 */
	@ApiModelProperty("失效日期")
	private Date invalidDate;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	private Date modifiedTime;
}
