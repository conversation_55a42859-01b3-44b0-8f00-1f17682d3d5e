package ins.channel.systeminteractivelog.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import ins.platform.common.CreateTime;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GPSYSTEMINTERACTIVELOG的PO对象<br/>
 * 对应表名：GPSYSTEMINTERACTIVELOG,备注：系统交互报文存储表
 *
 */
@Data
@Table(name = "GPSYSTEMINTERACTIVELOG")
public class Gpsysteminteractivelog implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：REQUESTID,备注：UUID序列号 */
	@Column(name = "REQUESTID", description = "UUID序列号")
	private String requestid;
	/** 对应字段：MODELNAME,备注：模块名称 */
	@Column(name = "MODELNAME", description = "模块名称")
	private String modelName;
	/** 对应字段：REQUESTMESSAGE,备注：请求消息体 */
	@Column(name = "REQUESTMESSAGE", description = "请求消息体")
	private String requestmessage;
	/** 对应字段：RESPONSEMESSAGE,备注：响应消息体 */
	@Column(name = "RESPONSEMESSAGE", description = "响应消息体")
	private String responsemessage;
	/** 对应字段：THIRDINTERMESSAGE,备注：第三方系统交互请求响应消息 */
	@Column(name = "THIRDINTERMESSAGE", description = "第三方系统交互请求响应消息")
	private String thirdintermessage;
	/** 对应字段：RESPONSESTATUS,备注：响应状态码：0- 成功, 1- ESB失败, 2- 应用失败 */
	@Column(name = "RESPONSESTATUS", description = "响应状态码：0- 成功, 1- ESB失败, 2- 应用失败")
	private String responsestatus;
	/** 对应字段：INSERTTIME,备注：请求落地时间 */
	@Column(name = "INSERTTIME", description = "请求落地时间")
	@CreateTime
	private Date inserttime;
}
