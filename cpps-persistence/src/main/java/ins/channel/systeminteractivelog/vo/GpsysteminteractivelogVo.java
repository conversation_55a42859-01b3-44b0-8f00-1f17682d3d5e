package ins.channel.systeminteractivelog.vo;

import java.io.Serializable;
import java.util.Date;

import ins.platform.common.CreateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GpsysteminteractivelogVo对象.对应实体描述：系统交互报文存储表
 *
 */
@Data
@ApiModel("GpsysteminteractivelogVo对象")
public class GpsysteminteractivelogVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：REQUESTID,备注：UUID序列号 */
	@ApiModelProperty("UUID序列号")
	private String requestid;
	/** 对应字段：MODELNAME,备注：模块名称 */
	@ApiModelProperty("模块名称")
	private String modelName;
	/** 对应字段：REQUESTMESSAGE,备注：请求消息体 */
	@ApiModelProperty("请求消息体")
	private String requestmessage;
	/** 对应字段：RESPONSEMESSAGE,备注：响应消息体 */
	@ApiModelProperty("响应消息体")
	private String responsemessage;
	/** 对应字段：THIRDINTERMESSAGE,备注：第三方系统交互请求响应消息 */
	@ApiModelProperty("第三方系统交互请求响应消息")
	private String thirdintermessage;
	/** 对应字段：RESPONSESTATUS,备注：响应状态码：0- 成功, 1- ESB失败, 2- 应用失败 */
	@ApiModelProperty("响应状态码：0- 成功, 1- ESB失败, 2- 应用失败")
	private String responsestatus;
	/** 对应字段：INSERTTIME,备注：请求落地时间 */
	@ApiModelProperty("请求落地时间")
	@CreateTime
	private Date inserttime;
}
