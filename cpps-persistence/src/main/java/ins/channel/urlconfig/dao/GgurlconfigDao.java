package ins.channel.urlconfig.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.channel.urlconfig.po.Ggurlconfig;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 *
 * 表GGURLCONFIG对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GgurlconfigDao extends MybatisBaseDao<Ggurlconfig, String> {
	public List<Ggurlconfig> selectByCondition(Ggurlconfig ggUrlConfig);
}
