package ins.channel.urlconfig.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import ins.platform.common.CreateTime;
import ins.platform.common.ModifiedTime;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GGURLCONFIG的PO对象<br/>
 * 对应表名：GGURLCONFIG,备注：收付请求外部接口地址配置表
 *
 */
@Data
@Table(name = "GGURLCONFIG")
public class Ggurlconfig implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 PK */
	@Column(name = "ID", description = "主键 PK")
	private String id;
	/** 对应字段：SYSTEM_CODE,备注：系统代码 */
	@Column(name = "SYSTEM_CODE", description = "系统代码")
	private String systemCode;
	/** 对应字段：CODE_TYPE,备注：类型 */
	@Column(name = "CODE_TYPE", description = "类型")
	private String codeType;
	/** 对应字段：URL,备注：地址 */
	@Column(name = "URL", description = "地址")
	private String url;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@Column(name = "VALID_IND", description = "有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：FLAG,备注：标识 */
	@Column(name = "FLAG", description = "标识")
	private String flag;
	/** 对应字段：REMARK,备注：备注 */
	@Column(name = "REMARK", description = "备注")
	private String remark;
	/** 对应字段：CREATE_TIME,备注：记录插入时间 */
	@Column(name = "CREATE_TIME", description = "记录插入时间")
	@CreateTime
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：记录更新时间 */
	@Column(name = "MODIFIED_TIME", description = "记录更新时间")
	@ModifiedTime
	private Date modifiedTime;
}
