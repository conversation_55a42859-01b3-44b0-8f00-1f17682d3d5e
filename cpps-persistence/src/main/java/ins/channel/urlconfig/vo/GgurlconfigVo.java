package ins.channel.urlconfig.vo;

import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GgurlconfigVo对象.对应实体描述：收付请求外部接口地址配置表
 *
 */
@Data
@ApiModel("GgurlconfigVo对象")
public class GgurlconfigVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：ID,备注：主键 PK */
	@ApiModelProperty("主键 PK")
	private String id;
	/** 对应字段：SYSTEM_CODE,备注：系统代码 */
	@ApiModelProperty("系统代码")
	private String systemCode;
	/** 对应字段：CODE_TYPE,备注：类型 */
	@ApiModelProperty("类型")
	private String codeType;
	/** 对应字段：URL,备注：地址 */
	@ApiModelProperty("地址")
	private String url;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	private String validInd;
	/** 对应字段：FLAG,备注：标识 */
	@ApiModelProperty("标识")
	private String flag;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：CREATE_TIME,备注：记录插入时间 */
	@ApiModelProperty("记录插入时间")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：记录更新时间 */
	@ApiModelProperty("记录更新时间")
	private Date modifiedTime;
}
