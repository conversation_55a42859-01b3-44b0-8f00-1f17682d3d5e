package ins.channel.user.dao;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.baseCode.po.BaseCode;
import ins.channel.user.po.Gguser;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 *
 * 表GGUSER对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface GguserDao extends MybatisBaseDao<Gguser, String> {

    /**
     * 分页模糊查询
     * @param pageParam
     * @param clone
     * @return
     */
    Page<Gguser> pageByCondition(PageParam pageParam, Gguser clone);

    List<BaseCode> queryAll();

    List<Gguser> selectByCondition(Gguser gguser);

    List<Gguser> searchUserListByCompanyCode(String companyCode);


}
