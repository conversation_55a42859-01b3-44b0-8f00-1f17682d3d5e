package ins.channel.user.po;

import java.io.Serializable;
import java.util.Date;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表GGUSER的PO对象<br/>
 * 对应表名：GGUSER,备注：用户表
 *
 */
@Data
@Table(name = "GGUSER")
public class Gguser implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：USER_CODE */
	@Column(name = "USER_CODE")
	private String userCode;
	/** 对应字段：USER_CNAME */
	@Column(name = "USER_CNAME")
	private String userCname;
	/** 对应字段：USER_TNAME */
	@Column(name = "USER_TNAME")
	private String userTname;
	/** 对应字段：USER_ENAME */
	@Column(name = "USER_ENAME")
	private String userEname;
	/** 对应字段：PASSWORD */
	@Column(name = "PASSWORD")
	private String password;
	/** 对应字段：SEAL */
	@Column(name = "SEAL")
	private String seal;
	/** 对应字段：PASSWORD_SET_DATE */
	@Column(name = "PASSWORD_SET_DATE")
	private Date passwordSetDate;
	/** 对应字段：PASSWORD_EXPIRE_DATE */
	@Column(name = "PASSWORD_EXPIRE_DATE")
	private Date passwordExpireDate;
	/** 对应字段：COMPANY_CODE */
	@Column(name = "COMPANY_CODE")
	private String companyCode;
	/** 对应字段：ISSUE_COMPANY */
	@Column(name = "ISSUE_COMPANY")
	private String issueCompany;
	/** 对应字段：ACCOUNT_CODE */
	@Column(name = "ACCOUNT_CODE")
	private String accountCode;
	/** 对应字段：PHONE */
	@Column(name = "PHONE")
	private String phone;
	/** 对应字段：MOBILE */
	@Column(name = "MOBILE")
	private String mobile;
	/** 对应字段：ADDRESS */
	@Column(name = "ADDRESS")
	private String address;
	/** 对应字段：POST_CODE */
	@Column(name = "POST_CODE")
	private String postCode;
	/** 对应字段：EMAIL */
	@Column(name = "EMAIL")
	private String email;
	/** 对应字段：USER_IND */
	@Column(name = "USER_IND")
	private String userInd;
	/** 对应字段：LOGIN_SYSTEM */
	@Column(name = "LOGIN_SYSTEM")
	private String loginSystem;
	/** 对应字段：CREATOR_CODE */
	@Column(name = "CREATOR_CODE")
	private String creatorCode;
	/** 对应字段：CREATE_TIME */
	@Column(name = "CREATE_TIME")
	private Date createTime;
	/** 对应字段：UPDATER_CODE */
	@Column(name = "UPDATER_CODE")
	private String updaterCode;
	/** 对应字段：UPDATE_TIME */
	@Column(name = "UPDATE_TIME")
	private Date updateTime;
	/** 对应字段：VALID_IND */
	@Column(name = "VALID_IND")
	private String validInd;
	/** 对应字段：REMARK */
	@Column(name = "REMARK")
	private String remark;
	/** 对应字段：FLAG */
	@Column(name = "FLAG")
	private String flag;
	/** 对应字段：POWER_AUTH_IND */
	@Column(name = "POWER_AUTH_IND")
	private String powerAuthInd;
	/** 对应字段：IDENTIFY_NO */
	@Column(name = "IDENTIFY_NO")
	private String identifyNo;
	/** 对应字段：TEAM_MANAGER */
	@Column(name = "TEAM_MANAGER")
	private String teamManager;
	/** 对应字段：ACTION_URL */
	@Column(name = "ACTION_URL")
	private String actionUrl;
	/** 对应字段：MAC_NO */
	@Column(name = "MAC_NO")
	private String macNo;
	/** 对应字段：LAST_EDIT_DATE */
	@Column(name = "LAST_EDIT_DATE")
	private Date lastEditDate;
	/** 对应字段：REGISTER_NO */
	@Column(name = "REGISTER_NO")
	private String registerNo;
	/** 对应字段：OUTER_CODE */
	@Column(name = "OUTER_CODE")
	private String outerCode;
	/** 对应字段：COOPERATE_SITE_CODE */
	@Column(name = "COOPERATE_SITE_CODE")
	private String cooperateSiteCode;
	/** 对应字段：REQUEST_IND */
	@Column(name = "REQUEST_IND")
	private String requestInd;
	/** 对应字段：DEPARTMENT */
	@Column(name = "DEPARTMENT")
	private String department;
}
