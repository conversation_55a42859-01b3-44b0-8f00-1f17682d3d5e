package ins.channel.user.vo;

import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * GguserVo对象.对应实体描述：用户表
 *
 */
@Data
@ApiModel("GguserVo对象")
public class GguserVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：USER_CODE */
	@ApiModelProperty()
	private String userCode;
	/** 对应字段：USER_CNAME */
	@ApiModelProperty()
	private String userCname;
	/** 对应字段：USER_TNAME */
	@ApiModelProperty()
	private String userTname;
	/** 对应字段：USER_ENAME */
	@ApiModelProperty()
	private String userEname;
	/** 对应字段：PASSWORD */
	@ApiModelProperty()
	private String password;
	/** 对应字段：SEAL */
	@ApiModelProperty()
	private String seal;
	/** 对应字段：PASSWORD_SET_DATE */
	@ApiModelProperty()
	private Date passwordSetDate;
	/** 对应字段：PASSWORD_EXPIRE_DATE */
	@ApiModelProperty()
	private Date passwordExpireDate;
	/** 对应字段：COMPANY_CODE */
	@ApiModelProperty()
	private String companyCode;
	/** 对应字段：ISSUE_COMPANY */
	@ApiModelProperty()
	private String issueCompany;
	/** 对应字段：ACCOUNT_CODE */
	@ApiModelProperty()
	private String accountCode;
	/** 对应字段：PHONE */
	@ApiModelProperty()
	private String phone;
	/** 对应字段：MOBILE */
	@ApiModelProperty()
	private String mobile;
	/** 对应字段：ADDRESS */
	@ApiModelProperty()
	private String address;
	/** 对应字段：POST_CODE */
	@ApiModelProperty()
	private String postCode;
	/** 对应字段：EMAIL */
	@ApiModelProperty()
	private String email;
	/** 对应字段：USER_IND */
	@ApiModelProperty()
	private String userInd;
	/** 对应字段：LOGIN_SYSTEM */
	@ApiModelProperty()
	private String loginSystem;
	/** 对应字段：CREATOR_CODE */
	@ApiModelProperty()
	private String creatorCode;
	/** 对应字段：CREATE_TIME */
	@ApiModelProperty()
	private Date createTime;
	/** 对应字段：UPDATER_CODE */
	@ApiModelProperty()
	private String updaterCode;
	/** 对应字段：UPDATE_TIME */
	@ApiModelProperty()
	private Date updateTime;
	/** 对应字段：VALID_IND */
	@ApiModelProperty()
	private String validInd;
	/** 对应字段：REMARK */
	@ApiModelProperty()
	private String remark;
	/** 对应字段：FLAG */
	@ApiModelProperty()
	private String flag;
	/** 对应字段：POWER_AUTH_IND */
	@ApiModelProperty()
	private String powerAuthInd;
	/** 对应字段：IDENTIFY_NO */
	@ApiModelProperty()
	private String identifyNo;
	/** 对应字段：TEAM_MANAGER */
	@ApiModelProperty("团队长(所属公司名称)")
	private String teamManager;
	/** 对应字段：ACTION_URL */
	@ApiModelProperty()
	private String actionUrl;
	/** 对应字段：MAC_NO */
	@ApiModelProperty()
	private String macNo;
	/** 对应字段：LAST_EDIT_DATE */
	@ApiModelProperty()
	private Date lastEditDate;
	/** 对应字段：REGISTER_NO */
	@ApiModelProperty()
	private String registerNo;
	/** 对应字段：OUTER_CODE */
	@ApiModelProperty("所属公司代码")
	private String outerCode;
	/** 对应字段：COOPERATE_SITE_CODE */
	@ApiModelProperty()
	private String cooperateSiteCode;
	/** 对应字段：REQUEST_IND */
	@ApiModelProperty()
	private String requestInd;
	/** 对应字段：department */
	@ApiModelProperty("所属部门")
	private String department;
}
