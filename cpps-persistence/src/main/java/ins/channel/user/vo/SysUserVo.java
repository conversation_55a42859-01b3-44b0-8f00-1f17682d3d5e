package ins.channel.user.vo;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 *
 * SysUserVo对象.对应实体描述：系统用户表
 *
 */
@Data
@ApiModel("SysUserVo对象")
public class SysUserVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @NotBlank(message = "用户代码为空")
    @ApiModelProperty("用户代码")
    private String userCode;//用户代码
    @NotBlank(message = "用户姓名为空")
    @ApiModelProperty("用户姓名")
    private String userName;//用户姓名
    @NotBlank(message = "机构代码为空")
    @ApiModelProperty("机构代码")
    private String comCode;//机构代码
    @NotBlank(message = "邮箱为空")
    @ApiModelProperty("邮箱")
    private String email;//邮箱
//  @NotBlank(message = "电话为空")
    @ApiModelProperty("电话")
    private String mobilePhone;//电话
    @ApiModelProperty("性别")
    private String sex;//性别
//    @NotBlank(message = "职务为空")
    @ApiModelProperty("职务")
    private String jobTitle;//职务
    @ApiModelProperty("工号")
    private String jobNumber;//工号
    @NotBlank(message = "用户类型为空")
    @Pattern(regexp = "0|1|2|3|4|5", message = "用户类型:0-管理员用户,1-内部用户，2-接口用户，3-渠道用户，4-公估用户，5-外包用户")
    @ApiModelProperty("用户类型")
    private String userType;//用户类型:0-管理员用户,1-内部用户，2-接口用户，3-渠道用户，4-公估用户，5-外包用户
    @NotBlank(message = "用户状态为空")
    @ApiModelProperty("用户状态：-1锁定，0无效，1有效")
    private String validInd;//用户状态：-1锁定，0无效，1有效
    @ApiModelProperty("父级代码，只有用户类型是渠道用户时才有值")
    private String parentUserCode;//父级代码，只有用户类型是渠道用户时才有值
    @ApiModelProperty("1.N和空是主账号2.Y是子账号")
    private String luaHandlerFlag;//1.N和空是主账号2.Y是子账号
//    @NotBlank(message = "渠道代码为空")
    @ApiModelProperty("渠道代码")
    private String channelCode;//渠道代码
//    @NotBlank(message = "部门代码为空")
    @ApiModelProperty("部门代码")
    private String branchCode;//部门代码
    @ApiModelProperty("业务类型：1.\toffline-线下业务2.\tselling-互联网直销3.\tIntermediary-互联网中介")
    private String businessType;//业务类型：1.	offline-线下业务2.	selling-互联网直销3.	Intermediary-互联网中介
    @ApiModelProperty("跟进的内部销售人员代码")
    private String salesCode;//跟进的内部销售人员代码
    @ApiModelProperty("财务邮箱")
    private String financeMail;//财务邮箱
    @ApiModelProperty("财务联系人")
    private String financeMan;//财务联系人
    @ApiModelProperty("财务电话")
    private String financePhone;//财务电话
    @ApiModelProperty("用户属性")
    private List<UserProp> props;//用户属性

    private String regTime;//	String	TRUE	注册时间
    private BigDecimal lastLoginFailed;//	long	TRUE	最后登录失败次数
    private Date lastTime;//	Date	TRUE	最后登录成功时间
    private String lastIp;//	String	TRUE	最后登录成功IP
    private Date passwordSetDate;//	Date	TRUE	密码设置日期
    private Date passwordExpireDate;//	Date	TRUE	密码失效日期
    private String bussinesType;//	String	TRUE	业务类型：ffline-线下业务2.	selling-互联网直销3.	Intermediary-互联网中介
    private String financeEmail;//	String	TRUE	财务邮箱
    private String creatorCode;//创建人代码
    private Date createTime;//创建时间
    private String updaterCode;//更新人代码
    private Date updateTime;//更新时间
    private List<SysUserPropertyVo> properties;//用户属性

}
