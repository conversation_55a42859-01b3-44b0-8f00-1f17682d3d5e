package ins.channel.user.vo;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;


/**
 *
 * User对象.对应实体描述：系统用户表
 *
 */
@Data
@ApiModel("User对象")
public class User implements Serializable {
    private static final long serialVersionUID = 1L;
    @NotBlank(message = "操作类型不能为空")
    @Pattern(regexp = "ADD|DELETE|UPDATE", message = "operType字段只能是ADD|DELETE|UPDATE")
    @ApiModelProperty("操作类型")
    private String operType;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("数据同步时间(发送到队列的时间)")
    private Date operTime;
    @NotBlank(message = "用户代码不能为空")
    @ApiModelProperty("用户代码")
    private String operKey;
    @Valid
    @NotNull(message = "请求消息体不能为空")
    @ApiModelProperty("用户数据对象")
    private SysUserVo sysUser;

}
