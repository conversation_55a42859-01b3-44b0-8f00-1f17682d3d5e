<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.channel.dao.GgchannelDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.channel.po.Ggchannel">
		<id column="CHANNEL_CODE" property="channelCode" />
		<result column="CHANNEL_CNAME" property="channelCname" />
		<result column="CHANNEL_TNAME" property="channelTname" />
		<result column="CHANNEL_ENAME" property="channelEname" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="MODIFIED_TIME" property="modifiedTime" />
		<result column="VALID_IND" property="validInd" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		CHANNEL_CODE,
		CHANNEL_CNAME,
		CHANNEL_TNAME,
		CHANNEL_ENAME,
		CREATE_TIME,
		MODIFIED_TIME,
		VALID_IND,
		REMARK,
		FLAG
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="channelCode != null and channelCode != ''" >
			and CHANNEL_CODE = #{channelCode}
		</if>
		<if test="channelCname != null and channelCname != ''" >
			and CHANNEL_CNAME = #{channelCname}
		</if>
		<if test="channelTname != null and channelTname != ''" >
			and CHANNEL_TNAME = #{channelTname}
		</if>
		<if test="channelEname != null and channelEname != ''" >
			and CHANNEL_ENAME = #{channelEname}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="modifiedTime != null and modifiedTime != ''" >
			and MODIFIED_TIME = #{modifiedTime}
		</if>
		<if test="validInd != null and validInd != ''" >
			and VALID_IND = #{validInd}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GGCHANNEL
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGCHANNEL
		where CHANNEL_CODE = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGCHANNEL
		where CHANNEL_CODE in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.channel.po.Ggchannel">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GGCHANNEL
		where CHANNEL_CODE = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GGCHANNEL
		where CHANNEL_CODE in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.channel.po.Ggchannel">
		insert into GGCHANNEL (
			CHANNEL_CODE,
			CHANNEL_CNAME,
			CHANNEL_TNAME,
			CHANNEL_ENAME,
			CREATE_TIME,
			MODIFIED_TIME,
			VALID_IND,
			REMARK,
			FLAG
		) values (
			#{channelCode},
			#{channelCname},
			#{channelTname},
			#{channelEname},
			#{createTime},
			#{modifiedTime},
			#{validInd},
			#{remark},
			#{flag}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.channel.po.Ggchannel">
		insert into GGCHANNEL
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="channelCode != null" >
				CHANNEL_CODE,
			</if>
			<if test="channelCname != null" >
				CHANNEL_CNAME,
			</if>
			<if test="channelTname != null" >
				CHANNEL_TNAME,
			</if>
			<if test="channelEname != null" >
				CHANNEL_ENAME,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME,
			</if>
			<if test="validInd != null" >
				VALID_IND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="channelCode != null" >
				#{channelCode},
			</if>
			<if test="channelCname != null" >
				#{channelCname},
			</if>
			<if test="channelTname != null" >
				#{channelTname},
			</if>
			<if test="channelEname != null" >
				#{channelEname},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="modifiedTime != null" >
				#{modifiedTime},
			</if>
			<if test="validInd != null" >
				#{validInd},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.channel.po.Ggchannel">
		update GGCHANNEL 
		<set>
			<if test="channelCname != null" >
				CHANNEL_CNAME=#{channelCname},
			</if>
			<if test="channelTname != null" >
				CHANNEL_TNAME=#{channelTname},
			</if>
			<if test="channelEname != null" >
				CHANNEL_ENAME=#{channelEname},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME=#{modifiedTime},
			</if>
			<if test="validInd != null" >
				VALID_IND=#{validInd},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
		</set>
		where CHANNEL_CODE = #{channelCode }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.channel.po.Ggchannel">
		update GGCHANNEL set
			CHANNEL_CNAME=#{channelCname},
			CHANNEL_TNAME=#{channelTname},
			CHANNEL_ENAME=#{channelEname},
			CREATE_TIME=#{createTime},
			MODIFIED_TIME=#{modifiedTime},
			VALID_IND=#{validInd},
			REMARK=#{remark},
			FLAG=#{flag},
		where CHANNEL_CODE = #{channelCode}	</update>
</mapper>
