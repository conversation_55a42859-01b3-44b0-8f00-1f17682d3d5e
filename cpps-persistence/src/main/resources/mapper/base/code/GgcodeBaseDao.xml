<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.code.dao.GgcodeDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.code.po.Ggcode">
		<id column="GID" property="gid" />
		<result column="CODE_TYPE" property="codeType" />
		<result column="COMPANY_CODE" property="companyCode" />
		<result column="CODE_CODE" property="codeCode" />
		<result column="CODE_CNAME" property="codeCname" />
		<result column="CODE_TNAME" property="codeTname" />
		<result column="CODE_ENAME" property="codeEname" />
		<result column="VALID_IND" property="validInd" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="DISPLAY_NO" property="displayNo" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="MODIFIED_TIME" property="modifiedTime" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		GID,
		CODE_TYPE,
		COMPANY_CODE,
		CODE_CODE,
		CODE_CNAME,
		CODE_TNAME,
		CODE_ENAME,
		VALID_IND,
		REMARK,
		FLAG,
		DISPLAY_NO,
		CREATE_TIME,
		MODIFIED_TIME
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="gid != null and gid != ''" >
			and GID = #{gid}
		</if>
		<if test="codeType != null and codeType != ''" >
			and CODE_TYPE = #{codeType}
		</if>
		<if test="companyCode != null and companyCode != ''" >
			and COMPANY_CODE = #{companyCode}
		</if>
		<if test="codeCode != null and codeCode != ''" >
			and CODE_CODE = #{codeCode}
		</if>
		<if test="codeCname != null and codeCname != ''" >
			and CODE_CNAME = #{codeCname}
		</if>
		<if test="codeTname != null and codeTname != ''" >
			and CODE_TNAME = #{codeTname}
		</if>
		<if test="codeEname != null and codeEname != ''" >
			and CODE_ENAME = #{codeEname}
		</if>
		<if test="validInd != null and validInd != ''" >
			and VALID_IND = #{validInd}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="displayNo != null and displayNo != ''" >
			and DISPLAY_NO = #{displayNo}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="modifiedTime != null and modifiedTime != ''" >
			and MODIFIED_TIME = #{modifiedTime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GGCODE
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGCODE
		where GID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGCODE
		where GID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.code.po.Ggcode">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GGCODE
		where GID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GGCODE
		where GID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.code.po.Ggcode">
		insert into GGCODE (
			GID,
			CODE_TYPE,
			COMPANY_CODE,
			CODE_CODE,
			CODE_CNAME,
			CODE_TNAME,
			CODE_ENAME,
			VALID_IND,
			REMARK,
			FLAG,
			DISPLAY_NO,
			CREATE_TIME,
			MODIFIED_TIME
		) values (
			#{gid},
			#{codeType},
			#{companyCode},
			#{codeCode},
			#{codeCname},
			#{codeTname},
			#{codeEname},
			#{validInd},
			#{remark},
			#{flag},
			#{displayNo},
			#{createTime},
			#{modifiedTime}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.code.po.Ggcode">
		insert into GGCODE
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="gid != null" >
				GID,
			</if>
			<if test="codeType != null" >
				CODE_TYPE,
			</if>
			<if test="companyCode != null" >
				COMPANY_CODE,
			</if>
			<if test="codeCode != null" >
				CODE_CODE,
			</if>
			<if test="codeCname != null" >
				CODE_CNAME,
			</if>
			<if test="codeTname != null" >
				CODE_TNAME,
			</if>
			<if test="codeEname != null" >
				CODE_ENAME,
			</if>
			<if test="validInd != null" >
				VALID_IND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="displayNo != null" >
				DISPLAY_NO,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="gid != null" >
				#{gid},
			</if>
			<if test="codeType != null" >
				#{codeType},
			</if>
			<if test="companyCode != null" >
				#{companyCode},
			</if>
			<if test="codeCode != null" >
				#{codeCode},
			</if>
			<if test="codeCname != null" >
				#{codeCname},
			</if>
			<if test="codeTname != null" >
				#{codeTname},
			</if>
			<if test="codeEname != null" >
				#{codeEname},
			</if>
			<if test="validInd != null" >
				#{validInd},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="displayNo != null" >
				#{displayNo},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="modifiedTime != null" >
				#{modifiedTime}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.code.po.Ggcode">
		update GGCODE 
		<set>
			<if test="codeType != null" >
				CODE_TYPE=#{codeType},
			</if>
			<if test="companyCode != null" >
				COMPANY_CODE=#{companyCode},
			</if>
			<if test="codeCode != null" >
				CODE_CODE=#{codeCode},
			</if>
			<if test="codeCname != null" >
				CODE_CNAME=#{codeCname},
			</if>
			<if test="codeTname != null" >
				CODE_TNAME=#{codeTname},
			</if>
			<if test="codeEname != null" >
				CODE_ENAME=#{codeEname},
			</if>
			<if test="validInd != null" >
				VALID_IND=#{validInd},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="displayNo != null" >
				DISPLAY_NO=#{displayNo},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME=#{modifiedTime},
			</if>
		</set>
		where GID = #{gid }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.code.po.Ggcode">
		update GGCODE set
			CODE_TYPE=#{codeType},
			COMPANY_CODE=#{companyCode},
			CODE_CODE=#{codeCode},
			CODE_CNAME=#{codeCname},
			CODE_TNAME=#{codeTname},
			CODE_ENAME=#{codeEname},
			VALID_IND=#{validInd},
			REMARK=#{remark},
			FLAG=#{flag},
			DISPLAY_NO=#{displayNo},
			CREATE_TIME=#{createTime},
			MODIFIED_TIME=#{modifiedTime},
		where GID = #{gid}	</update>
</mapper>
