<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.codetransfer.dao.GfcodetransferDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.codetransfer.po.Gfcodetransfer">
		<id column="CODE_CODE" property="codeCode" />
		<id column="TRANS_TYPE" property="transType" />
		<result column="CODE_NAME" property="codeName" />
		<result column="TRANS_CODE_CODE" property="transCodeCode" />
		<result column="TRANS_CODE_NAME" property="transCodeName" />
		<result column="VALID_IND" property="validInd" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="MODIFIED_TIME" property="modifiedTime" />
		<result column="UPDATE_TIMES" property="updateTimes" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		TRANS_TYPE,
		CODE_CODE,
		CODE_NAME,
		TRANS_CODE_CODE,
		TRANS_CODE_NAME,
		VALID_IND,
		REMARK,
		FLAG,
		CREATE_TIME,
		MODIFIED_TIME,
		UPDATE_TIMES
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="transType != null and transType != ''" >
			and TRANS_TYPE = #{transType}
		</if>
		<if test="codeCode != null and codeCode != ''" >
			and CODE_CODE = #{codeCode}
		</if>
		<if test="codeName != null and codeName != ''" >
			and CODE_NAME = #{codeName}
		</if>
		<if test="transCodeCode != null and transCodeCode != ''" >
			and TRANS_CODE_CODE = #{transCodeCode}
		</if>
		<if test="transCodeName != null and transCodeName != ''" >
			and TRANS_CODE_NAME = #{transCodeName}
		</if>
		<if test="validInd != null and validInd != ''" >
			and VALID_IND = #{validInd}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="modifiedTime != null and modifiedTime != ''" >
			and MODIFIED_TIME = #{modifiedTime}
		</if>
		<if test="updateTimes != null and updateTimes != ''" >
			and UPDATE_TIMES = #{updateTimes}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GFCODETRANSFER
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GFCODETRANSFER
		<trim prefix="WHERE" prefixOverrides="AND">
			AND CODE_CODE = #{codeCode} 
			AND TRANS_TYPE = #{transType} 
		</trim> 
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GFCODETRANSFER		
		where
		<foreach item="item" index="index" collection="list" open="(" separator="OR" close=")">
			(
			CODE_CODE = #{item.codeCode} 
			AND TRANS_TYPE = #{item.transType} 
			)
		</foreach> 
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.codetransfer.po.Gfcodetransfer">
		<include refid="Base_Select_By_Entity" />
	</select>
	
	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GFCODETRANSFER 
		<trim prefix="WHERE" prefixOverrides="AND">
			AND CODE_CODE = #{codeCode} 
			AND TRANS_TYPE = #{transType} 
		</trim>
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GFCODETRANSFER
		where
		<foreach item="item" index="index" collection="list" open="(" separator="OR" close=")">
			(
			CODE_CODE = #{item.codeCode} 
			AND TRANS_TYPE = #{item.transType} 
			)
		</foreach> 
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.codetransfer.po.Gfcodetransfer">
		insert into GFCODETRANSFER 
		<trim prefix="(" suffix=")" suffixOverrides="," >
			TRANS_TYPE,
			CODE_CODE,
			CODE_NAME,
			TRANS_CODE_CODE,
			TRANS_CODE_NAME,
			VALID_IND,
			REMARK,
			FLAG,
			CREATE_TIME,
			MODIFIED_TIME,
			UPDATE_TIMES
		</trim>
		values 
		<trim prefix="(" suffix=")" suffixOverrides="," > 
			#{transType},
			#{codeCode},
			#{codeName},
			#{transCodeCode},
			#{transCodeName},
			#{validInd},
			#{remark},
			#{flag},
			#{createTime},
			#{modifiedTime},
			#{updateTimes}
		</trim>
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.codetransfer.po.Gfcodetransfer">
		insert into GFCODETRANSFER
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="transType != null" >
				TRANS_TYPE,
			</if>
			<if test="codeCode != null" >
				CODE_CODE,
			</if>
			<if test="codeName != null" >
				CODE_NAME,
			</if>
			<if test="transCodeCode != null" >
				TRANS_CODE_CODE,
			</if>
			<if test="transCodeName != null" >
				TRANS_CODE_NAME,
			</if>
			<if test="validInd != null" >
				VALID_IND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME,
			</if>
			<if test="updateTimes != null" >
				UPDATE_TIMES
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="transType != null" >
				#{transType},
			</if>
			<if test="codeCode != null" >
				#{codeCode},
			</if>
			<if test="codeName != null" >
				#{codeName},
			</if>
			<if test="transCodeCode != null" >
				#{transCodeCode},
			</if>
			<if test="transCodeName != null" >
				#{transCodeName},
			</if>
			<if test="validInd != null" >
				#{validInd},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="modifiedTime != null" >
				#{modifiedTime},
			</if>
			<if test="updateTimes != null" >
				#{updateTimes}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.codetransfer.po.Gfcodetransfer">
		update GFCODETRANSFER 
		<set>			
			<if test="codeName != null" >
				CODE_NAME=#{codeName},
			</if>
			<if test="transCodeCode != null" >
				TRANS_CODE_CODE=#{transCodeCode},
			</if>
			<if test="transCodeName != null" >
				TRANS_CODE_NAME=#{transCodeName},
			</if>
			<if test="validInd != null" >
				VALID_IND=#{validInd},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME=#{modifiedTime},
			</if>
			<if test="updateTimes != null" >
				UPDATE_TIMES=#{updateTimes},
			</if>
		</set>
		<trim prefix="WHERE" prefixOverrides="AND">
			AND CODE_CODE = #{codeCode} 
			AND TRANS_TYPE = #{transType} 
		</trim> 
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.codetransfer.po.Gfcodetransfer">
		update GFCODETRANSFER 
		<set>
			CODE_NAME=#{codeName},
			TRANS_CODE_CODE=#{transCodeCode},
			TRANS_CODE_NAME=#{transCodeName},
			VALID_IND=#{validInd},
			REMARK=#{remark},
			FLAG=#{flag},
			CREATE_TIME=#{createTime},
			MODIFIED_TIME=#{modifiedTime},
			UPDATE_TIMES=#{updateTimes},
		</set>
		<trim prefix="WHERE" prefixOverrides="AND">
			AND CODE_CODE = #{codeCode} 
			AND TRANS_TYPE = #{transType} 
		</trim> 
	</update>
</mapper>
