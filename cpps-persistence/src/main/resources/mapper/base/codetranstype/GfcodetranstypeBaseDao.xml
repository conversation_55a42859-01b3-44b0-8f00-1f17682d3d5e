<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.codetranstype.dao.GfcodetranstypeDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.codetranstype.po.Gfcodetranstype">
		<id column="GID" property="gid" />
		<result column="TRANS_TYPE" property="transType" />
		<result column="TRANS_TYPE_CDESC" property="transTypeCdesc" />
		<result column="TRANS_TYPE_TDESC" property="transTypeTdesc" />
		<result column="TRANS_TYPE_EDESC" property="transTypeEdesc" />
		<result column="VALID_IND" property="validInd" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="VAILD_DATE" property="vaildDate" />
		<result column="INVALID_DATE" property="invalidDate" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="MODIFIED_TIME" property="modifiedTime" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		GID,
		TRANS_TYPE,
		TRANS_TYPE_CDESC,
		TRANS_TYPE_TDESC,
		TRANS_TYPE_EDESC,
		VALID_IND,
		REMARK,
		FLAG,
		VAILD_DATE,
		INVALID_DATE,
		CREATE_TIME,
		MODIFIED_TIME
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="gid != null and gid != ''" >
			and GID = #{gid}
		</if>
		<if test="transType != null and transType != ''" >
			and TRANS_TYPE = #{transType}
		</if>
		<if test="transTypeCdesc != null and transTypeCdesc != ''" >
			and TRANS_TYPE_CDESC = #{transTypeCdesc}
		</if>
		<if test="transTypeTdesc != null and transTypeTdesc != ''" >
			and TRANS_TYPE_TDESC = #{transTypeTdesc}
		</if>
		<if test="transTypeEdesc != null and transTypeEdesc != ''" >
			and TRANS_TYPE_EDESC = #{transTypeEdesc}
		</if>
		<if test="validInd != null and validInd != ''" >
			and VALID_IND = #{validInd}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="vaildDate != null and vaildDate != ''" >
			and VAILD_DATE = #{vaildDate}
		</if>
		<if test="invalidDate != null and invalidDate != ''" >
			and INVALID_DATE = #{invalidDate}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="modifiedTime != null and modifiedTime != ''" >
			and MODIFIED_TIME = #{modifiedTime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GFCODETRANSTYPE
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GFCODETRANSTYPE
		where GID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GFCODETRANSTYPE
		where GID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.codetranstype.po.Gfcodetranstype">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GFCODETRANSTYPE
		where GID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GFCODETRANSTYPE
		where GID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.codetranstype.po.Gfcodetranstype">
		insert into GFCODETRANSTYPE (
			GID,
			TRANS_TYPE,
			TRANS_TYPE_CDESC,
			TRANS_TYPE_TDESC,
			TRANS_TYPE_EDESC,
			VALID_IND,
			REMARK,
			FLAG,
			VAILD_DATE,
			INVALID_DATE,
			CREATE_TIME,
			MODIFIED_TIME
		) values (
			#{gid},
			#{transType},
			#{transTypeCdesc},
			#{transTypeTdesc},
			#{transTypeEdesc},
			#{validInd},
			#{remark},
			#{flag},
			#{vaildDate},
			#{invalidDate},
			#{createTime},
			#{modifiedTime}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.codetranstype.po.Gfcodetranstype">
		insert into GFCODETRANSTYPE
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="gid != null" >
				GID,
			</if>
			<if test="transType != null" >
				TRANS_TYPE,
			</if>
			<if test="transTypeCdesc != null" >
				TRANS_TYPE_CDESC,
			</if>
			<if test="transTypeTdesc != null" >
				TRANS_TYPE_TDESC,
			</if>
			<if test="transTypeEdesc != null" >
				TRANS_TYPE_EDESC,
			</if>
			<if test="validInd != null" >
				VALID_IND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="vaildDate != null" >
				VAILD_DATE,
			</if>
			<if test="invalidDate != null" >
				INVALID_DATE,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="gid != null" >
				#{gid},
			</if>
			<if test="transType != null" >
				#{transType},
			</if>
			<if test="transTypeCdesc != null" >
				#{transTypeCdesc},
			</if>
			<if test="transTypeTdesc != null" >
				#{transTypeTdesc},
			</if>
			<if test="transTypeEdesc != null" >
				#{transTypeEdesc},
			</if>
			<if test="validInd != null" >
				#{validInd},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="vaildDate != null" >
				#{vaildDate},
			</if>
			<if test="invalidDate != null" >
				#{invalidDate},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="modifiedTime != null" >
				#{modifiedTime}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.codetranstype.po.Gfcodetranstype">
		update GFCODETRANSTYPE 
		<set>
			<if test="transType != null" >
				TRANS_TYPE=#{transType},
			</if>
			<if test="transTypeCdesc != null" >
				TRANS_TYPE_CDESC=#{transTypeCdesc},
			</if>
			<if test="transTypeTdesc != null" >
				TRANS_TYPE_TDESC=#{transTypeTdesc},
			</if>
			<if test="transTypeEdesc != null" >
				TRANS_TYPE_EDESC=#{transTypeEdesc},
			</if>
			<if test="validInd != null" >
				VALID_IND=#{validInd},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="vaildDate != null" >
				VAILD_DATE=#{vaildDate},
			</if>
			<if test="invalidDate != null" >
				INVALID_DATE=#{invalidDate},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME=#{modifiedTime},
			</if>
		</set>
		where GID = #{gid }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.codetranstype.po.Gfcodetranstype">
		update GFCODETRANSTYPE set
			TRANS_TYPE=#{transType},
			TRANS_TYPE_CDESC=#{transTypeCdesc},
			TRANS_TYPE_TDESC=#{transTypeTdesc},
			TRANS_TYPE_EDESC=#{transTypeEdesc},
			VALID_IND=#{validInd},
			REMARK=#{remark},
			FLAG=#{flag},
			VAILD_DATE=#{vaildDate},
			INVALID_DATE=#{invalidDate},
			CREATE_TIME=#{createTime},
			MODIFIED_TIME=#{modifiedTime},
		where GID = #{gid}	</update>
</mapper>
