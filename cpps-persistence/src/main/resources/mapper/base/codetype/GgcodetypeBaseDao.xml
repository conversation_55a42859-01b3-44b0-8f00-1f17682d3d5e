<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.codetype.dao.GgcodetypeDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.codetype.po.Ggcodetype">
		<id column="GID" property="gid" />
		<result column="CODE_TYPE" property="codeType" />
		<result column="CODE_TYPE_CDESC" property="codeTypeCdesc" />
		<result column="CODE_TYPE_TDESC" property="codeTypeTdesc" />
		<result column="CODE_TYPE_EDESC" property="codeTypeEdesc" />
		<result column="VALID_IND" property="validInd" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="MODIFIED_TIME" property="modifiedTime" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		GID,
		CODE_TYPE,
		CODE_TYPE_CDESC,
		CODE_TYPE_TDESC,
		CODE_TYPE_EDESC,
		VALID_IND,
		REMARK,
		FLAG,
		CREATE_TIME,
		MODIFIED_TIME
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="gid != null and gid != ''" >
			and GID = #{gid}
		</if>
		<if test="codeType != null and codeType != ''" >
			and CODE_TYPE = #{codeType}
		</if>
		<if test="codeTypeCdesc != null and codeTypeCdesc != ''" >
			and CODE_TYPE_CDESC = #{codeTypeCdesc}
		</if>
		<if test="codeTypeTdesc != null and codeTypeTdesc != ''" >
			and CODE_TYPE_TDESC = #{codeTypeTdesc}
		</if>
		<if test="codeTypeEdesc != null and codeTypeEdesc != ''" >
			and CODE_TYPE_EDESC = #{codeTypeEdesc}
		</if>
		<if test="validInd != null and validInd != ''" >
			and VALID_IND = #{validInd}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="modifiedTime != null and modifiedTime != ''" >
			and MODIFIED_TIME = #{modifiedTime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GGCODETYPE
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGCODETYPE
		where GID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGCODETYPE
		where GID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.codetype.po.Ggcodetype">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GGCODETYPE
		where GID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GGCODETYPE
		where GID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.codetype.po.Ggcodetype">
		insert into GGCODETYPE (
			GID,
			CODE_TYPE,
			CODE_TYPE_CDESC,
			CODE_TYPE_TDESC,
			CODE_TYPE_EDESC,
			VALID_IND,
			REMARK,
			FLAG,
			CREATE_TIME,
			MODIFIED_TIME
		) values (
			#{gid},
			#{codeType},
			#{codeTypeCdesc},
			#{codeTypeTdesc},
			#{codeTypeEdesc},
			#{validInd},
			#{remark},
			#{flag},
			#{createTime},
			#{modifiedTime}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.codetype.po.Ggcodetype">
		insert into GGCODETYPE
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="gid != null" >
				GID,
			</if>
			<if test="codeType != null" >
				CODE_TYPE,
			</if>
			<if test="codeTypeCdesc != null" >
				CODE_TYPE_CDESC,
			</if>
			<if test="codeTypeTdesc != null" >
				CODE_TYPE_TDESC,
			</if>
			<if test="codeTypeEdesc != null" >
				CODE_TYPE_EDESC,
			</if>
			<if test="validInd != null" >
				VALID_IND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="gid != null" >
				#{gid},
			</if>
			<if test="codeType != null" >
				#{codeType},
			</if>
			<if test="codeTypeCdesc != null" >
				#{codeTypeCdesc},
			</if>
			<if test="codeTypeTdesc != null" >
				#{codeTypeTdesc},
			</if>
			<if test="codeTypeEdesc != null" >
				#{codeTypeEdesc},
			</if>
			<if test="validInd != null" >
				#{validInd},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="modifiedTime != null" >
				#{modifiedTime}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.codetype.po.Ggcodetype">
		update GGCODETYPE 
		<set>
			<if test="codeType != null" >
				CODE_TYPE=#{codeType},
			</if>
			<if test="codeTypeCdesc != null" >
				CODE_TYPE_CDESC=#{codeTypeCdesc},
			</if>
			<if test="codeTypeTdesc != null" >
				CODE_TYPE_TDESC=#{codeTypeTdesc},
			</if>
			<if test="codeTypeEdesc != null" >
				CODE_TYPE_EDESC=#{codeTypeEdesc},
			</if>
			<if test="validInd != null" >
				VALID_IND=#{validInd},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME=#{modifiedTime},
			</if>
		</set>
		where GID = #{gid }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.codetype.po.Ggcodetype">
		update GGCODETYPE set
			CODE_TYPE=#{codeType},
			CODE_TYPE_CDESC=#{codeTypeCdesc},
			CODE_TYPE_TDESC=#{codeTypeTdesc},
			CODE_TYPE_EDESC=#{codeTypeEdesc},
			VALID_IND=#{validInd},
			REMARK=#{remark},
			FLAG=#{flag},
			CREATE_TIME=#{createTime},
			MODIFIED_TIME=#{modifiedTime},
		where GID = #{gid}	</update>
</mapper>
