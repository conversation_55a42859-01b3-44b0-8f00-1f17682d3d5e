<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.company.dao.GgcompanyDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.company.po.Ggcompany">
		<id column="COMPANY_CODE" property="companyCode" />
		<result column="COMPANY_CNAME" property="companyCname" />
		<result column="COMPANY_TNAME" property="companyTname" />
		<result column="COMPANY_ENAME" property="companyEname" />
		<result column="ADDRESS_CNAME" property="addressCname" />
		<result column="ADDRESS_TNAME" property="addressTname" />
		<result column="ADDRESS_ENAME" property="addressEname" />
		<result column="INSURER_CNAME" property="insurerCname" />
		<result column="INSURER_TNAME" property="insurerTname" />
		<result column="INSURER_ENAME" property="insurerEname" />
		<result column="UPPER_COMPANY_CODE" property="upperCompanyCode" />
		<result column="COM_ATTRIBUTE" property="comAttribute" />
		<result column="COM_TYPE" property="comType" />
		<result column="CENTER_IND" property="centerInd" />
		<result column="COM_LEVEL" property="comLevel" />
		<result column="POST_CODE" property="postCode" />
		<result column="PHONE_NUMBER" property="phoneNumber" />
		<result column="FAX_NUMBER" property="faxNumber" />
		<result column="MANAGER" property="manager" />
		<result column="WEB_ADDRESS" property="webAddress" />
		<result column="SERVICE_PHONE" property="servicePhone" />
		<result column="REPORT_PHONE" property="reportPhone" />
		<result column="CREATOR_CODE" property="creatorCode" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="UPDATER_CODE" property="updaterCode" />
		<result column="UPDATE_TIME" property="updateTime" />
		<result column="VALID_IND" property="validInd" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="SHORT_CNAME" property="shortCname" />
		<result column="SHORT_TNAME" property="shortTname" />
		<result column="SHORT_ENAME" property="shortEname" />
		<result column="TAX_NUMBER" property="taxNumber" />
		<result column="EMAIL" property="email" />
		<result column="PRINT_POLICY_COM_CODE" property="printPolicyComCode" />
		<result column="PRINTIN_VOICE_COM_CODE" property="printinVoiceComCode" />
		<result column="CITY_CODE" property="cityCode" />
		<result column="REQUEST_IND" property="requestInd" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		COMPANY_CODE,
		COMPANY_CNAME,
		COMPANY_TNAME,
		COMPANY_ENAME,
		ADDRESS_CNAME,
		ADDRESS_TNAME,
		ADDRESS_ENAME,
		INSURER_CNAME,
		INSURER_TNAME,
		INSURER_ENAME,
		UPPER_COMPANY_CODE,
		COM_ATTRIBUTE,
		COM_TYPE,
		CENTER_IND,
		COM_LEVEL,
		POST_CODE,
		PHONE_NUMBER,
		FAX_NUMBER,
		MANAGER,
		WEB_ADDRESS,
		SERVICE_PHONE,
		REPORT_PHONE,
		CREATOR_CODE,
		CREATE_TIME,
		UPDATER_CODE,
		UPDATE_TIME,
		VALID_IND,
		REMARK,
		FLAG,
		SHORT_CNAME,
		SHORT_TNAME,
		SHORT_ENAME,
		TAX_NUMBER,
		EMAIL,
		PRINT_POLICY_COM_CODE,
		PRINTIN_VOICE_COM_CODE,
		CITY_CODE,
		REQUEST_IND
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="companyCode != null and companyCode != ''" >
			and COMPANY_CODE = #{companyCode}
		</if>
		<if test="companyCname != null and companyCname != ''" >
			and COMPANY_CNAME = #{companyCname}
		</if>
		<if test="companyTname != null and companyTname != ''" >
			and COMPANY_TNAME = #{companyTname}
		</if>
		<if test="companyEname != null and companyEname != ''" >
			and COMPANY_ENAME = #{companyEname}
		</if>
		<if test="addressCname != null and addressCname != ''" >
			and ADDRESS_CNAME = #{addressCname}
		</if>
		<if test="addressTname != null and addressTname != ''" >
			and ADDRESS_TNAME = #{addressTname}
		</if>
		<if test="addressEname != null and addressEname != ''" >
			and ADDRESS_ENAME = #{addressEname}
		</if>
		<if test="insurerCname != null and insurerCname != ''" >
			and INSURER_CNAME = #{insurerCname}
		</if>
		<if test="insurerTname != null and insurerTname != ''" >
			and INSURER_TNAME = #{insurerTname}
		</if>
		<if test="insurerEname != null and insurerEname != ''" >
			and INSURER_ENAME = #{insurerEname}
		</if>
		<if test="upperCompanyCode != null and upperCompanyCode != ''" >
			and UPPER_COMPANY_CODE = #{upperCompanyCode}
		</if>
		<if test="comAttribute != null and comAttribute != ''" >
			and COM_ATTRIBUTE = #{comAttribute}
		</if>
		<if test="comType != null and comType != ''" >
			and COM_TYPE = #{comType}
		</if>
		<if test="centerInd != null and centerInd != ''" >
			and CENTER_IND = #{centerInd}
		</if>
		<if test="comLevel != null and comLevel != ''" >
			and COM_LEVEL = #{comLevel}
		</if>
		<if test="postCode != null and postCode != ''" >
			and POST_CODE = #{postCode}
		</if>
		<if test="phoneNumber != null and phoneNumber != ''" >
			and PHONE_NUMBER = #{phoneNumber}
		</if>
		<if test="faxNumber != null and faxNumber != ''" >
			and FAX_NUMBER = #{faxNumber}
		</if>
		<if test="manager != null and manager != ''" >
			and MANAGER = #{manager}
		</if>
		<if test="webAddress != null and webAddress != ''" >
			and WEB_ADDRESS = #{webAddress}
		</if>
		<if test="servicePhone != null and servicePhone != ''" >
			and SERVICE_PHONE = #{servicePhone}
		</if>
		<if test="reportPhone != null and reportPhone != ''" >
			and REPORT_PHONE = #{reportPhone}
		</if>
		<if test="creatorCode != null and creatorCode != ''" >
			and CREATOR_CODE = #{creatorCode}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="updaterCode != null and updaterCode != ''" >
			and UPDATER_CODE = #{updaterCode}
		</if>
		<if test="updateTime != null and updateTime != ''" >
			and UPDATE_TIME = #{updateTime}
		</if>
		<if test="validInd != null and validInd != ''" >
			and VALID_IND = #{validInd}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="shortCname != null and shortCname != ''" >
			and SHORT_CNAME = #{shortCname}
		</if>
		<if test="shortTname != null and shortTname != ''" >
			and SHORT_TNAME = #{shortTname}
		</if>
		<if test="shortEname != null and shortEname != ''" >
			and SHORT_ENAME = #{shortEname}
		</if>
		<if test="taxNumber != null and taxNumber != ''" >
			and TAX_NUMBER = #{taxNumber}
		</if>
		<if test="email != null and email != ''" >
			and EMAIL = #{email}
		</if>
		<if test="printPolicyComCode != null and printPolicyComCode != ''" >
			and PRINT_POLICY_COM_CODE = #{printPolicyComCode}
		</if>
		<if test="printinVoiceComCode != null and printinVoiceComCode != ''" >
			and PRINTIN_VOICE_COM_CODE = #{printinVoiceComCode}
		</if>
		<if test="cityCode != null and cityCode != ''" >
			and CITY_CODE = #{cityCode}
		</if>
		<if test="requestInd != null and requestInd != ''" >
			and REQUEST_IND = #{requestInd}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GGCOMPANY
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGCOMPANY
		where COMPANY_CODE = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGCOMPANY
		where COMPANY_CODE in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.company.po.Ggcompany">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GGCOMPANY
		where COMPANY_CODE = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GGCOMPANY
		where COMPANY_CODE in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.company.po.Ggcompany">
		insert into GGCOMPANY (
			COMPANY_CODE,
			COMPANY_CNAME,
			COMPANY_TNAME,
			COMPANY_ENAME,
			ADDRESS_CNAME,
			ADDRESS_TNAME,
			ADDRESS_ENAME,
			INSURER_CNAME,
			INSURER_TNAME,
			INSURER_ENAME,
			UPPER_COMPANY_CODE,
			COM_ATTRIBUTE,
			COM_TYPE,
			CENTER_IND,
			COM_LEVEL,
			POST_CODE,
			PHONE_NUMBER,
			FAX_NUMBER,
			MANAGER,
			WEB_ADDRESS,
			SERVICE_PHONE,
			REPORT_PHONE,
			CREATOR_CODE,
			CREATE_TIME,
			UPDATER_CODE,
			UPDATE_TIME,
			VALID_IND,
			REMARK,
			FLAG,
			SHORT_CNAME,
			SHORT_TNAME,
			SHORT_ENAME,
			TAX_NUMBER,
			EMAIL,
			PRINT_POLICY_COM_CODE,
			PRINTIN_VOICE_COM_CODE,
			CITY_CODE,
			REQUEST_IND
		) values (
			#{companyCode},
			#{companyCname},
			#{companyTname},
			#{companyEname},
			#{addressCname},
			#{addressTname},
			#{addressEname},
			#{insurerCname},
			#{insurerTname},
			#{insurerEname},
			#{upperCompanyCode},
			#{comAttribute},
			#{comType},
			#{centerInd},
			#{comLevel},
			#{postCode},
			#{phoneNumber},
			#{faxNumber},
			#{manager},
			#{webAddress},
			#{servicePhone},
			#{reportPhone},
			#{creatorCode},
			#{createTime},
			#{updaterCode},
			#{updateTime},
			#{validInd},
			#{remark},
			#{flag},
			#{shortCname},
			#{shortTname},
			#{shortEname},
			#{taxNumber},
			#{email},
			#{printPolicyComCode},
			#{printinVoiceComCode},
			#{cityCode},
			#{requestInd}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.company.po.Ggcompany">
		insert into GGCOMPANY
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="companyCode != null" >
				COMPANY_CODE,
			</if>
			<if test="companyCname != null" >
				COMPANY_CNAME,
			</if>
			<if test="companyTname != null" >
				COMPANY_TNAME,
			</if>
			<if test="companyEname != null" >
				COMPANY_ENAME,
			</if>
			<if test="addressCname != null" >
				ADDRESS_CNAME,
			</if>
			<if test="addressTname != null" >
				ADDRESS_TNAME,
			</if>
			<if test="addressEname != null" >
				ADDRESS_ENAME,
			</if>
			<if test="insurerCname != null" >
				INSURER_CNAME,
			</if>
			<if test="insurerTname != null" >
				INSURER_TNAME,
			</if>
			<if test="insurerEname != null" >
				INSURER_ENAME,
			</if>
			<if test="upperCompanyCode != null" >
				UPPER_COMPANY_CODE,
			</if>
			<if test="comAttribute != null" >
				COM_ATTRIBUTE,
			</if>
			<if test="comType != null" >
				COM_TYPE,
			</if>
			<if test="centerInd != null" >
				CENTER_IND,
			</if>
			<if test="comLevel != null" >
				COM_LEVEL,
			</if>
			<if test="postCode != null" >
				POST_CODE,
			</if>
			<if test="phoneNumber != null" >
				PHONE_NUMBER,
			</if>
			<if test="faxNumber != null" >
				FAX_NUMBER,
			</if>
			<if test="manager != null" >
				MANAGER,
			</if>
			<if test="webAddress != null" >
				WEB_ADDRESS,
			</if>
			<if test="servicePhone != null" >
				SERVICE_PHONE,
			</if>
			<if test="reportPhone != null" >
				REPORT_PHONE,
			</if>
			<if test="creatorCode != null" >
				CREATOR_CODE,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="updaterCode != null" >
				UPDATER_CODE,
			</if>
			<if test="updateTime != null" >
				UPDATE_TIME,
			</if>
			<if test="validInd != null" >
				VALID_IND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="shortCname != null" >
				SHORT_CNAME,
			</if>
			<if test="shortTname != null" >
				SHORT_TNAME,
			</if>
			<if test="shortEname != null" >
				SHORT_ENAME,
			</if>
			<if test="taxNumber != null" >
				TAX_NUMBER,
			</if>
			<if test="email != null" >
				EMAIL,
			</if>
			<if test="printPolicyComCode != null" >
				PRINT_POLICY_COM_CODE,
			</if>
			<if test="printinVoiceComCode != null" >
				PRINTIN_VOICE_COM_CODE,
			</if>
			<if test="cityCode != null" >
				CITY_CODE,
			</if>
			<if test="requestInd != null" >
				REQUEST_IND
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="companyCode != null" >
				#{companyCode},
			</if>
			<if test="companyCname != null" >
				#{companyCname},
			</if>
			<if test="companyTname != null" >
				#{companyTname},
			</if>
			<if test="companyEname != null" >
				#{companyEname},
			</if>
			<if test="addressCname != null" >
				#{addressCname},
			</if>
			<if test="addressTname != null" >
				#{addressTname},
			</if>
			<if test="addressEname != null" >
				#{addressEname},
			</if>
			<if test="insurerCname != null" >
				#{insurerCname},
			</if>
			<if test="insurerTname != null" >
				#{insurerTname},
			</if>
			<if test="insurerEname != null" >
				#{insurerEname},
			</if>
			<if test="upperCompanyCode != null" >
				#{upperCompanyCode},
			</if>
			<if test="comAttribute != null" >
				#{comAttribute},
			</if>
			<if test="comType != null" >
				#{comType},
			</if>
			<if test="centerInd != null" >
				#{centerInd},
			</if>
			<if test="comLevel != null" >
				#{comLevel},
			</if>
			<if test="postCode != null" >
				#{postCode},
			</if>
			<if test="phoneNumber != null" >
				#{phoneNumber},
			</if>
			<if test="faxNumber != null" >
				#{faxNumber},
			</if>
			<if test="manager != null" >
				#{manager},
			</if>
			<if test="webAddress != null" >
				#{webAddress},
			</if>
			<if test="servicePhone != null" >
				#{servicePhone},
			</if>
			<if test="reportPhone != null" >
				#{reportPhone},
			</if>
			<if test="creatorCode != null" >
				#{creatorCode},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="updaterCode != null" >
				#{updaterCode},
			</if>
			<if test="updateTime != null" >
				#{updateTime},
			</if>
			<if test="validInd != null" >
				#{validInd},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="shortCname != null" >
				#{shortCname},
			</if>
			<if test="shortTname != null" >
				#{shortTname},
			</if>
			<if test="shortEname != null" >
				#{shortEname},
			</if>
			<if test="taxNumber != null" >
				#{taxNumber},
			</if>
			<if test="email != null" >
				#{email},
			</if>
			<if test="printPolicyComCode != null" >
				#{printPolicyComCode},
			</if>
			<if test="printinVoiceComCode != null" >
				#{printinVoiceComCode},
			</if>
			<if test="cityCode != null" >
				#{cityCode},
			</if>
			<if test="requestInd != null" >
				#{requestInd}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.company.po.Ggcompany">
		update GGCOMPANY 
		<set>
			<if test="companyCname != null" >
				COMPANY_CNAME=#{companyCname},
			</if>
			<if test="companyTname != null" >
				COMPANY_TNAME=#{companyTname},
			</if>
			<if test="companyEname != null" >
				COMPANY_ENAME=#{companyEname},
			</if>
			<if test="addressCname != null" >
				ADDRESS_CNAME=#{addressCname},
			</if>
			<if test="addressTname != null" >
				ADDRESS_TNAME=#{addressTname},
			</if>
			<if test="addressEname != null" >
				ADDRESS_ENAME=#{addressEname},
			</if>
			<if test="insurerCname != null" >
				INSURER_CNAME=#{insurerCname},
			</if>
			<if test="insurerTname != null" >
				INSURER_TNAME=#{insurerTname},
			</if>
			<if test="insurerEname != null" >
				INSURER_ENAME=#{insurerEname},
			</if>
			<if test="upperCompanyCode != null" >
				UPPER_COMPANY_CODE=#{upperCompanyCode},
			</if>
			<if test="comAttribute != null" >
				COM_ATTRIBUTE=#{comAttribute},
			</if>
			<if test="comType != null" >
				COM_TYPE=#{comType},
			</if>
			<if test="centerInd != null" >
				CENTER_IND=#{centerInd},
			</if>
			<if test="comLevel != null" >
				COM_LEVEL=#{comLevel},
			</if>
			<if test="postCode != null" >
				POST_CODE=#{postCode},
			</if>
			<if test="phoneNumber != null" >
				PHONE_NUMBER=#{phoneNumber},
			</if>
			<if test="faxNumber != null" >
				FAX_NUMBER=#{faxNumber},
			</if>
			<if test="manager != null" >
				MANAGER=#{manager},
			</if>
			<if test="webAddress != null" >
				WEB_ADDRESS=#{webAddress},
			</if>
			<if test="servicePhone != null" >
				SERVICE_PHONE=#{servicePhone},
			</if>
			<if test="reportPhone != null" >
				REPORT_PHONE=#{reportPhone},
			</if>
			<if test="creatorCode != null" >
				CREATOR_CODE=#{creatorCode},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="updaterCode != null" >
				UPDATER_CODE=#{updaterCode},
			</if>
			<if test="updateTime != null" >
				UPDATE_TIME=#{updateTime},
			</if>
			<if test="validInd != null" >
				VALID_IND=#{validInd},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="shortCname != null" >
				SHORT_CNAME=#{shortCname},
			</if>
			<if test="shortTname != null" >
				SHORT_TNAME=#{shortTname},
			</if>
			<if test="shortEname != null" >
				SHORT_ENAME=#{shortEname},
			</if>
			<if test="taxNumber != null" >
				TAX_NUMBER=#{taxNumber},
			</if>
			<if test="email != null" >
				EMAIL=#{email},
			</if>
			<if test="printPolicyComCode != null" >
				PRINT_POLICY_COM_CODE=#{printPolicyComCode},
			</if>
			<if test="printinVoiceComCode != null" >
				PRINTIN_VOICE_COM_CODE=#{printinVoiceComCode},
			</if>
			<if test="cityCode != null" >
				CITY_CODE=#{cityCode},
			</if>
			<if test="requestInd != null" >
				REQUEST_IND=#{requestInd},
			</if>
		</set>
		where COMPANY_CODE = #{companyCode }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.company.po.Ggcompany">
		update GGCOMPANY set
			COMPANY_CNAME=#{companyCname},
			COMPANY_TNAME=#{companyTname},
			COMPANY_ENAME=#{companyEname},
			ADDRESS_CNAME=#{addressCname},
			ADDRESS_TNAME=#{addressTname},
			ADDRESS_ENAME=#{addressEname},
			INSURER_CNAME=#{insurerCname},
			INSURER_TNAME=#{insurerTname},
			INSURER_ENAME=#{insurerEname},
			UPPER_COMPANY_CODE=#{upperCompanyCode},
			COM_ATTRIBUTE=#{comAttribute},
			COM_TYPE=#{comType},
			CENTER_IND=#{centerInd},
			COM_LEVEL=#{comLevel},
			POST_CODE=#{postCode},
			PHONE_NUMBER=#{phoneNumber},
			FAX_NUMBER=#{faxNumber},
			MANAGER=#{manager},
			WEB_ADDRESS=#{webAddress},
			SERVICE_PHONE=#{servicePhone},
			REPORT_PHONE=#{reportPhone},
			CREATOR_CODE=#{creatorCode},
			CREATE_TIME=#{createTime},
			UPDATER_CODE=#{updaterCode},
			UPDATE_TIME=#{updateTime},
			VALID_IND=#{validInd},
			REMARK=#{remark},
			FLAG=#{flag},
			SHORT_CNAME=#{shortCname},
			SHORT_TNAME=#{shortTname},
			SHORT_ENAME=#{shortEname},
			TAX_NUMBER=#{taxNumber},
			EMAIL=#{email},
			PRINT_POLICY_COM_CODE=#{printPolicyComCode},
			PRINTIN_VOICE_COM_CODE=#{printinVoiceComCode},
			CITY_CODE=#{cityCode},
			REQUEST_IND=#{requestInd},
		where COMPANY_CODE = #{companyCode}	</update>
</mapper>
