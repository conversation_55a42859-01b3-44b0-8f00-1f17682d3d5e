<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.currency.dao.GgcurrencyDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.currency.po.Ggcurrency">
		<id column="GID" property="gid" />
		<result column="CURRENCY_CODE" property="currencyCode" />
		<result column="CURRENCY_CNAME" property="currencyCname" />
		<result column="CURRENCY_TNAME" property="currencyTname" />
		<result column="CURRENCY_ENAME" property="currencyEname" />
		<result column="CREATOR_CODE" property="creatorCode" />
		<result column="UPDATER_CODE" property="updaterCode" />
		<result column="VALID_DATE" property="validDate" />
		<result column="INVALID_DATE" property="invalidDate" />
		<result column="VALID_IND" property="validInd" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="CREATE_BY" property="createBy" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="MODIFIED_BY" property="modifiedBy" />
		<result column="MODIFIED_TIME" property="modifiedTime" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		GID,
		CURRENCY_CODE,
		CURRENCY_CNAME,
		CURRENCY_TNAME,
		CURRENCY_ENAME,
		CREATOR_CODE,
		UPDATER_CODE,
		VALID_DATE,
		INVALID_DATE,
		VALID_IND,
		REMARK,
		FLAG,
		CREATE_BY,
		CREATE_TIME,
		MODIFIED_BY,
		MODIFIED_TIME
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="gid != null and gid != ''" >
			and GID = #{gid}
		</if>
		<if test="currencyCode != null and currencyCode != ''" >
			and CURRENCY_CODE = #{currencyCode}
		</if>
		<if test="currencyCname != null and currencyCname != ''" >
			and CURRENCY_CNAME = #{currencyCname}
		</if>
		<if test="currencyTname != null and currencyTname != ''" >
			and CURRENCY_TNAME = #{currencyTname}
		</if>
		<if test="currencyEname != null and currencyEname != ''" >
			and CURRENCY_ENAME = #{currencyEname}
		</if>
		<if test="creatorCode != null and creatorCode != ''" >
			and CREATOR_CODE = #{creatorCode}
		</if>
		<if test="updaterCode != null and updaterCode != ''" >
			and UPDATER_CODE = #{updaterCode}
		</if>
		<if test="validDate != null and validDate != ''" >
			and VALID_DATE = #{validDate}
		</if>
		<if test="invalidDate != null and invalidDate != ''" >
			and INVALID_DATE = #{invalidDate}
		</if>
		<if test="validInd != null and validInd != ''" >
			and VALID_IND = #{validInd}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="createBy != null and createBy != ''" >
			and CREATE_BY = #{createBy}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="modifiedBy != null and modifiedBy != ''" >
			and MODIFIED_BY = #{modifiedBy}
		</if>
		<if test="modifiedTime != null and modifiedTime != ''" >
			and MODIFIED_TIME = #{modifiedTime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GGCURRENCY
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGCURRENCY
		where GID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGCURRENCY
		where GID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.currency.po.Ggcurrency">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GGCURRENCY
		where GID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GGCURRENCY
		where GID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.currency.po.Ggcurrency">
		insert into GGCURRENCY (
			GID,
			CURRENCY_CODE,
			CURRENCY_CNAME,
			CURRENCY_TNAME,
			CURRENCY_ENAME,
			CREATOR_CODE,
			UPDATER_CODE,
			VALID_DATE,
			INVALID_DATE,
			VALID_IND,
			REMARK,
			FLAG,
			CREATE_BY,
			CREATE_TIME,
			MODIFIED_BY,
			MODIFIED_TIME
		) values (
			#{gid},
			#{currencyCode},
			#{currencyCname},
			#{currencyTname},
			#{currencyEname},
			#{creatorCode},
			#{updaterCode},
			#{validDate},
			#{invalidDate},
			#{validInd},
			#{remark},
			#{flag},
			#{createBy},
			#{createTime},
			#{modifiedBy},
			#{modifiedTime}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.currency.po.Ggcurrency">
		insert into GGCURRENCY
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="gid != null" >
				GID,
			</if>
			<if test="currencyCode != null" >
				CURRENCY_CODE,
			</if>
			<if test="currencyCname != null" >
				CURRENCY_CNAME,
			</if>
			<if test="currencyTname != null" >
				CURRENCY_TNAME,
			</if>
			<if test="currencyEname != null" >
				CURRENCY_ENAME,
			</if>
			<if test="creatorCode != null" >
				CREATOR_CODE,
			</if>
			<if test="updaterCode != null" >
				UPDATER_CODE,
			</if>
			<if test="validDate != null" >
				VALID_DATE,
			</if>
			<if test="invalidDate != null" >
				INVALID_DATE,
			</if>
			<if test="validInd != null" >
				VALID_IND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="createBy != null" >
				CREATE_BY,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="modifiedBy != null" >
				MODIFIED_BY,
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="gid != null" >
				#{gid},
			</if>
			<if test="currencyCode != null" >
				#{currencyCode},
			</if>
			<if test="currencyCname != null" >
				#{currencyCname},
			</if>
			<if test="currencyTname != null" >
				#{currencyTname},
			</if>
			<if test="currencyEname != null" >
				#{currencyEname},
			</if>
			<if test="creatorCode != null" >
				#{creatorCode},
			</if>
			<if test="updaterCode != null" >
				#{updaterCode},
			</if>
			<if test="validDate != null" >
				#{validDate},
			</if>
			<if test="invalidDate != null" >
				#{invalidDate},
			</if>
			<if test="validInd != null" >
				#{validInd},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="createBy != null" >
				#{createBy},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="modifiedBy != null" >
				#{modifiedBy},
			</if>
			<if test="modifiedTime != null" >
				#{modifiedTime}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.currency.po.Ggcurrency">
		update GGCURRENCY 
		<set>
			<if test="currencyCode != null" >
				CURRENCY_CODE=#{currencyCode},
			</if>
			<if test="currencyCname != null" >
				CURRENCY_CNAME=#{currencyCname},
			</if>
			<if test="currencyTname != null" >
				CURRENCY_TNAME=#{currencyTname},
			</if>
			<if test="currencyEname != null" >
				CURRENCY_ENAME=#{currencyEname},
			</if>
			<if test="creatorCode != null" >
				CREATOR_CODE=#{creatorCode},
			</if>
			<if test="updaterCode != null" >
				UPDATER_CODE=#{updaterCode},
			</if>
			<if test="validDate != null" >
				VALID_DATE=#{validDate},
			</if>
			<if test="invalidDate != null" >
				INVALID_DATE=#{invalidDate},
			</if>
			<if test="validInd != null" >
				VALID_IND=#{validInd},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="createBy != null" >
				CREATE_BY=#{createBy},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="modifiedBy != null" >
				MODIFIED_BY=#{modifiedBy},
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME=#{modifiedTime},
			</if>
		</set>
		where GID = #{gid }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.currency.po.Ggcurrency">
		update GGCURRENCY set
			CURRENCY_CODE=#{currencyCode},
			CURRENCY_CNAME=#{currencyCname},
			CURRENCY_TNAME=#{currencyTname},
			CURRENCY_ENAME=#{currencyEname},
			CREATOR_CODE=#{creatorCode},
			UPDATER_CODE=#{updaterCode},
			VALID_DATE=#{validDate},
			INVALID_DATE=#{invalidDate},
			VALID_IND=#{validInd},
			REMARK=#{remark},
			FLAG=#{flag},
			CREATE_BY=#{createBy},
			CREATE_TIME=#{createTime},
			MODIFIED_BY=#{modifiedBy},
			MODIFIED_TIME=#{modifiedTime},
		where GID=#{gid}
	</update>
</mapper>
