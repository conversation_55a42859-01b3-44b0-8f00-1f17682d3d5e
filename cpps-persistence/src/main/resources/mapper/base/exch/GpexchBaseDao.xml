<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.exch.dao.GpexchDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.exch.po.Gpexch">
		<id column="GID" property="gid" />
		<result column="EXCH_DATE" property="exchDate" />
		<result column="BASE" property="base" />
		<result column="BASE_CURRENCY" property="baseCurrency" />
		<result column="EXCH_CURRENCY" property="exchCurrency" />
		<result column="EXCH_RATE" property="exchRate" />
		<result column="VALID_IND" property="validInd" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="EXCH_TYPE" property="exchType" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="MODIFIED_TIME" property="modifiedTime" />
		<result column="SEND_MQ_STATUS" property="sendMqStatus" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		GID,
		EXCH_DATE,
		BASE,
		BASE_CURRENCY,
		EXCH_CURRENCY,
		EXCH_RATE,
		VALID_IND,
		REMARK,
		nvl(FLAG,0) as FLAG,
		EXCH_TYPE,
		CREATE_TIME,
		MODIFIED_TIME,
		SEND_MQ_STATUS
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="gid != null and gid != ''" >
			and GID = #{gid}
		</if>
		<if test="exchDate != null and exchDate != ''" >
			and EXCH_DATE = #{exchDate}
		</if>
		<if test="base != null and base != ''" >
			and BASE = #{base}
		</if>
		<if test="baseCurrency != null and baseCurrency != ''" >
			and BASE_CURRENCY = #{baseCurrency}
		</if>
		<if test="exchCurrency != null and exchCurrency != ''" >
			and EXCH_CURRENCY = #{exchCurrency}
		</if>
		<if test="exchRate != null and exchRate != ''" >
			and EXCH_RATE = #{exchRate}
		</if>
		<if test="validInd != null and validInd != ''" >
			and VALID_IND = #{validInd}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="exchType != null and exchType != ''" >
			and EXCH_TYPE = #{exchType}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="modifiedTime != null and modifiedTime != ''" >
			and MODIFIED_TIME = #{modifiedTime}
		</if>
		<if test="sendMqStatus != null and sendMqStatus != ''" >
			and SEND_MQ_STATUS = #{sendMqStatus}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GPEXCH
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GPEXCH
		where GID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GPEXCH
		where GID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.exch.po.Gpexch">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GPEXCH
		where GID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GPEXCH
		where GID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.exch.po.Gpexch">
		insert into GPEXCH (
			GID,
			EXCH_DATE,
			BASE,
			BASE_CURRENCY,
			EXCH_CURRENCY,
			EXCH_RATE,
			VALID_IND,
			REMARK,
			FLAG,
			EXCH_TYPE,
			CREATE_TIME,
			MODIFIED_TIME,
			SEND_MQ_STATUS
		) values (
			#{gid},
			#{exchDate},
			#{base},
			#{baseCurrency},
			#{exchCurrency},
			#{exchRate},
			#{validInd},
			#{remark},
			#{flag},
			#{exchType},
			#{createTime},
			#{modifiedTime},
			#{sendMqStatus}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.exch.po.Gpexch">
		insert into GPEXCH
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="gid != null" >
				GID,
			</if>
			<if test="exchDate != null" >
				EXCH_DATE,
			</if>
			<if test="base != null" >
				BASE,
			</if>
			<if test="baseCurrency != null" >
				BASE_CURRENCY,
			</if>
			<if test="exchCurrency != null" >
				EXCH_CURRENCY,
			</if>
			<if test="exchRate != null" >
				EXCH_RATE,
			</if>
			<if test="validInd != null" >
				VALID_IND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="exchType != null" >
				EXCH_TYPE,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME,
			</if>
			<if test="sendMqStatus != null" >
				SEND_MQ_STATUS
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="gid != null" >
				#{gid},
			</if>
			<if test="exchDate != null" >
				#{exchDate},
			</if>
			<if test="base != null" >
				#{base},
			</if>
			<if test="baseCurrency != null" >
				#{baseCurrency},
			</if>
			<if test="exchCurrency != null" >
				#{exchCurrency},
			</if>
			<if test="exchRate != null" >
				#{exchRate},
			</if>
			<if test="validInd != null" >
				#{validInd},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="exchType != null" >
				#{exchType},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="modifiedTime != null" >
				#{modifiedTime},
			</if>
			<if test="sendMqStatus != null" >
				#{sendMqStatus}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.exch.po.Gpexch">
		update GPEXCH 
		<set>
			<if test="exchDate != null" >
				EXCH_DATE=#{exchDate},
			</if>
			<if test="base != null" >
				BASE=#{base},
			</if>
			<if test="baseCurrency != null" >
				BASE_CURRENCY=#{baseCurrency},
			</if>
			<if test="exchCurrency != null" >
				EXCH_CURRENCY=#{exchCurrency},
			</if>
			<if test="exchRate != null" >
				EXCH_RATE=#{exchRate},
			</if>
			<if test="validInd != null" >
				VALID_IND=#{validInd},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="exchType != null" >
				EXCH_TYPE=#{exchType},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME=#{modifiedTime},
			</if>
			<if test="sendMqStatus != null" >
				SEND_MQ_STATUS=#{sendMqStatus},
			</if>
		</set>
		where GID = #{gid }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.exch.po.Gpexch">
		update GPEXCH set
			EXCH_DATE=#{exchDate},
			BASE=#{base},
			BASE_CURRENCY=#{baseCurrency},
			EXCH_CURRENCY=#{exchCurrency},
			EXCH_RATE=#{exchRate},
			VALID_IND=#{validInd},
			REMARK=#{remark},
			FLAG=#{flag},
			EXCH_TYPE=#{exchType},
			CREATE_TIME=#{createTime},
			MODIFIED_TIME=#{modifiedTime},
			SEND_MQ_STATUS=#{sendMqStatus},
		where GID = #{gid}	</update>
</mapper>
