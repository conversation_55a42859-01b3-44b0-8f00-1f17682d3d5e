<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.feetypecode.dao.GpfeetypecodeDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.feetypecode.po.Gpfeetypecode">
		<id column="FEE_TYPE_CODE" property="feeTypeCode" />
		<result column="FEE_TYPE_CODE_CNAME" property="feeTypeCodeCname" />
		<result column="FEE_TYPE_CODE_TNAME" property="feeTypeCodeTname" />
		<result column="FEE_TYPE_CODE_ENAME" property="feeTypeCodeEname" />
		<result column="CAL_SIGN" property="calSign" />
		<result column="FEE_TYPE_CODE_ACC" property="feeTypeCodeAcc" />
		<result column="VALIDIND" property="validind" />
		<result column="FLAG" property="flag" />
		<result column="REMARK" property="remark" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="MODIFIED_TIME" property="modifiedTime" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		FEE_TYPE_CODE,
		FEE_TYPE_CODE_CNAME,
		FEE_TYPE_CODE_TNAME,
		FEE_TYPE_CODE_ENAME,
		CAL_SIGN,
		FEE_TYPE_CODE_ACC,
		VALIDIND,
		FLAG,
		REMARK,
		CREATE_TIME,
		MODIFIED_TIME
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="feeTypeCode != null and feeTypeCode != ''" >
			and FEE_TYPE_CODE = #{feeTypeCode}
		</if>
		<if test="feeTypeCodeCname != null and feeTypeCodeCname != ''" >
			and FEE_TYPE_CODE_CNAME = #{feeTypeCodeCname}
		</if>
		<if test="feeTypeCodeTname != null and feeTypeCodeTname != ''" >
			and FEE_TYPE_CODE_TNAME = #{feeTypeCodeTname}
		</if>
		<if test="feeTypeCodeEname != null and feeTypeCodeEname != ''" >
			and FEE_TYPE_CODE_ENAME = #{feeTypeCodeEname}
		</if>
		<if test="calSign != null and calSign != ''" >
			and CAL_SIGN = #{calSign}
		</if>
		<if test="feeTypeCodeAcc != null and feeTypeCodeAcc != ''" >
			and FEE_TYPE_CODE_ACC = #{feeTypeCodeAcc}
		</if>
		<if test="validind != null and validind != ''" >
			and VALIDIND = #{validind}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="modifiedTime != null and modifiedTime != ''" >
			and MODIFIED_TIME = #{modifiedTime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GPFEETYPECODE
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GPFEETYPECODE
		where FEE_TYPE_CODE = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GPFEETYPECODE
		where FEE_TYPE_CODE in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.feetypecode.po.Gpfeetypecode">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GPFEETYPECODE
		where FEE_TYPE_CODE = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GPFEETYPECODE
		where FEE_TYPE_CODE in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.feetypecode.po.Gpfeetypecode">
		insert into GPFEETYPECODE (
			FEE_TYPE_CODE,
			FEE_TYPE_CODE_CNAME,
			FEE_TYPE_CODE_TNAME,
			FEE_TYPE_CODE_ENAME,
			CAL_SIGN,
			FEE_TYPE_CODE_ACC,
			VALIDIND,
			FLAG,
			REMARK,
			CREATE_TIME,
			MODIFIED_TIME
		) values (
			#{feeTypeCode},
			#{feeTypeCodeCname},
			#{feeTypeCodeTname},
			#{feeTypeCodeEname},
			#{calSign},
			#{feeTypeCodeAcc},
			#{validind},
			#{flag},
			#{remark},
			#{createTime},
			#{modifiedTime}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.feetypecode.po.Gpfeetypecode">
		insert into GPFEETYPECODE
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="feeTypeCode != null" >
				FEE_TYPE_CODE,
			</if>
			<if test="feeTypeCodeCname != null" >
				FEE_TYPE_CODE_CNAME,
			</if>
			<if test="feeTypeCodeTname != null" >
				FEE_TYPE_CODE_TNAME,
			</if>
			<if test="feeTypeCodeEname != null" >
				FEE_TYPE_CODE_ENAME,
			</if>
			<if test="calSign != null" >
				CAL_SIGN,
			</if>
			<if test="feeTypeCodeAcc != null" >
				FEE_TYPE_CODE_ACC,
			</if>
			<if test="validind != null" >
				VALIDIND,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="feeTypeCode != null" >
				#{feeTypeCode},
			</if>
			<if test="feeTypeCodeCname != null" >
				#{feeTypeCodeCname},
			</if>
			<if test="feeTypeCodeTname != null" >
				#{feeTypeCodeTname},
			</if>
			<if test="feeTypeCodeEname != null" >
				#{feeTypeCodeEname},
			</if>
			<if test="calSign != null" >
				#{calSign},
			</if>
			<if test="feeTypeCodeAcc != null" >
				#{feeTypeCodeAcc},
			</if>
			<if test="validind != null" >
				#{validind},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="modifiedTime != null" >
				#{modifiedTime}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.feetypecode.po.Gpfeetypecode">
		update GPFEETYPECODE 
		<set>
			<if test="feeTypeCodeCname != null" >
				FEE_TYPE_CODE_CNAME=#{feeTypeCodeCname},
			</if>
			<if test="feeTypeCodeTname != null" >
				FEE_TYPE_CODE_TNAME=#{feeTypeCodeTname},
			</if>
			<if test="feeTypeCodeEname != null" >
				FEE_TYPE_CODE_ENAME=#{feeTypeCodeEname},
			</if>
			<if test="calSign != null" >
				CAL_SIGN=#{calSign},
			</if>
			<if test="feeTypeCodeAcc != null" >
				FEE_TYPE_CODE_ACC=#{feeTypeCodeAcc},
			</if>
			<if test="validind != null" >
				VALIDIND=#{validind},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME=#{modifiedTime},
			</if>
		</set>
		where FEE_TYPE_CODE = #{feeTypeCode }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.feetypecode.po.Gpfeetypecode">
		update GPFEETYPECODE set
			FEE_TYPE_CODE_CNAME=#{feeTypeCodeCname},
			FEE_TYPE_CODE_TNAME=#{feeTypeCodeTname},
			FEE_TYPE_CODE_ENAME=#{feeTypeCodeEname},
			CAL_SIGN=#{calSign},
			FEE_TYPE_CODE_ACC=#{feeTypeCodeAcc},
			VALIDIND=#{validind},
			FLAG=#{flag},
			REMARK=#{remark},
			CREATE_TIME=#{createTime},
			MODIFIED_TIME=#{modifiedTime},
		where FEE_TYPE_CODE = #{feeTypeCode}	</update>
</mapper>
