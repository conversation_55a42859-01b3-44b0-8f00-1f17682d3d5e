<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gsclientmain.dao.GsclientmainDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gsclientmain.po.Gsclientmain">
		<id column="CLIENTCODE" property="clientcode" />
		<result column="CLIENTCNAME" property="clientcname" />
		<result column="CLIENTENAME" property="clientename" />
		<result column="CLIENTTYPE" property="clienttype" />
		<result column="JOINDATE" property="joindate" />
		<result column="BLACKLISTIND" property="blacklistind" />
		<result column="FORMALIND" property="formalind" />
		<result column="VIPIND" property="vipind" />
		<result column="CANCELIND" property="cancelind" />
		<result column="CANCELREASON" property="cancelReason" />
		<result column="CANCELDATE" property="cancelDate" />
		<result column="RELATEDCLIENT" property="relatedclient" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="CONNECTION" property="connection" />
		<result column="POSTCODE" property="postCode" />
		<result column="POSTADDRESS" property="postAddress" />
		<result column="QUALITYIND" property="qualityind" />
		<result column="CLIENTSTATUS" property="clientstatus" />
		<result column="VISITIND" property="visitind" />
		<result column="BANKCODE" property="bankCode" />
		<result column="ACCOUNTNO" property="accountNo" />
		<result column="MONEYLAUNDERINGIND" property="moneylaunderingind" />
		<result column="DIRECTCLIENTIND" property="directclientind" />
		<result column="SCANDOCIND" property="scandocind" />
		<result column="ORIGINCLIENTCODE" property="originclientcode" />
		<result column="RELATECLIENTCODE" property="relateclientcode" />
		<result column="CREATORCODE" property="creatorcode" />
		<result column="CREATETIME" property="createTime" />
		<result column="UPDATERCODE" property="updaterCode" />
		<result column="UPDATETIME" property="updatetime" />
		<result column="ITEMPROVINCECODE" property="itemprovincecode" />
		<result column="ITEMCITYCODE" property="itemcitycode" />
		<result column="ITEMPROVINCECNAME" property="itemprovincecname" />
		<result column="ITEMCITYCNAME" property="itemcitycname" />
		<result column="STARLEVEL" property="starlevel" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
		<result column="BANKNAME" property="bankName" />
		<result column="EMAIL" property="email" />
		<result column="INDUSTRYMAINCODE" property="industrymaincode" />
		<result column="INDUSTRYKINDCODE" property="industrykindcode" />
		<result column="TALLAGETYPE" property="tallagetype" />
		<result column="TALLAGENO" property="tallageno" />
		<result column="CORPORATEADDRESS" property="corporateaddress" />
		<result column="OPENINGBANK" property="openingbank" />
		<result column="TALLAGEBANKCODE" property="tallagebankcode" />
		<result column="CORPORATETEL" property="corporatetel" />
		<result column="SENDADDRESS" property="sendaddress" />
		<result column="TALLAGEREMARKS" property="tallageremarks" />
		<result column="INVOICETYPE" property="invoicetype" />
		<result column="REGISTERCODE" property="registercode" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		CLIENTCODE,
		CLIENTCNAME,
		CLIENTENAME,
		CLIENTTYPE,
		JOINDATE,
		BLACKLISTIND,
		FORMALIND,
		VIPIND,
		CANCELIND,
		CANCELREASON,
		CANCELDATE,
		RELATEDCLIENT,
		REMARK,
		FLAG,
		CONNECTION,
		POSTCODE,
		POSTADDRESS,
		QUALITYIND,
		CLIENTSTATUS,
		VISITIND,
		BANKCODE,
		ACCOUNTNO,
		MONEYLAUNDERINGIND,
		DIRECTCLIENTIND,
		SCANDOCIND,
		ORIGINCLIENTCODE,
		RELATECLIENTCODE,
		CREATORCODE,
		CREATETIME,
		UPDATERCODE,
		UPDATETIME,
		ITEMPROVINCECODE,
		ITEMCITYCODE,
		ITEMPROVINCECNAME,
		ITEMCITYCNAME,
		STARLEVEL,
		UPDATESYSDATE,
		BANKNAME,
		EMAIL,
		INDUSTRYMAINCODE,
		INDUSTRYKINDCODE,
		TALLAGETYPE,
		TALLAGENO,
		CORPORATEADDRESS,
		OPENINGBANK,
		TALLAGEBANKCODE,
		CORPORATETEL,
		SENDADDRESS,
		TALLAGEREMARKS,
		INVOICETYPE,
		REGISTERCODE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="clientcode != null and clientcode != ''" >
			and CLIENTCODE = #{clientcode}
		</if>
		<if test="clientcname != null and clientcname != ''" >
			and CLIENTCNAME = #{clientcname}
		</if>
		<if test="clientename != null and clientename != ''" >
			and CLIENTENAME = #{clientename}
		</if>
		<if test="clienttype != null and clienttype != ''" >
			and CLIENTTYPE = #{clienttype}
		</if>
		<if test="joindate != null and joindate != ''" >
			and JOINDATE = #{joindate}
		</if>
		<if test="blacklistind != null and blacklistind != ''" >
			and BLACKLISTIND = #{blacklistind}
		</if>
		<if test="formalind != null and formalind != ''" >
			and FORMALIND = #{formalind}
		</if>
		<if test="vipind != null and vipind != ''" >
			and VIPIND = #{vipind}
		</if>
		<if test="cancelind != null and cancelind != ''" >
			and CANCELIND = #{cancelind}
		</if>
		<if test="cancelReason != null and cancelReason != ''" >
			and CANCELREASON = #{cancelReason}
		</if>
		<if test="cancelDate != null and cancelDate != ''" >
			and CANCELDATE = #{cancelDate}
		</if>
		<if test="relatedclient != null and relatedclient != ''" >
			and RELATEDCLIENT = #{relatedclient}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="connection != null and connection != ''" >
			and CONNECTION = #{connection}
		</if>
		<if test="postCode != null and postCode != ''" >
			and POSTCODE = #{postCode}
		</if>
		<if test="postAddress != null and postAddress != ''" >
			and POSTADDRESS = #{postAddress}
		</if>
		<if test="qualityind != null and qualityind != ''" >
			and QUALITYIND = #{qualityind}
		</if>
		<if test="clientstatus != null and clientstatus != ''" >
			and CLIENTSTATUS = #{clientstatus}
		</if>
		<if test="visitind != null and visitind != ''" >
			and VISITIND = #{visitind}
		</if>
		<if test="bankCode != null and bankCode != ''" >
			and BANKCODE = #{bankCode}
		</if>
		<if test="accountNo != null and accountNo != ''" >
			and ACCOUNTNO = #{accountNo}
		</if>
		<if test="moneylaunderingind != null and moneylaunderingind != ''" >
			and MONEYLAUNDERINGIND = #{moneylaunderingind}
		</if>
		<if test="directclientind != null and directclientind != ''" >
			and DIRECTCLIENTIND = #{directclientind}
		</if>
		<if test="scandocind != null and scandocind != ''" >
			and SCANDOCIND = #{scandocind}
		</if>
		<if test="originclientcode != null and originclientcode != ''" >
			and ORIGINCLIENTCODE = #{originclientcode}
		</if>
		<if test="relateclientcode != null and relateclientcode != ''" >
			and RELATECLIENTCODE = #{relateclientcode}
		</if>
		<if test="creatorcode != null and creatorcode != ''" >
			and CREATORCODE = #{creatorcode}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATETIME = #{createTime}
		</if>
		<if test="updaterCode != null and updaterCode != ''" >
			and UPDATERCODE = #{updaterCode}
		</if>
		<if test="updatetime != null and updatetime != ''" >
			and UPDATETIME = #{updatetime}
		</if>
		<if test="itemprovincecode != null and itemprovincecode != ''" >
			and ITEMPROVINCECODE = #{itemprovincecode}
		</if>
		<if test="itemcitycode != null and itemcitycode != ''" >
			and ITEMCITYCODE = #{itemcitycode}
		</if>
		<if test="itemprovincecname != null and itemprovincecname != ''" >
			and ITEMPROVINCECNAME = #{itemprovincecname}
		</if>
		<if test="itemcitycname != null and itemcitycname != ''" >
			and ITEMCITYCNAME = #{itemcitycname}
		</if>
		<if test="starlevel != null and starlevel != ''" >
			and STARLEVEL = #{starlevel}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
		<if test="bankName != null and bankName != ''" >
			and BANKNAME = #{bankName}
		</if>
		<if test="email != null and email != ''" >
			and EMAIL = #{email}
		</if>
		<if test="industrymaincode != null and industrymaincode != ''" >
			and INDUSTRYMAINCODE = #{industrymaincode}
		</if>
		<if test="industrykindcode != null and industrykindcode != ''" >
			and INDUSTRYKINDCODE = #{industrykindcode}
		</if>
		<if test="tallagetype != null and tallagetype != ''" >
			and TALLAGETYPE = #{tallagetype}
		</if>
		<if test="tallageno != null and tallageno != ''" >
			and TALLAGENO = #{tallageno}
		</if>
		<if test="corporateaddress != null and corporateaddress != ''" >
			and CORPORATEADDRESS = #{corporateaddress}
		</if>
		<if test="openingbank != null and openingbank != ''" >
			and OPENINGBANK = #{openingbank}
		</if>
		<if test="tallagebankcode != null and tallagebankcode != ''" >
			and TALLAGEBANKCODE = #{tallagebankcode}
		</if>
		<if test="corporatetel != null and corporatetel != ''" >
			and CORPORATETEL = #{corporatetel}
		</if>
		<if test="sendaddress != null and sendaddress != ''" >
			and SENDADDRESS = #{sendaddress}
		</if>
		<if test="tallageremarks != null and tallageremarks != ''" >
			and TALLAGEREMARKS = #{tallageremarks}
		</if>
		<if test="invoicetype != null and invoicetype != ''" >
			and INVOICETYPE = #{invoicetype}
		</if>
		<if test="registercode != null and registercode != ''" >
			and REGISTERCODE = #{registercode}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GSCLIENTMAIN
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GSCLIENTMAIN
		where CLIENTCODE = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GSCLIENTMAIN
		where CLIENTCODE in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gsclientmain.po.Gsclientmain">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GSCLIENTMAIN
		where CLIENTCODE = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GSCLIENTMAIN
		where CLIENTCODE in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gsclientmain.po.Gsclientmain">
		insert into GSCLIENTMAIN (
			CLIENTCODE,
			CLIENTCNAME,
			CLIENTENAME,
			CLIENTTYPE,
			JOINDATE,
			BLACKLISTIND,
			FORMALIND,
			VIPIND,
			CANCELIND,
			CANCELREASON,
			CANCELDATE,
			RELATEDCLIENT,
			REMARK,
			FLAG,
			CONNECTION,
			POSTCODE,
			POSTADDRESS,
			QUALITYIND,
			CLIENTSTATUS,
			VISITIND,
			BANKCODE,
			ACCOUNTNO,
			MONEYLAUNDERINGIND,
			DIRECTCLIENTIND,
			SCANDOCIND,
			ORIGINCLIENTCODE,
			RELATECLIENTCODE,
			CREATORCODE,
			CREATETIME,
			UPDATERCODE,
			UPDATETIME,
			ITEMPROVINCECODE,
			ITEMCITYCODE,
			ITEMPROVINCECNAME,
			ITEMCITYCNAME,
			STARLEVEL,
			UPDATESYSDATE,
			BANKNAME,
			EMAIL,
			INDUSTRYMAINCODE,
			INDUSTRYKINDCODE,
			TALLAGETYPE,
			TALLAGENO,
			CORPORATEADDRESS,
			OPENINGBANK,
			TALLAGEBANKCODE,
			CORPORATETEL,
			SENDADDRESS,
			TALLAGEREMARKS,
			INVOICETYPE,
			REGISTERCODE
		) values (
			#{clientcode},
			#{clientcname},
			#{clientename},
			#{clienttype},
			#{joindate},
			#{blacklistind},
			#{formalind},
			#{vipind},
			#{cancelind},
			#{cancelReason},
			#{cancelDate},
			#{relatedclient},
			#{remark},
			#{flag},
			#{connection},
			#{postCode},
			#{postAddress},
			#{qualityind},
			#{clientstatus},
			#{visitind},
			#{bankCode},
			#{accountNo},
			#{moneylaunderingind},
			#{directclientind},
			#{scandocind},
			#{originclientcode},
			#{relateclientcode},
			#{creatorcode},
			#{createTime},
			#{updaterCode},
			#{updatetime},
			#{itemprovincecode},
			#{itemcitycode},
			#{itemprovincecname},
			#{itemcitycname},
			#{starlevel},
			#{updatesysdate},
			#{bankName},
			#{email},
			#{industrymaincode},
			#{industrykindcode},
			#{tallagetype},
			#{tallageno},
			#{corporateaddress},
			#{openingbank},
			#{tallagebankcode},
			#{corporatetel},
			#{sendaddress},
			#{tallageremarks},
			#{invoicetype},
			#{registercode}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gsclientmain.po.Gsclientmain">
		insert into GSCLIENTMAIN
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="clientcode != null" >
				CLIENTCODE,
			</if>
			<if test="clientcname != null" >
				CLIENTCNAME,
			</if>
			<if test="clientename != null" >
				CLIENTENAME,
			</if>
			<if test="clienttype != null" >
				CLIENTTYPE,
			</if>
			<if test="joindate != null" >
				JOINDATE,
			</if>
			<if test="blacklistind != null" >
				BLACKLISTIND,
			</if>
			<if test="formalind != null" >
				FORMALIND,
			</if>
			<if test="vipind != null" >
				VIPIND,
			</if>
			<if test="cancelind != null" >
				CANCELIND,
			</if>
			<if test="cancelReason != null" >
				CANCELREASON,
			</if>
			<if test="cancelDate != null" >
				CANCELDATE,
			</if>
			<if test="relatedclient != null" >
				RELATEDCLIENT,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="connection != null" >
				CONNECTION,
			</if>
			<if test="postCode != null" >
				POSTCODE,
			</if>
			<if test="postAddress != null" >
				POSTADDRESS,
			</if>
			<if test="qualityind != null" >
				QUALITYIND,
			</if>
			<if test="clientstatus != null" >
				CLIENTSTATUS,
			</if>
			<if test="visitind != null" >
				VISITIND,
			</if>
			<if test="bankCode != null" >
				BANKCODE,
			</if>
			<if test="accountNo != null" >
				ACCOUNTNO,
			</if>
			<if test="moneylaunderingind != null" >
				MONEYLAUNDERINGIND,
			</if>
			<if test="directclientind != null" >
				DIRECTCLIENTIND,
			</if>
			<if test="scandocind != null" >
				SCANDOCIND,
			</if>
			<if test="originclientcode != null" >
				ORIGINCLIENTCODE,
			</if>
			<if test="relateclientcode != null" >
				RELATECLIENTCODE,
			</if>
			<if test="creatorcode != null" >
				CREATORCODE,
			</if>
			<if test="createTime != null" >
				CREATETIME,
			</if>
			<if test="updaterCode != null" >
				UPDATERCODE,
			</if>
			<if test="updatetime != null" >
				UPDATETIME,
			</if>
			<if test="itemprovincecode != null" >
				ITEMPROVINCECODE,
			</if>
			<if test="itemcitycode != null" >
				ITEMCITYCODE,
			</if>
			<if test="itemprovincecname != null" >
				ITEMPROVINCECNAME,
			</if>
			<if test="itemcitycname != null" >
				ITEMCITYCNAME,
			</if>
			<if test="starlevel != null" >
				STARLEVEL,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE,
			</if>
			<if test="bankName != null" >
				BANKNAME,
			</if>
			<if test="email != null" >
				EMAIL,
			</if>
			<if test="industrymaincode != null" >
				INDUSTRYMAINCODE,
			</if>
			<if test="industrykindcode != null" >
				INDUSTRYKINDCODE,
			</if>
			<if test="tallagetype != null" >
				TALLAGETYPE,
			</if>
			<if test="tallageno != null" >
				TALLAGENO,
			</if>
			<if test="corporateaddress != null" >
				CORPORATEADDRESS,
			</if>
			<if test="openingbank != null" >
				OPENINGBANK,
			</if>
			<if test="tallagebankcode != null" >
				TALLAGEBANKCODE,
			</if>
			<if test="corporatetel != null" >
				CORPORATETEL,
			</if>
			<if test="sendaddress != null" >
				SENDADDRESS,
			</if>
			<if test="tallageremarks != null" >
				TALLAGEREMARKS,
			</if>
			<if test="invoicetype != null" >
				INVOICETYPE,
			</if>
			<if test="registercode != null" >
				REGISTERCODE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="clientcode != null" >
				#{clientcode},
			</if>
			<if test="clientcname != null" >
				#{clientcname},
			</if>
			<if test="clientename != null" >
				#{clientename},
			</if>
			<if test="clienttype != null" >
				#{clienttype},
			</if>
			<if test="joindate != null" >
				#{joindate},
			</if>
			<if test="blacklistind != null" >
				#{blacklistind},
			</if>
			<if test="formalind != null" >
				#{formalind},
			</if>
			<if test="vipind != null" >
				#{vipind},
			</if>
			<if test="cancelind != null" >
				#{cancelind},
			</if>
			<if test="cancelReason != null" >
				#{cancelReason},
			</if>
			<if test="cancelDate != null" >
				#{cancelDate},
			</if>
			<if test="relatedclient != null" >
				#{relatedclient},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="connection != null" >
				#{connection},
			</if>
			<if test="postCode != null" >
				#{postCode},
			</if>
			<if test="postAddress != null" >
				#{postAddress},
			</if>
			<if test="qualityind != null" >
				#{qualityind},
			</if>
			<if test="clientstatus != null" >
				#{clientstatus},
			</if>
			<if test="visitind != null" >
				#{visitind},
			</if>
			<if test="bankCode != null" >
				#{bankCode},
			</if>
			<if test="accountNo != null" >
				#{accountNo},
			</if>
			<if test="moneylaunderingind != null" >
				#{moneylaunderingind},
			</if>
			<if test="directclientind != null" >
				#{directclientind},
			</if>
			<if test="scandocind != null" >
				#{scandocind},
			</if>
			<if test="originclientcode != null" >
				#{originclientcode},
			</if>
			<if test="relateclientcode != null" >
				#{relateclientcode},
			</if>
			<if test="creatorcode != null" >
				#{creatorcode},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="updaterCode != null" >
				#{updaterCode},
			</if>
			<if test="updatetime != null" >
				#{updatetime},
			</if>
			<if test="itemprovincecode != null" >
				#{itemprovincecode},
			</if>
			<if test="itemcitycode != null" >
				#{itemcitycode},
			</if>
			<if test="itemprovincecname != null" >
				#{itemprovincecname},
			</if>
			<if test="itemcitycname != null" >
				#{itemcitycname},
			</if>
			<if test="starlevel != null" >
				#{starlevel},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate},
			</if>
			<if test="bankName != null" >
				#{bankName},
			</if>
			<if test="email != null" >
				#{email},
			</if>
			<if test="industrymaincode != null" >
				#{industrymaincode},
			</if>
			<if test="industrykindcode != null" >
				#{industrykindcode},
			</if>
			<if test="tallagetype != null" >
				#{tallagetype},
			</if>
			<if test="tallageno != null" >
				#{tallageno},
			</if>
			<if test="corporateaddress != null" >
				#{corporateaddress},
			</if>
			<if test="openingbank != null" >
				#{openingbank},
			</if>
			<if test="tallagebankcode != null" >
				#{tallagebankcode},
			</if>
			<if test="corporatetel != null" >
				#{corporatetel},
			</if>
			<if test="sendaddress != null" >
				#{sendaddress},
			</if>
			<if test="tallageremarks != null" >
				#{tallageremarks},
			</if>
			<if test="invoicetype != null" >
				#{invoicetype},
			</if>
			<if test="registercode != null" >
				#{registercode}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gsclientmain.po.Gsclientmain">
		update GSCLIENTMAIN 
		<set>
			<if test="clientcname != null" >
				CLIENTCNAME=#{clientcname},
			</if>
			<if test="clientename != null" >
				CLIENTENAME=#{clientename},
			</if>
			<if test="clienttype != null" >
				CLIENTTYPE=#{clienttype},
			</if>
			<if test="joindate != null" >
				JOINDATE=#{joindate},
			</if>
			<if test="blacklistind != null" >
				BLACKLISTIND=#{blacklistind},
			</if>
			<if test="formalind != null" >
				FORMALIND=#{formalind},
			</if>
			<if test="vipind != null" >
				VIPIND=#{vipind},
			</if>
			<if test="cancelind != null" >
				CANCELIND=#{cancelind},
			</if>
			<if test="cancelReason != null" >
				CANCELREASON=#{cancelReason},
			</if>
			<if test="cancelDate != null" >
				CANCELDATE=#{cancelDate},
			</if>
			<if test="relatedclient != null" >
				RELATEDCLIENT=#{relatedclient},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="connection != null" >
				CONNECTION=#{connection},
			</if>
			<if test="postCode != null" >
				POSTCODE=#{postCode},
			</if>
			<if test="postAddress != null" >
				POSTADDRESS=#{postAddress},
			</if>
			<if test="qualityind != null" >
				QUALITYIND=#{qualityind},
			</if>
			<if test="clientstatus != null" >
				CLIENTSTATUS=#{clientstatus},
			</if>
			<if test="visitind != null" >
				VISITIND=#{visitind},
			</if>
			<if test="bankCode != null" >
				BANKCODE=#{bankCode},
			</if>
			<if test="accountNo != null" >
				ACCOUNTNO=#{accountNo},
			</if>
			<if test="moneylaunderingind != null" >
				MONEYLAUNDERINGIND=#{moneylaunderingind},
			</if>
			<if test="directclientind != null" >
				DIRECTCLIENTIND=#{directclientind},
			</if>
			<if test="scandocind != null" >
				SCANDOCIND=#{scandocind},
			</if>
			<if test="originclientcode != null" >
				ORIGINCLIENTCODE=#{originclientcode},
			</if>
			<if test="relateclientcode != null" >
				RELATECLIENTCODE=#{relateclientcode},
			</if>
			<if test="creatorcode != null" >
				CREATORCODE=#{creatorcode},
			</if>
			<if test="createTime != null" >
				CREATETIME=#{createTime},
			</if>
			<if test="updaterCode != null" >
				UPDATERCODE=#{updaterCode},
			</if>
			<if test="updatetime != null" >
				UPDATETIME=#{updatetime},
			</if>
			<if test="itemprovincecode != null" >
				ITEMPROVINCECODE=#{itemprovincecode},
			</if>
			<if test="itemcitycode != null" >
				ITEMCITYCODE=#{itemcitycode},
			</if>
			<if test="itemprovincecname != null" >
				ITEMPROVINCECNAME=#{itemprovincecname},
			</if>
			<if test="itemcitycname != null" >
				ITEMCITYCNAME=#{itemcitycname},
			</if>
			<if test="starlevel != null" >
				STARLEVEL=#{starlevel},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
			<if test="bankName != null" >
				BANKNAME=#{bankName},
			</if>
			<if test="email != null" >
				EMAIL=#{email},
			</if>
			<if test="industrymaincode != null" >
				INDUSTRYMAINCODE=#{industrymaincode},
			</if>
			<if test="industrykindcode != null" >
				INDUSTRYKINDCODE=#{industrykindcode},
			</if>
			<if test="tallagetype != null" >
				TALLAGETYPE=#{tallagetype},
			</if>
			<if test="tallageno != null" >
				TALLAGENO=#{tallageno},
			</if>
			<if test="corporateaddress != null" >
				CORPORATEADDRESS=#{corporateaddress},
			</if>
			<if test="openingbank != null" >
				OPENINGBANK=#{openingbank},
			</if>
			<if test="tallagebankcode != null" >
				TALLAGEBANKCODE=#{tallagebankcode},
			</if>
			<if test="corporatetel != null" >
				CORPORATETEL=#{corporatetel},
			</if>
			<if test="sendaddress != null" >
				SENDADDRESS=#{sendaddress},
			</if>
			<if test="tallageremarks != null" >
				TALLAGEREMARKS=#{tallageremarks},
			</if>
			<if test="invoicetype != null" >
				INVOICETYPE=#{invoicetype},
			</if>
			<if test="registercode != null" >
				REGISTERCODE=#{registercode},
			</if>
		</set>
		where CLIENTCODE = #{clientcode }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gsclientmain.po.Gsclientmain">
		update GSCLIENTMAIN set
			CLIENTCNAME=#{clientcname},
			CLIENTENAME=#{clientename},
			CLIENTTYPE=#{clienttype},
			JOINDATE=#{joindate},
			BLACKLISTIND=#{blacklistind},
			FORMALIND=#{formalind},
			VIPIND=#{vipind},
			CANCELIND=#{cancelind},
			CANCELREASON=#{cancelReason},
			CANCELDATE=#{cancelDate},
			RELATEDCLIENT=#{relatedclient},
			REMARK=#{remark},
			FLAG=#{flag},
			CONNECTION=#{connection},
			POSTCODE=#{postCode},
			POSTADDRESS=#{postAddress},
			QUALITYIND=#{qualityind},
			CLIENTSTATUS=#{clientstatus},
			VISITIND=#{visitind},
			BANKCODE=#{bankCode},
			ACCOUNTNO=#{accountNo},
			MONEYLAUNDERINGIND=#{moneylaunderingind},
			DIRECTCLIENTIND=#{directclientind},
			SCANDOCIND=#{scandocind},
			ORIGINCLIENTCODE=#{originclientcode},
			RELATECLIENTCODE=#{relateclientcode},
			CREATORCODE=#{creatorcode},
			CREATETIME=#{createTime},
			UPDATERCODE=#{updaterCode},
			UPDATETIME=#{updatetime},
			ITEMPROVINCECODE=#{itemprovincecode},
			ITEMCITYCODE=#{itemcitycode},
			ITEMPROVINCECNAME=#{itemprovincecname},
			ITEMCITYCNAME=#{itemcitycname},
			STARLEVEL=#{starlevel},
			UPDATESYSDATE=#{updatesysdate},
			BANKNAME=#{bankName},
			EMAIL=#{email},
			INDUSTRYMAINCODE=#{industrymaincode},
			INDUSTRYKINDCODE=#{industrykindcode},
			TALLAGETYPE=#{tallagetype},
			TALLAGENO=#{tallageno},
			CORPORATEADDRESS=#{corporateaddress},
			OPENINGBANK=#{openingbank},
			TALLAGEBANKCODE=#{tallagebankcode},
			CORPORATETEL=#{corporatetel},
			SENDADDRESS=#{sendaddress},
			TALLAGEREMARKS=#{tallageremarks},
			INVOICETYPE=#{invoicetype},
			REGISTERCODE=#{registercode},
		where CLIENTCODE = #{clientcode}	</update>
	<insert id="batchInsert" parameterType="java.util.List">
		INSERT  ALL
		<foreach collection="list" item="item" separator="">
			INTO GSCLIENTMAIN VALUES
			(#{item.clientcode},#{item.clientcname},#{item.clientename},#{item.clienttype},#{item.joindate},
			#{item.blacklistind},#{item.formalind},#{item.vipind},#{item.cancelind},#{item.cancelReason},
			#{item.cancelDate},#{item.relatedclient},#{item.remark},#{item.flag},#{item.connection},#{item.postCode},
			#{item.postAddress},#{item.qualityind},#{item.clientstatus},#{item.visitind},#{item.bankCode},
			#{item.accountNo},#{item.moneylaunderingind},#{item.directclientind},#{item.scandocind},
			#{item.originclientcode},#{item.relateclientcode},#{item.creatorcode},#{item.createTime},
			#{item.updaterCode},#{item.updatetime},#{item.itemprovincecode},#{item.itemcitycode},
			#{item.itemprovincecname},#{item.itemcitycname},#{item.starlevel},#{item.updatesysdate},
			#{item.bankName},#{item.email},#{item.industrymaincode},#{item.industrykindcode},#{item.tallagetype},
			#{item.tallageno},#{item.corporateaddress},#{item.openingbank},#{item.tallagebankcode},#{item.corporatetel},
			#{item.sendaddress},#{item.tallageremarks},#{item.invoicetype},#{item.registercode})
		</foreach>
		select 1 from dual
	</insert>


</mapper>
