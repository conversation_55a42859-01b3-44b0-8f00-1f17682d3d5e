<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gumaxno.dao.GumaxnoDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gumaxno.po.Gumaxno">
		<id column="ID" property="id" />
		<result column="POLICYNO" property="policyNo" />
		<result column="VERSIONNO" property="versionNo" />
		<result column="STATUS" property="status" />
		<result column="FLAG" property="flag" />
		<result column="REMARK" property="remark" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		POLICYNO,
		VERSIONNO,
		STATUS,
		FLAG,
		REMARK
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="versionNo != null and versionNo != ''" >
			and VERSIONNO = #{versionNo}
		</if>
		<if test="status != null and status != ''" >
			and STATUS = #{status}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUMAXNO
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUMAXNO
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUMAXNO
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gumaxno.po.Gumaxno">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUMAXNO
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUMAXNO
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gumaxno.po.Gumaxno">
		insert into GUMAXNO (
			ID,
			POLICYNO,
			VERSIONNO,
			STATUS,
			FLAG,
			REMARK
		) values (
			#{id},
			#{policyNo},
			#{versionNo},
			#{status},
			#{flag},
			#{remark}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gumaxno.po.Gumaxno">
		insert into GUMAXNO
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="versionNo != null" >
				VERSIONNO,
			</if>
			<if test="status != null" >
				STATUS,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="remark != null" >
				REMARK
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="versionNo != null" >
				#{versionNo},
			</if>
			<if test="status != null" >
				#{status},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="remark != null" >
				#{remark}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gumaxno.po.Gumaxno">
		update GUMAXNO 
		<set>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="versionNo != null" >
				VERSIONNO=#{versionNo},
			</if>
			<if test="status != null" >
				STATUS=#{status},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gumaxno.po.Gumaxno">
		update GUMAXNO set
			POLICYNO=#{policyNo},
			VERSIONNO=#{versionNo},
			STATUS=#{status},
			FLAG=#{flag},
			REMARK=#{remark}
		where ID = #{id}	</update>
</mapper>
