<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ins.channel.gupolicycoinsurance.dao.GuPolicyCoinsuranceDtoMapper" >
  <resultMap id="BaseResultMap" type="ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDto" >
    <id column="POLICYNO" property="policyno" jdbcType="VARCHAR" />
    <id column="COINSCODE" property="coinscode" jdbcType="VARCHAR" />
    <id column="PLANCODE" property="plancode" jdbcType="VARCHAR" />
    <id column="RISKCODE" property="riskcode" jdbcType="VARCHAR" />
    <id column="SECONDLEVELCOINSCODE" property="secondlevelcoinscode" jdbcType="VARCHAR" />
    <id column="THIRDLEVELCOINSNAME" property="thirdlevelcoinsname" jdbcType="VARCHAR" />
    <result column="COINSPOLICYNO" property="coinspolicyno" jdbcType="VARCHAR" />
    <result column="COINSNAME" property="coinsname" jdbcType="VARCHAR" />
    <result column="COINSTYPE" property="coinstype" jdbcType="VARCHAR" />
    <result column="COINSRATE" property="coinsrate" jdbcType="DECIMAL" />
    <result column="PRINCIPALIND" property="principalind" jdbcType="VARCHAR" />
    <result column="LEADERIND" property="leaderind" jdbcType="VARCHAR" />
    <result column="CHIEFADJUSTERIND" property="chiefadjusterind" jdbcType="VARCHAR" />
    <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
    <result column="COINSINSURED" property="coinsinsured" jdbcType="DECIMAL" />
    <result column="COINSPREMIUM" property="coinspremium" jdbcType="DECIMAL" />
    <result column="COINSHANDLINGRATE" property="coinshandlingrate" jdbcType="DECIMAL" />
    <result column="COINSHANDLINGFEE" property="coinshandlingfee" jdbcType="DECIMAL" />
    <result column="COINSAGENCYRATE" property="coinsagencyrate" jdbcType="DECIMAL" />
    <result column="COINSAGENCYCOMMISSION" property="coinsagencycommission" jdbcType="DECIMAL" />
    <result column="COINSISSUERATE" property="coinsissuerate" jdbcType="DECIMAL" />
    <result column="COINSISSUEEXPENSE" property="coinsissueexpense" jdbcType="DECIMAL" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="FLAG" property="flag" jdbcType="VARCHAR" />
    <result column="COINSPREMIUMACCEPTIND" property="coinspremiumacceptind" jdbcType="VARCHAR" />
    <result column="COINSPREMIUMCOMPOSE" property="coinspremiumcompose" jdbcType="VARCHAR" />
    <result column="COINSAGENCYPAYIND" property="coinsagencypayind" jdbcType="VARCHAR" />
    <result column="COINSLEVYRATE" property="coinslevyrate" jdbcType="DECIMAL" />
    <result column="COINSLEVY" property="coinslevy" jdbcType="DECIMAL" />
    <result column="COINSLEVYACCEPTIND" property="coinslevyacceptind" jdbcType="VARCHAR" />
    <result column="COINSOTHFEERATE" property="coinsothfeerate" jdbcType="DECIMAL" />
    <result column="COINSOTHFEE" property="coinsothfee" jdbcType="DECIMAL" />
    <result column="COINSOTHFEEACCEPTIND" property="coinsothfeeacceptind" jdbcType="VARCHAR" />
    <result column="COINSCOMPRESERVERATE" property="coinscompreserverate" jdbcType="DECIMAL" />
    <result column="COINSCOMPRESERVE" property="coinscompreserve" jdbcType="DECIMAL" />
    <result column="COINSCOMPRESACCEPTIND" property="coinscompresacceptind" jdbcType="VARCHAR" />
    <result column="DEBITACCEPTER" property="debitaccepter" jdbcType="VARCHAR" />
    <result column="COINSCLAIMIND" property="coinsclaimind" jdbcType="VARCHAR" />
    <result column="SUBPOLICYNO" property="subpolicyno" jdbcType="VARCHAR" />
    <result column="COINSNOTAXPREMIUM" property="coinsnotaxpremium" jdbcType="DECIMAL" />
    <result column="COINSTAXAMOUNT" property="coinstaxamount" jdbcType="DECIMAL" />
    <result column="SECONDLEVELCOINSNAME" property="secondlevelcoinsname" jdbcType="VARCHAR" />
    <result column="RELATEDCOINSPOLICYNO" property="relatedcoinspolicyno" jdbcType="VARCHAR" />
    <result column="RELATEDCOINSCODE" property="relatedcoinscode" jdbcType="VARCHAR" />
    <result column="RELATEDCOINSCNAME" property="relatedcoinscname" jdbcType="VARCHAR" />
    <result column="SEALSTEAMCODE" property="sealsteamcode" jdbcType="VARCHAR" />
    <result column="SEALSTEAMCNAME" property="sealsteamcname" jdbcType="VARCHAR" />
    <result column="SEALSMANCODE" property="sealsmancode" jdbcType="VARCHAR" />
    <result column="SEALSMANCNAME" property="sealsmancname" jdbcType="VARCHAR" />
    <result column="WHOLERATE" property="wholerate" jdbcType="DECIMAL" />
    <result column="ORISINGLENO" property="orisingleno" jdbcType="VARCHAR" />
    <result column="RELATEDCOINSTYPE" property="relatedcoinstype" jdbcType="VARCHAR" />
    <result column="CHANNELDETAILCODE" property="channeldetailcode" jdbcType="VARCHAR" />
    <result column="CHANNELDETAILCNAME" property="channeldetailcname" jdbcType="VARCHAR" />
    <result column="COINSISSUEEXPENSETAX" property="coinsissueexpensetax" jdbcType="DECIMAL" />
    <result column="COINSOTHFEETAX" property="coinsothfeetax" jdbcType="DECIMAL" />
    <result column="COINSINSUREDCNY" property="coinsinsuredcny" jdbcType="DECIMAL" />
    <result column="COINSPREMIUMCNY" property="coinspremiumcny" jdbcType="DECIMAL" />
    <result column="COINSNOTAXPREMIUMCNY" property="coinsnotaxpremiumcny" jdbcType="DECIMAL" />
    <result column="COINSTAXAMOUNTCNY" property="coinstaxamountcny" jdbcType="DECIMAL" />
    <result column="CURRENCYCNY" property="currencycny" jdbcType="VARCHAR" />
    <result column="SERIALNO" property="serialno" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    POLICYNO, COINSCODE, PLANCODE, RISKCODE, SECONDLEVELCOINSCODE, THIRDLEVELCOINSNAME, 
    COINSPOLICYNO, COINSNAME, COINSTYPE, COINSRATE, PRINCIPALIND, LEADERIND, CHIEFADJUSTERIND, 
    CURRENCY, COINSINSURED, COINSPREMIUM, COINSHANDLINGRATE, COINSHANDLINGFEE, COINSAGENCYRATE, 
    COINSAGENCYCOMMISSION, COINSISSUERATE, COINSISSUEEXPENSE, REMARK, FLAG, COINSPREMIUMACCEPTIND, 
    COINSPREMIUMCOMPOSE, COINSAGENCYPAYIND, COINSLEVYRATE, COINSLEVY, COINSLEVYACCEPTIND, 
    COINSOTHFEERATE, COINSOTHFEE, COINSOTHFEEACCEPTIND, COINSCOMPRESERVERATE, COINSCOMPRESERVE, 
    COINSCOMPRESACCEPTIND, DEBITACCEPTER, COINSCLAIMIND, SUBPOLICYNO, COINSNOTAXPREMIUM, 
    COINSTAXAMOUNT, SECONDLEVELCOINSNAME, RELATEDCOINSPOLICYNO, RELATEDCOINSCODE, RELATEDCOINSCNAME, 
    SEALSTEAMCODE, SEALSTEAMCNAME, SEALSMANCODE, SEALSMANCNAME, WHOLERATE, ORISINGLENO, 
    RELATEDCOINSTYPE, CHANNELDETAILCODE, CHANNELDETAILCNAME, COINSISSUEEXPENSETAX, COINSOTHFEETAX, 
    COINSINSUREDCNY, COINSPREMIUMCNY, COINSNOTAXPREMIUMCNY, COINSTAXAMOUNTCNY, CURRENCYCNY, 
    SERIALNO
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDtoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from GUPOLICYCOINSURANCE
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDtoKey" >
    select 
    <include refid="Base_Column_List" />
    from GUPOLICYCOINSURANCE
    where POLICYNO = #{policyno,jdbcType=VARCHAR}
      and COINSCODE = #{coinscode,jdbcType=VARCHAR}
      and PLANCODE = #{plancode,jdbcType=VARCHAR}
      and RISKCODE = #{riskcode,jdbcType=VARCHAR}
      and SECONDLEVELCOINSCODE = #{secondlevelcoinscode,jdbcType=VARCHAR}
      and THIRDLEVELCOINSNAME = #{thirdlevelcoinsname,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDtoKey" >
    delete from GUPOLICYCOINSURANCE
    where POLICYNO = #{policyno,jdbcType=VARCHAR}
      and COINSCODE = #{coinscode,jdbcType=VARCHAR}
      and PLANCODE = #{plancode,jdbcType=VARCHAR}
      and RISKCODE = #{riskcode,jdbcType=VARCHAR}
      and SECONDLEVELCOINSCODE = #{secondlevelcoinscode,jdbcType=VARCHAR}
      and THIRDLEVELCOINSNAME = #{thirdlevelcoinsname,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDtoExample" >
    delete from GUPOLICYCOINSURANCE
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDto" >
    insert into GUPOLICYCOINSURANCE (POLICYNO, COINSCODE, PLANCODE, 
      RISKCODE, SECONDLEVELCOINSCODE, THIRDLEVELCOINSNAME, 
      COINSPOLICYNO, COINSNAME, COINSTYPE, 
      COINSRATE, PRINCIPALIND, LEADERIND, 
      CHIEFADJUSTERIND, CURRENCY, COINSINSURED, 
      COINSPREMIUM, COINSHANDLINGRATE, COINSHANDLINGFEE, 
      COINSAGENCYRATE, COINSAGENCYCOMMISSION, 
      COINSISSUERATE, COINSISSUEEXPENSE, REMARK, 
      FLAG, COINSPREMIUMACCEPTIND, COINSPREMIUMCOMPOSE, 
      COINSAGENCYPAYIND, COINSLEVYRATE, COINSLEVY, 
      COINSLEVYACCEPTIND, COINSOTHFEERATE, COINSOTHFEE, 
      COINSOTHFEEACCEPTIND, COINSCOMPRESERVERATE, 
      COINSCOMPRESERVE, COINSCOMPRESACCEPTIND, 
      DEBITACCEPTER, COINSCLAIMIND, SUBPOLICYNO, 
      COINSNOTAXPREMIUM, COINSTAXAMOUNT, SECONDLEVELCOINSNAME, 
      RELATEDCOINSPOLICYNO, RELATEDCOINSCODE, 
      RELATEDCOINSCNAME, SEALSTEAMCODE, SEALSTEAMCNAME, 
      SEALSMANCODE, SEALSMANCNAME, WHOLERATE, 
      ORISINGLENO, RELATEDCOINSTYPE, CHANNELDETAILCODE, 
      CHANNELDETAILCNAME, COINSISSUEEXPENSETAX, 
      COINSOTHFEETAX, COINSINSUREDCNY, COINSPREMIUMCNY, 
      COINSNOTAXPREMIUMCNY, COINSTAXAMOUNTCNY, 
      CURRENCYCNY, SERIALNO)
    values (#{policyno,jdbcType=VARCHAR}, #{coinscode,jdbcType=VARCHAR}, #{plancode,jdbcType=VARCHAR}, 
      #{riskcode,jdbcType=VARCHAR}, #{secondlevelcoinscode,jdbcType=VARCHAR}, #{thirdlevelcoinsname,jdbcType=VARCHAR}, 
      #{coinspolicyno,jdbcType=VARCHAR}, #{coinsname,jdbcType=VARCHAR}, #{coinstype,jdbcType=VARCHAR}, 
      #{coinsrate,jdbcType=DECIMAL}, #{principalind,jdbcType=VARCHAR}, #{leaderind,jdbcType=VARCHAR}, 
      #{chiefadjusterind,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, #{coinsinsured,jdbcType=DECIMAL}, 
      #{coinspremium,jdbcType=DECIMAL}, #{coinshandlingrate,jdbcType=DECIMAL}, #{coinshandlingfee,jdbcType=DECIMAL}, 
      #{coinsagencyrate,jdbcType=DECIMAL}, #{coinsagencycommission,jdbcType=DECIMAL}, 
      #{coinsissuerate,jdbcType=DECIMAL}, #{coinsissueexpense,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, 
      #{flag,jdbcType=VARCHAR}, #{coinspremiumacceptind,jdbcType=VARCHAR}, #{coinspremiumcompose,jdbcType=VARCHAR}, 
      #{coinsagencypayind,jdbcType=VARCHAR}, #{coinslevyrate,jdbcType=DECIMAL}, #{coinslevy,jdbcType=DECIMAL}, 
      #{coinslevyacceptind,jdbcType=VARCHAR}, #{coinsothfeerate,jdbcType=DECIMAL}, #{coinsothfee,jdbcType=DECIMAL}, 
      #{coinsothfeeacceptind,jdbcType=VARCHAR}, #{coinscompreserverate,jdbcType=DECIMAL}, 
      #{coinscompreserve,jdbcType=DECIMAL}, #{coinscompresacceptind,jdbcType=VARCHAR}, 
      #{debitaccepter,jdbcType=VARCHAR}, #{coinsclaimind,jdbcType=VARCHAR}, #{subpolicyno,jdbcType=VARCHAR}, 
      #{coinsnotaxpremium,jdbcType=DECIMAL}, #{coinstaxamount,jdbcType=DECIMAL}, #{secondlevelcoinsname,jdbcType=VARCHAR}, 
      #{relatedcoinspolicyno,jdbcType=VARCHAR}, #{relatedcoinscode,jdbcType=VARCHAR}, 
      #{relatedcoinscname,jdbcType=VARCHAR}, #{sealsteamcode,jdbcType=VARCHAR}, #{sealsteamcname,jdbcType=VARCHAR}, 
      #{sealsmancode,jdbcType=VARCHAR}, #{sealsmancname,jdbcType=VARCHAR}, #{wholerate,jdbcType=DECIMAL}, 
      #{orisingleno,jdbcType=VARCHAR}, #{relatedcoinstype,jdbcType=VARCHAR}, #{channeldetailcode,jdbcType=VARCHAR}, 
      #{channeldetailcname,jdbcType=VARCHAR}, #{coinsissueexpensetax,jdbcType=DECIMAL}, 
      #{coinsothfeetax,jdbcType=DECIMAL}, #{coinsinsuredcny,jdbcType=DECIMAL}, #{coinspremiumcny,jdbcType=DECIMAL}, 
      #{coinsnotaxpremiumcny,jdbcType=DECIMAL}, #{coinstaxamountcny,jdbcType=DECIMAL}, 
      #{currencycny,jdbcType=VARCHAR}, #{serialno,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDto" >
    insert into GUPOLICYCOINSURANCE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="policyno != null" >
        POLICYNO,
      </if>
      <if test="coinscode != null" >
        COINSCODE,
      </if>
      <if test="plancode != null" >
        PLANCODE,
      </if>
      <if test="riskcode != null" >
        RISKCODE,
      </if>
      <if test="secondlevelcoinscode != null" >
        SECONDLEVELCOINSCODE,
      </if>
      <if test="thirdlevelcoinsname != null" >
        THIRDLEVELCOINSNAME,
      </if>
      <if test="coinspolicyno != null" >
        COINSPOLICYNO,
      </if>
      <if test="coinsname != null" >
        COINSNAME,
      </if>
      <if test="coinstype != null" >
        COINSTYPE,
      </if>
      <if test="coinsrate != null" >
        COINSRATE,
      </if>
      <if test="principalind != null" >
        PRINCIPALIND,
      </if>
      <if test="leaderind != null" >
        LEADERIND,
      </if>
      <if test="chiefadjusterind != null" >
        CHIEFADJUSTERIND,
      </if>
      <if test="currency != null" >
        CURRENCY,
      </if>
      <if test="coinsinsured != null" >
        COINSINSURED,
      </if>
      <if test="coinspremium != null" >
        COINSPREMIUM,
      </if>
      <if test="coinshandlingrate != null" >
        COINSHANDLINGRATE,
      </if>
      <if test="coinshandlingfee != null" >
        COINSHANDLINGFEE,
      </if>
      <if test="coinsagencyrate != null" >
        COINSAGENCYRATE,
      </if>
      <if test="coinsagencycommission != null" >
        COINSAGENCYCOMMISSION,
      </if>
      <if test="coinsissuerate != null" >
        COINSISSUERATE,
      </if>
      <if test="coinsissueexpense != null" >
        COINSISSUEEXPENSE,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="flag != null" >
        FLAG,
      </if>
      <if test="coinspremiumacceptind != null" >
        COINSPREMIUMACCEPTIND,
      </if>
      <if test="coinspremiumcompose != null" >
        COINSPREMIUMCOMPOSE,
      </if>
      <if test="coinsagencypayind != null" >
        COINSAGENCYPAYIND,
      </if>
      <if test="coinslevyrate != null" >
        COINSLEVYRATE,
      </if>
      <if test="coinslevy != null" >
        COINSLEVY,
      </if>
      <if test="coinslevyacceptind != null" >
        COINSLEVYACCEPTIND,
      </if>
      <if test="coinsothfeerate != null" >
        COINSOTHFEERATE,
      </if>
      <if test="coinsothfee != null" >
        COINSOTHFEE,
      </if>
      <if test="coinsothfeeacceptind != null" >
        COINSOTHFEEACCEPTIND,
      </if>
      <if test="coinscompreserverate != null" >
        COINSCOMPRESERVERATE,
      </if>
      <if test="coinscompreserve != null" >
        COINSCOMPRESERVE,
      </if>
      <if test="coinscompresacceptind != null" >
        COINSCOMPRESACCEPTIND,
      </if>
      <if test="debitaccepter != null" >
        DEBITACCEPTER,
      </if>
      <if test="coinsclaimind != null" >
        COINSCLAIMIND,
      </if>
      <if test="subpolicyno != null" >
        SUBPOLICYNO,
      </if>
      <if test="coinsnotaxpremium != null" >
        COINSNOTAXPREMIUM,
      </if>
      <if test="coinstaxamount != null" >
        COINSTAXAMOUNT,
      </if>
      <if test="secondlevelcoinsname != null" >
        SECONDLEVELCOINSNAME,
      </if>
      <if test="relatedcoinspolicyno != null" >
        RELATEDCOINSPOLICYNO,
      </if>
      <if test="relatedcoinscode != null" >
        RELATEDCOINSCODE,
      </if>
      <if test="relatedcoinscname != null" >
        RELATEDCOINSCNAME,
      </if>
      <if test="sealsteamcode != null" >
        SEALSTEAMCODE,
      </if>
      <if test="sealsteamcname != null" >
        SEALSTEAMCNAME,
      </if>
      <if test="sealsmancode != null" >
        SEALSMANCODE,
      </if>
      <if test="sealsmancname != null" >
        SEALSMANCNAME,
      </if>
      <if test="wholerate != null" >
        WHOLERATE,
      </if>
      <if test="orisingleno != null" >
        ORISINGLENO,
      </if>
      <if test="relatedcoinstype != null" >
        RELATEDCOINSTYPE,
      </if>
      <if test="channeldetailcode != null" >
        CHANNELDETAILCODE,
      </if>
      <if test="channeldetailcname != null" >
        CHANNELDETAILCNAME,
      </if>
      <if test="coinsissueexpensetax != null" >
        COINSISSUEEXPENSETAX,
      </if>
      <if test="coinsothfeetax != null" >
        COINSOTHFEETAX,
      </if>
      <if test="coinsinsuredcny != null" >
        COINSINSUREDCNY,
      </if>
      <if test="coinspremiumcny != null" >
        COINSPREMIUMCNY,
      </if>
      <if test="coinsnotaxpremiumcny != null" >
        COINSNOTAXPREMIUMCNY,
      </if>
      <if test="coinstaxamountcny != null" >
        COINSTAXAMOUNTCNY,
      </if>
      <if test="currencycny != null" >
        CURRENCYCNY,
      </if>
      <if test="serialno != null" >
        SERIALNO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="policyno != null" >
        #{policyno,jdbcType=VARCHAR},
      </if>
      <if test="coinscode != null" >
        #{coinscode,jdbcType=VARCHAR},
      </if>
      <if test="plancode != null" >
        #{plancode,jdbcType=VARCHAR},
      </if>
      <if test="riskcode != null" >
        #{riskcode,jdbcType=VARCHAR},
      </if>
      <if test="secondlevelcoinscode != null" >
        #{secondlevelcoinscode,jdbcType=VARCHAR},
      </if>
      <if test="thirdlevelcoinsname != null" >
        #{thirdlevelcoinsname,jdbcType=VARCHAR},
      </if>
      <if test="coinspolicyno != null" >
        #{coinspolicyno,jdbcType=VARCHAR},
      </if>
      <if test="coinsname != null" >
        #{coinsname,jdbcType=VARCHAR},
      </if>
      <if test="coinstype != null" >
        #{coinstype,jdbcType=VARCHAR},
      </if>
      <if test="coinsrate != null" >
        #{coinsrate,jdbcType=DECIMAL},
      </if>
      <if test="principalind != null" >
        #{principalind,jdbcType=VARCHAR},
      </if>
      <if test="leaderind != null" >
        #{leaderind,jdbcType=VARCHAR},
      </if>
      <if test="chiefadjusterind != null" >
        #{chiefadjusterind,jdbcType=VARCHAR},
      </if>
      <if test="currency != null" >
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="coinsinsured != null" >
        #{coinsinsured,jdbcType=DECIMAL},
      </if>
      <if test="coinspremium != null" >
        #{coinspremium,jdbcType=DECIMAL},
      </if>
      <if test="coinshandlingrate != null" >
        #{coinshandlingrate,jdbcType=DECIMAL},
      </if>
      <if test="coinshandlingfee != null" >
        #{coinshandlingfee,jdbcType=DECIMAL},
      </if>
      <if test="coinsagencyrate != null" >
        #{coinsagencyrate,jdbcType=DECIMAL},
      </if>
      <if test="coinsagencycommission != null" >
        #{coinsagencycommission,jdbcType=DECIMAL},
      </if>
      <if test="coinsissuerate != null" >
        #{coinsissuerate,jdbcType=DECIMAL},
      </if>
      <if test="coinsissueexpense != null" >
        #{coinsissueexpense,jdbcType=DECIMAL},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="flag != null" >
        #{flag,jdbcType=VARCHAR},
      </if>
      <if test="coinspremiumacceptind != null" >
        #{coinspremiumacceptind,jdbcType=VARCHAR},
      </if>
      <if test="coinspremiumcompose != null" >
        #{coinspremiumcompose,jdbcType=VARCHAR},
      </if>
      <if test="coinsagencypayind != null" >
        #{coinsagencypayind,jdbcType=VARCHAR},
      </if>
      <if test="coinslevyrate != null" >
        #{coinslevyrate,jdbcType=DECIMAL},
      </if>
      <if test="coinslevy != null" >
        #{coinslevy,jdbcType=DECIMAL},
      </if>
      <if test="coinslevyacceptind != null" >
        #{coinslevyacceptind,jdbcType=VARCHAR},
      </if>
      <if test="coinsothfeerate != null" >
        #{coinsothfeerate,jdbcType=DECIMAL},
      </if>
      <if test="coinsothfee != null" >
        #{coinsothfee,jdbcType=DECIMAL},
      </if>
      <if test="coinsothfeeacceptind != null" >
        #{coinsothfeeacceptind,jdbcType=VARCHAR},
      </if>
      <if test="coinscompreserverate != null" >
        #{coinscompreserverate,jdbcType=DECIMAL},
      </if>
      <if test="coinscompreserve != null" >
        #{coinscompreserve,jdbcType=DECIMAL},
      </if>
      <if test="coinscompresacceptind != null" >
        #{coinscompresacceptind,jdbcType=VARCHAR},
      </if>
      <if test="debitaccepter != null" >
        #{debitaccepter,jdbcType=VARCHAR},
      </if>
      <if test="coinsclaimind != null" >
        #{coinsclaimind,jdbcType=VARCHAR},
      </if>
      <if test="subpolicyno != null" >
        #{subpolicyno,jdbcType=VARCHAR},
      </if>
      <if test="coinsnotaxpremium != null" >
        #{coinsnotaxpremium,jdbcType=DECIMAL},
      </if>
      <if test="coinstaxamount != null" >
        #{coinstaxamount,jdbcType=DECIMAL},
      </if>
      <if test="secondlevelcoinsname != null" >
        #{secondlevelcoinsname,jdbcType=VARCHAR},
      </if>
      <if test="relatedcoinspolicyno != null" >
        #{relatedcoinspolicyno,jdbcType=VARCHAR},
      </if>
      <if test="relatedcoinscode != null" >
        #{relatedcoinscode,jdbcType=VARCHAR},
      </if>
      <if test="relatedcoinscname != null" >
        #{relatedcoinscname,jdbcType=VARCHAR},
      </if>
      <if test="sealsteamcode != null" >
        #{sealsteamcode,jdbcType=VARCHAR},
      </if>
      <if test="sealsteamcname != null" >
        #{sealsteamcname,jdbcType=VARCHAR},
      </if>
      <if test="sealsmancode != null" >
        #{sealsmancode,jdbcType=VARCHAR},
      </if>
      <if test="sealsmancname != null" >
        #{sealsmancname,jdbcType=VARCHAR},
      </if>
      <if test="wholerate != null" >
        #{wholerate,jdbcType=DECIMAL},
      </if>
      <if test="orisingleno != null" >
        #{orisingleno,jdbcType=VARCHAR},
      </if>
      <if test="relatedcoinstype != null" >
        #{relatedcoinstype,jdbcType=VARCHAR},
      </if>
      <if test="channeldetailcode != null" >
        #{channeldetailcode,jdbcType=VARCHAR},
      </if>
      <if test="channeldetailcname != null" >
        #{channeldetailcname,jdbcType=VARCHAR},
      </if>
      <if test="coinsissueexpensetax != null" >
        #{coinsissueexpensetax,jdbcType=DECIMAL},
      </if>
      <if test="coinsothfeetax != null" >
        #{coinsothfeetax,jdbcType=DECIMAL},
      </if>
      <if test="coinsinsuredcny != null" >
        #{coinsinsuredcny,jdbcType=DECIMAL},
      </if>
      <if test="coinspremiumcny != null" >
        #{coinspremiumcny,jdbcType=DECIMAL},
      </if>
      <if test="coinsnotaxpremiumcny != null" >
        #{coinsnotaxpremiumcny,jdbcType=DECIMAL},
      </if>
      <if test="coinstaxamountcny != null" >
        #{coinstaxamountcny,jdbcType=DECIMAL},
      </if>
      <if test="currencycny != null" >
        #{currencycny,jdbcType=VARCHAR},
      </if>
      <if test="serialno != null" >
        #{serialno,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDtoExample" resultType="java.lang.Integer" >
    select count(*) from GUPOLICYCOINSURANCE
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update GUPOLICYCOINSURANCE
    <set >
      <if test="record.policyno != null" >
        POLICYNO = #{record.policyno,jdbcType=VARCHAR},
      </if>
      <if test="record.coinscode != null" >
        COINSCODE = #{record.coinscode,jdbcType=VARCHAR},
      </if>
      <if test="record.plancode != null" >
        PLANCODE = #{record.plancode,jdbcType=VARCHAR},
      </if>
      <if test="record.riskcode != null" >
        RISKCODE = #{record.riskcode,jdbcType=VARCHAR},
      </if>
      <if test="record.secondlevelcoinscode != null" >
        SECONDLEVELCOINSCODE = #{record.secondlevelcoinscode,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdlevelcoinsname != null" >
        THIRDLEVELCOINSNAME = #{record.thirdlevelcoinsname,jdbcType=VARCHAR},
      </if>
      <if test="record.coinspolicyno != null" >
        COINSPOLICYNO = #{record.coinspolicyno,jdbcType=VARCHAR},
      </if>
      <if test="record.coinsname != null" >
        COINSNAME = #{record.coinsname,jdbcType=VARCHAR},
      </if>
      <if test="record.coinstype != null" >
        COINSTYPE = #{record.coinstype,jdbcType=VARCHAR},
      </if>
      <if test="record.coinsrate != null" >
        COINSRATE = #{record.coinsrate,jdbcType=DECIMAL},
      </if>
      <if test="record.principalind != null" >
        PRINCIPALIND = #{record.principalind,jdbcType=VARCHAR},
      </if>
      <if test="record.leaderind != null" >
        LEADERIND = #{record.leaderind,jdbcType=VARCHAR},
      </if>
      <if test="record.chiefadjusterind != null" >
        CHIEFADJUSTERIND = #{record.chiefadjusterind,jdbcType=VARCHAR},
      </if>
      <if test="record.currency != null" >
        CURRENCY = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.coinsinsured != null" >
        COINSINSURED = #{record.coinsinsured,jdbcType=DECIMAL},
      </if>
      <if test="record.coinspremium != null" >
        COINSPREMIUM = #{record.coinspremium,jdbcType=DECIMAL},
      </if>
      <if test="record.coinshandlingrate != null" >
        COINSHANDLINGRATE = #{record.coinshandlingrate,jdbcType=DECIMAL},
      </if>
      <if test="record.coinshandlingfee != null" >
        COINSHANDLINGFEE = #{record.coinshandlingfee,jdbcType=DECIMAL},
      </if>
      <if test="record.coinsagencyrate != null" >
        COINSAGENCYRATE = #{record.coinsagencyrate,jdbcType=DECIMAL},
      </if>
      <if test="record.coinsagencycommission != null" >
        COINSAGENCYCOMMISSION = #{record.coinsagencycommission,jdbcType=DECIMAL},
      </if>
      <if test="record.coinsissuerate != null" >
        COINSISSUERATE = #{record.coinsissuerate,jdbcType=DECIMAL},
      </if>
      <if test="record.coinsissueexpense != null" >
        COINSISSUEEXPENSE = #{record.coinsissueexpense,jdbcType=DECIMAL},
      </if>
      <if test="record.remark != null" >
        REMARK = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.flag != null" >
        FLAG = #{record.flag,jdbcType=VARCHAR},
      </if>
      <if test="record.coinspremiumacceptind != null" >
        COINSPREMIUMACCEPTIND = #{record.coinspremiumacceptind,jdbcType=VARCHAR},
      </if>
      <if test="record.coinspremiumcompose != null" >
        COINSPREMIUMCOMPOSE = #{record.coinspremiumcompose,jdbcType=VARCHAR},
      </if>
      <if test="record.coinsagencypayind != null" >
        COINSAGENCYPAYIND = #{record.coinsagencypayind,jdbcType=VARCHAR},
      </if>
      <if test="record.coinslevyrate != null" >
        COINSLEVYRATE = #{record.coinslevyrate,jdbcType=DECIMAL},
      </if>
      <if test="record.coinslevy != null" >
        COINSLEVY = #{record.coinslevy,jdbcType=DECIMAL},
      </if>
      <if test="record.coinslevyacceptind != null" >
        COINSLEVYACCEPTIND = #{record.coinslevyacceptind,jdbcType=VARCHAR},
      </if>
      <if test="record.coinsothfeerate != null" >
        COINSOTHFEERATE = #{record.coinsothfeerate,jdbcType=DECIMAL},
      </if>
      <if test="record.coinsothfee != null" >
        COINSOTHFEE = #{record.coinsothfee,jdbcType=DECIMAL},
      </if>
      <if test="record.coinsothfeeacceptind != null" >
        COINSOTHFEEACCEPTIND = #{record.coinsothfeeacceptind,jdbcType=VARCHAR},
      </if>
      <if test="record.coinscompreserverate != null" >
        COINSCOMPRESERVERATE = #{record.coinscompreserverate,jdbcType=DECIMAL},
      </if>
      <if test="record.coinscompreserve != null" >
        COINSCOMPRESERVE = #{record.coinscompreserve,jdbcType=DECIMAL},
      </if>
      <if test="record.coinscompresacceptind != null" >
        COINSCOMPRESACCEPTIND = #{record.coinscompresacceptind,jdbcType=VARCHAR},
      </if>
      <if test="record.debitaccepter != null" >
        DEBITACCEPTER = #{record.debitaccepter,jdbcType=VARCHAR},
      </if>
      <if test="record.coinsclaimind != null" >
        COINSCLAIMIND = #{record.coinsclaimind,jdbcType=VARCHAR},
      </if>
      <if test="record.subpolicyno != null" >
        SUBPOLICYNO = #{record.subpolicyno,jdbcType=VARCHAR},
      </if>
      <if test="record.coinsnotaxpremium != null" >
        COINSNOTAXPREMIUM = #{record.coinsnotaxpremium,jdbcType=DECIMAL},
      </if>
      <if test="record.coinstaxamount != null" >
        COINSTAXAMOUNT = #{record.coinstaxamount,jdbcType=DECIMAL},
      </if>
      <if test="record.secondlevelcoinsname != null" >
        SECONDLEVELCOINSNAME = #{record.secondlevelcoinsname,jdbcType=VARCHAR},
      </if>
      <if test="record.relatedcoinspolicyno != null" >
        RELATEDCOINSPOLICYNO = #{record.relatedcoinspolicyno,jdbcType=VARCHAR},
      </if>
      <if test="record.relatedcoinscode != null" >
        RELATEDCOINSCODE = #{record.relatedcoinscode,jdbcType=VARCHAR},
      </if>
      <if test="record.relatedcoinscname != null" >
        RELATEDCOINSCNAME = #{record.relatedcoinscname,jdbcType=VARCHAR},
      </if>
      <if test="record.sealsteamcode != null" >
        SEALSTEAMCODE = #{record.sealsteamcode,jdbcType=VARCHAR},
      </if>
      <if test="record.sealsteamcname != null" >
        SEALSTEAMCNAME = #{record.sealsteamcname,jdbcType=VARCHAR},
      </if>
      <if test="record.sealsmancode != null" >
        SEALSMANCODE = #{record.sealsmancode,jdbcType=VARCHAR},
      </if>
      <if test="record.sealsmancname != null" >
        SEALSMANCNAME = #{record.sealsmancname,jdbcType=VARCHAR},
      </if>
      <if test="record.wholerate != null" >
        WHOLERATE = #{record.wholerate,jdbcType=DECIMAL},
      </if>
      <if test="record.orisingleno != null" >
        ORISINGLENO = #{record.orisingleno,jdbcType=VARCHAR},
      </if>
      <if test="record.relatedcoinstype != null" >
        RELATEDCOINSTYPE = #{record.relatedcoinstype,jdbcType=VARCHAR},
      </if>
      <if test="record.channeldetailcode != null" >
        CHANNELDETAILCODE = #{record.channeldetailcode,jdbcType=VARCHAR},
      </if>
      <if test="record.channeldetailcname != null" >
        CHANNELDETAILCNAME = #{record.channeldetailcname,jdbcType=VARCHAR},
      </if>
      <if test="record.coinsissueexpensetax != null" >
        COINSISSUEEXPENSETAX = #{record.coinsissueexpensetax,jdbcType=DECIMAL},
      </if>
      <if test="record.coinsothfeetax != null" >
        COINSOTHFEETAX = #{record.coinsothfeetax,jdbcType=DECIMAL},
      </if>
      <if test="record.coinsinsuredcny != null" >
        COINSINSUREDCNY = #{record.coinsinsuredcny,jdbcType=DECIMAL},
      </if>
      <if test="record.coinspremiumcny != null" >
        COINSPREMIUMCNY = #{record.coinspremiumcny,jdbcType=DECIMAL},
      </if>
      <if test="record.coinsnotaxpremiumcny != null" >
        COINSNOTAXPREMIUMCNY = #{record.coinsnotaxpremiumcny,jdbcType=DECIMAL},
      </if>
      <if test="record.coinstaxamountcny != null" >
        COINSTAXAMOUNTCNY = #{record.coinstaxamountcny,jdbcType=DECIMAL},
      </if>
      <if test="record.currencycny != null" >
        CURRENCYCNY = #{record.currencycny,jdbcType=VARCHAR},
      </if>
      <if test="record.serialno != null" >
        SERIALNO = #{record.serialno,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update GUPOLICYCOINSURANCE
    set POLICYNO = #{record.policyno,jdbcType=VARCHAR},
      COINSCODE = #{record.coinscode,jdbcType=VARCHAR},
      PLANCODE = #{record.plancode,jdbcType=VARCHAR},
      RISKCODE = #{record.riskcode,jdbcType=VARCHAR},
      SECONDLEVELCOINSCODE = #{record.secondlevelcoinscode,jdbcType=VARCHAR},
      THIRDLEVELCOINSNAME = #{record.thirdlevelcoinsname,jdbcType=VARCHAR},
      COINSPOLICYNO = #{record.coinspolicyno,jdbcType=VARCHAR},
      COINSNAME = #{record.coinsname,jdbcType=VARCHAR},
      COINSTYPE = #{record.coinstype,jdbcType=VARCHAR},
      COINSRATE = #{record.coinsrate,jdbcType=DECIMAL},
      PRINCIPALIND = #{record.principalind,jdbcType=VARCHAR},
      LEADERIND = #{record.leaderind,jdbcType=VARCHAR},
      CHIEFADJUSTERIND = #{record.chiefadjusterind,jdbcType=VARCHAR},
      CURRENCY = #{record.currency,jdbcType=VARCHAR},
      COINSINSURED = #{record.coinsinsured,jdbcType=DECIMAL},
      COINSPREMIUM = #{record.coinspremium,jdbcType=DECIMAL},
      COINSHANDLINGRATE = #{record.coinshandlingrate,jdbcType=DECIMAL},
      COINSHANDLINGFEE = #{record.coinshandlingfee,jdbcType=DECIMAL},
      COINSAGENCYRATE = #{record.coinsagencyrate,jdbcType=DECIMAL},
      COINSAGENCYCOMMISSION = #{record.coinsagencycommission,jdbcType=DECIMAL},
      COINSISSUERATE = #{record.coinsissuerate,jdbcType=DECIMAL},
      COINSISSUEEXPENSE = #{record.coinsissueexpense,jdbcType=DECIMAL},
      REMARK = #{record.remark,jdbcType=VARCHAR},
      FLAG = #{record.flag,jdbcType=VARCHAR},
      COINSPREMIUMACCEPTIND = #{record.coinspremiumacceptind,jdbcType=VARCHAR},
      COINSPREMIUMCOMPOSE = #{record.coinspremiumcompose,jdbcType=VARCHAR},
      COINSAGENCYPAYIND = #{record.coinsagencypayind,jdbcType=VARCHAR},
      COINSLEVYRATE = #{record.coinslevyrate,jdbcType=DECIMAL},
      COINSLEVY = #{record.coinslevy,jdbcType=DECIMAL},
      COINSLEVYACCEPTIND = #{record.coinslevyacceptind,jdbcType=VARCHAR},
      COINSOTHFEERATE = #{record.coinsothfeerate,jdbcType=DECIMAL},
      COINSOTHFEE = #{record.coinsothfee,jdbcType=DECIMAL},
      COINSOTHFEEACCEPTIND = #{record.coinsothfeeacceptind,jdbcType=VARCHAR},
      COINSCOMPRESERVERATE = #{record.coinscompreserverate,jdbcType=DECIMAL},
      COINSCOMPRESERVE = #{record.coinscompreserve,jdbcType=DECIMAL},
      COINSCOMPRESACCEPTIND = #{record.coinscompresacceptind,jdbcType=VARCHAR},
      DEBITACCEPTER = #{record.debitaccepter,jdbcType=VARCHAR},
      COINSCLAIMIND = #{record.coinsclaimind,jdbcType=VARCHAR},
      SUBPOLICYNO = #{record.subpolicyno,jdbcType=VARCHAR},
      COINSNOTAXPREMIUM = #{record.coinsnotaxpremium,jdbcType=DECIMAL},
      COINSTAXAMOUNT = #{record.coinstaxamount,jdbcType=DECIMAL},
      SECONDLEVELCOINSNAME = #{record.secondlevelcoinsname,jdbcType=VARCHAR},
      RELATEDCOINSPOLICYNO = #{record.relatedcoinspolicyno,jdbcType=VARCHAR},
      RELATEDCOINSCODE = #{record.relatedcoinscode,jdbcType=VARCHAR},
      RELATEDCOINSCNAME = #{record.relatedcoinscname,jdbcType=VARCHAR},
      SEALSTEAMCODE = #{record.sealsteamcode,jdbcType=VARCHAR},
      SEALSTEAMCNAME = #{record.sealsteamcname,jdbcType=VARCHAR},
      SEALSMANCODE = #{record.sealsmancode,jdbcType=VARCHAR},
      SEALSMANCNAME = #{record.sealsmancname,jdbcType=VARCHAR},
      WHOLERATE = #{record.wholerate,jdbcType=DECIMAL},
      ORISINGLENO = #{record.orisingleno,jdbcType=VARCHAR},
      RELATEDCOINSTYPE = #{record.relatedcoinstype,jdbcType=VARCHAR},
      CHANNELDETAILCODE = #{record.channeldetailcode,jdbcType=VARCHAR},
      CHANNELDETAILCNAME = #{record.channeldetailcname,jdbcType=VARCHAR},
      COINSISSUEEXPENSETAX = #{record.coinsissueexpensetax,jdbcType=DECIMAL},
      COINSOTHFEETAX = #{record.coinsothfeetax,jdbcType=DECIMAL},
      COINSINSUREDCNY = #{record.coinsinsuredcny,jdbcType=DECIMAL},
      COINSPREMIUMCNY = #{record.coinspremiumcny,jdbcType=DECIMAL},
      COINSNOTAXPREMIUMCNY = #{record.coinsnotaxpremiumcny,jdbcType=DECIMAL},
      COINSTAXAMOUNTCNY = #{record.coinstaxamountcny,jdbcType=DECIMAL},
      CURRENCYCNY = #{record.currencycny,jdbcType=VARCHAR},
      SERIALNO = #{record.serialno,jdbcType=DECIMAL}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDto" >
    update GUPOLICYCOINSURANCE
    <set >
      <if test="coinspolicyno != null" >
        COINSPOLICYNO = #{coinspolicyno,jdbcType=VARCHAR},
      </if>
      <if test="coinsname != null" >
        COINSNAME = #{coinsname,jdbcType=VARCHAR},
      </if>
      <if test="coinstype != null" >
        COINSTYPE = #{coinstype,jdbcType=VARCHAR},
      </if>
      <if test="coinsrate != null" >
        COINSRATE = #{coinsrate,jdbcType=DECIMAL},
      </if>
      <if test="principalind != null" >
        PRINCIPALIND = #{principalind,jdbcType=VARCHAR},
      </if>
      <if test="leaderind != null" >
        LEADERIND = #{leaderind,jdbcType=VARCHAR},
      </if>
      <if test="chiefadjusterind != null" >
        CHIEFADJUSTERIND = #{chiefadjusterind,jdbcType=VARCHAR},
      </if>
      <if test="currency != null" >
        CURRENCY = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="coinsinsured != null" >
        COINSINSURED = #{coinsinsured,jdbcType=DECIMAL},
      </if>
      <if test="coinspremium != null" >
        COINSPREMIUM = #{coinspremium,jdbcType=DECIMAL},
      </if>
      <if test="coinshandlingrate != null" >
        COINSHANDLINGRATE = #{coinshandlingrate,jdbcType=DECIMAL},
      </if>
      <if test="coinshandlingfee != null" >
        COINSHANDLINGFEE = #{coinshandlingfee,jdbcType=DECIMAL},
      </if>
      <if test="coinsagencyrate != null" >
        COINSAGENCYRATE = #{coinsagencyrate,jdbcType=DECIMAL},
      </if>
      <if test="coinsagencycommission != null" >
        COINSAGENCYCOMMISSION = #{coinsagencycommission,jdbcType=DECIMAL},
      </if>
      <if test="coinsissuerate != null" >
        COINSISSUERATE = #{coinsissuerate,jdbcType=DECIMAL},
      </if>
      <if test="coinsissueexpense != null" >
        COINSISSUEEXPENSE = #{coinsissueexpense,jdbcType=DECIMAL},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="flag != null" >
        FLAG = #{flag,jdbcType=VARCHAR},
      </if>
      <if test="coinspremiumacceptind != null" >
        COINSPREMIUMACCEPTIND = #{coinspremiumacceptind,jdbcType=VARCHAR},
      </if>
      <if test="coinspremiumcompose != null" >
        COINSPREMIUMCOMPOSE = #{coinspremiumcompose,jdbcType=VARCHAR},
      </if>
      <if test="coinsagencypayind != null" >
        COINSAGENCYPAYIND = #{coinsagencypayind,jdbcType=VARCHAR},
      </if>
      <if test="coinslevyrate != null" >
        COINSLEVYRATE = #{coinslevyrate,jdbcType=DECIMAL},
      </if>
      <if test="coinslevy != null" >
        COINSLEVY = #{coinslevy,jdbcType=DECIMAL},
      </if>
      <if test="coinslevyacceptind != null" >
        COINSLEVYACCEPTIND = #{coinslevyacceptind,jdbcType=VARCHAR},
      </if>
      <if test="coinsothfeerate != null" >
        COINSOTHFEERATE = #{coinsothfeerate,jdbcType=DECIMAL},
      </if>
      <if test="coinsothfee != null" >
        COINSOTHFEE = #{coinsothfee,jdbcType=DECIMAL},
      </if>
      <if test="coinsothfeeacceptind != null" >
        COINSOTHFEEACCEPTIND = #{coinsothfeeacceptind,jdbcType=VARCHAR},
      </if>
      <if test="coinscompreserverate != null" >
        COINSCOMPRESERVERATE = #{coinscompreserverate,jdbcType=DECIMAL},
      </if>
      <if test="coinscompreserve != null" >
        COINSCOMPRESERVE = #{coinscompreserve,jdbcType=DECIMAL},
      </if>
      <if test="coinscompresacceptind != null" >
        COINSCOMPRESACCEPTIND = #{coinscompresacceptind,jdbcType=VARCHAR},
      </if>
      <if test="debitaccepter != null" >
        DEBITACCEPTER = #{debitaccepter,jdbcType=VARCHAR},
      </if>
      <if test="coinsclaimind != null" >
        COINSCLAIMIND = #{coinsclaimind,jdbcType=VARCHAR},
      </if>
      <if test="subpolicyno != null" >
        SUBPOLICYNO = #{subpolicyno,jdbcType=VARCHAR},
      </if>
      <if test="coinsnotaxpremium != null" >
        COINSNOTAXPREMIUM = #{coinsnotaxpremium,jdbcType=DECIMAL},
      </if>
      <if test="coinstaxamount != null" >
        COINSTAXAMOUNT = #{coinstaxamount,jdbcType=DECIMAL},
      </if>
      <if test="secondlevelcoinsname != null" >
        SECONDLEVELCOINSNAME = #{secondlevelcoinsname,jdbcType=VARCHAR},
      </if>
      <if test="relatedcoinspolicyno != null" >
        RELATEDCOINSPOLICYNO = #{relatedcoinspolicyno,jdbcType=VARCHAR},
      </if>
      <if test="relatedcoinscode != null" >
        RELATEDCOINSCODE = #{relatedcoinscode,jdbcType=VARCHAR},
      </if>
      <if test="relatedcoinscname != null" >
        RELATEDCOINSCNAME = #{relatedcoinscname,jdbcType=VARCHAR},
      </if>
      <if test="sealsteamcode != null" >
        SEALSTEAMCODE = #{sealsteamcode,jdbcType=VARCHAR},
      </if>
      <if test="sealsteamcname != null" >
        SEALSTEAMCNAME = #{sealsteamcname,jdbcType=VARCHAR},
      </if>
      <if test="sealsmancode != null" >
        SEALSMANCODE = #{sealsmancode,jdbcType=VARCHAR},
      </if>
      <if test="sealsmancname != null" >
        SEALSMANCNAME = #{sealsmancname,jdbcType=VARCHAR},
      </if>
      <if test="wholerate != null" >
        WHOLERATE = #{wholerate,jdbcType=DECIMAL},
      </if>
      <if test="orisingleno != null" >
        ORISINGLENO = #{orisingleno,jdbcType=VARCHAR},
      </if>
      <if test="relatedcoinstype != null" >
        RELATEDCOINSTYPE = #{relatedcoinstype,jdbcType=VARCHAR},
      </if>
      <if test="channeldetailcode != null" >
        CHANNELDETAILCODE = #{channeldetailcode,jdbcType=VARCHAR},
      </if>
      <if test="channeldetailcname != null" >
        CHANNELDETAILCNAME = #{channeldetailcname,jdbcType=VARCHAR},
      </if>
      <if test="coinsissueexpensetax != null" >
        COINSISSUEEXPENSETAX = #{coinsissueexpensetax,jdbcType=DECIMAL},
      </if>
      <if test="coinsothfeetax != null" >
        COINSOTHFEETAX = #{coinsothfeetax,jdbcType=DECIMAL},
      </if>
      <if test="coinsinsuredcny != null" >
        COINSINSUREDCNY = #{coinsinsuredcny,jdbcType=DECIMAL},
      </if>
      <if test="coinspremiumcny != null" >
        COINSPREMIUMCNY = #{coinspremiumcny,jdbcType=DECIMAL},
      </if>
      <if test="coinsnotaxpremiumcny != null" >
        COINSNOTAXPREMIUMCNY = #{coinsnotaxpremiumcny,jdbcType=DECIMAL},
      </if>
      <if test="coinstaxamountcny != null" >
        COINSTAXAMOUNTCNY = #{coinstaxamountcny,jdbcType=DECIMAL},
      </if>
      <if test="currencycny != null" >
        CURRENCYCNY = #{currencycny,jdbcType=VARCHAR},
      </if>
      <if test="serialno != null" >
        SERIALNO = #{serialno,jdbcType=DECIMAL},
      </if>
    </set>
    where POLICYNO = #{policyno,jdbcType=VARCHAR}
      and COINSCODE = #{coinscode,jdbcType=VARCHAR}
      and PLANCODE = #{plancode,jdbcType=VARCHAR}
      and RISKCODE = #{riskcode,jdbcType=VARCHAR}
      and SECONDLEVELCOINSCODE = #{secondlevelcoinscode,jdbcType=VARCHAR}
      and THIRDLEVELCOINSNAME = #{thirdlevelcoinsname,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDto" >
    update GUPOLICYCOINSURANCE
    set COINSPOLICYNO = #{coinspolicyno,jdbcType=VARCHAR},
      COINSNAME = #{coinsname,jdbcType=VARCHAR},
      COINSTYPE = #{coinstype,jdbcType=VARCHAR},
      COINSRATE = #{coinsrate,jdbcType=DECIMAL},
      PRINCIPALIND = #{principalind,jdbcType=VARCHAR},
      LEADERIND = #{leaderind,jdbcType=VARCHAR},
      CHIEFADJUSTERIND = #{chiefadjusterind,jdbcType=VARCHAR},
      CURRENCY = #{currency,jdbcType=VARCHAR},
      COINSINSURED = #{coinsinsured,jdbcType=DECIMAL},
      COINSPREMIUM = #{coinspremium,jdbcType=DECIMAL},
      COINSHANDLINGRATE = #{coinshandlingrate,jdbcType=DECIMAL},
      COINSHANDLINGFEE = #{coinshandlingfee,jdbcType=DECIMAL},
      COINSAGENCYRATE = #{coinsagencyrate,jdbcType=DECIMAL},
      COINSAGENCYCOMMISSION = #{coinsagencycommission,jdbcType=DECIMAL},
      COINSISSUERATE = #{coinsissuerate,jdbcType=DECIMAL},
      COINSISSUEEXPENSE = #{coinsissueexpense,jdbcType=DECIMAL},
      REMARK = #{remark,jdbcType=VARCHAR},
      FLAG = #{flag,jdbcType=VARCHAR},
      COINSPREMIUMACCEPTIND = #{coinspremiumacceptind,jdbcType=VARCHAR},
      COINSPREMIUMCOMPOSE = #{coinspremiumcompose,jdbcType=VARCHAR},
      COINSAGENCYPAYIND = #{coinsagencypayind,jdbcType=VARCHAR},
      COINSLEVYRATE = #{coinslevyrate,jdbcType=DECIMAL},
      COINSLEVY = #{coinslevy,jdbcType=DECIMAL},
      COINSLEVYACCEPTIND = #{coinslevyacceptind,jdbcType=VARCHAR},
      COINSOTHFEERATE = #{coinsothfeerate,jdbcType=DECIMAL},
      COINSOTHFEE = #{coinsothfee,jdbcType=DECIMAL},
      COINSOTHFEEACCEPTIND = #{coinsothfeeacceptind,jdbcType=VARCHAR},
      COINSCOMPRESERVERATE = #{coinscompreserverate,jdbcType=DECIMAL},
      COINSCOMPRESERVE = #{coinscompreserve,jdbcType=DECIMAL},
      COINSCOMPRESACCEPTIND = #{coinscompresacceptind,jdbcType=VARCHAR},
      DEBITACCEPTER = #{debitaccepter,jdbcType=VARCHAR},
      COINSCLAIMIND = #{coinsclaimind,jdbcType=VARCHAR},
      SUBPOLICYNO = #{subpolicyno,jdbcType=VARCHAR},
      COINSNOTAXPREMIUM = #{coinsnotaxpremium,jdbcType=DECIMAL},
      COINSTAXAMOUNT = #{coinstaxamount,jdbcType=DECIMAL},
      SECONDLEVELCOINSNAME = #{secondlevelcoinsname,jdbcType=VARCHAR},
      RELATEDCOINSPOLICYNO = #{relatedcoinspolicyno,jdbcType=VARCHAR},
      RELATEDCOINSCODE = #{relatedcoinscode,jdbcType=VARCHAR},
      RELATEDCOINSCNAME = #{relatedcoinscname,jdbcType=VARCHAR},
      SEALSTEAMCODE = #{sealsteamcode,jdbcType=VARCHAR},
      SEALSTEAMCNAME = #{sealsteamcname,jdbcType=VARCHAR},
      SEALSMANCODE = #{sealsmancode,jdbcType=VARCHAR},
      SEALSMANCNAME = #{sealsmancname,jdbcType=VARCHAR},
      WHOLERATE = #{wholerate,jdbcType=DECIMAL},
      ORISINGLENO = #{orisingleno,jdbcType=VARCHAR},
      RELATEDCOINSTYPE = #{relatedcoinstype,jdbcType=VARCHAR},
      CHANNELDETAILCODE = #{channeldetailcode,jdbcType=VARCHAR},
      CHANNELDETAILCNAME = #{channeldetailcname,jdbcType=VARCHAR},
      COINSISSUEEXPENSETAX = #{coinsissueexpensetax,jdbcType=DECIMAL},
      COINSOTHFEETAX = #{coinsothfeetax,jdbcType=DECIMAL},
      COINSINSUREDCNY = #{coinsinsuredcny,jdbcType=DECIMAL},
      COINSPREMIUMCNY = #{coinspremiumcny,jdbcType=DECIMAL},
      COINSNOTAXPREMIUMCNY = #{coinsnotaxpremiumcny,jdbcType=DECIMAL},
      COINSTAXAMOUNTCNY = #{coinstaxamountcny,jdbcType=DECIMAL},
      CURRENCYCNY = #{currencycny,jdbcType=VARCHAR},
      SERIALNO = #{serialno,jdbcType=DECIMAL}
    where POLICYNO = #{policyno,jdbcType=VARCHAR}
      and COINSCODE = #{coinscode,jdbcType=VARCHAR}
      and PLANCODE = #{plancode,jdbcType=VARCHAR}
      and RISKCODE = #{riskcode,jdbcType=VARCHAR}
      and SECONDLEVELCOINSCODE = #{secondlevelcoinscode,jdbcType=VARCHAR}
      and THIRDLEVELCOINSNAME = #{thirdlevelcoinsname,jdbcType=VARCHAR}
  </update>
</mapper>