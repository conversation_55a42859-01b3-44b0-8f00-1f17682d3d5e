<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicycopycoinsfee.dao.GupolicycopycoinsfeeDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicycopycoinsfee.po.Gupolicycopycoinsfee">
		<id column="ID" property="id" />
		<result column="POLICYNO" property="policyNo" />
		<result column="ENDORNO" property="endorNo" />
		<result column="SERIALNO" property="serialNo" />
		<result column="COINSPOLICYNO" property="coinspolicyno" />
		<result column="COINSCODE" property="coinsCode" />
		<result column="COINSNAME" property="coinsName" />
		<result column="COINSTYPE" property="coinsType" />
		<result column="COINSRATE" property="coinsRate" />
		<result column="PRINCIPALIND" property="principalind" />
		<result column="LEADERIND" property="leaderind" />
		<result column="CHIEFADJUSTERIND" property="chiefadjusterind" />
		<result column="CURRENCY" property="currency" />
		<result column="COINSINSURED" property="coinsinsured" />
		<result column="COINSPREMIUM" property="coinsPremium" />
		<result column="CHANGECOINSINSURED" property="changecoinsinsured" />
		<result column="CHANGECOINSPREMIUM" property="changecoinspremium" />
		<result column="COINSHANDLINGRATE" property="coinshandlingrate" />
		<result column="COINSHANDLINGFEE" property="coinshandlingfee" />
		<result column="CHANGECOINSHANDLINGFEE" property="changecoinshandlingfee" />
		<result column="COINSAGENCYRATE" property="coinsagencyrate" />
		<result column="COINSAGENCYCOMMISSION" property="coinsagencycommission" />
		<result column="CHANGECOINSAGENCYCOMMISSION" property="changecoinsagencycommission" />
		<result column="COINSISSUERATE" property="coinsissuerate" />
		<result column="COINSISSUEEXPENSE" property="coinsissueexpense" />
		<result column="CHANGECOINSISSUEEXPENSE" property="changecoinsissueexpense" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="COINSPREMIUMACCEPTIND" property="coinspremiumacceptind" />
		<result column="COINSPREMIUMCOMPOSE" property="coinspremiumcompose" />
		<result column="COINSAGENCYPAYIND" property="coinsagencypayind" />
		<result column="COINSCLAIMIND" property="coinsclaimind" />
		<result column="PLANCODE" property="plancode" />
		<result column="RISKCODE" property="riskCode" />
		<result column="SUBPOLICYNO" property="subpolicyno" />
		<result column="COINSNOTAXPREMIUM" property="coinsnotaxpremium" />
		<result column="COINSTAXAMOUNT" property="coinstaxamount" />
		<result column="CHANGECOINSNOTAXPREMIUM" property="changecoinsnotaxpremium" />
		<result column="CHANGECOINSTAXAMOUNT" property="changecoinstaxamount" />
		<result column="SECONDLEVELCOINSCODE" property="secondlevelcoinscode" />
		<result column="SECONDLEVELCOINSNAME" property="secondlevelcoinsname" />
		<result column="THIRDLEVELCOINSNAME" property="thirdlevelcoinsname" />
		<result column="SEALSTEAMCODE" property="sealsteamcode" />
		<result column="SEALSTEAMCNAME" property="sealsteamcname" />
		<result column="SEALSMANCODE" property="sealsmancode" />
		<result column="SEALSMANCNAME" property="sealsmancname" />
		<result column="CHANNELDETAILCODE" property="channeldetailcode" />
		<result column="CHANNELDETAILCNAME" property="channeldetailcname" />
		<result column="CURRENCYCNY" property="currencycny" />
		<result column="COINSINSUREDCNY" property="coinsinsuredcny" />
		<result column="COINSPREMIUMCNY" property="coinspremiumcny" />
		<result column="COINSNOTAXPREMIUMCNY" property="coinsnotaxpremiumcny" />
		<result column="COINSTAXAMOUNTCNY" property="coinstaxamountcny" />
		<result column="CHANGECOINSINSUREDCNY" property="changecoinsinsuredcny" />
		<result column="CHANGECOINSPREMIUMCNY" property="changecoinspremiumcny" />
		<result column="CHANGECOINSNOTAXPREMIUMCNY" property="changecoinsnotaxpremiumcny" />
		<result column="CHANGECOINSTAXAMOUNTCNY" property="changecoinstaxamountcny" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		POLICYNO,
		ENDORNO,
		SERIALNO,
		COINSPOLICYNO,
		COINSCODE,
		COINSNAME,
		COINSTYPE,
		COINSRATE,
		PRINCIPALIND,
		LEADERIND,
		CHIEFADJUSTERIND,
		CURRENCY,
		COINSINSURED,
		COINSPREMIUM,
		CHANGECOINSINSURED,
		CHANGECOINSPREMIUM,
		COINSHANDLINGRATE,
		COINSHANDLINGFEE,
		CHANGECOINSHANDLINGFEE,
		COINSAGENCYRATE,
		COINSAGENCYCOMMISSION,
		CHANGECOINSAGENCYCOMMISSION,
		COINSISSUERATE,
		COINSISSUEEXPENSE,
		CHANGECOINSISSUEEXPENSE,
		REMARK,
		FLAG,
		COINSPREMIUMACCEPTIND,
		COINSPREMIUMCOMPOSE,
		COINSAGENCYPAYIND,
		COINSCLAIMIND,
		PLANCODE,
		RISKCODE,
		SUBPOLICYNO,
		COINSNOTAXPREMIUM,
		COINSTAXAMOUNT,
		CHANGECOINSNOTAXPREMIUM,
		CHANGECOINSTAXAMOUNT,
		SECONDLEVELCOINSCODE,
		SECONDLEVELCOINSNAME,
		THIRDLEVELCOINSNAME,
		SEALSTEAMCODE,
		SEALSTEAMCNAME,
		SEALSMANCODE,
		SEALSMANCNAME,
		CHANNELDETAILCODE,
		CHANNELDETAILCNAME,
		CURRENCYCNY,
		COINSINSUREDCNY,
		COINSPREMIUMCNY,
		COINSNOTAXPREMIUMCNY,
		COINSTAXAMOUNTCNY,
		CHANGECOINSINSUREDCNY,
		CHANGECOINSPREMIUMCNY,
		CHANGECOINSNOTAXPREMIUMCNY,
		CHANGECOINSTAXAMOUNTCNY,
		INPUTDATE,
		UPDATESYSDATE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="endorNo != null and endorNo != ''" >
			and ENDORNO = #{endorNo}
		</if>
		<if test="serialNo != null and serialNo != ''" >
			and SERIALNO = #{serialNo}
		</if>
		<if test="coinspolicyno != null and coinspolicyno != ''" >
			and COINSPOLICYNO = #{coinspolicyno}
		</if>
		<if test="coinsCode != null and coinsCode != ''" >
			and COINSCODE = #{coinsCode}
		</if>
		<if test="coinsName != null and coinsName != ''" >
			and COINSNAME = #{coinsName}
		</if>
		<if test="coinsType != null and coinsType != ''" >
			and COINSTYPE = #{coinsType}
		</if>
		<if test="coinsRate != null and coinsRate != ''" >
			and COINSRATE = #{coinsRate}
		</if>
		<if test="principalind != null and principalind != ''" >
			and PRINCIPALIND = #{principalind}
		</if>
		<if test="leaderind != null and leaderind != ''" >
			and LEADERIND = #{leaderind}
		</if>
		<if test="chiefadjusterind != null and chiefadjusterind != ''" >
			and CHIEFADJUSTERIND = #{chiefadjusterind}
		</if>
		<if test="currency != null and currency != ''" >
			and CURRENCY = #{currency}
		</if>
		<if test="coinsinsured != null and coinsinsured != ''" >
			and COINSINSURED = #{coinsinsured}
		</if>
		<if test="coinsPremium != null and coinsPremium != ''" >
			and COINSPREMIUM = #{coinsPremium}
		</if>
		<if test="changecoinsinsured != null and changecoinsinsured != ''" >
			and CHANGECOINSINSURED = #{changecoinsinsured}
		</if>
		<if test="changecoinspremium != null and changecoinspremium != ''" >
			and CHANGECOINSPREMIUM = #{changecoinspremium}
		</if>
		<if test="coinshandlingrate != null and coinshandlingrate != ''" >
			and COINSHANDLINGRATE = #{coinshandlingrate}
		</if>
		<if test="coinshandlingfee != null and coinshandlingfee != ''" >
			and COINSHANDLINGFEE = #{coinshandlingfee}
		</if>
		<if test="changecoinshandlingfee != null and changecoinshandlingfee != ''" >
			and CHANGECOINSHANDLINGFEE = #{changecoinshandlingfee}
		</if>
		<if test="coinsagencyrate != null and coinsagencyrate != ''" >
			and COINSAGENCYRATE = #{coinsagencyrate}
		</if>
		<if test="coinsagencycommission != null and coinsagencycommission != ''" >
			and COINSAGENCYCOMMISSION = #{coinsagencycommission}
		</if>
		<if test="changecoinsagencycommission != null and changecoinsagencycommission != ''" >
			and CHANGECOINSAGENCYCOMMISSION = #{changecoinsagencycommission}
		</if>
		<if test="coinsissuerate != null and coinsissuerate != ''" >
			and COINSISSUERATE = #{coinsissuerate}
		</if>
		<if test="coinsissueexpense != null and coinsissueexpense != ''" >
			and COINSISSUEEXPENSE = #{coinsissueexpense}
		</if>
		<if test="changecoinsissueexpense != null and changecoinsissueexpense != ''" >
			and CHANGECOINSISSUEEXPENSE = #{changecoinsissueexpense}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="coinspremiumacceptind != null and coinspremiumacceptind != ''" >
			and COINSPREMIUMACCEPTIND = #{coinspremiumacceptind}
		</if>
		<if test="coinspremiumcompose != null and coinspremiumcompose != ''" >
			and COINSPREMIUMCOMPOSE = #{coinspremiumcompose}
		</if>
		<if test="coinsagencypayind != null and coinsagencypayind != ''" >
			and COINSAGENCYPAYIND = #{coinsagencypayind}
		</if>
		<if test="coinsclaimind != null and coinsclaimind != ''" >
			and COINSCLAIMIND = #{coinsclaimind}
		</if>
		<if test="plancode != null and plancode != ''" >
			and PLANCODE = #{plancode}
		</if>
		<if test="riskCode != null and riskCode != ''" >
			and RISKCODE = #{riskCode}
		</if>
		<if test="subpolicyno != null and subpolicyno != ''" >
			and SUBPOLICYNO = #{subpolicyno}
		</if>
		<if test="coinsnotaxpremium != null and coinsnotaxpremium != ''" >
			and COINSNOTAXPREMIUM = #{coinsnotaxpremium}
		</if>
		<if test="coinstaxamount != null and coinstaxamount != ''" >
			and COINSTAXAMOUNT = #{coinstaxamount}
		</if>
		<if test="changecoinsnotaxpremium != null and changecoinsnotaxpremium != ''" >
			and CHANGECOINSNOTAXPREMIUM = #{changecoinsnotaxpremium}
		</if>
		<if test="changecoinstaxamount != null and changecoinstaxamount != ''" >
			and CHANGECOINSTAXAMOUNT = #{changecoinstaxamount}
		</if>
		<if test="secondlevelcoinscode != null and secondlevelcoinscode != ''" >
			and SECONDLEVELCOINSCODE = #{secondlevelcoinscode}
		</if>
		<if test="secondlevelcoinsname != null and secondlevelcoinsname != ''" >
			and SECONDLEVELCOINSNAME = #{secondlevelcoinsname}
		</if>
		<if test="thirdlevelcoinsname != null and thirdlevelcoinsname != ''" >
			and THIRDLEVELCOINSNAME = #{thirdlevelcoinsname}
		</if>
		<if test="sealsteamcode != null and sealsteamcode != ''" >
			and SEALSTEAMCODE = #{sealsteamcode}
		</if>
		<if test="sealsteamcname != null and sealsteamcname != ''" >
			and SEALSTEAMCNAME = #{sealsteamcname}
		</if>
		<if test="sealsmancode != null and sealsmancode != ''" >
			and SEALSMANCODE = #{sealsmancode}
		</if>
		<if test="sealsmancname != null and sealsmancname != ''" >
			and SEALSMANCNAME = #{sealsmancname}
		</if>
		<if test="channeldetailcode != null and channeldetailcode != ''" >
			and CHANNELDETAILCODE = #{channeldetailcode}
		</if>
		<if test="channeldetailcname != null and channeldetailcname != ''" >
			and CHANNELDETAILCNAME = #{channeldetailcname}
		</if>
		<if test="currencycny != null and currencycny != ''" >
			and CURRENCYCNY = #{currencycny}
		</if>
		<if test="coinsinsuredcny != null and coinsinsuredcny != ''" >
			and COINSINSUREDCNY = #{coinsinsuredcny}
		</if>
		<if test="coinspremiumcny != null and coinspremiumcny != ''" >
			and COINSPREMIUMCNY = #{coinspremiumcny}
		</if>
		<if test="coinsnotaxpremiumcny != null and coinsnotaxpremiumcny != ''" >
			and COINSNOTAXPREMIUMCNY = #{coinsnotaxpremiumcny}
		</if>
		<if test="coinstaxamountcny != null and coinstaxamountcny != ''" >
			and COINSTAXAMOUNTCNY = #{coinstaxamountcny}
		</if>
		<if test="changecoinsinsuredcny != null and changecoinsinsuredcny != ''" >
			and CHANGECOINSINSUREDCNY = #{changecoinsinsuredcny}
		</if>
		<if test="changecoinspremiumcny != null and changecoinspremiumcny != ''" >
			and CHANGECOINSPREMIUMCNY = #{changecoinspremiumcny}
		</if>
		<if test="changecoinsnotaxpremiumcny != null and changecoinsnotaxpremiumcny != ''" >
			and CHANGECOINSNOTAXPREMIUMCNY = #{changecoinsnotaxpremiumcny}
		</if>
		<if test="changecoinstaxamountcny != null and changecoinstaxamountcny != ''" >
			and CHANGECOINSTAXAMOUNTCNY = #{changecoinstaxamountcny}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYCOINSFEE
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYCOINSFEE
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYCOINSFEE
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicycopycoinsfee.po.Gupolicycopycoinsfee">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYCOPYCOINSFEE
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYCOPYCOINSFEE
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicycopycoinsfee.po.Gupolicycopycoinsfee">
		insert into GUPOLICYCOPYCOINSFEE (
			ID,
			POLICYNO,
			ENDORNO,
			SERIALNO,
			COINSPOLICYNO,
			COINSCODE,
			COINSNAME,
			COINSTYPE,
			COINSRATE,
			PRINCIPALIND,
			LEADERIND,
			CHIEFADJUSTERIND,
			CURRENCY,
			COINSINSURED,
			COINSPREMIUM,
			CHANGECOINSINSURED,
			CHANGECOINSPREMIUM,
			COINSHANDLINGRATE,
			COINSHANDLINGFEE,
			CHANGECOINSHANDLINGFEE,
			COINSAGENCYRATE,
			COINSAGENCYCOMMISSION,
			CHANGECOINSAGENCYCOMMISSION,
			COINSISSUERATE,
			COINSISSUEEXPENSE,
			CHANGECOINSISSUEEXPENSE,
			REMARK,
			FLAG,
			COINSPREMIUMACCEPTIND,
			COINSPREMIUMCOMPOSE,
			COINSAGENCYPAYIND,
			COINSCLAIMIND,
			PLANCODE,
			RISKCODE,
			SUBPOLICYNO,
			COINSNOTAXPREMIUM,
			COINSTAXAMOUNT,
			CHANGECOINSNOTAXPREMIUM,
			CHANGECOINSTAXAMOUNT,
			SECONDLEVELCOINSCODE,
			SECONDLEVELCOINSNAME,
			THIRDLEVELCOINSNAME,
			SEALSTEAMCODE,
			SEALSTEAMCNAME,
			SEALSMANCODE,
			SEALSMANCNAME,
			CHANNELDETAILCODE,
			CHANNELDETAILCNAME,
			CURRENCYCNY,
			COINSINSUREDCNY,
			COINSPREMIUMCNY,
			COINSNOTAXPREMIUMCNY,
			COINSTAXAMOUNTCNY,
			CHANGECOINSINSUREDCNY,
			CHANGECOINSPREMIUMCNY,
			CHANGECOINSNOTAXPREMIUMCNY,
			CHANGECOINSTAXAMOUNTCNY,
			INPUTDATE,
			UPDATESYSDATE
		) values (
			#{id},
			#{policyNo},
			#{endorNo},
			#{serialNo},
			#{coinspolicyno},
			#{coinsCode},
			#{coinsName},
			#{coinsType},
			#{coinsRate},
			#{principalind},
			#{leaderind},
			#{chiefadjusterind},
			#{currency},
			#{coinsinsured},
			#{coinsPremium},
			#{changecoinsinsured},
			#{changecoinspremium},
			#{coinshandlingrate},
			#{coinshandlingfee},
			#{changecoinshandlingfee},
			#{coinsagencyrate},
			#{coinsagencycommission},
			#{changecoinsagencycommission},
			#{coinsissuerate},
			#{coinsissueexpense},
			#{changecoinsissueexpense},
			#{remark},
			#{flag},
			#{coinspremiumacceptind},
			#{coinspremiumcompose},
			#{coinsagencypayind},
			#{coinsclaimind},
			#{plancode},
			#{riskCode},
			#{subpolicyno},
			#{coinsnotaxpremium},
			#{coinstaxamount},
			#{changecoinsnotaxpremium},
			#{changecoinstaxamount},
			#{secondlevelcoinscode},
			#{secondlevelcoinsname},
			#{thirdlevelcoinsname},
			#{sealsteamcode},
			#{sealsteamcname},
			#{sealsmancode},
			#{sealsmancname},
			#{channeldetailcode},
			#{channeldetailcname},
			#{currencycny},
			#{coinsinsuredcny},
			#{coinspremiumcny},
			#{coinsnotaxpremiumcny},
			#{coinstaxamountcny},
			#{changecoinsinsuredcny},
			#{changecoinspremiumcny},
			#{changecoinsnotaxpremiumcny},
			#{changecoinstaxamountcny},
			#{inputDate},
			#{updatesysdate}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicycopycoinsfee.po.Gupolicycopycoinsfee">
		insert into GUPOLICYCOPYCOINSFEE
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="endorNo != null" >
				ENDORNO,
			</if>
			<if test="serialNo != null" >
				SERIALNO,
			</if>
			<if test="coinspolicyno != null" >
				COINSPOLICYNO,
			</if>
			<if test="coinsCode != null" >
				COINSCODE,
			</if>
			<if test="coinsName != null" >
				COINSNAME,
			</if>
			<if test="coinsType != null" >
				COINSTYPE,
			</if>
			<if test="coinsRate != null" >
				COINSRATE,
			</if>
			<if test="principalind != null" >
				PRINCIPALIND,
			</if>
			<if test="leaderind != null" >
				LEADERIND,
			</if>
			<if test="chiefadjusterind != null" >
				CHIEFADJUSTERIND,
			</if>
			<if test="currency != null" >
				CURRENCY,
			</if>
			<if test="coinsinsured != null" >
				COINSINSURED,
			</if>
			<if test="coinsPremium != null" >
				COINSPREMIUM,
			</if>
			<if test="changecoinsinsured != null" >
				CHANGECOINSINSURED,
			</if>
			<if test="changecoinspremium != null" >
				CHANGECOINSPREMIUM,
			</if>
			<if test="coinshandlingrate != null" >
				COINSHANDLINGRATE,
			</if>
			<if test="coinshandlingfee != null" >
				COINSHANDLINGFEE,
			</if>
			<if test="changecoinshandlingfee != null" >
				CHANGECOINSHANDLINGFEE,
			</if>
			<if test="coinsagencyrate != null" >
				COINSAGENCYRATE,
			</if>
			<if test="coinsagencycommission != null" >
				COINSAGENCYCOMMISSION,
			</if>
			<if test="changecoinsagencycommission != null" >
				CHANGECOINSAGENCYCOMMISSION,
			</if>
			<if test="coinsissuerate != null" >
				COINSISSUERATE,
			</if>
			<if test="coinsissueexpense != null" >
				COINSISSUEEXPENSE,
			</if>
			<if test="changecoinsissueexpense != null" >
				CHANGECOINSISSUEEXPENSE,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="coinspremiumacceptind != null" >
				COINSPREMIUMACCEPTIND,
			</if>
			<if test="coinspremiumcompose != null" >
				COINSPREMIUMCOMPOSE,
			</if>
			<if test="coinsagencypayind != null" >
				COINSAGENCYPAYIND,
			</if>
			<if test="coinsclaimind != null" >
				COINSCLAIMIND,
			</if>
			<if test="plancode != null" >
				PLANCODE,
			</if>
			<if test="riskCode != null" >
				RISKCODE,
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO,
			</if>
			<if test="coinsnotaxpremium != null" >
				COINSNOTAXPREMIUM,
			</if>
			<if test="coinstaxamount != null" >
				COINSTAXAMOUNT,
			</if>
			<if test="changecoinsnotaxpremium != null" >
				CHANGECOINSNOTAXPREMIUM,
			</if>
			<if test="changecoinstaxamount != null" >
				CHANGECOINSTAXAMOUNT,
			</if>
			<if test="secondlevelcoinscode != null" >
				SECONDLEVELCOINSCODE,
			</if>
			<if test="secondlevelcoinsname != null" >
				SECONDLEVELCOINSNAME,
			</if>
			<if test="thirdlevelcoinsname != null" >
				THIRDLEVELCOINSNAME,
			</if>
			<if test="sealsteamcode != null" >
				SEALSTEAMCODE,
			</if>
			<if test="sealsteamcname != null" >
				SEALSTEAMCNAME,
			</if>
			<if test="sealsmancode != null" >
				SEALSMANCODE,
			</if>
			<if test="sealsmancname != null" >
				SEALSMANCNAME,
			</if>
			<if test="channeldetailcode != null" >
				CHANNELDETAILCODE,
			</if>
			<if test="channeldetailcname != null" >
				CHANNELDETAILCNAME,
			</if>
			<if test="currencycny != null" >
				CURRENCYCNY,
			</if>
			<if test="coinsinsuredcny != null" >
				COINSINSUREDCNY,
			</if>
			<if test="coinspremiumcny != null" >
				COINSPREMIUMCNY,
			</if>
			<if test="coinsnotaxpremiumcny != null" >
				COINSNOTAXPREMIUMCNY,
			</if>
			<if test="coinstaxamountcny != null" >
				COINSTAXAMOUNTCNY,
			</if>
			<if test="changecoinsinsuredcny != null" >
				CHANGECOINSINSUREDCNY,
			</if>
			<if test="changecoinspremiumcny != null" >
				CHANGECOINSPREMIUMCNY,
			</if>
			<if test="changecoinsnotaxpremiumcny != null" >
				CHANGECOINSNOTAXPREMIUMCNY,
			</if>
			<if test="changecoinstaxamountcny != null" >
				CHANGECOINSTAXAMOUNTCNY,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="endorNo != null" >
				#{endorNo},
			</if>
			<if test="serialNo != null" >
				#{serialNo},
			</if>
			<if test="coinspolicyno != null" >
				#{coinspolicyno},
			</if>
			<if test="coinsCode != null" >
				#{coinsCode},
			</if>
			<if test="coinsName != null" >
				#{coinsName},
			</if>
			<if test="coinsType != null" >
				#{coinsType},
			</if>
			<if test="coinsRate != null" >
				#{coinsRate},
			</if>
			<if test="principalind != null" >
				#{principalind},
			</if>
			<if test="leaderind != null" >
				#{leaderind},
			</if>
			<if test="chiefadjusterind != null" >
				#{chiefadjusterind},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
			<if test="coinsinsured != null" >
				#{coinsinsured},
			</if>
			<if test="coinsPremium != null" >
				#{coinsPremium},
			</if>
			<if test="changecoinsinsured != null" >
				#{changecoinsinsured},
			</if>
			<if test="changecoinspremium != null" >
				#{changecoinspremium},
			</if>
			<if test="coinshandlingrate != null" >
				#{coinshandlingrate},
			</if>
			<if test="coinshandlingfee != null" >
				#{coinshandlingfee},
			</if>
			<if test="changecoinshandlingfee != null" >
				#{changecoinshandlingfee},
			</if>
			<if test="coinsagencyrate != null" >
				#{coinsagencyrate},
			</if>
			<if test="coinsagencycommission != null" >
				#{coinsagencycommission},
			</if>
			<if test="changecoinsagencycommission != null" >
				#{changecoinsagencycommission},
			</if>
			<if test="coinsissuerate != null" >
				#{coinsissuerate},
			</if>
			<if test="coinsissueexpense != null" >
				#{coinsissueexpense},
			</if>
			<if test="changecoinsissueexpense != null" >
				#{changecoinsissueexpense},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="coinspremiumacceptind != null" >
				#{coinspremiumacceptind},
			</if>
			<if test="coinspremiumcompose != null" >
				#{coinspremiumcompose},
			</if>
			<if test="coinsagencypayind != null" >
				#{coinsagencypayind},
			</if>
			<if test="coinsclaimind != null" >
				#{coinsclaimind},
			</if>
			<if test="plancode != null" >
				#{plancode},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="subpolicyno != null" >
				#{subpolicyno},
			</if>
			<if test="coinsnotaxpremium != null" >
				#{coinsnotaxpremium},
			</if>
			<if test="coinstaxamount != null" >
				#{coinstaxamount},
			</if>
			<if test="changecoinsnotaxpremium != null" >
				#{changecoinsnotaxpremium},
			</if>
			<if test="changecoinstaxamount != null" >
				#{changecoinstaxamount},
			</if>
			<if test="secondlevelcoinscode != null" >
				#{secondlevelcoinscode},
			</if>
			<if test="secondlevelcoinsname != null" >
				#{secondlevelcoinsname},
			</if>
			<if test="thirdlevelcoinsname != null" >
				#{thirdlevelcoinsname},
			</if>
			<if test="sealsteamcode != null" >
				#{sealsteamcode},
			</if>
			<if test="sealsteamcname != null" >
				#{sealsteamcname},
			</if>
			<if test="sealsmancode != null" >
				#{sealsmancode},
			</if>
			<if test="sealsmancname != null" >
				#{sealsmancname},
			</if>
			<if test="channeldetailcode != null" >
				#{channeldetailcode},
			</if>
			<if test="channeldetailcname != null" >
				#{channeldetailcname},
			</if>
			<if test="currencycny != null" >
				#{currencycny},
			</if>
			<if test="coinsinsuredcny != null" >
				#{coinsinsuredcny},
			</if>
			<if test="coinspremiumcny != null" >
				#{coinspremiumcny},
			</if>
			<if test="coinsnotaxpremiumcny != null" >
				#{coinsnotaxpremiumcny},
			</if>
			<if test="coinstaxamountcny != null" >
				#{coinstaxamountcny},
			</if>
			<if test="changecoinsinsuredcny != null" >
				#{changecoinsinsuredcny},
			</if>
			<if test="changecoinspremiumcny != null" >
				#{changecoinspremiumcny},
			</if>
			<if test="changecoinsnotaxpremiumcny != null" >
				#{changecoinsnotaxpremiumcny},
			</if>
			<if test="changecoinstaxamountcny != null" >
				#{changecoinstaxamountcny},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicycopycoinsfee.po.Gupolicycopycoinsfee">
		update GUPOLICYCOPYCOINSFEE 
		<set>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="endorNo != null" >
				ENDORNO=#{endorNo},
			</if>
			<if test="serialNo != null" >
				SERIALNO=#{serialNo},
			</if>
			<if test="coinspolicyno != null" >
				COINSPOLICYNO=#{coinspolicyno},
			</if>
			<if test="coinsCode != null" >
				COINSCODE=#{coinsCode},
			</if>
			<if test="coinsName != null" >
				COINSNAME=#{coinsName},
			</if>
			<if test="coinsType != null" >
				COINSTYPE=#{coinsType},
			</if>
			<if test="coinsRate != null" >
				COINSRATE=#{coinsRate},
			</if>
			<if test="principalind != null" >
				PRINCIPALIND=#{principalind},
			</if>
			<if test="leaderind != null" >
				LEADERIND=#{leaderind},
			</if>
			<if test="chiefadjusterind != null" >
				CHIEFADJUSTERIND=#{chiefadjusterind},
			</if>
			<if test="currency != null" >
				CURRENCY=#{currency},
			</if>
			<if test="coinsinsured != null" >
				COINSINSURED=#{coinsinsured},
			</if>
			<if test="coinsPremium != null" >
				COINSPREMIUM=#{coinsPremium},
			</if>
			<if test="changecoinsinsured != null" >
				CHANGECOINSINSURED=#{changecoinsinsured},
			</if>
			<if test="changecoinspremium != null" >
				CHANGECOINSPREMIUM=#{changecoinspremium},
			</if>
			<if test="coinshandlingrate != null" >
				COINSHANDLINGRATE=#{coinshandlingrate},
			</if>
			<if test="coinshandlingfee != null" >
				COINSHANDLINGFEE=#{coinshandlingfee},
			</if>
			<if test="changecoinshandlingfee != null" >
				CHANGECOINSHANDLINGFEE=#{changecoinshandlingfee},
			</if>
			<if test="coinsagencyrate != null" >
				COINSAGENCYRATE=#{coinsagencyrate},
			</if>
			<if test="coinsagencycommission != null" >
				COINSAGENCYCOMMISSION=#{coinsagencycommission},
			</if>
			<if test="changecoinsagencycommission != null" >
				CHANGECOINSAGENCYCOMMISSION=#{changecoinsagencycommission},
			</if>
			<if test="coinsissuerate != null" >
				COINSISSUERATE=#{coinsissuerate},
			</if>
			<if test="coinsissueexpense != null" >
				COINSISSUEEXPENSE=#{coinsissueexpense},
			</if>
			<if test="changecoinsissueexpense != null" >
				CHANGECOINSISSUEEXPENSE=#{changecoinsissueexpense},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="coinspremiumacceptind != null" >
				COINSPREMIUMACCEPTIND=#{coinspremiumacceptind},
			</if>
			<if test="coinspremiumcompose != null" >
				COINSPREMIUMCOMPOSE=#{coinspremiumcompose},
			</if>
			<if test="coinsagencypayind != null" >
				COINSAGENCYPAYIND=#{coinsagencypayind},
			</if>
			<if test="coinsclaimind != null" >
				COINSCLAIMIND=#{coinsclaimind},
			</if>
			<if test="plancode != null" >
				PLANCODE=#{plancode},
			</if>
			<if test="riskCode != null" >
				RISKCODE=#{riskCode},
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO=#{subpolicyno},
			</if>
			<if test="coinsnotaxpremium != null" >
				COINSNOTAXPREMIUM=#{coinsnotaxpremium},
			</if>
			<if test="coinstaxamount != null" >
				COINSTAXAMOUNT=#{coinstaxamount},
			</if>
			<if test="changecoinsnotaxpremium != null" >
				CHANGECOINSNOTAXPREMIUM=#{changecoinsnotaxpremium},
			</if>
			<if test="changecoinstaxamount != null" >
				CHANGECOINSTAXAMOUNT=#{changecoinstaxamount},
			</if>
			<if test="secondlevelcoinscode != null" >
				SECONDLEVELCOINSCODE=#{secondlevelcoinscode},
			</if>
			<if test="secondlevelcoinsname != null" >
				SECONDLEVELCOINSNAME=#{secondlevelcoinsname},
			</if>
			<if test="thirdlevelcoinsname != null" >
				THIRDLEVELCOINSNAME=#{thirdlevelcoinsname},
			</if>
			<if test="sealsteamcode != null" >
				SEALSTEAMCODE=#{sealsteamcode},
			</if>
			<if test="sealsteamcname != null" >
				SEALSTEAMCNAME=#{sealsteamcname},
			</if>
			<if test="sealsmancode != null" >
				SEALSMANCODE=#{sealsmancode},
			</if>
			<if test="sealsmancname != null" >
				SEALSMANCNAME=#{sealsmancname},
			</if>
			<if test="channeldetailcode != null" >
				CHANNELDETAILCODE=#{channeldetailcode},
			</if>
			<if test="channeldetailcname != null" >
				CHANNELDETAILCNAME=#{channeldetailcname},
			</if>
			<if test="currencycny != null" >
				CURRENCYCNY=#{currencycny},
			</if>
			<if test="coinsinsuredcny != null" >
				COINSINSUREDCNY=#{coinsinsuredcny},
			</if>
			<if test="coinspremiumcny != null" >
				COINSPREMIUMCNY=#{coinspremiumcny},
			</if>
			<if test="coinsnotaxpremiumcny != null" >
				COINSNOTAXPREMIUMCNY=#{coinsnotaxpremiumcny},
			</if>
			<if test="coinstaxamountcny != null" >
				COINSTAXAMOUNTCNY=#{coinstaxamountcny},
			</if>
			<if test="changecoinsinsuredcny != null" >
				CHANGECOINSINSUREDCNY=#{changecoinsinsuredcny},
			</if>
			<if test="changecoinspremiumcny != null" >
				CHANGECOINSPREMIUMCNY=#{changecoinspremiumcny},
			</if>
			<if test="changecoinsnotaxpremiumcny != null" >
				CHANGECOINSNOTAXPREMIUMCNY=#{changecoinsnotaxpremiumcny},
			</if>
			<if test="changecoinstaxamountcny != null" >
				CHANGECOINSTAXAMOUNTCNY=#{changecoinstaxamountcny},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicycopycoinsfee.po.Gupolicycopycoinsfee">
		update GUPOLICYCOPYCOINSFEE set
			POLICYNO=#{policyNo},
			ENDORNO=#{endorNo},
			SERIALNO=#{serialNo},
			COINSPOLICYNO=#{coinspolicyno},
			COINSCODE=#{coinsCode},
			COINSNAME=#{coinsName},
			COINSTYPE=#{coinsType},
			COINSRATE=#{coinsRate},
			PRINCIPALIND=#{principalind},
			LEADERIND=#{leaderind},
			CHIEFADJUSTERIND=#{chiefadjusterind},
			CURRENCY=#{currency},
			COINSINSURED=#{coinsinsured},
			COINSPREMIUM=#{coinsPremium},
			CHANGECOINSINSURED=#{changecoinsinsured},
			CHANGECOINSPREMIUM=#{changecoinspremium},
			COINSHANDLINGRATE=#{coinshandlingrate},
			COINSHANDLINGFEE=#{coinshandlingfee},
			CHANGECOINSHANDLINGFEE=#{changecoinshandlingfee},
			COINSAGENCYRATE=#{coinsagencyrate},
			COINSAGENCYCOMMISSION=#{coinsagencycommission},
			CHANGECOINSAGENCYCOMMISSION=#{changecoinsagencycommission},
			COINSISSUERATE=#{coinsissuerate},
			COINSISSUEEXPENSE=#{coinsissueexpense},
			CHANGECOINSISSUEEXPENSE=#{changecoinsissueexpense},
			REMARK=#{remark},
			FLAG=#{flag},
			COINSPREMIUMACCEPTIND=#{coinspremiumacceptind},
			COINSPREMIUMCOMPOSE=#{coinspremiumcompose},
			COINSAGENCYPAYIND=#{coinsagencypayind},
			COINSCLAIMIND=#{coinsclaimind},
			PLANCODE=#{plancode},
			RISKCODE=#{riskCode},
			SUBPOLICYNO=#{subpolicyno},
			COINSNOTAXPREMIUM=#{coinsnotaxpremium},
			COINSTAXAMOUNT=#{coinstaxamount},
			CHANGECOINSNOTAXPREMIUM=#{changecoinsnotaxpremium},
			CHANGECOINSTAXAMOUNT=#{changecoinstaxamount},
			SECONDLEVELCOINSCODE=#{secondlevelcoinscode},
			SECONDLEVELCOINSNAME=#{secondlevelcoinsname},
			THIRDLEVELCOINSNAME=#{thirdlevelcoinsname},
			SEALSTEAMCODE=#{sealsteamcode},
			SEALSTEAMCNAME=#{sealsteamcname},
			SEALSMANCODE=#{sealsmancode},
			SEALSMANCNAME=#{sealsmancname},
			CHANNELDETAILCODE=#{channeldetailcode},
			CHANNELDETAILCNAME=#{channeldetailcname},
			CURRENCYCNY=#{currencycny},
			COINSINSUREDCNY=#{coinsinsuredcny},
			COINSPREMIUMCNY=#{coinspremiumcny},
			COINSNOTAXPREMIUMCNY=#{coinsnotaxpremiumcny},
			COINSTAXAMOUNTCNY=#{coinstaxamountcny},
			CHANGECOINSINSUREDCNY=#{changecoinsinsuredcny},
			CHANGECOINSPREMIUMCNY=#{changecoinspremiumcny},
			CHANGECOINSNOTAXPREMIUMCNY=#{changecoinsnotaxpremiumcny},
			CHANGECOINSTAXAMOUNTCNY=#{changecoinstaxamountcny},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
		where ID = #{id}	</update>
</mapper>
