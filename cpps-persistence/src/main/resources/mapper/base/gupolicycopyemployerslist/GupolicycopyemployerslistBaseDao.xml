<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicycopyemployerslist.dao.GupolicycopyemployerslistDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicycopyemployerslist.po.Gupolicycopyemployerslist">
		<id column="ID" property="id" />
		<result column="POLICYNO" property="policyNo" />
		<result column="ENDORNO" property="endorNo" />
		<result column="SUBPOLICYNO" property="subpolicyno" />
		<result column="PLANID" property="planid" />
		<result column="ITEMNO" property="itemNo" />
		<result column="ITEMDETAILNO" property="itemdetailno" />
		<result column="DYNAMICTARGETTYPE" property="dynamictargettype" />
		<result column="LISTSEQNO" property="listseqno" />
		<result column="TARGETFLAG" property="targetflag" />
		<result column="EMPNAME" property="empname" />
		<result column="EMPSEX" property="empsex" />
		<result column="EMPBIRTHDAY" property="empbirthday" />
		<result column="EMPIDENTIFYTYPE" property="empidentifytype" />
		<result column="EMPIDENTIFYNUMBER" property="empidentifynumber" />
		<result column="OCCUPATIONCODE" property="occupationCode" />
		<result column="OCCUPATIONNAME" property="occupationname" />
		<result column="OCCUPATIONLEVEL" property="occupationlevel" />
		<result column="ENTRYDATE" property="entrydate" />
		<result column="RESIGNATIONDATE" property="resignationdate" />
		<result column="EFFECTIVEDATE" property="effectivedate" />
		<result column="ENDDATE" property="endDate" />
		<result column="CURRENCY" property="currency" />
		<result column="MONTHPAY" property="monthPay" />
		<result column="EMPINSURED" property="empinsured" />
		<result column="EMPPREMIUM" property="emppremium" />
		<result column="CHANGEEMPINSURED" property="changeempinsured" />
		<result column="CHANGEEMPPREMIUM" property="changeemppremium" />
		<result column="PROVINCECODE" property="provincecode" />
		<result column="CITYCODE" property="citycode" />
		<result column="COUNTYCODE" property="countycode" />
		<result column="MONEYLAUNDERINGIND" property="moneylaunderingind" />
		<result column="LISTBELONGIND" property="listbelongind" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
		<result column="PROJECT" property="project" />
		<result column="HOMEADDRESS" property="homeaddress" />
		<result column="HOMETEL" property="hometel" />
		<result column="DEPARTMENT" property="department" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		POLICYNO,
		ENDORNO,
		SUBPOLICYNO,
		PLANID,
		ITEMNO,
		ITEMDETAILNO,
		DYNAMICTARGETTYPE,
		LISTSEQNO,
		TARGETFLAG,
		EMPNAME,
		EMPSEX,
		EMPBIRTHDAY,
		EMPIDENTIFYTYPE,
		EMPIDENTIFYNUMBER,
		OCCUPATIONCODE,
		OCCUPATIONNAME,
		OCCUPATIONLEVEL,
		ENTRYDATE,
		RESIGNATIONDATE,
		EFFECTIVEDATE,
		ENDDATE,
		CURRENCY,
		MONTHPAY,
		EMPINSURED,
		EMPPREMIUM,
		CHANGEEMPINSURED,
		CHANGEEMPPREMIUM,
		PROVINCECODE,
		CITYCODE,
		COUNTYCODE,
		MONEYLAUNDERINGIND,
		LISTBELONGIND,
		INPUTDATE,
		UPDATESYSDATE,
		project,
		homeaddress,
		hometel,
		department
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="endorNo != null and endorNo != ''" >
			and ENDORNO = #{endorNo}
		</if>
		<if test="subpolicyno != null and subpolicyno != ''" >
			and SUBPOLICYNO = #{subpolicyno}
		</if>
		<if test="planid != null and planid != ''" >
			and PLANID = #{planid}
		</if>
		<if test="itemNo != null and itemNo != ''" >
			and ITEMNO = #{itemNo}
		</if>
		<if test="itemdetailno != null and itemdetailno != ''" >
			and ITEMDETAILNO = #{itemdetailno}
		</if>
		<if test="dynamictargettype != null and dynamictargettype != ''" >
			and DYNAMICTARGETTYPE = #{dynamictargettype}
		</if>
		<if test="listseqno != null and listseqno != ''" >
			and LISTSEQNO = #{listseqno}
		</if>
		<if test="targetflag != null and targetflag != ''" >
			and TARGETFLAG = #{targetflag}
		</if>
		<if test="empname != null and empname != ''" >
			and EMPNAME = #{empname}
		</if>
		<if test="empsex != null and empsex != ''" >
			and EMPSEX = #{empsex}
		</if>
		<if test="empbirthday != null and empbirthday != ''" >
			and EMPBIRTHDAY = #{empbirthday}
		</if>
		<if test="empidentifytype != null and empidentifytype != ''" >
			and EMPIDENTIFYTYPE = #{empidentifytype}
		</if>
		<if test="empidentifynumber != null and empidentifynumber != ''" >
			and EMPIDENTIFYNUMBER = #{empidentifynumber}
		</if>
		<if test="occupationCode != null and occupationCode != ''" >
			and OCCUPATIONCODE = #{occupationCode}
		</if>
		<if test="occupationname != null and occupationname != ''" >
			and OCCUPATIONNAME = #{occupationname}
		</if>
		<if test="occupationlevel != null and occupationlevel != ''" >
			and OCCUPATIONLEVEL = #{occupationlevel}
		</if>
		<if test="entrydate != null and entrydate != ''" >
			and ENTRYDATE = #{entrydate}
		</if>
		<if test="resignationdate != null and resignationdate != ''" >
			and RESIGNATIONDATE = #{resignationdate}
		</if>
		<if test="effectivedate != null and effectivedate != ''" >
			and EFFECTIVEDATE = #{effectivedate}
		</if>
		<if test="endDate != null and endDate != ''" >
			and ENDDATE = #{endDate}
		</if>
		<if test="currency != null and currency != ''" >
			and CURRENCY = #{currency}
		</if>
		<if test="monthPay != null and monthPay != ''" >
			and MONTHPAY = #{monthPay}
		</if>
		<if test="empinsured != null and empinsured != ''" >
			and EMPINSURED = #{empinsured}
		</if>
		<if test="emppremium != null and emppremium != ''" >
			and EMPPREMIUM = #{emppremium}
		</if>
		<if test="changeempinsured != null and changeempinsured != ''" >
			and CHANGEEMPINSURED = #{changeempinsured}
		</if>
		<if test="changeemppremium != null and changeemppremium != ''" >
			and CHANGEEMPPREMIUM = #{changeemppremium}
		</if>
		<if test="provincecode != null and provincecode != ''" >
			and PROVINCECODE = #{provincecode}
		</if>
		<if test="citycode != null and citycode != ''" >
			and CITYCODE = #{citycode}
		</if>
		<if test="countycode != null and countycode != ''" >
			and COUNTYCODE = #{countycode}
		</if>
		<if test="moneylaunderingind != null and moneylaunderingind != ''" >
			and MONEYLAUNDERINGIND = #{moneylaunderingind}
		</if>
		<if test="listbelongind != null and listbelongind != ''" >
			and LISTBELONGIND = #{listbelongind}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYEMPLOYERSLIST
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYEMPLOYERSLIST
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYEMPLOYERSLIST
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicycopyemployerslist.po.Gupolicycopyemployerslist">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYCOPYEMPLOYERSLIST
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYCOPYEMPLOYERSLIST
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicycopyemployerslist.po.Gupolicycopyemployerslist">
		insert into GUPOLICYCOPYEMPLOYERSLIST (
			ID,
			POLICYNO,
			ENDORNO,
			SUBPOLICYNO,
			PLANID,
			ITEMNO,
			ITEMDETAILNO,
			DYNAMICTARGETTYPE,
			LISTSEQNO,
			TARGETFLAG,
			EMPNAME,
			EMPSEX,
			EMPBIRTHDAY,
			EMPIDENTIFYTYPE,
			EMPIDENTIFYNUMBER,
			OCCUPATIONCODE,
			OCCUPATIONNAME,
			OCCUPATIONLEVEL,
			ENTRYDATE,
			RESIGNATIONDATE,
			EFFECTIVEDATE,
			ENDDATE,
			CURRENCY,
			MONTHPAY,
			EMPINSURED,
			EMPPREMIUM,
			CHANGEEMPINSURED,
			CHANGEEMPPREMIUM,
			PROVINCECODE,
			CITYCODE,
			COUNTYCODE,
			MONEYLAUNDERINGIND,
			LISTBELONGIND,
			INPUTDATE,
			UPDATESYSDATE
		) values (
			#{id},
			#{policyNo},
			#{endorNo},
			#{subpolicyno},
			#{planid},
			#{itemNo},
			#{itemdetailno},
			#{dynamictargettype},
			#{listseqno},
			#{targetflag},
			#{empname},
			#{empsex},
			#{empbirthday},
			#{empidentifytype},
			#{empidentifynumber},
			#{occupationCode},
			#{occupationname},
			#{occupationlevel},
			#{entrydate},
			#{resignationdate},
			#{effectivedate},
			#{endDate},
			#{currency},
			#{monthPay},
			#{empinsured},
			#{emppremium},
			#{changeempinsured},
			#{changeemppremium},
			#{provincecode},
			#{citycode},
			#{countycode},
			#{moneylaunderingind},
			#{listbelongind},
			#{inputDate},
			#{updatesysdate}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicycopyemployerslist.po.Gupolicycopyemployerslist">
		insert into GUPOLICYCOPYEMPLOYERSLIST
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="endorNo != null" >
				ENDORNO,
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO,
			</if>
			<if test="planid != null" >
				PLANID,
			</if>
			<if test="itemNo != null" >
				ITEMNO,
			</if>
			<if test="itemdetailno != null" >
				ITEMDETAILNO,
			</if>
			<if test="dynamictargettype != null" >
				DYNAMICTARGETTYPE,
			</if>
			<if test="listseqno != null" >
				LISTSEQNO,
			</if>
			<if test="targetflag != null" >
				TARGETFLAG,
			</if>
			<if test="empname != null" >
				EMPNAME,
			</if>
			<if test="empsex != null" >
				EMPSEX,
			</if>
			<if test="empbirthday != null" >
				EMPBIRTHDAY,
			</if>
			<if test="empidentifytype != null" >
				EMPIDENTIFYTYPE,
			</if>
			<if test="empidentifynumber != null" >
				EMPIDENTIFYNUMBER,
			</if>
			<if test="occupationCode != null" >
				OCCUPATIONCODE,
			</if>
			<if test="occupationname != null" >
				OCCUPATIONNAME,
			</if>
			<if test="occupationlevel != null" >
				OCCUPATIONLEVEL,
			</if>
			<if test="entrydate != null" >
				ENTRYDATE,
			</if>
			<if test="resignationdate != null" >
				RESIGNATIONDATE,
			</if>
			<if test="effectivedate != null" >
				EFFECTIVEDATE,
			</if>
			<if test="endDate != null" >
				ENDDATE,
			</if>
			<if test="currency != null" >
				CURRENCY,
			</if>
			<if test="monthPay != null" >
			    MONTHPAY,
			</if>
			<if test="empinsured != null" >
				EMPINSURED,
			</if>
			<if test="emppremium != null" >
				EMPPREMIUM,
			</if>
			<if test="changeempinsured != null" >
				CHANGEEMPINSURED,
			</if>
			<if test="changeemppremium != null" >
				CHANGEEMPPREMIUM,
			</if>
			<if test="provincecode != null" >
				PROVINCECODE,
			</if>
			<if test="citycode != null" >
				CITYCODE,
			</if>
			<if test="countycode != null" >
				COUNTYCODE,
			</if>
			<if test="moneylaunderingind != null" >
				MONEYLAUNDERINGIND,
			</if>
			<if test="listbelongind != null" >
				LISTBELONGIND,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE,
			</if>
			<if test="project != null" >
				PROJECT,
			</if>
			<if test="homeaddress != null" >
				HOMEADDRESS,
			</if>
			<if test="hometel != null" >
				HOMETEL,
			</if>
			<if test="department != null" >
				DEPARTMENT
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="endorNo != null" >
				#{endorNo},
			</if>
			<if test="subpolicyno != null" >
				#{subpolicyno},
			</if>
			<if test="planid != null" >
				#{planid},
			</if>
			<if test="itemNo != null" >
				#{itemNo},
			</if>
			<if test="itemdetailno != null" >
				#{itemdetailno},
			</if>
			<if test="dynamictargettype != null" >
				#{dynamictargettype},
			</if>
			<if test="listseqno != null" >
				#{listseqno},
			</if>
			<if test="targetflag != null" >
				#{targetflag},
			</if>
			<if test="empname != null" >
				#{empname},
			</if>
			<if test="empsex != null" >
				#{empsex},
			</if>
			<if test="empbirthday != null" >
				#{empbirthday},
			</if>
			<if test="empidentifytype != null" >
				#{empidentifytype},
			</if>
			<if test="empidentifynumber != null" >
				#{empidentifynumber},
			</if>
			<if test="occupationCode != null" >
				#{occupationCode},
			</if>
			<if test="occupationname != null" >
				#{occupationname},
			</if>
			<if test="occupationlevel != null" >
				#{occupationlevel},
			</if>
			<if test="entrydate != null" >
				#{entrydate},
			</if>
			<if test="resignationdate != null" >
				#{resignationdate},
			</if>
			<if test="effectivedate != null" >
				#{effectivedate},
			</if>
			<if test="endDate != null" >
				#{endDate},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
		    <if test="monthPay != null" >
				#{monthPay},
			</if>
			<if test="empinsured != null" >
				#{empinsured},
			</if>
			<if test="emppremium != null" >
				#{emppremium},
			</if>
			<if test="changeempinsured != null" >
				#{changeempinsured},
			</if>
			<if test="changeemppremium != null" >
				#{changeemppremium},
			</if>
			<if test="provincecode != null" >
				#{provincecode},
			</if>
			<if test="citycode != null" >
				#{citycode},
			</if>
			<if test="countycode != null" >
				#{countycode},
			</if>
			<if test="moneylaunderingind != null" >
				#{moneylaunderingind},
			</if>
			<if test="listbelongind != null" >
				#{listbelongind},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate},
			</if>
			<if test="project != null" >
					#{project},
			</if>
			<if test="homeaddress != null" >
					#{homeaddress},
			</if>
			<if test="hometel != null" >
					#{hometel},
			</if>
			<if test="department != null" >
					#{department}
				</if>
			</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicycopyemployerslist.po.Gupolicycopyemployerslist">
		update GUPOLICYCOPYEMPLOYERSLIST 
		<set>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="endorNo != null" >
				ENDORNO=#{endorNo},
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO=#{subpolicyno},
			</if>
			<if test="planid != null" >
				PLANID=#{planid},
			</if>
			<if test="itemNo != null" >
				ITEMNO=#{itemNo},
			</if>
			<if test="itemdetailno != null" >
				ITEMDETAILNO=#{itemdetailno},
			</if>
			<if test="dynamictargettype != null" >
				DYNAMICTARGETTYPE=#{dynamictargettype},
			</if>
			<if test="listseqno != null" >
				LISTSEQNO=#{listseqno},
			</if>
			<if test="targetflag != null" >
				TARGETFLAG=#{targetflag},
			</if>
			<if test="empname != null" >
				EMPNAME=#{empname},
			</if>
			<if test="empsex != null" >
				EMPSEX=#{empsex},
			</if>
			<if test="empbirthday != null" >
				EMPBIRTHDAY=#{empbirthday},
			</if>
			<if test="empidentifytype != null" >
				EMPIDENTIFYTYPE=#{empidentifytype},
			</if>
			<if test="empidentifynumber != null" >
				EMPIDENTIFYNUMBER=#{empidentifynumber},
			</if>
			<if test="occupationCode != null" >
				OCCUPATIONCODE=#{occupationCode},
			</if>
			<if test="occupationname != null" >
				OCCUPATIONNAME=#{occupationname},
			</if>
			<if test="occupationlevel != null" >
				OCCUPATIONLEVEL=#{occupationlevel},
			</if>
			<if test="entrydate != null" >
				ENTRYDATE=#{entrydate},
			</if>
			<if test="resignationdate != null" >
				RESIGNATIONDATE=#{resignationdate},
			</if>
			<if test="effectivedate != null" >
				EFFECTIVEDATE=#{effectivedate},
			</if>
			<if test="endDate != null" >
				ENDDATE=#{endDate},
			</if>
			<if test="currency != null" >
				CURRENCY=#{currency},
			</if>
			<if test="monthPay != null" >
				MONTHPAY=#{monthPay},
			</if>
			<if test="empinsured != null" >
				EMPINSURED=#{empinsured},
			</if>
			<if test="emppremium != null" >
				EMPPREMIUM=#{emppremium},
			</if>
			<if test="changeempinsured != null" >
				CHANGEEMPINSURED=#{changeempinsured},
			</if>
			<if test="changeemppremium != null" >
				CHANGEEMPPREMIUM=#{changeemppremium},
			</if>
			<if test="provincecode != null" >
				PROVINCECODE=#{provincecode},
			</if>
			<if test="citycode != null" >
				CITYCODE=#{citycode},
			</if>
			<if test="countycode != null" >
				COUNTYCODE=#{countycode},
			</if>
			<if test="moneylaunderingind != null" >
				MONEYLAUNDERINGIND=#{moneylaunderingind},
			</if>
			<if test="listbelongind != null" >
				LISTBELONGIND=#{listbelongind},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
			<if test="project != null" >
				project=#{project},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicycopyemployerslist.po.Gupolicycopyemployerslist">
		update GUPOLICYCOPYEMPLOYERSLIST set
			POLICYNO=#{policyNo},
			ENDORNO=#{endorNo},
			SUBPOLICYNO=#{subpolicyno},
			PLANID=#{planid},
			ITEMNO=#{itemNo},
			ITEMDETAILNO=#{itemdetailno},
			DYNAMICTARGETTYPE=#{dynamictargettype},
			LISTSEQNO=#{listseqno},
			TARGETFLAG=#{targetflag},
			EMPNAME=#{empname},
			EMPSEX=#{empsex},
			EMPBIRTHDAY=#{empbirthday},
			EMPIDENTIFYTYPE=#{empidentifytype},
			EMPIDENTIFYNUMBER=#{empidentifynumber},
			OCCUPATIONCODE=#{occupationCode},
			OCCUPATIONNAME=#{occupationname},
			OCCUPATIONLEVEL=#{occupationlevel},
			ENTRYDATE=#{entrydate},
			RESIGNATIONDATE=#{resignationdate},
			EFFECTIVEDATE=#{effectivedate},
			ENDDATE=#{endDate},
			CURRENCY=#{currency},
			MONTHPAY=#{monthPay},
			EMPINSURED=#{empinsured},
			EMPPREMIUM=#{emppremium},
			CHANGEEMPINSURED=#{changeempinsured},
			CHANGEEMPPREMIUM=#{changeemppremium},
			PROVINCECODE=#{provincecode},
			CITYCODE=#{citycode},
			COUNTYCODE=#{countycode},
			MONEYLAUNDERINGIND=#{moneylaunderingind},
			LISTBELONGIND=#{listbelongind},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
			project=#{project},
		where ID = #{id}	</update>

	<update id="batchUpdate" parameterType="java.util.List">
		update GUPOLICYCOPYEMPLOYERSLIST
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="POLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ENDORNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.endorNo,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SUBPOLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.subpolicyno,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="PLANID = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.planid,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemNo,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="ITEMDETAILNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemdetailno,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="DYNAMICTARGETTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.dynamictargettype,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="LISTSEQNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.listseqno,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="TARGETFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.targetflag,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="EMPNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.empname,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="EMPSEX = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.empsex,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="EMPBIRTHDAY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.empbirthday,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="EMPIDENTIFYTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.empidentifytype,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="EMPIDENTIFYNUMBER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.empidentifynumber,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="OCCUPATIONCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.occupationCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="OCCUPATIONNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.occupationname,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="OCCUPATIONLEVEL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.occupationlevel,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ENTRYDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.entrydate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="RESIGNATIONDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.resignationdate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="EFFECTIVEDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.effectivedate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="ENDDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.endDate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="CURRENCY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.currency,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="MONTHPAY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.monthPay,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="EMPINSURED = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.empinsured,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="EMPPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.emppremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="CHANGEEMPINSURED = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.changeempinsured,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="CHANGEEMPPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.changeemppremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="PROVINCECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.provincecode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CITYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.citycode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="COUNTYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.countycode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="MONEYLAUNDERINGIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.moneylaunderingind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="LISTBELONGIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.listbelongind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INPUTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="UPDATESYSDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
		</trim>
		where ID in
		<foreach close=")" collection="list" item="item" open="(" separator=", ">
			#{item.id,jdbcType=VARCHAR}
		</foreach>
	</update>
	<update id="batchUpdateSelective" parameterType="java.util.List">
		update GUPOLICYCOPYEMPLOYERSLIST
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="POLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.policyNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ENDORNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.endorNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.endorNo,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SUBPOLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.subpolicyno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.subpolicyno,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="PLANID = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.planid != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.planid,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemNo,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMDETAILNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemdetailno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemdetailno,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="DYNAMICTARGETTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.dynamictargettype != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.dynamictargettype,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="LISTSEQNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.listseqno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.listseqno,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="TARGETFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.targetflag != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.targetflag,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="EMPNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.empname != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.empname,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="EMPSEX = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.empsex != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.empsex,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="EMPBIRTHDAY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.empbirthday != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.empbirthday,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="EMPIDENTIFYTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.empidentifytype != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.empidentifytype,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="EMPIDENTIFYNUMBER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.empidentifynumber != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.empidentifynumber,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="OCCUPATIONCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.occupationCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.occupationCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="OCCUPATIONNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.occupationname != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.occupationname,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="OCCUPATIONLEVEL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.occupationlevel != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.occupationlevel,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ENTRYDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.entrydate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.entrydate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="RESIGNATIONDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.resignationdate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.resignationdate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="EFFECTIVEDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.effectivedate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.effectivedate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="ENDDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.endDate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.endDate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="CURRENCY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.currency != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.currency,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="MONTHPAY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.monthPay != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.monthPay,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="EMPINSURED = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.empinsured != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.empinsured,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="EMPPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.emppremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.emppremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="CHANGEEMPINSURED = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.changeempinsured != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.changeempinsured,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="CHANGEEMPPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.changeemppremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.changeemppremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="PROVINCECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.provincecode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.provincecode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CITYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.citycode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.citycode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="COUNTYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.countycode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.countycode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="MONEYLAUNDERINGIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.moneylaunderingind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.moneylaunderingind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="LISTBELONGIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.listbelongind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.listbelongind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INPUTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.inputDate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="UPDATESYSDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.updatesysdate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
		</trim>
		where ID in
		<foreach close=")" collection="list" item="item" open="(" separator=", ">
			#{item.id,jdbcType=VARCHAR}
		</foreach>
	</update>
	<insert id="batchInsert" parameterType="java.util.List">
		INSERT  ALL
		<foreach collection="list" item="item" separator="">
			INTO GUPOLICYCOPYEMPLOYERSLIST VALUES
			(#{item.id,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.endorNo,jdbcType=VARCHAR},
			#{item.subpolicyno,jdbcType=VARCHAR}, #{item.planid,jdbcType=VARCHAR}, #{item.itemNo,jdbcType=DECIMAL},
			#{item.itemdetailno,jdbcType=VARCHAR}, #{item.dynamictargettype,jdbcType=VARCHAR},
			#{item.listseqno,jdbcType=DECIMAL}, #{item.targetflag,jdbcType=VARCHAR}, #{item.empname,jdbcType=VARCHAR},
			#{item.empsex,jdbcType=VARCHAR}, #{item.empbirthday,jdbcType=VARCHAR}, #{item.empidentifytype,jdbcType=VARCHAR},
			#{item.empidentifynumber,jdbcType=VARCHAR}, #{item.occupationCode,jdbcType=VARCHAR},
			#{item.occupationname,jdbcType=VARCHAR}, #{item.occupationlevel,jdbcType=VARCHAR},
			#{item.entrydate,jdbcType=TIMESTAMP}, #{item.resignationdate,jdbcType=TIMESTAMP},
			#{item.effectivedate,jdbcType=TIMESTAMP}, #{item.endDate,jdbcType=TIMESTAMP}, #{item.currency,jdbcType=VARCHAR},
			#{item.monthPay,jdbcType=DECIMAL}, #{item.empinsured,jdbcType=DECIMAL}, #{item.emppremium,jdbcType=DECIMAL},
			#{item.changeempinsured,jdbcType=DECIMAL}, #{item.changeemppremium,jdbcType=DECIMAL},
			#{item.provincecode,jdbcType=VARCHAR}, #{item.citycode,jdbcType=VARCHAR}, #{item.countycode,jdbcType=VARCHAR},
			#{item.moneylaunderingind,jdbcType=VARCHAR}, #{item.listbelongind,jdbcType=VARCHAR},
			#{item.inputDate,jdbcType=TIMESTAMP}, #{item.updatesysdate,jdbcType=TIMESTAMP}, #{item.project,jdbcType=VARCHAR}
			, #{item.homeaddress,jdbcType=VARCHAR}, #{item.hometel,jdbcType=VARCHAR}, #{item.department,jdbcType=VARCHAR})
		</foreach>
		select 1 from dual
	</insert>
</mapper>
