<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicycopyemployersplan.dao.GupolicycopyemployersplanDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicycopyemployersplan.po.Gupolicycopyemployersplan">
		<id column="ID" property="id" />
		<result column="POLICYNO" property="policyNo" />
		<result column="ENDORNO" property="endorNo" />
		<result column="SUBPOLICYNO" property="subpolicyno" />
		<result column="PLANID" property="planid" />
		<result column="PLANCODE" property="plancode" />
		<result column="RISKCODE" property="riskCode" />
		<result column="ITEMNO" property="itemNo" />
		<result column="ITEMCODE" property="itemCode" />
		<result column="ITEMDETAILNO" property="itemdetailno" />
		<result column="CURRENCY" property="currency" />
		<result column="INDUSTRYTYPE" property="industrytype" />
		<result column="INDUSTRYDETAIL" property="industrydetail" />
		<result column="SCHEMECODE" property="schemecode" />
		<result column="SCHEMENAME" property="schemename" />
		<result column="PERSONNELTYPE" property="personneltype" />
		<result column="DYNAMICTARGETTYPE" property="dynamictargettype" />
		<result column="LISTSEQNO" property="listseqno" />
		<result column="TARGETFLAG" property="targetflag" />
		<result column="TARGETCALCULATE" property="targetcalculate" />
		<result column="EMPLOYEES" property="employees" />
		<result column="EMPLOYEESTHISCHANGE" property="employeesthischange" />
		<result column="LEGALFEESLIMIT" property="legalfeeslimit" />
		<result column="LEGALFEESRATE" property="legalfeesrate" />
		<result column="LEGALSUMPREMIUM" property="legalsumpremium" />
		<result column="CHANGELEGALSUMPREMIUM" property="changelegalsumpremium" />
		<result column="PERSONCASUALTIESLIMIT" property="personcasualtieslimit" />
		<result column="PERSONCASUALTIESRATE" property="personcasualtiesrate" />
		<result column="PERSONMEDICALLIMIT" property="personmedicallimit" />
		<result column="PERSONMEDICALRATE" property="personmedicalrate" />
		<result column="FIELDALIMIT" property="fieldalimit" />
		<result column="FIELDARATE" property="fieldarate" />
		<result column="FIELDBLIMIT" property="fieldblimit" />
		<result column="FIELDBRATE" property="fieldbrate" />
		<result column="FIELDC" property="fieldc" />
		<result column="FIELD" property="field" />
		<result column="PERSONSUMPREMIUM" property="personsumpremium" />
		<result column="CHANGEPERSONSUMPREMIUM" property="changepersonsumpremium" />
		<result column="LISTBELONGIND" property="listbelongind" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		POLICYNO,
		ENDORNO,
		SUBPOLICYNO,
		PLANID,
		PLANCODE,
		RISKCODE,
		ITEMNO,
		ITEMCODE,
		ITEMDETAILNO,
		CURRENCY,
		INDUSTRYTYPE,
		INDUSTRYDETAIL,
		SCHEMECODE,
		SCHEMENAME,
		PERSONNELTYPE,
		DYNAMICTARGETTYPE,
		LISTSEQNO,
		TARGETFLAG,
		TARGETCALCULATE,
		EMPLOYEES,
		EMPLOYEESTHISCHANGE,
		LEGALFEESLIMIT,
		LEGALFEESRATE,
		LEGALSUMPREMIUM,
		CHANGELEGALSUMPREMIUM,
		PERSONCASUALTIESLIMIT,
		PERSONCASUALTIESRATE,
		PERSONMEDICALLIMIT,
		PERSONMEDICALRATE,
		FIELDALIMIT,
		FIELDARATE,
		FIELDBLIMIT,
		FIELDBRATE,
		FIELDC,
		FIELD,
		PERSONSUMPREMIUM,
		CHANGEPERSONSUMPREMIUM,
		LISTBELONGIND,
		INPUTDATE,
		UPDATESYSDATE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="endorNo != null and endorNo != ''" >
			and ENDORNO = #{endorNo}
		</if>
		<if test="subpolicyno != null and subpolicyno != ''" >
			and SUBPOLICYNO = #{subpolicyno}
		</if>
		<if test="planid != null and planid != ''" >
			and PLANID = #{planid}
		</if>
		<if test="plancode != null and plancode != ''" >
			and PLANCODE = #{plancode}
		</if>
		<if test="riskCode != null and riskCode != ''" >
			and RISKCODE = #{riskCode}
		</if>
		<if test="itemNo != null and itemNo != ''" >
			and ITEMNO = #{itemNo}
		</if>
		<if test="itemCode != null and itemCode != ''" >
			and ITEMCODE = #{itemCode}
		</if>
		<if test="itemdetailno != null and itemdetailno != ''" >
			and ITEMDETAILNO = #{itemdetailno}
		</if>
		<if test="currency != null and currency != ''" >
			and CURRENCY = #{currency}
		</if>
		<if test="industrytype != null and industrytype != ''" >
			and INDUSTRYTYPE = #{industrytype}
		</if>
		<if test="industrydetail != null and industrydetail != ''" >
			and INDUSTRYDETAIL = #{industrydetail}
		</if>
		<if test="schemecode != null and schemecode != ''" >
			and SCHEMECODE = #{schemecode}
		</if>
		<if test="schemename != null and schemename != ''" >
			and SCHEMENAME = #{schemename}
		</if>
		<if test="personneltype != null and personneltype != ''" >
			and PERSONNELTYPE = #{personneltype}
		</if>
		<if test="dynamictargettype != null and dynamictargettype != ''" >
			and DYNAMICTARGETTYPE = #{dynamictargettype}
		</if>
		<if test="listseqno != null and listseqno != ''" >
			and LISTSEQNO = #{listseqno}
		</if>
		<if test="targetflag != null and targetflag != ''" >
			and TARGETFLAG = #{targetflag}
		</if>
		<if test="targetcalculate != null and targetcalculate != ''" >
			and TARGETCALCULATE = #{targetcalculate}
		</if>
		<if test="employees != null and employees != ''" >
			and EMPLOYEES = #{employees}
		</if>
		<if test="employeesthischange != null and employeesthischange != ''" >
			and EMPLOYEESTHISCHANGE = #{employeesthischange}
		</if>
		<if test="legalfeeslimit != null and legalfeeslimit != ''" >
			and LEGALFEESLIMIT = #{legalfeeslimit}
		</if>
		<if test="legalfeesrate != null and legalfeesrate != ''" >
			and LEGALFEESRATE = #{legalfeesrate}
		</if>
		<if test="legalsumpremium != null and legalsumpremium != ''" >
			and LEGALSUMPREMIUM = #{legalsumpremium}
		</if>
		<if test="changelegalsumpremium != null and changelegalsumpremium != ''" >
			and CHANGELEGALSUMPREMIUM = #{changelegalsumpremium}
		</if>
		<if test="personcasualtieslimit != null and personcasualtieslimit != ''" >
			and PERSONCASUALTIESLIMIT = #{personcasualtieslimit}
		</if>
		<if test="personcasualtiesrate != null and personcasualtiesrate != ''" >
			and PERSONCASUALTIESRATE = #{personcasualtiesrate}
		</if>
		<if test="personmedicallimit != null and personmedicallimit != ''" >
			and PERSONMEDICALLIMIT = #{personmedicallimit}
		</if>
		<if test="personmedicalrate != null and personmedicalrate != ''" >
			and PERSONMEDICALRATE = #{personmedicalrate}
		</if>
		<if test="fieldalimit != null and fieldalimit != ''" >
			and FIELDALIMIT = #{fieldalimit}
		</if>
		<if test="fieldarate != null and fieldarate != ''" >
			and FIELDARATE = #{fieldarate}
		</if>
		<if test="fieldblimit != null and fieldblimit != ''" >
			and FIELDBLIMIT = #{fieldblimit}
		</if>
		<if test="fieldbrate != null and fieldbrate != ''" >
			and FIELDBRATE = #{fieldbrate}
		</if>
		<if test="fieldc != null and fieldc != ''" >
			and FIELDC = #{fieldc}
		</if>
		<if test="field != null and field != ''" >
			and FIELD = #{field}
		</if>
		<if test="personsumpremium != null and personsumpremium != ''" >
			and PERSONSUMPREMIUM = #{personsumpremium}
		</if>
		<if test="changepersonsumpremium != null and changepersonsumpremium != ''" >
			and CHANGEPERSONSUMPREMIUM = #{changepersonsumpremium}
		</if>
		<if test="listbelongind != null and listbelongind != ''" >
			and LISTBELONGIND = #{listbelongind}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYEMPLOYERSPLAN
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYEMPLOYERSPLAN
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYEMPLOYERSPLAN
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicycopyemployersplan.po.Gupolicycopyemployersplan">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYCOPYEMPLOYERSPLAN
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYCOPYEMPLOYERSPLAN
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicycopyemployersplan.po.Gupolicycopyemployersplan">
		insert into GUPOLICYCOPYEMPLOYERSPLAN (
			ID,
			POLICYNO,
			ENDORNO,
			SUBPOLICYNO,
			PLANID,
			PLANCODE,
			RISKCODE,
			ITEMNO,
			ITEMCODE,
			ITEMDETAILNO,
			CURRENCY,
			INDUSTRYTYPE,
			INDUSTRYDETAIL,
			SCHEMECODE,
			SCHEMENAME,
			PERSONNELTYPE,
			DYNAMICTARGETTYPE,
			LISTSEQNO,
			TARGETFLAG,
			TARGETCALCULATE,
			EMPLOYEES,
			EMPLOYEESTHISCHANGE,
			LEGALFEESLIMIT,
			LEGALFEESRATE,
			LEGALSUMPREMIUM,
			CHANGELEGALSUMPREMIUM,
			PERSONCASUALTIESLIMIT,
			PERSONCASUALTIESRATE,
			PERSONMEDICALLIMIT,
			PERSONMEDICALRATE,
			FIELDALIMIT,
			FIELDARATE,
			FIELDBLIMIT,
			FIELDBRATE,
			FIELDC,
			FIELD,
			PERSONSUMPREMIUM,
			CHANGEPERSONSUMPREMIUM,
			LISTBELONGIND,
			INPUTDATE,
			UPDATESYSDATE
		) values (
			#{id},
			#{policyNo},
			#{endorNo},
			#{subpolicyno},
			#{planid},
			#{plancode},
			#{riskCode},
			#{itemNo},
			#{itemCode},
			#{itemdetailno},
			#{currency},
			#{industrytype},
			#{industrydetail},
			#{schemecode},
			#{schemename},
			#{personneltype},
			#{dynamictargettype},
			#{listseqno},
			#{targetflag},
			#{targetcalculate},
			#{employees},
			#{employeesthischange},
			#{legalfeeslimit},
			#{legalfeesrate},
			#{legalsumpremium},
			#{changelegalsumpremium},
			#{personcasualtieslimit},
			#{personcasualtiesrate},
			#{personmedicallimit},
			#{personmedicalrate},
			#{fieldalimit},
			#{fieldarate},
			#{fieldblimit},
			#{fieldbrate},
			#{fieldc},
			#{field},
			#{personsumpremium},
			#{changepersonsumpremium},
			#{listbelongind},
			#{inputDate},
			#{updatesysdate}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicycopyemployersplan.po.Gupolicycopyemployersplan">
		insert into GUPOLICYCOPYEMPLOYERSPLAN
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="endorNo != null" >
				ENDORNO,
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO,
			</if>
			<if test="planid != null" >
				PLANID,
			</if>
			<if test="plancode != null" >
				PLANCODE,
			</if>
			<if test="riskCode != null" >
				RISKCODE,
			</if>
			<if test="itemNo != null" >
				ITEMNO,
			</if>
			<if test="itemCode != null" >
				ITEMCODE,
			</if>
			<if test="itemdetailno != null" >
				ITEMDETAILNO,
			</if>
			<if test="currency != null" >
				CURRENCY,
			</if>
			<if test="industrytype != null" >
				INDUSTRYTYPE,
			</if>
			<if test="industrydetail != null" >
				INDUSTRYDETAIL,
			</if>
			<if test="schemecode != null" >
				SCHEMECODE,
			</if>
			<if test="schemename != null" >
				SCHEMENAME,
			</if>
			<if test="personneltype != null" >
				PERSONNELTYPE,
			</if>
			<if test="dynamictargettype != null" >
				DYNAMICTARGETTYPE,
			</if>
			<if test="listseqno != null" >
				LISTSEQNO,
			</if>
			<if test="targetflag != null" >
				TARGETFLAG,
			</if>
			<if test="targetcalculate != null" >
				TARGETCALCULATE,
			</if>
			<if test="employees != null" >
				EMPLOYEES,
			</if>
			<if test="employeesthischange != null" >
				EMPLOYEESTHISCHANGE,
			</if>
			<if test="legalfeeslimit != null" >
				LEGALFEESLIMIT,
			</if>
			<if test="legalfeesrate != null" >
				LEGALFEESRATE,
			</if>
			<if test="legalsumpremium != null" >
				LEGALSUMPREMIUM,
			</if>
			<if test="changelegalsumpremium != null" >
				CHANGELEGALSUMPREMIUM,
			</if>
			<if test="personcasualtieslimit != null" >
				PERSONCASUALTIESLIMIT,
			</if>
			<if test="personcasualtiesrate != null" >
				PERSONCASUALTIESRATE,
			</if>
			<if test="personmedicallimit != null" >
				PERSONMEDICALLIMIT,
			</if>
			<if test="personmedicalrate != null" >
				PERSONMEDICALRATE,
			</if>
			<if test="fieldalimit != null" >
				FIELDALIMIT,
			</if>
			<if test="fieldarate != null" >
				FIELDARATE,
			</if>
			<if test="fieldblimit != null" >
				FIELDBLIMIT,
			</if>
			<if test="fieldbrate != null" >
				FIELDBRATE,
			</if>
			<if test="fieldc != null" >
				FIELDC,
			</if>
			<if test="field != null" >
				FIELD,
			</if>
			<if test="personsumpremium != null" >
				PERSONSUMPREMIUM,
			</if>
			<if test="changepersonsumpremium != null" >
				CHANGEPERSONSUMPREMIUM,
			</if>
			<if test="listbelongind != null" >
				LISTBELONGIND,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="endorNo != null" >
				#{endorNo},
			</if>
			<if test="subpolicyno != null" >
				#{subpolicyno},
			</if>
			<if test="planid != null" >
				#{planid},
			</if>
			<if test="plancode != null" >
				#{plancode},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="itemNo != null" >
				#{itemNo},
			</if>
			<if test="itemCode != null" >
				#{itemCode},
			</if>
			<if test="itemdetailno != null" >
				#{itemdetailno},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
			<if test="industrytype != null" >
				#{industrytype},
			</if>
			<if test="industrydetail != null" >
				#{industrydetail},
			</if>
			<if test="schemecode != null" >
				#{schemecode},
			</if>
			<if test="schemename != null" >
				#{schemename},
			</if>
			<if test="personneltype != null" >
				#{personneltype},
			</if>
			<if test="dynamictargettype != null" >
				#{dynamictargettype},
			</if>
			<if test="listseqno != null" >
				#{listseqno},
			</if>
			<if test="targetflag != null" >
				#{targetflag},
			</if>
			<if test="targetcalculate != null" >
				#{targetcalculate},
			</if>
			<if test="employees != null" >
				#{employees},
			</if>
			<if test="employeesthischange != null" >
				#{employeesthischange},
			</if>
			<if test="legalfeeslimit != null" >
				#{legalfeeslimit},
			</if>
			<if test="legalfeesrate != null" >
				#{legalfeesrate},
			</if>
			<if test="legalsumpremium != null" >
				#{legalsumpremium},
			</if>
			<if test="changelegalsumpremium != null" >
				#{changelegalsumpremium},
			</if>
			<if test="personcasualtieslimit != null" >
				#{personcasualtieslimit},
			</if>
			<if test="personcasualtiesrate != null" >
				#{personcasualtiesrate},
			</if>
			<if test="personmedicallimit != null" >
				#{personmedicallimit},
			</if>
			<if test="personmedicalrate != null" >
				#{personmedicalrate},
			</if>
			<if test="fieldalimit != null" >
				#{fieldalimit},
			</if>
			<if test="fieldarate != null" >
				#{fieldarate},
			</if>
			<if test="fieldblimit != null" >
				#{fieldblimit},
			</if>
			<if test="fieldbrate != null" >
				#{fieldbrate},
			</if>
			<if test="fieldc != null" >
				#{fieldc},
			</if>
			<if test="field != null" >
				#{field},
			</if>
			<if test="personsumpremium != null" >
				#{personsumpremium},
			</if>
			<if test="changepersonsumpremium != null" >
				#{changepersonsumpremium},
			</if>
			<if test="listbelongind != null" >
				#{listbelongind},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate}
			</if>
		</trim>
	</insert>

    <!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicycopyemployersplan.po.Gupolicycopyemployersplan">
		update GUPOLICYCOPYEMPLOYERSPLAN 
		<set>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="endorNo != null" >
				ENDORNO=#{endorNo},
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO=#{subpolicyno},
			</if>
			<if test="planid != null" >
				PLANID=#{planid},
			</if>
			<if test="plancode != null" >
				PLANCODE=#{plancode},
			</if>
			<if test="riskCode != null" >
				RISKCODE=#{riskCode},
			</if>
			<if test="itemNo != null" >
				ITEMNO=#{itemNo},
			</if>
			<if test="itemCode != null" >
				ITEMCODE=#{itemCode},
			</if>
			<if test="itemdetailno != null" >
				ITEMDETAILNO=#{itemdetailno},
			</if>
			<if test="currency != null" >
				CURRENCY=#{currency},
			</if>
			<if test="industrytype != null" >
				INDUSTRYTYPE=#{industrytype},
			</if>
			<if test="industrydetail != null" >
				INDUSTRYDETAIL=#{industrydetail},
			</if>
			<if test="schemecode != null" >
				SCHEMECODE=#{schemecode},
			</if>
			<if test="schemename != null" >
				SCHEMENAME=#{schemename},
			</if>
			<if test="personneltype != null" >
				PERSONNELTYPE=#{personneltype},
			</if>
			<if test="dynamictargettype != null" >
				DYNAMICTARGETTYPE=#{dynamictargettype},
			</if>
			<if test="listseqno != null" >
				LISTSEQNO=#{listseqno},
			</if>
			<if test="targetflag != null" >
				TARGETFLAG=#{targetflag},
			</if>
			<if test="targetcalculate != null" >
				TARGETCALCULATE=#{targetcalculate},
			</if>
			<if test="employees != null" >
				EMPLOYEES=#{employees},
			</if>
			<if test="employeesthischange != null" >
				EMPLOYEESTHISCHANGE=#{employeesthischange},
			</if>
			<if test="legalfeeslimit != null" >
				LEGALFEESLIMIT=#{legalfeeslimit},
			</if>
			<if test="legalfeesrate != null" >
				LEGALFEESRATE=#{legalfeesrate},
			</if>
			<if test="legalsumpremium != null" >
				LEGALSUMPREMIUM=#{legalsumpremium},
			</if>
			<if test="changelegalsumpremium != null" >
				CHANGELEGALSUMPREMIUM=#{changelegalsumpremium},
			</if>
			<if test="personcasualtieslimit != null" >
				PERSONCASUALTIESLIMIT=#{personcasualtieslimit},
			</if>
			<if test="personcasualtiesrate != null" >
				PERSONCASUALTIESRATE=#{personcasualtiesrate},
			</if>
			<if test="personmedicallimit != null" >
				PERSONMEDICALLIMIT=#{personmedicallimit},
			</if>
			<if test="personmedicalrate != null" >
				PERSONMEDICALRATE=#{personmedicalrate},
			</if>
			<if test="fieldalimit != null" >
				FIELDALIMIT=#{fieldalimit},
			</if>
			<if test="fieldarate != null" >
				FIELDARATE=#{fieldarate},
			</if>
			<if test="fieldblimit != null" >
				FIELDBLIMIT=#{fieldblimit},
			</if>
			<if test="fieldbrate != null" >
				FIELDBRATE=#{fieldbrate},
			</if>
			<if test="fieldc != null" >
				FIELDC=#{fieldc},
			</if>
			<if test="field != null" >
				FIELD=#{field},
			</if>
			<if test="personsumpremium != null" >
				PERSONSUMPREMIUM=#{personsumpremium},
			</if>
			<if test="changepersonsumpremium != null" >
				CHANGEPERSONSUMPREMIUM=#{changepersonsumpremium},
			</if>
			<if test="listbelongind != null" >
				LISTBELONGIND=#{listbelongind},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicycopyemployersplan.po.Gupolicycopyemployersplan">
		update GUPOLICYCOPYEMPLOYERSPLAN set
			POLICYNO=#{policyNo},
			ENDORNO=#{endorNo},
			SUBPOLICYNO=#{subpolicyno},
			PLANID=#{planid},
			PLANCODE=#{plancode},
			RISKCODE=#{riskCode},
			ITEMNO=#{itemNo},
			ITEMCODE=#{itemCode},
			ITEMDETAILNO=#{itemdetailno},
			CURRENCY=#{currency},
			INDUSTRYTYPE=#{industrytype},
			INDUSTRYDETAIL=#{industrydetail},
			SCHEMECODE=#{schemecode},
			SCHEMENAME=#{schemename},
			PERSONNELTYPE=#{personneltype},
			DYNAMICTARGETTYPE=#{dynamictargettype},
			LISTSEQNO=#{listseqno},
			TARGETFLAG=#{targetflag},
			TARGETCALCULATE=#{targetcalculate},
			EMPLOYEES=#{employees},
			EMPLOYEESTHISCHANGE=#{employeesthischange},
			LEGALFEESLIMIT=#{legalfeeslimit},
			LEGALFEESRATE=#{legalfeesrate},
			LEGALSUMPREMIUM=#{legalsumpremium},
			CHANGELEGALSUMPREMIUM=#{changelegalsumpremium},
			PERSONCASUALTIESLIMIT=#{personcasualtieslimit},
			PERSONCASUALTIESRATE=#{personcasualtiesrate},
			PERSONMEDICALLIMIT=#{personmedicallimit},
			PERSONMEDICALRATE=#{personmedicalrate},
			FIELDALIMIT=#{fieldalimit},
			FIELDARATE=#{fieldarate},
			FIELDBLIMIT=#{fieldblimit},
			FIELDBRATE=#{fieldbrate},
			FIELDC=#{fieldc},
			FIELD=#{field},
			PERSONSUMPREMIUM=#{personsumpremium},
			CHANGEPERSONSUMPREMIUM=#{changepersonsumpremium},
			LISTBELONGIND=#{listbelongind},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
		where ID = #{id}	</update>

	<insert id="batchInsert">
		INSERT ALL
		<foreach collection="list" item="item" separator="">
			INTO GUPOLICYCOPYEMPLOYERSPLAN VALUES
			(#{item.id}, #{item.policyNo}, #{item.endorNo}, #{item.subpolicyno}, #{item.planid}, #{item.plancode},
			#{item.riskCode}, #{item.itemNo}, #{item.itemCode}, #{item.itemdetailno}, #{item.currency}, #{item.industrytype},
			#{item.industrydetail}, #{item.schemecode}, #{item.schemename}, #{item.personneltype}, #{item.dynamictargettype},
			#{item.listseqno}, #{item.targetflag}, #{item.targetcalculate}, #{item.employees}, #{item.employeesthischange},
			#{item.legalfeeslimit}, #{item.legalfeesrate}, #{item.legalsumpremium}, #{item.changelegalsumpremium},
			#{item.personcasualtieslimit}, #{item.personcasualtiesrate}, #{item.personmedicallimit}, #{item.personmedicalrate},
			#{item.fieldalimit}, #{item.fieldarate}, #{item.fieldblimit}, #{item.fieldbrate}, #{item.fieldc},
			#{item.field}, #{item.personsumpremium}, #{item.changepersonsumpremium}, #{item.listbelongind}, #{item.inputDate},
			#{item.updatesysdate})
		</foreach>
		select 1 from dual
	</insert>
</mapper>
