<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicycopyholder.dao.GupolicycopyholderDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicycopyholder.po.Gupolicycopyholder">
		<id column="ID" property="id" />
		<result column="POLICYNO" property="policyNo" />
		<result column="ENDORNO" property="endorNo" />
		<result column="PRODUCTCODE" property="productcode" />
		<result column="INSURANCECOMPANYCODE" property="insurancecompanycode" />
		<result column="INSUREDTYPE" property="insuredType" />
		<result column="VIPIND" property="vipind" />
		<result column="INSUREDCODE" property="insuredCode" />
		<result column="INSUREDNAME" property="insuredName" />
		<result column="INSUREDADDRESS" property="insuredAddress" />
		<result column="IDENTIFYTYPE" property="identifyType" />
		<result column="IDENTIFYNUMBER" property="identifyNumber" />
		<result column="SEX" property="sex" />
		<result column="BIRTHDATE" property="birthDate" />
		<result column="STARLEVEL" property="starlevel" />
		<result column="SOCIALSECURITYNO" property="socialsecurityno" />
		<result column="ITEMPROVINCECODE" property="itemprovincecode" />
		<result column="ITEMPROVINCECNAME" property="itemprovincecname" />
		<result column="ITEMCITYCODE" property="itemcitycode" />
		<result column="ITEMCITYCNAME" property="itemcitycname" />
		<result column="COUNTYCODE" property="countycode" />
		<result column="COUNTYCNAME" property="countycname" />
		<result column="POSTCODE" property="postCode" />
		<result column="MOBILEPHONE" property="mobilephone" />
		<result column="OFFICEPHONE" property="officephone" />
		<result column="HOMEPHONE" property="homePhone" />
		<result column="BANKNAME" property="bankName" />
		<result column="BANKNUMBER" property="banknumber" />
		<result column="OCCUPATIONCODE" property="occupationCode" />
		<result column="COMPANYNATURE" property="companynature" />
		<result column="NUMBEROFUNITS" property="numberofunits" />
		<result column="EMAIL" property="email" />
		<result column="INDUSTRYMAINCODE" property="industrymaincode" />
		<result column="INDUSTRYKINDCODE" property="industrykindcode" />
		<result column="CONTACTNAME" property="contactname" />
		<result column="CONTACTSEX" property="contactsex" />
		<result column="CONTACTPOSITION" property="contactposition" />
		<result column="CONTACTDEPARTMENT" property="contactdepartment" />
		<result column="CONTACTOFFICENUMBER" property="contactofficenumber" />
		<result column="CONTACTMOBILE" property="contactmobile" />
		<result column="CONTACTPHONE" property="contactphone" />
		<result column="REMARK" property="remark" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		POLICYNO,
		ENDORNO,
		PRODUCTCODE,
		INSURANCECOMPANYCODE,
		INSUREDTYPE,
		VIPIND,
		INSUREDCODE,
		INSUREDNAME,
		INSUREDADDRESS,
		IDENTIFYTYPE,
		IDENTIFYNUMBER,
		SEX,
		BIRTHDATE,
		STARLEVEL,
		SOCIALSECURITYNO,
		ITEMPROVINCECODE,
		ITEMPROVINCECNAME,
		ITEMCITYCODE,
		ITEMCITYCNAME,
		COUNTYCODE,
		COUNTYCNAME,
		POSTCODE,
		MOBILEPHONE,
		OFFICEPHONE,
		HOMEPHONE,
		BANKNAME,
		BANKNUMBER,
		OCCUPATIONCODE,
		COMPANYNATURE,
		NUMBEROFUNITS,
		EMAIL,
		INDUSTRYMAINCODE,
		INDUSTRYKINDCODE,
		CONTACTNAME,
		CONTACTSEX,
		CONTACTPOSITION,
		CONTACTDEPARTMENT,
		CONTACTOFFICENUMBER,
		CONTACTMOBILE,
		CONTACTPHONE,
		REMARK,
		INPUTDATE,
		UPDATESYSDATE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="endorNo != null and endorNo != ''" >
			and ENDORNO = #{endorNo}
		</if>
		<if test="productcode != null and productcode != ''" >
			and PRODUCTCODE = #{productcode}
		</if>
		<if test="insurancecompanycode != null and insurancecompanycode != ''" >
			and INSURANCECOMPANYCODE = #{insurancecompanycode}
		</if>
		<if test="insuredType != null and insuredType != ''" >
			and INSUREDTYPE = #{insuredType}
		</if>
		<if test="vipind != null and vipind != ''" >
			and VIPIND = #{vipind}
		</if>
		<if test="insuredCode != null and insuredCode != ''" >
			and INSUREDCODE = #{insuredCode}
		</if>
		<if test="insuredName != null and insuredName != ''" >
			and INSUREDNAME = #{insuredName}
		</if>
		<if test="insuredAddress != null and insuredAddress != ''" >
			and INSUREDADDRESS = #{insuredAddress}
		</if>
		<if test="identifyType != null and identifyType != ''" >
			and IDENTIFYTYPE = #{identifyType}
		</if>
		<if test="identifyNumber != null and identifyNumber != ''" >
			and IDENTIFYNUMBER = #{identifyNumber}
		</if>
		<if test="sex != null and sex != ''" >
			and SEX = #{sex}
		</if>
		<if test="birthDate != null and birthDate != ''" >
			and BIRTHDATE = #{birthDate}
		</if>
		<if test="starlevel != null and starlevel != ''" >
			and STARLEVEL = #{starlevel}
		</if>
		<if test="socialsecurityno != null and socialsecurityno != ''" >
			and SOCIALSECURITYNO = #{socialsecurityno}
		</if>
		<if test="itemprovincecode != null and itemprovincecode != ''" >
			and ITEMPROVINCECODE = #{itemprovincecode}
		</if>
		<if test="itemprovincecname != null and itemprovincecname != ''" >
			and ITEMPROVINCECNAME = #{itemprovincecname}
		</if>
		<if test="itemcitycode != null and itemcitycode != ''" >
			and ITEMCITYCODE = #{itemcitycode}
		</if>
		<if test="itemcitycname != null and itemcitycname != ''" >
			and ITEMCITYCNAME = #{itemcitycname}
		</if>
		<if test="countycode != null and countycode != ''" >
			and COUNTYCODE = #{countycode}
		</if>
		<if test="countycname != null and countycname != ''" >
			and COUNTYCNAME = #{countycname}
		</if>
		<if test="postCode != null and postCode != ''" >
			and POSTCODE = #{postCode}
		</if>
		<if test="mobilephone != null and mobilephone != ''" >
			and MOBILEPHONE = #{mobilephone}
		</if>
		<if test="officephone != null and officephone != ''" >
			and OFFICEPHONE = #{officephone}
		</if>
		<if test="homePhone != null and homePhone != ''" >
			and HOMEPHONE = #{homePhone}
		</if>
		<if test="bankName != null and bankName != ''" >
			and BANKNAME = #{bankName}
		</if>
		<if test="banknumber != null and banknumber != ''" >
			and BANKNUMBER = #{banknumber}
		</if>
		<if test="occupationCode != null and occupationCode != ''" >
			and OCCUPATIONCODE = #{occupationCode}
		</if>
		<if test="companynature != null and companynature != ''" >
			and COMPANYNATURE = #{companynature}
		</if>
		<if test="numberofunits != null and numberofunits != ''" >
			and NUMBEROFUNITS = #{numberofunits}
		</if>
		<if test="email != null and email != ''" >
			and EMAIL = #{email}
		</if>
		<if test="industrymaincode != null and industrymaincode != ''" >
			and INDUSTRYMAINCODE = #{industrymaincode}
		</if>
		<if test="industrykindcode != null and industrykindcode != ''" >
			and INDUSTRYKINDCODE = #{industrykindcode}
		</if>
		<if test="contactname != null and contactname != ''" >
			and CONTACTNAME = #{contactname}
		</if>
		<if test="contactsex != null and contactsex != ''" >
			and CONTACTSEX = #{contactsex}
		</if>
		<if test="contactposition != null and contactposition != ''" >
			and CONTACTPOSITION = #{contactposition}
		</if>
		<if test="contactdepartment != null and contactdepartment != ''" >
			and CONTACTDEPARTMENT = #{contactdepartment}
		</if>
		<if test="contactofficenumber != null and contactofficenumber != ''" >
			and CONTACTOFFICENUMBER = #{contactofficenumber}
		</if>
		<if test="contactmobile != null and contactmobile != ''" >
			and CONTACTMOBILE = #{contactmobile}
		</if>
		<if test="contactphone != null and contactphone != ''" >
			and CONTACTPHONE = #{contactphone}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYHOLDER
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYHOLDER
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYHOLDER
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicycopyholder.po.Gupolicycopyholder">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYCOPYHOLDER
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYCOPYHOLDER
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicycopyholder.po.Gupolicycopyholder">
		insert into GUPOLICYCOPYHOLDER (
			ID,
			POLICYNO,
			ENDORNO,
			PRODUCTCODE,
			INSURANCECOMPANYCODE,
			INSUREDTYPE,
			VIPIND,
			INSUREDCODE,
			INSUREDNAME,
			INSUREDADDRESS,
			IDENTIFYTYPE,
			IDENTIFYNUMBER,
			SEX,
			BIRTHDATE,
			STARLEVEL,
			SOCIALSECURITYNO,
			ITEMPROVINCECODE,
			ITEMPROVINCECNAME,
			ITEMCITYCODE,
			ITEMCITYCNAME,
			COUNTYCODE,
			COUNTYCNAME,
			POSTCODE,
			MOBILEPHONE,
			OFFICEPHONE,
			HOMEPHONE,
			BANKNAME,
			BANKNUMBER,
			OCCUPATIONCODE,
			COMPANYNATURE,
			NUMBEROFUNITS,
			EMAIL,
			INDUSTRYMAINCODE,
			INDUSTRYKINDCODE,
			CONTACTNAME,
			CONTACTSEX,
			CONTACTPOSITION,
			CONTACTDEPARTMENT,
			CONTACTOFFICENUMBER,
			CONTACTMOBILE,
			CONTACTPHONE,
			REMARK,
			INPUTDATE,
			UPDATESYSDATE
		) values (
			#{id},
			#{policyNo},
			#{endorNo},
			#{productcode},
			#{insurancecompanycode},
			#{insuredType},
			#{vipind},
			#{insuredCode},
			#{insuredName},
			#{insuredAddress},
			#{identifyType},
			#{identifyNumber},
			#{sex},
			#{birthDate},
			#{starlevel},
			#{socialsecurityno},
			#{itemprovincecode},
			#{itemprovincecname},
			#{itemcitycode},
			#{itemcitycname},
			#{countycode},
			#{countycname},
			#{postCode},
			#{mobilephone},
			#{officephone},
			#{homePhone},
			#{bankName},
			#{banknumber},
			#{occupationCode},
			#{companynature},
			#{numberofunits},
			#{email},
			#{industrymaincode},
			#{industrykindcode},
			#{contactname},
			#{contactsex},
			#{contactposition},
			#{contactdepartment},
			#{contactofficenumber},
			#{contactmobile},
			#{contactphone},
			#{remark},
			#{inputDate},
			#{updatesysdate}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicycopyholder.po.Gupolicycopyholder">
		insert into GUPOLICYCOPYHOLDER
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="endorNo != null" >
				ENDORNO,
			</if>
			<if test="productcode != null" >
				PRODUCTCODE,
			</if>
			<if test="insurancecompanycode != null" >
				INSURANCECOMPANYCODE,
			</if>
			<if test="insuredType != null" >
				INSUREDTYPE,
			</if>
			<if test="vipind != null" >
				VIPIND,
			</if>
			<if test="insuredCode != null" >
				INSUREDCODE,
			</if>
			<if test="insuredName != null" >
				INSUREDNAME,
			</if>
			<if test="insuredAddress != null" >
				INSUREDADDRESS,
			</if>
			<if test="identifyType != null" >
				IDENTIFYTYPE,
			</if>
			<if test="identifyNumber != null" >
				IDENTIFYNUMBER,
			</if>
			<if test="sex != null" >
				SEX,
			</if>
			<if test="birthDate != null" >
				BIRTHDATE,
			</if>
			<if test="starlevel != null" >
				STARLEVEL,
			</if>
			<if test="socialsecurityno != null" >
				SOCIALSECURITYNO,
			</if>
			<if test="itemprovincecode != null" >
				ITEMPROVINCECODE,
			</if>
			<if test="itemprovincecname != null" >
				ITEMPROVINCECNAME,
			</if>
			<if test="itemcitycode != null" >
				ITEMCITYCODE,
			</if>
			<if test="itemcitycname != null" >
				ITEMCITYCNAME,
			</if>
			<if test="countycode != null" >
				COUNTYCODE,
			</if>
			<if test="countycname != null" >
				COUNTYCNAME,
			</if>
			<if test="postCode != null" >
				POSTCODE,
			</if>
			<if test="mobilephone != null" >
				MOBILEPHONE,
			</if>
			<if test="officephone != null" >
				OFFICEPHONE,
			</if>
			<if test="homePhone != null" >
				HOMEPHONE,
			</if>
			<if test="bankName != null" >
				BANKNAME,
			</if>
			<if test="banknumber != null" >
				BANKNUMBER,
			</if>
			<if test="occupationCode != null" >
				OCCUPATIONCODE,
			</if>
			<if test="companynature != null" >
				COMPANYNATURE,
			</if>
			<if test="numberofunits != null" >
				NUMBEROFUNITS,
			</if>
			<if test="email != null" >
				EMAIL,
			</if>
			<if test="industrymaincode != null" >
				INDUSTRYMAINCODE,
			</if>
			<if test="industrykindcode != null" >
				INDUSTRYKINDCODE,
			</if>
			<if test="contactname != null" >
				CONTACTNAME,
			</if>
			<if test="contactsex != null" >
				CONTACTSEX,
			</if>
			<if test="contactposition != null" >
				CONTACTPOSITION,
			</if>
			<if test="contactdepartment != null" >
				CONTACTDEPARTMENT,
			</if>
			<if test="contactofficenumber != null" >
				CONTACTOFFICENUMBER,
			</if>
			<if test="contactmobile != null" >
				CONTACTMOBILE,
			</if>
			<if test="contactphone != null" >
				CONTACTPHONE,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="endorNo != null" >
				#{endorNo},
			</if>
			<if test="productcode != null" >
				#{productcode},
			</if>
			<if test="insurancecompanycode != null" >
				#{insurancecompanycode},
			</if>
			<if test="insuredType != null" >
				#{insuredType},
			</if>
			<if test="vipind != null" >
				#{vipind},
			</if>
			<if test="insuredCode != null" >
				#{insuredCode},
			</if>
			<if test="insuredName != null" >
				#{insuredName},
			</if>
			<if test="insuredAddress != null" >
				#{insuredAddress},
			</if>
			<if test="identifyType != null" >
				#{identifyType},
			</if>
			<if test="identifyNumber != null" >
				#{identifyNumber},
			</if>
			<if test="sex != null" >
				#{sex},
			</if>
			<if test="birthDate != null" >
				#{birthDate},
			</if>
			<if test="starlevel != null" >
				#{starlevel},
			</if>
			<if test="socialsecurityno != null" >
				#{socialsecurityno},
			</if>
			<if test="itemprovincecode != null" >
				#{itemprovincecode},
			</if>
			<if test="itemprovincecname != null" >
				#{itemprovincecname},
			</if>
			<if test="itemcitycode != null" >
				#{itemcitycode},
			</if>
			<if test="itemcitycname != null" >
				#{itemcitycname},
			</if>
			<if test="countycode != null" >
				#{countycode},
			</if>
			<if test="countycname != null" >
				#{countycname},
			</if>
			<if test="postCode != null" >
				#{postCode},
			</if>
			<if test="mobilephone != null" >
				#{mobilephone},
			</if>
			<if test="officephone != null" >
				#{officephone},
			</if>
			<if test="homePhone != null" >
				#{homePhone},
			</if>
			<if test="bankName != null" >
				#{bankName},
			</if>
			<if test="banknumber != null" >
				#{banknumber},
			</if>
			<if test="occupationCode != null" >
				#{occupationCode},
			</if>
			<if test="companynature != null" >
				#{companynature},
			</if>
			<if test="numberofunits != null" >
				#{numberofunits},
			</if>
			<if test="email != null" >
				#{email},
			</if>
			<if test="industrymaincode != null" >
				#{industrymaincode},
			</if>
			<if test="industrykindcode != null" >
				#{industrykindcode},
			</if>
			<if test="contactname != null" >
				#{contactname},
			</if>
			<if test="contactsex != null" >
				#{contactsex},
			</if>
			<if test="contactposition != null" >
				#{contactposition},
			</if>
			<if test="contactdepartment != null" >
				#{contactdepartment},
			</if>
			<if test="contactofficenumber != null" >
				#{contactofficenumber},
			</if>
			<if test="contactmobile != null" >
				#{contactmobile},
			</if>
			<if test="contactphone != null" >
				#{contactphone},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate}
			</if>
		</trim>
	</insert>

    <!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicycopyholder.po.Gupolicycopyholder">
		update GUPOLICYCOPYHOLDER 
		<set>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="endorNo != null" >
				ENDORNO=#{endorNo},
			</if>
			<if test="productcode != null" >
				PRODUCTCODE=#{productcode},
			</if>
			<if test="insurancecompanycode != null" >
				INSURANCECOMPANYCODE=#{insurancecompanycode},
			</if>
			<if test="insuredType != null" >
				INSUREDTYPE=#{insuredType},
			</if>
			<if test="vipind != null" >
				VIPIND=#{vipind},
			</if>
			<if test="insuredCode != null" >
				INSUREDCODE=#{insuredCode},
			</if>
			<if test="insuredName != null" >
				INSUREDNAME=#{insuredName},
			</if>
			<if test="insuredAddress != null" >
				INSUREDADDRESS=#{insuredAddress},
			</if>
			<if test="identifyType != null" >
				IDENTIFYTYPE=#{identifyType},
			</if>
			<if test="identifyNumber != null" >
				IDENTIFYNUMBER=#{identifyNumber},
			</if>
			<if test="sex != null" >
				SEX=#{sex},
			</if>
			<if test="birthDate != null" >
				BIRTHDATE=#{birthDate},
			</if>
			<if test="starlevel != null" >
				STARLEVEL=#{starlevel},
			</if>
			<if test="socialsecurityno != null" >
				SOCIALSECURITYNO=#{socialsecurityno},
			</if>
			<if test="itemprovincecode != null" >
				ITEMPROVINCECODE=#{itemprovincecode},
			</if>
			<if test="itemprovincecname != null" >
				ITEMPROVINCECNAME=#{itemprovincecname},
			</if>
			<if test="itemcitycode != null" >
				ITEMCITYCODE=#{itemcitycode},
			</if>
			<if test="itemcitycname != null" >
				ITEMCITYCNAME=#{itemcitycname},
			</if>
			<if test="countycode != null" >
				COUNTYCODE=#{countycode},
			</if>
			<if test="countycname != null" >
				COUNTYCNAME=#{countycname},
			</if>
			<if test="postCode != null" >
				POSTCODE=#{postCode},
			</if>
			<if test="mobilephone != null" >
				MOBILEPHONE=#{mobilephone},
			</if>
			<if test="officephone != null" >
				OFFICEPHONE=#{officephone},
			</if>
			<if test="homePhone != null" >
				HOMEPHONE=#{homePhone},
			</if>
			<if test="bankName != null" >
				BANKNAME=#{bankName},
			</if>
			<if test="banknumber != null" >
				BANKNUMBER=#{banknumber},
			</if>
			<if test="occupationCode != null" >
				OCCUPATIONCODE=#{occupationCode},
			</if>
			<if test="companynature != null" >
				COMPANYNATURE=#{companynature},
			</if>
			<if test="numberofunits != null" >
				NUMBEROFUNITS=#{numberofunits},
			</if>
			<if test="email != null" >
				EMAIL=#{email},
			</if>
			<if test="industrymaincode != null" >
				INDUSTRYMAINCODE=#{industrymaincode},
			</if>
			<if test="industrykindcode != null" >
				INDUSTRYKINDCODE=#{industrykindcode},
			</if>
			<if test="contactname != null" >
				CONTACTNAME=#{contactname},
			</if>
			<if test="contactsex != null" >
				CONTACTSEX=#{contactsex},
			</if>
			<if test="contactposition != null" >
				CONTACTPOSITION=#{contactposition},
			</if>
			<if test="contactdepartment != null" >
				CONTACTDEPARTMENT=#{contactdepartment},
			</if>
			<if test="contactofficenumber != null" >
				CONTACTOFFICENUMBER=#{contactofficenumber},
			</if>
			<if test="contactmobile != null" >
				CONTACTMOBILE=#{contactmobile},
			</if>
			<if test="contactphone != null" >
				CONTACTPHONE=#{contactphone},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicycopyholder.po.Gupolicycopyholder">
		update GUPOLICYCOPYHOLDER set
			POLICYNO=#{policyNo},
			ENDORNO=#{endorNo},
			PRODUCTCODE=#{productcode},
			INSURANCECOMPANYCODE=#{insurancecompanycode},
			INSUREDTYPE=#{insuredType},
			VIPIND=#{vipind},
			INSUREDCODE=#{insuredCode},
			INSUREDNAME=#{insuredName},
			INSUREDADDRESS=#{insuredAddress},
			IDENTIFYTYPE=#{identifyType},
			IDENTIFYNUMBER=#{identifyNumber},
			SEX=#{sex},
			BIRTHDATE=#{birthDate},
			STARLEVEL=#{starlevel},
			SOCIALSECURITYNO=#{socialsecurityno},
			ITEMPROVINCECODE=#{itemprovincecode},
			ITEMPROVINCECNAME=#{itemprovincecname},
			ITEMCITYCODE=#{itemcitycode},
			ITEMCITYCNAME=#{itemcitycname},
			COUNTYCODE=#{countycode},
			COUNTYCNAME=#{countycname},
			POSTCODE=#{postCode},
			MOBILEPHONE=#{mobilephone},
			OFFICEPHONE=#{officephone},
			HOMEPHONE=#{homePhone},
			BANKNAME=#{bankName},
			BANKNUMBER=#{banknumber},
			OCCUPATIONCODE=#{occupationCode},
			COMPANYNATURE=#{companynature},
			NUMBEROFUNITS=#{numberofunits},
			EMAIL=#{email},
			INDUSTRYMAINCODE=#{industrymaincode},
			INDUSTRYKINDCODE=#{industrykindcode},
			CONTACTNAME=#{contactname},
			CONTACTSEX=#{contactsex},
			CONTACTPOSITION=#{contactposition},
			CONTACTDEPARTMENT=#{contactdepartment},
			CONTACTOFFICENUMBER=#{contactofficenumber},
			CONTACTMOBILE=#{contactmobile},
			CONTACTPHONE=#{contactphone},
			REMARK=#{remark},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
		where ID = #{id}	</update>

	<insert id="batchInsert">
		INSERT ALL
		<foreach collection="list" item="item" separator="">
			INTO GUPOLICYCOPYHOLDER VALUES
			(#{item.id}, #{item.policyNo}, #{item.endorNo}, #{item.productcode}, #{item.insurancecompanycode},
			#{item.insuredType}, #{item.vipind}, #{item.insuredCode}, #{item.insuredName}, #{item.insuredAddress},
			#{item.identifyType}, #{item.identifyNumber}, #{item.sex}, #{item.birthDate}, #{item.starlevel},
			#{item.socialsecurityno}, #{item.itemprovincecode}, #{item.itemprovincecname}, #{item.itemcitycode},
			#{item.itemcitycname}, #{item.countycode}, #{item.countycname}, #{item.postCode}, #{item.mobilephone},
			#{item.officephone}, #{item.homePhone}, #{item.bankName}, #{item.banknumber}, #{item.occupationCode},
			#{item.companynature}, #{item.numberofunits}, #{item.email}, #{item.industrymaincode}, #{item.industrykindcode},
			#{item.contactname}, #{item.contactsex}, #{item.contactposition}, #{item.contactdepartment},
			#{item.contactofficenumber}, #{item.contactmobile}, #{item.contactphone}, #{item.remark},
			#{item.inputDate}, #{item.updatesysdate})
		</foreach>
		select 1 from dual
	</insert>

	<delete id="deleteByPolicyNo" parameterType="string">
		delete from GUPOLICYCOPYHOLDER where policyNo=#{policyNo}
	</delete>
</mapper>
