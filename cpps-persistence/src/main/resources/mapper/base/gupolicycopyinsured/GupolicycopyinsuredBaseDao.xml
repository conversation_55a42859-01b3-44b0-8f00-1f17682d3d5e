<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicycopyinsured.dao.GupolicycopyinsuredDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicycopyinsured.po.Gupolicycopyinsured">
		<id column="ID" property="id" />
		<result column="POLICYNO" property="policyNo" />
		<result column="ENDORNO" property="endorNo" />
		<result column="PLANCODE" property="plancode" />
		<result column="RISKCODE" property="riskCode" />
		<result column="SUBPOLICYNO" property="subpolicyno" />
		<result column="SERIALNO" property="serialNo" />
		<result column="INSUREDTYPE" property="insuredType" />
		<result column="INSUREDCODE" property="insuredCode" />
		<result column="INSUREDNAME" property="insuredName" />
		<result column="INSUREDADDRESS" property="insuredAddress" />
		<result column="INSUREDBUSINESSSOURCE" property="insuredbusinesssource" />
		<result column="INSUREDFLAG" property="insuredFlag" />
		<result column="INSUREDROLE" property="insuredrole" />
		<result column="IDENTIFYTYPE" property="identifyType" />
		<result column="IDENTIFYNUMBER" property="identifyNumber" />
		<result column="INSUREDRELATION" property="insuredrelation" />
		<result column="RELATESERIALNO" property="relateSerialNo" />
		<result column="INSUREDIND" property="insuredind" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="INSUREDSEX" property="insuredsex" />
		<result column="INSUREDBIRTHDATE" property="insuredbirthdate" />
		<result column="INSUREDPOSTCODE" property="insuredpostcode" />
		<result column="INSUREDOFFICEPHONE" property="insuredofficephone" />
		<result column="INSUREDMOBILEPHONE" property="insuredmobilephone" />
		<result column="INSUREDHOMEPHONE" property="insuredhomephone" />
		<result column="CONTACTNAME" property="contactname" />
		<result column="CONTACTPHONE" property="contactphone" />
		<result column="MARRIAGESTATUS" property="marriagestatus" />
		<result column="EDUCATIONBACKGROUND" property="educationbackground" />
		<result column="RELATIONWITHHOLDER" property="relationwithholder" />
		<result column="CONTACTSEX" property="contactsex" />
		<result column="CONTACTTYPE" property="contacttype" />
		<result column="CONTACTDEPARTMENT" property="contactdepartment" />
		<result column="CONTACTPOSITION" property="contactposition" />
		<result column="CONTACTOFFICENUMBER" property="contactofficenumber" />
		<result column="CONTACTMOBILE" property="contactmobile" />
		<result column="RELATEDTYPE" property="relatedtype" />
		<result column="ITEMPROVINCECODE" property="itemprovincecode" />
		<result column="ITEMCITYCODE" property="itemcitycode" />
		<result column="ITEMPROVINCECNAME" property="itemprovincecname" />
		<result column="ITEMCITYCNAME" property="itemcitycname" />
		<result column="MONEYLAUNDERINGIND" property="moneylaunderingind" />
		<result column="STARLEVEL" property="starlevel" />
		<result column="VIPIND" property="vipind" />
		<result column="MACHINECODE" property="machinecode" />
		<result column="IDCOLLECTIND" property="idcollectind" />
		<result column="COUNTYCODE" property="countycode" />
		<result column="EMAIL" property="email" />
		<result column="INDUSTRYMAINCODE" property="industrymaincode" />
		<result column="INDUSTRYKINDCODE" property="industrykindcode" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		POLICYNO,
		ENDORNO,
		PLANCODE,
		RISKCODE,
		SUBPOLICYNO,
		SERIALNO,
		INSUREDTYPE,
		INSUREDCODE,
		INSUREDNAME,
		INSUREDADDRESS,
		INSUREDBUSINESSSOURCE,
		INSUREDFLAG,
		INSUREDROLE,
		IDENTIFYTYPE,
		IDENTIFYNUMBER,
		INSUREDRELATION,
		RELATESERIALNO,
		INSUREDIND,
		REMARK,
		FLAG,
		INSUREDSEX,
		INSUREDBIRTHDATE,
		INSUREDPOSTCODE,
		INSUREDOFFICEPHONE,
		INSUREDMOBILEPHONE,
		INSUREDHOMEPHONE,
		CONTACTNAME,
		CONTACTPHONE,
		MARRIAGESTATUS,
		EDUCATIONBACKGROUND,
		RELATIONWITHHOLDER,
		CONTACTSEX,
		CONTACTTYPE,
		CONTACTDEPARTMENT,
		CONTACTPOSITION,
		CONTACTOFFICENUMBER,
		CONTACTMOBILE,
		RELATEDTYPE,
		ITEMPROVINCECODE,
		ITEMCITYCODE,
		ITEMPROVINCECNAME,
		ITEMCITYCNAME,
		MONEYLAUNDERINGIND,
		STARLEVEL,
		VIPIND,
		MACHINECODE,
		IDCOLLECTIND,
		COUNTYCODE,
		EMAIL,
		INDUSTRYMAINCODE,
		INDUSTRYKINDCODE,
		INPUTDATE,
		UPDATESYSDATE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="endorNo != null and endorNo != ''" >
			and ENDORNO = #{endorNo}
		</if>
		<if test="plancode != null and plancode != ''" >
			and PLANCODE = #{plancode}
		</if>
		<if test="riskCode != null and riskCode != ''" >
			and RISKCODE = #{riskCode}
		</if>
		<if test="subpolicyno != null and subpolicyno != ''" >
			and SUBPOLICYNO = #{subpolicyno}
		</if>
		<if test="serialNo != null and serialNo != ''" >
			and SERIALNO = #{serialNo}
		</if>
		<if test="insuredType != null and insuredType != ''" >
			and INSUREDTYPE = #{insuredType}
		</if>
		<if test="insuredCode != null and insuredCode != ''" >
			and INSUREDCODE = #{insuredCode}
		</if>
		<if test="insuredName != null and insuredName != ''" >
			and INSUREDNAME = #{insuredName}
		</if>
		<if test="insuredAddress != null and insuredAddress != ''" >
			and INSUREDADDRESS = #{insuredAddress}
		</if>
		<if test="insuredbusinesssource != null and insuredbusinesssource != ''" >
			and INSUREDBUSINESSSOURCE = #{insuredbusinesssource}
		</if>
		<if test="insuredFlag != null and insuredFlag != ''" >
			and INSUREDFLAG = #{insuredFlag}
		</if>
		<if test="insuredrole != null and insuredrole != ''" >
			and INSUREDROLE = #{insuredrole}
		</if>
		<if test="identifyType != null and identifyType != ''" >
			and IDENTIFYTYPE = #{identifyType}
		</if>
		<if test="identifyNumber != null and identifyNumber != ''" >
			and IDENTIFYNUMBER = #{identifyNumber}
		</if>
		<if test="insuredrelation != null and insuredrelation != ''" >
			and INSUREDRELATION = #{insuredrelation}
		</if>
		<if test="relateSerialNo != null and relateSerialNo != ''" >
			and RELATESERIALNO = #{relateSerialNo}
		</if>
		<if test="insuredind != null and insuredind != ''" >
			and INSUREDIND = #{insuredind}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="insuredsex != null and insuredsex != ''" >
			and INSUREDSEX = #{insuredsex}
		</if>
		<if test="insuredbirthdate != null and insuredbirthdate != ''" >
			and INSUREDBIRTHDATE = #{insuredbirthdate}
		</if>
		<if test="insuredpostcode != null and insuredpostcode != ''" >
			and INSUREDPOSTCODE = #{insuredpostcode}
		</if>
		<if test="insuredofficephone != null and insuredofficephone != ''" >
			and INSUREDOFFICEPHONE = #{insuredofficephone}
		</if>
		<if test="insuredmobilephone != null and insuredmobilephone != ''" >
			and INSUREDMOBILEPHONE = #{insuredmobilephone}
		</if>
		<if test="insuredhomephone != null and insuredhomephone != ''" >
			and INSUREDHOMEPHONE = #{insuredhomephone}
		</if>
		<if test="contactname != null and contactname != ''" >
			and CONTACTNAME = #{contactname}
		</if>
		<if test="contactphone != null and contactphone != ''" >
			and CONTACTPHONE = #{contactphone}
		</if>
		<if test="marriagestatus != null and marriagestatus != ''" >
			and MARRIAGESTATUS = #{marriagestatus}
		</if>
		<if test="educationbackground != null and educationbackground != ''" >
			and EDUCATIONBACKGROUND = #{educationbackground}
		</if>
		<if test="relationwithholder != null and relationwithholder != ''" >
			and RELATIONWITHHOLDER = #{relationwithholder}
		</if>
		<if test="contactsex != null and contactsex != ''" >
			and CONTACTSEX = #{contactsex}
		</if>
		<if test="contacttype != null and contacttype != ''" >
			and CONTACTTYPE = #{contacttype}
		</if>
		<if test="contactdepartment != null and contactdepartment != ''" >
			and CONTACTDEPARTMENT = #{contactdepartment}
		</if>
		<if test="contactposition != null and contactposition != ''" >
			and CONTACTPOSITION = #{contactposition}
		</if>
		<if test="contactofficenumber != null and contactofficenumber != ''" >
			and CONTACTOFFICENUMBER = #{contactofficenumber}
		</if>
		<if test="contactmobile != null and contactmobile != ''" >
			and CONTACTMOBILE = #{contactmobile}
		</if>
		<if test="relatedtype != null and relatedtype != ''" >
			and RELATEDTYPE = #{relatedtype}
		</if>
		<if test="itemprovincecode != null and itemprovincecode != ''" >
			and ITEMPROVINCECODE = #{itemprovincecode}
		</if>
		<if test="itemcitycode != null and itemcitycode != ''" >
			and ITEMCITYCODE = #{itemcitycode}
		</if>
		<if test="itemprovincecname != null and itemprovincecname != ''" >
			and ITEMPROVINCECNAME = #{itemprovincecname}
		</if>
		<if test="itemcitycname != null and itemcitycname != ''" >
			and ITEMCITYCNAME = #{itemcitycname}
		</if>
		<if test="moneylaunderingind != null and moneylaunderingind != ''" >
			and MONEYLAUNDERINGIND = #{moneylaunderingind}
		</if>
		<if test="starlevel != null and starlevel != ''" >
			and STARLEVEL = #{starlevel}
		</if>
		<if test="vipind != null and vipind != ''" >
			and VIPIND = #{vipind}
		</if>
		<if test="machinecode != null and machinecode != ''" >
			and MACHINECODE = #{machinecode}
		</if>
		<if test="idcollectind != null and idcollectind != ''" >
			and IDCOLLECTIND = #{idcollectind}
		</if>
		<if test="countycode != null and countycode != ''" >
			and COUNTYCODE = #{countycode}
		</if>
		<if test="email != null and email != ''" >
			and EMAIL = #{email}
		</if>
		<if test="industrymaincode != null and industrymaincode != ''" >
			and INDUSTRYMAINCODE = #{industrymaincode}
		</if>
		<if test="industrykindcode != null and industrykindcode != ''" >
			and INDUSTRYKINDCODE = #{industrykindcode}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYINSURED
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYINSURED
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYINSURED
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicycopyinsured.po.Gupolicycopyinsured">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYCOPYINSURED
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYCOPYINSURED
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicycopyinsured.po.Gupolicycopyinsured">
		insert into GUPOLICYCOPYINSURED (
			ID,
			POLICYNO,
			ENDORNO,
			PLANCODE,
			RISKCODE,
			SUBPOLICYNO,
			SERIALNO,
			INSUREDTYPE,
			INSUREDCODE,
			INSUREDNAME,
			INSUREDADDRESS,
			INSUREDBUSINESSSOURCE,
			INSUREDFLAG,
			INSUREDROLE,
			IDENTIFYTYPE,
			IDENTIFYNUMBER,
			INSUREDRELATION,
			RELATESERIALNO,
			INSUREDIND,
			REMARK,
			FLAG,
			INSUREDSEX,
			INSUREDBIRTHDATE,
			INSUREDPOSTCODE,
			INSUREDOFFICEPHONE,
			INSUREDMOBILEPHONE,
			INSUREDHOMEPHONE,
			CONTACTNAME,
			CONTACTPHONE,
			MARRIAGESTATUS,
			EDUCATIONBACKGROUND,
			RELATIONWITHHOLDER,
			CONTACTSEX,
			CONTACTTYPE,
			CONTACTDEPARTMENT,
			CONTACTPOSITION,
			CONTACTOFFICENUMBER,
			CONTACTMOBILE,
			RELATEDTYPE,
			ITEMPROVINCECODE,
			ITEMCITYCODE,
			ITEMPROVINCECNAME,
			ITEMCITYCNAME,
			MONEYLAUNDERINGIND,
			STARLEVEL,
			VIPIND,
			MACHINECODE,
			IDCOLLECTIND,
			COUNTYCODE,
			EMAIL,
			INDUSTRYMAINCODE,
			INDUSTRYKINDCODE,
			INPUTDATE,
			UPDATESYSDATE
		) values (
			#{id},
			#{policyNo},
			#{endorNo},
			#{plancode},
			#{riskCode},
			#{subpolicyno},
			#{serialNo},
			#{insuredType},
			#{insuredCode},
			#{insuredName},
			#{insuredAddress},
			#{insuredbusinesssource},
			#{insuredFlag},
			#{insuredrole},
			#{identifyType},
			#{identifyNumber},
			#{insuredrelation},
			#{relateSerialNo},
			#{insuredind},
			#{remark},
			#{flag},
			#{insuredsex},
			#{insuredbirthdate},
			#{insuredpostcode},
			#{insuredofficephone},
			#{insuredmobilephone},
			#{insuredhomephone},
			#{contactname},
			#{contactphone},
			#{marriagestatus},
			#{educationbackground},
			#{relationwithholder},
			#{contactsex},
			#{contacttype},
			#{contactdepartment},
			#{contactposition},
			#{contactofficenumber},
			#{contactmobile},
			#{relatedtype},
			#{itemprovincecode},
			#{itemcitycode},
			#{itemprovincecname},
			#{itemcitycname},
			#{moneylaunderingind},
			#{starlevel},
			#{vipind},
			#{machinecode},
			#{idcollectind},
			#{countycode},
			#{email},
			#{industrymaincode},
			#{industrykindcode},
			#{inputDate},
			#{updatesysdate}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicycopyinsured.po.Gupolicycopyinsured">
		insert into GUPOLICYCOPYINSURED
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="endorNo != null" >
				ENDORNO,
			</if>
			<if test="plancode != null" >
				PLANCODE,
			</if>
			<if test="riskCode != null" >
				RISKCODE,
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO,
			</if>
			<if test="serialNo != null" >
				SERIALNO,
			</if>
			<if test="insuredType != null" >
				INSUREDTYPE,
			</if>
			<if test="insuredCode != null" >
				INSUREDCODE,
			</if>
			<if test="insuredName != null" >
				INSUREDNAME,
			</if>
			<if test="insuredAddress != null" >
				INSUREDADDRESS,
			</if>
			<if test="insuredbusinesssource != null" >
				INSUREDBUSINESSSOURCE,
			</if>
			<if test="insuredFlag != null" >
				INSUREDFLAG,
			</if>
			<if test="insuredrole != null" >
				INSUREDROLE,
			</if>
			<if test="identifyType != null" >
				IDENTIFYTYPE,
			</if>
			<if test="identifyNumber != null" >
				IDENTIFYNUMBER,
			</if>
			<if test="insuredrelation != null" >
				INSUREDRELATION,
			</if>
			<if test="relateSerialNo != null" >
				RELATESERIALNO,
			</if>
			<if test="insuredind != null" >
				INSUREDIND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="insuredsex != null" >
				INSUREDSEX,
			</if>
			<if test="insuredbirthdate != null" >
				INSUREDBIRTHDATE,
			</if>
			<if test="insuredpostcode != null" >
				INSUREDPOSTCODE,
			</if>
			<if test="insuredofficephone != null" >
				INSUREDOFFICEPHONE,
			</if>
			<if test="insuredmobilephone != null" >
				INSUREDMOBILEPHONE,
			</if>
			<if test="insuredhomephone != null" >
				INSUREDHOMEPHONE,
			</if>
			<if test="contactname != null" >
				CONTACTNAME,
			</if>
			<if test="contactphone != null" >
				CONTACTPHONE,
			</if>
			<if test="marriagestatus != null" >
				MARRIAGESTATUS,
			</if>
			<if test="educationbackground != null" >
				EDUCATIONBACKGROUND,
			</if>
			<if test="relationwithholder != null" >
				RELATIONWITHHOLDER,
			</if>
			<if test="contactsex != null" >
				CONTACTSEX,
			</if>
			<if test="contacttype != null" >
				CONTACTTYPE,
			</if>
			<if test="contactdepartment != null" >
				CONTACTDEPARTMENT,
			</if>
			<if test="contactposition != null" >
				CONTACTPOSITION,
			</if>
			<if test="contactofficenumber != null" >
				CONTACTOFFICENUMBER,
			</if>
			<if test="contactmobile != null" >
				CONTACTMOBILE,
			</if>
			<if test="relatedtype != null" >
				RELATEDTYPE,
			</if>
			<if test="itemprovincecode != null" >
				ITEMPROVINCECODE,
			</if>
			<if test="itemcitycode != null" >
				ITEMCITYCODE,
			</if>
			<if test="itemprovincecname != null" >
				ITEMPROVINCECNAME,
			</if>
			<if test="itemcitycname != null" >
				ITEMCITYCNAME,
			</if>
			<if test="moneylaunderingind != null" >
				MONEYLAUNDERINGIND,
			</if>
			<if test="starlevel != null" >
				STARLEVEL,
			</if>
			<if test="vipind != null" >
				VIPIND,
			</if>
			<if test="machinecode != null" >
				MACHINECODE,
			</if>
			<if test="idcollectind != null" >
				IDCOLLECTIND,
			</if>
			<if test="countycode != null" >
				COUNTYCODE,
			</if>
			<if test="email != null" >
				EMAIL,
			</if>
			<if test="industrymaincode != null" >
				INDUSTRYMAINCODE,
			</if>
			<if test="industrykindcode != null" >
				INDUSTRYKINDCODE,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="endorNo != null" >
				#{endorNo},
			</if>
			<if test="plancode != null" >
				#{plancode},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="subpolicyno != null" >
				#{subpolicyno},
			</if>
			<if test="serialNo != null" >
				#{serialNo},
			</if>
			<if test="insuredType != null" >
				#{insuredType},
			</if>
			<if test="insuredCode != null" >
				#{insuredCode},
			</if>
			<if test="insuredName != null" >
				#{insuredName},
			</if>
			<if test="insuredAddress != null" >
				#{insuredAddress},
			</if>
			<if test="insuredbusinesssource != null" >
				#{insuredbusinesssource},
			</if>
			<if test="insuredFlag != null" >
				#{insuredFlag},
			</if>
			<if test="insuredrole != null" >
				#{insuredrole},
			</if>
			<if test="identifyType != null" >
				#{identifyType},
			</if>
			<if test="identifyNumber != null" >
				#{identifyNumber},
			</if>
			<if test="insuredrelation != null" >
				#{insuredrelation},
			</if>
			<if test="relateSerialNo != null" >
				#{relateSerialNo},
			</if>
			<if test="insuredind != null" >
				#{insuredind},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="insuredsex != null" >
				#{insuredsex},
			</if>
			<if test="insuredbirthdate != null" >
				#{insuredbirthdate},
			</if>
			<if test="insuredpostcode != null" >
				#{insuredpostcode},
			</if>
			<if test="insuredofficephone != null" >
				#{insuredofficephone},
			</if>
			<if test="insuredmobilephone != null" >
				#{insuredmobilephone},
			</if>
			<if test="insuredhomephone != null" >
				#{insuredhomephone},
			</if>
			<if test="contactname != null" >
				#{contactname},
			</if>
			<if test="contactphone != null" >
				#{contactphone},
			</if>
			<if test="marriagestatus != null" >
				#{marriagestatus},
			</if>
			<if test="educationbackground != null" >
				#{educationbackground},
			</if>
			<if test="relationwithholder != null" >
				#{relationwithholder},
			</if>
			<if test="contactsex != null" >
				#{contactsex},
			</if>
			<if test="contacttype != null" >
				#{contacttype},
			</if>
			<if test="contactdepartment != null" >
				#{contactdepartment},
			</if>
			<if test="contactposition != null" >
				#{contactposition},
			</if>
			<if test="contactofficenumber != null" >
				#{contactofficenumber},
			</if>
			<if test="contactmobile != null" >
				#{contactmobile},
			</if>
			<if test="relatedtype != null" >
				#{relatedtype},
			</if>
			<if test="itemprovincecode != null" >
				#{itemprovincecode},
			</if>
			<if test="itemcitycode != null" >
				#{itemcitycode},
			</if>
			<if test="itemprovincecname != null" >
				#{itemprovincecname},
			</if>
			<if test="itemcitycname != null" >
				#{itemcitycname},
			</if>
			<if test="moneylaunderingind != null" >
				#{moneylaunderingind},
			</if>
			<if test="starlevel != null" >
				#{starlevel},
			</if>
			<if test="vipind != null" >
				#{vipind},
			</if>
			<if test="machinecode != null" >
				#{machinecode},
			</if>
			<if test="idcollectind != null" >
				#{idcollectind},
			</if>
			<if test="countycode != null" >
				#{countycode},
			</if>
			<if test="email != null" >
				#{email},
			</if>
			<if test="industrymaincode != null" >
				#{industrymaincode},
			</if>
			<if test="industrykindcode != null" >
				#{industrykindcode},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate}
			</if>
		</trim>
	</insert>

    <!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicycopyinsured.po.Gupolicycopyinsured">
		update GUPOLICYCOPYINSURED 
		<set>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="endorNo != null" >
				ENDORNO=#{endorNo},
			</if>
			<if test="plancode != null" >
				PLANCODE=#{plancode},
			</if>
			<if test="riskCode != null" >
				RISKCODE=#{riskCode},
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO=#{subpolicyno},
			</if>
			<if test="serialNo != null" >
				SERIALNO=#{serialNo},
			</if>
			<if test="insuredType != null" >
				INSUREDTYPE=#{insuredType},
			</if>
			<if test="insuredCode != null" >
				INSUREDCODE=#{insuredCode},
			</if>
			<if test="insuredName != null" >
				INSUREDNAME=#{insuredName},
			</if>
			<if test="insuredAddress != null" >
				INSUREDADDRESS=#{insuredAddress},
			</if>
			<if test="insuredbusinesssource != null" >
				INSUREDBUSINESSSOURCE=#{insuredbusinesssource},
			</if>
			<if test="insuredFlag != null" >
				INSUREDFLAG=#{insuredFlag},
			</if>
			<if test="insuredrole != null" >
				INSUREDROLE=#{insuredrole},
			</if>
			<if test="identifyType != null" >
				IDENTIFYTYPE=#{identifyType},
			</if>
			<if test="identifyNumber != null" >
				IDENTIFYNUMBER=#{identifyNumber},
			</if>
			<if test="insuredrelation != null" >
				INSUREDRELATION=#{insuredrelation},
			</if>
			<if test="relateSerialNo != null" >
				RELATESERIALNO=#{relateSerialNo},
			</if>
			<if test="insuredind != null" >
				INSUREDIND=#{insuredind},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="insuredsex != null" >
				INSUREDSEX=#{insuredsex},
			</if>
			<if test="insuredbirthdate != null" >
				INSUREDBIRTHDATE=#{insuredbirthdate},
			</if>
			<if test="insuredpostcode != null" >
				INSUREDPOSTCODE=#{insuredpostcode},
			</if>
			<if test="insuredofficephone != null" >
				INSUREDOFFICEPHONE=#{insuredofficephone},
			</if>
			<if test="insuredmobilephone != null" >
				INSUREDMOBILEPHONE=#{insuredmobilephone},
			</if>
			<if test="insuredhomephone != null" >
				INSUREDHOMEPHONE=#{insuredhomephone},
			</if>
			<if test="contactname != null" >
				CONTACTNAME=#{contactname},
			</if>
			<if test="contactphone != null" >
				CONTACTPHONE=#{contactphone},
			</if>
			<if test="marriagestatus != null" >
				MARRIAGESTATUS=#{marriagestatus},
			</if>
			<if test="educationbackground != null" >
				EDUCATIONBACKGROUND=#{educationbackground},
			</if>
			<if test="relationwithholder != null" >
				RELATIONWITHHOLDER=#{relationwithholder},
			</if>
			<if test="contactsex != null" >
				CONTACTSEX=#{contactsex},
			</if>
			<if test="contacttype != null" >
				CONTACTTYPE=#{contacttype},
			</if>
			<if test="contactdepartment != null" >
				CONTACTDEPARTMENT=#{contactdepartment},
			</if>
			<if test="contactposition != null" >
				CONTACTPOSITION=#{contactposition},
			</if>
			<if test="contactofficenumber != null" >
				CONTACTOFFICENUMBER=#{contactofficenumber},
			</if>
			<if test="contactmobile != null" >
				CONTACTMOBILE=#{contactmobile},
			</if>
			<if test="relatedtype != null" >
				RELATEDTYPE=#{relatedtype},
			</if>
			<if test="itemprovincecode != null" >
				ITEMPROVINCECODE=#{itemprovincecode},
			</if>
			<if test="itemcitycode != null" >
				ITEMCITYCODE=#{itemcitycode},
			</if>
			<if test="itemprovincecname != null" >
				ITEMPROVINCECNAME=#{itemprovincecname},
			</if>
			<if test="itemcitycname != null" >
				ITEMCITYCNAME=#{itemcitycname},
			</if>
			<if test="moneylaunderingind != null" >
				MONEYLAUNDERINGIND=#{moneylaunderingind},
			</if>
			<if test="starlevel != null" >
				STARLEVEL=#{starlevel},
			</if>
			<if test="vipind != null" >
				VIPIND=#{vipind},
			</if>
			<if test="machinecode != null" >
				MACHINECODE=#{machinecode},
			</if>
			<if test="idcollectind != null" >
				IDCOLLECTIND=#{idcollectind},
			</if>
			<if test="countycode != null" >
				COUNTYCODE=#{countycode},
			</if>
			<if test="email != null" >
				EMAIL=#{email},
			</if>
			<if test="industrymaincode != null" >
				INDUSTRYMAINCODE=#{industrymaincode},
			</if>
			<if test="industrykindcode != null" >
				INDUSTRYKINDCODE=#{industrykindcode},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicycopyinsured.po.Gupolicycopyinsured">
		update GUPOLICYCOPYINSURED set
			POLICYNO=#{policyNo},
			ENDORNO=#{endorNo},
			PLANCODE=#{plancode},
			RISKCODE=#{riskCode},
			SUBPOLICYNO=#{subpolicyno},
			SERIALNO=#{serialNo},
			INSUREDTYPE=#{insuredType},
			INSUREDCODE=#{insuredCode},
			INSUREDNAME=#{insuredName},
			INSUREDADDRESS=#{insuredAddress},
			INSUREDBUSINESSSOURCE=#{insuredbusinesssource},
			INSUREDFLAG=#{insuredFlag},
			INSUREDROLE=#{insuredrole},
			IDENTIFYTYPE=#{identifyType},
			IDENTIFYNUMBER=#{identifyNumber},
			INSUREDRELATION=#{insuredrelation},
			RELATESERIALNO=#{relateSerialNo},
			INSUREDIND=#{insuredind},
			REMARK=#{remark},
			FLAG=#{flag},
			INSUREDSEX=#{insuredsex},
			INSUREDBIRTHDATE=#{insuredbirthdate},
			INSUREDPOSTCODE=#{insuredpostcode},
			INSUREDOFFICEPHONE=#{insuredofficephone},
			INSUREDMOBILEPHONE=#{insuredmobilephone},
			INSUREDHOMEPHONE=#{insuredhomephone},
			CONTACTNAME=#{contactname},
			CONTACTPHONE=#{contactphone},
			MARRIAGESTATUS=#{marriagestatus},
			EDUCATIONBACKGROUND=#{educationbackground},
			RELATIONWITHHOLDER=#{relationwithholder},
			CONTACTSEX=#{contactsex},
			CONTACTTYPE=#{contacttype},
			CONTACTDEPARTMENT=#{contactdepartment},
			CONTACTPOSITION=#{contactposition},
			CONTACTOFFICENUMBER=#{contactofficenumber},
			CONTACTMOBILE=#{contactmobile},
			RELATEDTYPE=#{relatedtype},
			ITEMPROVINCECODE=#{itemprovincecode},
			ITEMCITYCODE=#{itemcitycode},
			ITEMPROVINCECNAME=#{itemprovincecname},
			ITEMCITYCNAME=#{itemcitycname},
			MONEYLAUNDERINGIND=#{moneylaunderingind},
			STARLEVEL=#{starlevel},
			VIPIND=#{vipind},
			MACHINECODE=#{machinecode},
			IDCOLLECTIND=#{idcollectind},
			COUNTYCODE=#{countycode},
			EMAIL=#{email},
			INDUSTRYMAINCODE=#{industrymaincode},
			INDUSTRYKINDCODE=#{industrykindcode},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
		where ID = #{id}	</update>

	<insert id="batchInsert">
		INSERT ALL
		<foreach collection="list" item="item" separator="">
			INTO GUPOLICYCOPYINSURED VALUES
			(#{item.id}, #{item.policyNo}, #{item.endorNo}, #{item.plancode}, #{item.riskCode},
			#{item.subpolicyno}, #{item.serialNo}, #{item.insuredType}, #{item.insuredCode},
			#{item.insuredName}, #{item.insuredAddress}, #{item.insuredbusinesssource}, #{item.insuredFlag},
			#{item.insuredrole}, #{item.identifyType}, #{item.identifyNumber}, #{item.insuredrelation},
			#{item.relateSerialNo}, #{item.insuredind}, #{item.remark}, #{item.flag}, #{item.insuredsex},
			#{item.insuredbirthdate}, #{item.insuredpostcode}, #{item.insuredofficephone},
			#{item.insuredmobilephone}, #{item.insuredhomephone}, #{item.contactname}, #{item.contactphone},
			#{item.marriagestatus}, #{item.educationbackground}, #{item.relationwithholder}, #{item.contactsex},
			#{item.contacttype}, #{item.contactdepartment}, #{item.contactposition}, #{item.contactofficenumber},
			#{item.contactmobile}, #{item.relatedtype}, #{item.itemprovincecode}, #{item.itemcitycode},
			#{item.itemprovincecname}, #{item.itemcitycname}, #{item.moneylaunderingind}, #{item.starlevel},
			#{item.vipind}, #{item.machinecode}, #{item.idcollectind}, #{item.countycode}, #{item.email},
			#{item.industrymaincode}, #{item.industrykindcode}, #{item.inputDate}, #{item.updatesysdate})
		</foreach>
		select 1 from dual
	</insert>

	<delete id="deleteByPolicyNo" parameterType="string">
		delete from GUPOLICYCOPYINSURED where policyNo=#{policyNo}
	</delete>
</mapper>
