<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicycopyitemaccilist.dao.GupolicycopyitemaccilistDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicycopyitemaccilist.po.Gupolicycopyitemaccilist">
		<id column="CLIENTNO" property="clientno" />
		<id column="ENDORSEQNO" property="endorseqno" />
		<id column="ITEMNO" property="itemNo" />
		<id column="PLANCODE" property="plancode" />
		<id column="POLICYNO" property="policyNo" />
		<id column="RISKCODE" property="riskCode" />
		<result column="ITEMCODE" property="itemCode" />
		<result column="ITEMNAME" property="itemName" />
		<result column="ITEMDETAILNO" property="itemdetailno" />
		<result column="ITEMDETAILCODE" property="itemdetailcode" />
		<result column="ITEMDETAILNAME" property="itemDetailName" />
		<result column="CLIENTCODE" property="clientcode" />
		<result column="MEMBERNO" property="memberno" />
		<result column="CLIENTCNAME" property="clientcname" />
		<result column="CLIENTENAME" property="clientename" />
		<result column="SEX" property="sex" />
		<result column="BIRTHDAY" property="birthday" />
		<result column="AGE" property="age" />
		<result column="IDENTIFYTYPEA" property="identifytypea" />
		<result column="IDENTIFYNOA" property="identifynoa" />
		<result column="IDENTIFYTYPEB" property="identifytypeb" />
		<result column="IDENTIFYNOB" property="identifynob" />
		<result column="ENROLLMENTDATE" property="enrollmentdate" />
		<result column="STARTDATE" property="startDate" />
		<result column="ENDDATE" property="endDate" />
		<result column="BANKNAME" property="bankName" />
		<result column="BANKACCOUNTNO" property="bankaccountno" />
		<result column="CREDITNO" property="creditNo" />
		<result column="CREDITEXPIRY" property="creditexpiry" />
		<result column="OCCUPATIONTYPE" property="occupationtype" />
		<result column="OCCUPATION" property="occupation" />
		<result column="OCCUPATIONCODE" property="occupationCode" />
		<result column="JOBTITLE" property="jobTitle" />
		<result column="HOMEADDRESS" property="homeAddress" />
		<result column="HOMETEL" property="hometel" />
		<result column="BUSINESSNATURE" property="businessNature" />
		<result column="JOBUNITCODE" property="jobunitcode" />
		<result column="JOBUNITNAME" property="jobunitname" />
		<result column="OFFICETEL" property="officetel" />
		<result column="EMPLOYERNAME" property="employername" />
		<result column="EMPLOYEEIND" property="employeeind" />
		<result column="MATERNITYIND" property="maternityind" />
		<result column="AUTOPAYIND" property="autopayind" />
		<result column="RELATIONCODE" property="relationCode" />
		<result column="RELATIONSHIP" property="relationship" />
		<result column="PROJECTCODE" property="projectcode" />
		<result column="UWCOUNT" property="uwcount" />
		<result column="SUMINSURED" property="suminsured" />
		<result column="BASEPREMIUM" property="basePremium" />
		<result column="NETPREMIUM" property="netPremium" />
		<result column="GROSSPREMIUM" property="grosspremium" />
		<result column="ANNUALPREMIUM" property="annualpremium" />
		<result column="PRORATAPREMIUM" property="proratapremium" />
		<result column="EXPRIREFUND" property="exprirefund" />
		<result column="PREEXISTIND" property="preexistind" />
		<result column="ACTIVEIND" property="activeind" />
		<result column="COMMENCEDATE" property="commencedate" />
		<result column="EMAIL" property="email" />
		<result column="DISTRICT" property="district" />
		<result column="IPASERVICE" property="ipaservice" />
		<result column="COUNTRYCODE" property="countryCode" />
		<result column="REGISTADDRESS" property="registaddress" />
		<result column="MEMBERREFRENCE" property="memberrefrence" />
		<result column="DISCOUNT" property="discount" />
		<result column="SPECIALCLAUSE" property="specialclause" />
		<result column="ENDORIND" property="endorind" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="JOURNEYSTART" property="journeystart" />
		<result column="JOURNEYEND" property="journeyend" />
		<result column="JOURNEYBACK" property="journeyback" />
		<result column="APPLIRELATION" property="applirelation" />
		<result column="LINKERNAME" property="linkerName" />
		<result column="LINKERPHONE" property="linkerphone" />
		<result column="INNERREMARK" property="innerremark" />
		<result column="PLEDGED" property="pledged" />
		<result column="CLAIMPAYWAY" property="claimpayway" />
		<result column="CLIENTTYPE" property="clienttype" />
		<result column="OCCUPATIONTYPENAME" property="occupationtypename" />
		<result column="OCCUPATIONLEVEL" property="occupationlevel" />
		<result column="MAININSUREDIND" property="maininsuredind" />
		<result column="SURNAME" property="surname" />
		<result column="MONIKER" property="moniker" />
		<result column="FIRSTNAME" property="firstname" />
		<result column="LASTNAME" property="lastname" />
		<result column="PROVINCECODE" property="provincecode" />
		<result column="CITYCODE" property="citycode" />
		<result column="COUNTYCODE" property="countycode" />
		<result column="DISPLAYNO" property="displayNo" />
		<result column="ENDORFLAG" property="endorflag" />
		<result column="GROUPTYPE" property="groupType" />
		<result column="SIGNATURE" property="signature" />
		<result column="ITEMADDRESS" property="itemAddress" />
		<result column="USENATURECODE" property="useNatureCode" />
		<result column="DRIVERTYPE" property="drivertype" />
		<result column="DRIVERLICENSENOOLD" property="driverlicensenoold" />
		<result column="DRIVERLICENSEEXPIRATIONDATE" property="driverlicenseexpirationdate" />
		<result column="BENEFITPROJECT" property="benefitproject" />
		<result column="SOCIALSECURITYINFO" property="socialsecurityinfo" />
		<result column="DRIVINGMODEL" property="drivingmodel" />
		<result column="DRIVERLICENSENO" property="driverlicenseno" />
		<result column="ONTHEJOBSTATUS" property="onthejobstatus" />
		<result column="PLATCUSTOMNO" property="platcustomno" />
		<result column="BEIFENDESCRIPTION" property="beifendescription" />
		<result column="INSUREDNAME" property="insuredName" />
		<result column="INSUREDSEX" property="insuredsex" />
		<result column="INSUREDBIRTHDAY" property="insuredbirthday" />
		<result column="IDENTIFYTYPEC" property="identifytypec" />
		<result column="IDENTIFYNOC" property="identifynoc" />
		<result column="INSUREDHOMEADDRESS" property="insuredhomeaddress" />
		<result column="INSUREDHOMETEL" property="insuredhometel" />
		<result column="INSUREDPHONE" property="insuredPhone" />
		<result column="INSUREDEMAIL" property="insuredemail" />
		<result column="INSUREDCLASS" property="insuredclass" />
		<result column="SCHOOL" property="school" />
		<result column="JOINTINSUREDFLAG" property="jointinsuredflag" />
		<result column="VACCINATION" property="vaccination" />
		<result column="INOCULABILITYTIME" property="inoculabilitytime" />
		<result column="PORTABLETYPE" property="portabletype" />
		<result column="BRAND" property="brand" />
		<result column="MODEL" property="model" />
		<result column="UNIQUEENCODING" property="uniqueencoding" />
		<result column="UPLOADTYPE" property="uploadtype" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		POLICYNO,
		ENDORSEQNO,
		ITEMNO,
		ITEMCODE,
		ITEMNAME,
		ITEMDETAILNO,
		ITEMDETAILCODE,
		ITEMDETAILNAME,
		CLIENTNO,
		CLIENTCODE,
		MEMBERNO,
		CLIENTCNAME,
		CLIENTENAME,
		SEX,
		BIRTHDAY,
		AGE,
		IDENTIFYTYPEA,
		IDENTIFYNOA,
		IDENTIFYTYPEB,
		IDENTIFYNOB,
		ENROLLMENTDATE,
		STARTDATE,
		ENDDATE,
		BANKNAME,
		BANKACCOUNTNO,
		CREDITNO,
		CREDITEXPIRY,
		OCCUPATIONTYPE,
		OCCUPATION,
		OCCUPATIONCODE,
		JOBTITLE,
		HOMEADDRESS,
		HOMETEL,
		BUSINESSNATURE,
		JOBUNITCODE,
		JOBUNITNAME,
		OFFICETEL,
		EMPLOYERNAME,
		EMPLOYEEIND,
		MATERNITYIND,
		AUTOPAYIND,
		RELATIONCODE,
		RELATIONSHIP,
		PROJECTCODE,
		UWCOUNT,
		SUMINSURED,
		BASEPREMIUM,
		NETPREMIUM,
		GROSSPREMIUM,
		ANNUALPREMIUM,
		PRORATAPREMIUM,
		EXPRIREFUND,
		PREEXISTIND,
		ACTIVEIND,
		COMMENCEDATE,
		EMAIL,
		DISTRICT,
		IPASERVICE,
		COUNTRYCODE,
		REGISTADDRESS,
		MEMBERREFRENCE,
		DISCOUNT,
		SPECIALCLAUSE,
		ENDORIND,
		REMARK,
		FLAG,
		JOURNEYSTART,
		JOURNEYEND,
		JOURNEYBACK,
		APPLIRELATION,
		LINKERNAME,
		LINKERPHONE,
		INNERREMARK,
		PLEDGED,
		CLAIMPAYWAY,
		CLIENTTYPE,
		OCCUPATIONTYPENAME,
		OCCUPATIONLEVEL,
		MAININSUREDIND,
		PLANCODE,
		RISKCODE,
		SURNAME,
		MONIKER,
		FIRSTNAME,
		LASTNAME,
		PROVINCECODE,
		CITYCODE,
		COUNTYCODE,
		DISPLAYNO,
		ENDORFLAG,
		GROUPTYPE,
		SIGNATURE,
		ITEMADDRESS,
		USENATURECODE,
		DRIVERTYPE,
		DRIVERLICENSENOOLD,
		DRIVERLICENSEEXPIRATIONDATE,
		BENEFITPROJECT,
		SOCIALSECURITYINFO,
		DRIVINGMODEL,
		DRIVERLICENSENO,
		ONTHEJOBSTATUS,
		PLATCUSTOMNO,
		BEIFENDESCRIPTION,
		INSUREDNAME,
		INSUREDSEX,
		INSUREDBIRTHDAY,
		IDENTIFYTYPEC,
		IDENTIFYNOC,
		INSUREDHOMEADDRESS,
		INSUREDHOMETEL,
		INSUREDPHONE,
		INSUREDEMAIL,
		INSUREDCLASS,
		SCHOOL,
		JOINTINSUREDFLAG,
		VACCINATION,
		INOCULABILITYTIME,
		PORTABLETYPE,
		BRAND,
		MODEL,
		UNIQUEENCODING,
		UPLOADTYPE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="endorseqno != null and endorseqno != ''" >
			and ENDORSEQNO = #{endorseqno}
		</if>
		<if test="itemNo != null and itemNo != ''" >
			and ITEMNO = #{itemNo}
		</if>
		<if test="itemCode != null and itemCode != ''" >
			and ITEMCODE = #{itemCode}
		</if>
		<if test="itemName != null and itemName != ''" >
			and ITEMNAME = #{itemName}
		</if>
		<if test="itemdetailno != null and itemdetailno != ''" >
			and ITEMDETAILNO = #{itemdetailno}
		</if>
		<if test="itemdetailcode != null and itemdetailcode != ''" >
			and ITEMDETAILCODE = #{itemdetailcode}
		</if>
		<if test="itemDetailName != null and itemDetailName != ''" >
			and ITEMDETAILNAME = #{itemDetailName}
		</if>
		<if test="clientno != null and clientno != ''" >
			and CLIENTNO = #{clientno}
		</if>
		<if test="clientcode != null and clientcode != ''" >
			and CLIENTCODE = #{clientcode}
		</if>
		<if test="memberno != null and memberno != ''" >
			and MEMBERNO = #{memberno}
		</if>
		<if test="clientcname != null and clientcname != ''" >
			and CLIENTCNAME = #{clientcname}
		</if>
		<if test="clientename != null and clientename != ''" >
			and CLIENTENAME = #{clientename}
		</if>
		<if test="sex != null and sex != ''" >
			and SEX = #{sex}
		</if>
		<if test="birthday != null and birthday != ''" >
			and BIRTHDAY = #{birthday}
		</if>
		<if test="age != null and age != ''" >
			and AGE = #{age}
		</if>
		<if test="identifytypea != null and identifytypea != ''" >
			and IDENTIFYTYPEA = #{identifytypea}
		</if>
		<if test="identifynoa != null and identifynoa != ''" >
			and IDENTIFYNOA = #{identifynoa}
		</if>
		<if test="identifytypeb != null and identifytypeb != ''" >
			and IDENTIFYTYPEB = #{identifytypeb}
		</if>
		<if test="identifynob != null and identifynob != ''" >
			and IDENTIFYNOB = #{identifynob}
		</if>
		<if test="enrollmentdate != null and enrollmentdate != ''" >
			and ENROLLMENTDATE = #{enrollmentdate}
		</if>
		<if test="startDate != null and startDate != ''" >
			and STARTDATE = #{startDate}
		</if>
		<if test="endDate != null and endDate != ''" >
			and ENDDATE = #{endDate}
		</if>
		<if test="bankName != null and bankName != ''" >
			and BANKNAME = #{bankName}
		</if>
		<if test="bankaccountno != null and bankaccountno != ''" >
			and BANKACCOUNTNO = #{bankaccountno}
		</if>
		<if test="creditNo != null and creditNo != ''" >
			and CREDITNO = #{creditNo}
		</if>
		<if test="creditexpiry != null and creditexpiry != ''" >
			and CREDITEXPIRY = #{creditexpiry}
		</if>
		<if test="occupationtype != null and occupationtype != ''" >
			and OCCUPATIONTYPE = #{occupationtype}
		</if>
		<if test="occupation != null and occupation != ''" >
			and OCCUPATION = #{occupation}
		</if>
		<if test="occupationCode != null and occupationCode != ''" >
			and OCCUPATIONCODE = #{occupationCode}
		</if>
		<if test="jobTitle != null and jobTitle != ''" >
			and JOBTITLE = #{jobTitle}
		</if>
		<if test="homeAddress != null and homeAddress != ''" >
			and HOMEADDRESS = #{homeAddress}
		</if>
		<if test="hometel != null and hometel != ''" >
			and HOMETEL = #{hometel}
		</if>
		<if test="businessNature != null and businessNature != ''" >
			and BUSINESSNATURE = #{businessNature}
		</if>
		<if test="jobunitcode != null and jobunitcode != ''" >
			and JOBUNITCODE = #{jobunitcode}
		</if>
		<if test="jobunitname != null and jobunitname != ''" >
			and JOBUNITNAME = #{jobunitname}
		</if>
		<if test="officetel != null and officetel != ''" >
			and OFFICETEL = #{officetel}
		</if>
		<if test="employername != null and employername != ''" >
			and EMPLOYERNAME = #{employername}
		</if>
		<if test="employeeind != null and employeeind != ''" >
			and EMPLOYEEIND = #{employeeind}
		</if>
		<if test="maternityind != null and maternityind != ''" >
			and MATERNITYIND = #{maternityind}
		</if>
		<if test="autopayind != null and autopayind != ''" >
			and AUTOPAYIND = #{autopayind}
		</if>
		<if test="relationCode != null and relationCode != ''" >
			and RELATIONCODE = #{relationCode}
		</if>
		<if test="relationship != null and relationship != ''" >
			and RELATIONSHIP = #{relationship}
		</if>
		<if test="projectcode != null and projectcode != ''" >
			and PROJECTCODE = #{projectcode}
		</if>
		<if test="uwcount != null and uwcount != ''" >
			and UWCOUNT = #{uwcount}
		</if>
		<if test="suminsured != null and suminsured != ''" >
			and SUMINSURED = #{suminsured}
		</if>
		<if test="basePremium != null and basePremium != ''" >
			and BASEPREMIUM = #{basePremium}
		</if>
		<if test="netPremium != null and netPremium != ''" >
			and NETPREMIUM = #{netPremium}
		</if>
		<if test="grosspremium != null and grosspremium != ''" >
			and GROSSPREMIUM = #{grosspremium}
		</if>
		<if test="annualpremium != null and annualpremium != ''" >
			and ANNUALPREMIUM = #{annualpremium}
		</if>
		<if test="proratapremium != null and proratapremium != ''" >
			and PRORATAPREMIUM = #{proratapremium}
		</if>
		<if test="exprirefund != null and exprirefund != ''" >
			and EXPRIREFUND = #{exprirefund}
		</if>
		<if test="preexistind != null and preexistind != ''" >
			and PREEXISTIND = #{preexistind}
		</if>
		<if test="activeind != null and activeind != ''" >
			and ACTIVEIND = #{activeind}
		</if>
		<if test="commencedate != null and commencedate != ''" >
			and COMMENCEDATE = #{commencedate}
		</if>
		<if test="email != null and email != ''" >
			and EMAIL = #{email}
		</if>
		<if test="district != null and district != ''" >
			and DISTRICT = #{district}
		</if>
		<if test="ipaservice != null and ipaservice != ''" >
			and IPASERVICE = #{ipaservice}
		</if>
		<if test="countryCode != null and countryCode != ''" >
			and COUNTRYCODE = #{countryCode}
		</if>
		<if test="registaddress != null and registaddress != ''" >
			and REGISTADDRESS = #{registaddress}
		</if>
		<if test="memberrefrence != null and memberrefrence != ''" >
			and MEMBERREFRENCE = #{memberrefrence}
		</if>
		<if test="discount != null and discount != ''" >
			and DISCOUNT = #{discount}
		</if>
		<if test="specialclause != null and specialclause != ''" >
			and SPECIALCLAUSE = #{specialclause}
		</if>
		<if test="endorind != null and endorind != ''" >
			and ENDORIND = #{endorind}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="journeystart != null and journeystart != ''" >
			and JOURNEYSTART = #{journeystart}
		</if>
		<if test="journeyend != null and journeyend != ''" >
			and JOURNEYEND = #{journeyend}
		</if>
		<if test="journeyback != null and journeyback != ''" >
			and JOURNEYBACK = #{journeyback}
		</if>
		<if test="applirelation != null and applirelation != ''" >
			and APPLIRELATION = #{applirelation}
		</if>
		<if test="linkerName != null and linkerName != ''" >
			and LINKERNAME = #{linkerName}
		</if>
		<if test="linkerphone != null and linkerphone != ''" >
			and LINKERPHONE = #{linkerphone}
		</if>
		<if test="innerremark != null and innerremark != ''" >
			and INNERREMARK = #{innerremark}
		</if>
		<if test="pledged != null and pledged != ''" >
			and PLEDGED = #{pledged}
		</if>
		<if test="claimpayway != null and claimpayway != ''" >
			and CLAIMPAYWAY = #{claimpayway}
		</if>
		<if test="clienttype != null and clienttype != ''" >
			and CLIENTTYPE = #{clienttype}
		</if>
		<if test="occupationtypename != null and occupationtypename != ''" >
			and OCCUPATIONTYPENAME = #{occupationtypename}
		</if>
		<if test="occupationlevel != null and occupationlevel != ''" >
			and OCCUPATIONLEVEL = #{occupationlevel}
		</if>
		<if test="maininsuredind != null and maininsuredind != ''" >
			and MAININSUREDIND = #{maininsuredind}
		</if>
		<if test="plancode != null and plancode != ''" >
			and PLANCODE = #{plancode}
		</if>
		<if test="riskCode != null and riskCode != ''" >
			and RISKCODE = #{riskCode}
		</if>
		<if test="surname != null and surname != ''" >
			and SURNAME = #{surname}
		</if>
		<if test="moniker != null and moniker != ''" >
			and MONIKER = #{moniker}
		</if>
		<if test="firstname != null and firstname != ''" >
			and FIRSTNAME = #{firstname}
		</if>
		<if test="lastname != null and lastname != ''" >
			and LASTNAME = #{lastname}
		</if>
		<if test="provincecode != null and provincecode != ''" >
			and PROVINCECODE = #{provincecode}
		</if>
		<if test="citycode != null and citycode != ''" >
			and CITYCODE = #{citycode}
		</if>
		<if test="countycode != null and countycode != ''" >
			and COUNTYCODE = #{countycode}
		</if>
		<if test="displayNo != null and displayNo != ''" >
			and DISPLAYNO = #{displayNo}
		</if>
		<if test="endorflag != null and endorflag != ''" >
			and ENDORFLAG = #{endorflag}
		</if>
		<if test="groupType != null and groupType != ''" >
			and GROUPTYPE = #{groupType}
		</if>
		<if test="signature != null and signature != ''" >
			and SIGNATURE = #{signature}
		</if>
		<if test="itemAddress != null and itemAddress != ''" >
			and ITEMADDRESS = #{itemAddress}
		</if>
		<if test="useNatureCode != null and useNatureCode != ''" >
			and USENATURECODE = #{useNatureCode}
		</if>
		<if test="drivertype != null and drivertype != ''" >
			and DRIVERTYPE = #{drivertype}
		</if>
		<if test="driverlicensenoold != null and driverlicensenoold != ''" >
			and DRIVERLICENSENOOLD = #{driverlicensenoold}
		</if>
		<if test="driverlicenseexpirationdate != null and driverlicenseexpirationdate != ''" >
			and DRIVERLICENSEEXPIRATIONDATE = #{driverlicenseexpirationdate}
		</if>
		<if test="benefitproject != null and benefitproject != ''" >
			and BENEFITPROJECT = #{benefitproject}
		</if>
		<if test="socialsecurityinfo != null and socialsecurityinfo != ''" >
			and SOCIALSECURITYINFO = #{socialsecurityinfo}
		</if>
		<if test="drivingmodel != null and drivingmodel != ''" >
			and DRIVINGMODEL = #{drivingmodel}
		</if>
		<if test="driverlicenseno != null and driverlicenseno != ''" >
			and DRIVERLICENSENO = #{driverlicenseno}
		</if>
		<if test="onthejobstatus != null and onthejobstatus != ''" >
			and ONTHEJOBSTATUS = #{onthejobstatus}
		</if>
		<if test="platcustomno != null and platcustomno != ''" >
			and PLATCUSTOMNO = #{platcustomno}
		</if>
		<if test="beifendescription != null and beifendescription != ''" >
			and BEIFENDESCRIPTION = #{beifendescription}
		</if>
		<if test="insuredName != null and insuredName != ''" >
			and INSUREDNAME = #{insuredName}
		</if>
		<if test="insuredsex != null and insuredsex != ''" >
			and INSUREDSEX = #{insuredsex}
		</if>
		<if test="insuredbirthday != null and insuredbirthday != ''" >
			and INSUREDBIRTHDAY = #{insuredbirthday}
		</if>
		<if test="identifytypec != null and identifytypec != ''" >
			and IDENTIFYTYPEC = #{identifytypec}
		</if>
		<if test="identifynoc != null and identifynoc != ''" >
			and IDENTIFYNOC = #{identifynoc}
		</if>
		<if test="insuredhomeaddress != null and insuredhomeaddress != ''" >
			and INSUREDHOMEADDRESS = #{insuredhomeaddress}
		</if>
		<if test="insuredhometel != null and insuredhometel != ''" >
			and INSUREDHOMETEL = #{insuredhometel}
		</if>
		<if test="insuredPhone != null and insuredPhone != ''" >
			and INSUREDPHONE = #{insuredPhone}
		</if>
		<if test="insuredemail != null and insuredemail != ''" >
			and INSUREDEMAIL = #{insuredemail}
		</if>
		<if test="insuredclass != null and insuredclass != ''" >
			and INSUREDCLASS = #{insuredclass}
		</if>
		<if test="school != null and school != ''" >
			and SCHOOL = #{school}
		</if>
		<if test="jointinsuredflag != null and jointinsuredflag != ''" >
			and JOINTINSUREDFLAG = #{jointinsuredflag}
		</if>
		<if test="vaccination != null and vaccination != ''" >
			and VACCINATION = #{vaccination}
		</if>
		<if test="inoculabilitytime != null and inoculabilitytime != ''" >
			and INOCULABILITYTIME = #{inoculabilitytime}
		</if>
		<if test="portabletype != null and portabletype != ''" >
			and PORTABLETYPE = #{portabletype}
		</if>
		<if test="brand != null and brand != ''" >
			and BRAND = #{brand}
		</if>
		<if test="model != null and model != ''" >
			and MODEL = #{model}
		</if>
		<if test="uniqueencoding != null and uniqueencoding != ''" >
			and UNIQUEENCODING = #{uniqueencoding}
		</if>
		<if test="uploadtype != null and uploadtype != ''" >
			and UPLOADTYPE = #{uploadtype}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYITEMACCILIST
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYITEMACCILIST
		<trim prefix="WHERE" prefixOverrides="AND">
			AND CLIENTNO = #{clientno} 
			AND ENDORSEQNO = #{endorseqno} 
			AND ITEMNO = #{itemNo} 
			AND PLANCODE = #{plancode} 
			AND POLICYNO = #{policyNo} 
			AND RISKCODE = #{riskCode} 
		</trim> 
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYITEMACCILIST		
		where
		<foreach item="item" index="index" collection="list" open="(" separator="OR" close=")">
			(
			CLIENTNO = #{item.clientno} 
			AND ENDORSEQNO = #{item.endorseqno} 
			AND ITEMNO = #{item.itemNo} 
			AND PLANCODE = #{item.plancode} 
			AND POLICYNO = #{item.policyNo} 
			AND RISKCODE = #{item.riskCode} 
			)
		</foreach> 
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicycopyitemaccilist.po.Gupolicycopyitemaccilist">
		<include refid="Base_Select_By_Entity" />
	</select>
	
	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYCOPYITEMACCILIST 
		<trim prefix="WHERE" prefixOverrides="AND">
			AND CLIENTNO = #{clientno} 
			AND ENDORSEQNO = #{endorseqno} 
			AND ITEMNO = #{itemNo} 
			AND PLANCODE = #{plancode} 
			AND POLICYNO = #{policyNo} 
			AND RISKCODE = #{riskCode} 
		</trim>
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYCOPYITEMACCILIST
		where
		<foreach item="item" index="index" collection="list" open="(" separator="OR" close=")">
			(
			CLIENTNO = #{item.clientno} 
			AND ENDORSEQNO = #{item.endorseqno} 
			AND ITEMNO = #{item.itemNo} 
			AND PLANCODE = #{item.plancode} 
			AND POLICYNO = #{item.policyNo} 
			AND RISKCODE = #{item.riskCode} 
			)
		</foreach> 
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicycopyitemaccilist.po.Gupolicycopyitemaccilist">
		insert into GUPOLICYCOPYITEMACCILIST 
		<trim prefix="(" suffix=")" suffixOverrides="," >
			POLICYNO,
			ENDORSEQNO,
			ITEMNO,
			ITEMCODE,
			ITEMNAME,
			ITEMDETAILNO,
			ITEMDETAILCODE,
			ITEMDETAILNAME,
			CLIENTNO,
			CLIENTCODE,
			MEMBERNO,
			CLIENTCNAME,
			CLIENTENAME,
			SEX,
			BIRTHDAY,
			AGE,
			IDENTIFYTYPEA,
			IDENTIFYNOA,
			IDENTIFYTYPEB,
			IDENTIFYNOB,
			ENROLLMENTDATE,
			STARTDATE,
			ENDDATE,
			BANKNAME,
			BANKACCOUNTNO,
			CREDITNO,
			CREDITEXPIRY,
			OCCUPATIONTYPE,
			OCCUPATION,
			OCCUPATIONCODE,
			JOBTITLE,
			HOMEADDRESS,
			HOMETEL,
			BUSINESSNATURE,
			JOBUNITCODE,
			JOBUNITNAME,
			OFFICETEL,
			EMPLOYERNAME,
			EMPLOYEEIND,
			MATERNITYIND,
			AUTOPAYIND,
			RELATIONCODE,
			RELATIONSHIP,
			PROJECTCODE,
			UWCOUNT,
			SUMINSURED,
			BASEPREMIUM,
			NETPREMIUM,
			GROSSPREMIUM,
			ANNUALPREMIUM,
			PRORATAPREMIUM,
			EXPRIREFUND,
			PREEXISTIND,
			ACTIVEIND,
			COMMENCEDATE,
			EMAIL,
			DISTRICT,
			IPASERVICE,
			COUNTRYCODE,
			REGISTADDRESS,
			MEMBERREFRENCE,
			DISCOUNT,
			SPECIALCLAUSE,
			ENDORIND,
			REMARK,
			FLAG,
			JOURNEYSTART,
			JOURNEYEND,
			JOURNEYBACK,
			APPLIRELATION,
			LINKERNAME,
			LINKERPHONE,
			INNERREMARK,
			PLEDGED,
			CLAIMPAYWAY,
			CLIENTTYPE,
			OCCUPATIONTYPENAME,
			OCCUPATIONLEVEL,
			MAININSUREDIND,
			PLANCODE,
			RISKCODE,
			SURNAME,
			MONIKER,
			FIRSTNAME,
			LASTNAME,
			PROVINCECODE,
			CITYCODE,
			COUNTYCODE,
			DISPLAYNO,
			ENDORFLAG,
			GROUPTYPE,
			SIGNATURE,
			ITEMADDRESS,
			USENATURECODE,
			DRIVERTYPE,
			DRIVERLICENSENOOLD,
			DRIVERLICENSEEXPIRATIONDATE,
			BENEFITPROJECT,
			SOCIALSECURITYINFO,
			DRIVINGMODEL,
			DRIVERLICENSENO,
			ONTHEJOBSTATUS,
			PLATCUSTOMNO,
			BEIFENDESCRIPTION,
			INSUREDNAME,
			INSUREDSEX,
			INSUREDBIRTHDAY,
			IDENTIFYTYPEC,
			IDENTIFYNOC,
			INSUREDHOMEADDRESS,
			INSUREDHOMETEL,
			INSUREDPHONE,
			INSUREDEMAIL,
			INSUREDCLASS,
			SCHOOL,
			JOINTINSUREDFLAG,
			VACCINATION,
			INOCULABILITYTIME,
			PORTABLETYPE,
			BRAND,
			MODEL,
			UNIQUEENCODING,
			UPLOADTYPE
		</trim>
		values 
		<trim prefix="(" suffix=")" suffixOverrides="," > 
			#{policyNo},
			#{endorseqno},
			#{itemNo},
			#{itemCode},
			#{itemName},
			#{itemdetailno},
			#{itemdetailcode},
			#{itemDetailName},
			#{clientno},
			#{clientcode},
			#{memberno},
			#{clientcname},
			#{clientename},
			#{sex},
			#{birthday},
			#{age},
			#{identifytypea},
			#{identifynoa},
			#{identifytypeb},
			#{identifynob},
			#{enrollmentdate},
			#{startDate},
			#{endDate},
			#{bankName},
			#{bankaccountno},
			#{creditNo},
			#{creditexpiry},
			#{occupationtype},
			#{occupation},
			#{occupationCode},
			#{jobTitle},
			#{homeAddress},
			#{hometel},
			#{businessNature},
			#{jobunitcode},
			#{jobunitname},
			#{officetel},
			#{employername},
			#{employeeind},
			#{maternityind},
			#{autopayind},
			#{relationCode},
			#{relationship},
			#{projectcode},
			#{uwcount},
			#{suminsured},
			#{basePremium},
			#{netPremium},
			#{grosspremium},
			#{annualpremium},
			#{proratapremium},
			#{exprirefund},
			#{preexistind},
			#{activeind},
			#{commencedate},
			#{email},
			#{district},
			#{ipaservice},
			#{countryCode},
			#{registaddress},
			#{memberrefrence},
			#{discount},
			#{specialclause},
			#{endorind},
			#{remark},
			#{flag},
			#{journeystart},
			#{journeyend},
			#{journeyback},
			#{applirelation},
			#{linkerName},
			#{linkerphone},
			#{innerremark},
			#{pledged},
			#{claimpayway},
			#{clienttype},
			#{occupationtypename},
			#{occupationlevel},
			#{maininsuredind},
			#{plancode},
			#{riskCode},
			#{surname},
			#{moniker},
			#{firstname},
			#{lastname},
			#{provincecode},
			#{citycode},
			#{countycode},
			#{displayNo},
			#{endorflag},
			#{groupType},
			#{signature},
			#{itemAddress},
			#{useNatureCode},
			#{drivertype},
			#{driverlicensenoold},
			#{driverlicenseexpirationdate},
			#{benefitproject},
			#{socialsecurityinfo},
			#{drivingmodel},
			#{driverlicenseno},
			#{onthejobstatus},
			#{platcustomno},
			#{beifendescription},
			#{insuredName},
			#{insuredsex},
			#{insuredbirthday},
			#{identifytypec},
			#{identifynoc},
			#{insuredhomeaddress},
			#{insuredhometel},
			#{insuredPhone},
			#{insuredemail},
			#{insuredclass},
			#{school},
			#{jointinsuredflag},
			#{vaccination},
			#{inoculabilitytime},
			#{portabletype},
			#{brand},
			#{model},
			#{uniqueencoding},
			#{uploadtype}
		</trim>
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicycopyitemaccilist.po.Gupolicycopyitemaccilist">
		insert into GUPOLICYCOPYITEMACCILIST
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="endorseqno != null" >
				ENDORSEQNO,
			</if>
			<if test="itemNo != null" >
				ITEMNO,
			</if>
			<if test="itemCode != null" >
				ITEMCODE,
			</if>
			<if test="itemName != null" >
				ITEMNAME,
			</if>
			<if test="itemdetailno != null" >
				ITEMDETAILNO,
			</if>
			<if test="itemdetailcode != null" >
				ITEMDETAILCODE,
			</if>
			<if test="itemDetailName != null" >
				ITEMDETAILNAME,
			</if>
			<if test="clientno != null" >
				CLIENTNO,
			</if>
			<if test="clientcode != null" >
				CLIENTCODE,
			</if>
			<if test="memberno != null" >
				MEMBERNO,
			</if>
			<if test="clientcname != null" >
				CLIENTCNAME,
			</if>
			<if test="clientename != null" >
				CLIENTENAME,
			</if>
			<if test="sex != null" >
				SEX,
			</if>
			<if test="birthday != null" >
				BIRTHDAY,
			</if>
			<if test="age != null" >
				AGE,
			</if>
			<if test="identifytypea != null" >
				IDENTIFYTYPEA,
			</if>
			<if test="identifynoa != null" >
				IDENTIFYNOA,
			</if>
			<if test="identifytypeb != null" >
				IDENTIFYTYPEB,
			</if>
			<if test="identifynob != null" >
				IDENTIFYNOB,
			</if>
			<if test="enrollmentdate != null" >
				ENROLLMENTDATE,
			</if>
			<if test="startDate != null" >
				STARTDATE,
			</if>
			<if test="endDate != null" >
				ENDDATE,
			</if>
			<if test="bankName != null" >
				BANKNAME,
			</if>
			<if test="bankaccountno != null" >
				BANKACCOUNTNO,
			</if>
			<if test="creditNo != null" >
				CREDITNO,
			</if>
			<if test="creditexpiry != null" >
				CREDITEXPIRY,
			</if>
			<if test="occupationtype != null" >
				OCCUPATIONTYPE,
			</if>
			<if test="occupation != null" >
				OCCUPATION,
			</if>
			<if test="occupationCode != null" >
				OCCUPATIONCODE,
			</if>
			<if test="jobTitle != null" >
				JOBTITLE,
			</if>
			<if test="homeAddress != null" >
				HOMEADDRESS,
			</if>
			<if test="hometel != null" >
				HOMETEL,
			</if>
			<if test="businessNature != null" >
				BUSINESSNATURE,
			</if>
			<if test="jobunitcode != null" >
				JOBUNITCODE,
			</if>
			<if test="jobunitname != null" >
				JOBUNITNAME,
			</if>
			<if test="officetel != null" >
				OFFICETEL,
			</if>
			<if test="employername != null" >
				EMPLOYERNAME,
			</if>
			<if test="employeeind != null" >
				EMPLOYEEIND,
			</if>
			<if test="maternityind != null" >
				MATERNITYIND,
			</if>
			<if test="autopayind != null" >
				AUTOPAYIND,
			</if>
			<if test="relationCode != null" >
				RELATIONCODE,
			</if>
			<if test="relationship != null" >
				RELATIONSHIP,
			</if>
			<if test="projectcode != null" >
				PROJECTCODE,
			</if>
			<if test="uwcount != null" >
				UWCOUNT,
			</if>
			<if test="suminsured != null" >
				SUMINSURED,
			</if>
			<if test="basePremium != null" >
				BASEPREMIUM,
			</if>
			<if test="netPremium != null" >
				NETPREMIUM,
			</if>
			<if test="grosspremium != null" >
				GROSSPREMIUM,
			</if>
			<if test="annualpremium != null" >
				ANNUALPREMIUM,
			</if>
			<if test="proratapremium != null" >
				PRORATAPREMIUM,
			</if>
			<if test="exprirefund != null" >
				EXPRIREFUND,
			</if>
			<if test="preexistind != null" >
				PREEXISTIND,
			</if>
			<if test="activeind != null" >
				ACTIVEIND,
			</if>
			<if test="commencedate != null" >
				COMMENCEDATE,
			</if>
			<if test="email != null" >
				EMAIL,
			</if>
			<if test="district != null" >
				DISTRICT,
			</if>
			<if test="ipaservice != null" >
				IPASERVICE,
			</if>
			<if test="countryCode != null" >
				COUNTRYCODE,
			</if>
			<if test="registaddress != null" >
				REGISTADDRESS,
			</if>
			<if test="memberrefrence != null" >
				MEMBERREFRENCE,
			</if>
			<if test="discount != null" >
				DISCOUNT,
			</if>
			<if test="specialclause != null" >
				SPECIALCLAUSE,
			</if>
			<if test="endorind != null" >
				ENDORIND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="journeystart != null" >
				JOURNEYSTART,
			</if>
			<if test="journeyend != null" >
				JOURNEYEND,
			</if>
			<if test="journeyback != null" >
				JOURNEYBACK,
			</if>
			<if test="applirelation != null" >
				APPLIRELATION,
			</if>
			<if test="linkerName != null" >
				LINKERNAME,
			</if>
			<if test="linkerphone != null" >
				LINKERPHONE,
			</if>
			<if test="innerremark != null" >
				INNERREMARK,
			</if>
			<if test="pledged != null" >
				PLEDGED,
			</if>
			<if test="claimpayway != null" >
				CLAIMPAYWAY,
			</if>
			<if test="clienttype != null" >
				CLIENTTYPE,
			</if>
			<if test="occupationtypename != null" >
				OCCUPATIONTYPENAME,
			</if>
			<if test="occupationlevel != null" >
				OCCUPATIONLEVEL,
			</if>
			<if test="maininsuredind != null" >
				MAININSUREDIND,
			</if>
			<if test="plancode != null" >
				PLANCODE,
			</if>
			<if test="riskCode != null" >
				RISKCODE,
			</if>
			<if test="surname != null" >
				SURNAME,
			</if>
			<if test="moniker != null" >
				MONIKER,
			</if>
			<if test="firstname != null" >
				FIRSTNAME,
			</if>
			<if test="lastname != null" >
				LASTNAME,
			</if>
			<if test="provincecode != null" >
				PROVINCECODE,
			</if>
			<if test="citycode != null" >
				CITYCODE,
			</if>
			<if test="countycode != null" >
				COUNTYCODE,
			</if>
			<if test="displayNo != null" >
				DISPLAYNO,
			</if>
			<if test="endorflag != null" >
				ENDORFLAG,
			</if>
			<if test="groupType != null" >
				GROUPTYPE,
			</if>
			<if test="signature != null" >
				SIGNATURE,
			</if>
			<if test="itemAddress != null" >
				ITEMADDRESS,
			</if>
			<if test="useNatureCode != null" >
				USENATURECODE,
			</if>
			<if test="drivertype != null" >
				DRIVERTYPE,
			</if>
			<if test="driverlicensenoold != null" >
				DRIVERLICENSENOOLD,
			</if>
			<if test="driverlicenseexpirationdate != null" >
				DRIVERLICENSEEXPIRATIONDATE,
			</if>
			<if test="benefitproject != null" >
				BENEFITPROJECT,
			</if>
			<if test="socialsecurityinfo != null" >
				SOCIALSECURITYINFO,
			</if>
			<if test="drivingmodel != null" >
				DRIVINGMODEL,
			</if>
			<if test="driverlicenseno != null" >
				DRIVERLICENSENO,
			</if>
			<if test="onthejobstatus != null" >
				ONTHEJOBSTATUS,
			</if>
			<if test="platcustomno != null" >
				PLATCUSTOMNO,
			</if>
			<if test="beifendescription != null" >
				BEIFENDESCRIPTION,
			</if>
			<if test="insuredName != null" >
				INSUREDNAME,
			</if>
			<if test="insuredsex != null" >
				INSUREDSEX,
			</if>
			<if test="insuredbirthday != null" >
				INSUREDBIRTHDAY,
			</if>
			<if test="identifytypec != null" >
				IDENTIFYTYPEC,
			</if>
			<if test="identifynoc != null" >
				IDENTIFYNOC,
			</if>
			<if test="insuredhomeaddress != null" >
				INSUREDHOMEADDRESS,
			</if>
			<if test="insuredhometel != null" >
				INSUREDHOMETEL,
			</if>
			<if test="insuredPhone != null" >
				INSUREDPHONE,
			</if>
			<if test="insuredemail != null" >
				INSUREDEMAIL,
			</if>
			<if test="insuredclass != null" >
				INSUREDCLASS,
			</if>
			<if test="school != null" >
				SCHOOL,
			</if>
			<if test="jointinsuredflag != null" >
				JOINTINSUREDFLAG,
			</if>
			<if test="vaccination != null" >
				VACCINATION,
			</if>
			<if test="inoculabilitytime != null" >
				INOCULABILITYTIME,
			</if>
			<if test="portabletype != null" >
				PORTABLETYPE,
			</if>
			<if test="brand != null" >
				BRAND,
			</if>
			<if test="model != null" >
				MODEL,
			</if>
			<if test="uniqueencoding != null" >
				UNIQUEENCODING,
			</if>
			<if test="uploadtype != null" >
				UPLOADTYPE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="endorseqno != null" >
				#{endorseqno},
			</if>
			<if test="itemNo != null" >
				#{itemNo},
			</if>
			<if test="itemCode != null" >
				#{itemCode},
			</if>
			<if test="itemName != null" >
				#{itemName},
			</if>
			<if test="itemdetailno != null" >
				#{itemdetailno},
			</if>
			<if test="itemdetailcode != null" >
				#{itemdetailcode},
			</if>
			<if test="itemDetailName != null" >
				#{itemDetailName},
			</if>
			<if test="clientno != null" >
				#{clientno},
			</if>
			<if test="clientcode != null" >
				#{clientcode},
			</if>
			<if test="memberno != null" >
				#{memberno},
			</if>
			<if test="clientcname != null" >
				#{clientcname},
			</if>
			<if test="clientename != null" >
				#{clientename},
			</if>
			<if test="sex != null" >
				#{sex},
			</if>
			<if test="birthday != null" >
				#{birthday},
			</if>
			<if test="age != null" >
				#{age},
			</if>
			<if test="identifytypea != null" >
				#{identifytypea},
			</if>
			<if test="identifynoa != null" >
				#{identifynoa},
			</if>
			<if test="identifytypeb != null" >
				#{identifytypeb},
			</if>
			<if test="identifynob != null" >
				#{identifynob},
			</if>
			<if test="enrollmentdate != null" >
				#{enrollmentdate},
			</if>
			<if test="startDate != null" >
				#{startDate},
			</if>
			<if test="endDate != null" >
				#{endDate},
			</if>
			<if test="bankName != null" >
				#{bankName},
			</if>
			<if test="bankaccountno != null" >
				#{bankaccountno},
			</if>
			<if test="creditNo != null" >
				#{creditNo},
			</if>
			<if test="creditexpiry != null" >
				#{creditexpiry},
			</if>
			<if test="occupationtype != null" >
				#{occupationtype},
			</if>
			<if test="occupation != null" >
				#{occupation},
			</if>
			<if test="occupationCode != null" >
				#{occupationCode},
			</if>
			<if test="jobTitle != null" >
				#{jobTitle},
			</if>
			<if test="homeAddress != null" >
				#{homeAddress},
			</if>
			<if test="hometel != null" >
				#{hometel},
			</if>
			<if test="businessNature != null" >
				#{businessNature},
			</if>
			<if test="jobunitcode != null" >
				#{jobunitcode},
			</if>
			<if test="jobunitname != null" >
				#{jobunitname},
			</if>
			<if test="officetel != null" >
				#{officetel},
			</if>
			<if test="employername != null" >
				#{employername},
			</if>
			<if test="employeeind != null" >
				#{employeeind},
			</if>
			<if test="maternityind != null" >
				#{maternityind},
			</if>
			<if test="autopayind != null" >
				#{autopayind},
			</if>
			<if test="relationCode != null" >
				#{relationCode},
			</if>
			<if test="relationship != null" >
				#{relationship},
			</if>
			<if test="projectcode != null" >
				#{projectcode},
			</if>
			<if test="uwcount != null" >
				#{uwcount},
			</if>
			<if test="suminsured != null" >
				#{suminsured},
			</if>
			<if test="basePremium != null" >
				#{basePremium},
			</if>
			<if test="netPremium != null" >
				#{netPremium},
			</if>
			<if test="grosspremium != null" >
				#{grosspremium},
			</if>
			<if test="annualpremium != null" >
				#{annualpremium},
			</if>
			<if test="proratapremium != null" >
				#{proratapremium},
			</if>
			<if test="exprirefund != null" >
				#{exprirefund},
			</if>
			<if test="preexistind != null" >
				#{preexistind},
			</if>
			<if test="activeind != null" >
				#{activeind},
			</if>
			<if test="commencedate != null" >
				#{commencedate},
			</if>
			<if test="email != null" >
				#{email},
			</if>
			<if test="district != null" >
				#{district},
			</if>
			<if test="ipaservice != null" >
				#{ipaservice},
			</if>
			<if test="countryCode != null" >
				#{countryCode},
			</if>
			<if test="registaddress != null" >
				#{registaddress},
			</if>
			<if test="memberrefrence != null" >
				#{memberrefrence},
			</if>
			<if test="discount != null" >
				#{discount},
			</if>
			<if test="specialclause != null" >
				#{specialclause},
			</if>
			<if test="endorind != null" >
				#{endorind},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="journeystart != null" >
				#{journeystart},
			</if>
			<if test="journeyend != null" >
				#{journeyend},
			</if>
			<if test="journeyback != null" >
				#{journeyback},
			</if>
			<if test="applirelation != null" >
				#{applirelation},
			</if>
			<if test="linkerName != null" >
				#{linkerName},
			</if>
			<if test="linkerphone != null" >
				#{linkerphone},
			</if>
			<if test="innerremark != null" >
				#{innerremark},
			</if>
			<if test="pledged != null" >
				#{pledged},
			</if>
			<if test="claimpayway != null" >
				#{claimpayway},
			</if>
			<if test="clienttype != null" >
				#{clienttype},
			</if>
			<if test="occupationtypename != null" >
				#{occupationtypename},
			</if>
			<if test="occupationlevel != null" >
				#{occupationlevel},
			</if>
			<if test="maininsuredind != null" >
				#{maininsuredind},
			</if>
			<if test="plancode != null" >
				#{plancode},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="surname != null" >
				#{surname},
			</if>
			<if test="moniker != null" >
				#{moniker},
			</if>
			<if test="firstname != null" >
				#{firstname},
			</if>
			<if test="lastname != null" >
				#{lastname},
			</if>
			<if test="provincecode != null" >
				#{provincecode},
			</if>
			<if test="citycode != null" >
				#{citycode},
			</if>
			<if test="countycode != null" >
				#{countycode},
			</if>
			<if test="displayNo != null" >
				#{displayNo},
			</if>
			<if test="endorflag != null" >
				#{endorflag},
			</if>
			<if test="groupType != null" >
				#{groupType},
			</if>
			<if test="signature != null" >
				#{signature},
			</if>
			<if test="itemAddress != null" >
				#{itemAddress},
			</if>
			<if test="useNatureCode != null" >
				#{useNatureCode},
			</if>
			<if test="drivertype != null" >
				#{drivertype},
			</if>
			<if test="driverlicensenoold != null" >
				#{driverlicensenoold},
			</if>
			<if test="driverlicenseexpirationdate != null" >
				#{driverlicenseexpirationdate},
			</if>
			<if test="benefitproject != null" >
				#{benefitproject},
			</if>
			<if test="socialsecurityinfo != null" >
				#{socialsecurityinfo},
			</if>
			<if test="drivingmodel != null" >
				#{drivingmodel},
			</if>
			<if test="driverlicenseno != null" >
				#{driverlicenseno},
			</if>
			<if test="onthejobstatus != null" >
				#{onthejobstatus},
			</if>
			<if test="platcustomno != null" >
				#{platcustomno},
			</if>
			<if test="beifendescription != null" >
				#{beifendescription},
			</if>
			<if test="insuredName != null" >
				#{insuredName},
			</if>
			<if test="insuredsex != null" >
				#{insuredsex},
			</if>
			<if test="insuredbirthday != null" >
				#{insuredbirthday},
			</if>
			<if test="identifytypec != null" >
				#{identifytypec},
			</if>
			<if test="identifynoc != null" >
				#{identifynoc},
			</if>
			<if test="insuredhomeaddress != null" >
				#{insuredhomeaddress},
			</if>
			<if test="insuredhometel != null" >
				#{insuredhometel},
			</if>
			<if test="insuredPhone != null" >
				#{insuredPhone},
			</if>
			<if test="insuredemail != null" >
				#{insuredemail},
			</if>
			<if test="insuredclass != null" >
				#{insuredclass},
			</if>
			<if test="school != null" >
				#{school},
			</if>
			<if test="jointinsuredflag != null" >
				#{jointinsuredflag},
			</if>
			<if test="vaccination != null" >
				#{vaccination},
			</if>
			<if test="inoculabilitytime != null" >
				#{inoculabilitytime},
			</if>
			<if test="portabletype != null" >
				#{portabletype},
			</if>
			<if test="brand != null" >
				#{brand},
			</if>
			<if test="model != null" >
				#{model},
			</if>
			<if test="uniqueencoding != null" >
				#{uniqueencoding},
			</if>
			<if test="uploadtype != null" >
				#{uploadtype}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicycopyitemaccilist.po.Gupolicycopyitemaccilist">
		update GUPOLICYCOPYITEMACCILIST 
		<set>			
			<if test="itemCode != null" >
				ITEMCODE=#{itemCode},
			</if>
			<if test="itemName != null" >
				ITEMNAME=#{itemName},
			</if>
			<if test="itemdetailno != null" >
				ITEMDETAILNO=#{itemdetailno},
			</if>
			<if test="itemdetailcode != null" >
				ITEMDETAILCODE=#{itemdetailcode},
			</if>
			<if test="itemDetailName != null" >
				ITEMDETAILNAME=#{itemDetailName},
			</if>
			<if test="clientcode != null" >
				CLIENTCODE=#{clientcode},
			</if>
			<if test="memberno != null" >
				MEMBERNO=#{memberno},
			</if>
			<if test="clientcname != null" >
				CLIENTCNAME=#{clientcname},
			</if>
			<if test="clientename != null" >
				CLIENTENAME=#{clientename},
			</if>
			<if test="sex != null" >
				SEX=#{sex},
			</if>
			<if test="birthday != null" >
				BIRTHDAY=#{birthday},
			</if>
			<if test="age != null" >
				AGE=#{age},
			</if>
			<if test="identifytypea != null" >
				IDENTIFYTYPEA=#{identifytypea},
			</if>
			<if test="identifynoa != null" >
				IDENTIFYNOA=#{identifynoa},
			</if>
			<if test="identifytypeb != null" >
				IDENTIFYTYPEB=#{identifytypeb},
			</if>
			<if test="identifynob != null" >
				IDENTIFYNOB=#{identifynob},
			</if>
			<if test="enrollmentdate != null" >
				ENROLLMENTDATE=#{enrollmentdate},
			</if>
			<if test="startDate != null" >
				STARTDATE=#{startDate},
			</if>
			<if test="endDate != null" >
				ENDDATE=#{endDate},
			</if>
			<if test="bankName != null" >
				BANKNAME=#{bankName},
			</if>
			<if test="bankaccountno != null" >
				BANKACCOUNTNO=#{bankaccountno},
			</if>
			<if test="creditNo != null" >
				CREDITNO=#{creditNo},
			</if>
			<if test="creditexpiry != null" >
				CREDITEXPIRY=#{creditexpiry},
			</if>
			<if test="occupationtype != null" >
				OCCUPATIONTYPE=#{occupationtype},
			</if>
			<if test="occupation != null" >
				OCCUPATION=#{occupation},
			</if>
			<if test="occupationCode != null" >
				OCCUPATIONCODE=#{occupationCode},
			</if>
			<if test="jobTitle != null" >
				JOBTITLE=#{jobTitle},
			</if>
			<if test="homeAddress != null" >
				HOMEADDRESS=#{homeAddress},
			</if>
			<if test="hometel != null" >
				HOMETEL=#{hometel},
			</if>
			<if test="businessNature != null" >
				BUSINESSNATURE=#{businessNature},
			</if>
			<if test="jobunitcode != null" >
				JOBUNITCODE=#{jobunitcode},
			</if>
			<if test="jobunitname != null" >
				JOBUNITNAME=#{jobunitname},
			</if>
			<if test="officetel != null" >
				OFFICETEL=#{officetel},
			</if>
			<if test="employername != null" >
				EMPLOYERNAME=#{employername},
			</if>
			<if test="employeeind != null" >
				EMPLOYEEIND=#{employeeind},
			</if>
			<if test="maternityind != null" >
				MATERNITYIND=#{maternityind},
			</if>
			<if test="autopayind != null" >
				AUTOPAYIND=#{autopayind},
			</if>
			<if test="relationCode != null" >
				RELATIONCODE=#{relationCode},
			</if>
			<if test="relationship != null" >
				RELATIONSHIP=#{relationship},
			</if>
			<if test="projectcode != null" >
				PROJECTCODE=#{projectcode},
			</if>
			<if test="uwcount != null" >
				UWCOUNT=#{uwcount},
			</if>
			<if test="suminsured != null" >
				SUMINSURED=#{suminsured},
			</if>
			<if test="basePremium != null" >
				BASEPREMIUM=#{basePremium},
			</if>
			<if test="netPremium != null" >
				NETPREMIUM=#{netPremium},
			</if>
			<if test="grosspremium != null" >
				GROSSPREMIUM=#{grosspremium},
			</if>
			<if test="annualpremium != null" >
				ANNUALPREMIUM=#{annualpremium},
			</if>
			<if test="proratapremium != null" >
				PRORATAPREMIUM=#{proratapremium},
			</if>
			<if test="exprirefund != null" >
				EXPRIREFUND=#{exprirefund},
			</if>
			<if test="preexistind != null" >
				PREEXISTIND=#{preexistind},
			</if>
			<if test="activeind != null" >
				ACTIVEIND=#{activeind},
			</if>
			<if test="commencedate != null" >
				COMMENCEDATE=#{commencedate},
			</if>
			<if test="email != null" >
				EMAIL=#{email},
			</if>
			<if test="district != null" >
				DISTRICT=#{district},
			</if>
			<if test="ipaservice != null" >
				IPASERVICE=#{ipaservice},
			</if>
			<if test="countryCode != null" >
				COUNTRYCODE=#{countryCode},
			</if>
			<if test="registaddress != null" >
				REGISTADDRESS=#{registaddress},
			</if>
			<if test="memberrefrence != null" >
				MEMBERREFRENCE=#{memberrefrence},
			</if>
			<if test="discount != null" >
				DISCOUNT=#{discount},
			</if>
			<if test="specialclause != null" >
				SPECIALCLAUSE=#{specialclause},
			</if>
			<if test="endorind != null" >
				ENDORIND=#{endorind},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="journeystart != null" >
				JOURNEYSTART=#{journeystart},
			</if>
			<if test="journeyend != null" >
				JOURNEYEND=#{journeyend},
			</if>
			<if test="journeyback != null" >
				JOURNEYBACK=#{journeyback},
			</if>
			<if test="applirelation != null" >
				APPLIRELATION=#{applirelation},
			</if>
			<if test="linkerName != null" >
				LINKERNAME=#{linkerName},
			</if>
			<if test="linkerphone != null" >
				LINKERPHONE=#{linkerphone},
			</if>
			<if test="innerremark != null" >
				INNERREMARK=#{innerremark},
			</if>
			<if test="pledged != null" >
				PLEDGED=#{pledged},
			</if>
			<if test="claimpayway != null" >
				CLAIMPAYWAY=#{claimpayway},
			</if>
			<if test="clienttype != null" >
				CLIENTTYPE=#{clienttype},
			</if>
			<if test="occupationtypename != null" >
				OCCUPATIONTYPENAME=#{occupationtypename},
			</if>
			<if test="occupationlevel != null" >
				OCCUPATIONLEVEL=#{occupationlevel},
			</if>
			<if test="maininsuredind != null" >
				MAININSUREDIND=#{maininsuredind},
			</if>
			<if test="surname != null" >
				SURNAME=#{surname},
			</if>
			<if test="moniker != null" >
				MONIKER=#{moniker},
			</if>
			<if test="firstname != null" >
				FIRSTNAME=#{firstname},
			</if>
			<if test="lastname != null" >
				LASTNAME=#{lastname},
			</if>
			<if test="provincecode != null" >
				PROVINCECODE=#{provincecode},
			</if>
			<if test="citycode != null" >
				CITYCODE=#{citycode},
			</if>
			<if test="countycode != null" >
				COUNTYCODE=#{countycode},
			</if>
			<if test="displayNo != null" >
				DISPLAYNO=#{displayNo},
			</if>
			<if test="endorflag != null" >
				ENDORFLAG=#{endorflag},
			</if>
			<if test="groupType != null" >
				GROUPTYPE=#{groupType},
			</if>
			<if test="signature != null" >
				SIGNATURE=#{signature},
			</if>
			<if test="itemAddress != null" >
				ITEMADDRESS=#{itemAddress},
			</if>
			<if test="useNatureCode != null" >
				USENATURECODE=#{useNatureCode},
			</if>
			<if test="drivertype != null" >
				DRIVERTYPE=#{drivertype},
			</if>
			<if test="driverlicensenoold != null" >
				DRIVERLICENSENOOLD=#{driverlicensenoold},
			</if>
			<if test="driverlicenseexpirationdate != null" >
				DRIVERLICENSEEXPIRATIONDATE=#{driverlicenseexpirationdate},
			</if>
			<if test="benefitproject != null" >
				BENEFITPROJECT=#{benefitproject},
			</if>
			<if test="socialsecurityinfo != null" >
				SOCIALSECURITYINFO=#{socialsecurityinfo},
			</if>
			<if test="drivingmodel != null" >
				DRIVINGMODEL=#{drivingmodel},
			</if>
			<if test="driverlicenseno != null" >
				DRIVERLICENSENO=#{driverlicenseno},
			</if>
			<if test="onthejobstatus != null" >
				ONTHEJOBSTATUS=#{onthejobstatus},
			</if>
			<if test="platcustomno != null" >
				PLATCUSTOMNO=#{platcustomno},
			</if>
			<if test="beifendescription != null" >
				BEIFENDESCRIPTION=#{beifendescription},
			</if>
			<if test="insuredName != null" >
				INSUREDNAME=#{insuredName},
			</if>
			<if test="insuredsex != null" >
				INSUREDSEX=#{insuredsex},
			</if>
			<if test="insuredbirthday != null" >
				INSUREDBIRTHDAY=#{insuredbirthday},
			</if>
			<if test="identifytypec != null" >
				IDENTIFYTYPEC=#{identifytypec},
			</if>
			<if test="identifynoc != null" >
				IDENTIFYNOC=#{identifynoc},
			</if>
			<if test="insuredhomeaddress != null" >
				INSUREDHOMEADDRESS=#{insuredhomeaddress},
			</if>
			<if test="insuredhometel != null" >
				INSUREDHOMETEL=#{insuredhometel},
			</if>
			<if test="insuredPhone != null" >
				INSUREDPHONE=#{insuredPhone},
			</if>
			<if test="insuredemail != null" >
				INSUREDEMAIL=#{insuredemail},
			</if>
			<if test="insuredclass != null" >
				INSUREDCLASS=#{insuredclass},
			</if>
			<if test="school != null" >
				SCHOOL=#{school},
			</if>
			<if test="jointinsuredflag != null" >
				JOINTINSUREDFLAG=#{jointinsuredflag},
			</if>
			<if test="vaccination != null" >
				VACCINATION=#{vaccination},
			</if>
			<if test="inoculabilitytime != null" >
				INOCULABILITYTIME=#{inoculabilitytime},
			</if>
			<if test="portabletype != null" >
				PORTABLETYPE=#{portabletype},
			</if>
			<if test="brand != null" >
				BRAND=#{brand},
			</if>
			<if test="model != null" >
				MODEL=#{model},
			</if>
			<if test="uniqueencoding != null" >
				UNIQUEENCODING=#{uniqueencoding},
			</if>
			<if test="uploadtype != null" >
				UPLOADTYPE=#{uploadtype},
			</if>
		</set>
		<trim prefix="WHERE" prefixOverrides="AND">
			AND CLIENTNO = #{clientno} 
			AND ENDORSEQNO = #{endorseqno} 
			AND ITEMNO = #{itemNo} 
			AND PLANCODE = #{plancode} 
			AND POLICYNO = #{policyNo} 
			AND RISKCODE = #{riskCode} 
		</trim> 
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicycopyitemaccilist.po.Gupolicycopyitemaccilist">
		update GUPOLICYCOPYITEMACCILIST 
		<set>
			ITEMCODE=#{itemCode},
			ITEMNAME=#{itemName},
			ITEMDETAILNO=#{itemdetailno},
			ITEMDETAILCODE=#{itemdetailcode},
			ITEMDETAILNAME=#{itemDetailName},
			CLIENTCODE=#{clientcode},
			MEMBERNO=#{memberno},
			CLIENTCNAME=#{clientcname},
			CLIENTENAME=#{clientename},
			SEX=#{sex},
			BIRTHDAY=#{birthday},
			AGE=#{age},
			IDENTIFYTYPEA=#{identifytypea},
			IDENTIFYNOA=#{identifynoa},
			IDENTIFYTYPEB=#{identifytypeb},
			IDENTIFYNOB=#{identifynob},
			ENROLLMENTDATE=#{enrollmentdate},
			STARTDATE=#{startDate},
			ENDDATE=#{endDate},
			BANKNAME=#{bankName},
			BANKACCOUNTNO=#{bankaccountno},
			CREDITNO=#{creditNo},
			CREDITEXPIRY=#{creditexpiry},
			OCCUPATIONTYPE=#{occupationtype},
			OCCUPATION=#{occupation},
			OCCUPATIONCODE=#{occupationCode},
			JOBTITLE=#{jobTitle},
			HOMEADDRESS=#{homeAddress},
			HOMETEL=#{hometel},
			BUSINESSNATURE=#{businessNature},
			JOBUNITCODE=#{jobunitcode},
			JOBUNITNAME=#{jobunitname},
			OFFICETEL=#{officetel},
			EMPLOYERNAME=#{employername},
			EMPLOYEEIND=#{employeeind},
			MATERNITYIND=#{maternityind},
			AUTOPAYIND=#{autopayind},
			RELATIONCODE=#{relationCode},
			RELATIONSHIP=#{relationship},
			PROJECTCODE=#{projectcode},
			UWCOUNT=#{uwcount},
			SUMINSURED=#{suminsured},
			BASEPREMIUM=#{basePremium},
			NETPREMIUM=#{netPremium},
			GROSSPREMIUM=#{grosspremium},
			ANNUALPREMIUM=#{annualpremium},
			PRORATAPREMIUM=#{proratapremium},
			EXPRIREFUND=#{exprirefund},
			PREEXISTIND=#{preexistind},
			ACTIVEIND=#{activeind},
			COMMENCEDATE=#{commencedate},
			EMAIL=#{email},
			DISTRICT=#{district},
			IPASERVICE=#{ipaservice},
			COUNTRYCODE=#{countryCode},
			REGISTADDRESS=#{registaddress},
			MEMBERREFRENCE=#{memberrefrence},
			DISCOUNT=#{discount},
			SPECIALCLAUSE=#{specialclause},
			ENDORIND=#{endorind},
			REMARK=#{remark},
			FLAG=#{flag},
			JOURNEYSTART=#{journeystart},
			JOURNEYEND=#{journeyend},
			JOURNEYBACK=#{journeyback},
			APPLIRELATION=#{applirelation},
			LINKERNAME=#{linkerName},
			LINKERPHONE=#{linkerphone},
			INNERREMARK=#{innerremark},
			PLEDGED=#{pledged},
			CLAIMPAYWAY=#{claimpayway},
			CLIENTTYPE=#{clienttype},
			OCCUPATIONTYPENAME=#{occupationtypename},
			OCCUPATIONLEVEL=#{occupationlevel},
			MAININSUREDIND=#{maininsuredind},
			SURNAME=#{surname},
			MONIKER=#{moniker},
			FIRSTNAME=#{firstname},
			LASTNAME=#{lastname},
			PROVINCECODE=#{provincecode},
			CITYCODE=#{citycode},
			COUNTYCODE=#{countycode},
			DISPLAYNO=#{displayNo},
			ENDORFLAG=#{endorflag},
			GROUPTYPE=#{groupType},
			SIGNATURE=#{signature},
			ITEMADDRESS=#{itemAddress},
			USENATURECODE=#{useNatureCode},
			DRIVERTYPE=#{drivertype},
			DRIVERLICENSENOOLD=#{driverlicensenoold},
			DRIVERLICENSEEXPIRATIONDATE=#{driverlicenseexpirationdate},
			BENEFITPROJECT=#{benefitproject},
			SOCIALSECURITYINFO=#{socialsecurityinfo},
			DRIVINGMODEL=#{drivingmodel},
			DRIVERLICENSENO=#{driverlicenseno},
			ONTHEJOBSTATUS=#{onthejobstatus},
			PLATCUSTOMNO=#{platcustomno},
			BEIFENDESCRIPTION=#{beifendescription},
			INSUREDNAME=#{insuredName},
			INSUREDSEX=#{insuredsex},
			INSUREDBIRTHDAY=#{insuredbirthday},
			IDENTIFYTYPEC=#{identifytypec},
			IDENTIFYNOC=#{identifynoc},
			INSUREDHOMEADDRESS=#{insuredhomeaddress},
			INSUREDHOMETEL=#{insuredhometel},
			INSUREDPHONE=#{insuredPhone},
			INSUREDEMAIL=#{insuredemail},
			INSUREDCLASS=#{insuredclass},
			SCHOOL=#{school},
			JOINTINSUREDFLAG=#{jointinsuredflag},
			VACCINATION=#{vaccination},
			INOCULABILITYTIME=#{inoculabilitytime},
			PORTABLETYPE=#{portabletype},
			BRAND=#{brand},
			MODEL=#{model},
			UNIQUEENCODING=#{uniqueencoding},
			UPLOADTYPE=#{uploadtype},
		</set>
		<trim prefix="WHERE" prefixOverrides="AND">
			AND CLIENTNO = #{clientno} 
			AND ENDORSEQNO = #{endorseqno} 
			AND ITEMNO = #{itemNo} 
			AND PLANCODE = #{plancode} 
			AND POLICYNO = #{policyNo} 
			AND RISKCODE = #{riskCode} 
		</trim> 
	</update>
</mapper>
