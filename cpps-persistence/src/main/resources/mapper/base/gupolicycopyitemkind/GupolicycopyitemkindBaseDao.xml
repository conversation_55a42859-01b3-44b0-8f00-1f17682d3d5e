<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicycopyitemkind.dao.GupolicycopyitemkindDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicycopyitemkind.po.Gupolicycopyitemkind">
		<id column="ID" property="id" />
		<result column="POLICYNO" property="policyNo" />
		<result column="ENDORNO" property="endorNo" />
		<result column="SUBPOLICYNO" property="subpolicyno" />
		<result column="PLANCODE" property="plancode" />
		<result column="RISKCODE" property="riskCode" />
		<result column="ITEMKINDNO" property="itemKindNo" />
		<result column="ITEMNO" property="itemNo" />
		<result column="ITEMCODE" property="itemCode" />
		<result column="ITEMDETAILNO" property="itemdetailno" />
		<result column="ITEMDETAILCODE" property="itemdetailcode" />
		<result column="ITEMDETAILLIST" property="itemdetaillist" />
		<result column="PLANID" property="planid" />
		<result column="KINDIND" property="kindind" />
		<result column="KINDCODE" property="kindCode" />
		<result column="KINDNAME" property="kindName" />
		<result column="MODECODE" property="modeCode" />
		<result column="MODENAME" property="modeName" />
		<result column="STARTDATE" property="startDate" />
		<result column="ENDDATE" property="endDate" />
		<result column="CALCULATEIND" property="calculateind" />
		<result column="CURRENCY" property="currency" />
		<result column="UNIT" property="unit" />
		<result column="SUMVALUE" property="sumValue" />
		<result column="SUMINSURED" property="suminsured" />
		<result column="CHANGEINSURED" property="changeinsured" />
		<result column="RATEPERIOD" property="ratePeriod" />
		<result column="RATE" property="rate" />
		<result column="SHORTRATEFLAG" property="shortrateFlag" />
		<result column="SHORTRATE" property="shortRate" />
		<result column="SHORTRATEDENOMINATOR" property="shortratedenominator" />
		<result column="LOADING" property="loading" />
		<result column="GROSSPREMIUM" property="grosspremium" />
		<result column="CHANGEGROSSPREMIUM" property="changegrosspremium" />
		<result column="NOTAXPREMIUM" property="notaxpremium" />
		<result column="TAXAMOUNT" property="taxamount" />
		<result column="CHANGENOTAXPREMIUM" property="changenotaxpremium" />
		<result column="CHANGETAXAMOUNT" property="changetaxamount" />
		<result column="NETPREMIUM" property="netPremium" />
		<result column="CHANGENETPREMIUM" property="changenetpremium" />
		<result column="DEDUCTIBLERATE" property="deductibleRate" />
		<result column="DEDUCTIBLE" property="deductible" />
		<result column="YEARPREMIUM" property="yearpremium" />
		<result column="SURRENDERIND" property="surrenderind" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="LIABCODE" property="liabCode" />
		<result column="UNITPREMIUM" property="unitPremium" />
		<result column="DISCOUNT" property="discount" />
		<result column="QUANTITY" property="quantity" />
		<result column="UWCOUNT" property="uwcount" />
		<result column="UWPREMIUM" property="uwpremium" />
		<result column="CHANGEUWPREMIUM" property="changeuwpremium" />
		<result column="ORIGINUWPREMIUM" property="originuwpremium" />
		<result column="ORIGINGROSSPREMIUM" property="origingrosspremium" />
		<result column="COMMISSION" property="commission" />
		<result column="CHANGECOMMISSION" property="changecommission" />
		<result column="AGENTRATE" property="agentrate" />
		<result column="PUBINSUREDIND" property="pubinsuredind" />
		<result column="SPECIALIND" property="specialind" />
		<result column="MUSTINSUREIND" property="mustinsureind" />
		<result column="OURAMOUNT" property="ouramount" />
		<result column="OURPREMIUM" property="ourpremium" />
		<result column="OURNOTTAXPREMIUM" property="ournottaxpremium" />
		<result column="OURTAXAMOUNT" property="ourtaxamount" />
		<result column="CHANGEOURAMOUNT" property="changeouramount" />
		<result column="CHANGEOURPREMIUM" property="changeourpremium" />
		<result column="CHANGEOURNOTTAXPREMIUM" property="changeournottaxpremium" />
		<result column="CHANGEOURTAXAMOUNT" property="changeourtaxamount" />
		<result column="CURRENCYCNY" property="currencycny" />
		<result column="SUMINSUREDCNY" property="suminsuredcny" />
		<result column="UWPREMIUMCNY" property="uwpremiumcny" />
		<result column="NOTAXPREMIUMCNY" property="notaxpremiumcny" />
		<result column="TAXAMOUNTCNY" property="taxamountcny" />
		<result column="CHANGEINSUREDCNY" property="changeinsuredcny" />
		<result column="CHANGEUWPREMIUMCNY" property="changeuwpremiumcny" />
		<result column="CHANGENOTAXPREMIUMCNY" property="changenotaxpremiumcny" />
		<result column="CHANGETAXAMOUNTCNY" property="changetaxamountcny" />
		<result column="OURAMOUNTCNY" property="ouramountcny" />
		<result column="OURPREMIUMCNY" property="ourpremiumcny" />
		<result column="OURNOTTAXPREMIUMCNY" property="ournottaxpremiumcny" />
		<result column="OURTAXAMOUNTCNY" property="ourtaxamountcny" />
		<result column="CHANGEOURAMOUNTCNY" property="changeouramountcny" />
		<result column="CHANGEOURPREMIUMCNY" property="changeourpremiumcny" />
		<result column="CHANGEOURNOTTAXPREMIUMCNY" property="changeournottaxpremiumcny" />
		<result column="CHANGEOURTAXAMOUNTCNY" property="changeourtaxamountcny" />
		<result column="CLAIMCOUNTLIMIT" property="claimcountlimit" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		POLICYNO,
		ENDORNO,
		SUBPOLICYNO,
		PLANCODE,
		RISKCODE,
		ITEMKINDNO,
		ITEMNO,
		ITEMCODE,
		ITEMDETAILNO,
		ITEMDETAILCODE,
		ITEMDETAILLIST,
		PLANID,
		KINDIND,
		KINDCODE,
		KINDNAME,
		MODECODE,
		MODENAME,
		STARTDATE,
		ENDDATE,
		CALCULATEIND,
		CURRENCY,
		UNIT,
		SUMVALUE,
		SUMINSURED,
		CHANGEINSURED,
		RATEPERIOD,
		RATE,
		SHORTRATEFLAG,
		SHORTRATE,
		SHORTRATEDENOMINATOR,
		LOADING,
		GROSSPREMIUM,
		CHANGEGROSSPREMIUM,
		NOTAXPREMIUM,
		TAXAMOUNT,
		CHANGENOTAXPREMIUM,
		CHANGETAXAMOUNT,
		NETPREMIUM,
		CHANGENETPREMIUM,
		DEDUCTIBLERATE,
		DEDUCTIBLE,
		YEARPREMIUM,
		SURRENDERIND,
		REMARK,
		FLAG,
		LIABCODE,
		UNITPREMIUM,
		DISCOUNT,
		QUANTITY,
		UWCOUNT,
		UWPREMIUM,
		CHANGEUWPREMIUM,
		ORIGINUWPREMIUM,
		ORIGINGROSSPREMIUM,
		COMMISSION,
		CHANGECOMMISSION,
		AGENTRATE,
		PUBINSUREDIND,
		SPECIALIND,
		MUSTINSUREIND,
		OURAMOUNT,
		OURPREMIUM,
		OURNOTTAXPREMIUM,
		OURTAXAMOUNT,
		CHANGEOURAMOUNT,
		CHANGEOURPREMIUM,
		CHANGEOURNOTTAXPREMIUM,
		CHANGEOURTAXAMOUNT,
		CURRENCYCNY,
		SUMINSUREDCNY,
		UWPREMIUMCNY,
		NOTAXPREMIUMCNY,
		TAXAMOUNTCNY,
		CHANGEINSUREDCNY,
		CHANGEUWPREMIUMCNY,
		CHANGENOTAXPREMIUMCNY,
		CHANGETAXAMOUNTCNY,
		OURAMOUNTCNY,
		OURPREMIUMCNY,
		OURNOTTAXPREMIUMCNY,
		OURTAXAMOUNTCNY,
		CHANGEOURAMOUNTCNY,
		CHANGEOURPREMIUMCNY,
		CHANGEOURNOTTAXPREMIUMCNY,
		CHANGEOURTAXAMOUNTCNY,
		CLAIMCOUNTLIMIT,
		INPUTDATE,
		UPDATESYSDATE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="endorNo != null and endorNo != ''" >
			and ENDORNO = #{endorNo}
		</if>
		<if test="subpolicyno != null and subpolicyno != ''" >
			and SUBPOLICYNO = #{subpolicyno}
		</if>
		<if test="plancode != null and plancode != ''" >
			and PLANCODE = #{plancode}
		</if>
		<if test="riskCode != null and riskCode != ''" >
			and RISKCODE = #{riskCode}
		</if>
		<if test="itemKindNo != null and itemKindNo != ''" >
			and ITEMKINDNO = #{itemKindNo}
		</if>
		<if test="itemNo != null and itemNo != ''" >
			and ITEMNO = #{itemNo}
		</if>
		<if test="itemCode != null and itemCode != ''" >
			and ITEMCODE = #{itemCode}
		</if>
		<if test="itemdetailno != null and itemdetailno != ''" >
			and ITEMDETAILNO = #{itemdetailno}
		</if>
		<if test="itemdetailcode != null and itemdetailcode != ''" >
			and ITEMDETAILCODE = #{itemdetailcode}
		</if>
		<if test="itemdetaillist != null and itemdetaillist != ''" >
			and ITEMDETAILLIST = #{itemdetaillist}
		</if>
		<if test="planid != null and planid != ''" >
			and PLANID = #{planid}
		</if>
		<if test="kindind != null and kindind != ''" >
			and KINDIND = #{kindind}
		</if>
		<if test="kindCode != null and kindCode != ''" >
			and KINDCODE = #{kindCode}
		</if>
		<if test="kindName != null and kindName != ''" >
			and KINDNAME = #{kindName}
		</if>
		<if test="modeCode != null and modeCode != ''" >
			and MODECODE = #{modeCode}
		</if>
		<if test="modeName != null and modeName != ''" >
			and MODENAME = #{modeName}
		</if>
		<if test="startDate != null and startDate != ''" >
			and STARTDATE = #{startDate}
		</if>
		<if test="endDate != null and endDate != ''" >
			and ENDDATE = #{endDate}
		</if>
		<if test="calculateind != null and calculateind != ''" >
			and CALCULATEIND = #{calculateind}
		</if>
		<if test="currency != null and currency != ''" >
			and CURRENCY = #{currency}
		</if>
		<if test="unit != null and unit != ''" >
			and UNIT = #{unit}
		</if>
		<if test="sumValue != null and sumValue != ''" >
			and SUMVALUE = #{sumValue}
		</if>
		<if test="suminsured != null and suminsured != ''" >
			and SUMINSURED = #{suminsured}
		</if>
		<if test="changeinsured != null and changeinsured != ''" >
			and CHANGEINSURED = #{changeinsured}
		</if>
		<if test="ratePeriod != null and ratePeriod != ''" >
			and RATEPERIOD = #{ratePeriod}
		</if>
		<if test="rate != null and rate != ''" >
			and RATE = #{rate}
		</if>
		<if test="shortrateFlag != null and shortrateFlag != ''" >
			and SHORTRATEFLAG = #{shortrateFlag}
		</if>
		<if test="shortRate != null and shortRate != ''" >
			and SHORTRATE = #{shortRate}
		</if>
		<if test="shortratedenominator != null and shortratedenominator != ''" >
			and SHORTRATEDENOMINATOR = #{shortratedenominator}
		</if>
		<if test="loading != null and loading != ''" >
			and LOADING = #{loading}
		</if>
		<if test="grosspremium != null and grosspremium != ''" >
			and GROSSPREMIUM = #{grosspremium}
		</if>
		<if test="changegrosspremium != null and changegrosspremium != ''" >
			and CHANGEGROSSPREMIUM = #{changegrosspremium}
		</if>
		<if test="notaxpremium != null and notaxpremium != ''" >
			and NOTAXPREMIUM = #{notaxpremium}
		</if>
		<if test="taxamount != null and taxamount != ''" >
			and TAXAMOUNT = #{taxamount}
		</if>
		<if test="changenotaxpremium != null and changenotaxpremium != ''" >
			and CHANGENOTAXPREMIUM = #{changenotaxpremium}
		</if>
		<if test="changetaxamount != null and changetaxamount != ''" >
			and CHANGETAXAMOUNT = #{changetaxamount}
		</if>
		<if test="netPremium != null and netPremium != ''" >
			and NETPREMIUM = #{netPremium}
		</if>
		<if test="changenetpremium != null and changenetpremium != ''" >
			and CHANGENETPREMIUM = #{changenetpremium}
		</if>
		<if test="deductibleRate != null and deductibleRate != ''" >
			and DEDUCTIBLERATE = #{deductibleRate}
		</if>
		<if test="deductible != null and deductible != ''" >
			and DEDUCTIBLE = #{deductible}
		</if>
		<if test="yearpremium != null and yearpremium != ''" >
			and YEARPREMIUM = #{yearpremium}
		</if>
		<if test="surrenderind != null and surrenderind != ''" >
			and SURRENDERIND = #{surrenderind}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="liabCode != null and liabCode != ''" >
			and LIABCODE = #{liabCode}
		</if>
		<if test="unitPremium != null and unitPremium != ''" >
			and UNITPREMIUM = #{unitPremium}
		</if>
		<if test="discount != null and discount != ''" >
			and DISCOUNT = #{discount}
		</if>
		<if test="quantity != null and quantity != ''" >
			and QUANTITY = #{quantity}
		</if>
		<if test="uwcount != null and uwcount != ''" >
			and UWCOUNT = #{uwcount}
		</if>
		<if test="uwpremium != null and uwpremium != ''" >
			and UWPREMIUM = #{uwpremium}
		</if>
		<if test="changeuwpremium != null and changeuwpremium != ''" >
			and CHANGEUWPREMIUM = #{changeuwpremium}
		</if>
		<if test="originuwpremium != null and originuwpremium != ''" >
			and ORIGINUWPREMIUM = #{originuwpremium}
		</if>
		<if test="origingrosspremium != null and origingrosspremium != ''" >
			and ORIGINGROSSPREMIUM = #{origingrosspremium}
		</if>
		<if test="commission != null and commission != ''" >
			and COMMISSION = #{commission}
		</if>
		<if test="changecommission != null and changecommission != ''" >
			and CHANGECOMMISSION = #{changecommission}
		</if>
		<if test="agentrate != null and agentrate != ''" >
			and AGENTRATE = #{agentrate}
		</if>
		<if test="pubinsuredind != null and pubinsuredind != ''" >
			and PUBINSUREDIND = #{pubinsuredind}
		</if>
		<if test="specialind != null and specialind != ''" >
			and SPECIALIND = #{specialind}
		</if>
		<if test="mustinsureind != null and mustinsureind != ''" >
			and MUSTINSUREIND = #{mustinsureind}
		</if>
		<if test="ouramount != null and ouramount != ''" >
			and OURAMOUNT = #{ouramount}
		</if>
		<if test="ourpremium != null and ourpremium != ''" >
			and OURPREMIUM = #{ourpremium}
		</if>
		<if test="ournottaxpremium != null and ournottaxpremium != ''" >
			and OURNOTTAXPREMIUM = #{ournottaxpremium}
		</if>
		<if test="ourtaxamount != null and ourtaxamount != ''" >
			and OURTAXAMOUNT = #{ourtaxamount}
		</if>
		<if test="changeouramount != null and changeouramount != ''" >
			and CHANGEOURAMOUNT = #{changeouramount}
		</if>
		<if test="changeourpremium != null and changeourpremium != ''" >
			and CHANGEOURPREMIUM = #{changeourpremium}
		</if>
		<if test="changeournottaxpremium != null and changeournottaxpremium != ''" >
			and CHANGEOURNOTTAXPREMIUM = #{changeournottaxpremium}
		</if>
		<if test="changeourtaxamount != null and changeourtaxamount != ''" >
			and CHANGEOURTAXAMOUNT = #{changeourtaxamount}
		</if>
		<if test="currencycny != null and currencycny != ''" >
			and CURRENCYCNY = #{currencycny}
		</if>
		<if test="suminsuredcny != null and suminsuredcny != ''" >
			and SUMINSUREDCNY = #{suminsuredcny}
		</if>
		<if test="uwpremiumcny != null and uwpremiumcny != ''" >
			and UWPREMIUMCNY = #{uwpremiumcny}
		</if>
		<if test="notaxpremiumcny != null and notaxpremiumcny != ''" >
			and NOTAXPREMIUMCNY = #{notaxpremiumcny}
		</if>
		<if test="taxamountcny != null and taxamountcny != ''" >
			and TAXAMOUNTCNY = #{taxamountcny}
		</if>
		<if test="changeinsuredcny != null and changeinsuredcny != ''" >
			and CHANGEINSUREDCNY = #{changeinsuredcny}
		</if>
		<if test="changeuwpremiumcny != null and changeuwpremiumcny != ''" >
			and CHANGEUWPREMIUMCNY = #{changeuwpremiumcny}
		</if>
		<if test="changenotaxpremiumcny != null and changenotaxpremiumcny != ''" >
			and CHANGENOTAXPREMIUMCNY = #{changenotaxpremiumcny}
		</if>
		<if test="changetaxamountcny != null and changetaxamountcny != ''" >
			and CHANGETAXAMOUNTCNY = #{changetaxamountcny}
		</if>
		<if test="ouramountcny != null and ouramountcny != ''" >
			and OURAMOUNTCNY = #{ouramountcny}
		</if>
		<if test="ourpremiumcny != null and ourpremiumcny != ''" >
			and OURPREMIUMCNY = #{ourpremiumcny}
		</if>
		<if test="ournottaxpremiumcny != null and ournottaxpremiumcny != ''" >
			and OURNOTTAXPREMIUMCNY = #{ournottaxpremiumcny}
		</if>
		<if test="ourtaxamountcny != null and ourtaxamountcny != ''" >
			and OURTAXAMOUNTCNY = #{ourtaxamountcny}
		</if>
		<if test="changeouramountcny != null and changeouramountcny != ''" >
			and CHANGEOURAMOUNTCNY = #{changeouramountcny}
		</if>
		<if test="changeourpremiumcny != null and changeourpremiumcny != ''" >
			and CHANGEOURPREMIUMCNY = #{changeourpremiumcny}
		</if>
		<if test="changeournottaxpremiumcny != null and changeournottaxpremiumcny != ''" >
			and CHANGEOURNOTTAXPREMIUMCNY = #{changeournottaxpremiumcny}
		</if>
		<if test="changeourtaxamountcny != null and changeourtaxamountcny != ''" >
			and CHANGEOURTAXAMOUNTCNY = #{changeourtaxamountcny}
		</if>
		<if test="claimcountlimit != null and claimcountlimit != ''" >
			and CLAIMCOUNTLIMIT = #{claimcountlimit}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYITEMKIND
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYITEMKIND
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYITEMKIND
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicycopyitemkind.po.Gupolicycopyitemkind">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYCOPYITEMKIND
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYCOPYITEMKIND
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicycopyitemkind.po.Gupolicycopyitemkind">
		insert into GUPOLICYCOPYITEMKIND (
			ID,
			POLICYNO,
			ENDORNO,
			SUBPOLICYNO,
			PLANCODE,
			RISKCODE,
			ITEMKINDNO,
			ITEMNO,
			ITEMCODE,
			ITEMDETAILNO,
			ITEMDETAILCODE,
			ITEMDETAILLIST,
			PLANID,
			KINDIND,
			KINDCODE,
			KINDNAME,
			MODECODE,
			MODENAME,
			STARTDATE,
			ENDDATE,
			CALCULATEIND,
			CURRENCY,
			UNIT,
			SUMVALUE,
			SUMINSURED,
			CHANGEINSURED,
			RATEPERIOD,
			RATE,
			SHORTRATEFLAG,
			SHORTRATE,
			SHORTRATEDENOMINATOR,
			LOADING,
			GROSSPREMIUM,
			CHANGEGROSSPREMIUM,
			NOTAXPREMIUM,
			TAXAMOUNT,
			CHANGENOTAXPREMIUM,
			CHANGETAXAMOUNT,
			NETPREMIUM,
			CHANGENETPREMIUM,
			DEDUCTIBLERATE,
			DEDUCTIBLE,
			YEARPREMIUM,
			SURRENDERIND,
			REMARK,
			FLAG,
			LIABCODE,
			UNITPREMIUM,
			DISCOUNT,
			QUANTITY,
			UWCOUNT,
			UWPREMIUM,
			CHANGEUWPREMIUM,
			ORIGINUWPREMIUM,
			ORIGINGROSSPREMIUM,
			COMMISSION,
			CHANGECOMMISSION,
			AGENTRATE,
			PUBINSUREDIND,
			SPECIALIND,
			MUSTINSUREIND,
			OURAMOUNT,
			OURPREMIUM,
			OURNOTTAXPREMIUM,
			OURTAXAMOUNT,
			CHANGEOURAMOUNT,
			CHANGEOURPREMIUM,
			CHANGEOURNOTTAXPREMIUM,
			CHANGEOURTAXAMOUNT,
			CURRENCYCNY,
			SUMINSUREDCNY,
			UWPREMIUMCNY,
			NOTAXPREMIUMCNY,
			TAXAMOUNTCNY,
			CHANGEINSUREDCNY,
			CHANGEUWPREMIUMCNY,
			CHANGENOTAXPREMIUMCNY,
			CHANGETAXAMOUNTCNY,
			OURAMOUNTCNY,
			OURPREMIUMCNY,
			OURNOTTAXPREMIUMCNY,
			OURTAXAMOUNTCNY,
			CHANGEOURAMOUNTCNY,
			CHANGEOURPREMIUMCNY,
			CHANGEOURNOTTAXPREMIUMCNY,
			CHANGEOURTAXAMOUNTCNY,
			CLAIMCOUNTLIMIT,
			INPUTDATE,
			UPDATESYSDATE
		) values (
			#{id},
			#{policyNo},
			#{endorNo},
			#{subpolicyno},
			#{plancode},
			#{riskCode},
			#{itemKindNo},
			#{itemNo},
			#{itemCode},
			#{itemdetailno},
			#{itemdetailcode},
			#{itemdetaillist},
			#{planid},
			#{kindind},
			#{kindCode},
			#{kindName},
			#{modeCode},
			#{modeName},
			#{startDate},
			#{endDate},
			#{calculateind},
			#{currency},
			#{unit},
			#{sumValue},
			#{suminsured},
			#{changeinsured},
			#{ratePeriod},
			#{rate},
			#{shortrateFlag},
			#{shortRate},
			#{shortratedenominator},
			#{loading},
			#{grosspremium},
			#{changegrosspremium},
			#{notaxpremium},
			#{taxamount},
			#{changenotaxpremium},
			#{changetaxamount},
			#{netPremium},
			#{changenetpremium},
			#{deductibleRate},
			#{deductible},
			#{yearpremium},
			#{surrenderind},
			#{remark},
			#{flag},
			#{liabCode},
			#{unitPremium},
			#{discount},
			#{quantity},
			#{uwcount},
			#{uwpremium},
			#{changeuwpremium},
			#{originuwpremium},
			#{origingrosspremium},
			#{commission},
			#{changecommission},
			#{agentrate},
			#{pubinsuredind},
			#{specialind},
			#{mustinsureind},
			#{ouramount},
			#{ourpremium},
			#{ournottaxpremium},
			#{ourtaxamount},
			#{changeouramount},
			#{changeourpremium},
			#{changeournottaxpremium},
			#{changeourtaxamount},
			#{currencycny},
			#{suminsuredcny},
			#{uwpremiumcny},
			#{notaxpremiumcny},
			#{taxamountcny},
			#{changeinsuredcny},
			#{changeuwpremiumcny},
			#{changenotaxpremiumcny},
			#{changetaxamountcny},
			#{ouramountcny},
			#{ourpremiumcny},
			#{ournottaxpremiumcny},
			#{ourtaxamountcny},
			#{changeouramountcny},
			#{changeourpremiumcny},
			#{changeournottaxpremiumcny},
			#{changeourtaxamountcny},
			#{claimcountlimit},
			#{inputDate},
			#{updatesysdate}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicycopyitemkind.po.Gupolicycopyitemkind">
		insert into GUPOLICYCOPYITEMKIND
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="endorNo != null" >
				ENDORNO,
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO,
			</if>
			<if test="plancode != null" >
				PLANCODE,
			</if>
			<if test="riskCode != null" >
				RISKCODE,
			</if>
			<if test="itemKindNo != null" >
				ITEMKINDNO,
			</if>
			<if test="itemNo != null" >
				ITEMNO,
			</if>
			<if test="itemCode != null" >
				ITEMCODE,
			</if>
			<if test="itemdetailno != null" >
				ITEMDETAILNO,
			</if>
			<if test="itemdetailcode != null" >
				ITEMDETAILCODE,
			</if>
			<if test="itemdetaillist != null" >
				ITEMDETAILLIST,
			</if>
			<if test="planid != null" >
				PLANID,
			</if>
			<if test="kindind != null" >
				KINDIND,
			</if>
			<if test="kindCode != null" >
				KINDCODE,
			</if>
			<if test="kindName != null" >
				KINDNAME,
			</if>
			<if test="modeCode != null" >
				MODECODE,
			</if>
			<if test="modeName != null" >
				MODENAME,
			</if>
			<if test="startDate != null" >
				STARTDATE,
			</if>
			<if test="endDate != null" >
				ENDDATE,
			</if>
			<if test="calculateind != null" >
				CALCULATEIND,
			</if>
			<if test="currency != null" >
				CURRENCY,
			</if>
			<if test="unit != null" >
				UNIT,
			</if>
			<if test="sumValue != null" >
				SUMVALUE,
			</if>
			<if test="suminsured != null" >
				SUMINSURED,
			</if>
			<if test="changeinsured != null" >
				CHANGEINSURED,
			</if>
			<if test="ratePeriod != null" >
				RATEPERIOD,
			</if>
			<if test="rate != null" >
				RATE,
			</if>
			<if test="shortrateFlag != null" >
				SHORTRATEFLAG,
			</if>
			<if test="shortRate != null" >
				SHORTRATE,
			</if>
			<if test="shortratedenominator != null" >
				SHORTRATEDENOMINATOR,
			</if>
			<if test="loading != null" >
				LOADING,
			</if>
			<if test="grosspremium != null" >
				GROSSPREMIUM,
			</if>
			<if test="changegrosspremium != null" >
				CHANGEGROSSPREMIUM,
			</if>
			<if test="notaxpremium != null" >
				NOTAXPREMIUM,
			</if>
			<if test="taxamount != null" >
				TAXAMOUNT,
			</if>
			<if test="changenotaxpremium != null" >
				CHANGENOTAXPREMIUM,
			</if>
			<if test="changetaxamount != null" >
				CHANGETAXAMOUNT,
			</if>
			<if test="netPremium != null" >
				NETPREMIUM,
			</if>
			<if test="changenetpremium != null" >
				CHANGENETPREMIUM,
			</if>
			<if test="deductibleRate != null" >
				DEDUCTIBLERATE,
			</if>
			<if test="deductible != null" >
				DEDUCTIBLE,
			</if>
			<if test="yearpremium != null" >
				YEARPREMIUM,
			</if>
			<if test="surrenderind != null" >
				SURRENDERIND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="liabCode != null" >
				LIABCODE,
			</if>
			<if test="unitPremium != null" >
				UNITPREMIUM,
			</if>
			<if test="discount != null" >
				DISCOUNT,
			</if>
			<if test="quantity != null" >
				QUANTITY,
			</if>
			<if test="uwcount != null" >
				UWCOUNT,
			</if>
			<if test="uwpremium != null" >
				UWPREMIUM,
			</if>
			<if test="changeuwpremium != null" >
				CHANGEUWPREMIUM,
			</if>
			<if test="originuwpremium != null" >
				ORIGINUWPREMIUM,
			</if>
			<if test="origingrosspremium != null" >
				ORIGINGROSSPREMIUM,
			</if>
			<if test="commission != null" >
				COMMISSION,
			</if>
			<if test="changecommission != null" >
				CHANGECOMMISSION,
			</if>
			<if test="agentrate != null" >
				AGENTRATE,
			</if>
			<if test="pubinsuredind != null" >
				PUBINSUREDIND,
			</if>
			<if test="specialind != null" >
				SPECIALIND,
			</if>
			<if test="mustinsureind != null" >
				MUSTINSUREIND,
			</if>
			<if test="ouramount != null" >
				OURAMOUNT,
			</if>
			<if test="ourpremium != null" >
				OURPREMIUM,
			</if>
			<if test="ournottaxpremium != null" >
				OURNOTTAXPREMIUM,
			</if>
			<if test="ourtaxamount != null" >
				OURTAXAMOUNT,
			</if>
			<if test="changeouramount != null" >
				CHANGEOURAMOUNT,
			</if>
			<if test="changeourpremium != null" >
				CHANGEOURPREMIUM,
			</if>
			<if test="changeournottaxpremium != null" >
				CHANGEOURNOTTAXPREMIUM,
			</if>
			<if test="changeourtaxamount != null" >
				CHANGEOURTAXAMOUNT,
			</if>
			<if test="currencycny != null" >
				CURRENCYCNY,
			</if>
			<if test="suminsuredcny != null" >
				SUMINSUREDCNY,
			</if>
			<if test="uwpremiumcny != null" >
				UWPREMIUMCNY,
			</if>
			<if test="notaxpremiumcny != null" >
				NOTAXPREMIUMCNY,
			</if>
			<if test="taxamountcny != null" >
				TAXAMOUNTCNY,
			</if>
			<if test="changeinsuredcny != null" >
				CHANGEINSUREDCNY,
			</if>
			<if test="changeuwpremiumcny != null" >
				CHANGEUWPREMIUMCNY,
			</if>
			<if test="changenotaxpremiumcny != null" >
				CHANGENOTAXPREMIUMCNY,
			</if>
			<if test="changetaxamountcny != null" >
				CHANGETAXAMOUNTCNY,
			</if>
			<if test="ouramountcny != null" >
				OURAMOUNTCNY,
			</if>
			<if test="ourpremiumcny != null" >
				OURPREMIUMCNY,
			</if>
			<if test="ournottaxpremiumcny != null" >
				OURNOTTAXPREMIUMCNY,
			</if>
			<if test="ourtaxamountcny != null" >
				OURTAXAMOUNTCNY,
			</if>
			<if test="changeouramountcny != null" >
				CHANGEOURAMOUNTCNY,
			</if>
			<if test="changeourpremiumcny != null" >
				CHANGEOURPREMIUMCNY,
			</if>
			<if test="changeournottaxpremiumcny != null" >
				CHANGEOURNOTTAXPREMIUMCNY,
			</if>
			<if test="changeourtaxamountcny != null" >
				CHANGEOURTAXAMOUNTCNY,
			</if>
			<if test="claimcountlimit != null" >
				CLAIMCOUNTLIMIT,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="endorNo != null" >
				#{endorNo},
			</if>
			<if test="subpolicyno != null" >
				#{subpolicyno},
			</if>
			<if test="plancode != null" >
				#{plancode},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="itemKindNo != null" >
				#{itemKindNo},
			</if>
			<if test="itemNo != null" >
				#{itemNo},
			</if>
			<if test="itemCode != null" >
				#{itemCode},
			</if>
			<if test="itemdetailno != null" >
				#{itemdetailno},
			</if>
			<if test="itemdetailcode != null" >
				#{itemdetailcode},
			</if>
			<if test="itemdetaillist != null" >
				#{itemdetaillist},
			</if>
			<if test="planid != null" >
				#{planid},
			</if>
			<if test="kindind != null" >
				#{kindind},
			</if>
			<if test="kindCode != null" >
				#{kindCode},
			</if>
			<if test="kindName != null" >
				#{kindName},
			</if>
			<if test="modeCode != null" >
				#{modeCode},
			</if>
			<if test="modeName != null" >
				#{modeName},
			</if>
			<if test="startDate != null" >
				#{startDate},
			</if>
			<if test="endDate != null" >
				#{endDate},
			</if>
			<if test="calculateind != null" >
				#{calculateind},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
			<if test="unit != null" >
				#{unit},
			</if>
			<if test="sumValue != null" >
				#{sumValue},
			</if>
			<if test="suminsured != null" >
				#{suminsured},
			</if>
			<if test="changeinsured != null" >
				#{changeinsured},
			</if>
			<if test="ratePeriod != null" >
				#{ratePeriod},
			</if>
			<if test="rate != null" >
				#{rate},
			</if>
			<if test="shortrateFlag != null" >
				#{shortrateFlag},
			</if>
			<if test="shortRate != null" >
				#{shortRate},
			</if>
			<if test="shortratedenominator != null" >
				#{shortratedenominator},
			</if>
			<if test="loading != null" >
				#{loading},
			</if>
			<if test="grosspremium != null" >
				#{grosspremium},
			</if>
			<if test="changegrosspremium != null" >
				#{changegrosspremium},
			</if>
			<if test="notaxpremium != null" >
				#{notaxpremium},
			</if>
			<if test="taxamount != null" >
				#{taxamount},
			</if>
			<if test="changenotaxpremium != null" >
				#{changenotaxpremium},
			</if>
			<if test="changetaxamount != null" >
				#{changetaxamount},
			</if>
			<if test="netPremium != null" >
				#{netPremium},
			</if>
			<if test="changenetpremium != null" >
				#{changenetpremium},
			</if>
			<if test="deductibleRate != null" >
				#{deductibleRate},
			</if>
			<if test="deductible != null" >
				#{deductible},
			</if>
			<if test="yearpremium != null" >
				#{yearpremium},
			</if>
			<if test="surrenderind != null" >
				#{surrenderind},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="liabCode != null" >
				#{liabCode},
			</if>
			<if test="unitPremium != null" >
				#{unitPremium},
			</if>
			<if test="discount != null" >
				#{discount},
			</if>
			<if test="quantity != null" >
				#{quantity},
			</if>
			<if test="uwcount != null" >
				#{uwcount},
			</if>
			<if test="uwpremium != null" >
				#{uwpremium},
			</if>
			<if test="changeuwpremium != null" >
				#{changeuwpremium},
			</if>
			<if test="originuwpremium != null" >
				#{originuwpremium},
			</if>
			<if test="origingrosspremium != null" >
				#{origingrosspremium},
			</if>
			<if test="commission != null" >
				#{commission},
			</if>
			<if test="changecommission != null" >
				#{changecommission},
			</if>
			<if test="agentrate != null" >
				#{agentrate},
			</if>
			<if test="pubinsuredind != null" >
				#{pubinsuredind},
			</if>
			<if test="specialind != null" >
				#{specialind},
			</if>
			<if test="mustinsureind != null" >
				#{mustinsureind},
			</if>
			<if test="ouramount != null" >
				#{ouramount},
			</if>
			<if test="ourpremium != null" >
				#{ourpremium},
			</if>
			<if test="ournottaxpremium != null" >
				#{ournottaxpremium},
			</if>
			<if test="ourtaxamount != null" >
				#{ourtaxamount},
			</if>
			<if test="changeouramount != null" >
				#{changeouramount},
			</if>
			<if test="changeourpremium != null" >
				#{changeourpremium},
			</if>
			<if test="changeournottaxpremium != null" >
				#{changeournottaxpremium},
			</if>
			<if test="changeourtaxamount != null" >
				#{changeourtaxamount},
			</if>
			<if test="currencycny != null" >
				#{currencycny},
			</if>
			<if test="suminsuredcny != null" >
				#{suminsuredcny},
			</if>
			<if test="uwpremiumcny != null" >
				#{uwpremiumcny},
			</if>
			<if test="notaxpremiumcny != null" >
				#{notaxpremiumcny},
			</if>
			<if test="taxamountcny != null" >
				#{taxamountcny},
			</if>
			<if test="changeinsuredcny != null" >
				#{changeinsuredcny},
			</if>
			<if test="changeuwpremiumcny != null" >
				#{changeuwpremiumcny},
			</if>
			<if test="changenotaxpremiumcny != null" >
				#{changenotaxpremiumcny},
			</if>
			<if test="changetaxamountcny != null" >
				#{changetaxamountcny},
			</if>
			<if test="ouramountcny != null" >
				#{ouramountcny},
			</if>
			<if test="ourpremiumcny != null" >
				#{ourpremiumcny},
			</if>
			<if test="ournottaxpremiumcny != null" >
				#{ournottaxpremiumcny},
			</if>
			<if test="ourtaxamountcny != null" >
				#{ourtaxamountcny},
			</if>
			<if test="changeouramountcny != null" >
				#{changeouramountcny},
			</if>
			<if test="changeourpremiumcny != null" >
				#{changeourpremiumcny},
			</if>
			<if test="changeournottaxpremiumcny != null" >
				#{changeournottaxpremiumcny},
			</if>
			<if test="changeourtaxamountcny != null" >
				#{changeourtaxamountcny},
			</if>
			<if test="claimcountlimit != null" >
				#{claimcountlimit},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate}
			</if>
		</trim>
	</insert>

    <!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicycopyitemkind.po.Gupolicycopyitemkind">
		update GUPOLICYCOPYITEMKIND 
		<set>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="endorNo != null" >
				ENDORNO=#{endorNo},
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO=#{subpolicyno},
			</if>
			<if test="plancode != null" >
				PLANCODE=#{plancode},
			</if>
			<if test="riskCode != null" >
				RISKCODE=#{riskCode},
			</if>
			<if test="itemKindNo != null" >
				ITEMKINDNO=#{itemKindNo},
			</if>
			<if test="itemNo != null" >
				ITEMNO=#{itemNo},
			</if>
			<if test="itemCode != null" >
				ITEMCODE=#{itemCode},
			</if>
			<if test="itemdetailno != null" >
				ITEMDETAILNO=#{itemdetailno},
			</if>
			<if test="itemdetailcode != null" >
				ITEMDETAILCODE=#{itemdetailcode},
			</if>
			<if test="itemdetaillist != null" >
				ITEMDETAILLIST=#{itemdetaillist},
			</if>
			<if test="planid != null" >
				PLANID=#{planid},
			</if>
			<if test="kindind != null" >
				KINDIND=#{kindind},
			</if>
			<if test="kindCode != null" >
				KINDCODE=#{kindCode},
			</if>
			<if test="kindName != null" >
				KINDNAME=#{kindName},
			</if>
			<if test="modeCode != null" >
				MODECODE=#{modeCode},
			</if>
			<if test="modeName != null" >
				MODENAME=#{modeName},
			</if>
			<if test="startDate != null" >
				STARTDATE=#{startDate},
			</if>
			<if test="endDate != null" >
				ENDDATE=#{endDate},
			</if>
			<if test="calculateind != null" >
				CALCULATEIND=#{calculateind},
			</if>
			<if test="currency != null" >
				CURRENCY=#{currency},
			</if>
			<if test="unit != null" >
				UNIT=#{unit},
			</if>
			<if test="sumValue != null" >
				SUMVALUE=#{sumValue},
			</if>
			<if test="suminsured != null" >
				SUMINSURED=#{suminsured},
			</if>
			<if test="changeinsured != null" >
				CHANGEINSURED=#{changeinsured},
			</if>
			<if test="ratePeriod != null" >
				RATEPERIOD=#{ratePeriod},
			</if>
			<if test="rate != null" >
				RATE=#{rate},
			</if>
			<if test="shortrateFlag != null" >
				SHORTRATEFLAG=#{shortrateFlag},
			</if>
			<if test="shortRate != null" >
				SHORTRATE=#{shortRate},
			</if>
			<if test="shortratedenominator != null" >
				SHORTRATEDENOMINATOR=#{shortratedenominator},
			</if>
			<if test="loading != null" >
				LOADING=#{loading},
			</if>
			<if test="grosspremium != null" >
				GROSSPREMIUM=#{grosspremium},
			</if>
			<if test="changegrosspremium != null" >
				CHANGEGROSSPREMIUM=#{changegrosspremium},
			</if>
			<if test="notaxpremium != null" >
				NOTAXPREMIUM=#{notaxpremium},
			</if>
			<if test="taxamount != null" >
				TAXAMOUNT=#{taxamount},
			</if>
			<if test="changenotaxpremium != null" >
				CHANGENOTAXPREMIUM=#{changenotaxpremium},
			</if>
			<if test="changetaxamount != null" >
				CHANGETAXAMOUNT=#{changetaxamount},
			</if>
			<if test="netPremium != null" >
				NETPREMIUM=#{netPremium},
			</if>
			<if test="changenetpremium != null" >
				CHANGENETPREMIUM=#{changenetpremium},
			</if>
			<if test="deductibleRate != null" >
				DEDUCTIBLERATE=#{deductibleRate},
			</if>
			<if test="deductible != null" >
				DEDUCTIBLE=#{deductible},
			</if>
			<if test="yearpremium != null" >
				YEARPREMIUM=#{yearpremium},
			</if>
			<if test="surrenderind != null" >
				SURRENDERIND=#{surrenderind},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="liabCode != null" >
				LIABCODE=#{liabCode},
			</if>
			<if test="unitPremium != null" >
				UNITPREMIUM=#{unitPremium},
			</if>
			<if test="discount != null" >
				DISCOUNT=#{discount},
			</if>
			<if test="quantity != null" >
				QUANTITY=#{quantity},
			</if>
			<if test="uwcount != null" >
				UWCOUNT=#{uwcount},
			</if>
			<if test="uwpremium != null" >
				UWPREMIUM=#{uwpremium},
			</if>
			<if test="changeuwpremium != null" >
				CHANGEUWPREMIUM=#{changeuwpremium},
			</if>
			<if test="originuwpremium != null" >
				ORIGINUWPREMIUM=#{originuwpremium},
			</if>
			<if test="origingrosspremium != null" >
				ORIGINGROSSPREMIUM=#{origingrosspremium},
			</if>
			<if test="commission != null" >
				COMMISSION=#{commission},
			</if>
			<if test="changecommission != null" >
				CHANGECOMMISSION=#{changecommission},
			</if>
			<if test="agentrate != null" >
				AGENTRATE=#{agentrate},
			</if>
			<if test="pubinsuredind != null" >
				PUBINSUREDIND=#{pubinsuredind},
			</if>
			<if test="specialind != null" >
				SPECIALIND=#{specialind},
			</if>
			<if test="mustinsureind != null" >
				MUSTINSUREIND=#{mustinsureind},
			</if>
			<if test="ouramount != null" >
				OURAMOUNT=#{ouramount},
			</if>
			<if test="ourpremium != null" >
				OURPREMIUM=#{ourpremium},
			</if>
			<if test="ournottaxpremium != null" >
				OURNOTTAXPREMIUM=#{ournottaxpremium},
			</if>
			<if test="ourtaxamount != null" >
				OURTAXAMOUNT=#{ourtaxamount},
			</if>
			<if test="changeouramount != null" >
				CHANGEOURAMOUNT=#{changeouramount},
			</if>
			<if test="changeourpremium != null" >
				CHANGEOURPREMIUM=#{changeourpremium},
			</if>
			<if test="changeournottaxpremium != null" >
				CHANGEOURNOTTAXPREMIUM=#{changeournottaxpremium},
			</if>
			<if test="changeourtaxamount != null" >
				CHANGEOURTAXAMOUNT=#{changeourtaxamount},
			</if>
			<if test="currencycny != null" >
				CURRENCYCNY=#{currencycny},
			</if>
			<if test="suminsuredcny != null" >
				SUMINSUREDCNY=#{suminsuredcny},
			</if>
			<if test="uwpremiumcny != null" >
				UWPREMIUMCNY=#{uwpremiumcny},
			</if>
			<if test="notaxpremiumcny != null" >
				NOTAXPREMIUMCNY=#{notaxpremiumcny},
			</if>
			<if test="taxamountcny != null" >
				TAXAMOUNTCNY=#{taxamountcny},
			</if>
			<if test="changeinsuredcny != null" >
				CHANGEINSUREDCNY=#{changeinsuredcny},
			</if>
			<if test="changeuwpremiumcny != null" >
				CHANGEUWPREMIUMCNY=#{changeuwpremiumcny},
			</if>
			<if test="changenotaxpremiumcny != null" >
				CHANGENOTAXPREMIUMCNY=#{changenotaxpremiumcny},
			</if>
			<if test="changetaxamountcny != null" >
				CHANGETAXAMOUNTCNY=#{changetaxamountcny},
			</if>
			<if test="ouramountcny != null" >
				OURAMOUNTCNY=#{ouramountcny},
			</if>
			<if test="ourpremiumcny != null" >
				OURPREMIUMCNY=#{ourpremiumcny},
			</if>
			<if test="ournottaxpremiumcny != null" >
				OURNOTTAXPREMIUMCNY=#{ournottaxpremiumcny},
			</if>
			<if test="ourtaxamountcny != null" >
				OURTAXAMOUNTCNY=#{ourtaxamountcny},
			</if>
			<if test="changeouramountcny != null" >
				CHANGEOURAMOUNTCNY=#{changeouramountcny},
			</if>
			<if test="changeourpremiumcny != null" >
				CHANGEOURPREMIUMCNY=#{changeourpremiumcny},
			</if>
			<if test="changeournottaxpremiumcny != null" >
				CHANGEOURNOTTAXPREMIUMCNY=#{changeournottaxpremiumcny},
			</if>
			<if test="changeourtaxamountcny != null" >
				CHANGEOURTAXAMOUNTCNY=#{changeourtaxamountcny},
			</if>
			<if test="claimcountlimit != null" >
				CLAIMCOUNTLIMIT=#{claimcountlimit},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicycopyitemkind.po.Gupolicycopyitemkind">
		update GUPOLICYCOPYITEMKIND set
			POLICYNO=#{policyNo},
			ENDORNO=#{endorNo},
			SUBPOLICYNO=#{subpolicyno},
			PLANCODE=#{plancode},
			RISKCODE=#{riskCode},
			ITEMKINDNO=#{itemKindNo},
			ITEMNO=#{itemNo},
			ITEMCODE=#{itemCode},
			ITEMDETAILNO=#{itemdetailno},
			ITEMDETAILCODE=#{itemdetailcode},
			ITEMDETAILLIST=#{itemdetaillist},
			PLANID=#{planid},
			KINDIND=#{kindind},
			KINDCODE=#{kindCode},
			KINDNAME=#{kindName},
			MODECODE=#{modeCode},
			MODENAME=#{modeName},
			STARTDATE=#{startDate},
			ENDDATE=#{endDate},
			CALCULATEIND=#{calculateind},
			CURRENCY=#{currency},
			UNIT=#{unit},
			SUMVALUE=#{sumValue},
			SUMINSURED=#{suminsured},
			CHANGEINSURED=#{changeinsured},
			RATEPERIOD=#{ratePeriod},
			RATE=#{rate},
			SHORTRATEFLAG=#{shortrateFlag},
			SHORTRATE=#{shortRate},
			SHORTRATEDENOMINATOR=#{shortratedenominator},
			LOADING=#{loading},
			GROSSPREMIUM=#{grosspremium},
			CHANGEGROSSPREMIUM=#{changegrosspremium},
			NOTAXPREMIUM=#{notaxpremium},
			TAXAMOUNT=#{taxamount},
			CHANGENOTAXPREMIUM=#{changenotaxpremium},
			CHANGETAXAMOUNT=#{changetaxamount},
			NETPREMIUM=#{netPremium},
			CHANGENETPREMIUM=#{changenetpremium},
			DEDUCTIBLERATE=#{deductibleRate},
			DEDUCTIBLE=#{deductible},
			YEARPREMIUM=#{yearpremium},
			SURRENDERIND=#{surrenderind},
			REMARK=#{remark},
			FLAG=#{flag},
			LIABCODE=#{liabCode},
			UNITPREMIUM=#{unitPremium},
			DISCOUNT=#{discount},
			QUANTITY=#{quantity},
			UWCOUNT=#{uwcount},
			UWPREMIUM=#{uwpremium},
			CHANGEUWPREMIUM=#{changeuwpremium},
			ORIGINUWPREMIUM=#{originuwpremium},
			ORIGINGROSSPREMIUM=#{origingrosspremium},
			COMMISSION=#{commission},
			CHANGECOMMISSION=#{changecommission},
			AGENTRATE=#{agentrate},
			PUBINSUREDIND=#{pubinsuredind},
			SPECIALIND=#{specialind},
			MUSTINSUREIND=#{mustinsureind},
			OURAMOUNT=#{ouramount},
			OURPREMIUM=#{ourpremium},
			OURNOTTAXPREMIUM=#{ournottaxpremium},
			OURTAXAMOUNT=#{ourtaxamount},
			CHANGEOURAMOUNT=#{changeouramount},
			CHANGEOURPREMIUM=#{changeourpremium},
			CHANGEOURNOTTAXPREMIUM=#{changeournottaxpremium},
			CHANGEOURTAXAMOUNT=#{changeourtaxamount},
			CURRENCYCNY=#{currencycny},
			SUMINSUREDCNY=#{suminsuredcny},
			UWPREMIUMCNY=#{uwpremiumcny},
			NOTAXPREMIUMCNY=#{notaxpremiumcny},
			TAXAMOUNTCNY=#{taxamountcny},
			CHANGEINSUREDCNY=#{changeinsuredcny},
			CHANGEUWPREMIUMCNY=#{changeuwpremiumcny},
			CHANGENOTAXPREMIUMCNY=#{changenotaxpremiumcny},
			CHANGETAXAMOUNTCNY=#{changetaxamountcny},
			OURAMOUNTCNY=#{ouramountcny},
			OURPREMIUMCNY=#{ourpremiumcny},
			OURNOTTAXPREMIUMCNY=#{ournottaxpremiumcny},
			OURTAXAMOUNTCNY=#{ourtaxamountcny},
			CHANGEOURAMOUNTCNY=#{changeouramountcny},
			CHANGEOURPREMIUMCNY=#{changeourpremiumcny},
			CHANGEOURNOTTAXPREMIUMCNY=#{changeournottaxpremiumcny},
			CHANGEOURTAXAMOUNTCNY=#{changeourtaxamountcny},
			CLAIMCOUNTLIMIT=#{claimcountlimit},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
		where ID = #{id}	</update>

	<insert id="batchInsert">
		INSERT ALL
		<foreach collection="list" item="item" separator="">
			INTO GUPOLICYCOPYITEMKIND VALUES
			(#{item.id}, #{item.policyNo}, #{item.endorNo}, #{item.subpolicyno}, #{item.plancode},
			#{item.riskCode}, #{item.itemKindNo}, #{item.itemNo}, #{item.itemCode}, #{item.itemdetailno},
			#{item.itemdetailcode}, #{item.itemdetaillist}, #{item.planid}, #{item.kindind}, #{item.kindCode},
			#{item.kindName}, #{item.modeCode}, #{item.modeName}, #{item.startDate}, #{item.endDate},
			#{item.calculateind}, #{item.currency}, #{item.unit}, #{item.sumValue}, #{item.suminsured},
			#{item.changeinsured}, #{item.ratePeriod}, #{item.rate}, #{item.shortrateFlag}, #{item.shortRate},
			#{item.shortratedenominator}, #{item.loading}, #{item.grosspremium}, #{item.changegrosspremium},
			#{item.notaxpremium}, #{item.taxamount}, #{item.changenotaxpremium}, #{item.changetaxamount},
			#{item.netPremium}, #{item.changenetpremium}, #{item.deductibleRate}, #{item.deductible},
			#{item.yearpremium}, #{item.surrenderind}, #{item.remark}, #{item.flag}, #{item.liabCode},
			#{item.unitPremium}, #{item.discount}, #{item.quantity}, #{item.uwcount}, #{item.uwpremium},
			#{item.changeuwpremium}, #{item.originuwpremium}, #{item.origingrosspremium}, #{item.commission},
			#{item.changecommission}, #{item.agentrate}, #{item.pubinsuredind}, #{item.specialind},
			#{item.mustinsureind}, #{item.ouramount}, #{item.ourpremium}, #{item.ournottaxpremium},
			#{item.ourtaxamount}, #{item.changeouramount}, #{item.changeourpremium}, #{item.changeournottaxpremium},
			#{item.changeourtaxamount}, #{item.currencycny}, #{item.suminsuredcny}, #{item.uwpremiumcny},
			#{item.notaxpremiumcny}, #{item.taxamountcny}, #{item.changeinsuredcny}, #{item.changeuwpremiumcny},
			#{item.changenotaxpremiumcny}, #{item.changetaxamountcny}, #{item.ouramountcny}, #{item.ourpremiumcny},
			#{item.ournottaxpremiumcny}, #{item.ourtaxamountcny}, #{item.changeouramountcny}, #{item.changeourpremiumcny},
			#{item.changeournottaxpremiumcny}, #{item.changeourtaxamountcny}, #{item.claimcountlimit}, #{item.inputDate},
			#{item.updatesysdate})
		</foreach>
		select 1 from dual
	</insert>
</mapper>
