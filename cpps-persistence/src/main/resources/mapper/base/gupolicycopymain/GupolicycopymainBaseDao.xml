<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicycopymain.dao.GupolicycopymainDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicycopymain.po.Gupolicycopymain">
		<id column="ID" property="id" />
		<result column="PROPOSALNO" property="proposalNo" />
		<result column="POLICYNO" property="policyNo" />
		<result column="ENDORSERIALNO" property="endorserialno" />
		<result column="ENDORSEQNO" property="endorseqno" />
		<result column="ENDORNO" property="endorNo" />
		<result column="LANGUAGE" property="language" />
		<result column="PRODUCTCODE" property="productcode" />
		<result column="GROUPIND" property="groupind" />
		<result column="INSURANCECOMPANYCODE" property="insurancecompanycode" />
		<result column="SURVEYIND" property="surveyind" />
		<result column="FLOWID" property="flowid" />
		<result column="COMPANYCODE" property="companycode" />
		<result column="COMPANYNAME" property="companyname" />
		<result column="PROJECTMANAGERCODE" property="projectmanagercode" />
		<result column="PROJECTMANAGERNAME" property="projectmanagername" />
		<result column="STARTDATE" property="startDate" />
		<result column="ENDDATE" property="endDate" />
		<result column="VALIDDATE" property="validDate" />
		<result column="APPLICODE" property="appliCode" />
		<result column="APPLINAME" property="appliName" />
		<result column="INSUREDCODE" property="insuredCode" />
		<result column="INSUREDNAME" property="insuredName" />
		<result column="CURRENCY" property="currency" />
		<result column="YEARPREMIUM" property="yearpremium" />
		<result column="SUMINSURED" property="suminsured" />
		<result column="SUMGROSSPREMIUM" property="sumgrosspremium" />
		<result column="SUMNETPREMIUM" property="sumnetpremium" />
		<result column="SUMUWPREMIUM" property="sumuwpremium" />
		<result column="NOTAXPREMIUM" property="notaxpremium" />
		<result column="TAXAMOUNT" property="taxamount" />
		<result column="CHANGEINSURED" property="changeinsured" />
		<result column="CHANGEGROSSPREMIUM" property="changegrosspremium" />
		<result column="CHANGENETPREMIUM" property="changenetpremium" />
		<result column="CHANGEUWPREMIUM" property="changeuwpremium" />
		<result column="CHANGENOTAXPREMIUM" property="changenotaxpremium" />
		<result column="CHANGETAXAMOUNT" property="changetaxamount" />
		<result column="CURRENCYCNY" property="currencycny" />
		<result column="SUMINSUREDCNY" property="suminsuredcny" />
		<result column="SUMGROSSPREMIUMCNY" property="sumgrosspremiumcny" />
		<result column="SUMUWPREMIUMCNY" property="sumuwpremiumcny" />
		<result column="NOTAXPREMIUMCNY" property="notaxpremiumcny" />
		<result column="TAXAMOUNTCNY" property="taxamountcny" />
		<result column="CHANGEINSUREDCNY" property="changeinsuredcny" />
		<result column="CHANGEGROSSPREMIUMCNY" property="changegrosspremiumcny" />
		<result column="CHANGEUWPREMIUMCNY" property="changeuwpremiumcny" />
		<result column="CHANGENOTAXPREMIUMCNY" property="changenotaxpremiumcny" />
		<result column="CHANGETAXAMOUNTCNY" property="changetaxamountcny" />
		<result column="UWYEAR" property="uwYear" />
		<result column="ACCEPTDATE" property="acceptdate" />
		<result column="UNDERWRITEIND" property="underwriteind" />
		<result column="UNDERWRITEENDDATE" property="underWriteEndDate" />
		<result column="SURRENDERIND" property="surrenderind" />
		<result column="CANCELIND" property="cancelind" />
		<result column="ENDIND" property="endind" />
		<result column="CODIND" property="codind" />
		<result column="CALCULATETYPE" property="calculatetype" />
		<result column="COINSIND" property="coinsind" />
		<result column="AGENTRATE" property="agentrate" />
		<result column="NOTAXAGENTRATE" property="notaxagentrate" />
		<result column="COMMISSION" property="commission" />
		<result column="CHANGECOMMISSION" property="changecommission" />
		<result column="FSH" property="fsh" />
		<result column="XSF" property="xsf" />
		<result column="XSFIND" property="xsfind" />
		<result column="INSTALLMENTNO" property="installmentno" />
		<result column="ENDORSETIMES" property="endorseTimes" />
		<result column="RENEWEDTIME" property="renewedtime" />
		<result column="REGISTTIMES" property="registTimes" />
		<result column="CLAIMSTIMES" property="claimstimes" />
		<result column="PRINTTIMES" property="printtimes" />
		<result column="ISSENDSMS" property="issendsms" />
		<result column="ISSENDEMAIL" property="issendemail" />
		<result column="REMARK" property="remark" />
		<result column="VALIDIND" property="validind" />
		<result column="FLAG" property="flag" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
		<result column="SETTLESTATUS" property="settleStatus" />
		<result column="SETTLEFEE" property="settlefee" />
		<result column="SETTLENO" property="settleNo" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		PROPOSALNO,
		POLICYNO,
		ENDORSERIALNO,
		ENDORSEQNO,
		ENDORNO,
		LANGUAGE,
		PRODUCTCODE,
		GROUPIND,
		INSURANCECOMPANYCODE,
		SURVEYIND,
		FLOWID,
		COMPANYCODE,
		COMPANYNAME,
		PROJECTMANAGERCODE,
		PROJECTMANAGERNAME,
		STARTDATE,
		ENDDATE,
		VALIDDATE,
		APPLICODE,
		APPLINAME,
		INSUREDCODE,
		INSUREDNAME,
		CURRENCY,
		YEARPREMIUM,
		SUMINSURED,
		SUMGROSSPREMIUM,
		SUMNETPREMIUM,
		SUMUWPREMIUM,
		NOTAXPREMIUM,
		TAXAMOUNT,
		CHANGEINSURED,
		CHANGEGROSSPREMIUM,
		CHANGENETPREMIUM,
		CHANGEUWPREMIUM,
		CHANGENOTAXPREMIUM,
		CHANGETAXAMOUNT,
		CURRENCYCNY,
		SUMINSUREDCNY,
		SUMGROSSPREMIUMCNY,
		SUMUWPREMIUMCNY,
		NOTAXPREMIUMCNY,
		TAXAMOUNTCNY,
		CHANGEINSUREDCNY,
		CHANGEGROSSPREMIUMCNY,
		CHANGEUWPREMIUMCNY,
		CHANGENOTAXPREMIUMCNY,
		CHANGETAXAMOUNTCNY,
		UWYEAR,
		ACCEPTDATE,
		UNDERWRITEIND,
		UNDERWRITEENDDATE,
		SURRENDERIND,
		CANCELIND,
		ENDIND,
		CODIND,
		CALCULATETYPE,
		COINSIND,
		AGENTRATE,
		NOTAXAGENTRATE,
		COMMISSION,
		CHANGECOMMISSION,
		FSH,
		XSF,
		XSFIND,
		INSTALLMENTNO,
		ENDORSETIMES,
		RENEWEDTIME,
		REGISTTIMES,
		CLAIMSTIMES,
		PRINTTIMES,
		ISSENDSMS,
		ISSENDEMAIL,
		REMARK,
		VALIDIND,
		FLAG,
		INPUTDATE,
		UPDATESYSDATE,
		SETTLESTATUS,
		SETTLEFEE,
		SETTLENO
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="proposalNo != null and proposalNo != ''" >
			and PROPOSALNO = #{proposalNo}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="endorserialno != null and endorserialno != ''" >
			and ENDORSERIALNO = #{endorserialno}
		</if>
		<if test="endorseqno != null and endorseqno != ''" >
			and ENDORSEQNO = #{endorseqno}
		</if>
		<if test="endorNo != null and endorNo != ''" >
			and ENDORNO = #{endorNo}
		</if>
		<if test="language != null and language != ''" >
			and LANGUAGE = #{language}
		</if>
		<if test="productcode != null and productcode != ''" >
			and PRODUCTCODE = #{productcode}
		</if>
		<if test="groupind != null and groupind != ''" >
			and GROUPIND = #{groupind}
		</if>
		<if test="insurancecompanycode != null and insurancecompanycode != ''" >
			and INSURANCECOMPANYCODE = #{insurancecompanycode}
		</if>
		<if test="surveyind != null and surveyind != ''" >
			and SURVEYIND = #{surveyind}
		</if>
		<if test="flowid != null and flowid != ''" >
			and FLOWID = #{flowid}
		</if>
		<if test="companycode != null and companycode != ''" >
			and COMPANYCODE = #{companycode}
		</if>
		<if test="companyname != null and companyname != ''" >
			and COMPANYNAME = #{companyname}
		</if>
		<if test="projectmanagercode != null and projectmanagercode != ''" >
			and PROJECTMANAGERCODE = #{projectmanagercode}
		</if>
		<if test="projectmanagername != null and projectmanagername != ''" >
			and PROJECTMANAGERNAME = #{projectmanagername}
		</if>
		<if test="startDate != null and startDate != ''" >
			and STARTDATE = #{startDate}
		</if>
		<if test="endDate != null and endDate != ''" >
			and ENDDATE = #{endDate}
		</if>
		<if test="validDate != null and validDate != ''" >
			and VALIDDATE = #{validDate}
		</if>
		<if test="appliCode != null and appliCode != ''" >
			and APPLICODE = #{appliCode}
		</if>
		<if test="appliName != null and appliName != ''" >
			and APPLINAME = #{appliName}
		</if>
		<if test="insuredCode != null and insuredCode != ''" >
			and INSUREDCODE = #{insuredCode}
		</if>
		<if test="insuredName != null and insuredName != ''" >
			and INSUREDNAME = #{insuredName}
		</if>
		<if test="currency != null and currency != ''" >
			and CURRENCY = #{currency}
		</if>
		<if test="yearpremium != null and yearpremium != ''" >
			and YEARPREMIUM = #{yearpremium}
		</if>
		<if test="suminsured != null and suminsured != ''" >
			and SUMINSURED = #{suminsured}
		</if>
		<if test="sumgrosspremium != null and sumgrosspremium != ''" >
			and SUMGROSSPREMIUM = #{sumgrosspremium}
		</if>
		<if test="sumnetpremium != null and sumnetpremium != ''" >
			and SUMNETPREMIUM = #{sumnetpremium}
		</if>
		<if test="sumuwpremium != null and sumuwpremium != ''" >
			and SUMUWPREMIUM = #{sumuwpremium}
		</if>
		<if test="notaxpremium != null and notaxpremium != ''" >
			and NOTAXPREMIUM = #{notaxpremium}
		</if>
		<if test="taxamount != null and taxamount != ''" >
			and TAXAMOUNT = #{taxamount}
		</if>
		<if test="changeinsured != null and changeinsured != ''" >
			and CHANGEINSURED = #{changeinsured}
		</if>
		<if test="changegrosspremium != null and changegrosspremium != ''" >
			and CHANGEGROSSPREMIUM = #{changegrosspremium}
		</if>
		<if test="changenetpremium != null and changenetpremium != ''" >
			and CHANGENETPREMIUM = #{changenetpremium}
		</if>
		<if test="changeuwpremium != null and changeuwpremium != ''" >
			and CHANGEUWPREMIUM = #{changeuwpremium}
		</if>
		<if test="changenotaxpremium != null and changenotaxpremium != ''" >
			and CHANGENOTAXPREMIUM = #{changenotaxpremium}
		</if>
		<if test="changetaxamount != null and changetaxamount != ''" >
			and CHANGETAXAMOUNT = #{changetaxamount}
		</if>
		<if test="currencycny != null and currencycny != ''" >
			and CURRENCYCNY = #{currencycny}
		</if>
		<if test="suminsuredcny != null and suminsuredcny != ''" >
			and SUMINSUREDCNY = #{suminsuredcny}
		</if>
		<if test="sumgrosspremiumcny != null and sumgrosspremiumcny != ''" >
			and SUMGROSSPREMIUMCNY = #{sumgrosspremiumcny}
		</if>
		<if test="sumuwpremiumcny != null and sumuwpremiumcny != ''" >
			and SUMUWPREMIUMCNY = #{sumuwpremiumcny}
		</if>
		<if test="notaxpremiumcny != null and notaxpremiumcny != ''" >
			and NOTAXPREMIUMCNY = #{notaxpremiumcny}
		</if>
		<if test="taxamountcny != null and taxamountcny != ''" >
			and TAXAMOUNTCNY = #{taxamountcny}
		</if>
		<if test="changeinsuredcny != null and changeinsuredcny != ''" >
			and CHANGEINSUREDCNY = #{changeinsuredcny}
		</if>
		<if test="changegrosspremiumcny != null and changegrosspremiumcny != ''" >
			and CHANGEGROSSPREMIUMCNY = #{changegrosspremiumcny}
		</if>
		<if test="changeuwpremiumcny != null and changeuwpremiumcny != ''" >
			and CHANGEUWPREMIUMCNY = #{changeuwpremiumcny}
		</if>
		<if test="changenotaxpremiumcny != null and changenotaxpremiumcny != ''" >
			and CHANGENOTAXPREMIUMCNY = #{changenotaxpremiumcny}
		</if>
		<if test="changetaxamountcny != null and changetaxamountcny != ''" >
			and CHANGETAXAMOUNTCNY = #{changetaxamountcny}
		</if>
		<if test="uwYear != null and uwYear != ''" >
			and UWYEAR = #{uwYear}
		</if>
		<if test="acceptdate != null and acceptdate != ''" >
			and ACCEPTDATE = #{acceptdate}
		</if>
		<if test="underwriteind != null and underwriteind != ''" >
			and UNDERWRITEIND = #{underwriteind}
		</if>
		<if test="underWriteEndDate != null and underWriteEndDate != ''" >
			and UNDERWRITEENDDATE = #{underWriteEndDate}
		</if>
		<if test="surrenderind != null and surrenderind != ''" >
			and SURRENDERIND = #{surrenderind}
		</if>
		<if test="cancelind != null and cancelind != ''" >
			and CANCELIND = #{cancelind}
		</if>
		<if test="endind != null and endind != ''" >
			and ENDIND = #{endind}
		</if>
		<if test="codind != null and codind != ''" >
			and CODIND = #{codind}
		</if>
		<if test="calculatetype != null and calculatetype != ''" >
			and CALCULATETYPE = #{calculatetype}
		</if>
		<if test="coinsind != null and coinsind != ''" >
			and COINSIND = #{coinsind}
		</if>
		<if test="agentrate != null and agentrate != ''" >
			and AGENTRATE = #{agentrate}
		</if>
		<if test="notaxagentrate != null and notaxagentrate != ''" >
			and NOTAXAGENTRATE = #{notaxagentrate}
		</if>
		<if test="commission != null and commission != ''" >
			and COMMISSION = #{commission}
		</if>
		<if test="changecommission != null and changecommission != ''" >
			and CHANGECOMMISSION = #{changecommission}
		</if>
		<if test="fsh != null and fsh != ''" >
			and FSH = #{fsh}
		</if>
		<if test="xsf != null and xsf != ''" >
			and XSF = #{xsf}
		</if>
		<if test="xsfind != null and xsfind != ''" >
			and XSFIND = #{xsfind}
		</if>
		<if test="installmentno != null and installmentno != ''" >
			and INSTALLMENTNO = #{installmentno}
		</if>
		<if test="endorseTimes != null and endorseTimes != ''" >
			and ENDORSETIMES = #{endorseTimes}
		</if>
		<if test="renewedtime != null and renewedtime != ''" >
			and RENEWEDTIME = #{renewedtime}
		</if>
		<if test="registTimes != null and registTimes != ''" >
			and REGISTTIMES = #{registTimes}
		</if>
		<if test="claimstimes != null and claimstimes != ''" >
			and CLAIMSTIMES = #{claimstimes}
		</if>
		<if test="printtimes != null and printtimes != ''" >
			and PRINTTIMES = #{printtimes}
		</if>
		<if test="issendsms != null and issendsms != ''" >
			and ISSENDSMS = #{issendsms}
		</if>
		<if test="issendemail != null and issendemail != ''" >
			and ISSENDEMAIL = #{issendemail}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="validind != null and validind != ''" >
			and VALIDIND = #{validind}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
		<if test="settleStatus != null and settleStatus != ''" >
			and SETTLESTATUS = #{settleStatus}
		</if>
		<if test="settlefee != null and settlefee != ''" >
			and SETTLEFEE = #{settlefee}
		</if>
		<if test="settleNo != null and settleNo != ''" >
			and SETTLENO = #{settleNo}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYMAIN
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYMAIN
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYMAIN
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicycopymain.po.Gupolicycopymain">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYCOPYMAIN
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYCOPYMAIN
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicycopymain.po.Gupolicycopymain">
		insert into GUPOLICYCOPYMAIN (
			ID,
			PROPOSALNO,
			POLICYNO,
			ENDORSERIALNO,
			ENDORSEQNO,
			ENDORNO,
			LANGUAGE,
			PRODUCTCODE,
			GROUPIND,
			INSURANCECOMPANYCODE,
			SURVEYIND,
			FLOWID,
			COMPANYCODE,
			COMPANYNAME,
			PROJECTMANAGERCODE,
			PROJECTMANAGERNAME,
			LASTMODIFYMANAGERCODE,
			LASTMODIFYMANAGERNAME,
			STARTDATE,
			ENDDATE,
			VALIDDATE,
			APPLICODE,
			APPLINAME,
			INSUREDCODE,
			INSUREDNAME,
			CURRENCY,
			YEARPREMIUM,
			SUMINSURED,
			SUMGROSSPREMIUM,
			SUMNETPREMIUM,
			SUMUWPREMIUM,
			NOTAXPREMIUM,
			TAXAMOUNT,
			CHANGEINSURED,
			CHANGEGROSSPREMIUM,
			CHANGENETPREMIUM,
			CHANGEUWPREMIUM,
			CHANGENOTAXPREMIUM,
			CHANGETAXAMOUNT,
			CURRENCYCNY,
			SUMINSUREDCNY,
			SUMGROSSPREMIUMCNY,
			SUMUWPREMIUMCNY,
			NOTAXPREMIUMCNY,
			TAXAMOUNTCNY,
			CHANGEINSUREDCNY,
			CHANGEGROSSPREMIUMCNY,
			CHANGEUWPREMIUMCNY,
			CHANGENOTAXPREMIUMCNY,
			CHANGETAXAMOUNTCNY,
			UWYEAR,
			ACCEPTDATE,
			UNDERWRITEIND,
			UNDERWRITEENDDATE,
			SURRENDERIND,
			CANCELIND,
			ENDIND,
			CODIND,
			CALCULATETYPE,
			COINSIND,
			AGENTRATE,
			NOTAXAGENTRATE,
			COMMISSION,
			CHANGECOMMISSION,
			FSH,
			XSF,
			XSFIND,
			INSTALLMENTNO,
			ENDORSETIMES,
			RENEWEDTIME,
			REGISTTIMES,
			CLAIMSTIMES,
			PRINTTIMES,
			ISSENDSMS,
			ISSENDEMAIL,
			REMARK,
			VALIDIND,
			FLAG,
			INPUTDATE,
			UPDATESYSDATE,
			SETTLESTATUS,
			SETTLEFEE,
			SETTLENO
		) values (
			#{id},
			#{proposalNo},
			#{policyNo},
			#{endorserialno},
			#{endorseqno},
			#{endorNo},
			#{language},
			#{productcode},
			#{groupind},
			#{insurancecompanycode},
			#{surveyind},
			#{flowid},
			#{companycode},
			#{companyname},
			#{projectmanagercode},
			#{projectmanagername},
			#{lastModifyManagerCode},
			#{lastModifyManagerName},
			#{startDate},
			#{endDate},
			#{validDate},
			#{appliCode},
			#{appliName},
			#{insuredCode},
			#{insuredName},
			#{currency},
			#{yearpremium},
			#{suminsured},
			#{sumgrosspremium},
			#{sumnetpremium},
			#{sumuwpremium},
			#{notaxpremium},
			#{taxamount},
			#{changeinsured},
			#{changegrosspremium},
			#{changenetpremium},
			#{changeuwpremium},
			#{changenotaxpremium},
			#{changetaxamount},
			#{currencycny},
			#{suminsuredcny},
			#{sumgrosspremiumcny},
			#{sumuwpremiumcny},
			#{notaxpremiumcny},
			#{taxamountcny},
			#{changeinsuredcny},
			#{changegrosspremiumcny},
			#{changeuwpremiumcny},
			#{changenotaxpremiumcny},
			#{changetaxamountcny},
			#{uwYear},
			#{acceptdate},
			#{underwriteind},
			#{underWriteEndDate},
			#{surrenderind},
			#{cancelind},
			#{endind},
			#{codind},
			#{calculatetype},
			#{coinsind},
			#{agentrate},
			#{notaxagentrate},
			#{commission},
			#{changecommission},
			#{fsh},
			#{xsf},
			#{xsfind},
			#{installmentno},
			#{endorseTimes},
			#{renewedtime},
			#{registTimes},
			#{claimstimes},
			#{printtimes},
			#{issendsms},
			#{issendemail},
			#{remark},
			#{validind},
			#{flag},
			#{inputDate},
			#{updatesysdate},
			#{settleStatus},
			#{settlefee},
			#{settleNo}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicycopymain.po.Gupolicycopymain">
		insert into GUPOLICYCOPYMAIN
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="proposalNo != null" >
				PROPOSALNO,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="endorserialno != null" >
				ENDORSERIALNO,
			</if>
			<if test="endorseqno != null" >
				ENDORSEQNO,
			</if>
			<if test="endorNo != null" >
				ENDORNO,
			</if>
			<if test="language != null" >
				LANGUAGE,
			</if>
			<if test="productcode != null" >
				PRODUCTCODE,
			</if>
			<if test="groupind != null" >
				GROUPIND,
			</if>
			<if test="insurancecompanycode != null" >
				INSURANCECOMPANYCODE,
			</if>
			<if test="surveyind != null" >
				SURVEYIND,
			</if>
			<if test="flowid != null" >
				FLOWID,
			</if>
			<if test="companycode != null" >
				COMPANYCODE,
			</if>
			<if test="companyname != null" >
				COMPANYNAME,
			</if>
			<if test="projectmanagercode != null" >
				PROJECTMANAGERCODE,
			</if>
			<if test="projectmanagername != null" >
				PROJECTMANAGERNAME,
			</if>
			<if test="lastModifyManagerCode != null" >
				LASTMODIFYMANAGERCODE,
			</if>
			<if test="lastModifyManagerName != null" >
				LASTMODIFYMANAGERNAME,
			</if>
			<if test="startDate != null" >
				STARTDATE,
			</if>
			<if test="endDate != null" >
				ENDDATE,
			</if>
			<if test="validDate != null" >
				VALIDDATE,
			</if>
			<if test="appliCode != null" >
				APPLICODE,
			</if>
			<if test="appliName != null" >
				APPLINAME,
			</if>
			<if test="insuredCode != null" >
				INSUREDCODE,
			</if>
			<if test="insuredName != null" >
				INSUREDNAME,
			</if>
			<if test="currency != null" >
				CURRENCY,
			</if>
			<if test="yearpremium != null" >
				YEARPREMIUM,
			</if>
			<if test="suminsured != null" >
				SUMINSURED,
			</if>
			<if test="sumgrosspremium != null" >
				SUMGROSSPREMIUM,
			</if>
			<if test="sumnetpremium != null" >
				SUMNETPREMIUM,
			</if>
			<if test="sumuwpremium != null" >
				SUMUWPREMIUM,
			</if>
			<if test="notaxpremium != null" >
				NOTAXPREMIUM,
			</if>
			<if test="taxamount != null" >
				TAXAMOUNT,
			</if>
			<if test="changeinsured != null" >
				CHANGEINSURED,
			</if>
			<if test="changegrosspremium != null" >
				CHANGEGROSSPREMIUM,
			</if>
			<if test="changenetpremium != null" >
				CHANGENETPREMIUM,
			</if>
			<if test="changeuwpremium != null" >
				CHANGEUWPREMIUM,
			</if>
			<if test="changenotaxpremium != null" >
				CHANGENOTAXPREMIUM,
			</if>
			<if test="changetaxamount != null" >
				CHANGETAXAMOUNT,
			</if>
			<if test="currencycny != null" >
				CURRENCYCNY,
			</if>
			<if test="suminsuredcny != null" >
				SUMINSUREDCNY,
			</if>
			<if test="sumgrosspremiumcny != null" >
				SUMGROSSPREMIUMCNY,
			</if>
			<if test="sumuwpremiumcny != null" >
				SUMUWPREMIUMCNY,
			</if>
			<if test="notaxpremiumcny != null" >
				NOTAXPREMIUMCNY,
			</if>
			<if test="taxamountcny != null" >
				TAXAMOUNTCNY,
			</if>
			<if test="changeinsuredcny != null" >
				CHANGEINSUREDCNY,
			</if>
			<if test="changegrosspremiumcny != null" >
				CHANGEGROSSPREMIUMCNY,
			</if>
			<if test="changeuwpremiumcny != null" >
				CHANGEUWPREMIUMCNY,
			</if>
			<if test="changenotaxpremiumcny != null" >
				CHANGENOTAXPREMIUMCNY,
			</if>
			<if test="changetaxamountcny != null" >
				CHANGETAXAMOUNTCNY,
			</if>
			<if test="uwYear != null" >
				UWYEAR,
			</if>
			<if test="acceptdate != null" >
				ACCEPTDATE,
			</if>
			<if test="underwriteind != null" >
				UNDERWRITEIND,
			</if>
			<if test="underWriteEndDate != null" >
				UNDERWRITEENDDATE,
			</if>
			<if test="surrenderind != null" >
				SURRENDERIND,
			</if>
			<if test="cancelind != null" >
				CANCELIND,
			</if>
			<if test="endind != null" >
				ENDIND,
			</if>
			<if test="codind != null" >
				CODIND,
			</if>
			<if test="calculatetype != null" >
				CALCULATETYPE,
			</if>
			<if test="coinsind != null" >
				COINSIND,
			</if>
			<if test="agentrate != null" >
				AGENTRATE,
			</if>
			<if test="notaxagentrate != null" >
				NOTAXAGENTRATE,
			</if>
			<if test="commission != null" >
				COMMISSION,
			</if>
			<if test="changecommission != null" >
				CHANGECOMMISSION,
			</if>
			<if test="fsh != null" >
				FSH,
			</if>
			<if test="xsf != null" >
				XSF,
			</if>
			<if test="xsfind != null" >
				XSFIND,
			</if>
			<if test="installmentno != null" >
				INSTALLMENTNO,
			</if>
			<if test="endorseTimes != null" >
				ENDORSETIMES,
			</if>
			<if test="renewedtime != null" >
				RENEWEDTIME,
			</if>
			<if test="registTimes != null" >
				REGISTTIMES,
			</if>
			<if test="claimstimes != null" >
				CLAIMSTIMES,
			</if>
			<if test="printtimes != null" >
				PRINTTIMES,
			</if>
			<if test="issendsms != null" >
				ISSENDSMS,
			</if>
			<if test="issendemail != null" >
				ISSENDEMAIL,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="validind != null" >
				VALIDIND,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE,
			</if>
			<if test="settleStatus != null" >
				SETTLESTATUS,
			</if>
			<if test="settlefee != null" >
				SETTLEFEE,
			</if>
			<if test="settleNo != null" >
				SETTLENO
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="proposalNo != null" >
				#{proposalNo},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="endorserialno != null" >
				#{endorserialno},
			</if>
			<if test="endorseqno != null" >
				#{endorseqno},
			</if>
			<if test="endorNo != null" >
				#{endorNo},
			</if>
			<if test="language != null" >
				#{language},
			</if>
			<if test="productcode != null" >
				#{productcode},
			</if>
			<if test="groupind != null" >
				#{groupind},
			</if>
			<if test="insurancecompanycode != null" >
				#{insurancecompanycode},
			</if>
			<if test="surveyind != null" >
				#{surveyind},
			</if>
			<if test="flowid != null" >
				#{flowid},
			</if>
			<if test="companycode != null" >
				#{companycode},
			</if>
			<if test="companyname != null" >
				#{companyname},
			</if>
			<if test="projectmanagercode != null" >
				#{projectmanagercode},
			</if>
			<if test="projectmanagername != null" >
				#{projectmanagername},
			</if>
			<if test="lastModifyManagerCode != null" >
				#{lastModifyManagerCode},
			</if>
			<if test="lastModifyManagerName != null" >
				#{lastModifyManagerName},
			</if>			
			<if test="startDate != null" >
				#{startDate},
			</if>
			<if test="endDate != null" >
				#{endDate},
			</if>
			<if test="validDate != null" >
				#{validDate},
			</if>
			<if test="appliCode != null" >
				#{appliCode},
			</if>
			<if test="appliName != null" >
				#{appliName},
			</if>
			<if test="insuredCode != null" >
				#{insuredCode},
			</if>
			<if test="insuredName != null" >
				#{insuredName},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
			<if test="yearpremium != null" >
				#{yearpremium},
			</if>
			<if test="suminsured != null" >
				#{suminsured},
			</if>
			<if test="sumgrosspremium != null" >
				#{sumgrosspremium},
			</if>
			<if test="sumnetpremium != null" >
				#{sumnetpremium},
			</if>
			<if test="sumuwpremium != null" >
				#{sumuwpremium},
			</if>
			<if test="notaxpremium != null" >
				#{notaxpremium},
			</if>
			<if test="taxamount != null" >
				#{taxamount},
			</if>
			<if test="changeinsured != null" >
				#{changeinsured},
			</if>
			<if test="changegrosspremium != null" >
				#{changegrosspremium},
			</if>
			<if test="changenetpremium != null" >
				#{changenetpremium},
			</if>
			<if test="changeuwpremium != null" >
				#{changeuwpremium},
			</if>
			<if test="changenotaxpremium != null" >
				#{changenotaxpremium},
			</if>
			<if test="changetaxamount != null" >
				#{changetaxamount},
			</if>
			<if test="currencycny != null" >
				#{currencycny},
			</if>
			<if test="suminsuredcny != null" >
				#{suminsuredcny},
			</if>
			<if test="sumgrosspremiumcny != null" >
				#{sumgrosspremiumcny},
			</if>
			<if test="sumuwpremiumcny != null" >
				#{sumuwpremiumcny},
			</if>
			<if test="notaxpremiumcny != null" >
				#{notaxpremiumcny},
			</if>
			<if test="taxamountcny != null" >
				#{taxamountcny},
			</if>
			<if test="changeinsuredcny != null" >
				#{changeinsuredcny},
			</if>
			<if test="changegrosspremiumcny != null" >
				#{changegrosspremiumcny},
			</if>
			<if test="changeuwpremiumcny != null" >
				#{changeuwpremiumcny},
			</if>
			<if test="changenotaxpremiumcny != null" >
				#{changenotaxpremiumcny},
			</if>
			<if test="changetaxamountcny != null" >
				#{changetaxamountcny},
			</if>
			<if test="uwYear != null" >
				#{uwYear},
			</if>
			<if test="acceptdate != null" >
				#{acceptdate},
			</if>
			<if test="underwriteind != null" >
				#{underwriteind},
			</if>
			<if test="underWriteEndDate != null" >
				#{underWriteEndDate},
			</if>
			<if test="surrenderind != null" >
				#{surrenderind},
			</if>
			<if test="cancelind != null" >
				#{cancelind},
			</if>
			<if test="endind != null" >
				#{endind},
			</if>
			<if test="codind != null" >
				#{codind},
			</if>
			<if test="calculatetype != null" >
				#{calculatetype},
			</if>
			<if test="coinsind != null" >
				#{coinsind},
			</if>
			<if test="agentrate != null" >
				#{agentrate},
			</if>
			<if test="notaxagentrate != null" >
				#{notaxagentrate},
			</if>
			<if test="commission != null" >
				#{commission},
			</if>
			<if test="changecommission != null" >
				#{changecommission},
			</if>
			<if test="fsh != null" >
				#{fsh},
			</if>
			<if test="xsf != null" >
				#{xsf},
			</if>
			<if test="xsfind != null" >
				#{xsfind},
			</if>
			<if test="installmentno != null" >
				#{installmentno},
			</if>
			<if test="endorseTimes != null" >
				#{endorseTimes},
			</if>
			<if test="renewedtime != null" >
				#{renewedtime},
			</if>
			<if test="registTimes != null" >
				#{registTimes},
			</if>
			<if test="claimstimes != null" >
				#{claimstimes},
			</if>
			<if test="printtimes != null" >
				#{printtimes},
			</if>
			<if test="issendsms != null" >
				#{issendsms},
			</if>
			<if test="issendemail != null" >
				#{issendemail},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="validind != null" >
				#{validind},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate},
			</if>
			<if test="settleStatus != null" >
				#{settleStatus},
			</if>
			<if test="settlefee != null" >
				#{settlefee},
			</if>
			<if test="settleNo != null" >
				#{settleNo}
			</if>
		</trim>
	</insert>

    <!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicycopymain.po.Gupolicycopymain">
		update GUPOLICYCOPYMAIN
		<set>
			<if test="proposalNo != null" >
				PROPOSALNO=#{proposalNo},
			</if>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="endorserialno != null" >
				ENDORSERIALNO=#{endorserialno},
			</if>
			<if test="endorseqno != null" >
				ENDORSEQNO=#{endorseqno},
			</if>
			<if test="endorNo != null" >
				ENDORNO=#{endorNo},
			</if>
			<if test="language != null" >
				LANGUAGE=#{language},
			</if>
			<if test="productcode != null" >
				PRODUCTCODE=#{productcode},
			</if>
			<if test="groupind != null" >
				GROUPIND=#{groupind},
			</if>
			<if test="insurancecompanycode != null" >
				INSURANCECOMPANYCODE=#{insurancecompanycode},
			</if>
			<if test="surveyind != null" >
				SURVEYIND=#{surveyind},
			</if>
			<if test="flowid != null" >
				FLOWID=#{flowid},
			</if>
			<if test="companycode != null" >
				COMPANYCODE=#{companycode},
			</if>
			<if test="companyname != null" >
				COMPANYNAME=#{companyname},
			</if>
			<if test="projectmanagercode != null" >
				PROJECTMANAGERCODE=#{projectmanagercode},
			</if>
			<if test="projectmanagername != null" >
				PROJECTMANAGERNAME=#{projectmanagername},
			</if>
			<if test="startDate != null" >
				STARTDATE=#{startDate},
			</if>
			<if test="endDate != null" >
				ENDDATE=#{endDate},
			</if>
			<if test="validDate != null" >
				VALIDDATE=#{validDate},
			</if>
			<if test="appliCode != null" >
				APPLICODE=#{appliCode},
			</if>
			<if test="appliName != null" >
				APPLINAME=#{appliName},
			</if>
			<if test="insuredCode != null" >
				INSUREDCODE=#{insuredCode},
			</if>
			<if test="insuredName != null" >
				INSUREDNAME=#{insuredName},
			</if>
			<if test="currency != null" >
				CURRENCY=#{currency},
			</if>
			<if test="yearpremium != null" >
				YEARPREMIUM=#{yearpremium},
			</if>
			<if test="suminsured != null" >
				SUMINSURED=#{suminsured},
			</if>
			<if test="sumgrosspremium != null" >
				SUMGROSSPREMIUM=#{sumgrosspremium},
			</if>
			<if test="sumnetpremium != null" >
				SUMNETPREMIUM=#{sumnetpremium},
			</if>
			<if test="sumuwpremium != null" >
				SUMUWPREMIUM=#{sumuwpremium},
			</if>
			<if test="notaxpremium != null" >
				NOTAXPREMIUM=#{notaxpremium},
			</if>
			<if test="taxamount != null" >
				TAXAMOUNT=#{taxamount},
			</if>
			<if test="changeinsured != null" >
				CHANGEINSURED=#{changeinsured},
			</if>
			<if test="changegrosspremium != null" >
				CHANGEGROSSPREMIUM=#{changegrosspremium},
			</if>
			<if test="changenetpremium != null" >
				CHANGENETPREMIUM=#{changenetpremium},
			</if>
			<if test="changeuwpremium != null" >
				CHANGEUWPREMIUM=#{changeuwpremium},
			</if>
			<if test="changenotaxpremium != null" >
				CHANGENOTAXPREMIUM=#{changenotaxpremium},
			</if>
			<if test="changetaxamount != null" >
				CHANGETAXAMOUNT=#{changetaxamount},
			</if>
			<if test="currencycny != null" >
				CURRENCYCNY=#{currencycny},
			</if>
			<if test="suminsuredcny != null" >
				SUMINSUREDCNY=#{suminsuredcny},
			</if>
			<if test="sumgrosspremiumcny != null" >
				SUMGROSSPREMIUMCNY=#{sumgrosspremiumcny},
			</if>
			<if test="sumuwpremiumcny != null" >
				SUMUWPREMIUMCNY=#{sumuwpremiumcny},
			</if>
			<if test="notaxpremiumcny != null" >
				NOTAXPREMIUMCNY=#{notaxpremiumcny},
			</if>
			<if test="taxamountcny != null" >
				TAXAMOUNTCNY=#{taxamountcny},
			</if>
			<if test="changeinsuredcny != null" >
				CHANGEINSUREDCNY=#{changeinsuredcny},
			</if>
			<if test="changegrosspremiumcny != null" >
				CHANGEGROSSPREMIUMCNY=#{changegrosspremiumcny},
			</if>
			<if test="changeuwpremiumcny != null" >
				CHANGEUWPREMIUMCNY=#{changeuwpremiumcny},
			</if>
			<if test="changenotaxpremiumcny != null" >
				CHANGENOTAXPREMIUMCNY=#{changenotaxpremiumcny},
			</if>
			<if test="changetaxamountcny != null" >
				CHANGETAXAMOUNTCNY=#{changetaxamountcny},
			</if>
			<if test="uwYear != null" >
				UWYEAR=#{uwYear},
			</if>
			<if test="acceptdate != null" >
				ACCEPTDATE=#{acceptdate},
			</if>
			<if test="underwriteind != null" >
				UNDERWRITEIND=#{underwriteind},
			</if>
			<if test="underWriteEndDate != null" >
				UNDERWRITEENDDATE=#{underWriteEndDate},
			</if>
			<if test="surrenderind != null" >
				SURRENDERIND=#{surrenderind},
			</if>
			<if test="cancelind != null" >
				CANCELIND=#{cancelind},
			</if>
			<if test="endind != null" >
				ENDIND=#{endind},
			</if>
			<if test="codind != null" >
				CODIND=#{codind},
			</if>
			<if test="calculatetype != null" >
				CALCULATETYPE=#{calculatetype},
			</if>
			<if test="coinsind != null" >
				COINSIND=#{coinsind},
			</if>
			<if test="agentrate != null" >
				AGENTRATE=#{agentrate},
			</if>
			<if test="notaxagentrate != null" >
				NOTAXAGENTRATE=#{notaxagentrate},
			</if>
			<if test="commission != null" >
				COMMISSION=#{commission},
			</if>
			<if test="changecommission != null" >
				CHANGECOMMISSION=#{changecommission},
			</if>
			<if test="fsh != null" >
				FSH=#{fsh},
			</if>
			<if test="xsf != null" >
				XSF=#{xsf},
			</if>
			<if test="xsfind != null" >
				XSFIND=#{xsfind},
			</if>
			<if test="installmentno != null" >
				INSTALLMENTNO=#{installmentno},
			</if>
			<if test="endorseTimes != null" >
				ENDORSETIMES=#{endorseTimes},
			</if>
			<if test="renewedtime != null" >
				RENEWEDTIME=#{renewedtime},
			</if>
			<if test="registTimes != null" >
				REGISTTIMES=#{registTimes},
			</if>
			<if test="claimstimes != null" >
				CLAIMSTIMES=#{claimstimes},
			</if>
			<if test="printtimes != null" >
				PRINTTIMES=#{printtimes},
			</if>
			<if test="issendsms != null" >
				ISSENDSMS=#{issendsms},
			</if>
			<if test="issendemail != null" >
				ISSENDEMAIL=#{issendemail},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="validind != null" >
				VALIDIND=#{validind},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
			<if test="settleStatus != null" >
				SETTLESTATUS=#{settleStatus},
			</if>
			<if test="settlefee != null" >
				SETTLEFEE=#{settlefee},
			</if>
			<if test="settleNo != null" >
				SETTLENO=#{settleNo},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicycopymain.po.Gupolicycopymain">
		update GUPOLICYCOPYMAIN set
			PROPOSALNO=#{proposalNo},
			POLICYNO=#{policyNo},
			ENDORSERIALNO=#{endorserialno},
			ENDORSEQNO=#{endorseqno},
			ENDORNO=#{endorNo},
			LANGUAGE=#{language},
			PRODUCTCODE=#{productcode},
			GROUPIND=#{groupind},
			INSURANCECOMPANYCODE=#{insurancecompanycode},
			SURVEYIND=#{surveyind},
			FLOWID=#{flowid},
			COMPANYCODE=#{companycode},
			COMPANYNAME=#{companyname},
			PROJECTMANAGERCODE=#{projectmanagercode},
			PROJECTMANAGERNAME=#{projectmanagername},
			STARTDATE=#{startDate},
			ENDDATE=#{endDate},
			VALIDDATE=#{validDate},
			APPLICODE=#{appliCode},
			APPLINAME=#{appliName},
			INSUREDCODE=#{insuredCode},
			INSUREDNAME=#{insuredName},
			CURRENCY=#{currency},
			YEARPREMIUM=#{yearpremium},
			SUMINSURED=#{suminsured},
			SUMGROSSPREMIUM=#{sumgrosspremium},
			SUMNETPREMIUM=#{sumnetpremium},
			SUMUWPREMIUM=#{sumuwpremium},
			NOTAXPREMIUM=#{notaxpremium},
			TAXAMOUNT=#{taxamount},
			CHANGEINSURED=#{changeinsured},
			CHANGEGROSSPREMIUM=#{changegrosspremium},
			CHANGENETPREMIUM=#{changenetpremium},
			CHANGEUWPREMIUM=#{changeuwpremium},
			CHANGENOTAXPREMIUM=#{changenotaxpremium},
			CHANGETAXAMOUNT=#{changetaxamount},
			CURRENCYCNY=#{currencycny},
			SUMINSUREDCNY=#{suminsuredcny},
			SUMGROSSPREMIUMCNY=#{sumgrosspremiumcny},
			SUMUWPREMIUMCNY=#{sumuwpremiumcny},
			NOTAXPREMIUMCNY=#{notaxpremiumcny},
			TAXAMOUNTCNY=#{taxamountcny},
			CHANGEINSUREDCNY=#{changeinsuredcny},
			CHANGEGROSSPREMIUMCNY=#{changegrosspremiumcny},
			CHANGEUWPREMIUMCNY=#{changeuwpremiumcny},
			CHANGENOTAXPREMIUMCNY=#{changenotaxpremiumcny},
			CHANGETAXAMOUNTCNY=#{changetaxamountcny},
			UWYEAR=#{uwYear},
			ACCEPTDATE=#{acceptdate},
			UNDERWRITEIND=#{underwriteind},
			UNDERWRITEENDDATE=#{underWriteEndDate},
			SURRENDERIND=#{surrenderind},
			CANCELIND=#{cancelind},
			ENDIND=#{endind},
			CODIND=#{codind},
			CALCULATETYPE=#{calculatetype},
			COINSIND=#{coinsind},
			AGENTRATE=#{agentrate},
			NOTAXAGENTRATE=#{notaxagentrate},
			COMMISSION=#{commission},
			CHANGECOMMISSION=#{changecommission},
			FSH=#{fsh},
			XSF=#{xsf},
			XSFIND=#{xsfind},
			INSTALLMENTNO=#{installmentno},
			ENDORSETIMES=#{endorseTimes},
			RENEWEDTIME=#{renewedtime},
			REGISTTIMES=#{registTimes},
			CLAIMSTIMES=#{claimstimes},
			PRINTTIMES=#{printtimes},
			ISSENDSMS=#{issendsms},
			ISSENDEMAIL=#{issendemail},
			REMARK=#{remark},
			VALIDIND=#{validind},
			FLAG=#{flag},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
			SETTLESTATUS=#{settleStatus},
			SETTLEFEE=#{settlefee},
			SETTLENO=#{settleNo},
		where ID = #{id}	</update>

	<insert id="batchInsert">
		INSERT ALL
		<foreach collection="list" item="item" separator="">
			INTO GUPOLICYCOPYMAIN VALUES
			(#{item.id}, #{item.proposalNo}, #{item.policyNo}, #{item.endorserialno}, #{item.endorseqno},
			#{item.endorNo}, #{item.language}, #{item.productcode}, #{item.groupind}, #{item.insurancecompanycode},
			#{item.surveyind}, #{item.flowid}, #{item.companycode}, #{item.companyname}, #{item.projectmanagercode},
			#{item.projectmanagername}, #{item.startDate}, #{item.endDate}, #{item.validDate}, #{item.appliCode},
			#{item.appliName}, #{item.insuredCode}, #{item.insuredName}, #{item.currency}, #{item.yearpremium},
			#{item.suminsured}, #{item.sumgrosspremium}, #{item.sumnetpremium}, #{item.sumuwpremium}, #{item.notaxpremium},
			#{item.taxamount}, #{item.changeinsured}, #{item.changegrosspremium}, #{item.changenetpremium}, #{item.changeuwpremium},
			#{item.changenotaxpremium}, #{item.changetaxamount}, #{item.currencycny}, #{item.suminsuredcny}, #{item.sumgrosspremiumcny},
			#{item.sumuwpremiumcny}, #{item.notaxpremiumcny}, #{item.taxamountcny}, #{item.changeinsuredcny}, #{item.changegrosspremiumcny},
			#{item.changeuwpremiumcny}, #{item.changenotaxpremiumcny}, #{item.changetaxamountcny}, #{item.uwYear}, #{item.acceptdate},
			#{item.underwriteind}, #{item.underWriteEndDate}, #{item.surrenderind}, #{item.cancelind}, #{item.endind},
			#{item.codind}, #{item.calculatetype}, #{item.coinsind}, #{item.agentrate}, #{item.notaxagentrate},
			#{item.commission}, #{item.changecommission}, #{item.fsh}, #{item.xsf}, #{item.xsfind}, #{item.installmentno},
			#{item.endorseTimes}, #{item.renewedtime}, #{item.registTimes}, #{item.claimstimes}, #{item.printtimes}, #{item.issendsms},
			#{item.issendemail}, #{item.remark}, #{item.validind}, #{item.flag}, #{item.inputDate}, #{item.updatesysdate}, #{item.settleStatus},
			#{item.settlefee}, #{item.settleNo}, #{item.lastModifyManagerCode}, #{item.lastModifyManagerName})
		</foreach>
		select 1 from dual
	</insert>
</mapper>
