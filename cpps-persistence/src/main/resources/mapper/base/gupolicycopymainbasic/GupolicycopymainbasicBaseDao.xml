<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicycopymainbasic.dao.GupolicycopymainbasicDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicycopymainbasic.po.Gupolicycopymainbasic">
		<id column="ID" property="id" />
		<result column="POLICYNO" property="policyNo" />
		<result column="ENDORNO" property="endorNo" />
		<result column="PRODUCTCODE" property="productcode" />
		<result column="SHAREHOLDERFLAG" property="shareHolderFlag" />
		<result column="INSURANCECOMPANYCODE" property="insurancecompanycode" />
		<result column="BUSINESSCHANNEL" property="businesschannel" />
		<result column="CHANNELDETAILCODE" property="channeldetailcode" />
		<result column="CHANNELTIP" property="channeltip" />
		<result column="CHANNELFLAG" property="channelflag" />
		<result column="INTERMEDIARYCODE" property="intermediarycode" />
		<result column="AGREEMENTNO" property="agreementNo" />
		<result column="BUSINESSSOURCE" property="businessSource" />
		<result column="BUSINESSTYPE" property="businessType" />
		<result column="ISSUEDATE" property="issueDate" />
		<result column="ISSUECOMPANY" property="issuecompany" />
		<result column="ISSUEPLACE" property="issueplace" />
		<result column="OPERATORCODE" property="operatorcode" />
		<result column="OPERATEDATE" property="operateDate" />
		<result column="ARGUESOLUTION" property="argueSolution" />
		<result column="ARBITORYNAME" property="arbitoryname" />
		<result column="DOMESTICIND" property="domesticind" />
		<result column="AGRICULTUREFLAG" property="agricultureflag" />
		<result column="MONEYSUSPICIOUSIND" property="moneysuspiciousind" />
		<result column="COMINSUREIND" property="cominsureind" />
		<result column="ISDIFFERENTPLACE" property="isdifferentplace" />
		<result column="POLICYSTYLE" property="policystyle" />
		<result column="ISFARMING" property="isfarming" />
		<result column="RENEWIND" property="renewind" />
		<result column="RENEWEDIND" property="renewedind" />
		<result column="RENEWEDTIME" property="renewedtime" />
		<result column="RENEWALNO" property="renewalno" />
		<result column="REPLACEDPOLICYNO" property="replacedpolicyno" />
		<result column="AUTORENEWIND" property="autorenewind" />
		<result column="MULTIRISKIND" property="multiriskind" />
		<result column="RISKAPPLYTYPE" property="riskapplytype" />
		<result column="PRODUCTEDITION" property="productedition" />
		<result column="PRODUCTEDITIONNAME" property="producteditionname" />
		<result column="COMPENSATIONTYPE" property="compensationtype" />
		<result column="DISCOVERSTARTDATE" property="discoverstartdate" />
		<result column="DISCOVERENDDATE" property="discoverenddate" />
		<result column="JUDICALCODE" property="judicalCode" />
		<result column="GEOGRAPHICALAREA" property="geographicalarea" />
		<result column="GEOGRAPHICALAREADESC" property="geographicalareadesc" />
		<result column="UPLOADIND" property="uploadind" />
		<result column="NOMINATIVEIND" property="nominativeind" />
		<result column="BUSINESSIND" property="businessind" />
		<result column="BUSINESSMODE" property="businessmode" />
		<result column="SALESTEAMCODE" property="salesteamcode" />
		<result column="SALESTEAMNAME" property="salesteamname" />
		<result column="SALESMANCODE" property="salesmancode" />
		<result column="SALESMANNAME" property="salesmanname" />
		<result column="SALESMANREGISTERNO" property="salesmanregisterno" />
		<result column="SELLERNAME" property="sellername" />
		<result column="SELLERREGISTERNO" property="sellerregisterno" />
		<result column="INTERSALESMANREGISTERNO" property="intersalesmanregisterno" />
		<result column="INTERSALESMANCODE" property="intersalesmancode" />
		<result column="TEAMMANAGERCODE" property="teammanagercode" />
		<result column="TEAMMANAGERNAME" property="teammanagername" />
		<result column="UNDERWRITECODE" property="underWriteCode" />
		<result column="UNDERWRITENAME" property="underWriteName" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		POLICYNO,
		ENDORNO,
		PRODUCTCODE,
		SHAREHOLDERFLAG,
		INSURANCECOMPANYCODE,
		BUSINESSCHANNEL,
		CHANNELDETAILCODE,
		CHANNELTIP,
		CHANNELFLAG,
		INTERMEDIARYCODE,
		AGREEMENTNO,
		BUSINESSSOURCE,
		BUSINESSTYPE,
		ISSUEDATE,
		ISSUECOMPANY,
		ISSUEPLACE,
		OPERATORCODE,
		OPERATEDATE,
		ARGUESOLUTION,
		ARBITORYNAME,
		DOMESTICIND,
		AGRICULTUREFLAG,
		MONEYSUSPICIOUSIND,
		COMINSUREIND,
		ISDIFFERENTPLACE,
		POLICYSTYLE,
		ISFARMING,
		RENEWIND,
		RENEWEDIND,
		RENEWEDTIME,
		RENEWALNO,
		REPLACEDPOLICYNO,
		AUTORENEWIND,
		MULTIRISKIND,
		RISKAPPLYTYPE,
		PRODUCTEDITION,
		PRODUCTEDITIONNAME,
		COMPENSATIONTYPE,
		DISCOVERSTARTDATE,
		DISCOVERENDDATE,
		JUDICALCODE,
		GEOGRAPHICALAREA,
		GEOGRAPHICALAREADESC,
		UPLOADIND,
		NOMINATIVEIND,
		BUSINESSIND,
		BUSINESSMODE,
		SALESTEAMCODE,
		SALESTEAMNAME,
		SALESMANCODE,
		SALESMANNAME,
		SALESMANREGISTERNO,
		SELLERNAME,
		SELLERREGISTERNO,
		INTERSALESMANREGISTERNO,
		INTERSALESMANCODE,
		TEAMMANAGERCODE,
		TEAMMANAGERNAME,
		UNDERWRITECODE,
		UNDERWRITENAME,
		INPUTDATE,
		UPDATESYSDATE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="endorNo != null and endorNo != ''" >
			and ENDORNO = #{endorNo}
		</if>
		<if test="productcode != null and productcode != ''" >
			and PRODUCTCODE = #{productcode}
		</if>
		<if test="shareHolderFlag != null and shareHolderFlag != ''" >
			and SHAREHOLDERFLAG = #{shareHolderFlag}
		</if>
		<if test="insurancecompanycode != null and insurancecompanycode != ''" >
			and INSURANCECOMPANYCODE = #{insurancecompanycode}
		</if>
		<if test="businesschannel != null and businesschannel != ''" >
			and BUSINESSCHANNEL = #{businesschannel}
		</if>
		<if test="channeldetailcode != null and channeldetailcode != ''" >
			and CHANNELDETAILCODE = #{channeldetailcode}
		</if>
		<if test="channeltip != null and channeltip != ''" >
			and CHANNELTIP = #{channeltip}
		</if>
		<if test="channelflag != null and channelflag != ''" >
			and CHANNELFLAG = #{channelflag}
		</if>
		<if test="intermediarycode != null and intermediarycode != ''" >
			and INTERMEDIARYCODE = #{intermediarycode}
		</if>
		<if test="agreementNo != null and agreementNo != ''" >
			and AGREEMENTNO = #{agreementNo}
		</if>
		<if test="businessSource != null and businessSource != ''" >
			and BUSINESSSOURCE = #{businessSource}
		</if>
		<if test="businessType != null and businessType != ''" >
			and BUSINESSTYPE = #{businessType}
		</if>
		<if test="issueDate != null and issueDate != ''" >
			and ISSUEDATE = #{issueDate}
		</if>
		<if test="issuecompany != null and issuecompany != ''" >
			and ISSUECOMPANY = #{issuecompany}
		</if>
		<if test="issueplace != null and issueplace != ''" >
			and ISSUEPLACE = #{issueplace}
		</if>
		<if test="operatorcode != null and operatorcode != ''" >
			and OPERATORCODE = #{operatorcode}
		</if>
		<if test="operateDate != null and operateDate != ''" >
			and OPERATEDATE = #{operateDate}
		</if>
		<if test="argueSolution != null and argueSolution != ''" >
			and ARGUESOLUTION = #{argueSolution}
		</if>
		<if test="arbitoryname != null and arbitoryname != ''" >
			and ARBITORYNAME = #{arbitoryname}
		</if>
		<if test="domesticind != null and domesticind != ''" >
			and DOMESTICIND = #{domesticind}
		</if>
		<if test="agricultureflag != null and agricultureflag != ''" >
			and AGRICULTUREFLAG = #{agricultureflag}
		</if>
		<if test="moneysuspiciousind != null and moneysuspiciousind != ''" >
			and MONEYSUSPICIOUSIND = #{moneysuspiciousind}
		</if>
		<if test="cominsureind != null and cominsureind != ''" >
			and COMINSUREIND = #{cominsureind}
		</if>
		<if test="isdifferentplace != null and isdifferentplace != ''" >
			and ISDIFFERENTPLACE = #{isdifferentplace}
		</if>
		<if test="policystyle != null and policystyle != ''" >
			and POLICYSTYLE = #{policystyle}
		</if>
		<if test="isfarming != null and isfarming != ''" >
			and ISFARMING = #{isfarming}
		</if>
		<if test="renewind != null and renewind != ''" >
			and RENEWIND = #{renewind}
		</if>
		<if test="renewedind != null and renewedind != ''" >
			and RENEWEDIND = #{renewedind}
		</if>
		<if test="renewedtime != null and renewedtime != ''" >
			and RENEWEDTIME = #{renewedtime}
		</if>
		<if test="renewalno != null and renewalno != ''" >
			and RENEWALNO = #{renewalno}
		</if>
		<if test="replacedpolicyno != null and replacedpolicyno != ''" >
			and REPLACEDPOLICYNO = #{replacedpolicyno}
		</if>
		<if test="autorenewind != null and autorenewind != ''" >
			and AUTORENEWIND = #{autorenewind}
		</if>
		<if test="multiriskind != null and multiriskind != ''" >
			and MULTIRISKIND = #{multiriskind}
		</if>
		<if test="riskapplytype != null and riskapplytype != ''" >
			and RISKAPPLYTYPE = #{riskapplytype}
		</if>
		<if test="productedition != null and productedition != ''" >
			and PRODUCTEDITION = #{productedition}
		</if>
		<if test="producteditionname != null and producteditionname != ''" >
			and PRODUCTEDITIONNAME = #{producteditionname}
		</if>
		<if test="compensationtype != null and compensationtype != ''" >
			and COMPENSATIONTYPE = #{compensationtype}
		</if>
		<if test="discoverstartdate != null and discoverstartdate != ''" >
			and DISCOVERSTARTDATE = #{discoverstartdate}
		</if>
		<if test="discoverenddate != null and discoverenddate != ''" >
			and DISCOVERENDDATE = #{discoverenddate}
		</if>
		<if test="judicalCode != null and judicalCode != ''" >
			and JUDICALCODE = #{judicalCode}
		</if>
		<if test="geographicalarea != null and geographicalarea != ''" >
			and GEOGRAPHICALAREA = #{geographicalarea}
		</if>
		<if test="geographicalareadesc != null and geographicalareadesc != ''" >
			and GEOGRAPHICALAREADESC = #{geographicalareadesc}
		</if>
		<if test="uploadind != null and uploadind != ''" >
			and UPLOADIND = #{uploadind}
		</if>
		<if test="nominativeind != null and nominativeind != ''" >
			and NOMINATIVEIND = #{nominativeind}
		</if>
		<if test="businessind != null and businessind != ''" >
			and BUSINESSIND = #{businessind}
		</if>
		<if test="businessmode != null and businessmode != ''" >
			and BUSINESSMODE = #{businessmode}
		</if>
		<if test="salesteamcode != null and salesteamcode != ''" >
			and SALESTEAMCODE = #{salesteamcode}
		</if>
		<if test="salesteamname != null and salesteamname != ''" >
			and SALESTEAMNAME = #{salesteamname}
		</if>
		<if test="salesmancode != null and salesmancode != ''" >
			and SALESMANCODE = #{salesmancode}
		</if>
		<if test="salesmanname != null and salesmanname != ''" >
			and SALESMANNAME = #{salesmanname}
		</if>
		<if test="salesmanregisterno != null and salesmanregisterno != ''" >
			and SALESMANREGISTERNO = #{salesmanregisterno}
		</if>
		<if test="sellername != null and sellername != ''" >
			and SELLERNAME = #{sellername}
		</if>
		<if test="sellerregisterno != null and sellerregisterno != ''" >
			and SELLERREGISTERNO = #{sellerregisterno}
		</if>
		<if test="intersalesmanregisterno != null and intersalesmanregisterno != ''" >
			and INTERSALESMANREGISTERNO = #{intersalesmanregisterno}
		</if>
		<if test="intersalesmancode != null and intersalesmancode != ''" >
			and INTERSALESMANCODE = #{intersalesmancode}
		</if>
		<if test="teammanagercode != null and teammanagercode != ''" >
			and TEAMMANAGERCODE = #{teammanagercode}
		</if>
		<if test="teammanagername != null and teammanagername != ''" >
			and TEAMMANAGERNAME = #{teammanagername}
		</if>
		<if test="underWriteCode != null and underWriteCode != ''" >
			and UNDERWRITECODE = #{underWriteCode}
		</if>
		<if test="underWriteName != null and underWriteName != ''" >
			and UNDERWRITENAME = #{underWriteName}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYMAINBASIC
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYMAINBASIC
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYCOPYMAINBASIC
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicycopymainbasic.po.Gupolicycopymainbasic">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYCOPYMAINBASIC
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYCOPYMAINBASIC
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicycopymainbasic.po.Gupolicycopymainbasic">
		insert into GUPOLICYCOPYMAINBASIC (
			ID,
			POLICYNO,
			ENDORNO,
			PRODUCTCODE,
			SHAREHOLDERFLAG,
			INSURANCECOMPANYCODE,
			BUSINESSCHANNEL,
			CHANNELDETAILCODE,
			CHANNELTIP,
			CHANNELFLAG,
			INTERMEDIARYCODE,
			AGREEMENTNO,
			BUSINESSSOURCE,
			BUSINESSTYPE,
			ISSUEDATE,
			ISSUECOMPANY,
			ISSUEPLACE,
			OPERATORCODE,
			OPERATEDATE,
			ARGUESOLUTION,
			ARBITORYNAME,
			DOMESTICIND,
			AGRICULTUREFLAG,
			MONEYSUSPICIOUSIND,
			COMINSUREIND,
			ISDIFFERENTPLACE,
			POLICYSTYLE,
			ISFARMING,
			RENEWIND,
			RENEWEDIND,
			RENEWEDTIME,
			RENEWALNO,
			REPLACEDPOLICYNO,
			AUTORENEWIND,
			MULTIRISKIND,
			RISKAPPLYTYPE,
			PRODUCTEDITION,
			PRODUCTEDITIONNAME,
			COMPENSATIONTYPE,
			DISCOVERSTARTDATE,
			DISCOVERENDDATE,
			JUDICALCODE,
			GEOGRAPHICALAREA,
			GEOGRAPHICALAREADESC,
			UPLOADIND,
			NOMINATIVEIND,
			BUSINESSIND,
			BUSINESSMODE,
			SALESTEAMCODE,
			SALESTEAMNAME,
			SALESMANCODE,
			SALESMANNAME,
			SALESMANREGISTERNO,
			SELLERNAME,
			SELLERREGISTERNO,
			INTERSALESMANREGISTERNO,
			INTERSALESMANCODE,
			TEAMMANAGERCODE,
			TEAMMANAGERNAME,
			UNDERWRITECODE,
			UNDERWRITENAME,
			INPUTDATE,
			UPDATESYSDATE
		) values (
			#{id},
			#{policyNo},
			#{endorNo},
			#{productcode},
			#{shareHolderFlag},
			#{insurancecompanycode},
			#{businesschannel},
			#{channeldetailcode},
			#{channeltip},
			#{channelflag},
			#{intermediarycode},
			#{agreementNo},
			#{businessSource},
			#{businessType},
			#{issueDate},
			#{issuecompany},
			#{issueplace},
			#{operatorcode},
			#{operateDate},
			#{argueSolution},
			#{arbitoryname},
			#{domesticind},
			#{agricultureflag},
			#{moneysuspiciousind},
			#{cominsureind},
			#{isdifferentplace},
			#{policystyle},
			#{isfarming},
			#{renewind},
			#{renewedind},
			#{renewedtime},
			#{renewalno},
			#{replacedpolicyno},
			#{autorenewind},
			#{multiriskind},
			#{riskapplytype},
			#{productedition},
			#{producteditionname},
			#{compensationtype},
			#{discoverstartdate},
			#{discoverenddate},
			#{judicalCode},
			#{geographicalarea},
			#{geographicalareadesc},
			#{uploadind},
			#{nominativeind},
			#{businessind},
			#{businessmode},
			#{salesteamcode},
			#{salesteamname},
			#{salesmancode},
			#{salesmanname},
			#{salesmanregisterno},
			#{sellername},
			#{sellerregisterno},
			#{intersalesmanregisterno},
			#{intersalesmancode},
			#{teammanagercode},
			#{teammanagername},
			#{underWriteCode},
			#{underWriteName},
			#{inputDate},
			#{updatesysdate}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicycopymainbasic.po.Gupolicycopymainbasic">
		insert into GUPOLICYCOPYMAINBASIC
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="endorNo != null" >
				ENDORNO,
			</if>
			<if test="productcode != null" >
				PRODUCTCODE,
			</if>
			<if test="shareHolderFlag != null" >
				SHAREHOLDERFLAG,
			</if>
			<if test="insurancecompanycode != null" >
				INSURANCECOMPANYCODE,
			</if>
			<if test="businesschannel != null" >
				BUSINESSCHANNEL,
			</if>
			<if test="channeldetailcode != null" >
				CHANNELDETAILCODE,
			</if>
			<if test="channeltip != null" >
				CHANNELTIP,
			</if>
			<if test="channelflag != null" >
				CHANNELFLAG,
			</if>
			<if test="intermediarycode != null" >
				INTERMEDIARYCODE,
			</if>
			<if test="agreementNo != null" >
				AGREEMENTNO,
			</if>
			<if test="businessSource != null" >
				BUSINESSSOURCE,
			</if>
			<if test="businessType != null" >
				BUSINESSTYPE,
			</if>
			<if test="issueDate != null" >
				ISSUEDATE,
			</if>
			<if test="issuecompany != null" >
				ISSUECOMPANY,
			</if>
			<if test="issueplace != null" >
				ISSUEPLACE,
			</if>
			<if test="operatorcode != null" >
				OPERATORCODE,
			</if>
			<if test="operateDate != null" >
				OPERATEDATE,
			</if>
			<if test="argueSolution != null" >
				ARGUESOLUTION,
			</if>
			<if test="arbitoryname != null" >
				ARBITORYNAME,
			</if>
			<if test="domesticind != null" >
				DOMESTICIND,
			</if>
			<if test="agricultureflag != null" >
				AGRICULTUREFLAG,
			</if>
			<if test="moneysuspiciousind != null" >
				MONEYSUSPICIOUSIND,
			</if>
			<if test="cominsureind != null" >
				COMINSUREIND,
			</if>
			<if test="isdifferentplace != null" >
				ISDIFFERENTPLACE,
			</if>
			<if test="policystyle != null" >
				POLICYSTYLE,
			</if>
			<if test="isfarming != null" >
				ISFARMING,
			</if>
			<if test="renewind != null" >
				RENEWIND,
			</if>
			<if test="renewedind != null" >
				RENEWEDIND,
			</if>
			<if test="renewedtime != null" >
				RENEWEDTIME,
			</if>
			<if test="renewalno != null" >
				RENEWALNO,
			</if>
			<if test="replacedpolicyno != null" >
				REPLACEDPOLICYNO,
			</if>
			<if test="autorenewind != null" >
				AUTORENEWIND,
			</if>
			<if test="multiriskind != null" >
				MULTIRISKIND,
			</if>
			<if test="riskapplytype != null" >
				RISKAPPLYTYPE,
			</if>
			<if test="productedition != null" >
				PRODUCTEDITION,
			</if>
			<if test="producteditionname != null" >
				PRODUCTEDITIONNAME,
			</if>
			<if test="compensationtype != null" >
				COMPENSATIONTYPE,
			</if>
			<if test="discoverstartdate != null" >
				DISCOVERSTARTDATE,
			</if>
			<if test="discoverenddate != null" >
				DISCOVERENDDATE,
			</if>
			<if test="judicalCode != null" >
				JUDICALCODE,
			</if>
			<if test="geographicalarea != null" >
				GEOGRAPHICALAREA,
			</if>
			<if test="geographicalareadesc != null" >
				GEOGRAPHICALAREADESC,
			</if>
			<if test="uploadind != null" >
				UPLOADIND,
			</if>
			<if test="nominativeind != null" >
				NOMINATIVEIND,
			</if>
			<if test="businessind != null" >
				BUSINESSIND,
			</if>
			<if test="businessmode != null" >
				BUSINESSMODE,
			</if>
			<if test="salesteamcode != null" >
				SALESTEAMCODE,
			</if>
			<if test="salesteamname != null" >
				SALESTEAMNAME,
			</if>
			<if test="salesmancode != null" >
				SALESMANCODE,
			</if>
			<if test="salesmanname != null" >
				SALESMANNAME,
			</if>
			<if test="salesmanregisterno != null" >
				SALESMANREGISTERNO,
			</if>
			<if test="sellername != null" >
				SELLERNAME,
			</if>
			<if test="sellerregisterno != null" >
				SELLERREGISTERNO,
			</if>
			<if test="intersalesmanregisterno != null" >
				INTERSALESMANREGISTERNO,
			</if>
			<if test="intersalesmancode != null" >
				INTERSALESMANCODE,
			</if>
			<if test="teammanagercode != null" >
				TEAMMANAGERCODE,
			</if>
			<if test="teammanagername != null" >
				TEAMMANAGERNAME,
			</if>
			<if test="underWriteCode != null" >
				UNDERWRITECODE,
			</if>
			<if test="underWriteName != null" >
				UNDERWRITENAME,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="endorNo != null" >
				#{endorNo},
			</if>
			<if test="productcode != null" >
				#{productcode},
			</if>
			<if test="shareHolderFlag != null" >
				#{shareHolderFlag},
			</if>
			<if test="insurancecompanycode != null" >
				#{insurancecompanycode},
			</if>
			<if test="businesschannel != null" >
				#{businesschannel},
			</if>
			<if test="channeldetailcode != null" >
				#{channeldetailcode},
			</if>
			<if test="channeltip != null" >
				#{channeltip},
			</if>
			<if test="channelflag != null" >
				#{channelflag},
			</if>
			<if test="intermediarycode != null" >
				#{intermediarycode},
			</if>
			<if test="agreementNo != null" >
				#{agreementNo},
			</if>
			<if test="businessSource != null" >
				#{businessSource},
			</if>
			<if test="businessType != null" >
				#{businessType},
			</if>
			<if test="issueDate != null" >
				#{issueDate},
			</if>
			<if test="issuecompany != null" >
				#{issuecompany},
			</if>
			<if test="issueplace != null" >
				#{issueplace},
			</if>
			<if test="operatorcode != null" >
				#{operatorcode},
			</if>
			<if test="operateDate != null" >
				#{operateDate},
			</if>
			<if test="argueSolution != null" >
				#{argueSolution},
			</if>
			<if test="arbitoryname != null" >
				#{arbitoryname},
			</if>
			<if test="domesticind != null" >
				#{domesticind},
			</if>
			<if test="agricultureflag != null" >
				#{agricultureflag},
			</if>
			<if test="moneysuspiciousind != null" >
				#{moneysuspiciousind},
			</if>
			<if test="cominsureind != null" >
				#{cominsureind},
			</if>
			<if test="isdifferentplace != null" >
				#{isdifferentplace},
			</if>
			<if test="policystyle != null" >
				#{policystyle},
			</if>
			<if test="isfarming != null" >
				#{isfarming},
			</if>
			<if test="renewind != null" >
				#{renewind},
			</if>
			<if test="renewedind != null" >
				#{renewedind},
			</if>
			<if test="renewedtime != null" >
				#{renewedtime},
			</if>
			<if test="renewalno != null" >
				#{renewalno},
			</if>
			<if test="replacedpolicyno != null" >
				#{replacedpolicyno},
			</if>
			<if test="autorenewind != null" >
				#{autorenewind},
			</if>
			<if test="multiriskind != null" >
				#{multiriskind},
			</if>
			<if test="riskapplytype != null" >
				#{riskapplytype},
			</if>
			<if test="productedition != null" >
				#{productedition},
			</if>
			<if test="producteditionname != null" >
				#{producteditionname},
			</if>
			<if test="compensationtype != null" >
				#{compensationtype},
			</if>
			<if test="discoverstartdate != null" >
				#{discoverstartdate},
			</if>
			<if test="discoverenddate != null" >
				#{discoverenddate},
			</if>
			<if test="judicalCode != null" >
				#{judicalCode},
			</if>
			<if test="geographicalarea != null" >
				#{geographicalarea},
			</if>
			<if test="geographicalareadesc != null" >
				#{geographicalareadesc},
			</if>
			<if test="uploadind != null" >
				#{uploadind},
			</if>
			<if test="nominativeind != null" >
				#{nominativeind},
			</if>
			<if test="businessind != null" >
				#{businessind},
			</if>
			<if test="businessmode != null" >
				#{businessmode},
			</if>
			<if test="salesteamcode != null" >
				#{salesteamcode},
			</if>
			<if test="salesteamname != null" >
				#{salesteamname},
			</if>
			<if test="salesmancode != null" >
				#{salesmancode},
			</if>
			<if test="salesmanname != null" >
				#{salesmanname},
			</if>
			<if test="salesmanregisterno != null" >
				#{salesmanregisterno},
			</if>
			<if test="sellername != null" >
				#{sellername},
			</if>
			<if test="sellerregisterno != null" >
				#{sellerregisterno},
			</if>
			<if test="intersalesmanregisterno != null" >
				#{intersalesmanregisterno},
			</if>
			<if test="intersalesmancode != null" >
				#{intersalesmancode},
			</if>
			<if test="teammanagercode != null" >
				#{teammanagercode},
			</if>
			<if test="teammanagername != null" >
				#{teammanagername},
			</if>
			<if test="underWriteCode != null" >
				#{underWriteCode},
			</if>
			<if test="underWriteName != null" >
				#{underWriteName},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate}
			</if>
		</trim>
	</insert>

    <!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicycopymainbasic.po.Gupolicycopymainbasic">
		update GUPOLICYCOPYMAINBASIC 
		<set>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="endorNo != null" >
				ENDORNO=#{endorNo},
			</if>
			<if test="productcode != null" >
				PRODUCTCODE=#{productcode},
			</if>
			<if test="shareHolderFlag != null" >
				SHAREHOLDERFLAG=#{shareHolderFlag},
			</if>
			<if test="insurancecompanycode != null" >
				INSURANCECOMPANYCODE=#{insurancecompanycode},
			</if>
			<if test="businesschannel != null" >
				BUSINESSCHANNEL=#{businesschannel},
			</if>
			<if test="channeldetailcode != null" >
				CHANNELDETAILCODE=#{channeldetailcode},
			</if>
			<if test="channeltip != null" >
				CHANNELTIP=#{channeltip},
			</if>
			<if test="channelflag != null" >
				CHANNELFLAG=#{channelflag},
			</if>
			<if test="intermediarycode != null" >
				INTERMEDIARYCODE=#{intermediarycode},
			</if>
			<if test="agreementNo != null" >
				AGREEMENTNO=#{agreementNo},
			</if>
			<if test="businessSource != null" >
				BUSINESSSOURCE=#{businessSource},
			</if>
			<if test="businessType != null" >
				BUSINESSTYPE=#{businessType},
			</if>
			<if test="issueDate != null" >
				ISSUEDATE=#{issueDate},
			</if>
			<if test="issuecompany != null" >
				ISSUECOMPANY=#{issuecompany},
			</if>
			<if test="issueplace != null" >
				ISSUEPLACE=#{issueplace},
			</if>
			<if test="operatorcode != null" >
				OPERATORCODE=#{operatorcode},
			</if>
			<if test="operateDate != null" >
				OPERATEDATE=#{operateDate},
			</if>
			<if test="argueSolution != null" >
				ARGUESOLUTION=#{argueSolution},
			</if>
			<if test="arbitoryname != null" >
				ARBITORYNAME=#{arbitoryname},
			</if>
			<if test="domesticind != null" >
				DOMESTICIND=#{domesticind},
			</if>
			<if test="agricultureflag != null" >
				AGRICULTUREFLAG=#{agricultureflag},
			</if>
			<if test="moneysuspiciousind != null" >
				MONEYSUSPICIOUSIND=#{moneysuspiciousind},
			</if>
			<if test="cominsureind != null" >
				COMINSUREIND=#{cominsureind},
			</if>
			<if test="isdifferentplace != null" >
				ISDIFFERENTPLACE=#{isdifferentplace},
			</if>
			<if test="policystyle != null" >
				POLICYSTYLE=#{policystyle},
			</if>
			<if test="isfarming != null" >
				ISFARMING=#{isfarming},
			</if>
			<if test="renewind != null" >
				RENEWIND=#{renewind},
			</if>
			<if test="renewedind != null" >
				RENEWEDIND=#{renewedind},
			</if>
			<if test="renewedtime != null" >
				RENEWEDTIME=#{renewedtime},
			</if>
			<if test="renewalno != null" >
				RENEWALNO=#{renewalno},
			</if>
			<if test="replacedpolicyno != null" >
				REPLACEDPOLICYNO=#{replacedpolicyno},
			</if>
			<if test="autorenewind != null" >
				AUTORENEWIND=#{autorenewind},
			</if>
			<if test="multiriskind != null" >
				MULTIRISKIND=#{multiriskind},
			</if>
			<if test="riskapplytype != null" >
				RISKAPPLYTYPE=#{riskapplytype},
			</if>
			<if test="productedition != null" >
				PRODUCTEDITION=#{productedition},
			</if>
			<if test="producteditionname != null" >
				PRODUCTEDITIONNAME=#{producteditionname},
			</if>
			<if test="compensationtype != null" >
				COMPENSATIONTYPE=#{compensationtype},
			</if>
			<if test="discoverstartdate != null" >
				DISCOVERSTARTDATE=#{discoverstartdate},
			</if>
			<if test="discoverenddate != null" >
				DISCOVERENDDATE=#{discoverenddate},
			</if>
			<if test="judicalCode != null" >
				JUDICALCODE=#{judicalCode},
			</if>
			<if test="geographicalarea != null" >
				GEOGRAPHICALAREA=#{geographicalarea},
			</if>
			<if test="geographicalareadesc != null" >
				GEOGRAPHICALAREADESC=#{geographicalareadesc},
			</if>
			<if test="uploadind != null" >
				UPLOADIND=#{uploadind},
			</if>
			<if test="nominativeind != null" >
				NOMINATIVEIND=#{nominativeind},
			</if>
			<if test="businessind != null" >
				BUSINESSIND=#{businessind},
			</if>
			<if test="businessmode != null" >
				BUSINESSMODE=#{businessmode},
			</if>
			<if test="salesteamcode != null" >
				SALESTEAMCODE=#{salesteamcode},
			</if>
			<if test="salesteamname != null" >
				SALESTEAMNAME=#{salesteamname},
			</if>
			<if test="salesmancode != null" >
				SALESMANCODE=#{salesmancode},
			</if>
			<if test="salesmanname != null" >
				SALESMANNAME=#{salesmanname},
			</if>
			<if test="salesmanregisterno != null" >
				SALESMANREGISTERNO=#{salesmanregisterno},
			</if>
			<if test="sellername != null" >
				SELLERNAME=#{sellername},
			</if>
			<if test="sellerregisterno != null" >
				SELLERREGISTERNO=#{sellerregisterno},
			</if>
			<if test="intersalesmanregisterno != null" >
				INTERSALESMANREGISTERNO=#{intersalesmanregisterno},
			</if>
			<if test="intersalesmancode != null" >
				INTERSALESMANCODE=#{intersalesmancode},
			</if>
			<if test="teammanagercode != null" >
				TEAMMANAGERCODE=#{teammanagercode},
			</if>
			<if test="teammanagername != null" >
				TEAMMANAGERNAME=#{teammanagername},
			</if>
			<if test="underWriteCode != null" >
				UNDERWRITECODE=#{underWriteCode},
			</if>
			<if test="underWriteName != null" >
				UNDERWRITENAME=#{underWriteName},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicycopymainbasic.po.Gupolicycopymainbasic">
		update GUPOLICYCOPYMAINBASIC set
			POLICYNO=#{policyNo},
			ENDORNO=#{endorNo},
			PRODUCTCODE=#{productcode},
			SHAREHOLDERFLAG=#{shareHolderFlag},
			INSURANCECOMPANYCODE=#{insurancecompanycode},
			BUSINESSCHANNEL=#{businesschannel},
			CHANNELDETAILCODE=#{channeldetailcode},
			CHANNELTIP=#{channeltip},
			CHANNELFLAG=#{channelflag},
			INTERMEDIARYCODE=#{intermediarycode},
			AGREEMENTNO=#{agreementNo},
			BUSINESSSOURCE=#{businessSource},
			BUSINESSTYPE=#{businessType},
			ISSUEDATE=#{issueDate},
			ISSUECOMPANY=#{issuecompany},
			ISSUEPLACE=#{issueplace},
			OPERATORCODE=#{operatorcode},
			OPERATEDATE=#{operateDate},
			ARGUESOLUTION=#{argueSolution},
			ARBITORYNAME=#{arbitoryname},
			DOMESTICIND=#{domesticind},
			AGRICULTUREFLAG=#{agricultureflag},
			MONEYSUSPICIOUSIND=#{moneysuspiciousind},
			COMINSUREIND=#{cominsureind},
			ISDIFFERENTPLACE=#{isdifferentplace},
			POLICYSTYLE=#{policystyle},
			ISFARMING=#{isfarming},
			RENEWIND=#{renewind},
			RENEWEDIND=#{renewedind},
			RENEWEDTIME=#{renewedtime},
			RENEWALNO=#{renewalno},
			REPLACEDPOLICYNO=#{replacedpolicyno},
			AUTORENEWIND=#{autorenewind},
			MULTIRISKIND=#{multiriskind},
			RISKAPPLYTYPE=#{riskapplytype},
			PRODUCTEDITION=#{productedition},
			PRODUCTEDITIONNAME=#{producteditionname},
			COMPENSATIONTYPE=#{compensationtype},
			DISCOVERSTARTDATE=#{discoverstartdate},
			DISCOVERENDDATE=#{discoverenddate},
			JUDICALCODE=#{judicalCode},
			GEOGRAPHICALAREA=#{geographicalarea},
			GEOGRAPHICALAREADESC=#{geographicalareadesc},
			UPLOADIND=#{uploadind},
			NOMINATIVEIND=#{nominativeind},
			BUSINESSIND=#{businessind},
			BUSINESSMODE=#{businessmode},
			SALESTEAMCODE=#{salesteamcode},
			SALESTEAMNAME=#{salesteamname},
			SALESMANCODE=#{salesmancode},
			SALESMANNAME=#{salesmanname},
			SALESMANREGISTERNO=#{salesmanregisterno},
			SELLERNAME=#{sellername},
			SELLERREGISTERNO=#{sellerregisterno},
			INTERSALESMANREGISTERNO=#{intersalesmanregisterno},
			INTERSALESMANCODE=#{intersalesmancode},
			TEAMMANAGERCODE=#{teammanagercode},
			TEAMMANAGERNAME=#{teammanagername},
			UNDERWRITECODE=#{underWriteCode},
			UNDERWRITENAME=#{underWriteName},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
		where ID = #{id}	</update>

	<insert id="batchInsert">
		INSERT ALL
		<foreach collection="list" item="item" separator="">
			INTO Gupolicycopymainbasic VALUES
			(#{item.id}, #{item.policyNo}, #{item.endorNo}, #{item.productcode}, #{item.shareHolderFlag}, #{item.insurancecompanycode},
			#{item.businesschannel}, #{item.channeldetailcode}, #{item.channeltip}, #{item.channelflag}, #{item.intermediarycode},
			#{item.agreementNo}, #{item.businessSource}, #{item.businessType}, #{item.issueDate}, #{item.issuecompany}, #{item.issueplace},
			#{item.operatorcode}, #{item.operateDate}, #{item.argueSolution}, #{item.arbitoryname}, #{item.domesticind}, #{item.agricultureflag},
			#{item.moneysuspiciousind}, #{item.cominsureind}, #{item.isdifferentplace}, #{item.policystyle}, #{item.isfarming}, #{item.renewind},
			#{item.renewedind}, #{item.renewedtime}, #{item.renewalno}, #{item.replacedpolicyno}, #{item.autorenewind}, #{item.multiriskind},
			#{item.riskapplytype}, #{item.productedition}, #{item.producteditionname}, #{item.compensationtype}, #{item.discoverstartdate},
			#{item.discoverenddate}, #{item.judicalCode}, #{item.geographicalarea}, #{item.geographicalareadesc}, #{item.uploadind},
			#{item.nominativeind}, #{item.businessind}, #{item.businessmode}, #{item.salesteamcode}, #{item.salesteamname}, #{item.salesmancode},
			#{item.salesmanname}, #{item.salesmanregisterno}, #{item.sellername}, #{item.sellerregisterno}, #{item.intersalesmanregisterno},
			#{item.intersalesmancode}, #{item.teammanagercode}, #{item.teammanagername}, #{item.underWriteCode}, #{item.underWriteName},
			#{item.inputDate}, #{item.updatesysdate})
		</foreach>
		select 1 from dual
	</insert>

	<delete id="deleteByPolicyNo" parameterType="string">
		delete from GUPOLICYCOPYMAINBASIC where policyNo=#{policyNo}
	</delete>
</mapper>
