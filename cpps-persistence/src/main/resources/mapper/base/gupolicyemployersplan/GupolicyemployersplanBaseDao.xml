<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicyemployersplan.dao.GupolicyemployersplanDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicyemployersplan.po.Gupolicyemployersplan">
		<id column="ID" property="id" />
		<result column="POLICYNO" property="policyNo" />
		<result column="SUBPOLICYNO" property="subpolicyno" />
		<result column="PLANID" property="planid" />
		<result column="PLANCODE" property="plancode" />
		<result column="RISKCODE" property="riskCode" />
		<result column="ITEMNO" property="itemNo" />
		<result column="ITEMCODE" property="itemCode" />
		<result column="ITEMDETAILNO" property="itemdetailno" />
		<result column="CURRENCY" property="currency" />
		<result column="INDUSTRYTYPE" property="industrytype" />
		<result column="INDUSTRYDETAIL" property="industrydetail" />
		<result column="SCHEMECODE" property="schemecode" />
		<result column="SCHEMENAME" property="schemename" />
		<result column="PERSONNELTYPE" property="personneltype" />
		<result column="DYNAMICTARGETTYPE" property="dynamictargettype" />
		<result column="LISTSEQNO" property="listseqno" />
		<result column="TARGETFLAG" property="targetflag" />
		<result column="TARGETCALCULATE" property="targetcalculate" />
		<result column="EMPLOYEES" property="employees" />
		<result column="EMPLOYEESTHISCHANGE" property="employeesthischange" />
		<result column="LEGALFEESLIMIT" property="legalfeeslimit" />
		<result column="LEGALFEESRATE" property="legalfeesrate" />
		<result column="LEGALSUMPREMIUM" property="legalsumpremium" />
		<result column="PERSONCASUALTIESLIMIT" property="personcasualtieslimit" />
		<result column="PERSONCASUALTIESRATE" property="personcasualtiesrate" />
		<result column="PERSONMEDICALLIMIT" property="personmedicallimit" />
		<result column="PERSONMEDICALRATE" property="personmedicalrate" />
		<result column="FIELDALIMIT" property="fieldalimit" />
		<result column="FIELDARATE" property="fieldarate" />
		<result column="FIELDBLIMIT" property="fieldblimit" />
		<result column="FIELDBRATE" property="fieldbrate" />
		<result column="FIELDC" property="fieldc" />
		<result column="FIELD" property="field" />
		<result column="PERSONSUMPREMIUM" property="personsumpremium" />
		<result column="LISTBELONGIND" property="listbelongind" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		POLICYNO,
		SUBPOLICYNO,
		PLANID,
		PLANCODE,
		RISKCODE,
		ITEMNO,
		ITEMCODE,
		ITEMDETAILNO,
		CURRENCY,
		INDUSTRYTYPE,
		INDUSTRYDETAIL,
		SCHEMECODE,
		SCHEMENAME,
		PERSONNELTYPE,
		DYNAMICTARGETTYPE,
		LISTSEQNO,
		TARGETFLAG,
		TARGETCALCULATE,
		EMPLOYEES,
		EMPLOYEESTHISCHANGE,
		LEGALFEESLIMIT,
		LEGALFEESRATE,
		LEGALSUMPREMIUM,
		PERSONCASUALTIESLIMIT,
		PERSONCASUALTIESRATE,
		PERSONMEDICALLIMIT,
		PERSONMEDICALRATE,
		FIELDALIMIT,
		FIELDARATE,
		FIELDBLIMIT,
		FIELDBRATE,
		FIELDC,
		FIELD,
		PERSONSUMPREMIUM,
		LISTBELONGIND,
		INPUTDATE,
		UPDATESYSDATE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="subpolicyno != null and subpolicyno != ''" >
			and SUBPOLICYNO = #{subpolicyno}
		</if>
		<if test="planid != null and planid != ''" >
			and PLANID = #{planid}
		</if>
		<if test="plancode != null and plancode != ''" >
			and PLANCODE = #{plancode}
		</if>
		<if test="riskCode != null and riskCode != ''" >
			and RISKCODE = #{riskCode}
		</if>
		<if test="itemNo != null and itemNo != ''" >
			and ITEMNO = #{itemNo}
		</if>
		<if test="itemCode != null and itemCode != ''" >
			and ITEMCODE = #{itemCode}
		</if>
		<if test="itemdetailno != null and itemdetailno != ''" >
			and ITEMDETAILNO = #{itemdetailno}
		</if>
		<if test="currency != null and currency != ''" >
			and CURRENCY = #{currency}
		</if>
		<if test="industrytype != null and industrytype != ''" >
			and INDUSTRYTYPE = #{industrytype}
		</if>
		<if test="industrydetail != null and industrydetail != ''" >
			and INDUSTRYDETAIL = #{industrydetail}
		</if>
		<if test="schemecode != null and schemecode != ''" >
			and SCHEMECODE = #{schemecode}
		</if>
		<if test="schemename != null and schemename != ''" >
			and SCHEMENAME = #{schemename}
		</if>
		<if test="personneltype != null and personneltype != ''" >
			and PERSONNELTYPE = #{personneltype}
		</if>
		<if test="dynamictargettype != null and dynamictargettype != ''" >
			and DYNAMICTARGETTYPE = #{dynamictargettype}
		</if>
		<if test="listseqno != null and listseqno != ''" >
			and LISTSEQNO = #{listseqno}
		</if>
		<if test="targetflag != null and targetflag != ''" >
			and TARGETFLAG = #{targetflag}
		</if>
		<if test="targetcalculate != null and targetcalculate != ''" >
			and TARGETCALCULATE = #{targetcalculate}
		</if>
		<if test="employees != null and employees != ''" >
			and EMPLOYEES = #{employees}
		</if>
		<if test="employeesthischange != null and employeesthischange != ''" >
			and EMPLOYEESTHISCHANGE = #{employeesthischange}
		</if>
		<if test="legalfeeslimit != null and legalfeeslimit != ''" >
			and LEGALFEESLIMIT = #{legalfeeslimit}
		</if>
		<if test="legalfeesrate != null and legalfeesrate != ''" >
			and LEGALFEESRATE = #{legalfeesrate}
		</if>
		<if test="legalsumpremium != null and legalsumpremium != ''" >
			and LEGALSUMPREMIUM = #{legalsumpremium}
		</if>
		<if test="personcasualtieslimit != null and personcasualtieslimit != ''" >
			and PERSONCASUALTIESLIMIT = #{personcasualtieslimit}
		</if>
		<if test="personcasualtiesrate != null and personcasualtiesrate != ''" >
			and PERSONCASUALTIESRATE = #{personcasualtiesrate}
		</if>
		<if test="personmedicallimit != null and personmedicallimit != ''" >
			and PERSONMEDICALLIMIT = #{personmedicallimit}
		</if>
		<if test="personmedicalrate != null and personmedicalrate != ''" >
			and PERSONMEDICALRATE = #{personmedicalrate}
		</if>
		<if test="fieldalimit != null and fieldalimit != ''" >
			and FIELDALIMIT = #{fieldalimit}
		</if>
		<if test="fieldarate != null and fieldarate != ''" >
			and FIELDARATE = #{fieldarate}
		</if>
		<if test="fieldblimit != null and fieldblimit != ''" >
			and FIELDBLIMIT = #{fieldblimit}
		</if>
		<if test="fieldbrate != null and fieldbrate != ''" >
			and FIELDBRATE = #{fieldbrate}
		</if>
		<if test="fieldc != null and fieldc != ''" >
			and FIELDC = #{fieldc}
		</if>
		<if test="field != null and field != ''" >
			and FIELD = #{field}
		</if>
		<if test="personsumpremium != null and personsumpremium != ''" >
			and PERSONSUMPREMIUM = #{personsumpremium}
		</if>
		<if test="listbelongind != null and listbelongind != ''" >
			and LISTBELONGIND = #{listbelongind}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYEMPLOYERSPLAN
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYEMPLOYERSPLAN
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYEMPLOYERSPLAN
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicyemployersplan.po.Gupolicyemployersplan">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYEMPLOYERSPLAN
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYEMPLOYERSPLAN
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicyemployersplan.po.Gupolicyemployersplan">
		insert into GUPOLICYEMPLOYERSPLAN (
			ID,
			POLICYNO,
			SUBPOLICYNO,
			PLANID,
			PLANCODE,
			RISKCODE,
			ITEMNO,
			ITEMCODE,
			ITEMDETAILNO,
			CURRENCY,
			INDUSTRYTYPE,
			INDUSTRYDETAIL,
			SCHEMECODE,
			SCHEMENAME,
			PERSONNELTYPE,
			DYNAMICTARGETTYPE,
			LISTSEQNO,
			TARGETFLAG,
			TARGETCALCULATE,
			EMPLOYEES,
			EMPLOYEESTHISCHANGE,
			LEGALFEESLIMIT,
			LEGALFEESRATE,
			LEGALSUMPREMIUM,
			PERSONCASUALTIESLIMIT,
			PERSONCASUALTIESRATE,
			PERSONMEDICALLIMIT,
			PERSONMEDICALRATE,
			FIELDALIMIT,
			FIELDARATE,
			FIELDBLIMIT,
			FIELDBRATE,
			FIELDC,
			FIELD,
			PERSONSUMPREMIUM,
			LISTBELONGIND,
			INPUTDATE,
			UPDATESYSDATE
		) values (
			#{id},
			#{policyNo},
			#{subpolicyno},
			#{planid},
			#{plancode},
			#{riskCode},
			#{itemNo},
			#{itemCode},
			#{itemdetailno},
			#{currency},
			#{industrytype},
			#{industrydetail},
			#{schemecode},
			#{schemename},
			#{personneltype},
			#{dynamictargettype},
			#{listseqno},
			#{targetflag},
			#{targetcalculate},
			#{employees},
			#{employeesthischange},
			#{legalfeeslimit},
			#{legalfeesrate},
			#{legalsumpremium},
			#{personcasualtieslimit},
			#{personcasualtiesrate},
			#{personmedicallimit},
			#{personmedicalrate},
			#{fieldalimit},
			#{fieldarate},
			#{fieldblimit},
			#{fieldbrate},
			#{fieldc},
			#{field},
			#{personsumpremium},
			#{listbelongind},
			#{inputDate},
			#{updatesysdate}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicyemployersplan.po.Gupolicyemployersplan">
		insert into GUPOLICYEMPLOYERSPLAN
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO,
			</if>
			<if test="planid != null" >
				PLANID,
			</if>
			<if test="plancode != null" >
				PLANCODE,
			</if>
			<if test="riskCode != null" >
				RISKCODE,
			</if>
			<if test="itemNo != null" >
				ITEMNO,
			</if>
			<if test="itemCode != null" >
				ITEMCODE,
			</if>
			<if test="itemdetailno != null" >
				ITEMDETAILNO,
			</if>
			<if test="currency != null" >
				CURRENCY,
			</if>
			<if test="industrytype != null" >
				INDUSTRYTYPE,
			</if>
			<if test="industrydetail != null" >
				INDUSTRYDETAIL,
			</if>
			<if test="schemecode != null" >
				SCHEMECODE,
			</if>
			<if test="schemename != null" >
				SCHEMENAME,
			</if>
			<if test="personneltype != null" >
				PERSONNELTYPE,
			</if>
			<if test="dynamictargettype != null" >
				DYNAMICTARGETTYPE,
			</if>
			<if test="listseqno != null" >
				LISTSEQNO,
			</if>
			<if test="targetflag != null" >
				TARGETFLAG,
			</if>
			<if test="targetcalculate != null" >
				TARGETCALCULATE,
			</if>
			<if test="employees != null" >
				EMPLOYEES,
			</if>
			<if test="employeesthischange != null" >
				EMPLOYEESTHISCHANGE,
			</if>
			<if test="legalfeeslimit != null" >
				LEGALFEESLIMIT,
			</if>
			<if test="legalfeesrate != null" >
				LEGALFEESRATE,
			</if>
			<if test="legalsumpremium != null" >
				LEGALSUMPREMIUM,
			</if>
			<if test="personcasualtieslimit != null" >
				PERSONCASUALTIESLIMIT,
			</if>
			<if test="personcasualtiesrate != null" >
				PERSONCASUALTIESRATE,
			</if>
			<if test="personmedicallimit != null" >
				PERSONMEDICALLIMIT,
			</if>
			<if test="personmedicalrate != null" >
				PERSONMEDICALRATE,
			</if>
			<if test="fieldalimit != null" >
				FIELDALIMIT,
			</if>
			<if test="fieldarate != null" >
				FIELDARATE,
			</if>
			<if test="fieldblimit != null" >
				FIELDBLIMIT,
			</if>
			<if test="fieldbrate != null" >
				FIELDBRATE,
			</if>
			<if test="fieldc != null" >
				FIELDC,
			</if>
			<if test="field != null" >
				FIELD,
			</if>
			<if test="personsumpremium != null" >
				PERSONSUMPREMIUM,
			</if>
			<if test="listbelongind != null" >
				LISTBELONGIND,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="subpolicyno != null" >
				#{subpolicyno},
			</if>
			<if test="planid != null" >
				#{planid},
			</if>
			<if test="plancode != null" >
				#{plancode},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="itemNo != null" >
				#{itemNo},
			</if>
			<if test="itemCode != null" >
				#{itemCode},
			</if>
			<if test="itemdetailno != null" >
				#{itemdetailno},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
			<if test="industrytype != null" >
				#{industrytype},
			</if>
			<if test="industrydetail != null" >
				#{industrydetail},
			</if>
			<if test="schemecode != null" >
				#{schemecode},
			</if>
			<if test="schemename != null" >
				#{schemename},
			</if>
			<if test="personneltype != null" >
				#{personneltype},
			</if>
			<if test="dynamictargettype != null" >
				#{dynamictargettype},
			</if>
			<if test="listseqno != null" >
				#{listseqno},
			</if>
			<if test="targetflag != null" >
				#{targetflag},
			</if>
			<if test="targetcalculate != null" >
				#{targetcalculate},
			</if>
			<if test="employees != null" >
				#{employees},
			</if>
			<if test="employeesthischange != null" >
				#{employeesthischange},
			</if>
			<if test="legalfeeslimit != null" >
				#{legalfeeslimit},
			</if>
			<if test="legalfeesrate != null" >
				#{legalfeesrate},
			</if>
			<if test="legalsumpremium != null" >
				#{legalsumpremium},
			</if>
			<if test="personcasualtieslimit != null" >
				#{personcasualtieslimit},
			</if>
			<if test="personcasualtiesrate != null" >
				#{personcasualtiesrate},
			</if>
			<if test="personmedicallimit != null" >
				#{personmedicallimit},
			</if>
			<if test="personmedicalrate != null" >
				#{personmedicalrate},
			</if>
			<if test="fieldalimit != null" >
				#{fieldalimit},
			</if>
			<if test="fieldarate != null" >
				#{fieldarate},
			</if>
			<if test="fieldblimit != null" >
				#{fieldblimit},
			</if>
			<if test="fieldbrate != null" >
				#{fieldbrate},
			</if>
			<if test="fieldc != null" >
				#{fieldc},
			</if>
			<if test="field != null" >
				#{field},
			</if>
			<if test="personsumpremium != null" >
				#{personsumpremium},
			</if>
			<if test="listbelongind != null" >
				#{listbelongind},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicyemployersplan.po.Gupolicyemployersplan">
		update GUPOLICYEMPLOYERSPLAN 
		<set>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO=#{subpolicyno},
			</if>
			<if test="planid != null" >
				PLANID=#{planid},
			</if>
			<if test="plancode != null" >
				PLANCODE=#{plancode},
			</if>
			<if test="riskCode != null" >
				RISKCODE=#{riskCode},
			</if>
			<if test="itemNo != null" >
				ITEMNO=#{itemNo},
			</if>
			<if test="itemCode != null" >
				ITEMCODE=#{itemCode},
			</if>
			<if test="itemdetailno != null" >
				ITEMDETAILNO=#{itemdetailno},
			</if>
			<if test="currency != null" >
				CURRENCY=#{currency},
			</if>
			<if test="industrytype != null" >
				INDUSTRYTYPE=#{industrytype},
			</if>
			<if test="industrydetail != null" >
				INDUSTRYDETAIL=#{industrydetail},
			</if>
			<if test="schemecode != null" >
				SCHEMECODE=#{schemecode},
			</if>
			<if test="schemename != null" >
				SCHEMENAME=#{schemename},
			</if>
			<if test="personneltype != null" >
				PERSONNELTYPE=#{personneltype},
			</if>
			<if test="dynamictargettype != null" >
				DYNAMICTARGETTYPE=#{dynamictargettype},
			</if>
			<if test="listseqno != null" >
				LISTSEQNO=#{listseqno},
			</if>
			<if test="targetflag != null" >
				TARGETFLAG=#{targetflag},
			</if>
			<if test="targetcalculate != null" >
				TARGETCALCULATE=#{targetcalculate},
			</if>
			<if test="employees != null" >
				EMPLOYEES=#{employees},
			</if>
			<if test="employeesthischange != null" >
				EMPLOYEESTHISCHANGE=#{employeesthischange},
			</if>
			<if test="legalfeeslimit != null" >
				LEGALFEESLIMIT=#{legalfeeslimit},
			</if>
			<if test="legalfeesrate != null" >
				LEGALFEESRATE=#{legalfeesrate},
			</if>
			<if test="legalsumpremium != null" >
				LEGALSUMPREMIUM=#{legalsumpremium},
			</if>
			<if test="personcasualtieslimit != null" >
				PERSONCASUALTIESLIMIT=#{personcasualtieslimit},
			</if>
			<if test="personcasualtiesrate != null" >
				PERSONCASUALTIESRATE=#{personcasualtiesrate},
			</if>
			<if test="personmedicallimit != null" >
				PERSONMEDICALLIMIT=#{personmedicallimit},
			</if>
			<if test="personmedicalrate != null" >
				PERSONMEDICALRATE=#{personmedicalrate},
			</if>
			<if test="fieldalimit != null" >
				FIELDALIMIT=#{fieldalimit},
			</if>
			<if test="fieldarate != null" >
				FIELDARATE=#{fieldarate},
			</if>
			<if test="fieldblimit != null" >
				FIELDBLIMIT=#{fieldblimit},
			</if>
			<if test="fieldbrate != null" >
				FIELDBRATE=#{fieldbrate},
			</if>
			<if test="fieldc != null" >
				FIELDC=#{fieldc},
			</if>
			<if test="field != null" >
				FIELD=#{field},
			</if>
			<if test="personsumpremium != null" >
				PERSONSUMPREMIUM=#{personsumpremium},
			</if>
			<if test="listbelongind != null" >
				LISTBELONGIND=#{listbelongind},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicyemployersplan.po.Gupolicyemployersplan">
		update GUPOLICYEMPLOYERSPLAN set
			POLICYNO=#{policyNo},
			SUBPOLICYNO=#{subpolicyno},
			PLANID=#{planid},
			PLANCODE=#{plancode},
			RISKCODE=#{riskCode},
			ITEMNO=#{itemNo},
			ITEMCODE=#{itemCode},
			ITEMDETAILNO=#{itemdetailno},
			CURRENCY=#{currency},
			INDUSTRYTYPE=#{industrytype},
			INDUSTRYDETAIL=#{industrydetail},
			SCHEMECODE=#{schemecode},
			SCHEMENAME=#{schemename},
			PERSONNELTYPE=#{personneltype},
			DYNAMICTARGETTYPE=#{dynamictargettype},
			LISTSEQNO=#{listseqno},
			TARGETFLAG=#{targetflag},
			TARGETCALCULATE=#{targetcalculate},
			EMPLOYEES=#{employees},
			EMPLOYEESTHISCHANGE=#{employeesthischange},
			LEGALFEESLIMIT=#{legalfeeslimit},
			LEGALFEESRATE=#{legalfeesrate},
			LEGALSUMPREMIUM=#{legalsumpremium},
			PERSONCASUALTIESLIMIT=#{personcasualtieslimit},
			PERSONCASUALTIESRATE=#{personcasualtiesrate},
			PERSONMEDICALLIMIT=#{personmedicallimit},
			PERSONMEDICALRATE=#{personmedicalrate},
			FIELDALIMIT=#{fieldalimit},
			FIELDARATE=#{fieldarate},
			FIELDBLIMIT=#{fieldblimit},
			FIELDBRATE=#{fieldbrate},
			FIELDC=#{fieldc},
			FIELD=#{field},
			PERSONSUMPREMIUM=#{personsumpremium},
			LISTBELONGIND=#{listbelongind},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
		where ID = #{id}	</update>

	<update id="batchUpdate" parameterType="java.util.List">
		update GUPOLICYEMPLOYERSPLAN
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="POLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SUBPOLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.subpolicyno,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="PLANID = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.planid,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="PLANCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.plancode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="RISKCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.riskCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemNo,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="ITEMCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMDETAILNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemdetailno,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CURRENCY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.currency,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INDUSTRYTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.industrytype,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INDUSTRYDETAIL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.industrydetail,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SCHEMECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.schemecode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SCHEMENAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.schemename,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="PERSONNELTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.personneltype,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="DYNAMICTARGETTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.dynamictargettype,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="LISTSEQNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.listseqno,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="TARGETFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.targetflag,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="TARGETCALCULATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.targetcalculate,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="EMPLOYEES = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.employees,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="EMPLOYEESTHISCHANGE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.employeesthischange,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="LEGALFEESLIMIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.legalfeeslimit,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="LEGALFEESRATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.legalfeesrate,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="LEGALSUMPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.legalsumpremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="PERSONCASUALTIESLIMIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.personcasualtieslimit,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="PERSONCASUALTIESRATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.personcasualtiesrate,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="PERSONMEDICALLIMIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.personmedicallimit,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="PERSONMEDICALRATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.personmedicalrate,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="FIELDALIMIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.fieldalimit,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="FIELDARATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.fieldarate,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="FIELDBLIMIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.fieldblimit,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="FIELDBRATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.fieldbrate,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="FIELDC = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.fieldc,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="FIELD = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.field,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="PERSONSUMPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.personsumpremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="LISTBELONGIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.listbelongind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INPUTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="UPDATESYSDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
		</trim>
		where ID in
		<foreach close=")" collection="list" item="item" open="(" separator=", ">
			#{item.id,jdbcType=VARCHAR}
		</foreach>
	</update>
	<update id="batchUpdateSelective" parameterType="java.util.List">
		update GUPOLICYEMPLOYERSPLAN
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="POLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.policyNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SUBPOLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.subpolicyno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.subpolicyno,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="PLANID = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.planid != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.planid,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="PLANCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.plancode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.plancode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="RISKCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.riskCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.riskCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemNo,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMDETAILNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemdetailno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemdetailno,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CURRENCY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.currency != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.currency,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INDUSTRYTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.industrytype != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.industrytype,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INDUSTRYDETAIL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.industrydetail != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.industrydetail,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SCHEMECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.schemecode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.schemecode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SCHEMENAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.schemename != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.schemename,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="PERSONNELTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.personneltype != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.personneltype,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="DYNAMICTARGETTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.dynamictargettype != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.dynamictargettype,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="LISTSEQNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.listseqno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.listseqno,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="TARGETFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.targetflag != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.targetflag,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="TARGETCALCULATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.targetcalculate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.targetcalculate,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="EMPLOYEES = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.employees != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.employees,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="EMPLOYEESTHISCHANGE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.employeesthischange != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.employeesthischange,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="LEGALFEESLIMIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.legalfeeslimit != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.legalfeeslimit,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="LEGALFEESRATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.legalfeesrate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.legalfeesrate,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="LEGALSUMPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.legalsumpremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.legalsumpremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="PERSONCASUALTIESLIMIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.personcasualtieslimit != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.personcasualtieslimit,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="PERSONCASUALTIESRATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.personcasualtiesrate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.personcasualtiesrate,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="PERSONMEDICALLIMIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.personmedicallimit != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.personmedicallimit,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="PERSONMEDICALRATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.personmedicalrate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.personmedicalrate,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="FIELDALIMIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.fieldalimit != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.fieldalimit,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="FIELDARATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.fieldarate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.fieldarate,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="FIELDBLIMIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.fieldblimit != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.fieldblimit,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="FIELDBRATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.fieldbrate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.fieldbrate,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="FIELDC = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.fieldc != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.fieldc,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="FIELD = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.field != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.field,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="PERSONSUMPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.personsumpremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.personsumpremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="LISTBELONGIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.listbelongind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.listbelongind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INPUTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.inputDate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="UPDATESYSDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.updatesysdate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
		</trim>
		where ID in
		<foreach close=")" collection="list" item="item" open="(" separator=", ">
			#{item.id,jdbcType=VARCHAR}
		</foreach>
	</update>
	<insert id="batchInsert" parameterType="java.util.List">
		INSERT  ALL
		<foreach collection="list" item="item" separator="">
			INTO GUPOLICYEMPLOYERSPLAN VALUES
			(#{item.id,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.subpolicyno,jdbcType=VARCHAR},
			#{item.planid,jdbcType=VARCHAR}, #{item.plancode,jdbcType=VARCHAR}, #{item.riskCode,jdbcType=VARCHAR},
			#{item.itemNo,jdbcType=DECIMAL}, #{item.itemCode,jdbcType=VARCHAR}, #{item.itemdetailno,jdbcType=VARCHAR},
			#{item.currency,jdbcType=VARCHAR}, #{item.industrytype,jdbcType=VARCHAR}, #{item.industrydetail,jdbcType=VARCHAR},
			#{item.schemecode,jdbcType=VARCHAR}, #{item.schemename,jdbcType=VARCHAR}, #{item.personneltype,jdbcType=VARCHAR},
			#{item.dynamictargettype,jdbcType=VARCHAR}, #{item.listseqno,jdbcType=DECIMAL},
			#{item.targetflag,jdbcType=VARCHAR}, #{item.targetcalculate,jdbcType=VARCHAR},
			#{item.employees,jdbcType=VARCHAR}, #{item.employeesthischange,jdbcType=VARCHAR},
			#{item.legalfeeslimit,jdbcType=DECIMAL}, #{item.legalfeesrate,jdbcType=DECIMAL},
			#{item.legalsumpremium,jdbcType=DECIMAL}, #{item.personcasualtieslimit,jdbcType=DECIMAL},
			#{item.personcasualtiesrate,jdbcType=DECIMAL}, #{item.personmedicallimit,jdbcType=DECIMAL},
			#{item.personmedicalrate,jdbcType=DECIMAL}, #{item.fieldalimit,jdbcType=DECIMAL},
			#{item.fieldarate,jdbcType=DECIMAL}, #{item.fieldblimit,jdbcType=DECIMAL}, #{item.fieldbrate,jdbcType=DECIMAL},
			#{item.fieldc,jdbcType=VARCHAR}, #{item.field,jdbcType=VARCHAR}, #{item.personsumpremium,jdbcType=DECIMAL},
			#{item.listbelongind,jdbcType=VARCHAR}, #{item.inputDate,jdbcType=TIMESTAMP}, #{item.updatesysdate,jdbcType=TIMESTAMP}
			)
		</foreach>
		select 1 from dual
	</insert>
</mapper>
