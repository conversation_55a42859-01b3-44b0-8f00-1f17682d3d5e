<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicyholder.dao.GupolicyholderDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicyholder.po.Gupolicyholder">
		<id column="ID" property="id" />
		<result column="POLICYNO" property="policyNo" />
		<result column="PRODUCTCODE" property="productcode" />
		<result column="INSURANCECOMPANYCODE" property="insurancecompanycode" />
		<result column="INSUREDTYPE" property="insuredType" />
		<result column="VIPIND" property="vipind" />
		<result column="INSUREDCODE" property="insuredCode" />
		<result column="INSUREDNAME" property="insuredName" />
		<result column="INSUREDADDRESS" property="insuredAddress" />
		<result column="IDENTIFYTYPE" property="identifyType" />
		<result column="IDENTIFYNUMBER" property="identifyNumber" />
		<result column="SEX" property="sex" />
		<result column="BIRTHDATE" property="birthDate" />
		<result column="STARLEVEL" property="starlevel" />
		<result column="SOCIALSECURITYNO" property="socialsecurityno" />
		<result column="ITEMPROVINCECODE" property="itemprovincecode" />
		<result column="ITEMPROVINCECNAME" property="itemprovincecname" />
		<result column="ITEMCITYCODE" property="itemcitycode" />
		<result column="ITEMCITYCNAME" property="itemcitycname" />
		<result column="COUNTYCODE" property="countycode" />
		<result column="COUNTYCNAME" property="countycname" />
		<result column="POSTCODE" property="postCode" />
		<result column="MOBILEPHONE" property="mobilephone" />
		<result column="OFFICEPHONE" property="officephone" />
		<result column="HOMEPHONE" property="homePhone" />
		<result column="BANKNAME" property="bankName" />
		<result column="BANKNUMBER" property="banknumber" />
		<result column="OCCUPATIONCODE" property="occupationCode" />
		<result column="COMPANYNATURE" property="companynature" />
		<result column="NUMBEROFUNITS" property="numberofunits" />
		<result column="EMAIL" property="email" />
		<result column="INDUSTRYMAINCODE" property="industrymaincode" />
		<result column="INDUSTRYKINDCODE" property="industrykindcode" />
		<result column="CONTACTNAME" property="contactname" />
		<result column="CONTACTSEX" property="contactsex" />
		<result column="CONTACTPOSITION" property="contactposition" />
		<result column="CONTACTDEPARTMENT" property="contactdepartment" />
		<result column="CONTACTOFFICENUMBER" property="contactofficenumber" />
		<result column="CONTACTMOBILE" property="contactmobile" />
		<result column="CONTACTPHONE" property="contactphone" />
		<result column="REMARK" property="remark" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		POLICYNO,
		PRODUCTCODE,
		INSURANCECOMPANYCODE,
		INSUREDTYPE,
		VIPIND,
		INSUREDCODE,
		INSUREDNAME,
		INSUREDADDRESS,
		IDENTIFYTYPE,
		IDENTIFYNUMBER,
		SEX,
		BIRTHDATE,
		STARLEVEL,
		SOCIALSECURITYNO,
		ITEMPROVINCECODE,
		ITEMPROVINCECNAME,
		ITEMCITYCODE,
		ITEMCITYCNAME,
		COUNTYCODE,
		COUNTYCNAME,
		POSTCODE,
		MOBILEPHONE,
		OFFICEPHONE,
		HOMEPHONE,
		BANKNAME,
		BANKNUMBER,
		OCCUPATIONCODE,
		COMPANYNATURE,
		NUMBEROFUNITS,
		EMAIL,
		INDUSTRYMAINCODE,
		INDUSTRYKINDCODE,
		CONTACTNAME,
		CONTACTSEX,
		CONTACTPOSITION,
		CONTACTDEPARTMENT,
		CONTACTOFFICENUMBER,
		CONTACTMOBILE,
		CONTACTPHONE,
		REMARK,
		INPUTDATE,
		UPDATESYSDATE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="productcode != null and productcode != ''" >
			and PRODUCTCODE = #{productcode}
		</if>
		<if test="insurancecompanycode != null and insurancecompanycode != ''" >
			and INSURANCECOMPANYCODE = #{insurancecompanycode}
		</if>
		<if test="insuredType != null and insuredType != ''" >
			and INSUREDTYPE = #{insuredType}
		</if>
		<if test="vipind != null and vipind != ''" >
			and VIPIND = #{vipind}
		</if>
		<if test="insuredCode != null and insuredCode != ''" >
			and INSUREDCODE = #{insuredCode}
		</if>
		<if test="insuredName != null and insuredName != ''" >
			and INSUREDNAME = #{insuredName}
		</if>
		<if test="insuredAddress != null and insuredAddress != ''" >
			and INSUREDADDRESS = #{insuredAddress}
		</if>
		<if test="identifyType != null and identifyType != ''" >
			and IDENTIFYTYPE = #{identifyType}
		</if>
		<if test="identifyNumber != null and identifyNumber != ''" >
			and IDENTIFYNUMBER = #{identifyNumber}
		</if>
		<if test="sex != null and sex != ''" >
			and SEX = #{sex}
		</if>
		<if test="birthDate != null and birthDate != ''" >
			and BIRTHDATE = #{birthDate}
		</if>
		<if test="starlevel != null and starlevel != ''" >
			and STARLEVEL = #{starlevel}
		</if>
		<if test="socialsecurityno != null and socialsecurityno != ''" >
			and SOCIALSECURITYNO = #{socialsecurityno}
		</if>
		<if test="itemprovincecode != null and itemprovincecode != ''" >
			and ITEMPROVINCECODE = #{itemprovincecode}
		</if>
		<if test="itemprovincecname != null and itemprovincecname != ''" >
			and ITEMPROVINCECNAME = #{itemprovincecname}
		</if>
		<if test="itemcitycode != null and itemcitycode != ''" >
			and ITEMCITYCODE = #{itemcitycode}
		</if>
		<if test="itemcitycname != null and itemcitycname != ''" >
			and ITEMCITYCNAME = #{itemcitycname}
		</if>
		<if test="countycode != null and countycode != ''" >
			and COUNTYCODE = #{countycode}
		</if>
		<if test="countycname != null and countycname != ''" >
			and COUNTYCNAME = #{countycname}
		</if>
		<if test="postCode != null and postCode != ''" >
			and POSTCODE = #{postCode}
		</if>
		<if test="mobilephone != null and mobilephone != ''" >
			and MOBILEPHONE = #{mobilephone}
		</if>
		<if test="officephone != null and officephone != ''" >
			and OFFICEPHONE = #{officephone}
		</if>
		<if test="homePhone != null and homePhone != ''" >
			and HOMEPHONE = #{homePhone}
		</if>
		<if test="bankName != null and bankName != ''" >
			and BANKNAME = #{bankName}
		</if>
		<if test="banknumber != null and banknumber != ''" >
			and BANKNUMBER = #{banknumber}
		</if>
		<if test="occupationCode != null and occupationCode != ''" >
			and OCCUPATIONCODE = #{occupationCode}
		</if>
		<if test="companynature != null and companynature != ''" >
			and COMPANYNATURE = #{companynature}
		</if>
		<if test="numberofunits != null and numberofunits != ''" >
			and NUMBEROFUNITS = #{numberofunits}
		</if>
		<if test="email != null and email != ''" >
			and EMAIL = #{email}
		</if>
		<if test="industrymaincode != null and industrymaincode != ''" >
			and INDUSTRYMAINCODE = #{industrymaincode}
		</if>
		<if test="industrykindcode != null and industrykindcode != ''" >
			and INDUSTRYKINDCODE = #{industrykindcode}
		</if>
		<if test="contactname != null and contactname != ''" >
			and CONTACTNAME = #{contactname}
		</if>
		<if test="contactsex != null and contactsex != ''" >
			and CONTACTSEX = #{contactsex}
		</if>
		<if test="contactposition != null and contactposition != ''" >
			and CONTACTPOSITION = #{contactposition}
		</if>
		<if test="contactdepartment != null and contactdepartment != ''" >
			and CONTACTDEPARTMENT = #{contactdepartment}
		</if>
		<if test="contactofficenumber != null and contactofficenumber != ''" >
			and CONTACTOFFICENUMBER = #{contactofficenumber}
		</if>
		<if test="contactmobile != null and contactmobile != ''" >
			and CONTACTMOBILE = #{contactmobile}
		</if>
		<if test="contactphone != null and contactphone != ''" >
			and CONTACTPHONE = #{contactphone}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYHOLDER
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYHOLDER
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYHOLDER
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicyholder.po.Gupolicyholder">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYHOLDER
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYHOLDER
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicyholder.po.Gupolicyholder">
		insert into GUPOLICYHOLDER (
			ID,
			POLICYNO,
			PRODUCTCODE,
			INSURANCECOMPANYCODE,
			INSUREDTYPE,
			VIPIND,
			INSUREDCODE,
			INSUREDNAME,
			INSUREDADDRESS,
			IDENTIFYTYPE,
			IDENTIFYNUMBER,
			SEX,
			BIRTHDATE,
			STARLEVEL,
			SOCIALSECURITYNO,
			ITEMPROVINCECODE,
			ITEMPROVINCECNAME,
			ITEMCITYCODE,
			ITEMCITYCNAME,
			COUNTYCODE,
			COUNTYCNAME,
			POSTCODE,
			MOBILEPHONE,
			OFFICEPHONE,
			HOMEPHONE,
			BANKNAME,
			BANKNUMBER,
			OCCUPATIONCODE,
			COMPANYNATURE,
			NUMBEROFUNITS,
			EMAIL,
			INDUSTRYMAINCODE,
			INDUSTRYKINDCODE,
			CONTACTNAME,
			CONTACTSEX,
			CONTACTPOSITION,
			CONTACTDEPARTMENT,
			CONTACTOFFICENUMBER,
			CONTACTMOBILE,
			CONTACTPHONE,
			REMARK,
			INPUTDATE,
			UPDATESYSDATE
		) values (
			#{id},
			#{policyNo},
			#{productcode},
			#{insurancecompanycode},
			#{insuredType},
			#{vipind},
			#{insuredCode},
			#{insuredName},
			#{insuredAddress},
			#{identifyType},
			#{identifyNumber},
			#{sex},
			#{birthDate},
			#{starlevel},
			#{socialsecurityno},
			#{itemprovincecode},
			#{itemprovincecname},
			#{itemcitycode},
			#{itemcitycname},
			#{countycode},
			#{countycname},
			#{postCode},
			#{mobilephone},
			#{officephone},
			#{homePhone},
			#{bankName},
			#{banknumber},
			#{occupationCode},
			#{companynature},
			#{numberofunits},
			#{email},
			#{industrymaincode},
			#{industrykindcode},
			#{contactname},
			#{contactsex},
			#{contactposition},
			#{contactdepartment},
			#{contactofficenumber},
			#{contactmobile},
			#{contactphone},
			#{remark},
			#{inputDate},
			#{updatesysdate}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicyholder.po.Gupolicyholder">
		insert into GUPOLICYHOLDER
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="productcode != null" >
				PRODUCTCODE,
			</if>
			<if test="insurancecompanycode != null" >
				INSURANCECOMPANYCODE,
			</if>
			<if test="insuredType != null" >
				INSUREDTYPE,
			</if>
			<if test="vipind != null" >
				VIPIND,
			</if>
			<if test="insuredCode != null" >
				INSUREDCODE,
			</if>
			<if test="insuredName != null" >
				INSUREDNAME,
			</if>
			<if test="insuredAddress != null" >
				INSUREDADDRESS,
			</if>
			<if test="identifyType != null" >
				IDENTIFYTYPE,
			</if>
			<if test="identifyNumber != null" >
				IDENTIFYNUMBER,
			</if>
			<if test="sex != null" >
				SEX,
			</if>
			<if test="birthDate != null" >
				BIRTHDATE,
			</if>
			<if test="starlevel != null" >
				STARLEVEL,
			</if>
			<if test="socialsecurityno != null" >
				SOCIALSECURITYNO,
			</if>
			<if test="itemprovincecode != null" >
				ITEMPROVINCECODE,
			</if>
			<if test="itemprovincecname != null" >
				ITEMPROVINCECNAME,
			</if>
			<if test="itemcitycode != null" >
				ITEMCITYCODE,
			</if>
			<if test="itemcitycname != null" >
				ITEMCITYCNAME,
			</if>
			<if test="countycode != null" >
				COUNTYCODE,
			</if>
			<if test="countycname != null" >
				COUNTYCNAME,
			</if>
			<if test="postCode != null" >
				POSTCODE,
			</if>
			<if test="mobilephone != null" >
				MOBILEPHONE,
			</if>
			<if test="officephone != null" >
				OFFICEPHONE,
			</if>
			<if test="homePhone != null" >
				HOMEPHONE,
			</if>
			<if test="bankName != null" >
				BANKNAME,
			</if>
			<if test="banknumber != null" >
				BANKNUMBER,
			</if>
			<if test="occupationCode != null" >
				OCCUPATIONCODE,
			</if>
			<if test="companynature != null" >
				COMPANYNATURE,
			</if>
			<if test="numberofunits != null" >
				NUMBEROFUNITS,
			</if>
			<if test="email != null" >
				EMAIL,
			</if>
			<if test="industrymaincode != null" >
				INDUSTRYMAINCODE,
			</if>
			<if test="industrykindcode != null" >
				INDUSTRYKINDCODE,
			</if>
			<if test="contactname != null" >
				CONTACTNAME,
			</if>
			<if test="contactsex != null" >
				CONTACTSEX,
			</if>
			<if test="contactposition != null" >
				CONTACTPOSITION,
			</if>
			<if test="contactdepartment != null" >
				CONTACTDEPARTMENT,
			</if>
			<if test="contactofficenumber != null" >
				CONTACTOFFICENUMBER,
			</if>
			<if test="contactmobile != null" >
				CONTACTMOBILE,
			</if>
			<if test="contactphone != null" >
				CONTACTPHONE,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="productcode != null" >
				#{productcode},
			</if>
			<if test="insurancecompanycode != null" >
				#{insurancecompanycode},
			</if>
			<if test="insuredType != null" >
				#{insuredType},
			</if>
			<if test="vipind != null" >
				#{vipind},
			</if>
			<if test="insuredCode != null" >
				#{insuredCode},
			</if>
			<if test="insuredName != null" >
				#{insuredName},
			</if>
			<if test="insuredAddress != null" >
				#{insuredAddress},
			</if>
			<if test="identifyType != null" >
				#{identifyType},
			</if>
			<if test="identifyNumber != null" >
				#{identifyNumber},
			</if>
			<if test="sex != null" >
				#{sex},
			</if>
			<if test="birthDate != null" >
				#{birthDate},
			</if>
			<if test="starlevel != null" >
				#{starlevel},
			</if>
			<if test="socialsecurityno != null" >
				#{socialsecurityno},
			</if>
			<if test="itemprovincecode != null" >
				#{itemprovincecode},
			</if>
			<if test="itemprovincecname != null" >
				#{itemprovincecname},
			</if>
			<if test="itemcitycode != null" >
				#{itemcitycode},
			</if>
			<if test="itemcitycname != null" >
				#{itemcitycname},
			</if>
			<if test="countycode != null" >
				#{countycode},
			</if>
			<if test="countycname != null" >
				#{countycname},
			</if>
			<if test="postCode != null" >
				#{postCode},
			</if>
			<if test="mobilephone != null" >
				#{mobilephone},
			</if>
			<if test="officephone != null" >
				#{officephone},
			</if>
			<if test="homePhone != null" >
				#{homePhone},
			</if>
			<if test="bankName != null" >
				#{bankName},
			</if>
			<if test="banknumber != null" >
				#{banknumber},
			</if>
			<if test="occupationCode != null" >
				#{occupationCode},
			</if>
			<if test="companynature != null" >
				#{companynature},
			</if>
			<if test="numberofunits != null" >
				#{numberofunits},
			</if>
			<if test="email != null" >
				#{email},
			</if>
			<if test="industrymaincode != null" >
				#{industrymaincode},
			</if>
			<if test="industrykindcode != null" >
				#{industrykindcode},
			</if>
			<if test="contactname != null" >
				#{contactname},
			</if>
			<if test="contactsex != null" >
				#{contactsex},
			</if>
			<if test="contactposition != null" >
				#{contactposition},
			</if>
			<if test="contactdepartment != null" >
				#{contactdepartment},
			</if>
			<if test="contactofficenumber != null" >
				#{contactofficenumber},
			</if>
			<if test="contactmobile != null" >
				#{contactmobile},
			</if>
			<if test="contactphone != null" >
				#{contactphone},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicyholder.po.Gupolicyholder">
		update GUPOLICYHOLDER 
		<set>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="productcode != null" >
				PRODUCTCODE=#{productcode},
			</if>
			<if test="insurancecompanycode != null" >
				INSURANCECOMPANYCODE=#{insurancecompanycode},
			</if>
			<if test="insuredType != null" >
				INSUREDTYPE=#{insuredType},
			</if>
			<if test="vipind != null" >
				VIPIND=#{vipind},
			</if>
			<if test="insuredCode != null" >
				INSUREDCODE=#{insuredCode},
			</if>
			<if test="insuredName != null" >
				INSUREDNAME=#{insuredName},
			</if>
			<if test="insuredAddress != null" >
				INSUREDADDRESS=#{insuredAddress},
			</if>
			<if test="identifyType != null" >
				IDENTIFYTYPE=#{identifyType},
			</if>
			<if test="identifyNumber != null" >
				IDENTIFYNUMBER=#{identifyNumber},
			</if>
			<if test="sex != null" >
				SEX=#{sex},
			</if>
			<if test="birthDate != null" >
				BIRTHDATE=#{birthDate},
			</if>
			<if test="starlevel != null" >
				STARLEVEL=#{starlevel},
			</if>
			<if test="socialsecurityno != null" >
				SOCIALSECURITYNO=#{socialsecurityno},
			</if>
			<if test="itemprovincecode != null" >
				ITEMPROVINCECODE=#{itemprovincecode},
			</if>
			<if test="itemprovincecname != null" >
				ITEMPROVINCECNAME=#{itemprovincecname},
			</if>
			<if test="itemcitycode != null" >
				ITEMCITYCODE=#{itemcitycode},
			</if>
			<if test="itemcitycname != null" >
				ITEMCITYCNAME=#{itemcitycname},
			</if>
			<if test="countycode != null" >
				COUNTYCODE=#{countycode},
			</if>
			<if test="countycname != null" >
				COUNTYCNAME=#{countycname},
			</if>
			<if test="postCode != null" >
				POSTCODE=#{postCode},
			</if>
			<if test="mobilephone != null" >
				MOBILEPHONE=#{mobilephone},
			</if>
			<if test="officephone != null" >
				OFFICEPHONE=#{officephone},
			</if>
			<if test="homePhone != null" >
				HOMEPHONE=#{homePhone},
			</if>
			<if test="bankName != null" >
				BANKNAME=#{bankName},
			</if>
			<if test="banknumber != null" >
				BANKNUMBER=#{banknumber},
			</if>
			<if test="occupationCode != null" >
				OCCUPATIONCODE=#{occupationCode},
			</if>
			<if test="companynature != null" >
				COMPANYNATURE=#{companynature},
			</if>
			<if test="numberofunits != null" >
				NUMBEROFUNITS=#{numberofunits},
			</if>
			<if test="email != null" >
				EMAIL=#{email},
			</if>
			<if test="industrymaincode != null" >
				INDUSTRYMAINCODE=#{industrymaincode},
			</if>
			<if test="industrykindcode != null" >
				INDUSTRYKINDCODE=#{industrykindcode},
			</if>
			<if test="contactname != null" >
				CONTACTNAME=#{contactname},
			</if>
			<if test="contactsex != null" >
				CONTACTSEX=#{contactsex},
			</if>
			<if test="contactposition != null" >
				CONTACTPOSITION=#{contactposition},
			</if>
			<if test="contactdepartment != null" >
				CONTACTDEPARTMENT=#{contactdepartment},
			</if>
			<if test="contactofficenumber != null" >
				CONTACTOFFICENUMBER=#{contactofficenumber},
			</if>
			<if test="contactmobile != null" >
				CONTACTMOBILE=#{contactmobile},
			</if>
			<if test="contactphone != null" >
				CONTACTPHONE=#{contactphone},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicyholder.po.Gupolicyholder">
		update GUPOLICYHOLDER set
			POLICYNO=#{policyNo},
			PRODUCTCODE=#{productcode},
			INSURANCECOMPANYCODE=#{insurancecompanycode},
			INSUREDTYPE=#{insuredType},
			VIPIND=#{vipind},
			INSUREDCODE=#{insuredCode},
			INSUREDNAME=#{insuredName},
			INSUREDADDRESS=#{insuredAddress},
			IDENTIFYTYPE=#{identifyType},
			IDENTIFYNUMBER=#{identifyNumber},
			SEX=#{sex},
			BIRTHDATE=#{birthDate},
			STARLEVEL=#{starlevel},
			SOCIALSECURITYNO=#{socialsecurityno},
			ITEMPROVINCECODE=#{itemprovincecode},
			ITEMPROVINCECNAME=#{itemprovincecname},
			ITEMCITYCODE=#{itemcitycode},
			ITEMCITYCNAME=#{itemcitycname},
			COUNTYCODE=#{countycode},
			COUNTYCNAME=#{countycname},
			POSTCODE=#{postCode},
			MOBILEPHONE=#{mobilephone},
			OFFICEPHONE=#{officephone},
			HOMEPHONE=#{homePhone},
			BANKNAME=#{bankName},
			BANKNUMBER=#{banknumber},
			OCCUPATIONCODE=#{occupationCode},
			COMPANYNATURE=#{companynature},
			NUMBEROFUNITS=#{numberofunits},
			EMAIL=#{email},
			INDUSTRYMAINCODE=#{industrymaincode},
			INDUSTRYKINDCODE=#{industrykindcode},
			CONTACTNAME=#{contactname},
			CONTACTSEX=#{contactsex},
			CONTACTPOSITION=#{contactposition},
			CONTACTDEPARTMENT=#{contactdepartment},
			CONTACTOFFICENUMBER=#{contactofficenumber},
			CONTACTMOBILE=#{contactmobile},
			CONTACTPHONE=#{contactphone},
			REMARK=#{remark},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
		where ID = #{id}	</update>

	<update id="batchUpdate" parameterType="java.util.List">
		update GUPOLICYHOLDER
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="POLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="PRODUCTCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.productcode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSURANCECOMPANYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insurancecompanycode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredType,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="VIPIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.vipind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredName,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDADDRESS = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredAddress,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="IDENTIFYTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.identifyType,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="IDENTIFYNUMBER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.identifyNumber,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SEX = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.sex,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="BIRTHDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.birthDate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="STARLEVEL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.starlevel,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SOCIALSECURITYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.socialsecurityno,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMPROVINCECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemprovincecode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMPROVINCECNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemprovincecname,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMCITYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemcitycode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMCITYCNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemcitycname,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="COUNTYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.countycode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="COUNTYCNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.countycname,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="POSTCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.postCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="MOBILEPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.mobilephone,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="OFFICEPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.officephone,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="HOMEPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.homePhone,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="BANKNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.bankName,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="BANKNUMBER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.banknumber,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="OCCUPATIONCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.occupationCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="COMPANYNATURE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.companynature,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="NUMBEROFUNITS = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.numberofunits,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="EMAIL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.email,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INDUSTRYMAINCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.industrymaincode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INDUSTRYKINDCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.industrykindcode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactname,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTSEX = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactsex,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTPOSITION = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactposition,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTDEPARTMENT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactdepartment,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTOFFICENUMBER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactofficenumber,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTMOBILE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactmobile,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactphone,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="REMARK = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INPUTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="UPDATESYSDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
		</trim>
		where ID in
		<foreach close=")" collection="list" item="item" open="(" separator=", ">
			#{item.id,jdbcType=VARCHAR}
		</foreach>
	</update>
	<update id="batchUpdateSelective" parameterType="java.util.List">
		update GUPOLICYHOLDER
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="POLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.policyNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="PRODUCTCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.productcode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.productcode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSURANCECOMPANYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insurancecompanycode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insurancecompanycode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredType != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredType,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="VIPIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.vipind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.vipind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredName != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredName,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDADDRESS = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredAddress != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredAddress,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="IDENTIFYTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.identifyType != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.identifyType,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="IDENTIFYNUMBER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.identifyNumber != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.identifyNumber,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SEX = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.sex != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.sex,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="BIRTHDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.birthDate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.birthDate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="STARLEVEL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.starlevel != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.starlevel,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SOCIALSECURITYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.socialsecurityno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.socialsecurityno,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMPROVINCECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemprovincecode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemprovincecode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMPROVINCECNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemprovincecname != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemprovincecname,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMCITYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemcitycode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemcitycode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMCITYCNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemcitycname != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemcitycname,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="COUNTYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.countycode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.countycode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="COUNTYCNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.countycname != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.countycname,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="POSTCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.postCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.postCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="MOBILEPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.mobilephone != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.mobilephone,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="OFFICEPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.officephone != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.officephone,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="HOMEPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.homePhone != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.homePhone,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="BANKNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.bankName != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.bankName,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="BANKNUMBER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.banknumber != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.banknumber,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="OCCUPATIONCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.occupationCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.occupationCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="COMPANYNATURE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.companynature != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.companynature,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="NUMBEROFUNITS = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.numberofunits != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.numberofunits,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="EMAIL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.email != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.email,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INDUSTRYMAINCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.industrymaincode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.industrymaincode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INDUSTRYKINDCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.industrykindcode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.industrykindcode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactname != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactname,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTSEX = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactsex != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactsex,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTPOSITION = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactposition != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactposition,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTDEPARTMENT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactdepartment != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactdepartment,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTOFFICENUMBER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactofficenumber != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactofficenumber,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTMOBILE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactmobile != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactmobile,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactphone != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactphone,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="REMARK = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.remark != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INPUTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.inputDate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="UPDATESYSDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.updatesysdate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
		</trim>
		where ID in
		<foreach close=")" collection="list" item="item" open="(" separator=", ">
			#{item.id,jdbcType=VARCHAR}
		</foreach>
	</update>
	<insert id="batchInsert" parameterType="java.util.List">
		INSERT  ALL
		<foreach collection="list" item="item" separator="">
			INTO GUPOLICYHOLDER VALUES
			(#{item.id,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.productcode,jdbcType=VARCHAR},
			#{item.insurancecompanycode,jdbcType=VARCHAR}, #{item.insuredType,jdbcType=VARCHAR},
			#{item.vipind,jdbcType=VARCHAR}, #{item.insuredCode,jdbcType=VARCHAR}, #{item.insuredName,jdbcType=VARCHAR},
			#{item.insuredAddress,jdbcType=VARCHAR}, #{item.identifyType,jdbcType=VARCHAR},
			#{item.identifyNumber,jdbcType=VARCHAR}, #{item.sex,jdbcType=VARCHAR}, #{item.birthDate,jdbcType=TIMESTAMP},
			#{item.starlevel,jdbcType=VARCHAR}, #{item.socialsecurityno,jdbcType=VARCHAR},
			#{item.itemprovincecode,jdbcType=VARCHAR}, #{item.itemprovincecname,jdbcType=VARCHAR},
			#{item.itemcitycode,jdbcType=VARCHAR}, #{item.itemcitycname,jdbcType=VARCHAR},
			#{item.countycode,jdbcType=VARCHAR}, #{item.countycname,jdbcType=VARCHAR}, #{item.postCode,jdbcType=VARCHAR},
			#{item.mobilephone,jdbcType=VARCHAR}, #{item.officephone,jdbcType=VARCHAR}, #{item.homePhone,jdbcType=VARCHAR},
			#{item.bankName,jdbcType=VARCHAR}, #{item.banknumber,jdbcType=VARCHAR}, #{item.occupationCode,jdbcType=VARCHAR},
			#{item.companynature,jdbcType=VARCHAR}, #{item.numberofunits,jdbcType=DECIMAL},
			#{item.email,jdbcType=VARCHAR}, #{item.industrymaincode,jdbcType=VARCHAR}, #{item.industrykindcode,jdbcType=VARCHAR},
			#{item.contactname,jdbcType=VARCHAR}, #{item.contactsex,jdbcType=VARCHAR}, #{item.contactposition,jdbcType=VARCHAR},
			#{item.contactdepartment,jdbcType=VARCHAR}, #{item.contactofficenumber,jdbcType=VARCHAR},
			#{item.contactmobile,jdbcType=VARCHAR}, #{item.contactphone,jdbcType=VARCHAR},
			#{item.remark,jdbcType=VARCHAR}, #{item.inputDate,jdbcType=TIMESTAMP}, #{item.updatesysdate,jdbcType=TIMESTAMP}
			)
		</foreach>
		select 1 from dual
	</insert>

	<delete id="deleteByPolicyNo" parameterType="string">
		delete from GUPOLICYHOLDER where policyNo=#{policyNo}
	</delete>
</mapper>
