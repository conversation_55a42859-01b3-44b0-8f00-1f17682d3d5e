<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicyinsured.dao.GupolicyinsuredDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicyinsured.po.Gupolicyinsured">
		<id column="ID" property="id" />
		<result column="POLICYNO" property="policyNo" />
		<result column="PLANCODE" property="plancode" />
		<result column="RISKCODE" property="riskCode" />
		<result column="SUBPOLICYNO" property="subpolicyno" />
		<result column="SERIALNO" property="serialNo" />
		<result column="INSUREDTYPE" property="insuredType" />
		<result column="INSUREDCODE" property="insuredCode" />
		<result column="INSUREDNAME" property="insuredName" />
		<result column="INSUREDADDRESS" property="insuredAddress" />
		<result column="INSUREDBUSINESSSOURCE" property="insuredbusinesssource" />
		<result column="INSUREDFLAG" property="insuredFlag" />
		<result column="INSUREDROLE" property="insuredrole" />
		<result column="IDENTIFYTYPE" property="identifyType" />
		<result column="IDENTIFYNUMBER" property="identifyNumber" />
		<result column="INSUREDRELATION" property="insuredrelation" />
		<result column="RELATESERIALNO" property="relateSerialNo" />
		<result column="INSUREDIND" property="insuredind" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="INSUREDSEX" property="insuredsex" />
		<result column="INSUREDBIRTHDATE" property="insuredbirthdate" />
		<result column="INSUREDPOSTCODE" property="insuredpostcode" />
		<result column="INSUREDOFFICEPHONE" property="insuredofficephone" />
		<result column="INSUREDMOBILEPHONE" property="insuredmobilephone" />
		<result column="INSUREDHOMEPHONE" property="insuredhomephone" />
		<result column="CONTACTNAME" property="contactname" />
		<result column="CONTACTPHONE" property="contactphone" />
		<result column="MARRIAGESTATUS" property="marriagestatus" />
		<result column="EDUCATIONBACKGROUND" property="educationbackground" />
		<result column="RELATIONWITHHOLDER" property="relationwithholder" />
		<result column="CONTACTSEX" property="contactsex" />
		<result column="CONTACTTYPE" property="contacttype" />
		<result column="CONTACTDEPARTMENT" property="contactdepartment" />
		<result column="CONTACTPOSITION" property="contactposition" />
		<result column="CONTACTOFFICENUMBER" property="contactofficenumber" />
		<result column="CONTACTMOBILE" property="contactmobile" />
		<result column="RELATEDTYPE" property="relatedtype" />
		<result column="ITEMPROVINCECODE" property="itemprovincecode" />
		<result column="ITEMCITYCODE" property="itemcitycode" />
		<result column="ITEMPROVINCECNAME" property="itemprovincecname" />
		<result column="ITEMCITYCNAME" property="itemcitycname" />
		<result column="MONEYLAUNDERINGIND" property="moneylaunderingind" />
		<result column="STARLEVEL" property="starlevel" />
		<result column="VIPIND" property="vipind" />
		<result column="MACHINECODE" property="machinecode" />
		<result column="IDCOLLECTIND" property="idcollectind" />
		<result column="COUNTYCODE" property="countycode" />
		<result column="EMAIL" property="email" />
		<result column="INDUSTRYMAINCODE" property="industrymaincode" />
		<result column="INDUSTRYKINDCODE" property="industrykindcode" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		POLICYNO,
		PLANCODE,
		RISKCODE,
		SUBPOLICYNO,
		SERIALNO,
		INSUREDTYPE,
		INSUREDCODE,
		INSUREDNAME,
		INSUREDADDRESS,
		INSUREDBUSINESSSOURCE,
		INSUREDFLAG,
		INSUREDROLE,
		IDENTIFYTYPE,
		IDENTIFYNUMBER,
		INSUREDRELATION,
		RELATESERIALNO,
		INSUREDIND,
		REMARK,
		FLAG,
		INSUREDSEX,
		INSUREDBIRTHDATE,
		INSUREDPOSTCODE,
		INSUREDOFFICEPHONE,
		INSUREDMOBILEPHONE,
		INSUREDHOMEPHONE,
		CONTACTNAME,
		CONTACTPHONE,
		MARRIAGESTATUS,
		EDUCATIONBACKGROUND,
		RELATIONWITHHOLDER,
		CONTACTSEX,
		CONTACTTYPE,
		CONTACTDEPARTMENT,
		CONTACTPOSITION,
		CONTACTOFFICENUMBER,
		CONTACTMOBILE,
		RELATEDTYPE,
		ITEMPROVINCECODE,
		ITEMCITYCODE,
		ITEMPROVINCECNAME,
		ITEMCITYCNAME,
		MONEYLAUNDERINGIND,
		STARLEVEL,
		VIPIND,
		MACHINECODE,
		IDCOLLECTIND,
		COUNTYCODE,
		EMAIL,
		INDUSTRYMAINCODE,
		INDUSTRYKINDCODE,
		INPUTDATE,
		UPDATESYSDATE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="plancode != null and plancode != ''" >
			and PLANCODE = #{plancode}
		</if>
		<if test="riskCode != null and riskCode != ''" >
			and RISKCODE = #{riskCode}
		</if>
		<if test="subpolicyno != null and subpolicyno != ''" >
			and SUBPOLICYNO = #{subpolicyno}
		</if>
		<if test="serialNo != null and serialNo != ''" >
			and SERIALNO = #{serialNo}
		</if>
		<if test="insuredType != null and insuredType != ''" >
			and INSUREDTYPE = #{insuredType}
		</if>
		<if test="insuredCode != null and insuredCode != ''" >
			and INSUREDCODE = #{insuredCode}
		</if>
		<if test="insuredName != null and insuredName != ''" >
			and INSUREDNAME = #{insuredName}
		</if>
		<if test="insuredAddress != null and insuredAddress != ''" >
			and INSUREDADDRESS = #{insuredAddress}
		</if>
		<if test="insuredbusinesssource != null and insuredbusinesssource != ''" >
			and INSUREDBUSINESSSOURCE = #{insuredbusinesssource}
		</if>
		<if test="insuredFlag != null and insuredFlag != ''" >
			and INSUREDFLAG = #{insuredFlag}
		</if>
		<if test="insuredrole != null and insuredrole != ''" >
			and INSUREDROLE = #{insuredrole}
		</if>
		<if test="identifyType != null and identifyType != ''" >
			and IDENTIFYTYPE = #{identifyType}
		</if>
		<if test="identifyNumber != null and identifyNumber != ''" >
			and IDENTIFYNUMBER = #{identifyNumber}
		</if>
		<if test="insuredrelation != null and insuredrelation != ''" >
			and INSUREDRELATION = #{insuredrelation}
		</if>
		<if test="relateSerialNo != null and relateSerialNo != ''" >
			and RELATESERIALNO = #{relateSerialNo}
		</if>
		<if test="insuredind != null and insuredind != ''" >
			and INSUREDIND = #{insuredind}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="insuredsex != null and insuredsex != ''" >
			and INSUREDSEX = #{insuredsex}
		</if>
		<if test="insuredbirthdate != null and insuredbirthdate != ''" >
			and INSUREDBIRTHDATE = #{insuredbirthdate}
		</if>
		<if test="insuredpostcode != null and insuredpostcode != ''" >
			and INSUREDPOSTCODE = #{insuredpostcode}
		</if>
		<if test="insuredofficephone != null and insuredofficephone != ''" >
			and INSUREDOFFICEPHONE = #{insuredofficephone}
		</if>
		<if test="insuredmobilephone != null and insuredmobilephone != ''" >
			and INSUREDMOBILEPHONE = #{insuredmobilephone}
		</if>
		<if test="insuredhomephone != null and insuredhomephone != ''" >
			and INSUREDHOMEPHONE = #{insuredhomephone}
		</if>
		<if test="contactname != null and contactname != ''" >
			and CONTACTNAME = #{contactname}
		</if>
		<if test="contactphone != null and contactphone != ''" >
			and CONTACTPHONE = #{contactphone}
		</if>
		<if test="marriagestatus != null and marriagestatus != ''" >
			and MARRIAGESTATUS = #{marriagestatus}
		</if>
		<if test="educationbackground != null and educationbackground != ''" >
			and EDUCATIONBACKGROUND = #{educationbackground}
		</if>
		<if test="relationwithholder != null and relationwithholder != ''" >
			and RELATIONWITHHOLDER = #{relationwithholder}
		</if>
		<if test="contactsex != null and contactsex != ''" >
			and CONTACTSEX = #{contactsex}
		</if>
		<if test="contacttype != null and contacttype != ''" >
			and CONTACTTYPE = #{contacttype}
		</if>
		<if test="contactdepartment != null and contactdepartment != ''" >
			and CONTACTDEPARTMENT = #{contactdepartment}
		</if>
		<if test="contactposition != null and contactposition != ''" >
			and CONTACTPOSITION = #{contactposition}
		</if>
		<if test="contactofficenumber != null and contactofficenumber != ''" >
			and CONTACTOFFICENUMBER = #{contactofficenumber}
		</if>
		<if test="contactmobile != null and contactmobile != ''" >
			and CONTACTMOBILE = #{contactmobile}
		</if>
		<if test="relatedtype != null and relatedtype != ''" >
			and RELATEDTYPE = #{relatedtype}
		</if>
		<if test="itemprovincecode != null and itemprovincecode != ''" >
			and ITEMPROVINCECODE = #{itemprovincecode}
		</if>
		<if test="itemcitycode != null and itemcitycode != ''" >
			and ITEMCITYCODE = #{itemcitycode}
		</if>
		<if test="itemprovincecname != null and itemprovincecname != ''" >
			and ITEMPROVINCECNAME = #{itemprovincecname}
		</if>
		<if test="itemcitycname != null and itemcitycname != ''" >
			and ITEMCITYCNAME = #{itemcitycname}
		</if>
		<if test="moneylaunderingind != null and moneylaunderingind != ''" >
			and MONEYLAUNDERINGIND = #{moneylaunderingind}
		</if>
		<if test="starlevel != null and starlevel != ''" >
			and STARLEVEL = #{starlevel}
		</if>
		<if test="vipind != null and vipind != ''" >
			and VIPIND = #{vipind}
		</if>
		<if test="machinecode != null and machinecode != ''" >
			and MACHINECODE = #{machinecode}
		</if>
		<if test="idcollectind != null and idcollectind != ''" >
			and IDCOLLECTIND = #{idcollectind}
		</if>
		<if test="countycode != null and countycode != ''" >
			and COUNTYCODE = #{countycode}
		</if>
		<if test="email != null and email != ''" >
			and EMAIL = #{email}
		</if>
		<if test="industrymaincode != null and industrymaincode != ''" >
			and INDUSTRYMAINCODE = #{industrymaincode}
		</if>
		<if test="industrykindcode != null and industrykindcode != ''" >
			and INDUSTRYKINDCODE = #{industrykindcode}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYINSURED
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYINSURED
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYINSURED
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicyinsured.po.Gupolicyinsured">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYINSURED
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYINSURED
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicyinsured.po.Gupolicyinsured">
		insert into GUPOLICYINSURED (
			ID,
			POLICYNO,
			PLANCODE,
			RISKCODE,
			SUBPOLICYNO,
			SERIALNO,
			INSUREDTYPE,
			INSUREDCODE,
			INSUREDNAME,
			INSUREDADDRESS,
			INSUREDBUSINESSSOURCE,
			INSUREDFLAG,
			INSUREDROLE,
			IDENTIFYTYPE,
			IDENTIFYNUMBER,
			INSUREDRELATION,
			RELATESERIALNO,
			INSUREDIND,
			REMARK,
			FLAG,
			INSUREDSEX,
			INSUREDBIRTHDATE,
			INSUREDPOSTCODE,
			INSUREDOFFICEPHONE,
			INSUREDMOBILEPHONE,
			INSUREDHOMEPHONE,
			CONTACTNAME,
			CONTACTPHONE,
			MARRIAGESTATUS,
			EDUCATIONBACKGROUND,
			RELATIONWITHHOLDER,
			CONTACTSEX,
			CONTACTTYPE,
			CONTACTDEPARTMENT,
			CONTACTPOSITION,
			CONTACTOFFICENUMBER,
			CONTACTMOBILE,
			RELATEDTYPE,
			ITEMPROVINCECODE,
			ITEMCITYCODE,
			ITEMPROVINCECNAME,
			ITEMCITYCNAME,
			MONEYLAUNDERINGIND,
			STARLEVEL,
			VIPIND,
			MACHINECODE,
			IDCOLLECTIND,
			COUNTYCODE,
			EMAIL,
			INDUSTRYMAINCODE,
			INDUSTRYKINDCODE,
			INPUTDATE,
			UPDATESYSDATE
		) values (
			#{id},
			#{policyNo},
			#{plancode},
			#{riskCode},
			#{subpolicyno},
			#{serialNo},
			#{insuredType},
			#{insuredCode},
			#{insuredName},
			#{insuredAddress},
			#{insuredbusinesssource},
			#{insuredFlag},
			#{insuredrole},
			#{identifyType},
			#{identifyNumber},
			#{insuredrelation},
			#{relateSerialNo},
			#{insuredind},
			#{remark},
			#{flag},
			#{insuredsex},
			#{insuredbirthdate},
			#{insuredpostcode},
			#{insuredofficephone},
			#{insuredmobilephone},
			#{insuredhomephone},
			#{contactname},
			#{contactphone},
			#{marriagestatus},
			#{educationbackground},
			#{relationwithholder},
			#{contactsex},
			#{contacttype},
			#{contactdepartment},
			#{contactposition},
			#{contactofficenumber},
			#{contactmobile},
			#{relatedtype},
			#{itemprovincecode},
			#{itemcitycode},
			#{itemprovincecname},
			#{itemcitycname},
			#{moneylaunderingind},
			#{starlevel},
			#{vipind},
			#{machinecode},
			#{idcollectind},
			#{countycode},
			#{email},
			#{industrymaincode},
			#{industrykindcode},
			#{inputDate},
			#{updatesysdate}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicyinsured.po.Gupolicyinsured">
		insert into GUPOLICYINSURED
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="plancode != null" >
				PLANCODE,
			</if>
			<if test="riskCode != null" >
				RISKCODE,
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO,
			</if>
			<if test="serialNo != null" >
				SERIALNO,
			</if>
			<if test="insuredType != null" >
				INSUREDTYPE,
			</if>
			<if test="insuredCode != null" >
				INSUREDCODE,
			</if>
			<if test="insuredName != null" >
				INSUREDNAME,
			</if>
			<if test="insuredAddress != null" >
				INSUREDADDRESS,
			</if>
			<if test="insuredbusinesssource != null" >
				INSUREDBUSINESSSOURCE,
			</if>
			<if test="insuredFlag != null" >
				INSUREDFLAG,
			</if>
			<if test="insuredrole != null" >
				INSUREDROLE,
			</if>
			<if test="identifyType != null" >
				IDENTIFYTYPE,
			</if>
			<if test="identifyNumber != null" >
				IDENTIFYNUMBER,
			</if>
			<if test="insuredrelation != null" >
				INSUREDRELATION,
			</if>
			<if test="relateSerialNo != null" >
				RELATESERIALNO,
			</if>
			<if test="insuredind != null" >
				INSUREDIND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="insuredsex != null" >
				INSUREDSEX,
			</if>
			<if test="insuredbirthdate != null" >
				INSUREDBIRTHDATE,
			</if>
			<if test="insuredpostcode != null" >
				INSUREDPOSTCODE,
			</if>
			<if test="insuredofficephone != null" >
				INSUREDOFFICEPHONE,
			</if>
			<if test="insuredmobilephone != null" >
				INSUREDMOBILEPHONE,
			</if>
			<if test="insuredhomephone != null" >
				INSUREDHOMEPHONE,
			</if>
			<if test="contactname != null" >
				CONTACTNAME,
			</if>
			<if test="contactphone != null" >
				CONTACTPHONE,
			</if>
			<if test="marriagestatus != null" >
				MARRIAGESTATUS,
			</if>
			<if test="educationbackground != null" >
				EDUCATIONBACKGROUND,
			</if>
			<if test="relationwithholder != null" >
				RELATIONWITHHOLDER,
			</if>
			<if test="contactsex != null" >
				CONTACTSEX,
			</if>
			<if test="contacttype != null" >
				CONTACTTYPE,
			</if>
			<if test="contactdepartment != null" >
				CONTACTDEPARTMENT,
			</if>
			<if test="contactposition != null" >
				CONTACTPOSITION,
			</if>
			<if test="contactofficenumber != null" >
				CONTACTOFFICENUMBER,
			</if>
			<if test="contactmobile != null" >
				CONTACTMOBILE,
			</if>
			<if test="relatedtype != null" >
				RELATEDTYPE,
			</if>
			<if test="itemprovincecode != null" >
				ITEMPROVINCECODE,
			</if>
			<if test="itemcitycode != null" >
				ITEMCITYCODE,
			</if>
			<if test="itemprovincecname != null" >
				ITEMPROVINCECNAME,
			</if>
			<if test="itemcitycname != null" >
				ITEMCITYCNAME,
			</if>
			<if test="moneylaunderingind != null" >
				MONEYLAUNDERINGIND,
			</if>
			<if test="starlevel != null" >
				STARLEVEL,
			</if>
			<if test="vipind != null" >
				VIPIND,
			</if>
			<if test="machinecode != null" >
				MACHINECODE,
			</if>
			<if test="idcollectind != null" >
				IDCOLLECTIND,
			</if>
			<if test="countycode != null" >
				COUNTYCODE,
			</if>
			<if test="email != null" >
				EMAIL,
			</if>
			<if test="industrymaincode != null" >
				INDUSTRYMAINCODE,
			</if>
			<if test="industrykindcode != null" >
				INDUSTRYKINDCODE,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="plancode != null" >
				#{plancode},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="subpolicyno != null" >
				#{subpolicyno},
			</if>
			<if test="serialNo != null" >
				#{serialNo},
			</if>
			<if test="insuredType != null" >
				#{insuredType},
			</if>
			<if test="insuredCode != null" >
				#{insuredCode},
			</if>
			<if test="insuredName != null" >
				#{insuredName},
			</if>
			<if test="insuredAddress != null" >
				#{insuredAddress},
			</if>
			<if test="insuredbusinesssource != null" >
				#{insuredbusinesssource},
			</if>
			<if test="insuredFlag != null" >
				#{insuredFlag},
			</if>
			<if test="insuredrole != null" >
				#{insuredrole},
			</if>
			<if test="identifyType != null" >
				#{identifyType},
			</if>
			<if test="identifyNumber != null" >
				#{identifyNumber},
			</if>
			<if test="insuredrelation != null" >
				#{insuredrelation},
			</if>
			<if test="relateSerialNo != null" >
				#{relateSerialNo},
			</if>
			<if test="insuredind != null" >
				#{insuredind},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="insuredsex != null" >
				#{insuredsex},
			</if>
			<if test="insuredbirthdate != null" >
				#{insuredbirthdate},
			</if>
			<if test="insuredpostcode != null" >
				#{insuredpostcode},
			</if>
			<if test="insuredofficephone != null" >
				#{insuredofficephone},
			</if>
			<if test="insuredmobilephone != null" >
				#{insuredmobilephone},
			</if>
			<if test="insuredhomephone != null" >
				#{insuredhomephone},
			</if>
			<if test="contactname != null" >
				#{contactname},
			</if>
			<if test="contactphone != null" >
				#{contactphone},
			</if>
			<if test="marriagestatus != null" >
				#{marriagestatus},
			</if>
			<if test="educationbackground != null" >
				#{educationbackground},
			</if>
			<if test="relationwithholder != null" >
				#{relationwithholder},
			</if>
			<if test="contactsex != null" >
				#{contactsex},
			</if>
			<if test="contacttype != null" >
				#{contacttype},
			</if>
			<if test="contactdepartment != null" >
				#{contactdepartment},
			</if>
			<if test="contactposition != null" >
				#{contactposition},
			</if>
			<if test="contactofficenumber != null" >
				#{contactofficenumber},
			</if>
			<if test="contactmobile != null" >
				#{contactmobile},
			</if>
			<if test="relatedtype != null" >
				#{relatedtype},
			</if>
			<if test="itemprovincecode != null" >
				#{itemprovincecode},
			</if>
			<if test="itemcitycode != null" >
				#{itemcitycode},
			</if>
			<if test="itemprovincecname != null" >
				#{itemprovincecname},
			</if>
			<if test="itemcitycname != null" >
				#{itemcitycname},
			</if>
			<if test="moneylaunderingind != null" >
				#{moneylaunderingind},
			</if>
			<if test="starlevel != null" >
				#{starlevel},
			</if>
			<if test="vipind != null" >
				#{vipind},
			</if>
			<if test="machinecode != null" >
				#{machinecode},
			</if>
			<if test="idcollectind != null" >
				#{idcollectind},
			</if>
			<if test="countycode != null" >
				#{countycode},
			</if>
			<if test="email != null" >
				#{email},
			</if>
			<if test="industrymaincode != null" >
				#{industrymaincode},
			</if>
			<if test="industrykindcode != null" >
				#{industrykindcode},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicyinsured.po.Gupolicyinsured">
		update GUPOLICYINSURED 
		<set>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="plancode != null" >
				PLANCODE=#{plancode},
			</if>
			<if test="riskCode != null" >
				RISKCODE=#{riskCode},
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO=#{subpolicyno},
			</if>
			<if test="serialNo != null" >
				SERIALNO=#{serialNo},
			</if>
			<if test="insuredType != null" >
				INSUREDTYPE=#{insuredType},
			</if>
			<if test="insuredCode != null" >
				INSUREDCODE=#{insuredCode},
			</if>
			<if test="insuredName != null" >
				INSUREDNAME=#{insuredName},
			</if>
			<if test="insuredAddress != null" >
				INSUREDADDRESS=#{insuredAddress},
			</if>
			<if test="insuredbusinesssource != null" >
				INSUREDBUSINESSSOURCE=#{insuredbusinesssource},
			</if>
			<if test="insuredFlag != null" >
				INSUREDFLAG=#{insuredFlag},
			</if>
			<if test="insuredrole != null" >
				INSUREDROLE=#{insuredrole},
			</if>
			<if test="identifyType != null" >
				IDENTIFYTYPE=#{identifyType},
			</if>
			<if test="identifyNumber != null" >
				IDENTIFYNUMBER=#{identifyNumber},
			</if>
			<if test="insuredrelation != null" >
				INSUREDRELATION=#{insuredrelation},
			</if>
			<if test="relateSerialNo != null" >
				RELATESERIALNO=#{relateSerialNo},
			</if>
			<if test="insuredind != null" >
				INSUREDIND=#{insuredind},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="insuredsex != null" >
				INSUREDSEX=#{insuredsex},
			</if>
			<if test="insuredbirthdate != null" >
				INSUREDBIRTHDATE=#{insuredbirthdate},
			</if>
			<if test="insuredpostcode != null" >
				INSUREDPOSTCODE=#{insuredpostcode},
			</if>
			<if test="insuredofficephone != null" >
				INSUREDOFFICEPHONE=#{insuredofficephone},
			</if>
			<if test="insuredmobilephone != null" >
				INSUREDMOBILEPHONE=#{insuredmobilephone},
			</if>
			<if test="insuredhomephone != null" >
				INSUREDHOMEPHONE=#{insuredhomephone},
			</if>
			<if test="contactname != null" >
				CONTACTNAME=#{contactname},
			</if>
			<if test="contactphone != null" >
				CONTACTPHONE=#{contactphone},
			</if>
			<if test="marriagestatus != null" >
				MARRIAGESTATUS=#{marriagestatus},
			</if>
			<if test="educationbackground != null" >
				EDUCATIONBACKGROUND=#{educationbackground},
			</if>
			<if test="relationwithholder != null" >
				RELATIONWITHHOLDER=#{relationwithholder},
			</if>
			<if test="contactsex != null" >
				CONTACTSEX=#{contactsex},
			</if>
			<if test="contacttype != null" >
				CONTACTTYPE=#{contacttype},
			</if>
			<if test="contactdepartment != null" >
				CONTACTDEPARTMENT=#{contactdepartment},
			</if>
			<if test="contactposition != null" >
				CONTACTPOSITION=#{contactposition},
			</if>
			<if test="contactofficenumber != null" >
				CONTACTOFFICENUMBER=#{contactofficenumber},
			</if>
			<if test="contactmobile != null" >
				CONTACTMOBILE=#{contactmobile},
			</if>
			<if test="relatedtype != null" >
				RELATEDTYPE=#{relatedtype},
			</if>
			<if test="itemprovincecode != null" >
				ITEMPROVINCECODE=#{itemprovincecode},
			</if>
			<if test="itemcitycode != null" >
				ITEMCITYCODE=#{itemcitycode},
			</if>
			<if test="itemprovincecname != null" >
				ITEMPROVINCECNAME=#{itemprovincecname},
			</if>
			<if test="itemcitycname != null" >
				ITEMCITYCNAME=#{itemcitycname},
			</if>
			<if test="moneylaunderingind != null" >
				MONEYLAUNDERINGIND=#{moneylaunderingind},
			</if>
			<if test="starlevel != null" >
				STARLEVEL=#{starlevel},
			</if>
			<if test="vipind != null" >
				VIPIND=#{vipind},
			</if>
			<if test="machinecode != null" >
				MACHINECODE=#{machinecode},
			</if>
			<if test="idcollectind != null" >
				IDCOLLECTIND=#{idcollectind},
			</if>
			<if test="countycode != null" >
				COUNTYCODE=#{countycode},
			</if>
			<if test="email != null" >
				EMAIL=#{email},
			</if>
			<if test="industrymaincode != null" >
				INDUSTRYMAINCODE=#{industrymaincode},
			</if>
			<if test="industrykindcode != null" >
				INDUSTRYKINDCODE=#{industrykindcode},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicyinsured.po.Gupolicyinsured">
		update GUPOLICYINSURED set
			POLICYNO=#{policyNo},
			PLANCODE=#{plancode},
			RISKCODE=#{riskCode},
			SUBPOLICYNO=#{subpolicyno},
			SERIALNO=#{serialNo},
			INSUREDTYPE=#{insuredType},
			INSUREDCODE=#{insuredCode},
			INSUREDNAME=#{insuredName},
			INSUREDADDRESS=#{insuredAddress},
			INSUREDBUSINESSSOURCE=#{insuredbusinesssource},
			INSUREDFLAG=#{insuredFlag},
			INSUREDROLE=#{insuredrole},
			IDENTIFYTYPE=#{identifyType},
			IDENTIFYNUMBER=#{identifyNumber},
			INSUREDRELATION=#{insuredrelation},
			RELATESERIALNO=#{relateSerialNo},
			INSUREDIND=#{insuredind},
			REMARK=#{remark},
			FLAG=#{flag},
			INSUREDSEX=#{insuredsex},
			INSUREDBIRTHDATE=#{insuredbirthdate},
			INSUREDPOSTCODE=#{insuredpostcode},
			INSUREDOFFICEPHONE=#{insuredofficephone},
			INSUREDMOBILEPHONE=#{insuredmobilephone},
			INSUREDHOMEPHONE=#{insuredhomephone},
			CONTACTNAME=#{contactname},
			CONTACTPHONE=#{contactphone},
			MARRIAGESTATUS=#{marriagestatus},
			EDUCATIONBACKGROUND=#{educationbackground},
			RELATIONWITHHOLDER=#{relationwithholder},
			CONTACTSEX=#{contactsex},
			CONTACTTYPE=#{contacttype},
			CONTACTDEPARTMENT=#{contactdepartment},
			CONTACTPOSITION=#{contactposition},
			CONTACTOFFICENUMBER=#{contactofficenumber},
			CONTACTMOBILE=#{contactmobile},
			RELATEDTYPE=#{relatedtype},
			ITEMPROVINCECODE=#{itemprovincecode},
			ITEMCITYCODE=#{itemcitycode},
			ITEMPROVINCECNAME=#{itemprovincecname},
			ITEMCITYCNAME=#{itemcitycname},
			MONEYLAUNDERINGIND=#{moneylaunderingind},
			STARLEVEL=#{starlevel},
			VIPIND=#{vipind},
			MACHINECODE=#{machinecode},
			IDCOLLECTIND=#{idcollectind},
			COUNTYCODE=#{countycode},
			EMAIL=#{email},
			INDUSTRYMAINCODE=#{industrymaincode},
			INDUSTRYKINDCODE=#{industrykindcode},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
		where ID = #{id}	</update>

	<update id="batchUpdate" parameterType="java.util.List">
		update GUPOLICYINSURED
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="POLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="PLANCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.plancode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="RISKCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.riskCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SUBPOLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.subpolicyno,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SERIALNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.serialNo,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="INSUREDTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredType,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredName,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDADDRESS = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredAddress,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDBUSINESSSOURCE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredbusinesssource,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredFlag,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDROLE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredrole,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="IDENTIFYTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.identifyType,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="IDENTIFYNUMBER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.identifyNumber,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDRELATION = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredrelation,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="RELATESERIALNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.relateSerialNo,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="INSUREDIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="REMARK = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="FLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.flag,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDSEX = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredsex,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDBIRTHDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredbirthdate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="INSUREDPOSTCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredpostcode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDOFFICEPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredofficephone,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDMOBILEPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredmobilephone,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSUREDHOMEPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredhomephone,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactname,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactphone,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="MARRIAGESTATUS = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.marriagestatus,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="EDUCATIONBACKGROUND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.educationbackground,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="RELATIONWITHHOLDER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.relationwithholder,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTSEX = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactsex,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contacttype,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTDEPARTMENT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactdepartment,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTPOSITION = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactposition,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTOFFICENUMBER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactofficenumber,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CONTACTMOBILE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactmobile,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="RELATEDTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.relatedtype,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMPROVINCECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemprovincecode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMCITYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemcitycode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMPROVINCECNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemprovincecname,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMCITYCNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemcitycname,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="MONEYLAUNDERINGIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.moneylaunderingind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="STARLEVEL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.starlevel,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="VIPIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.vipind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="MACHINECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.machinecode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="IDCOLLECTIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.idcollectind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="COUNTYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.countycode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="EMAIL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.email,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INDUSTRYMAINCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.industrymaincode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INDUSTRYKINDCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.industrykindcode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INPUTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="UPDATESYSDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
		</trim>
		where ID in
		<foreach close=")" collection="list" item="item" open="(" separator=", ">
			#{item.id,jdbcType=VARCHAR}
		</foreach>
	</update>
	<update id="batchUpdateSelective" parameterType="java.util.List">
		update GUPOLICYINSURED
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="POLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.policyNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="PLANCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.plancode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.plancode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="RISKCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.riskCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.riskCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SUBPOLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.subpolicyno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.subpolicyno,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SERIALNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.serialNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.serialNo,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredType != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredType,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredName != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredName,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDADDRESS = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredAddress != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredAddress,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDBUSINESSSOURCE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredbusinesssource != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredbusinesssource,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredFlag != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredFlag,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDROLE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredrole != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredrole,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="IDENTIFYTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.identifyType != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.identifyType,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="IDENTIFYNUMBER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.identifyNumber != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.identifyNumber,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDRELATION = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredrelation != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredrelation,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="RELATESERIALNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.relateSerialNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.relateSerialNo,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="REMARK = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.remark != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="FLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.flag != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.flag,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDSEX = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredsex != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredsex,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDBIRTHDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredbirthdate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredbirthdate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDPOSTCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredpostcode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredpostcode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDOFFICEPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredofficephone != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredofficephone,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDMOBILEPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredmobilephone != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredmobilephone,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSUREDHOMEPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insuredhomephone != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredhomephone,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactname != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactname,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTPHONE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactphone != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactphone,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="MARRIAGESTATUS = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.marriagestatus != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.marriagestatus,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="EDUCATIONBACKGROUND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.educationbackground != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.educationbackground,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="RELATIONWITHHOLDER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.relationwithholder != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.relationwithholder,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTSEX = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactsex != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactsex,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contacttype != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contacttype,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTDEPARTMENT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactdepartment != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactdepartment,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTPOSITION = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactposition != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactposition,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTOFFICENUMBER = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactofficenumber != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactofficenumber,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CONTACTMOBILE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.contactmobile != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.contactmobile,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="RELATEDTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.relatedtype != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.relatedtype,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMPROVINCECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemprovincecode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemprovincecode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMCITYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemcitycode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemcitycode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMPROVINCECNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemprovincecname != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemprovincecname,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMCITYCNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemcitycname != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemcitycname,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="MONEYLAUNDERINGIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.moneylaunderingind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.moneylaunderingind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="STARLEVEL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.starlevel != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.starlevel,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="VIPIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.vipind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.vipind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="MACHINECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.machinecode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.machinecode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="IDCOLLECTIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.idcollectind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.idcollectind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="COUNTYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.countycode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.countycode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="EMAIL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.email != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.email,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INDUSTRYMAINCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.industrymaincode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.industrymaincode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INDUSTRYKINDCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.industrykindcode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.industrykindcode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INPUTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.inputDate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="UPDATESYSDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.updatesysdate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
		</trim>
		where ID in
		<foreach close=")" collection="list" item="item" open="(" separator=", ">
			#{item.id,jdbcType=VARCHAR}
		</foreach>
	</update>
	<insert id="batchInsert" parameterType="java.util.List">
		INSERT  ALL
		<foreach collection="list" item="item" separator="">
			INTO GUPOLICYINSURED VALUES
			(#{item.id,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.plancode,jdbcType=VARCHAR},
			#{item.riskCode,jdbcType=VARCHAR}, #{item.subpolicyno,jdbcType=VARCHAR}, #{item.serialNo,jdbcType=DECIMAL},
			#{item.insuredType,jdbcType=VARCHAR}, #{item.insuredCode,jdbcType=VARCHAR}, #{item.insuredName,jdbcType=VARCHAR},
			#{item.insuredAddress,jdbcType=VARCHAR}, #{item.insuredbusinesssource,jdbcType=VARCHAR},
			#{item.insuredFlag,jdbcType=VARCHAR}, #{item.insuredrole,jdbcType=VARCHAR}, #{item.identifyType,jdbcType=VARCHAR},
			#{item.identifyNumber,jdbcType=VARCHAR}, #{item.insuredrelation,jdbcType=VARCHAR},
			#{item.relateSerialNo,jdbcType=DECIMAL}, #{item.insuredind,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
			#{item.flag,jdbcType=VARCHAR}, #{item.insuredsex,jdbcType=VARCHAR}, #{item.insuredbirthdate,jdbcType=TIMESTAMP},
			#{item.insuredpostcode,jdbcType=VARCHAR}, #{item.insuredofficephone,jdbcType=VARCHAR},
			#{item.insuredmobilephone,jdbcType=VARCHAR}, #{item.insuredhomephone,jdbcType=VARCHAR},
			#{item.contactname,jdbcType=VARCHAR}, #{item.contactphone,jdbcType=VARCHAR}, #{item.marriagestatus,jdbcType=VARCHAR},
			#{item.educationbackground,jdbcType=VARCHAR}, #{item.relationwithholder,jdbcType=VARCHAR},
			#{item.contactsex,jdbcType=VARCHAR}, #{item.contacttype,jdbcType=VARCHAR}, #{item.contactdepartment,jdbcType=VARCHAR},
			#{item.contactposition,jdbcType=VARCHAR}, #{item.contactofficenumber,jdbcType=VARCHAR},
			#{item.contactmobile,jdbcType=VARCHAR}, #{item.relatedtype,jdbcType=VARCHAR}, #{item.itemprovincecode,jdbcType=VARCHAR},
			#{item.itemcitycode,jdbcType=VARCHAR}, #{item.itemprovincecname,jdbcType=VARCHAR},
			#{item.itemcitycname,jdbcType=VARCHAR}, #{item.moneylaunderingind,jdbcType=VARCHAR},
			#{item.starlevel,jdbcType=VARCHAR}, #{item.vipind,jdbcType=VARCHAR}, #{item.machinecode,jdbcType=VARCHAR},
			#{item.idcollectind,jdbcType=VARCHAR}, #{item.countycode,jdbcType=VARCHAR}, #{item.email,jdbcType=VARCHAR},
			#{item.industrymaincode,jdbcType=VARCHAR}, #{item.industrykindcode,jdbcType=VARCHAR},
			#{item.inputDate,jdbcType=TIMESTAMP}, #{item.updatesysdate,jdbcType=TIMESTAMP})
		</foreach>
		select 1 from dual
	</insert>

	<delete id="deleteByPolicyNo" parameterType="string">
		delete from GUPOLICYINSURED where policyNo=#{policyNo}
	</delete>
</mapper>
