<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicyitemkind.dao.GupolicyitemkindDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicyitemkind.po.Gupolicyitemkind">
		<id column="ID" property="id" />
		<result column="POLICYNO" property="policyNo" />
		<result column="SUBPOLICYNO" property="subpolicyno" />
		<result column="PLANCODE" property="plancode" />
		<result column="RISKCODE" property="riskCode" />
		<result column="ITEMKINDNO" property="itemKindNo" />
		<result column="ITEMNO" property="itemNo" />
		<result column="ITEMCODE" property="itemCode" />
		<result column="ITEMDETAILNO" property="itemdetailno" />
		<result column="ITEMDETAILCODE" property="itemdetailcode" />
		<result column="ITEMDETAILLIST" property="itemdetaillist" />
		<result column="PLANID" property="planid" />
		<result column="KINDIND" property="kindind" />
		<result column="KINDCODE" property="kindCode" />
		<result column="KINDNAME" property="kindName" />
		<result column="MODECODE" property="modeCode" />
		<result column="MODENAME" property="modeName" />
		<result column="STARTDATE" property="startDate" />
		<result column="ENDDATE" property="endDate" />
		<result column="CALCULATEIND" property="calculateind" />
		<result column="CURRENCY" property="currency" />
		<result column="UNIT" property="unit" />
		<result column="SUMVALUE" property="sumValue" />
		<result column="SUMINSURED" property="suminsured" />
		<result column="RATEPERIOD" property="ratePeriod" />
		<result column="RATE" property="rate" />
		<result column="SHORTRATEFLAG" property="shortrateFlag" />
		<result column="SHORTRATE" property="shortRate" />
		<result column="SHORTRATEDENOMINATOR" property="shortratedenominator" />
		<result column="LOADING" property="loading" />
		<result column="GROSSPREMIUM" property="grosspremium" />
		<result column="NOTAXPREMIUM" property="notaxpremium" />
		<result column="TAXAMOUNT" property="taxamount" />
		<result column="NETPREMIUM" property="netPremium" />
		<result column="DEDUCTIBLERATE" property="deductibleRate" />
		<result column="DEDUCTIBLE" property="deductible" />
		<result column="YEARPREMIUM" property="yearpremium" />
		<result column="SURRENDERIND" property="surrenderind" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="LIABCODE" property="liabCode" />
		<result column="UNITPREMIUM" property="unitPremium" />
		<result column="DISCOUNT" property="discount" />
		<result column="QUANTITY" property="quantity" />
		<result column="UWCOUNT" property="uwcount" />
		<result column="UWPREMIUM" property="uwpremium" />
		<result column="ORIGINUWPREMIUM" property="originuwpremium" />
		<result column="ORIGINGROSSPREMIUM" property="origingrosspremium" />
		<result column="COMMISSION" property="commission" />
		<result column="AGENTRATE" property="agentrate" />
		<result column="PUBINSUREDIND" property="pubinsuredind" />
		<result column="SPECIALIND" property="specialind" />
		<result column="MUSTINSUREIND" property="mustinsureind" />
		<result column="OURAMOUNT" property="ouramount" />
		<result column="OURPREMIUM" property="ourpremium" />
		<result column="OURNOTTAXPREMIUM" property="ournottaxpremium" />
		<result column="OURTAXAMOUNT" property="ourtaxamount" />
		<result column="CURRENCYCNY" property="currencycny" />
		<result column="SUMINSUREDCNY" property="suminsuredcny" />
		<result column="UWPREMIUMCNY" property="uwpremiumcny" />
		<result column="NOTAXPREMIUMCNY" property="notaxpremiumcny" />
		<result column="TAXAMOUNTCNY" property="taxamountcny" />
		<result column="OURAMOUNTCNY" property="ouramountcny" />
		<result column="OURPREMIUMCNY" property="ourpremiumcny" />
		<result column="OURNOTTAXPREMIUMCNY" property="ournottaxpremiumcny" />
		<result column="OURTAXAMOUNTCNY" property="ourtaxamountcny" />
		<result column="CLAIMCOUNTLIMIT" property="claimcountlimit" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		POLICYNO,
		SUBPOLICYNO,
		PLANCODE,
		RISKCODE,
		ITEMKINDNO,
		ITEMNO,
		ITEMCODE,
		ITEMDETAILNO,
		ITEMDETAILCODE,
		ITEMDETAILLIST,
		PLANID,
		KINDIND,
		KINDCODE,
		KINDNAME,
		MODECODE,
		MODENAME,
		STARTDATE,
		ENDDATE,
		CALCULATEIND,
		CURRENCY,
		UNIT,
		SUMVALUE,
		SUMINSURED,
		RATEPERIOD,
		RATE,
		SHORTRATEFLAG,
		SHORTRATE,
		SHORTRATEDENOMINATOR,
		LOADING,
		GROSSPREMIUM,
		NOTAXPREMIUM,
		TAXAMOUNT,
		NETPREMIUM,
		DEDUCTIBLERATE,
		DEDUCTIBLE,
		YEARPREMIUM,
		SURRENDERIND,
		REMARK,
		FLAG,
		LIABCODE,
		UNITPREMIUM,
		DISCOUNT,
		QUANTITY,
		UWCOUNT,
		UWPREMIUM,
		ORIGINUWPREMIUM,
		ORIGINGROSSPREMIUM,
		COMMISSION,
		AGENTRATE,
		PUBINSUREDIND,
		SPECIALIND,
		MUSTINSUREIND,
		OURAMOUNT,
		OURPREMIUM,
		OURNOTTAXPREMIUM,
		OURTAXAMOUNT,
		CURRENCYCNY,
		SUMINSUREDCNY,
		UWPREMIUMCNY,
		NOTAXPREMIUMCNY,
		TAXAMOUNTCNY,
		OURAMOUNTCNY,
		OURPREMIUMCNY,
		OURNOTTAXPREMIUMCNY,
		OURTAXAMOUNTCNY,
		CLAIMCOUNTLIMIT,
		INPUTDATE,
		UPDATESYSDATE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="subpolicyno != null and subpolicyno != ''" >
			and SUBPOLICYNO = #{subpolicyno}
		</if>
		<if test="plancode != null and plancode != ''" >
			and PLANCODE = #{plancode}
		</if>
		<if test="riskCode != null and riskCode != ''" >
			and RISKCODE = #{riskCode}
		</if>
		<if test="itemKindNo != null and itemKindNo != ''" >
			and ITEMKINDNO = #{itemKindNo}
		</if>
		<if test="itemNo != null and itemNo != ''" >
			and ITEMNO = #{itemNo}
		</if>
		<if test="itemCode != null and itemCode != ''" >
			and ITEMCODE = #{itemCode}
		</if>
		<if test="itemdetailno != null and itemdetailno != ''" >
			and ITEMDETAILNO = #{itemdetailno}
		</if>
		<if test="itemdetailcode != null and itemdetailcode != ''" >
			and ITEMDETAILCODE = #{itemdetailcode}
		</if>
		<if test="itemdetaillist != null and itemdetaillist != ''" >
			and ITEMDETAILLIST = #{itemdetaillist}
		</if>
		<if test="planid != null and planid != ''" >
			and PLANID = #{planid}
		</if>
		<if test="kindind != null and kindind != ''" >
			and KINDIND = #{kindind}
		</if>
		<if test="kindCode != null and kindCode != ''" >
			and KINDCODE = #{kindCode}
		</if>
		<if test="kindName != null and kindName != ''" >
			and KINDNAME = #{kindName}
		</if>
		<if test="modeCode != null and modeCode != ''" >
			and MODECODE = #{modeCode}
		</if>
		<if test="modeName != null and modeName != ''" >
			and MODENAME = #{modeName}
		</if>
		<if test="startDate != null and startDate != ''" >
			and STARTDATE = #{startDate}
		</if>
		<if test="endDate != null and endDate != ''" >
			and ENDDATE = #{endDate}
		</if>
		<if test="calculateind != null and calculateind != ''" >
			and CALCULATEIND = #{calculateind}
		</if>
		<if test="currency != null and currency != ''" >
			and CURRENCY = #{currency}
		</if>
		<if test="unit != null and unit != ''" >
			and UNIT = #{unit}
		</if>
		<if test="sumValue != null and sumValue != ''" >
			and SUMVALUE = #{sumValue}
		</if>
		<if test="suminsured != null and suminsured != ''" >
			and SUMINSURED = #{suminsured}
		</if>
		<if test="ratePeriod != null and ratePeriod != ''" >
			and RATEPERIOD = #{ratePeriod}
		</if>
		<if test="rate != null and rate != ''" >
			and RATE = #{rate}
		</if>
		<if test="shortrateFlag != null and shortrateFlag != ''" >
			and SHORTRATEFLAG = #{shortrateFlag}
		</if>
		<if test="shortRate != null and shortRate != ''" >
			and SHORTRATE = #{shortRate}
		</if>
		<if test="shortratedenominator != null and shortratedenominator != ''" >
			and SHORTRATEDENOMINATOR = #{shortratedenominator}
		</if>
		<if test="loading != null and loading != ''" >
			and LOADING = #{loading}
		</if>
		<if test="grosspremium != null and grosspremium != ''" >
			and GROSSPREMIUM = #{grosspremium}
		</if>
		<if test="notaxpremium != null and notaxpremium != ''" >
			and NOTAXPREMIUM = #{notaxpremium}
		</if>
		<if test="taxamount != null and taxamount != ''" >
			and TAXAMOUNT = #{taxamount}
		</if>
		<if test="netPremium != null and netPremium != ''" >
			and NETPREMIUM = #{netPremium}
		</if>
		<if test="deductibleRate != null and deductibleRate != ''" >
			and DEDUCTIBLERATE = #{deductibleRate}
		</if>
		<if test="deductible != null and deductible != ''" >
			and DEDUCTIBLE = #{deductible}
		</if>
		<if test="yearpremium != null and yearpremium != ''" >
			and YEARPREMIUM = #{yearpremium}
		</if>
		<if test="surrenderind != null and surrenderind != ''" >
			and SURRENDERIND = #{surrenderind}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="liabCode != null and liabCode != ''" >
			and LIABCODE = #{liabCode}
		</if>
		<if test="unitPremium != null and unitPremium != ''" >
			and UNITPREMIUM = #{unitPremium}
		</if>
		<if test="discount != null and discount != ''" >
			and DISCOUNT = #{discount}
		</if>
		<if test="quantity != null and quantity != ''" >
			and QUANTITY = #{quantity}
		</if>
		<if test="uwcount != null and uwcount != ''" >
			and UWCOUNT = #{uwcount}
		</if>
		<if test="uwpremium != null and uwpremium != ''" >
			and UWPREMIUM = #{uwpremium}
		</if>
		<if test="originuwpremium != null and originuwpremium != ''" >
			and ORIGINUWPREMIUM = #{originuwpremium}
		</if>
		<if test="origingrosspremium != null and origingrosspremium != ''" >
			and ORIGINGROSSPREMIUM = #{origingrosspremium}
		</if>
		<if test="commission != null and commission != ''" >
			and COMMISSION = #{commission}
		</if>
		<if test="agentrate != null and agentrate != ''" >
			and AGENTRATE = #{agentrate}
		</if>
		<if test="pubinsuredind != null and pubinsuredind != ''" >
			and PUBINSUREDIND = #{pubinsuredind}
		</if>
		<if test="specialind != null and specialind != ''" >
			and SPECIALIND = #{specialind}
		</if>
		<if test="mustinsureind != null and mustinsureind != ''" >
			and MUSTINSUREIND = #{mustinsureind}
		</if>
		<if test="ouramount != null and ouramount != ''" >
			and OURAMOUNT = #{ouramount}
		</if>
		<if test="ourpremium != null and ourpremium != ''" >
			and OURPREMIUM = #{ourpremium}
		</if>
		<if test="ournottaxpremium != null and ournottaxpremium != ''" >
			and OURNOTTAXPREMIUM = #{ournottaxpremium}
		</if>
		<if test="ourtaxamount != null and ourtaxamount != ''" >
			and OURTAXAMOUNT = #{ourtaxamount}
		</if>
		<if test="currencycny != null and currencycny != ''" >
			and CURRENCYCNY = #{currencycny}
		</if>
		<if test="suminsuredcny != null and suminsuredcny != ''" >
			and SUMINSUREDCNY = #{suminsuredcny}
		</if>
		<if test="uwpremiumcny != null and uwpremiumcny != ''" >
			and UWPREMIUMCNY = #{uwpremiumcny}
		</if>
		<if test="notaxpremiumcny != null and notaxpremiumcny != ''" >
			and NOTAXPREMIUMCNY = #{notaxpremiumcny}
		</if>
		<if test="taxamountcny != null and taxamountcny != ''" >
			and TAXAMOUNTCNY = #{taxamountcny}
		</if>
		<if test="ouramountcny != null and ouramountcny != ''" >
			and OURAMOUNTCNY = #{ouramountcny}
		</if>
		<if test="ourpremiumcny != null and ourpremiumcny != ''" >
			and OURPREMIUMCNY = #{ourpremiumcny}
		</if>
		<if test="ournottaxpremiumcny != null and ournottaxpremiumcny != ''" >
			and OURNOTTAXPREMIUMCNY = #{ournottaxpremiumcny}
		</if>
		<if test="ourtaxamountcny != null and ourtaxamountcny != ''" >
			and OURTAXAMOUNTCNY = #{ourtaxamountcny}
		</if>
		<if test="claimcountlimit != null and claimcountlimit != ''" >
			and CLAIMCOUNTLIMIT = #{claimcountlimit}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYITEMKIND
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYITEMKIND
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYITEMKIND
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicyitemkind.po.Gupolicyitemkind">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYITEMKIND
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYITEMKIND
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicyitemkind.po.Gupolicyitemkind">
		insert into GUPOLICYITEMKIND (
			ID,
			POLICYNO,
			SUBPOLICYNO,
			PLANCODE,
			RISKCODE,
			ITEMKINDNO,
			ITEMNO,
			ITEMCODE,
			ITEMDETAILNO,
			ITEMDETAILCODE,
			ITEMDETAILLIST,
			PLANID,
			KINDIND,
			KINDCODE,
			KINDNAME,
			MODECODE,
			MODENAME,
			STARTDATE,
			ENDDATE,
			CALCULATEIND,
			CURRENCY,
			UNIT,
			SUMVALUE,
			SUMINSURED,
			RATEPERIOD,
			RATE,
			SHORTRATEFLAG,
			SHORTRATE,
			SHORTRATEDENOMINATOR,
			LOADING,
			GROSSPREMIUM,
			NOTAXPREMIUM,
			TAXAMOUNT,
			NETPREMIUM,
			DEDUCTIBLERATE,
			DEDUCTIBLE,
			YEARPREMIUM,
			SURRENDERIND,
			REMARK,
			FLAG,
			LIABCODE,
			UNITPREMIUM,
			DISCOUNT,
			QUANTITY,
			UWCOUNT,
			UWPREMIUM,
			ORIGINUWPREMIUM,
			ORIGINGROSSPREMIUM,
			COMMISSION,
			AGENTRATE,
			PUBINSUREDIND,
			SPECIALIND,
			MUSTINSUREIND,
			OURAMOUNT,
			OURPREMIUM,
			OURNOTTAXPREMIUM,
			OURTAXAMOUNT,
			CURRENCYCNY,
			SUMINSUREDCNY,
			UWPREMIUMCNY,
			NOTAXPREMIUMCNY,
			TAXAMOUNTCNY,
			OURAMOUNTCNY,
			OURPREMIUMCNY,
			OURNOTTAXPREMIUMCNY,
			OURTAXAMOUNTCNY,
			CLAIMCOUNTLIMIT,
			INPUTDATE,
			UPDATESYSDATE
		) values (
			#{id},
			#{policyNo},
			#{subpolicyno},
			#{plancode},
			#{riskCode},
			#{itemKindNo},
			#{itemNo},
			#{itemCode},
			#{itemdetailno},
			#{itemdetailcode},
			#{itemdetaillist},
			#{planid},
			#{kindind},
			#{kindCode},
			#{kindName},
			#{modeCode},
			#{modeName},
			#{startDate},
			#{endDate},
			#{calculateind},
			#{currency},
			#{unit},
			#{sumValue},
			#{suminsured},
			#{ratePeriod},
			#{rate},
			#{shortrateFlag},
			#{shortRate},
			#{shortratedenominator},
			#{loading},
			#{grosspremium},
			#{notaxpremium},
			#{taxamount},
			#{netPremium},
			#{deductibleRate},
			#{deductible},
			#{yearpremium},
			#{surrenderind},
			#{remark},
			#{flag},
			#{liabCode},
			#{unitPremium},
			#{discount},
			#{quantity},
			#{uwcount},
			#{uwpremium},
			#{originuwpremium},
			#{origingrosspremium},
			#{commission},
			#{agentrate},
			#{pubinsuredind},
			#{specialind},
			#{mustinsureind},
			#{ouramount},
			#{ourpremium},
			#{ournottaxpremium},
			#{ourtaxamount},
			#{currencycny},
			#{suminsuredcny},
			#{uwpremiumcny},
			#{notaxpremiumcny},
			#{taxamountcny},
			#{ouramountcny},
			#{ourpremiumcny},
			#{ournottaxpremiumcny},
			#{ourtaxamountcny},
			#{claimcountlimit},
			#{inputDate},
			#{updatesysdate}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicyitemkind.po.Gupolicyitemkind">
		insert into GUPOLICYITEMKIND
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO,
			</if>
			<if test="plancode != null" >
				PLANCODE,
			</if>
			<if test="riskCode != null" >
				RISKCODE,
			</if>
			<if test="itemKindNo != null" >
				ITEMKINDNO,
			</if>
			<if test="itemNo != null" >
				ITEMNO,
			</if>
			<if test="itemCode != null" >
				ITEMCODE,
			</if>
			<if test="itemdetailno != null" >
				ITEMDETAILNO,
			</if>
			<if test="itemdetailcode != null" >
				ITEMDETAILCODE,
			</if>
			<if test="itemdetaillist != null" >
				ITEMDETAILLIST,
			</if>
			<if test="planid != null" >
				PLANID,
			</if>
			<if test="kindind != null" >
				KINDIND,
			</if>
			<if test="kindCode != null" >
				KINDCODE,
			</if>
			<if test="kindName != null" >
				KINDNAME,
			</if>
			<if test="modeCode != null" >
				MODECODE,
			</if>
			<if test="modeName != null" >
				MODENAME,
			</if>
			<if test="startDate != null" >
				STARTDATE,
			</if>
			<if test="endDate != null" >
				ENDDATE,
			</if>
			<if test="calculateind != null" >
				CALCULATEIND,
			</if>
			<if test="currency != null" >
				CURRENCY,
			</if>
			<if test="unit != null" >
				UNIT,
			</if>
			<if test="sumValue != null" >
				SUMVALUE,
			</if>
			<if test="suminsured != null" >
				SUMINSURED,
			</if>
			<if test="ratePeriod != null" >
				RATEPERIOD,
			</if>
			<if test="rate != null" >
				RATE,
			</if>
			<if test="shortrateFlag != null" >
				SHORTRATEFLAG,
			</if>
			<if test="shortRate != null" >
				SHORTRATE,
			</if>
			<if test="shortratedenominator != null" >
				SHORTRATEDENOMINATOR,
			</if>
			<if test="loading != null" >
				LOADING,
			</if>
			<if test="grosspremium != null" >
				GROSSPREMIUM,
			</if>
			<if test="notaxpremium != null" >
				NOTAXPREMIUM,
			</if>
			<if test="taxamount != null" >
				TAXAMOUNT,
			</if>
			<if test="netPremium != null" >
				NETPREMIUM,
			</if>
			<if test="deductibleRate != null" >
				DEDUCTIBLERATE,
			</if>
			<if test="deductible != null" >
				DEDUCTIBLE,
			</if>
			<if test="yearpremium != null" >
				YEARPREMIUM,
			</if>
			<if test="surrenderind != null" >
				SURRENDERIND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="liabCode != null" >
				LIABCODE,
			</if>
			<if test="unitPremium != null" >
				UNITPREMIUM,
			</if>
			<if test="discount != null" >
				DISCOUNT,
			</if>
			<if test="quantity != null" >
				QUANTITY,
			</if>
			<if test="uwcount != null" >
				UWCOUNT,
			</if>
			<if test="uwpremium != null" >
				UWPREMIUM,
			</if>
			<if test="originuwpremium != null" >
				ORIGINUWPREMIUM,
			</if>
			<if test="origingrosspremium != null" >
				ORIGINGROSSPREMIUM,
			</if>
			<if test="commission != null" >
				COMMISSION,
			</if>
			<if test="agentrate != null" >
				AGENTRATE,
			</if>
			<if test="pubinsuredind != null" >
				PUBINSUREDIND,
			</if>
			<if test="specialind != null" >
				SPECIALIND,
			</if>
			<if test="mustinsureind != null" >
				MUSTINSUREIND,
			</if>
			<if test="ouramount != null" >
				OURAMOUNT,
			</if>
			<if test="ourpremium != null" >
				OURPREMIUM,
			</if>
			<if test="ournottaxpremium != null" >
				OURNOTTAXPREMIUM,
			</if>
			<if test="ourtaxamount != null" >
				OURTAXAMOUNT,
			</if>
			<if test="currencycny != null" >
				CURRENCYCNY,
			</if>
			<if test="suminsuredcny != null" >
				SUMINSUREDCNY,
			</if>
			<if test="uwpremiumcny != null" >
				UWPREMIUMCNY,
			</if>
			<if test="notaxpremiumcny != null" >
				NOTAXPREMIUMCNY,
			</if>
			<if test="taxamountcny != null" >
				TAXAMOUNTCNY,
			</if>
			<if test="ouramountcny != null" >
				OURAMOUNTCNY,
			</if>
			<if test="ourpremiumcny != null" >
				OURPREMIUMCNY,
			</if>
			<if test="ournottaxpremiumcny != null" >
				OURNOTTAXPREMIUMCNY,
			</if>
			<if test="ourtaxamountcny != null" >
				OURTAXAMOUNTCNY,
			</if>
			<if test="claimcountlimit != null" >
				CLAIMCOUNTLIMIT,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="subpolicyno != null" >
				#{subpolicyno},
			</if>
			<if test="plancode != null" >
				#{plancode},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="itemKindNo != null" >
				#{itemKindNo},
			</if>
			<if test="itemNo != null" >
				#{itemNo},
			</if>
			<if test="itemCode != null" >
				#{itemCode},
			</if>
			<if test="itemdetailno != null" >
				#{itemdetailno},
			</if>
			<if test="itemdetailcode != null" >
				#{itemdetailcode},
			</if>
			<if test="itemdetaillist != null" >
				#{itemdetaillist},
			</if>
			<if test="planid != null" >
				#{planid},
			</if>
			<if test="kindind != null" >
				#{kindind},
			</if>
			<if test="kindCode != null" >
				#{kindCode},
			</if>
			<if test="kindName != null" >
				#{kindName},
			</if>
			<if test="modeCode != null" >
				#{modeCode},
			</if>
			<if test="modeName != null" >
				#{modeName},
			</if>
			<if test="startDate != null" >
				#{startDate},
			</if>
			<if test="endDate != null" >
				#{endDate},
			</if>
			<if test="calculateind != null" >
				#{calculateind},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
			<if test="unit != null" >
				#{unit},
			</if>
			<if test="sumValue != null" >
				#{sumValue},
			</if>
			<if test="suminsured != null" >
				#{suminsured},
			</if>
			<if test="ratePeriod != null" >
				#{ratePeriod},
			</if>
			<if test="rate != null" >
				#{rate},
			</if>
			<if test="shortrateFlag != null" >
				#{shortrateFlag},
			</if>
			<if test="shortRate != null" >
				#{shortRate},
			</if>
			<if test="shortratedenominator != null" >
				#{shortratedenominator},
			</if>
			<if test="loading != null" >
				#{loading},
			</if>
			<if test="grosspremium != null" >
				#{grosspremium},
			</if>
			<if test="notaxpremium != null" >
				#{notaxpremium},
			</if>
			<if test="taxamount != null" >
				#{taxamount},
			</if>
			<if test="netPremium != null" >
				#{netPremium},
			</if>
			<if test="deductibleRate != null" >
				#{deductibleRate},
			</if>
			<if test="deductible != null" >
				#{deductible},
			</if>
			<if test="yearpremium != null" >
				#{yearpremium},
			</if>
			<if test="surrenderind != null" >
				#{surrenderind},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="liabCode != null" >
				#{liabCode},
			</if>
			<if test="unitPremium != null" >
				#{unitPremium},
			</if>
			<if test="discount != null" >
				#{discount},
			</if>
			<if test="quantity != null" >
				#{quantity},
			</if>
			<if test="uwcount != null" >
				#{uwcount},
			</if>
			<if test="uwpremium != null" >
				#{uwpremium},
			</if>
			<if test="originuwpremium != null" >
				#{originuwpremium},
			</if>
			<if test="origingrosspremium != null" >
				#{origingrosspremium},
			</if>
			<if test="commission != null" >
				#{commission},
			</if>
			<if test="agentrate != null" >
				#{agentrate},
			</if>
			<if test="pubinsuredind != null" >
				#{pubinsuredind},
			</if>
			<if test="specialind != null" >
				#{specialind},
			</if>
			<if test="mustinsureind != null" >
				#{mustinsureind},
			</if>
			<if test="ouramount != null" >
				#{ouramount},
			</if>
			<if test="ourpremium != null" >
				#{ourpremium},
			</if>
			<if test="ournottaxpremium != null" >
				#{ournottaxpremium},
			</if>
			<if test="ourtaxamount != null" >
				#{ourtaxamount},
			</if>
			<if test="currencycny != null" >
				#{currencycny},
			</if>
			<if test="suminsuredcny != null" >
				#{suminsuredcny},
			</if>
			<if test="uwpremiumcny != null" >
				#{uwpremiumcny},
			</if>
			<if test="notaxpremiumcny != null" >
				#{notaxpremiumcny},
			</if>
			<if test="taxamountcny != null" >
				#{taxamountcny},
			</if>
			<if test="ouramountcny != null" >
				#{ouramountcny},
			</if>
			<if test="ourpremiumcny != null" >
				#{ourpremiumcny},
			</if>
			<if test="ournottaxpremiumcny != null" >
				#{ournottaxpremiumcny},
			</if>
			<if test="ourtaxamountcny != null" >
				#{ourtaxamountcny},
			</if>
			<if test="claimcountlimit != null" >
				#{claimcountlimit},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicyitemkind.po.Gupolicyitemkind">
		update GUPOLICYITEMKIND 
		<set>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="subpolicyno != null" >
				SUBPOLICYNO=#{subpolicyno},
			</if>
			<if test="plancode != null" >
				PLANCODE=#{plancode},
			</if>
			<if test="riskCode != null" >
				RISKCODE=#{riskCode},
			</if>
			<if test="itemKindNo != null" >
				ITEMKINDNO=#{itemKindNo},
			</if>
			<if test="itemNo != null" >
				ITEMNO=#{itemNo},
			</if>
			<if test="itemCode != null" >
				ITEMCODE=#{itemCode},
			</if>
			<if test="itemdetailno != null" >
				ITEMDETAILNO=#{itemdetailno},
			</if>
			<if test="itemdetailcode != null" >
				ITEMDETAILCODE=#{itemdetailcode},
			</if>
			<if test="itemdetaillist != null" >
				ITEMDETAILLIST=#{itemdetaillist},
			</if>
			<if test="planid != null" >
				PLANID=#{planid},
			</if>
			<if test="kindind != null" >
				KINDIND=#{kindind},
			</if>
			<if test="kindCode != null" >
				KINDCODE=#{kindCode},
			</if>
			<if test="kindName != null" >
				KINDNAME=#{kindName},
			</if>
			<if test="modeCode != null" >
				MODECODE=#{modeCode},
			</if>
			<if test="modeName != null" >
				MODENAME=#{modeName},
			</if>
			<if test="startDate != null" >
				STARTDATE=#{startDate},
			</if>
			<if test="endDate != null" >
				ENDDATE=#{endDate},
			</if>
			<if test="calculateind != null" >
				CALCULATEIND=#{calculateind},
			</if>
			<if test="currency != null" >
				CURRENCY=#{currency},
			</if>
			<if test="unit != null" >
				UNIT=#{unit},
			</if>
			<if test="sumValue != null" >
				SUMVALUE=#{sumValue},
			</if>
			<if test="suminsured != null" >
				SUMINSURED=#{suminsured},
			</if>
			<if test="ratePeriod != null" >
				RATEPERIOD=#{ratePeriod},
			</if>
			<if test="rate != null" >
				RATE=#{rate},
			</if>
			<if test="shortrateFlag != null" >
				SHORTRATEFLAG=#{shortrateFlag},
			</if>
			<if test="shortRate != null" >
				SHORTRATE=#{shortRate},
			</if>
			<if test="shortratedenominator != null" >
				SHORTRATEDENOMINATOR=#{shortratedenominator},
			</if>
			<if test="loading != null" >
				LOADING=#{loading},
			</if>
			<if test="grosspremium != null" >
				GROSSPREMIUM=#{grosspremium},
			</if>
			<if test="notaxpremium != null" >
				NOTAXPREMIUM=#{notaxpremium},
			</if>
			<if test="taxamount != null" >
				TAXAMOUNT=#{taxamount},
			</if>
			<if test="netPremium != null" >
				NETPREMIUM=#{netPremium},
			</if>
			<if test="deductibleRate != null" >
				DEDUCTIBLERATE=#{deductibleRate},
			</if>
			<if test="deductible != null" >
				DEDUCTIBLE=#{deductible},
			</if>
			<if test="yearpremium != null" >
				YEARPREMIUM=#{yearpremium},
			</if>
			<if test="surrenderind != null" >
				SURRENDERIND=#{surrenderind},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="liabCode != null" >
				LIABCODE=#{liabCode},
			</if>
			<if test="unitPremium != null" >
				UNITPREMIUM=#{unitPremium},
			</if>
			<if test="discount != null" >
				DISCOUNT=#{discount},
			</if>
			<if test="quantity != null" >
				QUANTITY=#{quantity},
			</if>
			<if test="uwcount != null" >
				UWCOUNT=#{uwcount},
			</if>
			<if test="uwpremium != null" >
				UWPREMIUM=#{uwpremium},
			</if>
			<if test="originuwpremium != null" >
				ORIGINUWPREMIUM=#{originuwpremium},
			</if>
			<if test="origingrosspremium != null" >
				ORIGINGROSSPREMIUM=#{origingrosspremium},
			</if>
			<if test="commission != null" >
				COMMISSION=#{commission},
			</if>
			<if test="agentrate != null" >
				AGENTRATE=#{agentrate},
			</if>
			<if test="pubinsuredind != null" >
				PUBINSUREDIND=#{pubinsuredind},
			</if>
			<if test="specialind != null" >
				SPECIALIND=#{specialind},
			</if>
			<if test="mustinsureind != null" >
				MUSTINSUREIND=#{mustinsureind},
			</if>
			<if test="ouramount != null" >
				OURAMOUNT=#{ouramount},
			</if>
			<if test="ourpremium != null" >
				OURPREMIUM=#{ourpremium},
			</if>
			<if test="ournottaxpremium != null" >
				OURNOTTAXPREMIUM=#{ournottaxpremium},
			</if>
			<if test="ourtaxamount != null" >
				OURTAXAMOUNT=#{ourtaxamount},
			</if>
			<if test="currencycny != null" >
				CURRENCYCNY=#{currencycny},
			</if>
			<if test="suminsuredcny != null" >
				SUMINSUREDCNY=#{suminsuredcny},
			</if>
			<if test="uwpremiumcny != null" >
				UWPREMIUMCNY=#{uwpremiumcny},
			</if>
			<if test="notaxpremiumcny != null" >
				NOTAXPREMIUMCNY=#{notaxpremiumcny},
			</if>
			<if test="taxamountcny != null" >
				TAXAMOUNTCNY=#{taxamountcny},
			</if>
			<if test="ouramountcny != null" >
				OURAMOUNTCNY=#{ouramountcny},
			</if>
			<if test="ourpremiumcny != null" >
				OURPREMIUMCNY=#{ourpremiumcny},
			</if>
			<if test="ournottaxpremiumcny != null" >
				OURNOTTAXPREMIUMCNY=#{ournottaxpremiumcny},
			</if>
			<if test="ourtaxamountcny != null" >
				OURTAXAMOUNTCNY=#{ourtaxamountcny},
			</if>
			<if test="claimcountlimit != null" >
				CLAIMCOUNTLIMIT=#{claimcountlimit},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicyitemkind.po.Gupolicyitemkind">
		update GUPOLICYITEMKIND set
			POLICYNO=#{policyNo},
			SUBPOLICYNO=#{subpolicyno},
			PLANCODE=#{plancode},
			RISKCODE=#{riskCode},
			ITEMKINDNO=#{itemKindNo},
			ITEMNO=#{itemNo},
			ITEMCODE=#{itemCode},
			ITEMDETAILNO=#{itemdetailno},
			ITEMDETAILCODE=#{itemdetailcode},
			ITEMDETAILLIST=#{itemdetaillist},
			PLANID=#{planid},
			KINDIND=#{kindind},
			KINDCODE=#{kindCode},
			KINDNAME=#{kindName},
			MODECODE=#{modeCode},
			MODENAME=#{modeName},
			STARTDATE=#{startDate},
			ENDDATE=#{endDate},
			CALCULATEIND=#{calculateind},
			CURRENCY=#{currency},
			UNIT=#{unit},
			SUMVALUE=#{sumValue},
			SUMINSURED=#{suminsured},
			RATEPERIOD=#{ratePeriod},
			RATE=#{rate},
			SHORTRATEFLAG=#{shortrateFlag},
			SHORTRATE=#{shortRate},
			SHORTRATEDENOMINATOR=#{shortratedenominator},
			LOADING=#{loading},
			GROSSPREMIUM=#{grosspremium},
			NOTAXPREMIUM=#{notaxpremium},
			TAXAMOUNT=#{taxamount},
			NETPREMIUM=#{netPremium},
			DEDUCTIBLERATE=#{deductibleRate},
			DEDUCTIBLE=#{deductible},
			YEARPREMIUM=#{yearpremium},
			SURRENDERIND=#{surrenderind},
			REMARK=#{remark},
			FLAG=#{flag},
			LIABCODE=#{liabCode},
			UNITPREMIUM=#{unitPremium},
			DISCOUNT=#{discount},
			QUANTITY=#{quantity},
			UWCOUNT=#{uwcount},
			UWPREMIUM=#{uwpremium},
			ORIGINUWPREMIUM=#{originuwpremium},
			ORIGINGROSSPREMIUM=#{origingrosspremium},
			COMMISSION=#{commission},
			AGENTRATE=#{agentrate},
			PUBINSUREDIND=#{pubinsuredind},
			SPECIALIND=#{specialind},
			MUSTINSUREIND=#{mustinsureind},
			OURAMOUNT=#{ouramount},
			OURPREMIUM=#{ourpremium},
			OURNOTTAXPREMIUM=#{ournottaxpremium},
			OURTAXAMOUNT=#{ourtaxamount},
			CURRENCYCNY=#{currencycny},
			SUMINSUREDCNY=#{suminsuredcny},
			UWPREMIUMCNY=#{uwpremiumcny},
			NOTAXPREMIUMCNY=#{notaxpremiumcny},
			TAXAMOUNTCNY=#{taxamountcny},
			OURAMOUNTCNY=#{ouramountcny},
			OURPREMIUMCNY=#{ourpremiumcny},
			OURNOTTAXPREMIUMCNY=#{ournottaxpremiumcny},
			OURTAXAMOUNTCNY=#{ourtaxamountcny},
			CLAIMCOUNTLIMIT=#{claimcountlimit},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
		where ID = #{id}	</update>

	<update id="batchUpdate" parameterType="java.util.List">
		update GUPOLICYITEMKIND
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="POLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SUBPOLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.subpolicyno,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="PLANCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.plancode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="RISKCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.riskCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMKINDNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemKindNo,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="ITEMNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemNo,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="ITEMCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMDETAILNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemdetailno,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="ITEMDETAILCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemdetailcode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ITEMDETAILLIST = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemdetaillist,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="PLANID = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.planid,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="KINDIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.kindind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="KINDCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.kindCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="KINDNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.kindName,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="MODECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.modeCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="MODENAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.modeName,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="STARTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.startDate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="ENDDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.endDate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="CALCULATEIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.calculateind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CURRENCY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.currency,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="UNIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.unit,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SUMVALUE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.sumValue,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="SUMINSURED = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.suminsured,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="RATEPERIOD = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.ratePeriod,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="RATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.rate,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="SHORTRATEFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.shortrateFlag,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SHORTRATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.shortRate,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="SHORTRATEDENOMINATOR = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.shortratedenominator,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="LOADING = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.loading,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="GROSSPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.grosspremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="NOTAXPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.notaxpremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="TAXAMOUNT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.taxamount,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="NETPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.netPremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="DEDUCTIBLERATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.deductibleRate,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="DEDUCTIBLE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.deductible,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="YEARPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.yearpremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="SURRENDERIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.surrenderind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="REMARK = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="FLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.flag,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="LIABCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.liabCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="UNITPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.unitPremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="DISCOUNT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.discount,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="QUANTITY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.quantity,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="UWCOUNT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.uwcount,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="UWPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.uwpremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="ORIGINUWPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.originuwpremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="ORIGINGROSSPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.origingrosspremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="COMMISSION = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.commission,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="AGENTRATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.agentrate,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="PUBINSUREDIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.pubinsuredind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SPECIALIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.specialind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="MUSTINSUREIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.mustinsureind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="OURAMOUNT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.ouramount,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="OURPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.ourpremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="OURNOTTAXPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.ournottaxpremium,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="OURTAXAMOUNT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.ourtaxamount,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="CURRENCYCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.currencycny,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SUMINSUREDCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.suminsuredcny,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="UWPREMIUMCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.uwpremiumcny,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="NOTAXPREMIUMCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.notaxpremiumcny,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="TAXAMOUNTCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.taxamountcny,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="OURAMOUNTCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.ouramountcny,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="OURPREMIUMCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.ourpremiumcny,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="OURNOTTAXPREMIUMCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.ournottaxpremiumcny,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="OURTAXAMOUNTCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.ourtaxamountcny,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="CLAIMCOUNTLIMIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.claimcountlimit,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INPUTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="UPDATESYSDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
		</trim>
		where ID in
		<foreach close=")" collection="list" item="item" open="(" separator=", ">
			#{item.id,jdbcType=VARCHAR}
		</foreach>
	</update>
	<update id="batchUpdateSelective" parameterType="java.util.List">
		update GUPOLICYITEMKIND
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="POLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.policyNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SUBPOLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.subpolicyno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.subpolicyno,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="PLANCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.plancode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.plancode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="RISKCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.riskCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.riskCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMKINDNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemKindNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemKindNo,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemNo,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMDETAILNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemdetailno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemdetailno,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMDETAILCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemdetailcode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemdetailcode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ITEMDETAILLIST = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.itemdetaillist != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.itemdetaillist,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="PLANID = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.planid != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.planid,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="KINDIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.kindind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.kindind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="KINDCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.kindCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.kindCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="KINDNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.kindName != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.kindName,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="MODECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.modeCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.modeCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="MODENAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.modeName != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.modeName,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="STARTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.startDate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.startDate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="ENDDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.endDate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.endDate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="CALCULATEIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.calculateind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.calculateind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CURRENCY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.currency != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.currency,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="UNIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.unit != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.unit,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SUMVALUE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.sumValue != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.sumValue,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="SUMINSURED = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.suminsured != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.suminsured,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="RATEPERIOD = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.ratePeriod != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.ratePeriod,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="RATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.rate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.rate,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="SHORTRATEFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.shortrateFlag != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.shortrateFlag,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SHORTRATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.shortRate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.shortRate,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="SHORTRATEDENOMINATOR = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.shortratedenominator != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.shortratedenominator,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="LOADING = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.loading != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.loading,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="GROSSPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.grosspremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.grosspremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="NOTAXPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.notaxpremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.notaxpremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="TAXAMOUNT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.taxamount != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.taxamount,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="NETPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.netPremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.netPremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="DEDUCTIBLERATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.deductibleRate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.deductibleRate,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="DEDUCTIBLE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.deductible != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.deductible,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="YEARPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.yearpremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.yearpremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="SURRENDERIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.surrenderind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.surrenderind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="REMARK = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.remark != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="FLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.flag != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.flag,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="LIABCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.liabCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.liabCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="UNITPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.unitPremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.unitPremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="DISCOUNT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.discount != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.discount,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="QUANTITY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.quantity != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.quantity,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="UWCOUNT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.uwcount != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.uwcount,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="UWPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.uwpremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.uwpremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="ORIGINUWPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.originuwpremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.originuwpremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="ORIGINGROSSPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.origingrosspremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.origingrosspremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="COMMISSION = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.commission != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.commission,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="AGENTRATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.agentrate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.agentrate,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="PUBINSUREDIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.pubinsuredind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.pubinsuredind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SPECIALIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.specialind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.specialind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="MUSTINSUREIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.mustinsureind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.mustinsureind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="OURAMOUNT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.ouramount != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.ouramount,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="OURPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.ourpremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.ourpremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="OURNOTTAXPREMIUM = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.ournottaxpremium != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.ournottaxpremium,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="OURTAXAMOUNT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.ourtaxamount != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.ourtaxamount,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="CURRENCYCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.currencycny != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.currencycny,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SUMINSUREDCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.suminsuredcny != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.suminsuredcny,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="UWPREMIUMCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.uwpremiumcny != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.uwpremiumcny,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="NOTAXPREMIUMCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.notaxpremiumcny != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.notaxpremiumcny,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="TAXAMOUNTCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.taxamountcny != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.taxamountcny,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="OURAMOUNTCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.ouramountcny != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.ouramountcny,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="OURPREMIUMCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.ourpremiumcny != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.ourpremiumcny,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="OURNOTTAXPREMIUMCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.ournottaxpremiumcny != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.ournottaxpremiumcny,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="OURTAXAMOUNTCNY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.ourtaxamountcny != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.ourtaxamountcny,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="CLAIMCOUNTLIMIT = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.claimcountlimit != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.claimcountlimit,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INPUTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.inputDate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="UPDATESYSDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.updatesysdate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
		</trim>
		where ID in
		<foreach close=")" collection="list" item="item" open="(" separator=", ">
			#{item.id,jdbcType=VARCHAR}
		</foreach>
	</update>
	<insert id="batchInsert" parameterType="java.util.List">
		INSERT  ALL
		<foreach collection="list" item="item" separator="">
			INTO GUPOLICYITEMKIND VALUES
			(#{item.id,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.subpolicyno,jdbcType=VARCHAR},
			#{item.plancode,jdbcType=VARCHAR}, #{item.riskCode,jdbcType=VARCHAR}, #{item.itemKindNo,jdbcType=DECIMAL},
			#{item.itemNo,jdbcType=DECIMAL}, #{item.itemCode,jdbcType=VARCHAR}, #{item.itemdetailno,jdbcType=DECIMAL},
			#{item.itemdetailcode,jdbcType=VARCHAR}, #{item.itemdetaillist,jdbcType=VARCHAR},
			#{item.planid,jdbcType=VARCHAR}, #{item.kindind,jdbcType=VARCHAR}, #{item.kindCode,jdbcType=VARCHAR},
			#{item.kindName,jdbcType=VARCHAR}, #{item.modeCode,jdbcType=VARCHAR}, #{item.modeName,jdbcType=VARCHAR},
			#{item.startDate,jdbcType=TIMESTAMP}, #{item.endDate,jdbcType=TIMESTAMP}, #{item.calculateind,jdbcType=VARCHAR},
			#{item.currency,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR}, #{item.sumValue,jdbcType=DECIMAL},
			#{item.suminsured,jdbcType=DECIMAL}, #{item.ratePeriod,jdbcType=DECIMAL}, #{item.rate,jdbcType=DECIMAL},
			#{item.shortrateFlag,jdbcType=VARCHAR}, #{item.shortRate,jdbcType=DECIMAL}, #{item.shortratedenominator,jdbcType=DECIMAL},
			#{item.loading,jdbcType=DECIMAL}, #{item.grosspremium,jdbcType=DECIMAL}, #{item.notaxpremium,jdbcType=DECIMAL},
			#{item.taxamount,jdbcType=DECIMAL}, #{item.netPremium,jdbcType=DECIMAL}, #{item.deductibleRate,jdbcType=DECIMAL},
			#{item.deductible,jdbcType=DECIMAL}, #{item.yearpremium,jdbcType=DECIMAL}, #{item.surrenderind,jdbcType=VARCHAR},
			#{item.remark,jdbcType=VARCHAR}, #{item.flag,jdbcType=VARCHAR}, #{item.liabCode,jdbcType=VARCHAR},
			#{item.unitPremium,jdbcType=DECIMAL}, #{item.discount,jdbcType=DECIMAL}, #{item.quantity,jdbcType=DECIMAL},
			#{item.uwcount,jdbcType=DECIMAL}, #{item.uwpremium,jdbcType=DECIMAL}, #{item.originuwpremium,jdbcType=DECIMAL},
			#{item.origingrosspremium,jdbcType=DECIMAL}, #{item.commission,jdbcType=DECIMAL},
			#{item.agentrate,jdbcType=DECIMAL}, #{item.pubinsuredind,jdbcType=VARCHAR}, #{item.specialind,jdbcType=VARCHAR},
			#{item.mustinsureind,jdbcType=VARCHAR}, #{item.ouramount,jdbcType=DECIMAL}, #{item.ourpremium,jdbcType=DECIMAL},
			#{item.ournottaxpremium,jdbcType=DECIMAL}, #{item.ourtaxamount,jdbcType=DECIMAL},
			#{item.currencycny,jdbcType=VARCHAR}, #{item.suminsuredcny,jdbcType=DECIMAL}, #{item.uwpremiumcny,jdbcType=DECIMAL},
			#{item.notaxpremiumcny,jdbcType=DECIMAL}, #{item.taxamountcny,jdbcType=DECIMAL},
			#{item.ouramountcny,jdbcType=DECIMAL}, #{item.ourpremiumcny,jdbcType=DECIMAL},
			#{item.ournottaxpremiumcny,jdbcType=DECIMAL}, #{item.ourtaxamountcny,jdbcType=DECIMAL},
			#{item.claimcountlimit,jdbcType=VARCHAR}, #{item.inputDate,jdbcType=TIMESTAMP},
			#{item.updatesysdate,jdbcType=TIMESTAMP})
		</foreach>
		select 1 from dual
	</insert>
</mapper>
