<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicylog.dao.GupolicylogDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicylog.po.Gupolicylog">
		<id column="ID" property="id" />
		<result column="REQUESTCONTENT" property="requestcontent" />
		<result column="RESPONSECONTENT" property="responsecontent" />
		<result column="RESPONSESTATUS" property="responsestatus" />
		<result column="REQUESETDATE" property="requesetdate" />
		<result column="OPERATORCODE" property="operatorcode" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		REQUESTCONTENT,
		RESPONSECONTENT,
		RESPONSESTATUS,
		REQUESETDATE,
		OPERATORCODE,
		REMARK,
		FLAG
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="requestcontent != null and requestcontent != ''" >
			and REQUESTCONTENT = #{requestcontent}
		</if>
		<if test="responsecontent != null and responsecontent != ''" >
			and RESPONSECONTENT = #{responsecontent}
		</if>
		<if test="responsestatus != null and responsestatus != ''" >
			and RESPONSESTATUS = #{responsestatus}
		</if>
		<if test="requesetdate != null and requesetdate != ''" >
			and REQUESETDATE = #{requesetdate}
		</if>
		<if test="operatorcode != null and operatorcode != ''" >
			and OPERATORCODE = #{operatorcode}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYLOG
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYLOG
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYLOG
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicylog.po.Gupolicylog">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYLOG
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYLOG
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicylog.po.Gupolicylog">
		insert into GUPOLICYLOG (
			ID,
			REQUESTCONTENT,
			RESPONSECONTENT,
			RESPONSESTATUS,
			REQUESETDATE,
			OPERATORCODE,
			REMARK,
			FLAG
		) values (
			#{id},
			#{requestcontent},
			#{responsecontent},
			#{responsestatus},
			#{requesetdate},
			#{operatorcode},
			#{remark},
			#{flag}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicylog.po.Gupolicylog">
		insert into GUPOLICYLOG
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="requestcontent != null" >
				REQUESTCONTENT,
			</if>
			<if test="responsecontent != null" >
				RESPONSECONTENT,
			</if>
			<if test="responsestatus != null" >
				RESPONSESTATUS,
			</if>
			<if test="requesetdate != null" >
				REQUESETDATE,
			</if>
			<if test="operatorcode != null" >
				OPERATORCODE,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="requestcontent != null" >
				#{requestcontent},
			</if>
			<if test="responsecontent != null" >
				#{responsecontent},
			</if>
			<if test="responsestatus != null" >
				#{responsestatus},
			</if>
			<if test="requesetdate != null" >
				#{requesetdate},
			</if>
			<if test="operatorcode != null" >
				#{operatorcode},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicylog.po.Gupolicylog">
		update GUPOLICYLOG 
		<set>
			<if test="requestcontent != null" >
				REQUESTCONTENT=#{requestcontent},
			</if>
			<if test="responsecontent != null" >
				RESPONSECONTENT=#{responsecontent},
			</if>
			<if test="responsestatus != null" >
				RESPONSESTATUS=#{responsestatus},
			</if>
			<if test="requesetdate != null" >
				REQUESETDATE=#{requesetdate},
			</if>
			<if test="operatorcode != null" >
				OPERATORCODE=#{operatorcode},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicylog.po.Gupolicylog">
		update GUPOLICYLOG set
			REQUESTCONTENT=#{requestcontent},
			RESPONSECONTENT=#{responsecontent},
			RESPONSESTATUS=#{responsestatus},
			REQUESETDATE=#{requesetdate},
			OPERATORCODE=#{operatorcode},
			REMARK=#{remark},
			FLAG=#{flag},
		where ID = #{id}	</update>
</mapper>
