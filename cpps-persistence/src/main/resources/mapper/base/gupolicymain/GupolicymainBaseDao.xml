<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicymain.dao.GupolicymainDao">
    <!-- 默认开启二级缓存,使用两级缓存来处理 -->
    <cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="ins.channel.gupolicymain.po.Gupolicymain">
        <id column="ID" property="id"/>
        <result column="PROPOSALNO" property="proposalNo"/>
        <result column="POLICYNO" property="policyNo"/>
        <result column="LANGUAGE" property="language"/>
        <result column="PRODUCTCODE" property="productcode"/>
        <result column="GROUPIND" property="groupind"/>
        <result column="INSURANCECOMPANYCODE" property="insurancecompanycode"/>
        <result column="SURVEYIND" property="surveyind"/>
        <result column="FLOWID" property="flowid"/>
        <result column="COMPANYCODE" property="companycode"/>
        <result column="COMPANYNAME" property="companyname"/>
        <result column="PROJECTMANAGERCODE" property="projectmanagercode"/>
        <result column="PROJECTMANAGERNAME" property="projectmanagername"/>
        <result column="STARTDATE" property="startDate"/>
        <result column="ENDDATE" property="endDate"/>
        <result column="APPLICODE" property="appliCode"/>
        <result column="APPLINAME" property="appliName"/>
        <result column="INSUREDCODE" property="insuredCode"/>
        <result column="INSUREDNAME" property="insuredName"/>
        <result column="CURRENCY" property="currency"/>
        <result column="SUMINSURED" property="suminsured"/>
        <result column="SUMGROSSPREMIUM" property="sumgrosspremium"/>
        <result column="SUMNETPREMIUM" property="sumnetpremium"/>
        <result column="YEARPREMIUM" property="yearpremium"/>
        <result column="SUMUWPREMIUM" property="sumuwpremium"/>
        <result column="NOTAXPREMIUM" property="notaxpremium"/>
        <result column="TAXAMOUNT" property="taxamount"/>
        <result column="CURRENCYCNY" property="currencycny"/>
        <result column="SUMINSUREDCNY" property="suminsuredcny"/>
        <result column="SUMGROSSPREMIUMCNY" property="sumgrosspremiumcny"/>
        <result column="SUMUWPREMIUMCNY" property="sumuwpremiumcny"/>
        <result column="NOTAXPREMIUMCNY" property="notaxpremiumcny"/>
        <result column="TAXAMOUNTCNY" property="taxamountcny"/>
        <result column="UWYEAR" property="uwYear"/>
        <result column="ACCEPTDATE" property="acceptdate"/>
        <result column="UNDERWRITEIND" property="underwriteind"/>
        <result column="UNDERWRITEENDDATE" property="underWriteEndDate"/>
        <result column="SURRENDERIND" property="surrenderind"/>
        <result column="CANCELIND" property="cancelind"/>
        <result column="ENDIND" property="endind"/>
        <result column="CODIND" property="codind"/>
        <result column="CALCULATETYPE" property="calculatetype"/>
        <result column="COINSIND" property="coinsind"/>
        <result column="AGENTRATE" property="agentrate"/>
        <result column="NOTAXAGENTRATE" property="notaxagentrate"/>
        <result column="COMMISSION" property="commission"/>
        <result column="FSH" property="fsh"/>
        <result column="XSF" property="xsf"/>
        <result column="XSFIND" property="xsfind"/>
        <result column="INSTALLMENTNO" property="installmentno"/>
        <result column="ENDORSETIMES" property="endorseTimes"/>
        <result column="RENEWEDTIME" property="renewedtime"/>
        <result column="REGISTTIMES" property="registTimes"/>
        <result column="CLAIMSTIMES" property="claimstimes"/>
        <result column="PRINTTIMES" property="printtimes"/>
        <result column="ISSENDSMS" property="issendsms"/>
        <result column="ISSENDEMAIL" property="issendemail"/>
        <result column="REMARK" property="remark"/>
        <result column="VALIDIND" property="validind"/>
        <result column="FLAG" property="flag"/>
        <result column="INPUTDATE" property="inputDate"/>
        <result column="UPDATESYSDATE" property="updatesysdate"/>
        <result column="SETTLEFEE" property="settlefee"/>
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
		ID,
		PROPOSALNO,
		POLICYNO,
		LANGUAGE,
		PRODUCTCODE,
		GROUPIND,
		INSURANCECOMPANYCODE,
		SURVEYIND,
		FLOWID,
		COMPANYCODE,
		COMPANYNAME,
		PROJECTMANAGERCODE,
		PROJECTMANAGERNAME,
		STARTDATE,
		ENDDATE,
		APPLICODE,
		APPLINAME,
		INSUREDCODE,
		INSUREDNAME,
		CURRENCY,
		SUMINSURED,
		SUMGROSSPREMIUM,
		SUMNETPREMIUM,
		YEARPREMIUM,
		SUMUWPREMIUM,
		NOTAXPREMIUM,
		TAXAMOUNT,
		CURRENCYCNY,
		SUMINSUREDCNY,
		SUMGROSSPREMIUMCNY,
		SUMUWPREMIUMCNY,
		NOTAXPREMIUMCNY,
		TAXAMOUNTCNY,
		UWYEAR,
		ACCEPTDATE,
		UNDERWRITEIND,
		UNDERWRITEENDDATE,
		SURRENDERIND,
		CANCELIND,
		ENDIND,
		CODIND,
		CALCULATETYPE,
		COINSIND,
		AGENTRATE,
		NOTAXAGENTRATE,
		COMMISSION,
		FSH,
		XSF,
		XSFIND,
		INSTALLMENTNO,
		ENDORSETIMES,
		RENEWEDTIME,
		REGISTTIMES,
		CLAIMSTIMES,
		PRINTTIMES,
		ISSENDSMS,
		ISSENDEMAIL,
		REMARK,
		VALIDIND,
		FLAG,
		INPUTDATE,
		UPDATESYSDATE,
		SETTLEFEE
	</sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="id != null and id != ''">
            and ID = #{id}
        </if>
        <if test="proposalNo != null and proposalNo != ''">
            and PROPOSALNO = #{proposalNo}
        </if>
        <if test="policyNo != null and policyNo != ''">
            and POLICYNO = #{policyNo}
        </if>
        <if test="language != null and language != ''">
            and LANGUAGE = #{language}
        </if>
        <if test="productcode != null and productcode != ''">
            and PRODUCTCODE = #{productcode}
        </if>
        <if test="groupind != null and groupind != ''">
            and GROUPIND = #{groupind}
        </if>
        <if test="insurancecompanycode != null and insurancecompanycode != ''">
            and INSURANCECOMPANYCODE = #{insurancecompanycode}
        </if>
        <if test="surveyind != null and surveyind != ''">
            and SURVEYIND = #{surveyind}
        </if>
        <if test="flowid != null and flowid != ''">
            and FLOWID = #{flowid}
        </if>
        <if test="companycode != null and companycode != ''">
            and COMPANYCODE = #{companycode}
        </if>
        <if test="companyname != null and companyname != ''">
            and COMPANYNAME = #{companyname}
        </if>
        <if test="projectmanagercode != null and projectmanagercode != ''">
            and PROJECTMANAGERCODE = #{projectmanagercode}
        </if>
        <if test="projectmanagername != null and projectmanagername != ''">
            and PROJECTMANAGERNAME = #{projectmanagername}
        </if>
        <if test="startDate != null and startDate != ''">
            and STARTDATE = #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and ENDDATE = #{endDate}
        </if>
        <if test="appliCode != null and appliCode != ''">
            and APPLICODE = #{appliCode}
        </if>
        <if test="appliName != null and appliName != ''">
            and APPLINAME = #{appliName}
        </if>
        <if test="insuredCode != null and insuredCode != ''">
            and INSUREDCODE = #{insuredCode}
        </if>
        <if test="insuredName != null and insuredName != ''">
            and INSUREDNAME = #{insuredName}
        </if>
        <if test="currency != null and currency != ''">
            and CURRENCY = #{currency}
        </if>
        <if test="suminsured != null and suminsured != ''">
            and SUMINSURED = #{suminsured}
        </if>
        <if test="sumgrosspremium != null and sumgrosspremium != ''">
            and SUMGROSSPREMIUM = #{sumgrosspremium}
        </if>
        <if test="sumnetpremium != null and sumnetpremium != ''">
            and SUMNETPREMIUM = #{sumnetpremium}
        </if>
        <if test="yearpremium != null and yearpremium != ''">
            and YEARPREMIUM = #{yearpremium}
        </if>
        <if test="sumuwpremium != null and sumuwpremium != ''">
            and SUMUWPREMIUM = #{sumuwpremium}
        </if>
        <if test="notaxpremium != null and notaxpremium != ''">
            and NOTAXPREMIUM = #{notaxpremium}
        </if>
        <if test="taxamount != null and taxamount != ''">
            and TAXAMOUNT = #{taxamount}
        </if>
        <if test="currencycny != null and currencycny != ''">
            and CURRENCYCNY = #{currencycny}
        </if>
        <if test="suminsuredcny != null and suminsuredcny != ''">
            and SUMINSUREDCNY = #{suminsuredcny}
        </if>
        <if test="sumgrosspremiumcny != null and sumgrosspremiumcny != ''">
            and SUMGROSSPREMIUMCNY = #{sumgrosspremiumcny}
        </if>
        <if test="sumuwpremiumcny != null and sumuwpremiumcny != ''">
            and SUMUWPREMIUMCNY = #{sumuwpremiumcny}
        </if>
        <if test="notaxpremiumcny != null and notaxpremiumcny != ''">
            and NOTAXPREMIUMCNY = #{notaxpremiumcny}
        </if>
        <if test="taxamountcny != null and taxamountcny != ''">
            and TAXAMOUNTCNY = #{taxamountcny}
        </if>
        <if test="uwYear != null and uwYear != ''">
            and UWYEAR = #{uwYear}
        </if>
        <if test="acceptdate != null and acceptdate != ''">
            and ACCEPTDATE = #{acceptdate}
        </if>
        <if test="underwriteind != null and underwriteind != ''">
            and UNDERWRITEIND = #{underwriteind}
        </if>
        <if test="underWriteEndDate != null and underWriteEndDate != ''">
            and UNDERWRITEENDDATE = #{underWriteEndDate}
        </if>
        <if test="surrenderind != null and surrenderind != ''">
            and SURRENDERIND = #{surrenderind}
        </if>
        <if test="cancelind != null and cancelind != ''">
            and CANCELIND = #{cancelind}
        </if>
        <if test="endind != null and endind != ''">
            and ENDIND = #{endind}
        </if>
        <if test="codind != null and codind != ''">
            and CODIND = #{codind}
        </if>
        <if test="calculatetype != null and calculatetype != ''">
            and CALCULATETYPE = #{calculatetype}
        </if>
        <if test="coinsind != null and coinsind != ''">
            and COINSIND = #{coinsind}
        </if>
        <if test="agentrate != null and agentrate != ''">
            and AGENTRATE = #{agentrate}
        </if>
        <if test="notaxagentrate != null and notaxagentrate != ''">
            and NOTAXAGENTRATE = #{notaxagentrate}
        </if>
        <if test="commission != null and commission != ''">
            and COMMISSION = #{commission}
        </if>
        <if test="fsh != null and fsh != ''">
            and FSH = #{fsh}
        </if>
        <if test="xsf != null and xsf != ''">
            and XSF = #{xsf}
        </if>
        <if test="xsfind != null and xsfind != ''">
            and XSFIND = #{xsfind}
        </if>
        <if test="installmentno != null and installmentno != ''">
            and INSTALLMENTNO = #{installmentno}
        </if>
        <if test="endorseTimes != null and endorseTimes != ''">
            and ENDORSETIMES = #{endorseTimes}
        </if>
        <if test="renewedtime != null and renewedtime != ''">
            and RENEWEDTIME = #{renewedtime}
        </if>
        <if test="registTimes != null and registTimes != ''">
            and REGISTTIMES = #{registTimes}
        </if>
        <if test="claimstimes != null and claimstimes != ''">
            and CLAIMSTIMES = #{claimstimes}
        </if>
        <if test="printtimes != null and printtimes != ''">
            and PRINTTIMES = #{printtimes}
        </if>
        <if test="issendsms != null and issendsms != ''">
            and ISSENDSMS = #{issendsms}
        </if>
        <if test="issendemail != null and issendemail != ''">
            and ISSENDEMAIL = #{issendemail}
        </if>
        <if test="remark != null and remark != ''">
            and REMARK = #{remark}
        </if>
        <if test="validind != null and validind != ''">
            and VALIDIND = #{validind}
        </if>
        <if test="flag != null and flag != ''">
            and FLAG = #{flag}
        </if>
        <if test="inputDate != null and inputDate != ''">
            and INPUTDATE = #{inputDate}
        </if>
        <if test="updatesysdate != null and updatesysdate != ''">
            and UPDATESYSDATE = #{updatesysdate}
        </if>
        <if test="settlefee != null and settlefee != ''">
            and SETTLEFEE = #{settlefee}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
        <include refid="Base_Column_List"/>
        from GUPOLICYMAIN
        <where>
            <include refid="Base_Select_By_Entity_Where"/>
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="Base_Column_List"/>
        from GUPOLICYMAIN
        where ID = #{param1}
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="Base_Column_List"/>
        from GUPOLICYMAIN
        where ID in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicymain.po.Gupolicymain">
        <include refid="Base_Select_By_Entity"/>
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYMAIN
		where ID = #{param1}
	</delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys" parameterType="map">
        delete from GUPOLICYMAIN
        where ID in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" parameterType="ins.channel.gupolicymain.po.Gupolicymain">
		insert into GUPOLICYMAIN (
			ID,
			PROPOSALNO,
			POLICYNO,
			LANGUAGE,
			PRODUCTCODE,
			GROUPIND,
			INSURANCECOMPANYCODE,
			SURVEYIND,
			FLOWID,
			COMPANYCODE,
			COMPANYNAME,
			PROJECTMANAGERCODE,
			PROJECTMANAGERNAME,
			STARTDATE,
			ENDDATE,
			APPLICODE,
			APPLINAME,
			INSUREDCODE,
			INSUREDNAME,
			CURRENCY,
			SUMINSURED,
			SUMGROSSPREMIUM,
			SUMNETPREMIUM,
			YEARPREMIUM,
			SUMUWPREMIUM,
			NOTAXPREMIUM,
			TAXAMOUNT,
			CURRENCYCNY,
			SUMINSUREDCNY,
			SUMGROSSPREMIUMCNY,
			SUMUWPREMIUMCNY,
			NOTAXPREMIUMCNY,
			TAXAMOUNTCNY,
			UWYEAR,
			ACCEPTDATE,
			UNDERWRITEIND,
			UNDERWRITEENDDATE,
			SURRENDERIND,
			CANCELIND,
			ENDIND,
			CODIND,
			CALCULATETYPE,
			COINSIND,
			AGENTRATE,
			NOTAXAGENTRATE,
			COMMISSION,
			FSH,
			XSF,
			XSFIND,
			INSTALLMENTNO,
			ENDORSETIMES,
			RENEWEDTIME,
			REGISTTIMES,
			CLAIMSTIMES,
			PRINTTIMES,
			ISSENDSMS,
			ISSENDEMAIL,
			REMARK,
			VALIDIND,
			FLAG,
			INPUTDATE,
			UPDATESYSDATE,
			SETTLEFEE
		) values (
			#{id},
			#{proposalNo},
			#{policyNo},
			#{language},
			#{productcode},
			#{groupind},
			#{insurancecompanycode},
			#{surveyind},
			#{flowid},
			#{companycode},
			#{companyname},
			#{projectmanagercode},
			#{projectmanagername},
			#{startDate},
			#{endDate},
			#{appliCode},
			#{appliName},
			#{insuredCode},
			#{insuredName},
			#{currency},
			#{suminsured},
			#{sumgrosspremium},
			#{sumnetpremium},
			#{yearpremium},
			#{sumuwpremium},
			#{notaxpremium},
			#{taxamount},
			#{currencycny},
			#{suminsuredcny},
			#{sumgrosspremiumcny},
			#{sumuwpremiumcny},
			#{notaxpremiumcny},
			#{taxamountcny},
			#{uwYear},
			#{acceptdate},
			#{underwriteind},
			#{underWriteEndDate},
			#{surrenderind},
			#{cancelind},
			#{endind},
			#{codind},
			#{calculatetype},
			#{coinsind},
			#{agentrate},
			#{notaxagentrate},
			#{commission},
			#{fsh},
			#{xsf},
			#{xsfind},
			#{installmentno},
			#{endorseTimes},
			#{renewedtime},
			#{registTimes},
			#{claimstimes},
			#{printtimes},
			#{issendsms},
			#{issendemail},
			#{remark},
			#{validind},
			#{flag},
			#{inputDate},
			#{updatesysdate},
			#{settlefee}
		)
	</insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" parameterType="ins.channel.gupolicymain.po.Gupolicymain">
        insert into GUPOLICYMAIN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="proposalNo != null">
                PROPOSALNO,
            </if>
            <if test="policyNo != null">
                POLICYNO,
            </if>
            <if test="language != null">
                LANGUAGE,
            </if>
            <if test="productcode != null">
                PRODUCTCODE,
            </if>
            <if test="groupind != null">
                GROUPIND,
            </if>
            <if test="insurancecompanycode != null">
                INSURANCECOMPANYCODE,
            </if>
            <if test="surveyind != null">
                SURVEYIND,
            </if>
            <if test="flowid != null">
                FLOWID,
            </if>
            <if test="companycode != null">
                COMPANYCODE,
            </if>
            <if test="companyname != null">
                COMPANYNAME,
            </if>
            <if test="projectmanagercode != null">
                PROJECTMANAGERCODE,
            </if>
            <if test="projectmanagername != null">
                PROJECTMANAGERNAME,
            </if>
            <if test="startDate != null">
                STARTDATE,
            </if>
            <if test="endDate != null">
                ENDDATE,
            </if>
            <if test="appliCode != null">
                APPLICODE,
            </if>
            <if test="appliName != null">
                APPLINAME,
            </if>
            <if test="insuredCode != null">
                INSUREDCODE,
            </if>
            <if test="insuredName != null">
                INSUREDNAME,
            </if>
            <if test="currency != null">
                CURRENCY,
            </if>
            <if test="suminsured != null">
                SUMINSURED,
            </if>
            <if test="sumgrosspremium != null">
                SUMGROSSPREMIUM,
            </if>
            <if test="sumnetpremium != null">
                SUMNETPREMIUM,
            </if>
            <if test="yearpremium != null">
                YEARPREMIUM,
            </if>
            <if test="sumuwpremium != null">
                SUMUWPREMIUM,
            </if>
            <if test="notaxpremium != null">
                NOTAXPREMIUM,
            </if>
            <if test="taxamount != null">
                TAXAMOUNT,
            </if>
            <if test="currencycny != null">
                CURRENCYCNY,
            </if>
            <if test="suminsuredcny != null">
                SUMINSUREDCNY,
            </if>
            <if test="sumgrosspremiumcny != null">
                SUMGROSSPREMIUMCNY,
            </if>
            <if test="sumuwpremiumcny != null">
                SUMUWPREMIUMCNY,
            </if>
            <if test="notaxpremiumcny != null">
                NOTAXPREMIUMCNY,
            </if>
            <if test="taxamountcny != null">
                TAXAMOUNTCNY,
            </if>
            <if test="uwYear != null">
                UWYEAR,
            </if>
            <if test="acceptdate != null">
                ACCEPTDATE,
            </if>
            <if test="underwriteind != null">
                UNDERWRITEIND,
            </if>
            <if test="underWriteEndDate != null">
                UNDERWRITEENDDATE,
            </if>
            <if test="surrenderind != null">
                SURRENDERIND,
            </if>
            <if test="cancelind != null">
                CANCELIND,
            </if>
            <if test="endind != null">
                ENDIND,
            </if>
            <if test="codind != null">
                CODIND,
            </if>
            <if test="calculatetype != null">
                CALCULATETYPE,
            </if>
            <if test="coinsind != null">
                COINSIND,
            </if>
            <if test="agentrate != null">
                AGENTRATE,
            </if>
            <if test="notaxagentrate != null">
                NOTAXAGENTRATE,
            </if>
            <if test="commission != null">
                COMMISSION,
            </if>
            <if test="fsh != null">
                FSH,
            </if>
            <if test="xsf != null">
                XSF,
            </if>
            <if test="xsfind != null">
                XSFIND,
            </if>
            <if test="installmentno != null">
                INSTALLMENTNO,
            </if>
            <if test="endorseTimes != null">
                ENDORSETIMES,
            </if>
            <if test="renewedtime != null">
                RENEWEDTIME,
            </if>
            <if test="registTimes != null">
                REGISTTIMES,
            </if>
            <if test="claimstimes != null">
                CLAIMSTIMES,
            </if>
            <if test="printtimes != null">
                PRINTTIMES,
            </if>
            <if test="issendsms != null">
                ISSENDSMS,
            </if>
            <if test="issendemail != null">
                ISSENDEMAIL,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="validind != null">
                VALIDIND,
            </if>
            <if test="flag != null">
                FLAG,
            </if>
            <if test="inputDate != null">
                INPUTDATE,
            </if>
            <if test="updatesysdate != null">
                UPDATESYSDATE,
            </if>
            <if test="settlefee != null">
                SETTLEFEE
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="proposalNo != null">
                #{proposalNo},
            </if>
            <if test="policyNo != null">
                #{policyNo},
            </if>
            <if test="language != null">
                #{language},
            </if>
            <if test="productcode != null">
                #{productcode},
            </if>
            <if test="groupind != null">
                #{groupind},
            </if>
            <if test="insurancecompanycode != null">
                #{insurancecompanycode},
            </if>
            <if test="surveyind != null">
                #{surveyind},
            </if>
            <if test="flowid != null">
                #{flowid},
            </if>
            <if test="companycode != null">
                #{companycode},
            </if>
            <if test="companyname != null">
                #{companyname},
            </if>
            <if test="projectmanagercode != null">
                #{projectmanagercode},
            </if>
            <if test="projectmanagername != null">
                #{projectmanagername},
            </if>
            <if test="startDate != null">
                #{startDate},
            </if>
            <if test="endDate != null">
                #{endDate},
            </if>
            <if test="appliCode != null">
                #{appliCode},
            </if>
            <if test="appliName != null">
                #{appliName},
            </if>
            <if test="insuredCode != null">
                #{insuredCode},
            </if>
            <if test="insuredName != null">
                #{insuredName},
            </if>
            <if test="currency != null">
                #{currency},
            </if>
            <if test="suminsured != null">
                #{suminsured},
            </if>
            <if test="sumgrosspremium != null">
                #{sumgrosspremium},
            </if>
            <if test="sumnetpremium != null">
                #{sumnetpremium},
            </if>
            <if test="yearpremium != null">
                #{yearpremium},
            </if>
            <if test="sumuwpremium != null">
                #{sumuwpremium},
            </if>
            <if test="notaxpremium != null">
                #{notaxpremium},
            </if>
            <if test="taxamount != null">
                #{taxamount},
            </if>
            <if test="currencycny != null">
                #{currencycny},
            </if>
            <if test="suminsuredcny != null">
                #{suminsuredcny},
            </if>
            <if test="sumgrosspremiumcny != null">
                #{sumgrosspremiumcny},
            </if>
            <if test="sumuwpremiumcny != null">
                #{sumuwpremiumcny},
            </if>
            <if test="notaxpremiumcny != null">
                #{notaxpremiumcny},
            </if>
            <if test="taxamountcny != null">
                #{taxamountcny},
            </if>
            <if test="uwYear != null">
                #{uwYear},
            </if>
            <if test="acceptdate != null">
                #{acceptdate},
            </if>
            <if test="underwriteind != null">
                #{underwriteind},
            </if>
            <if test="underWriteEndDate != null">
                #{underWriteEndDate},
            </if>
            <if test="surrenderind != null">
                #{surrenderind},
            </if>
            <if test="cancelind != null">
                #{cancelind},
            </if>
            <if test="endind != null">
                #{endind},
            </if>
            <if test="codind != null">
                #{codind},
            </if>
            <if test="calculatetype != null">
                #{calculatetype},
            </if>
            <if test="coinsind != null">
                #{coinsind},
            </if>
            <if test="agentrate != null">
                #{agentrate},
            </if>
            <if test="notaxagentrate != null">
                #{notaxagentrate},
            </if>
            <if test="commission != null">
                #{commission},
            </if>
            <if test="fsh != null">
                #{fsh},
            </if>
            <if test="xsf != null">
                #{xsf},
            </if>
            <if test="xsfind != null">
                #{xsfind},
            </if>
            <if test="installmentno != null">
                #{installmentno},
            </if>
            <if test="endorseTimes != null">
                #{endorseTimes},
            </if>
            <if test="renewedtime != null">
                #{renewedtime},
            </if>
            <if test="registTimes != null">
                #{registTimes},
            </if>
            <if test="claimstimes != null">
                #{claimstimes},
            </if>
            <if test="printtimes != null">
                #{printtimes},
            </if>
            <if test="issendsms != null">
                #{issendsms},
            </if>
            <if test="issendemail != null">
                #{issendemail},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="validind != null">
                #{validind},
            </if>
            <if test="flag != null">
                #{flag},
            </if>
            <if test="inputDate != null">
                #{inputDate},
            </if>
            <if test="updatesysdate != null">
                #{updatesysdate},
            </if>
            <if test="settlefee != null">
                #{settlefee}
            </if>
        </trim>
    </insert>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" parameterType="ins.channel.gupolicymain.po.Gupolicymain">
        update GUPOLICYMAIN
        <set>
            <if test="proposalNo != null">
                PROPOSALNO=#{proposalNo},
            </if>
            <if test="policyNo != null">
                POLICYNO=#{policyNo},
            </if>
            <if test="language != null">
                LANGUAGE=#{language},
            </if>
            <if test="productcode != null">
                PRODUCTCODE=#{productcode},
            </if>
            <if test="groupind != null">
                GROUPIND=#{groupind},
            </if>
            <if test="insurancecompanycode != null">
                INSURANCECOMPANYCODE=#{insurancecompanycode},
            </if>
            <if test="surveyind != null">
                SURVEYIND=#{surveyind},
            </if>
            <if test="flowid != null">
                FLOWID=#{flowid},
            </if>
            <if test="companycode != null">
                COMPANYCODE=#{companycode},
            </if>
            <if test="companyname != null">
                COMPANYNAME=#{companyname},
            </if>
            <if test="projectmanagercode != null">
                PROJECTMANAGERCODE=#{projectmanagercode},
            </if>
            <if test="projectmanagername != null">
                PROJECTMANAGERNAME=#{projectmanagername},
            </if>
            <if test="startDate != null">
                STARTDATE=#{startDate},
            </if>
            <if test="endDate != null">
                ENDDATE=#{endDate},
            </if>
            <if test="appliCode != null">
                APPLICODE=#{appliCode},
            </if>
            <if test="appliName != null">
                APPLINAME=#{appliName},
            </if>
            <if test="insuredCode != null">
                INSUREDCODE=#{insuredCode},
            </if>
            <if test="insuredName != null">
                INSUREDNAME=#{insuredName},
            </if>
            <if test="currency != null">
                CURRENCY=#{currency},
            </if>
            <if test="suminsured != null">
                SUMINSURED=#{suminsured},
            </if>
            <if test="sumgrosspremium != null">
                SUMGROSSPREMIUM=#{sumgrosspremium},
            </if>
            <if test="sumnetpremium != null">
                SUMNETPREMIUM=#{sumnetpremium},
            </if>
            <if test="yearpremium != null">
                YEARPREMIUM=#{yearpremium},
            </if>
            <if test="sumuwpremium != null">
                SUMUWPREMIUM=#{sumuwpremium},
            </if>
            <if test="notaxpremium != null">
                NOTAXPREMIUM=#{notaxpremium},
            </if>
            <if test="taxamount != null">
                TAXAMOUNT=#{taxamount},
            </if>
            <if test="currencycny != null">
                CURRENCYCNY=#{currencycny},
            </if>
            <if test="suminsuredcny != null">
                SUMINSUREDCNY=#{suminsuredcny},
            </if>
            <if test="sumgrosspremiumcny != null">
                SUMGROSSPREMIUMCNY=#{sumgrosspremiumcny},
            </if>
            <if test="sumuwpremiumcny != null">
                SUMUWPREMIUMCNY=#{sumuwpremiumcny},
            </if>
            <if test="notaxpremiumcny != null">
                NOTAXPREMIUMCNY=#{notaxpremiumcny},
            </if>
            <if test="taxamountcny != null">
                TAXAMOUNTCNY=#{taxamountcny},
            </if>
            <if test="uwYear != null">
                UWYEAR=#{uwYear},
            </if>
            <if test="acceptdate != null">
                ACCEPTDATE=#{acceptdate},
            </if>
            <if test="underwriteind != null">
                UNDERWRITEIND=#{underwriteind},
            </if>
            <if test="underWriteEndDate != null">
                UNDERWRITEENDDATE=#{underWriteEndDate},
            </if>
            <if test="surrenderind != null">
                SURRENDERIND=#{surrenderind},
            </if>
            <if test="cancelind != null">
                CANCELIND=#{cancelind},
            </if>
            <if test="endind != null">
                ENDIND=#{endind},
            </if>
            <if test="codind != null">
                CODIND=#{codind},
            </if>
            <if test="calculatetype != null">
                CALCULATETYPE=#{calculatetype},
            </if>
            <if test="coinsind != null">
                COINSIND=#{coinsind},
            </if>
            <if test="agentrate != null">
                AGENTRATE=#{agentrate},
            </if>
            <if test="notaxagentrate != null">
                NOTAXAGENTRATE=#{notaxagentrate},
            </if>
            <if test="commission != null">
                COMMISSION=#{commission},
            </if>
            <if test="fsh != null">
                FSH=#{fsh},
            </if>
            <if test="xsf != null">
                XSF=#{xsf},
            </if>
            <if test="xsfind != null">
                XSFIND=#{xsfind},
            </if>
            <if test="installmentno != null">
                INSTALLMENTNO=#{installmentno},
            </if>
            <if test="endorseTimes != null">
                ENDORSETIMES=#{endorseTimes},
            </if>
            <if test="renewedtime != null">
                RENEWEDTIME=#{renewedtime},
            </if>
            <if test="registTimes != null">
                REGISTTIMES=#{registTimes},
            </if>
            <if test="claimstimes != null">
                CLAIMSTIMES=#{claimstimes},
            </if>
            <if test="printtimes != null">
                PRINTTIMES=#{printtimes},
            </if>
            <if test="issendsms != null">
                ISSENDSMS=#{issendsms},
            </if>
            <if test="issendemail != null">
                ISSENDEMAIL=#{issendemail},
            </if>
            <if test="remark != null">
                REMARK=#{remark},
            </if>
            <if test="validind != null">
                VALIDIND=#{validind},
            </if>
            <if test="flag != null">
                FLAG=#{flag},
            </if>
            <if test="inputDate != null">
                INPUTDATE=#{inputDate},
            </if>
            <if test="updatesysdate != null">
                UPDATESYSDATE=#{updatesysdate},
            </if>
            <if test="settlefee != null">
                SETTLEFEE=#{settlefee},
            </if>
        </set>
        where ID = #{id }
    </update>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" parameterType="ins.channel.gupolicymain.po.Gupolicymain">
		update GUPOLICYMAIN set
			PROPOSALNO=#{proposalNo},
			POLICYNO=#{policyNo},
			LANGUAGE=#{language},
			PRODUCTCODE=#{productcode},
			GROUPIND=#{groupind},
			INSURANCECOMPANYCODE=#{insurancecompanycode},
			SURVEYIND=#{surveyind},
			FLOWID=#{flowid},
			COMPANYCODE=#{companycode},
			COMPANYNAME=#{companyname},
			PROJECTMANAGERCODE=#{projectmanagercode},
			PROJECTMANAGERNAME=#{projectmanagername},
			STARTDATE=#{startDate},
			ENDDATE=#{endDate},
			APPLICODE=#{appliCode},
			APPLINAME=#{appliName},
			INSUREDCODE=#{insuredCode},
			INSUREDNAME=#{insuredName},
			CURRENCY=#{currency},
			SUMINSURED=#{suminsured},
			SUMGROSSPREMIUM=#{sumgrosspremium},
			SUMNETPREMIUM=#{sumnetpremium},
			YEARPREMIUM=#{yearpremium},
			SUMUWPREMIUM=#{sumuwpremium},
			NOTAXPREMIUM=#{notaxpremium},
			TAXAMOUNT=#{taxamount},
			CURRENCYCNY=#{currencycny},
			SUMINSUREDCNY=#{suminsuredcny},
			SUMGROSSPREMIUMCNY=#{sumgrosspremiumcny},
			SUMUWPREMIUMCNY=#{sumuwpremiumcny},
			NOTAXPREMIUMCNY=#{notaxpremiumcny},
			TAXAMOUNTCNY=#{taxamountcny},
			UWYEAR=#{uwYear},
			ACCEPTDATE=#{acceptdate},
			UNDERWRITEIND=#{underwriteind},
			UNDERWRITEENDDATE=#{underWriteEndDate},
			SURRENDERIND=#{surrenderind},
			CANCELIND=#{cancelind},
			ENDIND=#{endind},
			CODIND=#{codind},
			CALCULATETYPE=#{calculatetype},
			COINSIND=#{coinsind},
			AGENTRATE=#{agentrate},
			NOTAXAGENTRATE=#{notaxagentrate},
			COMMISSION=#{commission},
			FSH=#{fsh},
			XSF=#{xsf},
			XSFIND=#{xsfind},
			INSTALLMENTNO=#{installmentno},
			ENDORSETIMES=#{endorseTimes},
			RENEWEDTIME=#{renewedtime},
			REGISTTIMES=#{registTimes},
			CLAIMSTIMES=#{claimstimes},
			PRINTTIMES=#{printtimes},
			ISSENDSMS=#{issendsms},
			ISSENDEMAIL=#{issendemail},
			REMARK=#{remark},
			VALIDIND=#{validind},
			FLAG=#{flag},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
			SETTLEFEE=#{settlefee},
		where ID = #{id}	</update>


    <update id="batchUpdate" parameterType="java.util.List">
        update GUPOLICYMAIN
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="PROPOSALNO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.proposalNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="POLICYNO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="LANGUAGE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.language,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="PRODUCTCODE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.productcode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="GROUPIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.groupind,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="INSURANCECOMPANYCODE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.insurancecompanycode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="SURVEYIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.surveyind,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="FLOWID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.flowid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="COMPANYCODE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.companycode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="COMPANYNAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.companyname,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="PROJECTMANAGERCODE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.projectmanagercode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="PROJECTMANAGERNAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.projectmanagername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="STARTDATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.startDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="ENDDATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.endDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="APPLICODE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.appliCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="APPLINAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.appliName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="INSUREDCODE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="INSUREDNAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="CURRENCY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.currency,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="SUMINSURED = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.suminsured,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="SUMGROSSPREMIUM = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.sumgrosspremium,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="SUMNETPREMIUM = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.sumnetpremium,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="YEARPREMIUM = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.yearpremium,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="SUMUWPREMIUM = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.sumuwpremium,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="NOTAXPREMIUM = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.notaxpremium,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="TAXAMOUNT = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.taxamount,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="CURRENCYCNY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.currencycny,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="SUMINSUREDCNY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.suminsuredcny,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="SUMGROSSPREMIUMCNY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.sumgrosspremiumcny,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="SUMUWPREMIUMCNY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.sumuwpremiumcny,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="NOTAXPREMIUMCNY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.notaxpremiumcny,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="TAXAMOUNTCNY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.taxamountcny,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="UWYEAR = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.uwYear,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ACCEPTDATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.acceptdate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="UNDERWRITEIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.underwriteind,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="UNDERWRITEENDDATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.underWriteEndDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="SURRENDERIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.surrenderind,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="CANCELIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.cancelind,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ENDIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.endind,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="CODIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.codind,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="CALCULATETYPE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.calculatetype,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="COINSIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.coinsind,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="AGENTRATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.agentrate,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="NOTAXAGENTRATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.notaxagentrate,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="COMMISSION = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.commission,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="FSH = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.fsh,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="XSF = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.xsf,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="XSFIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.xsfind,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="INSTALLMENTNO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.installmentno,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="ENDORSETIMES = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.endorseTimes,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="RENEWEDTIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.renewedtime,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="REGISTTIMES = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.registTimes,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="CLAIMSTIMES = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.claimstimes,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="PRINTTIMES = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.printtimes,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="ISSENDSMS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.issendsms,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ISSENDEMAIL = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.issendemail,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="REMARK = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="VALIDIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.validind,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="FLAG = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.flag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="INPUTDATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="UPDATESYSDATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="SETTLEFEE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.settlefee,jdbcType=DECIMAL}
                </foreach>
            </trim>
        </trim>
        where ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="batchUpdateSelective" parameterType="java.util.List">
        update GUPOLICYMAIN
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="PROPOSALNO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.proposalNo != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.proposalNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="POLICYNO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.policyNo != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="LANGUAGE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.language != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.language,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="PRODUCTCODE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productcode != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.productcode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="GROUPIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.groupind != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.groupind,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="INSURANCECOMPANYCODE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.insurancecompanycode != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.insurancecompanycode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SURVEYIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.surveyind != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.surveyind,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="FLOWID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.flowid != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.flowid,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="COMPANYCODE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.companycode != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.companycode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="COMPANYNAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.companyname != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.companyname,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="PROJECTMANAGERCODE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectmanagercode != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.projectmanagercode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="PROJECTMANAGERNAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectmanagername != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.projectmanagername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="STARTDATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.startDate != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.startDate,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ENDDATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.endDate != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.endDate,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="APPLICODE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.appliCode != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.appliCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="APPLINAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.appliName != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.appliName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="INSUREDCODE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.insuredCode != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="INSUREDNAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.insuredName != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.insuredName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CURRENCY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.currency != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.currency,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SUMINSURED = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.suminsured != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.suminsured,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SUMGROSSPREMIUM = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sumgrosspremium != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.sumgrosspremium,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SUMNETPREMIUM = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sumnetpremium != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.sumnetpremium,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="YEARPREMIUM = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yearpremium != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.yearpremium,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SUMUWPREMIUM = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sumuwpremium != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.sumuwpremium,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="NOTAXPREMIUM = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.notaxpremium != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.notaxpremium,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="TAXAMOUNT = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taxamount != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.taxamount,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CURRENCYCNY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.currencycny != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.currencycny,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SUMINSUREDCNY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.suminsuredcny != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.suminsuredcny,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SUMGROSSPREMIUMCNY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sumgrosspremiumcny != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.sumgrosspremiumcny,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SUMUWPREMIUMCNY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sumuwpremiumcny != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.sumuwpremiumcny,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="NOTAXPREMIUMCNY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.notaxpremiumcny != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.notaxpremiumcny,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="TAXAMOUNTCNY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taxamountcny != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.taxamountcny,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UWYEAR = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.uwYear != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.uwYear,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ACCEPTDATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.acceptdate != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.acceptdate,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UNDERWRITEIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.underwriteind != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.underwriteind,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UNDERWRITEENDDATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.underWriteEndDate != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.underWriteEndDate,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SURRENDERIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.surrenderind != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.surrenderind,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CANCELIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cancelind != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.cancelind,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ENDIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.endind != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.endind,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CODIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.codind != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.codind,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CALCULATETYPE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.calculatetype != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.calculatetype,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="COINSIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.coinsind != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.coinsind,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="AGENTRATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.agentrate != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.agentrate,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="NOTAXAGENTRATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.notaxagentrate != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.notaxagentrate,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="COMMISSION = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.commission != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.commission,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="FSH = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.fsh != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.fsh,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="XSF = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.xsf != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.xsf,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="XSFIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.xsfind != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.xsfind,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="INSTALLMENTNO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.installmentno != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.installmentno,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ENDORSETIMES = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.endorseTimes != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.endorseTimes,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="RENEWEDTIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.renewedtime != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.renewedtime,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="REGISTTIMES = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.registTimes != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.registTimes,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CLAIMSTIMES = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.claimstimes != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.claimstimes,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="PRINTTIMES = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.printtimes != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.printtimes,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ISSENDSMS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.issendsms != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.issendsms,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ISSENDEMAIL = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.issendemail != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.issendemail,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="REMARK = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remark != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="VALIDIND = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.validind != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.validind,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="FLAG = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.flag != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.flag,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="INPUTDATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.inputDate != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UPDATESYSDATE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updatesysdate != null">
                        when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SETTLEFEE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when ID = #{item.id,jdbcType=VARCHAR} then #{item.settlefee,jdbcType=DECIMAL}
                </foreach>
            </trim>
        </trim>
        where ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="list" item="item" separator="">
            INTO GUPOLICYMAIN VALUES
            (#{item.id,jdbcType=VARCHAR}, #{item.proposalNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR},
            #{item.language,jdbcType=VARCHAR}, #{item.productcode,jdbcType=VARCHAR}, #{item.groupind,jdbcType=VARCHAR},
            #{item.insurancecompanycode,jdbcType=VARCHAR}, #{item.surveyind,jdbcType=VARCHAR},
            #{item.flowid,jdbcType=VARCHAR}, #{item.companycode,jdbcType=VARCHAR}, #{item.companyname,jdbcType=VARCHAR},
            #{item.projectmanagercode,jdbcType=VARCHAR}, #{item.projectmanagername,jdbcType=VARCHAR},
            #{item.startDate,jdbcType=TIMESTAMP}, #{item.endDate,jdbcType=TIMESTAMP},
            #{item.appliCode,jdbcType=VARCHAR},
            #{item.appliName,jdbcType=VARCHAR}, #{item.insuredCode,jdbcType=VARCHAR},
            #{item.insuredName,jdbcType=VARCHAR},
            #{item.currency,jdbcType=VARCHAR}, #{item.suminsured,jdbcType=DECIMAL},
            #{item.sumgrosspremium,jdbcType=DECIMAL},
            #{item.sumnetpremium,jdbcType=DECIMAL}, #{item.yearpremium,jdbcType=DECIMAL},
            #{item.sumuwpremium,jdbcType=DECIMAL},
            #{item.notaxpremium,jdbcType=DECIMAL}, #{item.taxamount,jdbcType=DECIMAL},
            #{item.currencycny,jdbcType=VARCHAR},
            #{item.suminsuredcny,jdbcType=DECIMAL}, #{item.sumgrosspremiumcny,jdbcType=DECIMAL},
            #{item.sumuwpremiumcny,jdbcType=DECIMAL}, #{item.notaxpremiumcny,jdbcType=DECIMAL},
            #{item.taxamountcny,jdbcType=DECIMAL}, #{item.uwYear,jdbcType=VARCHAR},
            #{item.acceptdate,jdbcType=TIMESTAMP},
            #{item.underwriteind,jdbcType=VARCHAR}, #{item.underWriteEndDate,jdbcType=TIMESTAMP},
            #{item.surrenderind,jdbcType=VARCHAR}, #{item.cancelind,jdbcType=VARCHAR}, #{item.endind,jdbcType=VARCHAR},
            #{item.codind,jdbcType=VARCHAR}, #{item.calculatetype,jdbcType=VARCHAR}, #{item.coinsind,jdbcType=VARCHAR},
            #{item.agentrate,jdbcType=DECIMAL}, #{item.notaxagentrate,jdbcType=DECIMAL},
            #{item.commission,jdbcType=DECIMAL},
            #{item.fsh,jdbcType=DECIMAL}, #{item.xsf,jdbcType=DECIMAL}, #{item.xsfind,jdbcType=VARCHAR},
            #{item.installmentno,jdbcType=DECIMAL}, #{item.endorseTimes,jdbcType=DECIMAL},
            #{item.renewedtime,jdbcType=DECIMAL}, #{item.registTimes,jdbcType=DECIMAL},
            #{item.claimstimes,jdbcType=DECIMAL},
            #{item.printtimes,jdbcType=DECIMAL}, #{item.issendsms,jdbcType=VARCHAR},
            #{item.issendemail,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, #{item.validind,jdbcType=VARCHAR}, #{item.flag,jdbcType=VARCHAR},
            #{item.inputDate,jdbcType=TIMESTAMP}, #{item.updatesysdate,jdbcType=TIMESTAMP},#{item.settlefee,jdbcType=DECIMAL})
        </foreach>
        select 1 from dual
    </insert>
</mapper>
