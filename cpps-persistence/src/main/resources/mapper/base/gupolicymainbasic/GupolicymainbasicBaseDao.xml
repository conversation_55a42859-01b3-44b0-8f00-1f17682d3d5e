<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicymainbasic.dao.GupolicymainbasicDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicymainbasic.po.Gupolicymainbasic">
		<id column="ID" property="id" />
		<result column="POLICYNO" property="policyNo" />
		<result column="PRODUCTCODE" property="productcode" />
		<result column="SHAREHOLDERFLAG" property="shareHolderFlag" />
		<result column="INSURANCECOMPANYCODE" property="insurancecompanycode" />
		<result column="BUSINESSCHANNEL" property="businesschannel" />
		<result column="CHANNELDETAILCODE" property="channeldetailcode" />
		<result column="CHANNELTIP" property="channeltip" />
		<result column="CHANNELFLAG" property="channelflag" />
		<result column="INTERMEDIARYCODE" property="intermediarycode" />
		<result column="AGREEMENTNO" property="agreementNo" />
		<result column="BUSINESSSOURCE" property="businessSource" />
		<result column="BUSINESSTYPE" property="businessType" />
		<result column="ISSUEDATE" property="issueDate" />
		<result column="ISSUECOMPANY" property="issuecompany" />
		<result column="ISSUEPLACE" property="issueplace" />
		<result column="OPERATORCODE" property="operatorcode" />
		<result column="OPERATEDATE" property="operateDate" />
		<result column="ARGUESOLUTION" property="argueSolution" />
		<result column="ARBITORYNAME" property="arbitoryname" />
		<result column="DOMESTICIND" property="domesticind" />
		<result column="AGRICULTUREFLAG" property="agricultureflag" />
		<result column="MONEYSUSPICIOUSIND" property="moneysuspiciousind" />
		<result column="COMINSUREIND" property="cominsureind" />
		<result column="ISDIFFERENTPLACE" property="isdifferentplace" />
		<result column="POLICYSTYLE" property="policystyle" />
		<result column="ISFARMING" property="isfarming" />
		<result column="RENEWIND" property="renewind" />
		<result column="RENEWEDIND" property="renewedind" />
		<result column="RENEWEDTIME" property="renewedtime" />
		<result column="RENEWALNO" property="renewalno" />
		<result column="REPLACEDPOLICYNO" property="replacedpolicyno" />
		<result column="AUTORENEWIND" property="autorenewind" />
		<result column="MULTIRISKIND" property="multiriskind" />
		<result column="RISKAPPLYTYPE" property="riskapplytype" />
		<result column="PRODUCTEDITION" property="productedition" />
		<result column="PRODUCTEDITIONNAME" property="producteditionname" />
		<result column="COMPENSATIONTYPE" property="compensationtype" />
		<result column="DISCOVERSTARTDATE" property="discoverstartdate" />
		<result column="DISCOVERENDDATE" property="discoverenddate" />
		<result column="JUDICALCODE" property="judicalCode" />
		<result column="GEOGRAPHICALAREA" property="geographicalarea" />
		<result column="GEOGRAPHICALAREADESC" property="geographicalareadesc" />
		<result column="UPLOADIND" property="uploadind" />
		<result column="NOMINATIVEIND" property="nominativeind" />
		<result column="BUSINESSIND" property="businessind" />
		<result column="BUSINESSMODE" property="businessmode" />
		<result column="SALESTEAMCODE" property="salesteamcode" />
		<result column="SALESTEAMNAME" property="salesteamname" />
		<result column="SALESMANCODE" property="salesmancode" />
		<result column="SALESMANNAME" property="salesmanname" />
		<result column="SALESMANREGISTERNO" property="salesmanregisterno" />
		<result column="SELLERNAME" property="sellername" />
		<result column="SELLERREGISTERNO" property="sellerregisterno" />
		<result column="INTERSALESMANREGISTERNO" property="intersalesmanregisterno" />
		<result column="INTERSALESMANCODE" property="intersalesmancode" />
		<result column="TEAMMANAGERCODE" property="teammanagercode" />
		<result column="TEAMMANAGERNAME" property="teammanagername" />
		<result column="UNDERWRITECODE" property="underWriteCode" />
		<result column="UNDERWRITENAME" property="underWriteName" />
		<result column="LASTMODIFIERCODE" property="lastmodifiercode" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		POLICYNO,
		PRODUCTCODE,
		SHAREHOLDERFLAG,
		INSURANCECOMPANYCODE,
		BUSINESSCHANNEL,
		CHANNELDETAILCODE,
		CHANNELTIP,
		CHANNELFLAG,
		INTERMEDIARYCODE,
		AGREEMENTNO,
		BUSINESSSOURCE,
		BUSINESSTYPE,
		ISSUEDATE,
		ISSUECOMPANY,
		ISSUEPLACE,
		OPERATORCODE,
		OPERATEDATE,
		ARGUESOLUTION,
		ARBITORYNAME,
		DOMESTICIND,
		AGRICULTUREFLAG,
		MONEYSUSPICIOUSIND,
		COMINSUREIND,
		ISDIFFERENTPLACE,
		POLICYSTYLE,
		ISFARMING,
		RENEWIND,
		RENEWEDIND,
		RENEWEDTIME,
		RENEWALNO,
		REPLACEDPOLICYNO,
		AUTORENEWIND,
		MULTIRISKIND,
		RISKAPPLYTYPE,
		PRODUCTEDITION,
		PRODUCTEDITIONNAME,
		COMPENSATIONTYPE,
		DISCOVERSTARTDATE,
		DISCOVERENDDATE,
		JUDICALCODE,
		GEOGRAPHICALAREA,
		GEOGRAPHICALAREADESC,
		UPLOADIND,
		NOMINATIVEIND,
		BUSINESSIND,
		BUSINESSMODE,
		SALESTEAMCODE,
		SALESTEAMNAME,
		SALESMANCODE,
		SALESMANNAME,
		SALESMANREGISTERNO,
		SELLERNAME,
		SELLERREGISTERNO,
		INTERSALESMANREGISTERNO,
		INTERSALESMANCODE,
		TEAMMANAGERCODE,
		TEAMMANAGERNAME,
		UNDERWRITECODE,
		UNDERWRITENAME,
		LASTMODIFIERCODE,
		INPUTDATE,
		UPDATESYSDATE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="productcode != null and productcode != ''" >
			and PRODUCTCODE = #{productcode}
		</if>
		<if test="shareHolderFlag != null and shareHolderFlag != ''" >
			and SHAREHOLDERFLAG = #{shareHolderFlag}
		</if>
		<if test="insurancecompanycode != null and insurancecompanycode != ''" >
			and INSURANCECOMPANYCODE = #{insurancecompanycode}
		</if>
		<if test="businesschannel != null and businesschannel != ''" >
			and BUSINESSCHANNEL = #{businesschannel}
		</if>
		<if test="channeldetailcode != null and channeldetailcode != ''" >
			and CHANNELDETAILCODE = #{channeldetailcode}
		</if>
		<if test="channeltip != null and channeltip != ''" >
			and CHANNELTIP = #{channeltip}
		</if>
		<if test="channelflag != null and channelflag != ''" >
			and CHANNELFLAG = #{channelflag}
		</if>
		<if test="intermediarycode != null and intermediarycode != ''" >
			and INTERMEDIARYCODE = #{intermediarycode}
		</if>
		<if test="agreementNo != null and agreementNo != ''" >
			and AGREEMENTNO = #{agreementNo}
		</if>
		<if test="businessSource != null and businessSource != ''" >
			and BUSINESSSOURCE = #{businessSource}
		</if>
		<if test="businessType != null and businessType != ''" >
			and BUSINESSTYPE = #{businessType}
		</if>
		<if test="issueDate != null and issueDate != ''" >
			and ISSUEDATE = #{issueDate}
		</if>
		<if test="issuecompany != null and issuecompany != ''" >
			and ISSUECOMPANY = #{issuecompany}
		</if>
		<if test="issueplace != null and issueplace != ''" >
			and ISSUEPLACE = #{issueplace}
		</if>
		<if test="operatorcode != null and operatorcode != ''" >
			and OPERATORCODE = #{operatorcode}
		</if>
		<if test="operateDate != null and operateDate != ''" >
			and OPERATEDATE = #{operateDate}
		</if>
		<if test="argueSolution != null and argueSolution != ''" >
			and ARGUESOLUTION = #{argueSolution}
		</if>
		<if test="arbitoryname != null and arbitoryname != ''" >
			and ARBITORYNAME = #{arbitoryname}
		</if>
		<if test="domesticind != null and domesticind != ''" >
			and DOMESTICIND = #{domesticind}
		</if>
		<if test="agricultureflag != null and agricultureflag != ''" >
			and AGRICULTUREFLAG = #{agricultureflag}
		</if>
		<if test="moneysuspiciousind != null and moneysuspiciousind != ''" >
			and MONEYSUSPICIOUSIND = #{moneysuspiciousind}
		</if>
		<if test="cominsureind != null and cominsureind != ''" >
			and COMINSUREIND = #{cominsureind}
		</if>
		<if test="isdifferentplace != null and isdifferentplace != ''" >
			and ISDIFFERENTPLACE = #{isdifferentplace}
		</if>
		<if test="policystyle != null and policystyle != ''" >
			and POLICYSTYLE = #{policystyle}
		</if>
		<if test="isfarming != null and isfarming != ''" >
			and ISFARMING = #{isfarming}
		</if>
		<if test="renewind != null and renewind != ''" >
			and RENEWIND = #{renewind}
		</if>
		<if test="renewedind != null and renewedind != ''" >
			and RENEWEDIND = #{renewedind}
		</if>
		<if test="renewedtime != null and renewedtime != ''" >
			and RENEWEDTIME = #{renewedtime}
		</if>
		<if test="renewalno != null and renewalno != ''" >
			and RENEWALNO = #{renewalno}
		</if>
		<if test="replacedpolicyno != null and replacedpolicyno != ''" >
			and REPLACEDPOLICYNO = #{replacedpolicyno}
		</if>
		<if test="autorenewind != null and autorenewind != ''" >
			and AUTORENEWIND = #{autorenewind}
		</if>
		<if test="multiriskind != null and multiriskind != ''" >
			and MULTIRISKIND = #{multiriskind}
		</if>
		<if test="riskapplytype != null and riskapplytype != ''" >
			and RISKAPPLYTYPE = #{riskapplytype}
		</if>
		<if test="productedition != null and productedition != ''" >
			and PRODUCTEDITION = #{productedition}
		</if>
		<if test="producteditionname != null and producteditionname != ''" >
			and PRODUCTEDITIONNAME = #{producteditionname}
		</if>
		<if test="compensationtype != null and compensationtype != ''" >
			and COMPENSATIONTYPE = #{compensationtype}
		</if>
		<if test="discoverstartdate != null and discoverstartdate != ''" >
			and DISCOVERSTARTDATE = #{discoverstartdate}
		</if>
		<if test="discoverenddate != null and discoverenddate != ''" >
			and DISCOVERENDDATE = #{discoverenddate}
		</if>
		<if test="judicalCode != null and judicalCode != ''" >
			and JUDICALCODE = #{judicalCode}
		</if>
		<if test="geographicalarea != null and geographicalarea != ''" >
			and GEOGRAPHICALAREA = #{geographicalarea}
		</if>
		<if test="geographicalareadesc != null and geographicalareadesc != ''" >
			and GEOGRAPHICALAREADESC = #{geographicalareadesc}
		</if>
		<if test="uploadind != null and uploadind != ''" >
			and UPLOADIND = #{uploadind}
		</if>
		<if test="nominativeind != null and nominativeind != ''" >
			and NOMINATIVEIND = #{nominativeind}
		</if>
		<if test="businessind != null and businessind != ''" >
			and BUSINESSIND = #{businessind}
		</if>
		<if test="businessmode != null and businessmode != ''" >
			and BUSINESSMODE = #{businessmode}
		</if>
		<if test="salesteamcode != null and salesteamcode != ''" >
			and SALESTEAMCODE = #{salesteamcode}
		</if>
		<if test="salesteamname != null and salesteamname != ''" >
			and SALESTEAMNAME = #{salesteamname}
		</if>
		<if test="salesmancode != null and salesmancode != ''" >
			and SALESMANCODE = #{salesmancode}
		</if>
		<if test="salesmanname != null and salesmanname != ''" >
			and SALESMANNAME = #{salesmanname}
		</if>
		<if test="salesmanregisterno != null and salesmanregisterno != ''" >
			and SALESMANREGISTERNO = #{salesmanregisterno}
		</if>
		<if test="sellername != null and sellername != ''" >
			and SELLERNAME = #{sellername}
		</if>
		<if test="sellerregisterno != null and sellerregisterno != ''" >
			and SELLERREGISTERNO = #{sellerregisterno}
		</if>
		<if test="intersalesmanregisterno != null and intersalesmanregisterno != ''" >
			and INTERSALESMANREGISTERNO = #{intersalesmanregisterno}
		</if>
		<if test="intersalesmancode != null and intersalesmancode != ''" >
			and INTERSALESMANCODE = #{intersalesmancode}
		</if>
		<if test="teammanagercode != null and teammanagercode != ''" >
			and TEAMMANAGERCODE = #{teammanagercode}
		</if>
		<if test="teammanagername != null and teammanagername != ''" >
			and TEAMMANAGERNAME = #{teammanagername}
		</if>
		<if test="underWriteCode != null and underWriteCode != ''" >
			and UNDERWRITECODE = #{underWriteCode}
		</if>
		<if test="underWriteName != null and underWriteName != ''" >
			and UNDERWRITENAME = #{underWriteName}
		</if>
		<if test="lastmodifiercode != null and lastmodifiercode != ''" >
			and LASTMODIFIERCODE = #{lastmodifiercode}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYMAINBASIC
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYMAINBASIC
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYMAINBASIC
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicymainbasic.po.Gupolicymainbasic">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYMAINBASIC
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYMAINBASIC
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicymainbasic.po.Gupolicymainbasic">
		insert into GUPOLICYMAINBASIC (
			ID,
			POLICYNO,
			PRODUCTCODE,
			SHAREHOLDERFLAG,
			INSURANCECOMPANYCODE,
			BUSINESSCHANNEL,
			CHANNELDETAILCODE,
			CHANNELTIP,
			CHANNELFLAG,
			INTERMEDIARYCODE,
			AGREEMENTNO,
			BUSINESSSOURCE,
			BUSINESSTYPE,
			ISSUEDATE,
			ISSUECOMPANY,
			ISSUEPLACE,
			OPERATORCODE,
			OPERATEDATE,
			ARGUESOLUTION,
			ARBITORYNAME,
			DOMESTICIND,
			AGRICULTUREFLAG,
			MONEYSUSPICIOUSIND,
			COMINSUREIND,
			ISDIFFERENTPLACE,
			POLICYSTYLE,
			ISFARMING,
			RENEWIND,
			RENEWEDIND,
			RENEWEDTIME,
			RENEWALNO,
			REPLACEDPOLICYNO,
			AUTORENEWIND,
			MULTIRISKIND,
			RISKAPPLYTYPE,
			PRODUCTEDITION,
			PRODUCTEDITIONNAME,
			COMPENSATIONTYPE,
			DISCOVERSTARTDATE,
			DISCOVERENDDATE,
			JUDICALCODE,
			GEOGRAPHICALAREA,
			GEOGRAPHICALAREADESC,
			UPLOADIND,
			NOMINATIVEIND,
			BUSINESSIND,
			BUSINESSMODE,
			SALESTEAMCODE,
			SALESTEAMNAME,
			SALESMANCODE,
			SALESMANNAME,
			SALESMANREGISTERNO,
			SELLERNAME,
			SELLERREGISTERNO,
			INTERSALESMANREGISTERNO,
			INTERSALESMANCODE,
			TEAMMANAGERCODE,
			TEAMMANAGERNAME,
			UNDERWRITECODE,
			UNDERWRITENAME,
			LASTMODIFIERCODE,
			INPUTDATE,
			UPDATESYSDATE
		) values (
			#{id},
			#{policyNo},
			#{productcode},
			#{shareHolderFlag},
			#{insurancecompanycode},
			#{businesschannel},
			#{channeldetailcode},
			#{channeltip},
			#{channelflag},
			#{intermediarycode},
			#{agreementNo},
			#{businessSource},
			#{businessType},
			#{issueDate},
			#{issuecompany},
			#{issueplace},
			#{operatorcode},
			#{operateDate},
			#{argueSolution},
			#{arbitoryname},
			#{domesticind},
			#{agricultureflag},
			#{moneysuspiciousind},
			#{cominsureind},
			#{isdifferentplace},
			#{policystyle},
			#{isfarming},
			#{renewind},
			#{renewedind},
			#{renewedtime},
			#{renewalno},
			#{replacedpolicyno},
			#{autorenewind},
			#{multiriskind},
			#{riskapplytype},
			#{productedition},
			#{producteditionname},
			#{compensationtype},
			#{discoverstartdate},
			#{discoverenddate},
			#{judicalCode},
			#{geographicalarea},
			#{geographicalareadesc},
			#{uploadind},
			#{nominativeind},
			#{businessind},
			#{businessmode},
			#{salesteamcode},
			#{salesteamname},
			#{salesmancode},
			#{salesmanname},
			#{salesmanregisterno},
			#{sellername},
			#{sellerregisterno},
			#{intersalesmanregisterno},
			#{intersalesmancode},
			#{teammanagercode},
			#{teammanagername},
			#{underWriteCode},
			#{underWriteName},
			#{lastmodifiercode},
			#{inputDate},
			#{updatesysdate}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicymainbasic.po.Gupolicymainbasic">
		insert into GUPOLICYMAINBASIC
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="productcode != null" >
				PRODUCTCODE,
			</if>
			<if test="shareHolderFlag != null" >
				SHAREHOLDERFLAG,
			</if>
			<if test="insurancecompanycode != null" >
				INSURANCECOMPANYCODE,
			</if>
			<if test="businesschannel != null" >
				BUSINESSCHANNEL,
			</if>
			<if test="channeldetailcode != null" >
				CHANNELDETAILCODE,
			</if>
			<if test="channeltip != null" >
				CHANNELTIP,
			</if>
			<if test="channelflag != null" >
				CHANNELFLAG,
			</if>
			<if test="intermediarycode != null" >
				INTERMEDIARYCODE,
			</if>
			<if test="agreementNo != null" >
				AGREEMENTNO,
			</if>
			<if test="businessSource != null" >
				BUSINESSSOURCE,
			</if>
			<if test="businessType != null" >
				BUSINESSTYPE,
			</if>
			<if test="issueDate != null" >
				ISSUEDATE,
			</if>
			<if test="issuecompany != null" >
				ISSUECOMPANY,
			</if>
			<if test="issueplace != null" >
				ISSUEPLACE,
			</if>
			<if test="operatorcode != null" >
				OPERATORCODE,
			</if>
			<if test="operateDate != null" >
				OPERATEDATE,
			</if>
			<if test="argueSolution != null" >
				ARGUESOLUTION,
			</if>
			<if test="arbitoryname != null" >
				ARBITORYNAME,
			</if>
			<if test="domesticind != null" >
				DOMESTICIND,
			</if>
			<if test="agricultureflag != null" >
				AGRICULTUREFLAG,
			</if>
			<if test="moneysuspiciousind != null" >
				MONEYSUSPICIOUSIND,
			</if>
			<if test="cominsureind != null" >
				COMINSUREIND,
			</if>
			<if test="isdifferentplace != null" >
				ISDIFFERENTPLACE,
			</if>
			<if test="policystyle != null" >
				POLICYSTYLE,
			</if>
			<if test="isfarming != null" >
				ISFARMING,
			</if>
			<if test="renewind != null" >
				RENEWIND,
			</if>
			<if test="renewedind != null" >
				RENEWEDIND,
			</if>
			<if test="renewedtime != null" >
				RENEWEDTIME,
			</if>
			<if test="renewalno != null" >
				RENEWALNO,
			</if>
			<if test="replacedpolicyno != null" >
				REPLACEDPOLICYNO,
			</if>
			<if test="autorenewind != null" >
				AUTORENEWIND,
			</if>
			<if test="multiriskind != null" >
				MULTIRISKIND,
			</if>
			<if test="riskapplytype != null" >
				RISKAPPLYTYPE,
			</if>
			<if test="productedition != null" >
				PRODUCTEDITION,
			</if>
			<if test="producteditionname != null" >
				PRODUCTEDITIONNAME,
			</if>
			<if test="compensationtype != null" >
				COMPENSATIONTYPE,
			</if>
			<if test="discoverstartdate != null" >
				DISCOVERSTARTDATE,
			</if>
			<if test="discoverenddate != null" >
				DISCOVERENDDATE,
			</if>
			<if test="judicalCode != null" >
				JUDICALCODE,
			</if>
			<if test="geographicalarea != null" >
				GEOGRAPHICALAREA,
			</if>
			<if test="geographicalareadesc != null" >
				GEOGRAPHICALAREADESC,
			</if>
			<if test="uploadind != null" >
				UPLOADIND,
			</if>
			<if test="nominativeind != null" >
				NOMINATIVEIND,
			</if>
			<if test="businessind != null" >
				BUSINESSIND,
			</if>
			<if test="businessmode != null" >
				BUSINESSMODE,
			</if>
			<if test="salesteamcode != null" >
				SALESTEAMCODE,
			</if>
			<if test="salesteamname != null" >
				SALESTEAMNAME,
			</if>
			<if test="salesmancode != null" >
				SALESMANCODE,
			</if>
			<if test="salesmanname != null" >
				SALESMANNAME,
			</if>
			<if test="salesmanregisterno != null" >
				SALESMANREGISTERNO,
			</if>
			<if test="sellername != null" >
				SELLERNAME,
			</if>
			<if test="sellerregisterno != null" >
				SELLERREGISTERNO,
			</if>
			<if test="intersalesmanregisterno != null" >
				INTERSALESMANREGISTERNO,
			</if>
			<if test="intersalesmancode != null" >
				INTERSALESMANCODE,
			</if>
			<if test="teammanagercode != null" >
				TEAMMANAGERCODE,
			</if>
			<if test="teammanagername != null" >
				TEAMMANAGERNAME,
			</if>
			<if test="underWriteCode != null" >
				UNDERWRITECODE,
			</if>
			<if test="underWriteName != null" >
				UNDERWRITENAME,
			</if>
			<if test="lastmodifiercode != null" >
				LASTMODIFIERCODE,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="productcode != null" >
				#{productcode},
			</if>
			<if test="shareHolderFlag != null" >
				#{shareHolderFlag},
			</if>
			<if test="insurancecompanycode != null" >
				#{insurancecompanycode},
			</if>
			<if test="businesschannel != null" >
				#{businesschannel},
			</if>
			<if test="channeldetailcode != null" >
				#{channeldetailcode},
			</if>
			<if test="channeltip != null" >
				#{channeltip},
			</if>
			<if test="channelflag != null" >
				#{channelflag},
			</if>
			<if test="intermediarycode != null" >
				#{intermediarycode},
			</if>
			<if test="agreementNo != null" >
				#{agreementNo},
			</if>
			<if test="businessSource != null" >
				#{businessSource},
			</if>
			<if test="businessType != null" >
				#{businessType},
			</if>
			<if test="issueDate != null" >
				#{issueDate},
			</if>
			<if test="issuecompany != null" >
				#{issuecompany},
			</if>
			<if test="issueplace != null" >
				#{issueplace},
			</if>
			<if test="operatorcode != null" >
				#{operatorcode},
			</if>
			<if test="operateDate != null" >
				#{operateDate},
			</if>
			<if test="argueSolution != null" >
				#{argueSolution},
			</if>
			<if test="arbitoryname != null" >
				#{arbitoryname},
			</if>
			<if test="domesticind != null" >
				#{domesticind},
			</if>
			<if test="agricultureflag != null" >
				#{agricultureflag},
			</if>
			<if test="moneysuspiciousind != null" >
				#{moneysuspiciousind},
			</if>
			<if test="cominsureind != null" >
				#{cominsureind},
			</if>
			<if test="isdifferentplace != null" >
				#{isdifferentplace},
			</if>
			<if test="policystyle != null" >
				#{policystyle},
			</if>
			<if test="isfarming != null" >
				#{isfarming},
			</if>
			<if test="renewind != null" >
				#{renewind},
			</if>
			<if test="renewedind != null" >
				#{renewedind},
			</if>
			<if test="renewedtime != null" >
				#{renewedtime},
			</if>
			<if test="renewalno != null" >
				#{renewalno},
			</if>
			<if test="replacedpolicyno != null" >
				#{replacedpolicyno},
			</if>
			<if test="autorenewind != null" >
				#{autorenewind},
			</if>
			<if test="multiriskind != null" >
				#{multiriskind},
			</if>
			<if test="riskapplytype != null" >
				#{riskapplytype},
			</if>
			<if test="productedition != null" >
				#{productedition},
			</if>
			<if test="producteditionname != null" >
				#{producteditionname},
			</if>
			<if test="compensationtype != null" >
				#{compensationtype},
			</if>
			<if test="discoverstartdate != null" >
				#{discoverstartdate},
			</if>
			<if test="discoverenddate != null" >
				#{discoverenddate},
			</if>
			<if test="judicalCode != null" >
				#{judicalCode},
			</if>
			<if test="geographicalarea != null" >
				#{geographicalarea},
			</if>
			<if test="geographicalareadesc != null" >
				#{geographicalareadesc},
			</if>
			<if test="uploadind != null" >
				#{uploadind},
			</if>
			<if test="nominativeind != null" >
				#{nominativeind},
			</if>
			<if test="businessind != null" >
				#{businessind},
			</if>
			<if test="businessmode != null" >
				#{businessmode},
			</if>
			<if test="salesteamcode != null" >
				#{salesteamcode},
			</if>
			<if test="salesteamname != null" >
				#{salesteamname},
			</if>
			<if test="salesmancode != null" >
				#{salesmancode},
			</if>
			<if test="salesmanname != null" >
				#{salesmanname},
			</if>
			<if test="salesmanregisterno != null" >
				#{salesmanregisterno},
			</if>
			<if test="sellername != null" >
				#{sellername},
			</if>
			<if test="sellerregisterno != null" >
				#{sellerregisterno},
			</if>
			<if test="intersalesmanregisterno != null" >
				#{intersalesmanregisterno},
			</if>
			<if test="intersalesmancode != null" >
				#{intersalesmancode},
			</if>
			<if test="teammanagercode != null" >
				#{teammanagercode},
			</if>
			<if test="teammanagername != null" >
				#{teammanagername},
			</if>
			<if test="underWriteCode != null" >
				#{underWriteCode},
			</if>
			<if test="underWriteName != null" >
				#{underWriteName},
			</if>
			<if test="lastmodifiercode != null" >
				#{lastmodifiercode},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicymainbasic.po.Gupolicymainbasic">
		update GUPOLICYMAINBASIC 
		<set>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="productcode != null" >
				PRODUCTCODE=#{productcode},
			</if>
			<if test="shareHolderFlag != null" >
				SHAREHOLDERFLAG=#{shareHolderFlag},
			</if>
			<if test="insurancecompanycode != null" >
				INSURANCECOMPANYCODE=#{insurancecompanycode},
			</if>
			<if test="businesschannel != null" >
				BUSINESSCHANNEL=#{businesschannel},
			</if>
			<if test="channeldetailcode != null" >
				CHANNELDETAILCODE=#{channeldetailcode},
			</if>
			<if test="channeltip != null" >
				CHANNELTIP=#{channeltip},
			</if>
			<if test="channelflag != null" >
				CHANNELFLAG=#{channelflag},
			</if>
			<if test="intermediarycode != null" >
				INTERMEDIARYCODE=#{intermediarycode},
			</if>
			<if test="agreementNo != null" >
				AGREEMENTNO=#{agreementNo},
			</if>
			<if test="businessSource != null" >
				BUSINESSSOURCE=#{businessSource},
			</if>
			<if test="businessType != null" >
				BUSINESSTYPE=#{businessType},
			</if>
			<if test="issueDate != null" >
				ISSUEDATE=#{issueDate},
			</if>
			<if test="issuecompany != null" >
				ISSUECOMPANY=#{issuecompany},
			</if>
			<if test="issueplace != null" >
				ISSUEPLACE=#{issueplace},
			</if>
			<if test="operatorcode != null" >
				OPERATORCODE=#{operatorcode},
			</if>
			<if test="operateDate != null" >
				OPERATEDATE=#{operateDate},
			</if>
			<if test="argueSolution != null" >
				ARGUESOLUTION=#{argueSolution},
			</if>
			<if test="arbitoryname != null" >
				ARBITORYNAME=#{arbitoryname},
			</if>
			<if test="domesticind != null" >
				DOMESTICIND=#{domesticind},
			</if>
			<if test="agricultureflag != null" >
				AGRICULTUREFLAG=#{agricultureflag},
			</if>
			<if test="moneysuspiciousind != null" >
				MONEYSUSPICIOUSIND=#{moneysuspiciousind},
			</if>
			<if test="cominsureind != null" >
				COMINSUREIND=#{cominsureind},
			</if>
			<if test="isdifferentplace != null" >
				ISDIFFERENTPLACE=#{isdifferentplace},
			</if>
			<if test="policystyle != null" >
				POLICYSTYLE=#{policystyle},
			</if>
			<if test="isfarming != null" >
				ISFARMING=#{isfarming},
			</if>
			<if test="renewind != null" >
				RENEWIND=#{renewind},
			</if>
			<if test="renewedind != null" >
				RENEWEDIND=#{renewedind},
			</if>
			<if test="renewedtime != null" >
				RENEWEDTIME=#{renewedtime},
			</if>
			<if test="renewalno != null" >
				RENEWALNO=#{renewalno},
			</if>
			<if test="replacedpolicyno != null" >
				REPLACEDPOLICYNO=#{replacedpolicyno},
			</if>
			<if test="autorenewind != null" >
				AUTORENEWIND=#{autorenewind},
			</if>
			<if test="multiriskind != null" >
				MULTIRISKIND=#{multiriskind},
			</if>
			<if test="riskapplytype != null" >
				RISKAPPLYTYPE=#{riskapplytype},
			</if>
			<if test="productedition != null" >
				PRODUCTEDITION=#{productedition},
			</if>
			<if test="producteditionname != null" >
				PRODUCTEDITIONNAME=#{producteditionname},
			</if>
			<if test="compensationtype != null" >
				COMPENSATIONTYPE=#{compensationtype},
			</if>
			<if test="discoverstartdate != null" >
				DISCOVERSTARTDATE=#{discoverstartdate},
			</if>
			<if test="discoverenddate != null" >
				DISCOVERENDDATE=#{discoverenddate},
			</if>
			<if test="judicalCode != null" >
				JUDICALCODE=#{judicalCode},
			</if>
			<if test="geographicalarea != null" >
				GEOGRAPHICALAREA=#{geographicalarea},
			</if>
			<if test="geographicalareadesc != null" >
				GEOGRAPHICALAREADESC=#{geographicalareadesc},
			</if>
			<if test="uploadind != null" >
				UPLOADIND=#{uploadind},
			</if>
			<if test="nominativeind != null" >
				NOMINATIVEIND=#{nominativeind},
			</if>
			<if test="businessind != null" >
				BUSINESSIND=#{businessind},
			</if>
			<if test="businessmode != null" >
				BUSINESSMODE=#{businessmode},
			</if>
			<if test="salesteamcode != null" >
				SALESTEAMCODE=#{salesteamcode},
			</if>
			<if test="salesteamname != null" >
				SALESTEAMNAME=#{salesteamname},
			</if>
			<if test="salesmancode != null" >
				SALESMANCODE=#{salesmancode},
			</if>
			<if test="salesmanname != null" >
				SALESMANNAME=#{salesmanname},
			</if>
			<if test="salesmanregisterno != null" >
				SALESMANREGISTERNO=#{salesmanregisterno},
			</if>
			<if test="sellername != null" >
				SELLERNAME=#{sellername},
			</if>
			<if test="sellerregisterno != null" >
				SELLERREGISTERNO=#{sellerregisterno},
			</if>
			<if test="intersalesmanregisterno != null" >
				INTERSALESMANREGISTERNO=#{intersalesmanregisterno},
			</if>
			<if test="intersalesmancode != null" >
				INTERSALESMANCODE=#{intersalesmancode},
			</if>
			<if test="teammanagercode != null" >
				TEAMMANAGERCODE=#{teammanagercode},
			</if>
			<if test="teammanagername != null" >
				TEAMMANAGERNAME=#{teammanagername},
			</if>
			<if test="underWriteCode != null" >
				UNDERWRITECODE=#{underWriteCode},
			</if>
			<if test="underWriteName != null" >
				UNDERWRITENAME=#{underWriteName},
			</if>
			<if test="lastmodifiercode != null" >
				LASTMODIFIERCODE=#{lastmodifiercode},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicymainbasic.po.Gupolicymainbasic">
		update GUPOLICYMAINBASIC set
			POLICYNO=#{policyNo},
			PRODUCTCODE=#{productcode},
			SHAREHOLDERFLAG=#{shareHolderFlag},
			INSURANCECOMPANYCODE=#{insurancecompanycode},
			BUSINESSCHANNEL=#{businesschannel},
			CHANNELDETAILCODE=#{channeldetailcode},
			CHANNELTIP=#{channeltip},
			CHANNELFLAG=#{channelflag},
			INTERMEDIARYCODE=#{intermediarycode},
			AGREEMENTNO=#{agreementNo},
			BUSINESSSOURCE=#{businessSource},
			BUSINESSTYPE=#{businessType},
			ISSUEDATE=#{issueDate},
			ISSUECOMPANY=#{issuecompany},
			ISSUEPLACE=#{issueplace},
			OPERATORCODE=#{operatorcode},
			OPERATEDATE=#{operateDate},
			ARGUESOLUTION=#{argueSolution},
			ARBITORYNAME=#{arbitoryname},
			DOMESTICIND=#{domesticind},
			AGRICULTUREFLAG=#{agricultureflag},
			MONEYSUSPICIOUSIND=#{moneysuspiciousind},
			COMINSUREIND=#{cominsureind},
			ISDIFFERENTPLACE=#{isdifferentplace},
			POLICYSTYLE=#{policystyle},
			ISFARMING=#{isfarming},
			RENEWIND=#{renewind},
			RENEWEDIND=#{renewedind},
			RENEWEDTIME=#{renewedtime},
			RENEWALNO=#{renewalno},
			REPLACEDPOLICYNO=#{replacedpolicyno},
			AUTORENEWIND=#{autorenewind},
			MULTIRISKIND=#{multiriskind},
			RISKAPPLYTYPE=#{riskapplytype},
			PRODUCTEDITION=#{productedition},
			PRODUCTEDITIONNAME=#{producteditionname},
			COMPENSATIONTYPE=#{compensationtype},
			DISCOVERSTARTDATE=#{discoverstartdate},
			DISCOVERENDDATE=#{discoverenddate},
			JUDICALCODE=#{judicalCode},
			GEOGRAPHICALAREA=#{geographicalarea},
			GEOGRAPHICALAREADESC=#{geographicalareadesc},
			UPLOADIND=#{uploadind},
			NOMINATIVEIND=#{nominativeind},
			BUSINESSIND=#{businessind},
			BUSINESSMODE=#{businessmode},
			SALESTEAMCODE=#{salesteamcode},
			SALESTEAMNAME=#{salesteamname},
			SALESMANCODE=#{salesmancode},
			SALESMANNAME=#{salesmanname},
			SALESMANREGISTERNO=#{salesmanregisterno},
			SELLERNAME=#{sellername},
			SELLERREGISTERNO=#{sellerregisterno},
			INTERSALESMANREGISTERNO=#{intersalesmanregisterno},
			INTERSALESMANCODE=#{intersalesmancode},
			TEAMMANAGERCODE=#{teammanagercode},
			TEAMMANAGERNAME=#{teammanagername},
			UNDERWRITECODE=#{underWriteCode},
			UNDERWRITENAME=#{underWriteName},
			LASTMODIFIERCODE=#{lastmodifiercode},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
		where ID = #{id}	</update>

	<update id="batchUpdate" parameterType="java.util.List">
		update GUPOLICYMAINBASIC
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="POLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="PRODUCTCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.productcode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SHAREHOLDERFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.shareHolderFlag,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INSURANCECOMPANYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.insurancecompanycode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="BUSINESSCHANNEL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.businesschannel,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CHANNELDETAILCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.channeldetailcode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CHANNELTIP = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.channeltip,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="CHANNELFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.channelflag,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INTERMEDIARYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.intermediarycode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="AGREEMENTNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.agreementNo,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="BUSINESSSOURCE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.businessSource,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="BUSINESSTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.businessType,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ISSUEDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.issueDate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="ISSUECOMPANY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.issuecompany,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ISSUEPLACE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.issueplace,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="OPERATORCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.operatorcode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="OPERATEDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.operateDate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="ARGUESOLUTION = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.argueSolution,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ARBITORYNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.arbitoryname,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="DOMESTICIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.domesticind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="AGRICULTUREFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.agricultureflag,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="MONEYSUSPICIOUSIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.moneysuspiciousind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="COMINSUREIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.cominsureind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ISDIFFERENTPLACE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.isdifferentplace,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="POLICYSTYLE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.policystyle,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="ISFARMING = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.isfarming,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="RENEWIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.renewind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="RENEWEDIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.renewedind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="RENEWEDTIME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.renewedtime,jdbcType=DECIMAL}
				</foreach>
			</trim>
			<trim prefix="RENEWALNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.renewalno,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="REPLACEDPOLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.replacedpolicyno,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="AUTORENEWIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.autorenewind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="MULTIRISKIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.multiriskind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="RISKAPPLYTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.riskapplytype,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="PRODUCTEDITION = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.productedition,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="PRODUCTEDITIONNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.producteditionname,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="COMPENSATIONTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.compensationtype,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="DISCOVERSTARTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.discoverstartdate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="DISCOVERENDDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.discoverenddate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="JUDICALCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.judicalCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="GEOGRAPHICALAREA = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.geographicalarea,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="GEOGRAPHICALAREADESC = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.geographicalareadesc,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="UPLOADIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.uploadind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="NOMINATIVEIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.nominativeind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="BUSINESSIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.businessind,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="BUSINESSMODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.businessmode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SALESTEAMCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.salesteamcode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SALESTEAMNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.salesteamname,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SALESMANCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.salesmancode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SALESMANNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.salesmanname,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SALESMANREGISTERNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.salesmanregisterno,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SELLERNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.sellername,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="SELLERREGISTERNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.sellerregisterno,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INTERSALESMANREGISTERNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.intersalesmanregisterno,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INTERSALESMANCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.intersalesmancode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="TEAMMANAGERCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.teammanagercode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="TEAMMANAGERNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.teammanagername,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="UNDERWRITECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.underWriteCode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="UNDERWRITENAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.underWriteName,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="LASTMODIFIERCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.lastmodifiercode,jdbcType=VARCHAR}
				</foreach>
			</trim>
			<trim prefix="INPUTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
			<trim prefix="UPDATESYSDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
				</foreach>
			</trim>
		</trim>
		where ID in
		<foreach close=")" collection="list" item="item" open="(" separator=", ">
			#{item.id,jdbcType=VARCHAR}
		</foreach>
	</update>
	<update id="batchUpdateSelective" parameterType="java.util.List">
		update GUPOLICYMAINBASIC
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="POLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.policyNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.policyNo,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="PRODUCTCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.productcode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.productcode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SHAREHOLDERFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.shareHolderFlag != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.shareHolderFlag,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INSURANCECOMPANYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.insurancecompanycode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.insurancecompanycode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="BUSINESSCHANNEL = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.businesschannel != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.businesschannel,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CHANNELDETAILCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.channeldetailcode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.channeldetailcode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CHANNELTIP = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.channeltip != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.channeltip,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="CHANNELFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.channelflag != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.channelflag,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INTERMEDIARYCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.intermediarycode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.intermediarycode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="AGREEMENTNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.agreementNo != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.agreementNo,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="BUSINESSSOURCE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.businessSource != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.businessSource,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="BUSINESSTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.businessType != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.businessType,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ISSUEDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.issueDate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.issueDate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="ISSUECOMPANY = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.issuecompany != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.issuecompany,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ISSUEPLACE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.issueplace != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.issueplace,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="OPERATORCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.operatorcode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.operatorcode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="OPERATEDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.operateDate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.operateDate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="ARGUESOLUTION = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.argueSolution != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.argueSolution,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ARBITORYNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.arbitoryname != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.arbitoryname,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="DOMESTICIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.domesticind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.domesticind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="AGRICULTUREFLAG = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.agricultureflag != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.agricultureflag,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="MONEYSUSPICIOUSIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.moneysuspiciousind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.moneysuspiciousind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="COMINSUREIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.cominsureind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.cominsureind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ISDIFFERENTPLACE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.isdifferentplace != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.isdifferentplace,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="POLICYSTYLE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.policystyle != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.policystyle,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="ISFARMING = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.isfarming != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.isfarming,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="RENEWIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.renewind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.renewind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="RENEWEDIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.renewedind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.renewedind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="RENEWEDTIME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.renewedtime != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.renewedtime,jdbcType=DECIMAL}
					</if>
				</foreach>
			</trim>
			<trim prefix="RENEWALNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.renewalno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.renewalno,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="REPLACEDPOLICYNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.replacedpolicyno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.replacedpolicyno,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="AUTORENEWIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.autorenewind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.autorenewind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="MULTIRISKIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.multiriskind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.multiriskind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="RISKAPPLYTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.riskapplytype != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.riskapplytype,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="PRODUCTEDITION = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.productedition != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.productedition,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="PRODUCTEDITIONNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.producteditionname != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.producteditionname,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="COMPENSATIONTYPE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.compensationtype != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.compensationtype,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="DISCOVERSTARTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.discoverstartdate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.discoverstartdate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="DISCOVERENDDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.discoverenddate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.discoverenddate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="JUDICALCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.judicalCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.judicalCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="GEOGRAPHICALAREA = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.geographicalarea != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.geographicalarea,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="GEOGRAPHICALAREADESC = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.geographicalareadesc != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.geographicalareadesc,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="UPLOADIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.uploadind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.uploadind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="NOMINATIVEIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.nominativeind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.nominativeind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="BUSINESSIND = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.businessind != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.businessind,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="BUSINESSMODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.businessmode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.businessmode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SALESTEAMCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.salesteamcode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.salesteamcode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SALESTEAMNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.salesteamname != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.salesteamname,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SALESMANCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.salesmancode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.salesmancode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SALESMANNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.salesmanname != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.salesmanname,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SALESMANREGISTERNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.salesmanregisterno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.salesmanregisterno,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SELLERNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.sellername != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.sellername,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="SELLERREGISTERNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.sellerregisterno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.sellerregisterno,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INTERSALESMANREGISTERNO = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.intersalesmanregisterno != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.intersalesmanregisterno,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INTERSALESMANCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.intersalesmancode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.intersalesmancode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="TEAMMANAGERCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.teammanagercode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.teammanagercode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="TEAMMANAGERNAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.teammanagername != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.teammanagername,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="UNDERWRITECODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.underWriteCode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.underWriteCode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="UNDERWRITENAME = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.underWriteName != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.underWriteName,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="LASTMODIFIERCODE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.lastmodifiercode != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.lastmodifiercode,jdbcType=VARCHAR}
					</if>
				</foreach>
			</trim>
			<trim prefix="INPUTDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.inputDate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.inputDate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
			<trim prefix="UPDATESYSDATE = case" suffix="end,">
				<foreach collection="list" index="index" item="item">
					<if test="item.updatesysdate != null">
						when ID = #{item.id,jdbcType=VARCHAR} then #{item.updatesysdate,jdbcType=TIMESTAMP}
					</if>
				</foreach>
			</trim>
		</trim>
		where ID in
		<foreach close=")" collection="list" item="item" open="(" separator=", ">
			#{item.id,jdbcType=VARCHAR}
		</foreach>
	</update>
	<insert id="batchInsert" parameterType="java.util.List">
		INSERT  ALL
		<foreach collection="list" item="item" separator="">
		INTO GUPOLICYMAINBASIC VALUES
			(#{item.id,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.productcode,jdbcType=VARCHAR},
			#{item.shareHolderFlag,jdbcType=VARCHAR}, #{item.insurancecompanycode,jdbcType=VARCHAR},
			#{item.businesschannel,jdbcType=VARCHAR}, #{item.channeldetailcode,jdbcType=VARCHAR},
			#{item.channeltip,jdbcType=VARCHAR}, #{item.channelflag,jdbcType=VARCHAR}, #{item.intermediarycode,jdbcType=VARCHAR},
			#{item.agreementNo,jdbcType=VARCHAR}, #{item.businessSource,jdbcType=VARCHAR},
			#{item.businessType,jdbcType=VARCHAR}, #{item.issueDate,jdbcType=TIMESTAMP}, #{item.issuecompany,jdbcType=VARCHAR},
			#{item.issueplace,jdbcType=VARCHAR}, #{item.operatorcode,jdbcType=VARCHAR}, #{item.operateDate,jdbcType=TIMESTAMP},
			#{item.argueSolution,jdbcType=VARCHAR}, #{item.arbitoryname,jdbcType=VARCHAR},
			#{item.domesticind,jdbcType=VARCHAR}, #{item.agricultureflag,jdbcType=VARCHAR},
			#{item.moneysuspiciousind,jdbcType=VARCHAR}, #{item.cominsureind,jdbcType=VARCHAR},
			#{item.isdifferentplace,jdbcType=VARCHAR}, #{item.policystyle,jdbcType=VARCHAR},
			#{item.isfarming,jdbcType=VARCHAR}, #{item.renewind,jdbcType=VARCHAR}, #{item.renewedind,jdbcType=VARCHAR},
			#{item.renewedtime,jdbcType=DECIMAL}, #{item.renewalno,jdbcType=VARCHAR}, #{item.replacedpolicyno,jdbcType=VARCHAR},
			#{item.autorenewind,jdbcType=VARCHAR}, #{item.multiriskind,jdbcType=VARCHAR}, #{item.riskapplytype,jdbcType=VARCHAR},
			#{item.productedition,jdbcType=VARCHAR}, #{item.producteditionname,jdbcType=VARCHAR},
			#{item.compensationtype,jdbcType=VARCHAR}, #{item.discoverstartdate,jdbcType=TIMESTAMP},
			#{item.discoverenddate,jdbcType=TIMESTAMP}, #{item.judicalCode,jdbcType=VARCHAR},
			#{item.geographicalarea,jdbcType=VARCHAR}, #{item.geographicalareadesc,jdbcType=VARCHAR},
			#{item.uploadind,jdbcType=VARCHAR}, #{item.nominativeind,jdbcType=VARCHAR}, #{item.businessind,jdbcType=VARCHAR},
			#{item.businessmode,jdbcType=VARCHAR}, #{item.salesteamcode,jdbcType=VARCHAR},
			#{item.salesteamname,jdbcType=VARCHAR}, #{item.salesmancode,jdbcType=VARCHAR},
			#{item.salesmanname,jdbcType=VARCHAR}, #{item.salesmanregisterno,jdbcType=VARCHAR},
			#{item.sellername,jdbcType=VARCHAR}, #{item.sellerregisterno,jdbcType=VARCHAR},
			#{item.intersalesmanregisterno,jdbcType=VARCHAR}, #{item.intersalesmancode,jdbcType=VARCHAR},
			#{item.teammanagercode,jdbcType=VARCHAR}, #{item.teammanagername,jdbcType=VARCHAR},
			#{item.underWriteCode,jdbcType=VARCHAR}, #{item.underWriteName,jdbcType=VARCHAR},
			#{item.lastmodifiercode,jdbcType=VARCHAR}, #{item.inputDate,jdbcType=TIMESTAMP},
			#{item.updatesysdate,jdbcType=TIMESTAMP})
		</foreach>
		select 1 from dual
	</insert>

	<delete id="deleteByPolicyNo" parameterType="string">
		delete from GUPOLICYMAINBASIC where policyNo = #{policyNo}
	</delete>
</mapper>
