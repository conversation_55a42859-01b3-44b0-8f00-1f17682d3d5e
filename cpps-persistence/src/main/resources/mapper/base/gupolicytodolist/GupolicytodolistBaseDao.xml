<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicytodolist.dao.GupolicytodolistDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicytodolist.po.Gupolicytodolist">
		<id column="ID" property="id" />
		<result column="ENDORNO" property="endorNo" />
		<result column="POLICYNO" property="policyNo" />
		<result column="DECLARATIONDATE" property="declarationdate" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		ENDORNO,
		POLICYNO,
		DECLARATIONDATE,
		REMARK,
		FLAG
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="endorNo != null and endorNo != ''" >
			and ENDORNO = #{endorNo}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="declarationdate != null and declarationdate != ''" >
			and DECLARATIONDATE = #{declarationdate}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYTODOLIST
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYTODOLIST
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYTODOLIST
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicytodolist.po.Gupolicytodolist">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYTODOLIST
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYTODOLIST
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicytodolist.po.Gupolicytodolist">
		insert into GUPOLICYTODOLIST (
			ID,
			ENDORNO,
			POLICYNO,
			DECLARATIONDATE,
			REMARK,
			FLAG
		) values (
			#{id},
			#{endorNo},
			#{policyNo},
			#{declarationdate},
			#{remark},
			#{flag}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicytodolist.po.Gupolicytodolist">
		insert into GUPOLICYTODOLIST
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="endorNo != null" >
				ENDORNO,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="declarationdate != null" >
				DECLARATIONDATE,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="endorNo != null" >
				#{endorNo},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="declarationdate != null" >
				#{declarationdate},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicytodolist.po.Gupolicytodolist">
		update GUPOLICYTODOLIST 
		<set>
			<if test="endorNo != null" >
				ENDORNO=#{endorNo},
			</if>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="declarationdate != null" >
				DECLARATIONDATE=#{declarationdate},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicytodolist.po.Gupolicytodolist">
		update GUPOLICYTODOLIST set
			ENDORNO=#{endorNo},
			POLICYNO=#{policyNo},
			DECLARATIONDATE=#{declarationdate},
			REMARK=#{remark},
			FLAG=#{flag},
		where ID = #{id}	</update>
</mapper>
