<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicyuserauthority.dao.GupolicyuserauthorityDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gupolicyuserauthority.po.Gupolicyuserauthority">
		<id column="ID" property="id" />
		<result column="USER_CODE" property="userCode" />
		<result column="COMPANY_CODE" property="companyCode" />
		<result column="POLICYNO" property="policyNo" />
		<result column="RISKCODE" property="riskCode" />
		<result column="CHANNELCODE" property="channelcode" />
		<result column="INSURANCECOMPANYCODE" property="insurancecompanycode" />
		<result column="SURVEYIND" property="surveyind" />
		<result column="VALIDIND" property="validind" />
		<result column="FLAG" property="flag" />
		<result column="REMARK" property="remark" />
		<result column="INPUTDATE" property="inputDate" />
		<result column="UPDATESYSDATE" property="updatesysdate" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		USER_CODE,
		COMPANY_CODE,
		POLICYNO,
		RISKCODE,
		CHANNELCODE,
		INSURANCECOMPANYCODE,
		SURVEYIND,
		VALIDIND,
		FLAG,
		REMARK,
		INPUTDATE,
		UPDATESYSDATE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="userCode != null and userCode != ''" >
			and USER_CODE = #{userCode}
		</if>
		<if test="companyCode != null and companyCode != ''" >
			and COMPANY_CODE = #{companyCode}
		</if>
		<if test="policyNo != null and policyNo != ''" >
			and POLICYNO = #{policyNo}
		</if>
		<if test="riskCode != null and riskCode != ''" >
			and RISKCODE = #{riskCode}
		</if>
		<if test="channelcode != null and channelcode != ''" >
			and CHANNELCODE = #{channelcode}
		</if>
		<if test="insurancecompanycode != null and insurancecompanycode != ''" >
			and INSURANCECOMPANYCODE = #{insurancecompanycode}
		</if>
		<if test="surveyind != null and surveyind != ''" >
			and SURVEYIND = #{surveyind}
		</if>
		<if test="validind != null and validind != ''" >
			and VALIDIND = #{validind}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="inputDate != null and inputDate != ''" >
			and INPUTDATE = #{inputDate}
		</if>
		<if test="updatesysdate != null and updatesysdate != ''" >
			and UPDATESYSDATE = #{updatesysdate}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYUSERAUTHORITY
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYUSERAUTHORITY
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GUPOLICYUSERAUTHORITY
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gupolicyuserauthority.po.Gupolicyuserauthority">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GUPOLICYUSERAUTHORITY
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GUPOLICYUSERAUTHORITY
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gupolicyuserauthority.po.Gupolicyuserauthority">
		insert into GUPOLICYUSERAUTHORITY (
			ID,
			USER_CODE,
			COMPANY_CODE,
			POLICYNO,
			RISKCODE,
			CHANNELCODE,
			INSURANCECOMPANYCODE,
			SURVEYIND,
			VALIDIND,
			FLAG,
			REMARK,
			INPUTDATE,
			UPDATESYSDATE
		) values (
			#{id},
			#{userCode},
			#{companyCode},
			#{policyNo},
			#{riskCode},
			#{channelcode},
			#{insurancecompanycode},
			#{surveyind},
			#{validind},
			#{flag},
			#{remark},
			#{inputDate},
			#{updatesysdate}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gupolicyuserauthority.po.Gupolicyuserauthority">
		insert into GUPOLICYUSERAUTHORITY
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="userCode != null" >
				USER_CODE,
			</if>
			<if test="companyCode != null" >
				COMPANY_CODE,
			</if>
			<if test="policyNo != null" >
				POLICYNO,
			</if>
			<if test="riskCode != null" >
				RISKCODE,
			</if>
			<if test="channelcode != null" >
				CHANNELCODE,
			</if>
			<if test="insurancecompanycode != null" >
				INSURANCECOMPANYCODE,
			</if>
			<if test="surveyind != null" >
				SURVEYIND,
			</if>
			<if test="validind != null" >
				VALIDIND,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="inputDate != null" >
				INPUTDATE,
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="userCode != null" >
				#{userCode},
			</if>
			<if test="companyCode != null" >
				#{companyCode},
			</if>
			<if test="policyNo != null" >
				#{policyNo},
			</if>
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="channelcode != null" >
				#{channelcode},
			</if>
			<if test="insurancecompanycode != null" >
				#{insurancecompanycode},
			</if>
			<if test="surveyind != null" >
				#{surveyind},
			</if>
			<if test="validind != null" >
				#{validind},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="inputDate != null" >
				#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				#{updatesysdate}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gupolicyuserauthority.po.Gupolicyuserauthority">
		update GUPOLICYUSERAUTHORITY 
		<set>
			<if test="userCode != null" >
				USER_CODE=#{userCode},
			</if>
			<if test="companyCode != null" >
				COMPANY_CODE=#{companyCode},
			</if>
			<if test="policyNo != null" >
				POLICYNO=#{policyNo},
			</if>
			<if test="riskCode != null" >
				RISKCODE=#{riskCode},
			</if>
			<if test="channelcode != null" >
				CHANNELCODE=#{channelcode},
			</if>
			<if test="insurancecompanycode != null" >
				INSURANCECOMPANYCODE=#{insurancecompanycode},
			</if>
			<if test="surveyind != null" >
				SURVEYIND=#{surveyind},
			</if>
			<if test="validind != null" >
				VALIDIND=#{validind},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="inputDate != null" >
				INPUTDATE=#{inputDate},
			</if>
			<if test="updatesysdate != null" >
				UPDATESYSDATE=#{updatesysdate},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gupolicyuserauthority.po.Gupolicyuserauthority">
		update GUPOLICYUSERAUTHORITY set
			USER_CODE=#{userCode},
			COMPANY_CODE=#{companyCode},
			POLICYNO=#{policyNo},
			RISKCODE=#{riskCode},
			CHANNELCODE=#{channelcode},
			INSURANCECOMPANYCODE=#{insurancecompanycode},
			SURVEYIND=#{surveyind},
			VALIDIND=#{validind},
			FLAG=#{flag},
			REMARK=#{remark},
			INPUTDATE=#{inputDate},
			UPDATESYSDATE=#{updatesysdate},
		where ID = #{id}	</update>
</mapper>
