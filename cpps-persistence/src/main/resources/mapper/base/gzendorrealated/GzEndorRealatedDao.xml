<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ins.channel.gzendorrealated.dao.GzEndorRealatedDtoMapper" >
  <resultMap id="BaseResultMap" type="ins.channel.gzendorrealated.dto.domain.GzEndorRealatedDto" >
    <result column="UUID" property="uuid" jdbcType="VARCHAR" />
    <result column="ENDORNO" property="endorno" jdbcType="VARCHAR" />
    <result column="POLICYNO" property="policyno" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    UUID, ENDORNO, POLICYNO
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="ins.channel.gzendorrealated.dto.domain.GzEndorRealatedDtoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from GZENDORREALATED
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="ins.channel.gzendorrealated.dto.domain.GzEndorRealatedDtoExample" >
    delete from GZENDORREALATED
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="ins.channel.gzendorrealated.dto.domain.GzEndorRealatedDto" >
    insert into GZENDORREALATED (UUID, ENDORNO, POLICYNO
      )
    values (#{uuid,jdbcType=VARCHAR}, #{endorno,jdbcType=VARCHAR}, #{policyno,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="ins.channel.gzendorrealated.dto.domain.GzEndorRealatedDto" >
    insert into GZENDORREALATED
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="uuid != null" >
        UUID,
      </if>
      <if test="endorno != null" >
        ENDORNO,
      </if>
      <if test="policyno != null" >
        POLICYNO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="uuid != null" >
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="endorno != null" >
        #{endorno,jdbcType=VARCHAR},
      </if>
      <if test="policyno != null" >
        #{policyno,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ins.channel.gzendorrealated.dto.domain.GzEndorRealatedDtoExample" resultType="java.lang.Integer" >
    select count(*) from GZENDORREALATED
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update GZENDORREALATED
    <set >
      <if test="record.uuid != null" >
        UUID = #{record.uuid,jdbcType=VARCHAR},
      </if>
      <if test="record.endorno != null" >
        ENDORNO = #{record.endorno,jdbcType=VARCHAR},
      </if>
      <if test="record.policyno != null" >
        POLICYNO = #{record.policyno,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update GZENDORREALATED
    set UUID = #{record.uuid,jdbcType=VARCHAR},
      ENDORNO = #{record.endorno,jdbcType=VARCHAR},
      POLICYNO = #{record.policyno,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>