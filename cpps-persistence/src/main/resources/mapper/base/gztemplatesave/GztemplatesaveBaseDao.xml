<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gztemplatesave.dao.GztemplatesaveDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.gztemplatesave.po.Gztemplatesave">
		<id column="UUID" property="uuid" />
		<result column="TEMPLATEDATA" property="templatedata" />
		<result column="CREATEDATE" property="createDate" />
		<result column="USERCODE" property="userCode" />
		<result column="REMARK" property="remark" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		UUID,
		TEMPLATEDATA,
		CREATEDATE,
		USERCODE,
		REMARK
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="uuid != null and uuid != ''" >
			and UUID = #{uuid}
		</if>
		<if test="templatedata != null and templatedata != ''" >
			and TEMPLATEDATA = #{templatedata}
		</if>
		<if test="createDate != null and createDate != ''" >
			and CREATEDATE = #{createDate}
		</if>
		<if test="userCode != null and userCode != ''" >
			and USERCODE = #{userCode}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GZTEMPLATESAVE
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GZTEMPLATESAVE
		where UUID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GZTEMPLATESAVE
		where UUID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.gztemplatesave.po.Gztemplatesave">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GZTEMPLATESAVE
		where UUID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GZTEMPLATESAVE
		where UUID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.gztemplatesave.po.Gztemplatesave">
		insert into GZTEMPLATESAVE (
			UUID,
			TEMPLATEDATA,
			CREATEDATE,
			USERCODE,
			REMARK
		) values (
			#{uuid},
			#{templatedata},
			#{createDate},
			#{userCode},
			#{remark}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.gztemplatesave.po.Gztemplatesave">
		insert into GZTEMPLATESAVE
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="uuid != null" >
				UUID,
			</if>
			<if test="templatedata != null" >
				TEMPLATEDATA,
			</if>
			<if test="createDate != null" >
				CREATEDATE,
			</if>
			<if test="userCode != null" >
				USERCODE,
			</if>
			<if test="remark != null" >
				REMARK
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="uuid != null" >
				#{uuid},
			</if>
			<if test="templatedata != null" >
				#{templatedata},
			</if>
			<if test="createDate != null" >
				#{createDate},
			</if>
			<if test="userCode != null" >
				#{userCode},
			</if>
			<if test="remark != null" >
				#{remark}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.gztemplatesave.po.Gztemplatesave">
		update GZTEMPLATESAVE 
		<set>
			<if test="templatedata != null" >
				TEMPLATEDATA=#{templatedata},
			</if>
			<if test="createDate != null" >
				CREATEDATE=#{createDate},
			</if>
			<if test="userCode != null" >
				USERCODE=#{userCode},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
		</set>
		where UUID = #{uuid }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.gztemplatesave.po.Gztemplatesave">
		update GZTEMPLATESAVE set
			TEMPLATEDATA=#{templatedata},
			CREATEDATE=#{createDate},
			USERCODE=#{userCode},
			REMARK=#{remark},
		where UUID = #{uuid}	</update>
</mapper>
