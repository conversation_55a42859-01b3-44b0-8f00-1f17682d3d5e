<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.paycomcodedefine.dao.GppaycomcodedefineDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.paycomcodedefine.po.Gppaycomcodedefine">
		<id column="PAYMENT_COMCODE" property="paymentComcode" />
		<result column="DEPARTMENT_CODE" property="departmentCode" />
		<result column="OFCENTER_CNAME" property="ofcenterCname" />
		<result column="OFCENTER_TNAME" property="ofcenterTname" />
		<result column="OFCENTER_ENAME" property="ofcenterEname" />
		<result column="VALID_IND" property="validInd" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="CREATE_CODE" property="createCode" />
		<result column="MODIFIED_TIME" property="modifiedTime" />
		<result column="MODIFIED_CODE" property="modifiedCode" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		PAYMENT_COMCODE,
		DEPARTMENT_CODE,
		OFCENTER_CNAME,
		OFCENTER_TNAME,
		OFCENTER_ENAME,
		VALID_IND,
		REMARK,
		FLAG,
		CREATE_TIME,
		CREATE_CODE,
		MODIFIED_TIME,
		MODIFIED_CODE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="paymentComcode != null and paymentComcode != ''" >
			and PAYMENT_COMCODE = #{paymentComcode}
		</if>
		<if test="departmentCode != null and departmentCode != ''" >
			and DEPARTMENT_CODE = #{departmentCode}
		</if>
		<if test="ofcenterCname != null and ofcenterCname != ''" >
			and OFCENTER_CNAME = #{ofcenterCname}
		</if>
		<if test="ofcenterTname != null and ofcenterTname != ''" >
			and OFCENTER_TNAME = #{ofcenterTname}
		</if>
		<if test="ofcenterEname != null and ofcenterEname != ''" >
			and OFCENTER_ENAME = #{ofcenterEname}
		</if>
		<if test="validInd != null and validInd != ''" >
			and VALID_IND = #{validInd}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="createCode != null and createCode != ''" >
			and CREATE_CODE = #{createCode}
		</if>
		<if test="modifiedTime != null and modifiedTime != ''" >
			and MODIFIED_TIME = #{modifiedTime}
		</if>
		<if test="modifiedCode != null and modifiedCode != ''" >
			and MODIFIED_CODE = #{modifiedCode}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GPPAYCOMCODEDEFINE
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GPPAYCOMCODEDEFINE
		where PAYMENT_COMCODE = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GPPAYCOMCODEDEFINE
		where PAYMENT_COMCODE in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.paycomcodedefine.po.Gppaycomcodedefine">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GPPAYCOMCODEDEFINE
		where PAYMENT_COMCODE = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GPPAYCOMCODEDEFINE
		where PAYMENT_COMCODE in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.paycomcodedefine.po.Gppaycomcodedefine">
		insert into GPPAYCOMCODEDEFINE (
			PAYMENT_COMCODE,
			DEPARTMENT_CODE,
			OFCENTER_CNAME,
			OFCENTER_TNAME,
			OFCENTER_ENAME,
			VALID_IND,
			REMARK,
			FLAG,
			CREATE_TIME,
			CREATE_CODE,
			MODIFIED_TIME,
			MODIFIED_CODE
		) values (
			#{paymentComcode},
			#{departmentCode},
			#{ofcenterCname},
			#{ofcenterTname},
			#{ofcenterEname},
			#{validInd},
			#{remark},
			#{flag},
			#{createTime},
			#{createCode},
			#{modifiedTime},
			#{modifiedCode}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.paycomcodedefine.po.Gppaycomcodedefine">
		insert into GPPAYCOMCODEDEFINE
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="paymentComcode != null" >
				PAYMENT_COMCODE,
			</if>
			<if test="departmentCode != null" >
				DEPARTMENT_CODE,
			</if>
			<if test="ofcenterCname != null" >
				OFCENTER_CNAME,
			</if>
			<if test="ofcenterTname != null" >
				OFCENTER_TNAME,
			</if>
			<if test="ofcenterEname != null" >
				OFCENTER_ENAME,
			</if>
			<if test="validInd != null" >
				VALID_IND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="createCode != null" >
				CREATE_CODE,
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME,
			</if>
			<if test="modifiedCode != null" >
				MODIFIED_CODE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="paymentComcode != null" >
				#{paymentComcode},
			</if>
			<if test="departmentCode != null" >
				#{departmentCode},
			</if>
			<if test="ofcenterCname != null" >
				#{ofcenterCname},
			</if>
			<if test="ofcenterTname != null" >
				#{ofcenterTname},
			</if>
			<if test="ofcenterEname != null" >
				#{ofcenterEname},
			</if>
			<if test="validInd != null" >
				#{validInd},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="createCode != null" >
				#{createCode},
			</if>
			<if test="modifiedTime != null" >
				#{modifiedTime},
			</if>
			<if test="modifiedCode != null" >
				#{modifiedCode}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.paycomcodedefine.po.Gppaycomcodedefine">
		update GPPAYCOMCODEDEFINE 
		<set>
			<if test="departmentCode != null" >
				DEPARTMENT_CODE=#{departmentCode},
			</if>
			<if test="ofcenterCname != null" >
				OFCENTER_CNAME=#{ofcenterCname},
			</if>
			<if test="ofcenterTname != null" >
				OFCENTER_TNAME=#{ofcenterTname},
			</if>
			<if test="ofcenterEname != null" >
				OFCENTER_ENAME=#{ofcenterEname},
			</if>
			<if test="validInd != null" >
				VALID_IND=#{validInd},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="createCode != null" >
				CREATE_CODE=#{createCode},
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME=#{modifiedTime},
			</if>
			<if test="modifiedCode != null" >
				MODIFIED_CODE=#{modifiedCode},
			</if>
		</set>
		where PAYMENT_COMCODE = #{paymentComcode }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.paycomcodedefine.po.Gppaycomcodedefine">
		update GPPAYCOMCODEDEFINE set
			DEPARTMENT_CODE=#{departmentCode},
			OFCENTER_CNAME=#{ofcenterCname},
			OFCENTER_TNAME=#{ofcenterTname},
			OFCENTER_ENAME=#{ofcenterEname},
			VALID_IND=#{validInd},
			REMARK=#{remark},
			FLAG=#{flag},
			CREATE_TIME=#{createTime},
			CREATE_CODE=#{createCode},
			MODIFIED_TIME=#{modifiedTime},
			MODIFIED_CODE=#{modifiedCode},
		where PAYMENT_COMCODE = #{paymentComcode}	</update>
</mapper>
