<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.paycomref.dao.GppaycomrefDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.paycomref.po.Gppaycomref">
		<id column="DEPARTMENT_CODE" property="departmentCode" />
		<id column="USER_TYPE" property="userType" />
		<result column="PAYMENT_COMCODE" property="paymentComcode" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		DEPARTMENT_CODE,
		USER_TYPE,
		PAYMENT_COMCODE
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="departmentCode != null and departmentCode != ''" >
			and DEPARTMENT_CODE = #{departmentCode}
		</if>
		<if test="userType != null and userType != ''" >
			and USER_TYPE = #{userType}
		</if>
		<if test="paymentComcode != null and paymentComcode != ''" >
			and PAYMENT_COMCODE = #{paymentComcode}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GPPAYCOMREF
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GPPAYCOMREF
		<trim prefix="WHERE" prefixOverrides="AND">
			AND DEPARTMENT_CODE = #{departmentCode} 
			AND USER_TYPE = #{userType} 
		</trim> 
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GPPAYCOMREF		
		where
		<foreach item="item" index="index" collection="list" open="(" separator="OR" close=")">
			(
			DEPARTMENT_CODE = #{item.departmentCode} 
			AND USER_TYPE = #{item.userType} 
			)
		</foreach> 
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.paycomref.po.Gppaycomref">
		<include refid="Base_Select_By_Entity" />
	</select>
	
	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GPPAYCOMREF 
		<trim prefix="WHERE" prefixOverrides="AND">
			AND DEPARTMENT_CODE = #{departmentCode} 
			AND USER_TYPE = #{userType} 
		</trim>
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GPPAYCOMREF
		where
		<foreach item="item" index="index" collection="list" open="(" separator="OR" close=")">
			(
			DEPARTMENT_CODE = #{item.departmentCode} 
			AND USER_TYPE = #{item.userType} 
			)
		</foreach> 
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.paycomref.po.Gppaycomref">
		insert into GPPAYCOMREF 
		<trim prefix="(" suffix=")" suffixOverrides="," >
			DEPARTMENT_CODE,
			USER_TYPE,
			PAYMENT_COMCODE
		</trim>
		values 
		<trim prefix="(" suffix=")" suffixOverrides="," > 
			#{departmentCode},
			#{userType},
			#{paymentComcode}
		</trim>
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.paycomref.po.Gppaycomref">
		insert into GPPAYCOMREF
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="departmentCode != null" >
				DEPARTMENT_CODE,
			</if>
			<if test="userType != null" >
				USER_TYPE,
			</if>
			<if test="paymentComcode != null" >
				PAYMENT_COMCODE
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="departmentCode != null" >
				#{departmentCode},
			</if>
			<if test="userType != null" >
				#{userType},
			</if>
			<if test="paymentComcode != null" >
				#{paymentComcode}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.paycomref.po.Gppaycomref">
		update GPPAYCOMREF 
		<set>			
			<if test="paymentComcode != null" >
				PAYMENT_COMCODE=#{paymentComcode},
			</if>
		</set>
		<trim prefix="WHERE" prefixOverrides="AND">
			AND DEPARTMENT_CODE = #{departmentCode} 
			AND USER_TYPE = #{userType} 
		</trim> 
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.paycomref.po.Gppaycomref">
		update GPPAYCOMREF 
		<set>
			PAYMENT_COMCODE=#{paymentComcode},
		</set>
		<trim prefix="WHERE" prefixOverrides="AND">
			AND DEPARTMENT_CODE = #{departmentCode} 
			AND USER_TYPE = #{userType} 
		</trim> 
	</update>
</mapper>
