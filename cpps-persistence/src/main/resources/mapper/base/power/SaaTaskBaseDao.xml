<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.power.dao.SaaTaskDao">
    <!-- 默认开启二级缓存,使用Least Recently Used（LRU，最近最少使用的）算法来收回 -->
    <!-- 	<cache/> -->
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="ins.channel.power.po.SaaTask">
        <id column="ID" property="id"/>
        <result column="SYSTEM" property="system"/>
        <result column="TASKCODE" property="taskcode"/>
        <result column="PARENTCODE" property="parentcode"/>
        <result column="TASKCNAME" property="taskcname"/>
        <result column="TASKENAME" property="taskename"/>
        <result column="URL" property="url"/>
        <result column="CREATORCODE" property="creatorcode"/>
        <result column="CREATETIME" property="createtime"/>
        <result column="VALIDFLAG" property="validflag"/>
        <result column="REMARK" property="remark"/>
        <result column="FLAG" property="flag"/>
        <result column="SYNFLAG" property="synflag"/>
        <result column="INSERTTIMEFORHIS" property="inserttimeforhis"/>
        <result column="OPERATETIMEFORHIS" property="operatetimeforhis"/>
        <result column="TASK_NUM" property="taskNum"/>
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="BaseColumnList">
		 ID,SYSTEM, TASKCODE, PARENTCODE, TASKCNAME, TASKENAME,
		 URL, CREATORCODE, CREATETIME, VALIDFLAG,REMARK, FLAG, SYNFLAG,INSERTTIMEFORHIS,OPERATETIMEFORHIS,TASK_NUM
	</sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="BaseSelectByEntityWhere">
        <if test="id != null">
            and ID = #{id}
        </if>
        <if test="system != null">
            and SYSTEM = #{system}
        </if>
        <if test="taskcode != null">
            and TASKCODE = #{taskcode}
        </if>
        <if test="parentcode != null">
            and PARENTCODE = #{parentcode}
        </if>
        <if test="taskcname != null">
            and TASKCNAME = #{taskcname}
        </if>
        <if test="taskename != null">
            and TASKENAME = #{taskename}
        </if>
        <if test="url != null">
            and URL = #{url}
        </if>
        <if test="creatorcode != null">
            and CREATORCODE = #{creatorcode}
        </if>
        <if test="createtime != null">
            and CREATETIME = #{createtime}
        </if>
        <if test="validflag != null">
            and VALIDFLAG = #{validflag}
        </if>
        <if test="remark != null">
            and REMARK = #{remark}
        </if>
        <if test="flag != null">
            and FLAG = #{flag}
        </if>
        <if test="synflag != null">
            and SYNFLAG = #{synflag}
        </if>
        <if test="inserttimeforhis != null">
            and INSERTTIMEFORHIS = #{inserttimeforhis}
        </if>
        <if test="operatetimeforhis != null">
            and OPERATETIMEFORHIS = #{operatetimeforhis}
        </if>
        <if test="taskNum != null and taskNum != ''">
            and TASK_NUM = #{taskNum}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="BaseSelectByEntity">
        select
        <include refid="BaseColumnList"/>
        from SAATASK
        <where>
            <include refid="BaseSelectByEntityWhere"/>
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAATASK
        where ID = #{param1}
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAATASK
        where ID in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.power.po.SaaTask">
        <include refid="BaseSelectByEntity"/>
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey" parameterType="map">
		delete from SAATASK
		where ID = #{param1}
	</delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys" parameterType="map">
        delete from SAATASK
        where ID in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" parameterType="ins.channel.power.po.SaaTask">
		insert into SAATASK (ID, SYSTEM,TASKCODE, PARENTCODE, TASKCNAME, TASKENAME,
			URL, CREATORCODE, CREATETIME, VALIDFLAG,
			REMARK, FLAG, SYNFLAG,TASK_NUM)
		values(#{id},#{system}, #{taskcode}, #{parentcode}, #{taskcname}, #{taskename},
			#{url}, #{creatorcode}, #{createtime}, #{validflag},
			#{remark}, #{flag}, #{synflag},#{taskNum})
	</insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" parameterType="ins.channel.power.po.SaaTask">
        insert into SAATASK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="system != null">
                SYSTEM,
            </if>
            <if test="taskcode != null">
                TASKCODE,
            </if>
            <if test="parentcode != null">
                PARENTCODE,
            </if>
            <if test="taskcname != null">
                TASKCNAME,
            </if>
            <if test="taskename != null">
                TASKENAME,
            </if>
            <if test="url != null">
                URL,
            </if>
            <if test="creatorcode != null">
                CREATORCODE,
            </if>
            <if test="createtime != null">
                CREATETIME,
            </if>
            <if test="validflag != null">
                VALIDFLAG,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="flag != null">
                FLAG,
            </if>
            <if test="synflag != null">
                SYNFLAG,
            </if>
            INSERTTIMEFORHIS,
            OPERATETIMEFORHIS,
            <if test="taskNum != null">
                TASK_NUM
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="system != null">
                #{system},
            </if>
            <if test="taskcode != null">
                #{taskcode},
            </if>
            <if test="parentcode != null">
                #{parentcode},
            </if>
            <if test="taskcname != null">
                #{taskcname},
            </if>
            <if test="taskename != null">
                #{taskename},
            </if>
            <if test="url != null">
                #{url},
            </if>
            <if test="creatorcode != null">
                #{creatorcode},
            </if>
            <if test="createtime != null">
                #{createtime},
            </if>
            <if test="validflag != null">
                #{validflag},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="flag != null">
                #{flag},
            </if>
            <if test="synflag != null">
                #{synflag},
            </if>
            <include refid="mybatis.common.Base_Current"/>,
            <include refid="mybatis.common.Base_Current"/>,
            <if test="taskNum != null">
                #{taskNum}
            </if>
        </trim>
    </insert>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" parameterType="ins.channel.power.po.SaaTask">
        update SAATASK
        <set>
            <if test="system != null">
                SYSTEM = #{systemCode}
            </if>
            <if test="taskcode != null">
                TASKCODE=#{taskcode},
            </if>
            <if test="parentcode != null">
                PARENTCODE=#{parentcode},
            </if>
            <if test="taskcname != null">
                TASKCNAME=#{taskcname},
            </if>
            <if test="taskename != null">
                TASKENAME=#{taskename},
            </if>
            <if test="url != null">
                URL=#{url},
            </if>
            <if test="creatorcode != null">
                CREATORCODE=#{creatorcode},
            </if>
            <if test="createtime != null">
                CREATETIME=#{createtime},
            </if>
            <if test="validflag != null">
                VALIDFLAG=#{validflag},
            </if>
            <if test="remark != null">
                REMARK=#{remark},
            </if>
            <if test="flag != null">
                FLAG=#{flag},
            </if>
            <if test="synflag != null">
                SYNFLAG=#{synflag},
            </if>
            OPERATETIMEFORHIS =<include refid="mybatis.common.Base_Current"/>,
            <if test="taskNum != null">
                TASK_NUM=#{taskNum},
            </if>
        </set>
        where ID = #{id}
    </update>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" parameterType="ins.channel.power.po.SaaTask">
        update SAATASK
        set
          SYSTEM=#{system},
          TASKCODE=#{taskcode},
          PARENTCODE=#{parentcode},
          TASKCNAME=#{taskcname},
          TASKENAME=#{taskename},
          URL=#{url},
          CREATORCODE=#{creatorcode},
          CREATETIME=#{createtime},
          VALIDFLAG=#{validflag},
          REMARK=#{remark},
          FLAG=#{flag},
          SYNFLAG=#{synflag},
          OPERATETIMEFORHIS =<include refid="mybatis.common.Base_Current"/>,
          TASK_NUM=#{taskNum},
        where ID = #{id}
    </update>

</mapper>
