<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.power.dao.SaaGradeDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.power.po.SaaGrade">
		<id column="ID" property="id" />
		<result column="GRADECNAME" property="gradecname" />
		<result column="GRADEENAME" property="gradeename" />
		<result column="COMCODE" property="comcode" />
		<result column="CREATORCODE" property="creatorcode" />
		<result column="UPDATERCODE" property="updatercode" />
		<result column="VALIDSTATUS" property="validstatus" />
		<result column="USERIND" property="userInd" />
		<result column="INSERTTIMEFORHIS" property="inserttimeforhis" />
		<result column="OPERATETIMEFORHIS" property="operatetimeforhis" />
		<result column="NUMFLAG" property="numflag" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		GRADECNAME,
		GRADEENAME,
		COMCODE,
		CREATORCODE,
		UPDATERCODE,
		VALIDSTATUS,
		USERIND,
		INSERTTIMEFORHIS,
		OPERATETIMEFORHIS,
		NUMFLAG
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="gradecname != null and gradecname != ''" >
			and GRADECNAME = #{gradecname}
		</if>
		<if test="gradeename != null and gradeename != ''" >
			and GRADEENAME = #{gradeename}
		</if>
		<if test="comcode != null and comcode != ''" >
			and COMCODE = #{comcode}
		</if>
		<if test="creatorcode != null and creatorcode != ''" >
			and CREATORCODE = #{creatorcode}
		</if>
		<if test="updatercode != null and updatercode != ''" >
			and UPDATERCODE = #{updatercode}
		</if>
		<if test="validstatus != null and validstatus != ''" >
			and VALIDSTATUS = #{validstatus}
		</if>
		<if test="userInd != null and userInd != ''" >
			and USERIND = #{userInd}
		</if>
		<if test="inserttimeforhis != null and inserttimeforhis != ''" >
			and INSERTTIMEFORHIS = #{inserttimeforhis}
		</if>
		<if test="operatetimeforhis != null and operatetimeforhis != ''" >
			and OPERATETIMEFORHIS = #{operatetimeforhis}
		</if>
		<if test="numflag != null and numflag != ''" >
			and NUMFLAG = #{numflag}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from SAAGRADE
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from SAAGRADE
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from SAAGRADE
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.power.po.SaaGrade">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from SAAGRADE
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from SAAGRADE
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.power.po.SaaGrade">
		insert into SAAGRADE (
			ID,
			GRADECNAME,
			GRADEENAME,
			COMCODE,
			CREATORCODE,
			UPDATERCODE,
			VALIDSTATUS,
			USERIND,
			OPERATETIMEFORHIS,
			NUMFLAG
		) values (
			#{id},
			#{gradecname},
			#{gradeename},
			#{comcode},
			#{creatorcode},
			#{updatercode},
			#{validstatus},
			#{userInd},
			<include refid="mybatis.common.Base_Current" />,
			#{numflag}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.power.po.SaaGrade">
		insert into SAAGRADE
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="gradecname != null" >
				GRADECNAME,
			</if>
			<if test="gradeename != null" >
				GRADEENAME,
			</if>
			<if test="comcode != null" >
				COMCODE,
			</if>
			<if test="creatorcode != null" >
				CREATORCODE,
			</if>
			<if test="updatercode != null" >
				UPDATERCODE,
			</if>
			<if test="validstatus != null" >
				VALIDSTATUS,
			</if>
			<if test="userInd != null" >
				USERIND,
			</if>
			INSERTTIMEFORHIS,
			OPERATETIMEFORHIS,
			<if test="numflag != null" >
				NUMFLAG
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="gradecname != null" >
				#{gradecname},
			</if>
			<if test="gradeename != null" >
				#{gradeename},
			</if>
			<if test="comcode != null" >
				#{comcode},
			</if>
			<if test="creatorcode != null" >
				#{creatorcode},
			</if>
			<if test="updatercode != null" >
				#{updatercode},
			</if>
			<if test="validstatus != null" >
				#{validstatus},
			</if>
			<if test="userInd != null" >
				#{userInd},
			</if>
		    <include refid="mybatis.common.Base_Current" />,
			<include refid="mybatis.common.Base_Current" />,
			<if test="numflag != null" >
				#{numflag}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.power.po.SaaGrade">
		update SAAGRADE 
		<set>
			<if test="gradecname != null" >
				GRADECNAME=#{gradecname},
			</if>
			<if test="gradeename != null" >
				GRADEENAME=#{gradeename},
			</if>
			<if test="comcode != null" >
				COMCODE=#{comcode},
			</if>
			<if test="creatorcode != null" >
				CREATORCODE=#{creatorcode},
			</if>
			<if test="updatercode != null" >
				UPDATERCODE=#{updatercode},
			</if>
			<if test="validstatus != null" >
				VALIDSTATUS=#{validstatus},
			</if>
			<if test="userInd != null" >
				USERIND=#{userInd},
			</if>
			OPERATETIMEFORHIS = <include refid="mybatis.common.Base_Current" />,
			<if test="numflag != null" >
				NUMFLAG=#{numflag},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.power.po.SaaGrade">
		update SAAGRADE set
			GRADECNAME=#{gradecname},
			GRADEENAME=#{gradeename},
			COMCODE=#{comcode},
			CREATORCODE=#{creatorcode},
			UPDATERCODE=#{updatercode},
			VALIDSTATUS=#{validstatus},
			USERIND=#{userInd},
			OPERATETIMEFORHIS = <include refid="mybatis.common.Base_Current" />,
			NUMFLAG=#{numflag},
		where ID = #{id}	</update>
</mapper>
