<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.power.dao.SaagradetaskDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.power.po.Saagradetask">
		<id column="ID" property="id" />
		<result column="GRADEID" property="gradeid" />
		<result column="TASKID" property="taskid" />
		<result column="CREATORCODE" property="creatorcode" />
		<result column="UPDATERCODE" property="updaterCode" />
		<result column="VALIDSTATUS" property="validStatus" />
		<result column="INSERTTIMEFORHIS" property="inserttimeforhis" />
		<result column="OPERATETIMEFORHIS" property="operatetimeforhis" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		GRADEID,
		TASKID,
		CREATORCODE,
		UPDATERCODE,
		VALIDSTATUS,
		INSERTTIMEFORHIS,
		OPERATETIMEFORHIS
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="gradeid != null and gradeid != ''" >
			and GRADEID = #{gradeid}
		</if>
		<if test="taskid != null and taskid != ''" >
			and TASKID = #{taskid}
		</if>
		<if test="creatorcode != null and creatorcode != ''" >
			and CREATORCODE = #{creatorcode}
		</if>
		<if test="updaterCode != null and updaterCode != ''" >
			and UPDATERCODE = #{updaterCode}
		</if>
		<if test="validStatus != null and validStatus != ''" >
			and VALIDSTATUS = #{validStatus}
		</if>
		<if test="inserttimeforhis != null and inserttimeforhis != ''" >
			and INSERTTIMEFORHIS = #{inserttimeforhis}
		</if>
		<if test="operatetimeforhis != null and operatetimeforhis != ''" >
			and OPERATETIMEFORHIS = #{operatetimeforhis}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from SAAGRADETASK
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from SAAGRADETASK
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from SAAGRADETASK
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.power.po.Saagradetask">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from SAAGRADETASK
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from SAAGRADETASK
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.power.po.Saagradetask">
		insert into SAAGRADETASK (
			ID,
			GRADEID,
			TASKID,
			CREATORCODE,
			UPDATERCODE,
			VALIDSTATUS,
			OPERATETIMEFORHIS
		) values (
			#{id},
			#{gradeid},
			#{taskid},
			#{creatorcode},
			#{updaterCode},
			#{validStatus},
			<include refid="mybatis.common.Base_Current" />
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.power.po.Saagradetask">
		insert into SAAGRADETASK
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="gradeid != null" >
				GRADEID,
			</if>
			<if test="taskid != null" >
				TASKID,
			</if>
			<if test="creatorcode != null" >
				CREATORCODE,
			</if>
			<if test="updaterCode != null" >
				UPDATERCODE,
			</if>
			<if test="validStatus != null" >
				VALIDSTATUS,
			</if>
			OPERATETIMEFORHIS
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="gradeid != null" >
				#{gradeid},
			</if>
			<if test="taskid != null" >
				#{taskid},
			</if>
			<if test="creatorcode != null" >
				#{creatorcode},
			</if>
			<if test="updaterCode != null" >
				#{updaterCode},
			</if>
			<if test="validStatus != null" >
				#{validStatus},
			</if>
			<include refid="mybatis.common.Base_Current" />
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.power.po.Saagradetask">
		update SAAGRADETASK 
		<set>
			<if test="gradeid != null" >
				GRADEID=#{gradeid},
			</if>
			<if test="taskid != null" >
				TASKID=#{taskid},
			</if>
			<if test="creatorcode != null" >
				CREATORCODE=#{creatorcode},
			</if>
			<if test="updaterCode != null" >
				UPDATERCODE=#{updaterCode},
			</if>
			<if test="validStatus != null" >
				VALIDSTATUS=#{validStatus},
			</if>
			OPERATETIMEFORHIS = <include refid="mybatis.common.Base_Current" />,
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.power.po.Saagradetask">
		update SAAGRADETASK set
			GRADEID=#{gradeid},
			TASKID=#{taskid},
			CREATORCODE=#{creatorcode},
			UPDATERCODE=#{updaterCode},
			VALIDSTATUS=#{validStatus},
			OPERATETIMEFORHIS = <include refid="mybatis.common.Base_Current" />,
		where ID = #{id}	</update>
</mapper>
