<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.power.dao.SaauserpaymentcompanyDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.power.po.Saauserpaymentcompany">
		<id column="ID" property="id" />
		<result column="USER_CODE" property="userCode" />
		<result column="USER_CNAME" property="userCname" />
		<result column="COM_CODE" property="comCode" />
		<result column="PAYMENT_COMCODE" property="paymentComcode" />
		<result column="PAYMENT_COMPANYNAME" property="paymentCompanyname" />
		<result column="CREATE_CODE" property="createCode" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="MODIFIED_TIME" property="modifiedTime" />
		<result column="DEFAULT_IND" property="defaultInd" />
		<result column="VALID_IND" property="validInd" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		USER_CODE,
		USER_CNAME,
		COM_CODE,
		PAYMENT_COMCODE,
		PAYMENT_COMPANYNAME,
		CREATE_CODE,
		CREATE_TIME,
		MODIFIED_TIME,
		DEFAULT_IND,
		VALID_IND
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="userCode != null and userCode != ''" >
			and USER_CODE = #{userCode}
		</if>
		<if test="userCname != null and userCname != ''" >
			and USER_CNAME = #{userCname}
		</if>
		<if test="comCode != null and comCode != ''" >
			and COM_CODE = #{comCode}
		</if>
		<if test="paymentComcode != null and paymentComcode != ''" >
			and PAYMENT_COMCODE = #{paymentComcode}
		</if>
		<if test="paymentCompanyname != null and paymentCompanyname != ''" >
			and PAYMENT_COMPANYNAME = #{paymentCompanyname}
		</if>
		<if test="createCode != null and createCode != ''" >
			and CREATE_CODE = #{createCode}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="modifiedTime != null and modifiedTime != ''" >
			and MODIFIED_TIME = #{modifiedTime}
		</if>
		<if test="defaultInd != null and defaultInd != ''" >
			and DEFAULT_IND = #{defaultInd}
		</if>
		<if test="validInd != null and validInd != ''" >
			and VALID_IND = #{validInd}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from SAAUSERPAYMENTCOMPANY
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from SAAUSERPAYMENTCOMPANY
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from SAAUSERPAYMENTCOMPANY
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.power.po.Saauserpaymentcompany">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from SAAUSERPAYMENTCOMPANY
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from SAAUSERPAYMENTCOMPANY
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.power.po.Saauserpaymentcompany">
		insert into SAAUSERPAYMENTCOMPANY (
			ID,
			USER_CODE,
			USER_CNAME,
			COM_CODE,
			PAYMENT_COMCODE,
			PAYMENT_COMPANYNAME,
			CREATE_CODE,
			CREATE_TIME,
			MODIFIED_TIME,
			DEFAULT_IND,
			VALID_IND
		) values (
			#{id},
			#{userCode},
			#{userCname},
			#{comCode},
			#{paymentComcode},
			#{paymentCompanyname},
			#{createCode},
			#{createTime},
			#{modifiedTime},
			#{defaultInd},
			#{validInd}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.power.po.Saauserpaymentcompany">
		insert into SAAUSERPAYMENTCOMPANY
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="userCode != null" >
				USER_CODE,
			</if>
			<if test="userCname != null" >
				USER_CNAME,
			</if>
			<if test="comCode != null" >
				COM_CODE,
			</if>
			<if test="paymentComcode != null" >
				PAYMENT_COMCODE,
			</if>
			<if test="paymentCompanyname != null" >
				PAYMENT_COMPANYNAME,
			</if>
			<if test="createCode != null" >
				CREATE_CODE,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME,
			</if>
			<if test="defaultInd != null" >
				DEFAULT_IND,
			</if>
			<if test="validInd != null" >
				VALID_IND
			</if>						
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="userCode != null" >
				#{userCode},
			</if>
			<if test="userCname != null" >
				#{userCname},
			</if>
			<if test="comCode != null" >
				#{comCode},
			</if>
			<if test="paymentComcode != null" >
				#{paymentComcode},
			</if>
			<if test="paymentCompanyname != null" >
				#{paymentCompanyname},
			</if>
			<if test="createCode != null" >
				#{createCode},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="modifiedTime != null" >
				#{modifiedTime},
			</if>
			<if test="defaultInd != null" >
				#{defaultInd},
			</if>
			<if test="validInd != null" >
				#{validInd},
			</if>						
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.power.po.Saauserpaymentcompany">
		update SAAUSERPAYMENTCOMPANY 
		<set>
			<if test="userCode != null" >
				USER_CODE=#{userCode},
			</if>
			<if test="userCname != null" >
				USER_CNAME=#{userCname},
			</if>
			<if test="comCode != null" >
				COM_CODE=#{comCode},
			</if>
			<if test="paymentComcode != null" >
				PAYMENT_COMCODE=#{paymentComcode},
			</if>
			<if test="paymentCompanyname != null" >
				PAYMENT_COMPANYNAME=#{paymentCompanyname},
			</if>
			<if test="createCode != null" >
				CREATE_CODE=#{createCode},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME=#{modifiedTime},
			</if>
			<if test="defaultInd != null" >
				DEFAULT_IND=#{defaultInd},
			</if>
			<if test="validInd != null" >
				VALID_IND=#{validInd}
			</if>						
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.power.po.Saauserpaymentcompany">
		update SAAUSERPAYMENTCOMPANY set
			USER_CODE=#{userCode},
			USER_CNAME=#{userCname},
			COM_CODE=#{comCode},
			PAYMENT_COMCODE=#{paymentComcode},
			PAYMENT_COMPANYNAME=#{paymentCompanyname},
			CREATE_CODE=#{createCode},
			CREATE_TIME=#{createTime},
			MODIFIED_TIME=#{modifiedTime},
			DEFAULT_IND=#{defaultInd},
			VALID_IND=#{validInd}						
		where ID = #{id}	</update>
</mapper>
