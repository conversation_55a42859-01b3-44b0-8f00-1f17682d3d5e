<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.power.dao.SaauserpermitdataDao">
    <!-- 默认开启二级缓存,使用两级缓存来处理 -->
    <cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="ins.channel.power.po.Saauserpermitdata">
        <id column="ID" property="id"/>
        <result column="SYSTEMCODE" property="systemCode"/>
        <result column="USERCODE" property="userCode"/>
        <result column="COMCODE" property="comCode"/>
        <result column="USERGRADEID" property="usergradeid"/>
        <result column="FACTORGROUP" property="factorgroup"/>
        <result column="FACTORFIELDID" property="factorfieldid"/>
        <result column="FACTORCODE" property="factorCode"/>
        <result column="DATAOPER" property="dataoper"/>
        <result column="DATAVALUE1" property="datavalue1"/>
        <result column="DATAVALUE2" property="datavalue2"/>
        <result column="CREATORCODE" property="creatorcode"/>
        <result column="CREATETIME" property="createTime"/>
        <result column="UPDATECODE" property="updateCode"/>
        <result column="UPDATETIME" property="updatetime"/>
        <result column="VALIDFLAG" property="validflag"/>
        <result column="REMARK" property="remark"/>
        <result column="FLAG" property="flag"/>
        <result column="SYNFLAG" property="synflag"/>
        <result column="INSERTTIMEFORHIS" property="inserttimeforhis"/>
        <result column="OPERATETIMEFORHIS" property="operatetimeforhis"/>
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
		ID,
		SYSTEMCODE,
		USERCODE,
		COMCODE,
		USERGRADEID,
		FACTORGROUP,
		FACTORFIELDID,
		FACTORCODE,
		DATAOPER,
		DATAVALUE1,
		DATAVALUE2,
		CREATORCODE,
		CREATETIME,
		UPDATECODE,
		UPDATETIME,
		VALIDFLAG,
		REMARK,
		FLAG,
		SYNFLAG,
		INSERTTIMEFORHIS,
		OPERATETIMEFORHIS
	</sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="id != null and id != ''">
            and ID = #{id}
        </if>
        <if test="systemCode != null and systemCode != ''">
            and SYSTEMCODE = #{systemCode}
        </if>
        <if test="userCode != null and userCode != ''">
            and USERCODE = #{userCode}
        </if>
        <if test="comCode != null and comCode != ''">
            and COMCODE = #{comCode}
        </if>
        <if test="usergradeid != null and usergradeid != ''">
            and USERGRADEID = #{usergradeid}
        </if>
        <if test="factorgroup != null and factorgroup != ''">
            and FACTORGROUP = #{factorgroup}
        </if>
        <if test="factorfieldid != null and factorfieldid != ''">
            and FACTORFIELDID = #{factorfieldid}
        </if>
        <if test="factorCode != null and factorCode != ''">
            and FACTORCODE = #{factorCode}
        </if>
        <if test="dataoper != null and dataoper != ''">
            and DATAOPER = #{dataoper}
        </if>
        <if test="datavalue1 != null and datavalue1 != ''">
            and DATAVALUE1 = #{datavalue1}
        </if>
        <if test="datavalue2 != null and datavalue2 != ''">
            and DATAVALUE2 = #{datavalue2}
        </if>
        <if test="creatorcode != null and creatorcode != ''">
            and CREATORCODE = #{creatorcode}
        </if>
        <if test="createTime != null and createTime != ''">
            and CREATETIME = #{createTime}
        </if>
        <if test="updateCode != null and updateCode != ''">
            and UPDATECODE = #{updateCode}
        </if>
        <if test="updatetime != null and updatetime != ''">
            and UPDATETIME = #{updatetime}
        </if>
        <if test="validflag != null and validflag != ''">
            and VALIDFLAG = #{validflag}
        </if>
        <if test="remark != null and remark != ''">
            and REMARK = #{remark}
        </if>
        <if test="flag != null and flag != ''">
            and FLAG = #{flag}
        </if>
        <if test="synflag != null and synflag != ''">
            and SYNFLAG = #{synflag}
        </if>
        <if test="inserttimeforhis != null and inserttimeforhis != ''">
            and INSERTTIMEFORHIS = #{inserttimeforhis}
        </if>
        <if test="operatetimeforhis != null and operatetimeforhis != ''">
            and OPERATETIMEFORHIS = #{operatetimeforhis}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
        <include refid="Base_Column_List"/>
        from SAAUSERPERMITDATA
        <where>
            <include refid="Base_Select_By_Entity_Where"/>
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="Base_Column_List"/>
        from SAAUSERPERMITDATA
        where ID = #{param1}
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="Base_Column_List"/>
        from SAAUSERPERMITDATA
        where ID in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.power.po.Saauserpermitdata">
        <include refid="Base_Select_By_Entity"/>
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey" parameterType="map">
		delete from SAAUSERPERMITDATA
		where ID = #{param1}
	</delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys" parameterType="map">
        delete from SAAUSERPERMITDATA
        where ID in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" parameterType="ins.channel.power.po.Saauserpermitdata">
        insert into SAAUSERPERMITDATA (
        ID,
        SYSTEMCODE,
        USERCODE,
        COMCODE,
        USERGRADEID,
        FACTORGROUP,
        FACTORFIELDID,
        FACTORCODE,
        DATAOPER,
        DATAVALUE1,
        DATAVALUE2,
        CREATORCODE,
        CREATETIME,
        UPDATECODE,
        UPDATETIME,
        VALIDFLAG,
        REMARK,
        FLAG,
        SYNFLAG,
        OPERATETIMEFORHIS
        ) values (
        #{id},
        #{systemCode},
        #{userCode},
        #{comCode},
        #{usergradeid},
        #{factorgroup},
        #{factorfieldid},
        #{factorCode},
        #{dataoper},
        #{datavalue1},
        #{datavalue2},
        #{creatorcode},
        #{createTime},
        #{updateCode},
        #{updatetime},
        #{validflag},
        #{remark},
        #{flag},
        #{synflag},
        <include refid="mybatis.common.Base_Current"/>
        )
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" parameterType="ins.channel.power.po.Saauserpermitdata">
        insert into SAAUSERPERMITDATA
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="systemCode != null">
                SYSTEMCODE,
            </if>
            <if test="userCode != null">
                USERCODE,
            </if>
            <if test="comCode != null">
                COMCODE,
            </if>
            <if test="usergradeid != null">
                USERGRADEID,
            </if>
            <if test="factorgroup != null">
                FACTORGROUP,
            </if>
            <if test="factorfieldid != null">
                FACTORFIELDID,
            </if>
            <if test="factorCode != null">
                FACTORCODE,
            </if>
            <if test="dataoper != null">
                DATAOPER,
            </if>
            <if test="datavalue1 != null">
                DATAVALUE1,
            </if>
            <if test="datavalue2 != null">
                DATAVALUE2,
            </if>
            <if test="creatorcode != null">
                CREATORCODE,
            </if>
            <if test="createTime != null">
                CREATETIME,
            </if>
            <if test="updateCode != null">
                UPDATECODE,
            </if>
            <if test="updatetime != null">
                UPDATETIME,
            </if>
            <if test="validflag != null">
                VALIDFLAG,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="flag != null">
                FLAG,
            </if>
            <if test="synflag != null">
                SYNFLAG,
            </if>
            INSERTTIMEFORHIS,
            OPERATETIMEFORHIS
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="systemCode != null">
                #{systemCode},
            </if>
            <if test="userCode != null">
                #{userCode},
            </if>
            <if test="comCode != null">
                #{comCode},
            </if>
            <if test="usergradeid != null">
                #{usergradeid},
            </if>
            <if test="factorgroup != null">
                #{factorgroup},
            </if>
            <if test="factorfieldid != null">
                #{factorfieldid},
            </if>
            <if test="factorCode != null">
                #{factorCode},
            </if>
            <if test="dataoper != null">
                #{dataoper},
            </if>
            <if test="datavalue1 != null">
                #{datavalue1},
            </if>
            <if test="datavalue2 != null">
                #{datavalue2},
            </if>
            <if test="creatorcode != null">
                #{creatorcode},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateCode != null">
                #{updateCode},
            </if>
            <if test="updatetime != null">
                #{updatetime},
            </if>
            <if test="validflag != null">
                #{validflag},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="flag != null">
                #{flag},
            </if>
            <if test="synflag != null">
                #{synflag},
            </if>
            <include refid="mybatis.common.Base_Current"/>,
            <include refid="mybatis.common.Base_Current"/>
        </trim>
    </insert>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" parameterType="ins.channel.power.po.Saauserpermitdata">
        update SAAUSERPERMITDATA
        <set>
            <if test="systemCode != null">
                SYSTEMCODE=#{systemCode},
            </if>
            <if test="userCode != null">
                USERCODE=#{userCode},
            </if>
            <if test="comCode != null">
                COMCODE=#{comCode},
            </if>
            <if test="usergradeid != null">
                USERGRADEID=#{usergradeid},
            </if>
            <if test="factorgroup != null">
                FACTORGROUP=#{factorgroup},
            </if>
            <if test="factorfieldid != null">
                FACTORFIELDID=#{factorfieldid},
            </if>
            <if test="factorCode != null">
                FACTORCODE=#{factorCode},
            </if>
            <if test="dataoper != null">
                DATAOPER=#{dataoper},
            </if>
            <if test="datavalue1 != null">
                DATAVALUE1=#{datavalue1},
            </if>
            <if test="datavalue2 != null">
                DATAVALUE2=#{datavalue2},
            </if>
            <if test="creatorcode != null">
                CREATORCODE=#{creatorcode},
            </if>
            <if test="createTime != null">
                CREATETIME=#{createTime},
            </if>
            <if test="updateCode != null">
                UPDATECODE=#{updateCode},
            </if>
            <if test="updatetime != null">
                UPDATETIME=#{updatetime},
            </if>
            <if test="validflag != null">
                VALIDFLAG=#{validflag},
            </if>
            <if test="remark != null">
                REMARK=#{remark},
            </if>
            <if test="flag != null">
                FLAG=#{flag},
            </if>
            <if test="synflag != null">
                SYNFLAG=#{synflag},
            </if>
            OPERATETIMEFORHIS =<include refid="mybatis.common.Base_Current"/>,
        </set>
        where ID = #{id }
    </update>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" parameterType="ins.channel.power.po.Saauserpermitdata">
        update SAAUSERPERMITDATA set
        SYSTEMCODE=#{systemCode},
        USERCODE=#{userCode},
        COMCODE=#{comCode},
        USERGRADEID=#{usergradeid},
        FACTORGROUP=#{factorgroup},
        FACTORFIELDID=#{factorfieldid},
        FACTORCODE=#{factorCode},
        DATAOPER=#{dataoper},
        DATAVALUE1=#{datavalue1},
        DATAVALUE2=#{datavalue2},
        CREATORCODE=#{creatorcode},
        CREATETIME=#{createTime},
        UPDATECODE=#{updateCode},
        UPDATETIME=#{updatetime},
        VALIDFLAG=#{validflag},
        REMARK=#{remark},
        FLAG=#{flag},
        SYNFLAG=#{synflag},
        OPERATETIMEFORHIS =<include refid="mybatis.common.Base_Current"/>,
        where ID = #{id}
    </update>
</mapper>
