<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.risk.dao.GgriskDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.risk.po.Ggrisk">
		<id column="RISK_CODE" property="riskCode" />
		<result column="RISK_CNAME" property="riskCname" />
		<result column="RISK_TNAME" property="riskTname" />
		<result column="RISK_ENAME" property="riskEname" />
		<result column="RISK_CLASS" property="riskClass" />
		<result column="OPENCOVER_IND" property="opencoverInd" />
		<result column="VALIDDATE" property="validDate" />
		<result column="INVALIDDATE" property="invalidDate" />
		<result column="VALID_IND" property="validInd" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="MODIFIED_TIME" property="modifiedTime" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		RISK_CODE,
		RISK_CNAME,
		RISK_TNAME,
		RISK_ENAME,
		RISK_CLASS,
		OPENCOVER_IND,
		VALIDDATE,
		INVALIDDATE,
		VALID_IND,
		REMARK,
		FLAG,
		CREATE_TIME,
		MODIFIED_TIME
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="riskCode != null and riskCode != ''" >
			and RISK_CODE = #{riskCode}
		</if>
		<if test="riskCname != null and riskCname != ''" >
			and RISK_CNAME = #{riskCname}
		</if>
		<if test="riskTname != null and riskTname != ''" >
			and RISK_TNAME = #{riskTname}
		</if>
		<if test="riskEname != null and riskEname != ''" >
			and RISK_ENAME = #{riskEname}
		</if>
		<if test="riskClass != null and riskClass != ''" >
			and RISK_CLASS = #{riskClass}
		</if>
		<if test="opencoverInd != null and opencoverInd != ''" >
			and OPENCOVER_IND = #{opencoverInd}
		</if>
		<if test="validDate != null and validDate != ''" >
			and VALIDDATE = #{validDate}
		</if>
		<if test="invalidDate != null and invalidDate != ''" >
			and INVALIDDATE = #{invalidDate}
		</if>
		<if test="validInd != null and validInd != ''" >
			and VALID_IND = #{validInd}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="modifiedTime != null and modifiedTime != ''" >
			and MODIFIED_TIME = #{modifiedTime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GGRISK
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGRISK
		where RISK_CODE = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGRISK
		where RISK_CODE in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.risk.po.Ggrisk">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GGRISK
		where RISK_CODE = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GGRISK
		where RISK_CODE in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.risk.po.Ggrisk">
		insert into GGRISK (
			RISK_CODE,
			RISK_CNAME,
			RISK_TNAME,
			RISK_ENAME,
			RISK_CLASS,
			OPENCOVER_IND,
			VALIDDATE,
			INVALIDDATE,
			VALID_IND,
			REMARK,
			FLAG,
			CREATE_TIME,
			MODIFIED_TIME
		) values (
			#{riskCode},
			#{riskCname},
			#{riskTname},
			#{riskEname},
			#{riskClass},
			#{opencoverInd},
			#{validDate},
			#{invalidDate},
			#{validInd},
			#{remark},
			#{flag},
			#{createTime},
			#{modifiedTime}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.risk.po.Ggrisk">
		insert into GGRISK
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="riskCode != null" >
				RISK_CODE,
			</if>
			<if test="riskCname != null" >
				RISK_CNAME,
			</if>
			<if test="riskTname != null" >
				RISK_TNAME,
			</if>
			<if test="riskEname != null" >
				RISK_ENAME,
			</if>
			<if test="riskClass != null" >
				RISK_CLASS,
			</if>
			<if test="opencoverInd != null" >
				OPENCOVER_IND,
			</if>
			<if test="validDate != null" >
				VALIDDATE,
			</if>
			<if test="invalidDate != null" >
				INVALIDDATE,
			</if>
			<if test="validInd != null" >
				VALID_IND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="riskCode != null" >
				#{riskCode},
			</if>
			<if test="riskCname != null" >
				#{riskCname},
			</if>
			<if test="riskTname != null" >
				#{riskTname},
			</if>
			<if test="riskEname != null" >
				#{riskEname},
			</if>
			<if test="riskClass != null" >
				#{riskClass},
			</if>
			<if test="opencoverInd != null" >
				#{opencoverInd},
			</if>
			<if test="validDate != null" >
				#{validDate},
			</if>
			<if test="invalidDate != null" >
				#{invalidDate},
			</if>
			<if test="validInd != null" >
				#{validInd},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="modifiedTime != null" >
				#{modifiedTime}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.risk.po.Ggrisk">
		update GGRISK 
		<set>
			<if test="riskCname != null" >
				RISK_CNAME=#{riskCname},
			</if>
			<if test="riskTname != null" >
				RISK_TNAME=#{riskTname},
			</if>
			<if test="riskEname != null" >
				RISK_ENAME=#{riskEname},
			</if>
			<if test="riskClass != null" >
				RISK_CLASS=#{riskClass},
			</if>
			<if test="opencoverInd != null" >
				OPENCOVER_IND=#{opencoverInd},
			</if>
			<if test="validDate != null" >
				VALIDDATE=#{validDate},
			</if>
			<if test="invalidDate != null" >
				INVALIDDATE=#{invalidDate},
			</if>
			<if test="validInd != null" >
				VALID_IND=#{validInd},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME=#{modifiedTime},
			</if>
		</set>
		where RISK_CODE = #{riskCode }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.risk.po.Ggrisk">
		update GGRISK set
			RISK_CNAME=#{riskCname},
			RISK_TNAME=#{riskTname},
			RISK_ENAME=#{riskEname},
			RISK_CLASS=#{riskClass},
			OPENCOVER_IND=#{opencoverInd},
			VALIDDATE=#{validDate},
			INVALIDDATE=#{invalidDate},
			VALID_IND=#{validInd},
			REMARK=#{remark},
			FLAG=#{flag},
			CREATE_TIME=#{createTime},
			MODIFIED_TIME=#{modifiedTime},
		where RISK_CODE = #{riskCode}	</update>
</mapper>
