<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.systeminteractivelog.dao.GpsysteminteractivelogDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.systeminteractivelog.po.Gpsysteminteractivelog">
		<id column="REQUESTID" property="requestid" />
		<result column="MODELNAME" property="modelName" />
		<result column="REQUESTMESSAGE" property="requestmessage" />
		<result column="RESPONSEMESSAGE" property="responsemessage" />
		<result column="THIRDINTERMESSAGE" property="thirdintermessage" />
		<result column="RESPONSESTATUS" property="responsestatus" />
		<result column="INSERTTIME" property="inserttime" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		REQUESTID,
		MODELNAME,
		REQUESTMESSAGE,
		RESPONSEMESSAGE,
		THIRDINTERMESSAGE,
		RESPONSESTATUS,
		INSERTTIME
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="requestid != null and requestid != ''" >
			and REQUESTID = #{requestid}
		</if>
		<if test="modelName != null and modelName != ''" >
			and MODELNAME = #{modelName}
		</if>
		<if test="requestmessage != null and requestmessage != ''" >
			and REQUESTMESSAGE = #{requestmessage}
		</if>
		<if test="responsemessage != null and responsemessage != ''" >
			and RESPONSEMESSAGE = #{responsemessage}
		</if>
		<if test="thirdintermessage != null and thirdintermessage != ''" >
			and THIRDINTERMESSAGE = #{thirdintermessage}
		</if>
		<if test="responsestatus != null and responsestatus != ''" >
			and RESPONSESTATUS = #{responsestatus}
		</if>
		<if test="inserttime != null and inserttime != ''" >
			and INSERTTIME = #{inserttime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GPSYSTEMINTERACTIVELOG
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GPSYSTEMINTERACTIVELOG
		where REQUESTID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GPSYSTEMINTERACTIVELOG
		where REQUESTID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.systeminteractivelog.po.Gpsysteminteractivelog">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GPSYSTEMINTERACTIVELOG
		where REQUESTID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GPSYSTEMINTERACTIVELOG
		where REQUESTID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.systeminteractivelog.po.Gpsysteminteractivelog">
		insert into GPSYSTEMINTERACTIVELOG (
			REQUESTID,
			MODELNAME,
			REQUESTMESSAGE,
			RESPONSEMESSAGE,
			THIRDINTERMESSAGE,
			RESPONSESTATUS,
			INSERTTIME
		) values (
			#{requestid},
			#{modelName},
			#{requestmessage},
			#{responsemessage},
			#{thirdintermessage},
			#{responsestatus},
			#{inserttime}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.systeminteractivelog.po.Gpsysteminteractivelog">
		insert into GPSYSTEMINTERACTIVELOG
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="requestid != null" >
				REQUESTID,
			</if>
			<if test="modelName != null" >
				MODELNAME,
			</if>
			<if test="requestmessage != null" >
				REQUESTMESSAGE,
			</if>
			<if test="responsemessage != null" >
				RESPONSEMESSAGE,
			</if>
			<if test="thirdintermessage != null" >
				THIRDINTERMESSAGE,
			</if>
			<if test="responsestatus != null" >
				RESPONSESTATUS,
			</if>
			<if test="inserttime != null" >
				INSERTTIME
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="requestid != null" >
				#{requestid},
			</if>
			<if test="modelName != null" >
				#{modelName},
			</if>
			<if test="requestmessage != null" >
				#{requestmessage},
			</if>
			<if test="responsemessage != null" >
				#{responsemessage},
			</if>
			<if test="thirdintermessage != null" >
				#{thirdintermessage},
			</if>
			<if test="responsestatus != null" >
				#{responsestatus},
			</if>
			<if test="inserttime != null" >
				#{inserttime}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.systeminteractivelog.po.Gpsysteminteractivelog">
		update GPSYSTEMINTERACTIVELOG 
		<set>
			<if test="modelName != null" >
				MODELNAME=#{modelName},
			</if>
			<if test="requestmessage != null" >
				REQUESTMESSAGE=#{requestmessage},
			</if>
			<if test="responsemessage != null" >
				RESPONSEMESSAGE=#{responsemessage},
			</if>
			<if test="thirdintermessage != null" >
				THIRDINTERMESSAGE=#{thirdintermessage},
			</if>
			<if test="responsestatus != null" >
				RESPONSESTATUS=#{responsestatus},
			</if>
			<if test="inserttime != null" >
				INSERTTIME=#{inserttime},
			</if>
		</set>
		where REQUESTID = #{requestid }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.systeminteractivelog.po.Gpsysteminteractivelog">
		update GPSYSTEMINTERACTIVELOG set
			MODELNAME=#{modelName},
			REQUESTMESSAGE=#{requestmessage},
			RESPONSEMESSAGE=#{responsemessage},
			THIRDINTERMESSAGE=#{thirdintermessage},
			RESPONSESTATUS=#{responsestatus},
			INSERTTIME=#{inserttime},
		where REQUESTID = #{requestid}	</update>
</mapper>
