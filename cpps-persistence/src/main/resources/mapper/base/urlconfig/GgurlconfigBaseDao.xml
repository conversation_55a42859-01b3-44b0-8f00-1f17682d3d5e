<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.urlconfig.dao.GgurlconfigDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.urlconfig.po.Ggurlconfig">
		<id column="ID" property="id" />
		<result column="SYSTEM_CODE" property="systemCode" />
		<result column="CODE_TYPE" property="codeType" />
		<result column="URL" property="url" />
		<result column="VALID_IND" property="validInd" />
		<result column="FLAG" property="flag" />
		<result column="REMARK" property="remark" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="MODIFIED_TIME" property="modifiedTime" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		ID,
		SYSTEM_CODE,
		CODE_TYPE,
		URL,
		VALID_IND,
		FLAG,
		REMARK,
		CREATE_TIME,
		MODIFIED_TIME
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and ID = #{id}
		</if>
		<if test="systemCode != null and systemCode != ''" >
			and SYSTEM_CODE = #{systemCode}
		</if>
		<if test="codeType != null and codeType != ''" >
			and CODE_TYPE = #{codeType}
		</if>
		<if test="url != null and url != ''" >
			and URL = #{url}
		</if>
		<if test="validInd != null and validInd != ''" >
			and VALID_IND = #{validInd}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="modifiedTime != null and modifiedTime != ''" >
			and MODIFIED_TIME = #{modifiedTime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GGURLCONFIG
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGURLCONFIG
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGURLCONFIG
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.urlconfig.po.Ggurlconfig">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GGURLCONFIG
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GGURLCONFIG
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.urlconfig.po.Ggurlconfig">
		insert into GGURLCONFIG (
			ID,
			SYSTEM_CODE,
			CODE_TYPE,
			URL,
			VALID_IND,
			FLAG,
			REMARK,
			CREATE_TIME,
			MODIFIED_TIME
		) values (
			#{id},
			#{systemCode},
			#{codeType},
			#{url},
			#{validInd},
			#{flag},
			#{remark},
			#{createTime},
			#{modifiedTime}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.urlconfig.po.Ggurlconfig">
		insert into GGURLCONFIG
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="systemCode != null" >
				SYSTEM_CODE,
			</if>
			<if test="codeType != null" >
				CODE_TYPE,
			</if>
			<if test="url != null" >
				URL,
			</if>
			<if test="validInd != null" >
				VALID_IND,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="systemCode != null" >
				#{systemCode},
			</if>
			<if test="codeType != null" >
				#{codeType},
			</if>
			<if test="url != null" >
				#{url},
			</if>
			<if test="validInd != null" >
				#{validInd},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="modifiedTime != null" >
				#{modifiedTime}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.urlconfig.po.Ggurlconfig">
		update GGURLCONFIG 
		<set>
			<if test="systemCode != null" >
				SYSTEM_CODE=#{systemCode},
			</if>
			<if test="codeType != null" >
				CODE_TYPE=#{codeType},
			</if>
			<if test="url != null" >
				URL=#{url},
			</if>
			<if test="validInd != null" >
				VALID_IND=#{validInd},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME=#{modifiedTime},
			</if>
		</set>
		where ID = #{id }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.urlconfig.po.Ggurlconfig">
		update GGURLCONFIG set
			SYSTEM_CODE=#{systemCode},
			CODE_TYPE=#{codeType},
			URL=#{url},
			VALID_IND=#{validInd},
			FLAG=#{flag},
			REMARK=#{remark},
			CREATE_TIME=#{createTime},
			MODIFIED_TIME=#{modifiedTime},
		where ID = #{id}	</update>
</mapper>
