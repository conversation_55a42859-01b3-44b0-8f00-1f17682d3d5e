<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.user.dao.GguserDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.user.po.Gguser">
		<id column="USER_CODE" property="userCode" />
		<result column="USER_CNAME" property="userCname" />
		<result column="USER_TNAME" property="userTname" />
		<result column="USER_ENAME" property="userEname" />
		<result column="PASSWORD" property="password" />
		<result column="SEAL" property="seal" />
		<result column="PASSWORD_SET_DATE" property="passwordSetDate" />
		<result column="PASSWORD_EXPIRE_DATE" property="passwordExpireDate" />
		<result column="COMPANY_CODE" property="companyCode" />
		<result column="ISSUE_COMPANY" property="issueCompany" />
		<result column="ACCOUNT_CODE" property="accountCode" />
		<result column="PHONE" property="phone" />
		<result column="MOBILE" property="mobile" />
		<result column="ADDRESS" property="address" />
		<result column="POST_CODE" property="postCode" />
		<result column="EMAIL" property="email" />
		<result column="USER_IND" property="userInd" />
		<result column="LOGIN_SYSTEM" property="loginSystem" />
		<result column="CREATOR_CODE" property="creatorCode" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="UPDATER_CODE" property="updaterCode" />
		<result column="UPDATE_TIME" property="updateTime" />
		<result column="VALID_IND" property="validInd" />
		<result column="REMARK" property="remark" />
		<result column="FLAG" property="flag" />
		<result column="POWER_AUTH_IND" property="powerAuthInd" />
		<result column="IDENTIFY_NO" property="identifyNo" />
		<result column="TEAM_MANAGER" property="teamManager" />
		<result column="ACTION_URL" property="actionUrl" />
		<result column="MAC_NO" property="macNo" />
		<result column="LAST_EDIT_DATE" property="lastEditDate" />
		<result column="REGISTER_NO" property="registerNo" />
		<result column="OUTER_CODE" property="outerCode" />
		<result column="COOPERATE_SITE_CODE" property="cooperateSiteCode" />
		<result column="REQUEST_IND" property="requestInd" />
		<result column="DEPARTMENT" property="department" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		USER_CODE,
		USER_CNAME,
		USER_TNAME,
		USER_ENAME,
		PASSWORD,
		SEAL,
		PASSWORD_SET_DATE,
		PASSWORD_EXPIRE_DATE,
		COMPANY_CODE,
		ISSUE_COMPANY,
		ACCOUNT_CODE,
		PHONE,
		MOBILE,
		ADDRESS,
		POST_CODE,
		EMAIL,
		USER_IND,
		LOGIN_SYSTEM,
		CREATOR_CODE,
		CREATE_TIME,
		UPDATER_CODE,
		UPDATE_TIME,
		VALID_IND,
		REMARK,
		FLAG,
		POWER_AUTH_IND,
		IDENTIFY_NO,
		TEAM_MANAGER,
		ACTION_URL,
		MAC_NO,
		LAST_EDIT_DATE,
		REGISTER_NO,
		OUTER_CODE,
		COOPERATE_SITE_CODE,
		REQUEST_IND,
		DEPARTMENT
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="userCode != null and userCode != ''" >
			and USER_CODE = #{userCode}
		</if>
		<if test="userCname != null and userCname != ''" >
			and USER_CNAME = #{userCname}
		</if>
		<if test="userTname != null and userTname != ''" >
			and USER_TNAME = #{userTname}
		</if>
		<if test="userEname != null and userEname != ''" >
			and USER_ENAME = #{userEname}
		</if>
		<if test="password != null and password != ''" >
			and PASSWORD = #{password}
		</if>
		<if test="seal != null and seal != ''" >
			and SEAL = #{seal}
		</if>
		<if test="passwordSetDate != null and passwordSetDate != ''" >
			and PASSWORD_SET_DATE = #{passwordSetDate}
		</if>
		<if test="passwordExpireDate != null and passwordExpireDate != ''" >
			and PASSWORD_EXPIRE_DATE = #{passwordExpireDate}
		</if>
		<if test="companyCode != null and companyCode != ''" >
			and COMPANY_CODE = #{companyCode}
		</if>
		<if test="issueCompany != null and issueCompany != ''" >
			and ISSUE_COMPANY = #{issueCompany}
		</if>
		<if test="accountCode != null and accountCode != ''" >
			and ACCOUNT_CODE = #{accountCode}
		</if>
		<if test="phone != null and phone != ''" >
			and PHONE = #{phone}
		</if>
		<if test="mobile != null and mobile != ''" >
			and MOBILE = #{mobile}
		</if>
		<if test="address != null and address != ''" >
			and ADDRESS = #{address}
		</if>
		<if test="postCode != null and postCode != ''" >
			and POST_CODE = #{postCode}
		</if>
		<if test="email != null and email != ''" >
			and EMAIL = #{email}
		</if>
		<if test="userInd != null and userInd != ''" >
			and USER_IND = #{userInd}
		</if>
		<if test="loginSystem != null and loginSystem != ''" >
			and LOGIN_SYSTEM = #{loginSystem}
		</if>
		<if test="creatorCode != null and creatorCode != ''" >
			and CREATOR_CODE = #{creatorCode}
		</if>
		<if test="createTime != null and createTime != ''" >
			and CREATE_TIME = #{createTime}
		</if>
		<if test="updaterCode != null and updaterCode != ''" >
			and UPDATER_CODE = #{updaterCode}
		</if>
		<if test="updateTime != null and updateTime != ''" >
			and UPDATE_TIME = #{updateTime}
		</if>
		<if test="validInd != null and validInd != ''" >
			and VALID_IND = #{validInd}
		</if>
		<if test="remark != null and remark != ''" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null and flag != ''" >
			and FLAG = #{flag}
		</if>
		<if test="powerAuthInd != null and powerAuthInd != ''" >
			and POWER_AUTH_IND = #{powerAuthInd}
		</if>
		<if test="identifyNo != null and identifyNo != ''" >
			and IDENTIFY_NO = #{identifyNo}
		</if>
		<if test="teamManager != null and teamManager != ''" >
			and TEAM_MANAGER = #{teamManager}
		</if>
		<if test="actionUrl != null and actionUrl != ''" >
			and ACTION_URL = #{actionUrl}
		</if>
		<if test="macNo != null and macNo != ''" >
			and MAC_NO = #{macNo}
		</if>
		<if test="lastEditDate != null and lastEditDate != ''" >
			and LAST_EDIT_DATE = #{lastEditDate}
		</if>
		<if test="registerNo != null and registerNo != ''" >
			and REGISTER_NO = #{registerNo}
		</if>
		<if test="outerCode != null and outerCode != ''" >
			and OUTER_CODE = #{outerCode}
		</if>
		<if test="cooperateSiteCode != null and cooperateSiteCode != ''" >
			and COOPERATE_SITE_CODE = #{cooperateSiteCode}
		</if>
		<if test="requestInd != null and requestInd != ''" >
			and REQUEST_IND = #{requestInd}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from GGUSER
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGUSER
		where USER_CODE = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from GGUSER
		where USER_CODE in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.user.po.Gguser">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from GGUSER
		where USER_CODE = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from GGUSER
		where USER_CODE in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert"  parameterType="ins.channel.user.po.Gguser">
		insert into GGUSER (
			USER_CODE,
			USER_CNAME,
			USER_TNAME,
			USER_ENAME,
			PASSWORD,
			SEAL,
			PASSWORD_SET_DATE,
			PASSWORD_EXPIRE_DATE,
			COMPANY_CODE,
			ISSUE_COMPANY,
			ACCOUNT_CODE,
			PHONE,
			MOBILE,
			ADDRESS,
			POST_CODE,
			EMAIL,
			USER_IND,
			LOGIN_SYSTEM,
			CREATOR_CODE,
			CREATE_TIME,
			UPDATER_CODE,
			UPDATE_TIME,
			VALID_IND,
			REMARK,
			FLAG,
			POWER_AUTH_IND,
			IDENTIFY_NO,
			TEAM_MANAGER,
			ACTION_URL,
			MAC_NO,
			LAST_EDIT_DATE,
			REGISTER_NO,
			OUTER_CODE,
			COOPERATE_SITE_CODE,
			REQUEST_IND,
			department
		) values (
			#{userCode},
			#{userCname},
			#{userTname},
			#{userEname},
			#{password},
			#{seal},
			#{passwordSetDate},
			#{passwordExpireDate},
			#{companyCode},
			#{issueCompany},
			#{accountCode},
			#{phone},
			#{mobile},
			#{address},
			#{postCode},
			#{email},
			#{userInd},
			#{loginSystem},
			#{creatorCode},
			#{createTime},
			#{updaterCode},
			#{updateTime},
			#{validInd},
			#{remark},
			#{flag},
			#{powerAuthInd},
			#{identifyNo},
			#{teamManager},
			#{actionUrl},
			#{macNo},
			#{lastEditDate},
			#{registerNo},
			#{outerCode},
			#{cooperateSiteCode},
			#{requestInd},
			#{department}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective"  parameterType="ins.channel.user.po.Gguser">
		insert into GGUSER
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="userCode != null" >
				USER_CODE,
			</if>
			<if test="userCname != null" >
				USER_CNAME,
			</if>
			<if test="userTname != null" >
				USER_TNAME,
			</if>
			<if test="userEname != null" >
				USER_ENAME,
			</if>
			<if test="password != null" >
				PASSWORD,
			</if>
			<if test="seal != null" >
				SEAL,
			</if>
			<if test="passwordSetDate != null" >
				PASSWORD_SET_DATE,
			</if>
			<if test="passwordExpireDate != null" >
				PASSWORD_EXPIRE_DATE,
			</if>
			<if test="companyCode != null" >
				COMPANY_CODE,
			</if>
			<if test="issueCompany != null" >
				ISSUE_COMPANY,
			</if>
			<if test="accountCode != null" >
				ACCOUNT_CODE,
			</if>
			<if test="phone != null" >
				PHONE,
			</if>
			<if test="mobile != null" >
				MOBILE,
			</if>
			<if test="address != null" >
				ADDRESS,
			</if>
			<if test="postCode != null" >
				POST_CODE,
			</if>
			<if test="email != null" >
				EMAIL,
			</if>
			<if test="userInd != null" >
				USER_IND,
			</if>
			<if test="loginSystem != null" >
				LOGIN_SYSTEM,
			</if>
			<if test="creatorCode != null" >
				CREATOR_CODE,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="updaterCode != null" >
				UPDATER_CODE,
			</if>
			<if test="updateTime != null" >
				UPDATE_TIME,
			</if>
			<if test="validInd != null" >
				VALID_IND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="powerAuthInd != null" >
				POWER_AUTH_IND,
			</if>
			<if test="identifyNo != null" >
				IDENTIFY_NO,
			</if>
			<if test="teamManager != null" >
				TEAM_MANAGER,
			</if>
			<if test="actionUrl != null" >
				ACTION_URL,
			</if>
			<if test="macNo != null" >
				MAC_NO,
			</if>
			<if test="lastEditDate != null" >
				LAST_EDIT_DATE,
			</if>
			<if test="registerNo != null" >
				REGISTER_NO,
			</if>
			<if test="outerCode != null" >
				OUTER_CODE,
			</if>
			<if test="cooperateSiteCode != null" >
				COOPERATE_SITE_CODE,
			</if>
			<if test="requestInd != null" >
				REQUEST_IND,
			</if>
			<if test="department != null and department != ''" >
				DEPARTMENT
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="userCode != null" >
				#{userCode},
			</if>
			<if test="userCname != null" >
				#{userCname},
			</if>
			<if test="userTname != null" >
				#{userTname},
			</if>
			<if test="userEname != null" >
				#{userEname},
			</if>
			<if test="password != null" >
				#{password},
			</if>
			<if test="seal != null" >
				#{seal},
			</if>
			<if test="passwordSetDate != null" >
				#{passwordSetDate},
			</if>
			<if test="passwordExpireDate != null" >
				#{passwordExpireDate},
			</if>
			<if test="companyCode != null" >
				#{companyCode},
			</if>
			<if test="issueCompany != null" >
				#{issueCompany},
			</if>
			<if test="accountCode != null" >
				#{accountCode},
			</if>
			<if test="phone != null" >
				#{phone},
			</if>
			<if test="mobile != null" >
				#{mobile},
			</if>
			<if test="address != null" >
				#{address},
			</if>
			<if test="postCode != null" >
				#{postCode},
			</if>
			<if test="email != null" >
				#{email},
			</if>
			<if test="userInd != null" >
				#{userInd},
			</if>
			<if test="loginSystem != null" >
				#{loginSystem},
			</if>
			<if test="creatorCode != null" >
				#{creatorCode},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="updaterCode != null" >
				#{updaterCode},
			</if>
			<if test="updateTime != null" >
				#{updateTime},
			</if>
			<if test="validInd != null" >
				#{validInd},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="powerAuthInd != null" >
				#{powerAuthInd},
			</if>
			<if test="identifyNo != null" >
				#{identifyNo},
			</if>
			<if test="teamManager != null" >
				#{teamManager},
			</if>
			<if test="actionUrl != null" >
				#{actionUrl},
			</if>
			<if test="macNo != null" >
				#{macNo},
			</if>
			<if test="lastEditDate != null" >
				#{lastEditDate},
			</if>
			<if test="registerNo != null" >
				#{registerNo},
			</if>
			<if test="outerCode != null" >
				#{outerCode},
			</if>
			<if test="cooperateSiteCode != null" >
				#{cooperateSiteCode},
			</if>
			<if test="requestInd != null" >
				#{requestInd},
			</if>
			<if test="department != null and department != ''" >
				#{department}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey"  parameterType="ins.channel.user.po.Gguser">
		update GGUSER 
		<set>
			<if test="userCname != null" >
				USER_CNAME=#{userCname},
			</if>
			<if test="userTname != null" >
				USER_TNAME=#{userTname},
			</if>
			<if test="userEname != null" >
				USER_ENAME=#{userEname},
			</if>
			<if test="password != null" >
				PASSWORD=#{password},
			</if>
			<if test="seal != null" >
				SEAL=#{seal},
			</if>
			<if test="passwordSetDate != null" >
				PASSWORD_SET_DATE=#{passwordSetDate},
			</if>
			<if test="passwordExpireDate != null" >
				PASSWORD_EXPIRE_DATE=#{passwordExpireDate},
			</if>
			<if test="companyCode != null" >
				COMPANY_CODE=#{companyCode},
			</if>
			<if test="issueCompany != null" >
				ISSUE_COMPANY=#{issueCompany},
			</if>
			<if test="accountCode != null" >
				ACCOUNT_CODE=#{accountCode},
			</if>
			<if test="phone != null" >
				PHONE=#{phone},
			</if>
			<if test="mobile != null" >
				MOBILE=#{mobile},
			</if>
			<if test="address != null" >
				ADDRESS=#{address},
			</if>
			<if test="postCode != null" >
				POST_CODE=#{postCode},
			</if>
			<if test="email != null" >
				EMAIL=#{email},
			</if>
			<if test="userInd != null" >
				USER_IND=#{userInd},
			</if>
			<if test="loginSystem != null" >
				LOGIN_SYSTEM=#{loginSystem},
			</if>
			<if test="creatorCode != null" >
				CREATOR_CODE=#{creatorCode},
			</if>
			<if test="createTime != null" >
				CREATE_TIME=#{createTime},
			</if>
			<if test="updaterCode != null" >
				UPDATER_CODE=#{updaterCode},
			</if>
			<if test="updateTime != null" >
				UPDATE_TIME=#{updateTime},
			</if>
			<if test="validInd != null" >
				VALID_IND=#{validInd},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="powerAuthInd != null" >
				POWER_AUTH_IND=#{powerAuthInd},
			</if>
			<if test="identifyNo != null" >
				IDENTIFY_NO=#{identifyNo},
			</if>
			<if test="teamManager != null" >
				TEAM_MANAGER=#{teamManager},
			</if>
			<if test="actionUrl != null" >
				ACTION_URL=#{actionUrl},
			</if>
			<if test="macNo != null" >
				MAC_NO=#{macNo},
			</if>
			<if test="lastEditDate != null" >
				LAST_EDIT_DATE=#{lastEditDate},
			</if>
			<if test="registerNo != null" >
				REGISTER_NO=#{registerNo},
			</if>
			<if test="outerCode != null" >
				OUTER_CODE=#{outerCode},
			</if>
			<if test="cooperateSiteCode != null" >
				COOPERATE_SITE_CODE=#{cooperateSiteCode},
			</if>
			<if test="requestInd != null" >
				REQUEST_IND=#{requestInd},
			</if>
			<if test="department != null and department != ''" >
				DEPARTMENT=#{department},
			</if>
		</set>
		where USER_CODE = #{userCode }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey"  parameterType="ins.channel.user.po.Gguser">
		update GGUSER set
			USER_CNAME=#{userCname},
			USER_TNAME=#{userTname},
			USER_ENAME=#{userEname},
			PASSWORD=#{password},
			SEAL=#{seal},
			PASSWORD_SET_DATE=#{passwordSetDate},
			PASSWORD_EXPIRE_DATE=#{passwordExpireDate},
			COMPANY_CODE=#{companyCode},
			ISSUE_COMPANY=#{issueCompany},
			ACCOUNT_CODE=#{accountCode},
			PHONE=#{phone},
			MOBILE=#{mobile},
			ADDRESS=#{address},
			POST_CODE=#{postCode},
			EMAIL=#{email},
			USER_IND=#{userInd},
			LOGIN_SYSTEM=#{loginSystem},
			CREATOR_CODE=#{creatorCode},
			CREATE_TIME=#{createTime},
			UPDATER_CODE=#{updaterCode},
			UPDATE_TIME=#{updateTime},
			VALID_IND=#{validInd},
			REMARK=#{remark},
			FLAG=#{flag},
			POWER_AUTH_IND=#{powerAuthInd},
			IDENTIFY_NO=#{identifyNo},
			TEAM_MANAGER=#{teamManager},
			ACTION_URL=#{actionUrl},
			MAC_NO=#{macNo},
			LAST_EDIT_DATE=#{lastEditDate},
			REGISTER_NO=#{registerNo},
			OUTER_CODE=#{outerCode},
			COOPERATE_SITE_CODE=#{cooperateSiteCode},
			REQUEST_IND=#{requestInd},
			DEPARTMENT=#{department},
		where USER_CODE = #{userCode}	</update>
</mapper>
