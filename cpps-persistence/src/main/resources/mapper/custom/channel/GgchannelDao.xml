<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.channel.dao.GgchannelDao">
	<!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
	<cache-ref  namespace="ins.channel.channel.dao.GgchannelDao"/>
	<!-- 请在下方添加自定义配置-->

	<!--分页模糊查询-->
	<select id="pageByCondition" resultMap="BaseResultMap" parameterType="ins.channel.channel.po.Ggchannel">
		select
		<include refid="Base_Column_List" />
		from GGCHANNEL
		<where>
			<if test="channelCode != null and channelCode != ''" >
				and CHANNEL_CODE like '%'|| #{channelCode}||'%'
			</if>
			<if test="channelCname != null and channelCname != ''" >
				and CHANNEL_CNAME like '%'|| #{channelCname}||'%'
			</if>
		</where>
	</select>

	<resultMap id="ChannelForSelect" type="ins.channel.channel.po.GgchannelSelect">
		<result column="CHANNEL_CODE" property="channelCode" />
		<result column="CHANNEL_CNAME" property="channelCname" />
	</resultMap>
	<!-- 仅下拉框查询查询进行分页查询 -->
	<select id="channelForSelectPage" resultMap="ChannelForSelect" parameterType="java.lang.String">
		select
		CHANNEL_CODE,CHANNEL_CNAME
		from GGCHANNEL
		<where>
			VALID_IND = '1'
			<if test="queryInfo != null and queryInfo != ''">
				and (CHANNEL_CODE like concat(concat('%',#{queryInfo}),'%')
				or CHANNEL_CNAME like concat(concat('%',#{queryInfo}),'%'))
			</if>
		</where>
	</select>

	<!-- 将CodeCode转换成相应文字 -->
	<select id="translate" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List"/>
		from GGCHANNEL
		<where>
			VALID_IND = '1'
			and CHANNEL_CODE = #{param1}
		</where>
	</select>
	<resultMap id="BaseCodeResultMap" type="ins.channel.baseCode.po.BaseCode">
		<result column="CODE_TYPE" property="codeType" />
		<result column="CODE_CODE" property="codeCode" />
		<result column="CODE_NAME" property="codeName" />
	</resultMap>
	<!--Modify By Zhoutaoyu 查询所有码表(供基础码表查询使用) 2019/11/20-->
	<select id="queryAll" resultMap="BaseCodeResultMap">
		SELECT 'channelCode' as codeType,
			   channel_code as codeCode,
			   CHANNEL_CNAME as codeName
		 FROM ggchannel
		 WHERE VALID_IND = '1'
    </select>
</mapper>
