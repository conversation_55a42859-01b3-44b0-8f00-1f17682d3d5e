<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.code.dao.GgcodeDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.code.dao.GgcodeDao"/>
    <!-- 请在下方添加自定义配置-->

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="searchPage" resultMap="BaseResultMap" parameterType="ins.channel.code.po.Ggcode">
        select
        <include refid="Base_Column_List"/>
        from GGCODE
        <where>
            <if test="codeType != null and codeType != ''">
                and CODE_TYPE = #{codeType}
            </if>
            <if test="companyCode != null and companyCode != ''">
                and COMPANY_CODE = #{companyCode}
            </if>
            <if test="codeCode != null and codeCode != ''">
                and CODE_CODE = #{codeCode}
            </if>
            <if test="validInd != null and validInd != ''">
                and VALID_IND = #{validInd}
            </if>
            <if test="codeCname != null and codeCname != ''">
                and CODE_CNAME = #{codeCname}
            </if>
            <if test="codeTname != null and codeTname != ''">
                and CODE_TNAME = #{codeTname}
            </if>
            <if test="remark != null and remark != ''">
                and REMARK = #{remark}
            </if>
        </where>
        order by MODIFIED_TIME desc
    </select>

    <resultMap id="CodeInfoForSelect" type="ins.channel.code.po.GgcodeSelect">
        <result column="CODE_CODE" property="codeCode" />
        <result column="CODE_CNAME" property="codeCname" />
    </resultMap>
    <!-- 仅查询codecode ,codeType 进行分页查询 -->
    <select id="codeInfoForSelectPage" resultMap="CodeInfoForSelect" parameterType="java.lang.String">
        select
            CODE_CODE,CODE_CNAME
        from GGCODE
        <where>
            VALID_IND = '1'
            <if test="queryInfo != null and queryInfo != ''">
                and (CODE_CODE like concat(concat('%',#{queryInfo}),'%')
                or CODE_CNAME like concat(concat('%',#{queryInfo}),'%'))
            </if>
        </where>
        order by DISPLAY_NO asc
    </select>

    <!-- 完整插入一条记录-->
    <insert id="insertAuto" parameterType="ins.channel.code.po.Ggcode">
        insert into GGCODE (GID,
                            CODE_TYPE,
                            COMPANY_CODE,
                            CODE_CODE,
                            CODE_CNAME,
                            CODE_TNAME,
                            CODE_ENAME,
                            VALID_IND,
                            REMARK,
                            FLAG,
                            DISPLAY_NO,
                            CREATE_TIME,
                            MODIFIED_TIME)
        values (SEQ_GGCODE.nextval,
                #{codeType},
                #{companyCode},
                #{codeCode},
                #{codeCname},
                #{codeTname},
                #{codeEname},
                #{validInd},
                #{remark},
                #{flag},
                #{displayNo},
                #{createTime},
                #{modifiedTime})
    </insert>
    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelectiveAuto" parameterType="ins.channel.code.po.Ggcode">
        insert into GGCODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            GID,
            <if test="codeType != null">
                CODE_TYPE,
            </if>
            <if test="companyCode != null">
                COMPANY_CODE,
            </if>
            <if test="codeCode != null">
                CODE_CODE,
            </if>
            <if test="codeCname != null">
                CODE_CNAME,
            </if>
            <if test="codeTname != null">
                CODE_TNAME,
            </if>
            <if test="codeEname != null">
                CODE_ENAME,
            </if>
            <if test="validInd != null">
                VALID_IND,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="flag != null">
                FLAG,
            </if>
            <if test="displayNo != null">
                DISPLAY_NO,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="modifiedTime != null">
                MODIFIED_TIME
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            SEQ_GGCODE.nextval,
            <if test="codeType != null">
                #{codeType},
            </if>
            <if test="companyCode != null">
                #{companyCode},
            </if>
            <if test="codeCode != null">
                #{codeCode},
            </if>
            <if test="codeCname != null">
                #{codeCname},
            </if>
            <if test="codeTname != null">
                #{codeTname},
            </if>
            <if test="codeEname != null">
                #{codeEname},
            </if>
            <if test="validInd != null">
                #{validInd},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="flag != null">
                #{flag},
            </if>
            <if test="displayNo != null">
                #{displayNo},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="modifiedTime != null">
                #{modifiedTime}
            </if>
        </trim>
    </insert>

    <!-- 分页查询支付方式（多条记录） -->
    <select id="searchCodeByRemark" resultMap="BaseResultMap" parameterType="ins.channel.code.po.GgcodePayWay">
        select
        <include refid="Base_Column_List"/>
        from GGCODE
        <where>
            VALID_IND = '1'
            <if test="remark != null and remark != ''">
                and remark like concat('%',concat(concat(#{remark},','),'%'))
            </if>
            <if test="companyCode != null and companyCode != ''">
                and COMPANY_CODE = #{companyCode}
            </if>
            <if test="codeType != null and codeType !='' ">
                and CODE_TYPE = #{codeType}
            </if>
            order by DISPLAY_NO asc
        </where>
    </select>

    <!-- 将CodeCode转换成相应文字 -->
    <select id="translate" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from GGCODE
        <where>
            VALID_IND = '1'
            and code_type = #{param1}
                and code_code = #{param2}
        </where>
    </select>

    <select id="codeInfoForSelect" resultMap="CodeInfoForSelect" parameterType="java.lang.String">
        select
        CODE_CODE,CODE_CNAME
        from GGCODE
        <where>
            VALID_IND = '1'
            and code_type = #{codeType}
        </where>
        order by DISPLAY_NO asc
    </select>

    <resultMap id="BaseCodeResultMap" type="ins.channel.baseCode.po.BaseCode">
        <result column="CODE_TYPE" property="codeType" />
        <result column="CODE_CODE" property="codeCode" />
        <result column="CODE_NAME" property="codeName" />
    </resultMap>
    <!--Modify By Zhoutaoyu 查询所有码表(供基础码表查询使用) 2019/11/20-->
    <select id="queryAll" resultMap="BaseCodeResultMap">
      SELECT CODE_TYPE, CODE_CODE, CODE_CNAME AS CODE_NAME
      FROM GGCODE
      WHERE VALID_IND = '1'
    </select>

    <select id="queryALLExclusions" resultMap="BaseCodeResultMap">
      SELECT CODE_TYPE, CODE_CODE, CODE_CNAME AS CODE_NAME
      FROM GGCODE
        <where>
            VALID_IND = '1'
            AND CODE_TYPE not in ('JobCode1219','JobCodeNew2022')
        </where>
    </select>

    <select id="queryByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from GGCODE
        <where>
            VALID_IND = '1'
            and CODE_CODE = #{codeCode}
        </where>
    </select>


</mapper>
