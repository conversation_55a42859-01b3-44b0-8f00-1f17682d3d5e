<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.codetransfer.dao.GfcodetransferDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.codetransfer.dao.GfcodetransferDao"/>
    <!-- 请在下方添加自定义配置-->

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="searchPage" resultMap="BaseResultMap" parameterType="ins.channel.codetransfer.po.Gfcodetransfer">
        select
        <include refid="Base_Column_List"/>
        from GFCODETRANSFER
        <where>
            <if test="transType != null and transType != ''">
                and TRANS_TYPE = #{transType}
            </if>
            <if test="codeCode != null and codeCode != ''">
                and CODE_CODE = #{codeCode}
            </if>
            <if test="codeName != null and codeName != ''">
                and CODE_NAME = #{codeName}
            </if>
            <if test="validInd != null and validInd != ''">
                and VALID_IND = #{validInd}
            </if>
			<if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
				and MODIFIED_TIME between to_date(concat(#{startTime},' 00:00:00'),'YYYY-MM-DD HH24:MI:SS') and
				to_date(concat(#{endTime},' 23:59:59'),'YYYY-MM-DD HH24:MI:SS')
			</if>
        </where>
    </select>

</mapper>
