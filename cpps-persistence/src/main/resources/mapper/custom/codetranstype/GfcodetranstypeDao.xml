<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.codetranstype.dao.GfcodetranstypeDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.codetranstype.dao.GfcodetranstypeDao"/>
    <!-- 请在下方添加自定义配置-->

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="searchPage" resultMap="BaseResultMap" parameterType="ins.channel.codetranstype.po.Gfcodetranstype">
        select
        <include refid="Base_Column_List"/>
        from GFCODETRANSTYPE
        <where>
            <if test="transType != null and transType != ''">
                and TRANS_TYPE = #{transType}
            </if>
            <if test="transTypeCdesc != null and transTypeCdesc != ''">
                and TRANS_TYPE_CDESC = #{transTypeCdesc}
            </if>
            <if test="validInd != null and validInd != ''">
                and VALID_IND = #{validInd}
            </if>
			<if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
				and MODIFIED_TIME between to_date(concat(#{startTime},' 00:00:00'),'YYYY-MM-DD HH24:MI:SS') and
				to_date(concat(#{endTime},' 23:59:59'),'YYYY-MM-DD HH24:MI:SS')
			</if>
        </where>
    </select>

    <!-- 完整插入一条记录-->
    <insert id="insertAuto" parameterType="ins.channel.codetranstype.po.Gfcodetranstype">
        insert into GFCODETRANSTYPE (GID,
                                     TRANS_TYPE,
                                     TRANS_TYPE_CDESC,
                                     TRANS_TYPE_TDESC,
                                     TRANS_TYPE_EDESC,
                                     VALID_IND,
                                     REMARK,
                                     FLAG,
                                     VAILD_DATE,
                                     INVALID_DATE,
                                     CREATE_TIME,
                                     MODIFIED_TIME)
        values (SEQ_GFCODETRANSTYPE.nextval,
                #{transType},
                #{transTypeCdesc},
                #{transTypeTdesc},
                #{transTypeEdesc},
                #{validInd},
                #{remark},
                #{flag},
                #{vaildDate},
                #{invalidDate},
                #{createTime},
                #{modifiedTime})
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelectiveAuto" parameterType="ins.channel.codetranstype.po.Gfcodetranstype">
        insert into GFCODETRANSTYPE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            GID,
            <if test="transType != null">
                TRANS_TYPE,
            </if>
            <if test="transTypeCdesc != null">
                TRANS_TYPE_CDESC,
            </if>
            <if test="transTypeTdesc != null">
                TRANS_TYPE_TDESC,
            </if>
            <if test="transTypeEdesc != null">
                TRANS_TYPE_EDESC,
            </if>
            <if test="validInd != null">
                VALID_IND,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="flag != null">
                FLAG,
            </if>
            <if test="vaildDate != null">
                VAILD_DATE,
            </if>
            <if test="invalidDate != null">
                INVALID_DATE,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="modifiedTime != null">
                MODIFIED_TIME
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            SEQ_GFCODETRANSTYPE.nextval,
            <if test="transType != null">
                #{transType},
            </if>
            <if test="transTypeCdesc != null">
                #{transTypeCdesc},
            </if>
            <if test="transTypeTdesc != null">
                #{transTypeTdesc},
            </if>
            <if test="transTypeEdesc != null">
                #{transTypeEdesc},
            </if>
            <if test="validInd != null">
                #{validInd},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="flag != null">
                #{flag},
            </if>
            <if test="vaildDate != null">
                #{vaildDate},
            </if>
            <if test="invalidDate != null">
                #{invalidDate},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="modifiedTime != null">
                #{modifiedTime}
            </if>
        </trim>
    </insert>

</mapper>
