<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.codetype.dao.GgcodetypeDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.codetype.dao.GgcodetypeDao"/>
    <!-- 请在下方添加自定义配置-->
    <!-- 按对象查询一页记录（多条记录） -->
    <select id="searchPage" resultMap="BaseResultMap" parameterType="ins.channel.codetype.po.GgcodetypeSearch">
        select
        <include refid="Base_Column_List"/>
        from GGCODETYPE
        <where>
            <!-- 按对象查询记录的WHERE部分 -->
            <if test="codeType != null and codeType != ''">
                and CODE_TYPE = #{codeType}
            </if>
            <if test="codeTypeCdesc != null and codeTypeCdesc != ''">
                and CODE_TYPE_CDESC = #{codeTypeCdesc}
            </if>
            <if test="validInd != null and validInd != ''">
                and VALID_IND = #{validInd}
            </if>
        </where>
        order by MODIFIED_TIME desc
    </select>

    <resultMap id="CodeTypeForSelect" type="ins.channel.codetype.po.GgcodetypeSelect">
        <result column="CODE_TYPE" property="codeType" />
        <result column="CODE_TYPE_CDESC" property="codeTypeCdesc" />
    </resultMap>
    <!-- 仅查询codecode ,codeType 进行分页查询 -->
    <select id="codeTypeForSelectPage" resultMap="CodeTypeForSelect" parameterType="java.lang.String">
        select
        CODE_TYPE,CODE_TYPE_CDESC
        from GGCODETYPE
        <where>
            VALID_IND = '1'
            <if test="queryInfo != null and queryInfo != ''">
                and (CODE_TYPE like concat(concat('%',#{queryInfo}),'%')
                or CODE_TYPE_CDESC like concat(concat('%',#{queryInfo}),'%'))
            </if>
        </where>
    </select>

    <!-- 完整插入一条记录-->
    <insert id="insertAuto" parameterType="ins.channel.codetype.po.Ggcodetype">
        insert into GGCODETYPE (GID,
                                CODE_TYPE,
                                CODE_TYPE_CDESC,
                                CODE_TYPE_TDESC,
                                CODE_TYPE_EDESC,
                                VALID_IND,
                                REMARK,
                                FLAG,
                                CREATE_TIME,
                                MODIFIED_TIME)
        values (seq_ggcodetype.nextval,
                #{codeType},
                #{codeTypeCdesc},
                #{codeTypeTdesc},
                #{codeTypeEdesc},
                #{validInd},
                #{remark},
                #{flag},
                #{createTime},
                #{modifiedTime})
    </insert>


    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelectiveAuto" parameterType="ins.channel.codetype.po.Ggcodetype">
        insert into GGCODETYPE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            GID,
            <if test="codeType != null">
                CODE_TYPE,
            </if>
            <if test="codeTypeCdesc != null">
                CODE_TYPE_CDESC,
            </if>
            <if test="codeTypeTdesc != null">
                CODE_TYPE_TDESC,
            </if>
            <if test="codeTypeEdesc != null">
                CODE_TYPE_EDESC,
            </if>
            <if test="validInd != null">
                VALID_IND,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="flag != null">
                FLAG,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="modifiedTime != null">
                MODIFIED_TIME
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            seq_ggcodetype.nextval,
            <if test="codeType != null">
                #{codeType},
            </if>
            <if test="codeTypeCdesc != null">
                #{codeTypeCdesc},
            </if>
            <if test="codeTypeTdesc != null">
                #{codeTypeTdesc},
            </if>
            <if test="codeTypeEdesc != null">
                #{codeTypeEdesc},
            </if>
            <if test="validInd != null">
                #{validInd},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="flag != null">
                #{flag},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="modifiedTime != null">
                #{modifiedTime}
            </if>
        </trim>
    </insert>

</mapper>
