<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.company.dao.GgcompanyDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.company.dao.GgcompanyDao"/>
    <!-- 请在下方添加自定义配置-->
    <!-- 按机构号查询上级机构 -->
    <select id="selectUpperComInfo" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="Base_Column_List"/>
        from GGCOMPANY
        where VALID_IND = '1'
        START WITH COMPANY_CODE =#{param1}
        CONNECT BY prior UPPER_COMPANY_CODE=COMPANY_CODE
    </select>

    <!-- 根据当前机构查询下级机构机构-->
    <select id="selectUnderComInfo" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="Base_Column_List"/>
        from GGCOMPANY
        where VALID_IND = '1'
        START WITH COMPANY_CODE =#{param1}
        CONNECT BY prior COMPANY_CODE=UPPER_COMPANY_CODE
    </select>

    <!--模糊查询机构信息-->
    <select id="pageByCondition" resultMap="BaseResultMap" parameterType="ins.channel.company.po.Ggcompany">
        select
        <include refid="Base_Column_List"/>
        from GGCOMPANY
        <where>
            <if test="companyCode != null and companyCode != ''">
                and COMPANY_CODE like '%'|| #{companyCode}||'%'
            </if>
            <if test="companyCname != null and companyCname != ''">
                and COMPANY_CNAME like '%'|| #{companyCname}||'%'
            </if>
        </where>
    </select>


    <resultMap id="CompanyForSelect" type="ins.channel.company.po.GgcompanySelect">
        <result column="COMPANY_CODE" property="companyCode"/>
        <result column="COMPANY_CNAME" property="companyCname"/>
    </resultMap>
    <!-- COMPANY_CODE ,COMPANY_CNAME 查询登录用户有权限的关联机构 供下拉框使用-->
    <select id="companyForSelect" resultMap="CompanyForSelect"  parameterType="map">
        select
        COMPANY_CODE,
        COMPANY_CNAME
        from GGCOMPANY
        <where>
            VALID_IND = '1'
            <if test="queryInfo != null and queryInfo != ''">
                and (COMPANY_CODE like concat(concat('%',#{queryInfo}),'%')
                or COMPANY_CNAME like concat(concat('%',#{queryInfo}),'%'))
            </if>
            <if test="companyCodes != null and companyCodes.size() &gt; 0">
                and COMPANY_CODE IN
                <foreach collection="companyCodes" item="comCode" open="(" close=")" separator=",">
                    &apos;${comCode}&apos;
                </foreach>
            </if>
             order by COMPANY_CODE
        </where>
    </select>


    <select id="companyForSelectAll" resultMap="CompanyForSelect">
        select
        COMPANY_CODE,
        COMPANY_CNAME
        from GGCOMPANY
        where
        VALID_IND = '1'
    </select>

    <!-- 查询单个机构信息 -->
    <select id="selectCompanyInfo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from GGCOMPANY
        <where>
            COMPANY_CODE = #{param1}
        </where>
    </select>

    <select id="queryuserpermitdataInfo" resultType="ins.channel.power.vo.UserpermitdataVo">
        select t.COMPANY_CODE                                     as COMCODE,
               t.COMPANY_CNAME,
               t.UPPER_COMPANY_CODE,
               case when t2.VALIDFLAG = '1' then '1' else '0' END AS FLAG
        from GGCOMPANY t
                 left join SAAUSERPERMITDATA t2
                           on t.COMPANY_CODE = t2.DATAVALUE2 and t2.USERCODE = #{param1}
        where t.VALID_IND = '1'
        order by t.COMPANY_CODE
    </select>

    <select id="queryGgcompanyListByCodeSet" resultType="ins.channel.company.po.Ggcompany">
        select
        <include refid="Base_Column_List"/>
        from GGCOMPANY
        where 1=1
        <if test="addSet != null and addSet.size() != 0">
            and COMPANY_CODE in
            <foreach item="item" index="index" collection="addSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 通用查询结果列-->
    <sql id="Page_Column_List">
		g.COMPANY_CODE           as COMPANY_CODE,
       g.COMPANY_CNAME          as COMPANY_CNAME,
       g.COMPANY_TNAME          as COMPANY_TNAME,
       g.COMPANY_ENAME          as COMPANY_ENAME,
       g.ADDRESS_CNAME          as ADDRESS_CNAME,
       g.ADDRESS_TNAME          as ADDRESS_TNAME,
       g.ADDRESS_ENAME          as ADDRESS_ENAME,
       g.INSURER_CNAME          as INSURER_CNAME,
       g.INSURER_TNAME          as INSURER_TNAME,
       g.INSURER_ENAME          as INSURER_ENAME,
       g.UPPER_COMPANY_CODE     as UPPER_COMPANY_CODE,
       g.COM_ATTRIBUTE          as COM_ATTRIBUTE,
       g.COM_TYPE               as COM_TYPE,
       g.CENTER_IND             as CENTER_IND,
       g.COM_LEVEL              as COM_LEVEL,
       g.POST_CODE              as POST_CODE,
       g.PHONE_NUMBER           as PHONE_NUMBER,
       g.FAX_NUMBER             as FAX_NUMBER,
       g.MANAGER                as MANAGER,
       g.WEB_ADDRESS            as WEB_ADDRESS,
       g.SERVICE_PHONE          as SERVICE_PHONE,
       g.REPORT_PHONE           as REPORT_PHONE,
       g.CREATOR_CODE           as CREATOR_CODE,
       g.CREATE_TIME            as CREATE_TIME,
       g.UPDATER_CODE           as UPDATER_CODE,
       g.UPDATE_TIME            as UPDATE_TIME,
       g.VALID_IND              as VALID_IND,
       g.REMARK                 as REMARK,
       g.FLAG                   as FLAG,
       g.SHORT_CNAME            as SHORT_CNAME,
       g.SHORT_TNAME            as SHORT_TNAME,
       g.SHORT_ENAME            as SHORT_ENAME,
       g.TAX_NUMBER             as TAX_NUMBER,
       g.EMAIL                  as EMAIL,
       g.PRINT_POLICY_COM_CODE  as PRINT_POLICY_COM_CODE,
       g.PRINTIN_VOICE_COM_CODE as PRINTIN_VOICE_COM_CODE,
       g.CITY_CODE              as CITY_CODE,
       g.REQUEST_IND            as REQUEST_IND,
       p.PAYMENT_COMCODE		as PAYMENT_COMCODE
    </sql>

    <resultMap id="PageResultMap" type="ins.channel.company.po.Ggcompany">
        <id column="COMPANY_CODE" property="companyCode"/>
        <result column="COMPANY_CNAME" property="companyCname"/>
        <result column="COMPANY_TNAME" property="companyTname"/>
        <result column="COMPANY_ENAME" property="companyEname"/>
        <result column="ADDRESS_CNAME" property="addressCname"/>
        <result column="ADDRESS_TNAME" property="addressTname"/>
        <result column="ADDRESS_ENAME" property="addressEname"/>
        <result column="INSURER_CNAME" property="insurerCname"/>
        <result column="INSURER_TNAME" property="insurerTname"/>
        <result column="INSURER_ENAME" property="insurerEname"/>
        <result column="UPPER_COMPANY_CODE" property="upperCompanyCode"/>
        <result column="COM_ATTRIBUTE" property="comAttribute"/>
        <result column="COM_TYPE" property="comType"/>
        <result column="CENTER_IND" property="centerInd"/>
        <result column="COM_LEVEL" property="comLevel"/>
        <result column="POST_CODE" property="postCode"/>
        <result column="PHONE_NUMBER" property="phoneNumber"/>
        <result column="FAX_NUMBER" property="faxNumber"/>
        <result column="MANAGER" property="manager"/>
        <result column="WEB_ADDRESS" property="webAddress"/>
        <result column="SERVICE_PHONE" property="servicePhone"/>
        <result column="REPORT_PHONE" property="reportPhone"/>
        <result column="CREATOR_CODE" property="creatorCode"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATER_CODE" property="updaterCode"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="VALID_IND" property="validInd"/>
        <result column="REMARK" property="remark"/>
        <result column="FLAG" property="flag"/>
        <result column="SHORT_CNAME" property="shortCname"/>
        <result column="SHORT_TNAME" property="shortTname"/>
        <result column="SHORT_ENAME" property="shortEname"/>
        <result column="TAX_NUMBER" property="taxNumber"/>
        <result column="EMAIL" property="email"/>
        <result column="PRINT_POLICY_COM_CODE" property="printPolicyComCode"/>
        <result column="PRINTIN_VOICE_COM_CODE" property="printinVoiceComCode"/>
        <result column="CITY_CODE" property="cityCode"/>
        <result column="REQUEST_IND" property="requestInd"/>
        <result column="PAYMENT_COMCODE" property="paymentComcode"/>
    </resultMap>

    <!-- 按对象查询一页记录（多条记录） -->
    <!--<select id="searchPage" resultMap="BaseResultMap" parameterType="ins.channel.company.po.GgcompanySearch">-->
        <!--select-->
        <!--<include refid="Page_Column_List"/>-->
        <!--from GGCOMPANY g left join GPPAYCOMCODEDEFINE p on g.COMPANY_CODE = p.PAYMENT_COMCODE-->
        <!--<where>-->
            <!--<if test="companyCode != null and companyCode != ''">-->
                <!--and g.COMPANY_CODE like concat(#{companyCode},'%')-->
            <!--</if>-->
            <!--<if test="companyCname != null and companyCname != ''">-->
                <!--and g.COMPANY_CNAME like concat('%',concat(#{companyCname},'%'))-->
            <!--</if>-->
            <!--<if test="upperCompanyCode != null and upperCompanyCode != ''">-->
                <!--and g.UPPER_COMPANY_CODE like concat(#{upperCompanyCode},'%')-->
            <!--</if>-->
            <!--<if test="validInd != null and validInd != ''">-->
                <!--and g.VALID_IND = #{validInd}-->
            <!--</if>-->
            <!--<if test="taxNumber != null and taxNumber != ''">-->
                <!--and g.tax_number like concat(#{taxNumber},'%')-->
            <!--</if>-->
            <!--<if test="grantFlag != null and grantFlag != ''">-->
                <!--<if test='grantFlag == "1"'>-->
                    <!--and p.PAYMENT_COMCODE is null-->
                <!--</if>-->
                <!--<if test='grantFlag == "0"'>-->
                    <!--and p.PAYMENT_COMCODE is not null-->
                <!--</if>-->
            <!--</if>-->
        <!--</where>-->
        <!--order By  convert(int, g.COMPANY_CODE)-->
    <!--</select>-->

    <!-- 按对象查询一页记录（多条记录） -->
    <!--<select id="searchByRelatedPage" resultMap="BaseResultMap" parameterType="ins.channel.company.po.GgcompanySearch">-->
        <!--select-->
        <!--<include refid="Base_Column_List"/>-->
        <!--from GGCOMPANY g-->
        <!--<where>-->
            <!--<if test="companyCode != null and companyCode != ''">-->
                <!--and g.COMPANY_CODE like concat(#{companyCode},'%')-->
            <!--</if>-->
            <!--<if test="companyCname != null and companyCname != ''">-->
                <!--and g.COMPANY_CNAME like concat('%',concat(#{companyCname},'%'))-->
            <!--</if>-->
            <!--<if test="paymentCode != null and paymentCode != ''">-->
                <!--and g.COMPANY_CODE in (-->
                <!--select DEPARTMENT_CODE from GPPAYCOMREF where PAYMENT_COMCODE = #{paymentCode}-->
                <!--)-->
            <!--</if>-->
            <!--<if test="upperCompanyCode != null and upperCompanyCode != ''">-->
                <!--and g.UPPER_COMPANY_CODE like concat(#{upperCompanyCode},'%')-->
            <!--</if>-->
            <!--<if test="validInd != null and validInd != ''">-->
                <!--and g.VALID_IND = #{validInd}-->
            <!--</if>-->
            <!--<if test="taxNumber != null and taxNumber != ''">-->
                <!--and g.tax_number like concat(#{taxNumber},'%')-->
            <!--</if>-->
        <!--</where>-->
    <!--</select>-->

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="searchPage" resultMap="BaseResultMap" parameterType="ins.channel.company.po.GgcompanySearch">
        select
        <include refid="Base_Column_List"/>
        from GGCOMPANY g
        <where>
            <if test="companyCode != null and companyCode != ''">
                and g.COMPANY_CODE  like concat('%',concat(#{companyCode},'%'))
            </if>
            <if test="companyCname != null and companyCname != ''">
                and g.COMPANY_CNAME like concat('%',concat(#{companyCname},'%'))
            </if>
            <if test="upperCompanyCode != null and upperCompanyCode != ''">
                and g.UPPER_COMPANY_CODE = #{upperCompanyCode}
                <!-- and g.UPPER_COMPANY_CODE like concat('%',#{upperCompanyCode},'%')-->
            </if>
            <if test="validInd != null and validInd != ''">
                and g.VALID_IND = #{validInd}
            </if>
            <if test="comLevel != null and comLevel != ''">
                and g.COM_LEVEL != #{comLevel}
            </if>
        </where>
    </select>


    <resultMap id="BaseCodeResultMap" type="ins.channel.baseCode.po.BaseCode">
        <result column="CODE_TYPE" property="codeType"/>
        <result column="CODE_CODE" property="codeCode"/>
        <result column="CODE_NAME" property="codeName"/>
    </resultMap>
<!--Modify By Zhoutaoyu 查询所有出单机构信息(供基础码表查询使用) 2019/11/20-->
    <select id="queryAll" resultMap="BaseCodeResultMap">
        SELECT T.COMPANY_CODE AS CODE_CODE,
                      T.COMPANY_CNAME AS CODE_NAME,
                      'companyCode' AS CODE_TYPE
        FROM GGCOMPANY T
        WHERE T.VALID_IND = '1'
        ORDER BY T.COMPANY_CODE
    </select>

    <select id="selectByCondition" resultType="ins.channel.company.po.Ggcompany" parameterType="map">
        SELECT
        <include refid="Base_Column_List"/>
        FROM GGCOMPANY
        <where>
            <include refid="Base_Select_By_Entity_Where"/>
        </where>
    </select>


    <select id="selectUpperNameByUpperCode" resultType="ins.channel.company.po.Ggcompany">
        select
        <include refid="Base_Column_List"/>
         from GGCOMPANY
        WHERE COMPANY_CODE = #{upperCompanyCode}
    </select>
</mapper>
