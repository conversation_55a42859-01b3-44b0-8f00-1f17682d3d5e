<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.currency.dao.GgcurrencyDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.currency.dao.GgcurrencyDao"/>
    <!-- 请在下方添加自定义配置-->

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="searchPage" resultMap="BaseResultMap" parameterType="ins.channel.currency.po.Ggcurrency">
        select
        <include refid="Base_Column_List"/>
        from GGCURRENCY
        <where>
            <if test="currencyCode != null and currencyCode != ''">
                and CURRENCY_CODE like concat(#{currencyCode},'%')
            </if>
            <if test="currencyCname != null and currencyCname != ''">
                and CURRENCY_CNAME like concat(#{currencyCname},'%')
            </if>
            <if test="validind != null and validind != ''">
                and VALID_IND = #{validind}
            </if>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                and MODIFIED_TIME between to_date(concat(#{startTime},' 00:00:00'),'YYYY-MM-DD HH24:MI:SS') and
                to_date(concat(#{endTime},' 23:59:59'),'YYYY-MM-DD HH24:MI:SS')
            </if>
        </where>
    </select>
    <resultMap id="CurrencyForSelect" type="ins.channel.currency.po.GgcurrencySelect">
        <result column="CURRENCY_CODE" property="currencyCode" />
        <result column="CURRENCY_CNAME" property="currencyCname" />
    </resultMap>
    <!-- COMPANY_CODE ,COMPANY_CNAME 进行分页查询 -->
    <select id="currencyForSelectPage" resultMap="CurrencyForSelect" parameterType="java.lang.String">
        select
        CURRENCY_CODE,CURRENCY_CNAME
        from GGCURRENCY
        <where>
            VALID_IND = '1'
            <if test="queryInfo != null and queryInfo != ''">
                and (CURRENCY_CODE like concat(concat('%',#{queryInfo}),'%')
                or CURRENCY_CNAME like concat(concat('%',#{queryInfo}),'%'))
            </if>
        </where>
    </select>

    <!-- COMPANY_CODE ,COMPANY_CNAME 进行分页查询 -->
    <select id="currencyForSelect" resultMap="CurrencyForSelect" parameterType="java.lang.String">
        select
        CURRENCY_CODE,CURRENCY_CNAME
        from GGCURRENCY
        <where>
            VALID_IND = '1'
        </where>
        order by CURRENCY_CODE
    </select>

    <!-- 完整插入一条记录-->
    <insert id="insertAuto" parameterType="ins.channel.currency.po.Ggcurrency">
        insert into GGCURRENCY (GID,
                                CURRENCY_CODE,
                                CURRENCY_CNAME,
                                CURRENCY_TNAME,
                                CURRENCY_ENAME,
                                CREATOR_CODE,
                                UPDATER_CODE,
                                VALID_DATE,
                                INVALID_DATE,
                                VALID_IND,
                                REMARK,
                                FLAG,
                                CREATE_BY,
                                CREATE_TIME,
                                MODIFIED_BY,
                                MODIFIED_TIME)
        values (seq_GGCURRENCY.nextval,
                #{currencyCode},
                #{currencyCname},
                #{currencyTname},
                #{currencyEname},
                #{creatorCode},
                #{updaterCode},
                #{validDate},
                #{invalidDate},
                #{validInd},
                #{remark},
                #{flag},
                #{createBy},
                #{createTime},
                #{modifiedBy},
                #{modifiedTime})
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelectiveAuto" parameterType="ins.channel.currency.po.Ggcurrency">
        insert into GGCURRENCY
        <trim prefix="(" suffix=")" suffixOverrides=",">
                GID,
            <if test="currencyCode != null">
                CURRENCY_CODE,
            </if>
            <if test="currencyCname != null">
                CURRENCY_CNAME,
            </if>
            <if test="currencyTname != null">
                CURRENCY_TNAME,
            </if>
            <if test="currencyEname != null">
                CURRENCY_ENAME,
            </if>
            <if test="creatorCode != null">
                CREATOR_CODE,
            </if>
            <if test="updaterCode != null">
                UPDATER_CODE,
            </if>
            <if test="validDate != null">
                VALID_DATE,
            </if>
            <if test="invalidDate != null">
                INVALID_DATE,
            </if>
            <if test="validInd != null">
                VALID_IND,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="flag != null">
                FLAG,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="modifiedBy != null">
                MODIFIED_BY,
            </if>
            <if test="modifiedTime != null">
                MODIFIED_TIME
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            seq_GGCURRENCY.nextval,
            <if test="currencyCode != null">
                #{currencyCode},
            </if>
            <if test="currencyCname != null">
                #{currencyCname},
            </if>
            <if test="currencyTname != null">
                #{currencyTname},
            </if>
            <if test="currencyEname != null">
                #{currencyEname},
            </if>
            <if test="creatorCode != null">
                #{creatorCode},
            </if>
            <if test="updaterCode != null">
                #{updaterCode},
            </if>
            <if test="validDate != null">
                #{validDate},
            </if>
            <if test="invalidDate != null">
                #{invalidDate},
            </if>
            <if test="validInd != null">
                #{validInd},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="flag != null">
                #{flag},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="modifiedBy != null">
                #{modifiedBy},
            </if>
            <if test="modifiedTime != null">
                #{modifiedTime}
            </if>
        </trim>
    </insert>

    <!-- 将CodeCode转换成相应文字 -->
    <select id="translate" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from GGCURRENCY
        <where>
            VALID_IND = '1'
            and CURRENCY_CODE = #{param1}
        </where>
    </select>


    <select id="translateFromCName" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from GGCURRENCY
        <where>
            VALID_IND = '1'
            and CURRENCY_CNAME = #{param1}
        </where>
    </select>
</mapper>
