<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.exch.dao.GpexchDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.exch.dao.GpexchDao"/>
    <!-- 请在下方添加自定义配置-->

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="searchPage" resultMap="BaseResultMap" parameterType="ins.channel.exch.po.GpexchSearch">
        select
        <include refid="Base_Column_List"/>
        from GPEXCH
        <where>
            <if test="baseCurrency != null and baseCurrency != ''">
                and BASE_CURRENCY = #{baseCurrency}
            </if>
            <if test="exchCurrency != null and exchCurrency != ''">
                and EXCH_CURRENCY = #{exchCurrency}
            </if>
            <if test="validInd != null and validInd != ''">
                and VALID_IND = #{validInd}
            </if>
            <if test="flag != null and flag != ''">
                and nvl(flag,0) = #{flag}
            </if>
            <if test="exchDate != null and exchDate != ''">
                and EXCH_DATE = to_date(#{exchDate},'YYYY-MM-DD')
            </if>
            <if test="sendMqStatus != null and sendMqStatus != ''">
                and SEND_MQ_STATUS = #{sendMqStatus}
            </if>

        </where>
        order by EXCH_DATE desc
    </select>

    <!-- 完整插入一条记录-->
    <insert id="insertAuto" parameterType="ins.channel.exch.po.Gpexch">
        insert into GPEXCH (GID,
                            EXCH_DATE,
                            BASE,
                            BASE_CURRENCY,
                            EXCH_CURRENCY,
                            EXCH_RATE,
                            VALID_IND,
                            REMARK,
                            FLAG,
                            EXCH_TYPE,
                            CREATE_TIME,
                            MODIFIED_TIME)
        values (SEQ_GPEXCH.nextval,
                #{exchDate},
                #{base},
                #{baseCurrency},
                #{exchCurrency},
                #{exchRate},
                #{validInd},
                #{remark},
                #{flag},
                #{exchType},
                #{createTime},
                #{modifiedTime})
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelectiveAuto" parameterType="ins.channel.exch.po.Gpexch" useGeneratedKeys="true" keyProperty="gid" keyColumn="gid">
        <selectKey resultType="java.lang.String" keyProperty="gid" order="BEFORE">
            select to_char(SEQ_GPEXCH.nextval) gid from dual
        </selectKey>
        insert into GPEXCH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            GID,
            <if test="exchDate != null">
                EXCH_DATE,
            </if>
            <if test="base != null">
                BASE,
            </if>
            <if test="baseCurrency != null">
                BASE_CURRENCY,
            </if>
            <if test="exchCurrency != null">
                EXCH_CURRENCY,
            </if>
            <if test="exchRate != null">
                EXCH_RATE,
            </if>
            <if test="validInd != null">
                VALID_IND,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="flag != null">
                FLAG,
            </if>
            <if test="exchType != null">
                EXCH_TYPE,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="modifiedTime != null">
                MODIFIED_TIME,
            </if>
            <if test="sendMqStatus != null">
                SEND_MQ_STATUS
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{gid},
            <if test="exchDate != null">
                #{exchDate},
            </if>
            <if test="base != null">
                #{base},
            </if>
            <if test="baseCurrency != null">
                #{baseCurrency},
            </if>
            <if test="exchCurrency != null">
                #{exchCurrency},
            </if>
            <if test="exchRate != null">
                #{exchRate},
            </if>
            <if test="validInd != null">
                #{validInd},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="flag != null">
                #{flag},
            </if>
            <if test="exchType != null">
                #{exchType},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="modifiedTime != null">
                #{modifiedTime},
            </if>
            <if test="sendMqStatus != null">
                #{sendMqStatus}
            </if>
        </trim>
    </insert>

    <!--查询小于总换日期的数据-->
    <select id="getGtExchDateList" resultMap="BaseResultMap" parameterType="ins.channel.exch.po.Gpexch">
        select
        <include refid="Base_Column_List" />
        from GPEXCH where 1=1
        <if test="gid != null and gid != ''" >
            and GID = #{gid}
        </if>
        <if test="exchDate != null and exchDate != ''" >
            and EXCH_DATE = #{exchDate}
        </if>
        <if test="base != null and base != ''" >
            and BASE = #{base}
        </if>
        <if test="baseCurrency != null and baseCurrency != ''" >
            and BASE_CURRENCY = #{baseCurrency}
        </if>
        <if test="exchCurrency != null and exchCurrency != ''" >
            and EXCH_CURRENCY = #{exchCurrency}
        </if>
        <if test="exchRate != null and exchRate != ''" >
            and EXCH_RATE &gt;= #{exchRate}
        </if>
        <if test="validInd != null and validInd != ''" >
            and VALID_IND = #{validInd}
        </if>
        <if test="remark != null and remark != ''" >
            and REMARK = #{remark}
        </if>
        <if test="flag != null and flag != ''" >
            and FLAG = #{flag}
        </if>
        <if test="exchType != null and exchType != ''" >
            and EXCH_TYPE = #{exchType}
        </if>
        <if test="createTime != null and createTime != ''" >
            and CREATE_TIME = #{createTime}
        </if>
        <if test="modifiedTime != null and modifiedTime != ''" >
            and MODIFIED_TIME = #{modifiedTime}
        </if>
        order by exch_Date desc
    </select>

    <!-- 查询当前汇率 -->
    <select id="queryExchRate" resultType="java.math.BigDecimal" parameterType="map">
        select EXCH_RATE
        from (
            select EXCH_RATE
            from GPEXCH
            where VALID_IND = 1
            and BASE_CURRENCY = #{base}
            and EXCH_CURRENCY = #{exch}
            and EXCH_TYPE = #{exchType}
            and EXCH_DATE = (
                select max (EXCH_DATE)
                from GPEXCH
                where VALID_IND = 1
                and BASE_CURRENCY = #{base}
                and EXCH_CURRENCY = #{exch}
                and EXCH_TYPE = #{exchType}
                and EXCH_DATE &lt;= to_date(#{date}, 'YYYY-MM-DD')
            )
        )
        where rownum = 1
    </select>
    <update id="updateSendMqFlag">
        update gpexch set Send_Mq_Status = '1' where Send_Mq_Status = '0'
    </update>
</mapper>
