<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.feetypecode.dao.GpfeetypecodeDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.feetypecode.dao.GpfeetypecodeDao"/>
    <!-- 请在下方添加自定义配置-->
    <!-- 按对象查询一页记录（多条记录） -->
    <select id="searchPage" resultMap="BaseResultMap" parameterType="ins.channel.feetypecode.po.Gpfeetypecode">
        select
        <include refid="Base_Column_List"/>
        from GPFEETYPECODE
        <where>
            <if test="feeTypeCode != null and feeTypeCode != ''">
                and FEE_TYPE_CODE = #{feeTypeCode}
            </if>
            <if test="feeTypeCodeCname != null and feeTypeCodeCname != ''">
                and FEE_TYPE_CODE_CNAME = #{feeTypeCodeCname}
            </if>
            <if test="validind != null and validind != ''">
                and VALIDIND = #{validind}
            </if>
			<if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
				and MODIFIED_TIME between to_date(concat(#{startTime},' 00:00:00'),'YYYY-MM-DD HH24:MI:SS') and
				to_date(concat(#{endTime},' 23:59:59'),'YYYY-MM-DD HH24:MI:SS')
			</if>
        </where>

    </select>
    <!-- 完整插入一条记录-->
    <insert id="insertAuto" parameterType="ins.channel.feetypecode.po.Gpfeetypecode">
        insert into GPFEETYPECODE (
                                   FEE_TYPE_CODE,
                                    FEE_TYPE_CODE_CNAME,
                                    FEE_TYPE_CODE_TNAME,
                                    FEE_TYPE_CODE_ENAME,
                                    CAL_SIGN,
                                    FEE_TYPE_CODE_ACC,
                                    VALIDIND,
                                    FLAG,
                                    REMARK,
                                    CREATE_TIME,
                                    MODIFIED_TIME)
        values (
                #{feeTypeCode},
                #{feeTypeCodeCname},
                #{feeTypeCodeTname},
                #{feeTypeCodeEname},
                #{calSign},
                #{feeTypeCodeAcc},
                #{validind},
                #{flag},
                #{remark},
                #{createTime},
                #{modifiedTime})
    </insert>


</mapper>
