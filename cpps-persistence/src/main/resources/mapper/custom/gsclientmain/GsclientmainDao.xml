<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gsclientmain.dao.GsclientmainDao">
	<!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
	<cache-ref  namespace="ins.channel.gsclientmain.dao.GsclientmainDao"/>
	<!-- 请在下方添加自定义配置-->
	<resultMap id="BaseCodeResultMap" type="ins.channel.baseCode.po.BaseCode">
		<result column="CODE_TYPE" property="codeType" />
		<result column="CODE_CODE" property="codeCode" />
		<result column="CODE_NAME" property="codeName" />
	</resultMap>
	<!--Modify By Zhoutaoyu 查询所有所属公司信息(供基础码表查询使用) 2021/02/22-->
	<select id="queryAll" resultMap="BaseCodeResultMap">
		select
			COMPANY_CODE AS CODE_CODE,
			COMPANY_CNAME AS CODE_NAME,
			'CLIENT' AS CODE_TYPE
		from GGCOMPANY where VALID_IND = '1'
	</select>

	<select id="translate" resultType="java.lang.String">
		select
			COMPANY_CNAME AS CODE_NAME
		from GGCOMPANY where VALID_IND = '1'
		and COMPANY_CODE = #{clientCode}
	</select>

</mapper>
