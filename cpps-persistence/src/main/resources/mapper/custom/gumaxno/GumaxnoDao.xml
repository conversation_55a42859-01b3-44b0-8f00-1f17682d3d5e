<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gumaxno.dao.GumaxnoDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.gumaxno.dao.GumaxnoDao"/>
    <!-- 请在下方添加自定义配置-->
    <select id="selectByCondition" resultType="ins.channel.gumaxno.po.Gumaxno">
        <include refid="Base_Select_By_Entity"/>
        for UPDATE nowait
    </select>

    <update id="updateSelectiveByPolicyNoAndStatus">
        update GUMAXNO
        <set>
            <if test="maxno.versionNo != null">
                VERSIONNO=#{maxno.versionNo},
            </if>
            <if test="maxno.status != null">
                STATUS=#{maxno.status},
            </if>
        </set>
        where POLICYNO = #{maxno.policyNo}
        and STATUS=#{oldStatus}
        and FLAG = #{maxno.flag}
    </update>

</mapper>
