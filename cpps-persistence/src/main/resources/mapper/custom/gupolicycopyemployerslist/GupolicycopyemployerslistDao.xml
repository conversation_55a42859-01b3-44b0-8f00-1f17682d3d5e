<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicycopyemployerslist.dao.GupolicycopyemployerslistDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.gupolicycopyemployerslist.dao.GupolicycopyemployerslistDao"/>
    <!-- 请在下方添加自定义配置-->
    <select id="selectByCondition" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity"/>
        order by ENDDATE desc
    </select>

    <select id="searchBypolicyNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from GUPOLICYCOPYEMPLOYERSLIST
        <where>
            <if test="policyNo != null and policyNo != ''" >
                and POLICYNO = #{policyNo}
            </if>
            <if test="targetflag != null and targetflag != ''" >
                and TARGETFLAG != #{targetflag}
            </if>
            <if test="empname != null and empname != ''" >
                and EMPNAME = #{empname}
            </if>
            <if test="empidentifynumber != null and empidentifynumber != ''" >
                and EMPIDENTIFYNUMBER = #{empidentifynumber}
            </if>
        </where>
        order by ENDDATE desc
    </select>

    <select id="selectByConditionForPage" resultMap="BaseResultMap">
        select
            e.EMPNAME,
            e.EMPSEX,
            e.EMPBIRTHDAY,
            e.EMPIDENTIFYTYPE,
            e.EMPIDENTIFYNUMBER,
            e.OCCUPATIONNAME,
            e.ENTRYDATE,
            e.RESIGNATIONDATE,
            m.ACCEPTDATE as INPUTDATE,
            e.EFFECTIVEDATE,
            case e.TARGETFLAG when 'I' then '增员' when 'D' then '增员' when 'U' then '减员' end as TARGETFLAG,
            e.EMPPREMIUM,
            e.ENDDATE,
            m.companyname as LISTBELONGIND
        from
            gupolicycopymain m,
            GUPOLICYCOPYEMPLOYERSLIST e
        where m.ENDORNO = e.ENDORNO
          and e.ENDORNO = #{endorNo}
        order by e.ENDDATE desc,e.ID
    </select>


    <sql id="Select_By_EditType_Where">
        <if test="id != null and id != ''">
            and ID = #{id}
        </if>
        <if test="policyNo != null and policyNo != ''">
            and POLICYNO = #{policyNo}
        </if>
        <if test="endorNo != null and endorNo != ''">
            and ENDORNO = #{endorNo}
        </if>
        <if test="subpolicyno != null and subpolicyno != ''">
            and SUBPOLICYNO = #{subpolicyno}
        </if>
        <if test="planid != null and planid != ''">
            and PLANID = #{planid}
        </if>
        <if test="itemNo != null and itemNo != ''">
            and ITEMNO = #{itemNo}
        </if>
        <if test="itemdetailno != null and itemdetailno != ''">
            and ITEMDETAILNO = #{itemdetailno}
        </if>
        <if test="dynamictargettype != null and dynamictargettype != ''">
            and DYNAMICTARGETTYPE = #{dynamictargettype}
        </if>
        <if test="listseqno != null and listseqno != ''">
            and LISTSEQNO = #{listseqno}
        </if>
        <if test="empname != null and empname != ''">
            and EMPNAME = #{empname}
        </if>
        <if test="empsex != null and empsex != ''">
            and EMPSEX = #{empsex}
        </if>
        <if test="empbirthday != null and empbirthday != ''">
            and EMPBIRTHDAY = #{empbirthday}
        </if>
        <if test="empidentifytype != null and empidentifytype != ''">
            and EMPIDENTIFYTYPE = #{empidentifytype}
        </if>
        <if test="empidentifynumber != null and empidentifynumber != ''">
            and EMPIDENTIFYNUMBER = #{empidentifynumber}
        </if>
        <if test="occupationCode != null and occupationCode != ''">
            and OCCUPATIONCODE = #{occupationCode}
        </if>
        <if test="occupationname != null and occupationname != ''">
            and OCCUPATIONNAME = #{occupationname}
        </if>
        <if test="occupationlevel != null and occupationlevel != ''">
            and OCCUPATIONLEVEL = #{occupationlevel}
        </if>
        <if test="entrydate != null and entrydate != ''">
            and ENTRYDATE = #{entrydate}
        </if>
        <if test="resignationdate != null and resignationdate != ''">
            and RESIGNATIONDATE = #{resignationdate}
        </if>
        <if test="effectivedate != null and effectivedate != ''">
            and EFFECTIVEDATE = #{effectivedate}
        </if>
        <if test="endDate != null and endDate != ''">
            and ENDDATE = #{endDate}
        </if>
        <if test="currency != null and currency != ''">
            and CURRENCY = #{currency}
        </if>
        <if test="monthPay != null and monthPay != ''">
            and MONTHPAY = #{monthPay}
        </if>
        <if test="empinsured != null and empinsured != ''">
            and EMPINSURED = #{empinsured}
        </if>
        <if test="emppremium != null and emppremium != ''">
            and EMPPREMIUM = #{emppremium}
        </if>
        <if test="changeempinsured != null and changeempinsured != ''">
            and CHANGEEMPINSURED = #{changeempinsured}
        </if>
        <if test="changeemppremium != null and changeemppremium != ''">
            and CHANGEEMPPREMIUM = #{changeemppremium}
        </if>
        <if test="provincecode != null and provincecode != ''">
            and PROVINCECODE = #{provincecode}
        </if>
        <if test="citycode != null and citycode != ''">
            and CITYCODE = #{citycode}
        </if>
        <if test="countycode != null and countycode != ''">
            and COUNTYCODE = #{countycode}
        </if>
        <if test="moneylaunderingind != null and moneylaunderingind != ''">
            and MONEYLAUNDERINGIND = #{moneylaunderingind}
        </if>
        <if test="listbelongind != null and listbelongind != ''">
            and LISTBELONGIND = #{listbelongind}
        </if>
        <if test="inputDate != null and inputDate != ''">
            and INPUTDATE = #{inputDate}
        </if>
        <if test="updatesysdate != null and updatesysdate != ''">
            and UPDATESYSDATE = #{updatesysdate}
        </if>
        and TARGETFLAG = 'I' or TARGETFLAG = 'U'
    </sql>
    <select id="selectByEditType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from GUPOLICYCOPYEMPLOYERSLIST
        <where>
            <include refid="Select_By_EditType_Where"/>
        </where>
    </select>

    <delete id="deleteByEndorNo">
        delete from GUPOLICYCOPYEMPLOYERSLIST
        where ENDORNO = #{endorNo}
    </delete>

    <delete id="deleteByPolicyNo" parameterType="string">
        delete from GUPOLICYCOPYEMPLOYERSLIST
        where policyNo = #{policyNo}
    </delete>

    <!--修改保单雇员清单轨迹表-->
    <update id="updateEndorNo">
      UPDATE gupolicycopyemployerslist b SET b.targetflag = 'I' WHERE b.empidentifynumber IN
        (SELECT a.empidentifynumber FROM gupolicycopyemployerslist a
            WHERE a.endorno = #{endorNo} AND a.targetflag = 'U')
        AND b.targetflag = 'D'
    </update>
</mapper>
