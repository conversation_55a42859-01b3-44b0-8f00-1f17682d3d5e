<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicycopyemployersplan.dao.GupolicycopyemployersplanDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.gupolicycopyemployersplan.dao.GupolicycopyemployersplanDao"/>
    <!-- 请在下方添加自定义配置-->
    <sql id="Select_By_Targetcalculate_Where">
        <if test="id != null and id != ''">
            and ID = #{id}
        </if>
        <if test="policyNo != null and policyNo != ''">
            and POLICYNO = #{policyNo}
        </if>
        <if test="endorNo != null and endorNo != ''">
            and ENDORNO = #{endorNo}
        </if>
        <if test="subpolicyno != null and subpolicyno != ''">
            and SUBPOLICYNO = #{subpolicyno}
        </if>
        <if test="planid != null and planid != ''">
            and PLANID = #{planid}
        </if>
        <if test="plancode != null and plancode != ''">
            and PLANCODE = #{plancode}
        </if>
        <if test="riskCode != null and riskCode != ''">
            and RISKCODE = #{riskCode}
        </if>
        <if test="itemNo != null and itemNo != ''">
            and ITEMNO = #{itemNo}
        </if>
        <if test="itemCode != null and itemCode != ''">
            and ITEMCODE = #{itemCode}
        </if>
        <if test="itemdetailno != null and itemdetailno != ''">
            and ITEMDETAILNO = #{itemdetailno}
        </if>
        <if test="currency != null and currency != ''">
            and CURRENCY = #{currency}
        </if>
        <if test="industrytype != null and industrytype != ''">
            and INDUSTRYTYPE = #{industrytype}
        </if>
        <if test="industrydetail != null and industrydetail != ''">
            and INDUSTRYDETAIL = #{industrydetail}
        </if>
        <if test="schemecode != null and schemecode != ''">
            and SCHEMECODE = #{schemecode}
        </if>
        <if test="schemename != null and schemename != ''">
            and SCHEMENAME = #{schemename}
        </if>
        <if test="personneltype != null and personneltype != ''">
            and PERSONNELTYPE = #{personneltype}
        </if>
        <if test="dynamictargettype != null and dynamictargettype != ''">
            and DYNAMICTARGETTYPE = #{dynamictargettype}
        </if>
        <if test="listseqno != null and listseqno != ''">
            and LISTSEQNO = #{listseqno}
        </if>
        <if test="targetcalculate != null and targetcalculate != ''">
            and TARGETCALCULATE = #{targetcalculate}
        </if>
        <if test="employees != null and employees != ''">
            and EMPLOYEES = #{employees}
        </if>
        <if test="employeesthischange != null and employeesthischange != ''">
            and EMPLOYEESTHISCHANGE = #{employeesthischange}
        </if>
        <if test="legalfeeslimit != null and legalfeeslimit != ''">
            and LEGALFEESLIMIT = #{legalfeeslimit}
        </if>
        <if test="legalfeesrate != null and legalfeesrate != ''">
            and LEGALFEESRATE = #{legalfeesrate}
        </if>
        <if test="legalsumpremium != null and legalsumpremium != ''">
            and LEGALSUMPREMIUM = #{legalsumpremium}
        </if>
        <if test="changelegalsumpremium != null and changelegalsumpremium != ''">
            and CHANGELEGALSUMPREMIUM = #{changelegalsumpremium}
        </if>
        <if test="personcasualtiesrate != null and personcasualtiesrate != ''">
            and PERSONCASUALTIESRATE = #{personcasualtiesrate}
        </if>
        <if test="personmedicallimit != null and personmedicallimit != ''">
            and PERSONMEDICALLIMIT = #{personmedicallimit}
        </if>
        <if test="personmedicalrate != null and personmedicalrate != ''">
            and PERSONMEDICALRATE = #{personmedicalrate}
        </if>
        <if test="fieldalimit != null and fieldalimit != ''">
            and FIELDALIMIT = #{fieldalimit}
        </if>
        <if test="fieldarate != null and fieldarate != ''">
            and FIELDARATE = #{fieldarate}
        </if>
        <if test="fieldblimit != null and fieldblimit != ''">
            and FIELDBLIMIT = #{fieldblimit}
        </if>
        <if test="fieldbrate != null and fieldbrate != ''">
            and FIELDBRATE = #{fieldbrate}
        </if>
        <if test="fieldc != null and fieldc != ''">
            and FIELDC = #{fieldc}
        </if>
        <if test="field != null and field != ''">
            and FIELD = #{field}
        </if>
        <if test="personsumpremium != null and personsumpremium != ''">
            and PERSONSUMPREMIUM = #{personsumpremium}
        </if>
        <if test="changepersonsumpremium != null and changepersonsumpremium != ''">
            and CHANGEPERSONSUMPREMIUM = #{changepersonsumpremium}
        </if>
        <if test="listbelongind != null and listbelongind != ''">
            and LISTBELONGIND = #{listbelongind}
        </if>
        <if test="inputDate != null and inputDate != ''">
            and INPUTDATE = #{inputDate}
        </if>
        <if test="updatesysdate != null and updatesysdate != ''">
            and UPDATESYSDATE = #{updatesysdate}
        </if>
        and TARGETFLAG = 'I' or TARGETFLAG = 'U'
    </sql>
    <select id="selectByCondition" resultMap="BaseResultMap">
      <include refid="Base_Select_By_Entity"/>
    </select>

    <delete id="deleteByEndorNo">
        delete from GUPOLICYCOPYEMPLOYERSPLAN
        where ENDORNO = #{endorNo}
    </delete>

    <delete id="deleteByPolicyNo" parameterType="string">
        delete from GUPOLICYCOPYEMPLOYERSPLAN
        where policyNo = #{policyNo}
    </delete>
</mapper>
