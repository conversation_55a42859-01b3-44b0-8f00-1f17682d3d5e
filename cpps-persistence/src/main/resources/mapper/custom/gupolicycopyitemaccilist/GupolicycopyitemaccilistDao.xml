<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicycopyitemaccilist.dao.GupolicycopyitemaccilistDao">
    <!-- 开启二级缓存},#{item.item.在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.gupolicycopyitemaccilist.dao.GupolicycopyitemaccilistDao"/>
    <!-- 请在下方添加自定义配置-->

    <delete id="deleteByPolicyNo" parameterType="String">
		delete from GUPOLICYCOPYITEMACCILIST where policyNo=#{item.policyNo}
	</delete>

    <insert id="batchInsert">
        INSERT ALL
        <foreach collection="list" item="item" separator="">
            INTO GUPOLICYCOPYITEMACCILIST VALUES
            (#{item.id},#{item.policyNo},
            #{item.endorseqno},
            #{item.itemNo},
            #{item.itemCode},
            #{item.itemName},
            #{item.itemdetailno},
            #{item.itemdetailcode},
            #{item.itemDetailName},
            #{item.clientno},
            #{item.clientcode},
            #{item.memberno},
            #{item.clientcname},
            #{item.clientename},
            #{item.sex},
            #{item.birthday},
            #{item.age},
            #{item.identifytypea},
            #{item.identifynoa},
            #{item.identifytypeb},
            #{item.identifynob},
            #{item.enrollmentdate},
            #{item.startDate},
            #{item.endDate},
            #{item.bankName},
            #{item.bankaccountno},
            #{item.creditNo},
            #{item.creditexpiry},
            #{item.occupationtype},
            #{item.occupation},
            #{item.occupationCode},
            #{item.jobTitle},
            #{item.homeAddress},
            #{item.hometel},
            #{item.businessNature},
            #{item.jobunitcode},
            #{item.jobunitname},
            #{item.officetel},
            #{item.employername},
            #{item.employeeind},
            #{item.maternityind},
            #{item.autopayind},
            #{item.relationCode},
            #{item.relationship},
            #{item.projectcode},
            #{item.uwcount},
            #{item.suminsured},
            #{item.basePremium},
            #{item.netPremium},
            #{item.grosspremium},
            #{item.annualpremium},
            #{item.proratapremium},
            #{item.exprirefund},
            #{item.preexistind},
            #{item.activeind},
            #{item.commencedate},
            #{item.email},
            #{item.district},
            #{item.ipaservice},
            #{item.countryCode},
            #{item.registaddress},
            #{item.memberrefrence},
            #{item.discount},
            #{item.specialclause},
            #{item.endorind},
            #{item.remark},
            #{item.flag},
            #{item.journeystart},
            #{item.journeyend},
            #{item.journeyback},
            #{item.applirelation},
            #{item.linkerName},
            #{item.linkerphone},
            #{item.innerremark},
            #{item.pledged},
            #{item.claimpayway},
            #{item.clienttype},
            #{item.occupationtypename},
            #{item.occupationlevel},
            #{item.maininsuredind},
            #{item.plancode},
            #{item.riskCode},
            #{item.surname},
            #{item.moniker},
            #{item.firstname},
            #{item.lastname},
            #{item.provincecode},
            #{item.citycode},
            #{item.countycode},
            #{item.displayNo},
            #{item.inputDate},
            #{item.updatesysdate},
            #{item.endorflag},
            #{item.groupType},
            #{item.signature},
            #{item.itemAddress},
            #{item.useNatureCode},
            #{item.drivertype},
            #{item.driverlicensenoold},
            #{item.driverlicenseexpirationdate},
            #{item.benefitproject},
            #{item.socialsecurityinfo},
            #{item.drivingmodel},
            #{item.driverlicenseno},
            #{item.onthejobstatus},
            #{item.platcustomno},
            #{item.beifendescription},
            #{item.insuredName},
            #{item.insuredsex},
            #{item.insuredbirthday},
            #{item.identifytypec},
            #{item.identifynoc},
            #{item.insuredhomeaddress},
            #{item.insuredhometel},
            #{item.insuredPhone},
            #{item.insuredemail},
            #{item.insuredclass},
            #{item.school},
            #{item.jointinsuredflag},
            #{item.vaccination},
            #{item.inoculabilitytime},
            #{item.portabletype},
            #{item.brand},
            #{item.model},
            #{item.uniqueencoding},
            #{item.uploadtype})
        </foreach>
        select 1 from dual
    </insert>


</mapper>
