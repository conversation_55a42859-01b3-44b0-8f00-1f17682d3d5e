<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicycopymain.dao.GupolicycopymainDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.gupolicycopymain.dao.GupolicycopymainDao"/>
    <!-- 请在下方添加自定义配置-->

    <!-- 通用查询结果对象-->
    <resultMap id="ReqResultMap" type="ins.channel.gupolicycopymain.vo.DeclarationRespVo">
        <result column="POLICYNO" property="policyNo"/>
        <result column="LISTSEQNO" property="listSeqno"/>
        <result column="EMPNAME" property="empName"/>
        <result column="EMPIDENTIFYTYPE" property="empIdentifyType"/>
        <result column="EMPIDENTIFYNUMBER" property="empIdentifyNumber"/>
        <result column="PERSONCASUALTIESLIMIT" property="personcasualtieslimit"/>
        <result column="TARGETFLAG" property="targetFlag"/>
        <result column="ENTRYDATE" property="entryDate"/>
        <result column="EFFECTIVEDATE" property="effectiveDate"/>
        <result column="ENDDATE" property="endDate"/>
        <result column="ACCEPTDATE" property="acceptDate"/>
        <result column="MONTHPAY" property="monthPay"/>
        <result column="EMPPREMIUM" property="empPremium"/>
        <result column="APPLINAME" property="appliName"/>
        <result column="COMPANYNAME" property="companyName"/>
        <result column="UNDERWRITEIND" property="underWriteInd"/>
        <result column="ENDORNO" property="endorNo"/>
        <result column="PROJECTMANAGERNAME" property="projectManagerName"/>
        <result column="LASTMODIFYMANAGERNAME" property="lastModifyManagerName"/>
        <result column="CHANGEEMPPREMIUM" property="changeemppremium"/>
        <result column="RESIGNATIONDATE" property="resignationDate"/>
        <result column="PROJECT" property="project"/>
    </resultMap>
    <select id="pageQueryDeclarationByCondition" resultMap="ReqResultMap">
        SELECT GUCM.POLICYNO,<!--保单号码-->
        GUCEL.LISTSEQNO,<!--凭证号码-->
        GUCEL.EMPNAME,<!--被保险人名称-->
        GUCEL.EMPIDENTIFYTYPE,<!--证件类型-->
        GUCEL.EMPIDENTIFYNUMBER,<!--证件号码-->
        GUCEP.PERSONCASUALTIESLIMIT,<!--计划名称-->
        GUCEL.TARGETFLAG,<!--操作类型-->
        GUCEL.ENTRYDATE,<!--入职日期-->
        GUCEL.EFFECTIVEDATE,<!--生效日期-->
        GUCEL.ENDDATE,<!--到期日期-->
        GUCM.ACCEPTDATE,<!--申报日期-->
        GUCEL.MONTHPAY,<!--约定月薪-->
        GUCEL.EMPPREMIUM,<!--保费-每人毛保费-->
        GUCEL.CHANGEEMPPREMIUM,<!--每人应收保费-->
        GUCM.APPLINAME,<!--投保公司-投保人名称-->
        GUCM.COMPANYNAME, <!--关联机构-->
        GUCM.UNDERWRITEIND, <!--申报状态(核保标志)-->
        GUCM.ENDORNO, <!--申报单号-->
        GUCM.PROJECTMANAGERNAME, <!--归属业务员名称-->
        GUCM.LASTMODIFYMANAGERNAME,
        GUCEL.RESIGNATIONDATE <!--离职日期-->,
        GUCEL.PROJECT <!--项目-->
        FROM GUPOLICYCOPYMAIN GUCM
        RIGHT JOIN GUPOLICYCOPYEMPLOYERSLIST GUCEL ON GUCEL.POLICYNO=GUCM.POLICYNO
        AND GUCEL.ENDORNO=GUCM.ENDORNO
        AND (GUCEL.TARGETFLAG='I' OR GUCEL.TARGETFLAG='U' OR GUCEL.TARGETFLAG='E')
        LEFT JOIN GUPOLICYCOPYEMPLOYERSPLAN GUCEP ON GUCEP.POLICYNO=GUCM.POLICYNO
        AND GUCEP.ENDORNO=GUCM.ENDORNO
        AND GUCEP.ITEMNO=GUCEL.ITEMNO
        AND GUCEP.TARGETCALCULATE='1'
        <if test="isExcelBatchQueryFlag == 1">
            <!--判断是否符合批量查询条件-->
            LEFT JOIN GZDECLARATIONBATCHQUERYTEMP GZDBQT ON GZDBQT.EMPNAME=GUCEL.EMPNAME AND
            GZDBQT.EMPIDENTIFYNUMBER =GUCEL.EMPIDENTIFYNUMBER
        </if>
        <where>
            <if test="uuid != null and uuid != '' and isExcelBatchQueryFlag == 1"><!--查询批次号-->
                and GZDBQT.UUID = #{uuid}
            </if>
            <if test="projectManagerCode != null and projectManagerCode != ''">
                <!--用户职级为业务员增加限制查询条件-->
                and GUCM.PROJECTMANAGERCODE = #{projectManagerCode} and ( GUCM.LASTMODIFYMANAGERCODE = #{projectManagerCode} or GUCM.LASTMODIFYMANAGERCODE is null )
            </if>
            <if test="companyCode != null and companyCode != ''"><!--关联机构代码-->
                and GUCM.companyCode = #{companyCode}
            </if>
            <!--关联机构权限校验-->
            <if test="companyCodes != null and companyCodes.size() &gt; 0">
                and GUCM.companyCode IN
                <foreach collection="companyCodes" item="comCode" index="index" open="(" close=")" separator=",">
                    &apos;${comCode}&apos;
                    <!--  ${comCode}-->
                </foreach>
            </if>
            <!--			<if test="appliCode != null and appliCode != ''">&lt;!&ndash;投保人code&ndash;&gt;-->
            <!--				and GUCM.appliCode= #{appliCode}-->
            <!--			</if>-->
            <if test="appliName != null and appliName != ''"><!--投保人名称-->
                and GUCM.appliName like concat(concat('%',#{appliName}),'%')
            </if>
            <if test="projectManagerName != null and projectManagerName != ''"><!--投保人名称-->
                and GUCM.projectManagerName like concat(concat('%',#{projectManagerName}),'%')
            </if>
            <if test="policyNo != null and policyNo != ''"><!--保单号码-->
                and GUCM.policyNo like concat(concat('%',#{policyNo}),'%')
            </if>
            <if test="endorNo != null and endorNo != ''"><!--申报单号-->
                and GUCM.ENDORNO like concat(concat('%',#{endorNo}),'%')
            </if>
            <if test="productcode != null and productcode != ''"><!--保险编码-->
                and GUCM.PRODUCTCODE = #{productcode}
            </if>
            <if test="personcasualtieslimit != null and personcasualtieslimit != ''"><!--计划名称-责任限额-->
                and GUCEP.PERSONCASUALTIESLIMIT= #{personcasualtieslimit}
            </if>
            <if test="empName != null and empName != ''"><!--被保险人名称-->
                and GUCEL.empName like concat(concat('%',#{empName}),'%')
            </if>
            <if test="empIdentifyType != null and empIdentifyType != ''"><!--证件类型-->
                and GUCEL.empIdentifyType like concat(concat('%',#{empIdentifyType}),'%')
            </if>
            <if test="empIdentifyNumber != null and empIdentifyNumber != ''"><!--证件号码-->
                and GUCEL.empIdentifyNumber like concat(concat('%',#{empIdentifyNumber}),'%')
            </if>
            <if test="listSeqno != null and listSeqno != ''"><!--凭证号码-->
                and GUCEL.LISTSEQNO like concat(concat('%',#{listSeqno}),'%')
            </if>
            <if test="targetFlag != null and targetFlag != ''"><!--操作类型-->
                and GUCEL.targetFlag like concat(concat('%',#{targetFlag}),'%')
            </if>
            <if test="effectiveDateStart != null and effectiveDateEnd != null"><!--生效日期-->
                AND to_date(to_char(GUCEL.effectiveDate,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{effectiveDateStart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{effectiveDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>
            <if test="endDateStart != null and endDateEnd != null"><!--到期日期-->
                AND to_date(to_char(GUCEL.endDate,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{endDateStart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{endDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>
            <if test="acceptDateStart != null and acceptDateEnd != null"><!--申报日期-->
                AND to_date(to_char(GUCM.acceptDate,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{acceptDateStart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{acceptDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>
           or  GUCM.projectmanagercode = '0'
            <if test="uuid != null and uuid != '' and isExcelBatchQueryFlag == 1"><!--查询批次号-->
                and GZDBQT.UUID = #{uuid}
            </if>
            <if test="companyCode != null and companyCode != ''"><!--关联机构代码-->
                and GUCM.companyCode = #{companyCode}
            </if>
            <!--关联机构权限校验-->
            <if test="companyCodes != null and companyCodes.size() &gt; 0">
                and GUCM.companyCode IN
                <foreach collection="companyCodes" item="comCode" index="index" open="(" close=")" separator=",">
                    &apos;${comCode}&apos;
                    <!--  ${comCode}-->
                </foreach>
            </if>
            <!--			<if test="appliCode != null and appliCode != ''">&lt;!&ndash;投保人code&ndash;&gt;-->
            <!--				and GUCM.appliCode= #{appliCode}-->
            <!--			</if>-->
            <if test="appliName != null and appliName != ''"><!--投保人名称-->
                and GUCM.appliName like concat(concat('%',#{appliName}),'%')
            </if>
            <if test="projectManagerName != null and projectManagerName != ''"><!--投保人名称-->
                and GUCM.projectManagerName like concat(concat('%',#{projectManagerName}),'%')
            </if>
            <if test="policyNo != null and policyNo != ''"><!--保单号码-->
                and GUCM.policyNo like concat(concat('%',#{policyNo}),'%')
            </if>
            <if test="endorNo != null and endorNo != ''"><!--申报单号-->
                and GUCM.ENDORNO like concat(concat('%',#{endorNo}),'%')
            </if>
            <if test="productcode != null and productcode != ''"><!--保险编码-->
                and GUCM.PRODUCTCODE = #{productcode}
            </if>
            <if test="personcasualtieslimit != null and personcasualtieslimit != ''"><!--计划名称-责任限额-->
                and GUCEP.PERSONCASUALTIESLIMIT= #{personcasualtieslimit}
            </if>
            <if test="empName != null and empName != ''"><!--被保险人名称-->
                and GUCEL.empName like concat(concat('%',#{empName}),'%')
            </if>
            <if test="empIdentifyType != null and empIdentifyType != ''"><!--证件类型-->
                and GUCEL.empIdentifyType like concat(concat('%',#{empIdentifyType}),'%')
            </if>
            <if test="empIdentifyNumber != null and empIdentifyNumber != ''"><!--证件号码-->
                and GUCEL.empIdentifyNumber like concat(concat('%',#{empIdentifyNumber}),'%')
            </if>
            <if test="listSeqno != null and listSeqno != ''"><!--凭证号码-->
                and GUCEL.LISTSEQNO like concat(concat('%',#{listSeqno}),'%')
            </if>
            <if test="targetFlag != null and targetFlag != ''"><!--操作类型-->
                and GUCEL.targetFlag like concat(concat('%',#{targetFlag}),'%')
            </if>
            <if test="effectiveDateStart != null and effectiveDateEnd != null"><!--生效日期-->
                AND to_date(to_char(GUCEL.effectiveDate,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{effectiveDateStart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{effectiveDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>
            <if test="endDateStart != null and endDateEnd != null"><!--到期日期-->
                AND to_date(to_char(GUCEL.endDate,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{endDateStart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{endDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>
            <if test="acceptDateStart != null and acceptDateEnd != null"><!--申报日期-->
                AND to_date(to_char(GUCM.acceptDate,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{acceptDateStart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{acceptDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>
            <if test="department != null and department != ''">
                and GUCEL.department = #{department}
            </if>
        </where>
        <choose>
            <when test='orderColumn!=null and orderColumn!=""'>
                ORDER BY ${orderColumn} ${orderType}
            </when>
            <!-- 没有激活排序条件时，默认排序 -->
            <otherwise>
                order by GUCEL.INPUTDATE desc
            </otherwise>
        </choose>
    </select>


    <!--根据条件的查询结果集导出-->
    <select id="selectByAllDeclarationRespVo" resultMap="ReqResultMap">
        SELECT GUCM.POLICYNO,<!--保单号码-->
        GUCEL.LISTSEQNO,<!--凭证号码-->
        GUCEL.EMPNAME,<!--被保险人名称-->
        A.CODE_CNAME EMPIDENTIFYTYPE,<!--证件类型_GUCEL.EMPIDENTIFYTYPE-->
        GUCEL.EMPIDENTIFYNUMBER,<!--证件号码-->
        B.CODE_CNAME PERSONCASUALTIESLIMIT,<!--计划名称_GUCEP.PERSONCASUALTIESLIMIT-->
        DECODE(GUCEL.TARGETFLAG,'I','增员','E','更新','减员') TARGETFLAG,<!--操作类型-->
        GUCEL.ENTRYDATE,<!--入职日期-->
        GUCEL.EFFECTIVEDATE,<!--生效日期-->
        GUCEL.ENDDATE,<!--到期日期-->
        GUCM.ACCEPTDATE,<!--申报日期-->
        GUCEL.MONTHPAY,<!--约定月薪-->
        GUCEL.EMPPREMIUM,<!--保费-每人毛保费-->
        GUCEL.CHANGEEMPPREMIUM,<!--每人应收保费-->
        GUCM.APPLINAME,<!--投保公司-投保人名称-->
        GUCM.COMPANYNAME, <!--关联机构-->
        DECODE(GUCM.UNDERWRITEIND,'0','未申报','已申报') UNDERWRITEIND,<!--申报状态(核保标志)-->
        GUCM.ENDORNO, <!--申报单号-->
        GUCM.PROJECTMANAGERNAME, <!--归属业务员名称-->
        GUCM.LASTMODIFYMANAGERNAME,
        GUCEL.RESIGNATIONDATE <!--离职日期-->
        FROM GUPOLICYCOPYMAIN GUCM
        RIGHT JOIN GUPOLICYCOPYEMPLOYERSLIST GUCEL ON GUCEL.POLICYNO=GUCM.POLICYNO
        AND GUCEL.ENDORNO=GUCM.ENDORNO
        AND (GUCEL.TARGETFLAG='I' OR GUCEL.TARGETFLAG='U' OR GUCEL.TARGETFLAG='E')
        LEFT JOIN GUPOLICYCOPYEMPLOYERSPLAN GUCEP ON GUCEP.POLICYNO=GUCM.POLICYNO
        AND GUCEP.ENDORNO=GUCM.ENDORNO
        AND GUCEP.ITEMNO=GUCEL.ITEMNO
        AND GUCEP.TARGETCALCULATE='1'
        left join ggcode a on A.CODE_TYPE = 'ClientCertType' AND A.CODE_CODE = GUCEL.EMPIDENTIFYTYPE
        left join ggcode b on b.CODE_CODE = to_char(GUCEP.PERSONCASUALTIESLIMIT) AND B.CODE_TYPE = 'PlanName'
        <where>
            <if test="projectManagerCode != null and projectManagerCode != ''">
                <!--用户职级为业务员增加限制查询条件-->
                and GUCM.PROJECTMANAGERCODE = #{projectManagerCode} and ( GUCM.LASTMODIFYMANAGERCODE = #{projectManagerCode} or GUCM.LASTMODIFYMANAGERCODE is null )
            </if>
            <if test="companyCode != null and companyCode != ''"><!--关联机构代码-->
                and GUCM.companyCode = #{companyCode}
            </if>
            <!--关联机构权限校验-->
            <if test="companyCodes != null and companyCodes.size() &gt; 0">
                and GUCM.companyCode IN
                <foreach collection="companyCodes" item="comCode" index="index" open="(" close=")" separator=",">
                    &apos;${comCode}&apos;
                </foreach>
            </if>
            <if test="appliName != null and appliName != ''"><!--投保人名称-->
                and GUCM.appliName like concat(concat('%',#{appliName}),'%')
            </if>
            <if test="projectManagerName != null and projectManagerName != ''"><!--投保人名称-->
                and GUCM.projectManagerName like concat(concat('%',#{projectManagerName}),'%')
            </if>
            <if test="policyNo != null and policyNo != ''"><!--保单号码-->
                and GUCM.policyNo like concat(concat('%',#{policyNo}),'%')
            </if>
            <if test="endorNo != null and endorNo != ''"><!--申报单号-->
                and GUCM.ENDORNO like concat(concat('%',#{endorNo}),'%')
            </if>
            <if test="personcasualtieslimit != null and personcasualtieslimit != ''"><!--计划名称-责任限额-->
                and GUCEP.PERSONCASUALTIESLIMIT= #{personcasualtieslimit}
            </if>
            <if test="empName != null and empName != ''"><!--被保险人名称-->
                and GUCEL.empName like concat(concat('%',#{empName}),'%')
            </if>
            <if test="empIdentifyType != null and empIdentifyType != ''"><!--证件类型-->
                and GUCEL.empIdentifyType like concat(concat('%',#{empIdentifyType}),'%')
            </if>
            <if test="empIdentifyNumber != null and empIdentifyNumber != ''"><!--证件号码-->
                and GUCEL.empIdentifyNumber like concat(concat('%',#{empIdentifyNumber}),'%')
            </if>
            <if test="listSeqno != null and listSeqno != ''"><!--凭证号码-->
                and GUCEL.LISTSEQNO like concat(concat('%',#{listSeqno}),'%')
            </if>
            <if test="targetFlag != null and targetFlag != ''"><!--操作类型-->
                and GUCEL.targetFlag like concat(concat('%',#{targetFlag}),'%')
            </if>
            <if test="effectiveDateStart != null and effectiveDateEnd != null"><!--生效日期-->
                AND to_date(to_char(GUCEL.effectiveDate,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{effectiveDateStart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{effectiveDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>
            <if test="endDateStart != null and endDateEnd != null"><!--到期日期-->
                AND to_date(to_char(GUCEL.endDate,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{endDateStart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{endDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>
            <if test="acceptDateStart != null and acceptDateEnd != null"><!--申报日期-->
                AND to_date(to_char(GUCM.acceptDate,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{acceptDateStart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{acceptDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>

        </where>
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity"/>
    </select>

    <delete id="deleteByEndorNo" parameterType="java.lang.String">
		delete from GUPOLICYCOPYMAIN
		where ENDORNO = #{endorNo}
	</delete>

    <insert id="insertBatchDeclarationBatchQueryTemp">
        INSERT ALL
        <foreach collection="list" item="item" separator="">
            INTO GZDECLARATIONBATCHQUERYTEMP VALUES
            (#{item.uuid}, #{item.empName}, #{item.empIdentifyNumber})
        </foreach>
        select 1 from dual
    </insert>


    <!--结算查询结果-->
    <resultMap id="Settlement" type="ins.channel.gupolicycopymain.vo.SelectSettlementVo">
        <result property="policyno" column="policyno"/>
        <result property="endorno" column="endorno"/>
        <result property="settleno" column="settleno"/>
        <result property="empname" column="empname"/>
        <result property="validdate" column="validdate"/>
        <result property="enddate" column="enddate" />
        <result property="personcasualtieslimit" column="personcasualtieslimit"/>
        <result property="acceptdate" column="acceptdate"/>
        <result property="changegrosspremium" column="changegrosspremium"/>
        <result property="employeesthischange" column="employeesthischange"/>
        <result property="underwriteind" column="underwriteind"/>
        <result property="settlestatus" column="settlestatus"/>
        <result property="settlefee" column="settlefee"/>
        <result property="companyname" column="companyname"/>
        <result property="projectmanagername" column="projectmanagername"/>
        <result property="validdateEnd" column="validdateEnd"/>
        <result property="startdate" column="startdate"/>
        <result property="acceptdateEnd" column="acceptdateEnd"/>
        <result property="companyCode" column="companyCode"/>
        <result property="sumgrosspremium" column="sumgrosspremium"/>
    </resultMap>
    <!--结算查询结果-->
    <select id="shouByAll" resultMap="Settlement" >
        select
            GCM.policyno,<!--保单号 -->
            GCM.endorno,<!--申报单号 -->
            GCM.settleno,<!--结算单号 -->
            GCL.empname,<!--被保险人 -->
            GCM.validdate,<!--生效日期 -->
            GCM.enddate,<!--到期日期 -->
            GCM.acceptdate,<!--申报日期 -->
            GCM.changegrosspremium,<!--申报金额 -->
            GCM.underwriteind,<!--申报状态 -->
            GCM.settlestatus,<!--结算状态 -->
            GCM.settlefee,<!--已结算金额 -->
            GCM.companyname,<!--关联机构 -->
            GCM.companyCode,<!--关联机构代码-->
            GCM.projectmanagername,<!--业务员 -->
            cast(GCE.personcasualtieslimit as varchar2(20)) as personcasualtieslimit,<!--伤亡限额作为计划名称的判断条件-->
            GCE.employeesthischange<!-- 申报雇员数-->
            from  GupolicyCopyMain GCM
            INNER JOIN GupolicyCopyEmployersPlan GCE
            ON GCM.policyno=GCE.policyno and GCM.Endorno=GCE.endorno
            inner join GUPOLICYCOPYEMPLOYERSLIST gcl
            on GCM.policyno = gcl.policyno and GCM.Endorno= gcl.endorno
            and GCL.itemno = GCE.itemno
            <where>
                GCE.TARGETCALCULATE ='1'
                <!--关联机构权限校验-->
                <if test="companyCodes != null and companyCodes.size() &gt; 0">
                    and GCM.companyCode IN
                    <foreach collection="companyCodes" item="comCode" index="index" open="(" close=")" separator=",">
                        &apos;${comCode}&apos;
                        <!--  ${comCode}-->
                    </foreach>
                </if>
                <!--根据关联机构查询-->
                <if test="companyCode != null and companyCode !=''">
                    and GCM.companyCode = #{companyCode}
                </if>
                <!--根据保单号查询-->
                <if test="policyno != null and policyno != ''">
                    and GCM.policyno like concat(concat('%',#{policyno}),'%')
                </if>
                 <!--保险编码-->
                <if test="productcode != null and productcode != ''">
                    and GCM.PRODUCTCODE =#{productcode}
                </if>
                <!--根据计划名称查询-->
                <if test="personcasualtieslimit != null ">
                    and GCE.personcasualtieslimit = #{personcasualtieslimit}
                </if>
                <!--根据申报单号查询-->
                <if test="endorno != null and endorno != ''">
                    and GCM.endorno like concat(concat('%',#{endorno}),'%')
                </if>
                <!--根据到期日期起止查询-->
                <if test=" startdate != null and  enddate != null">
                    AND to_date(to_char(GCM.enddate,'yyyy-mm-dd'),'yyyy-mm-dd')
                    between to_date(to_char(#{startdate} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                    and to_date(to_char(#{enddate} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                </if>
                <!--根据生效日期起止查询-->
                <if test="validdate != null and  validdateEnd != null">
                    AND to_date(to_char(GCM.validdate,'yyyy-mm-dd'),'yyyy-mm-dd')
                    between to_date(to_char(#{validdate} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                    and to_date(to_char(#{validdateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                </if>
                <!--根据申报日期起止查询-->
                <if test="acceptdate != null and  acceptdateEnd != null">
                    AND to_date(to_char(GCM.acceptdate,'yyyy-mm-dd'),'yyyy-mm-dd')
                    between to_date(to_char(#{acceptdate} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                    and to_date(to_char(#{acceptdateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                </if>
                <!--根据申报状态查询-->
                <if test="underwriteind != null and underwriteind != ''">
                    and GCM.underwriteind = #{underwriteind}
                </if>
                <!--根据结算状态查询-->
                <if test="settlestatus != null and settlestatus != ''">
                    and GCM.settlestatus = #{settlestatus}
                </if>
                <!--根据结算单号查询-->
                <if test="settleno != null and settleno != ''">
                    and GCM.settleno like concat(concat('%',#{settleno}),'%')
                </if>
                <!--根据业务员查询-->
                <if test="projectmanagername != null and projectmanagername != ''">
                    and GCM.projectmanagername  like concat(concat('%',#{projectmanagername}),'%')
                </if>

                or
                GCE.TARGETCALCULATE ='1'
                and GCM.projectmanagername = '0'
                <!--关联机构权限校验-->
                <if test="companyCodes != null and companyCodes.size() &gt; 0">
                    and GCM.companyCode IN
                    <foreach collection="companyCodes" item="comCode" index="index" open="(" close=")" separator=",">
                        &apos;${comCode}&apos;
                        <!--  ${comCode}-->
                    </foreach>
                </if>
                <!--根据关联机构查询-->
                <if test="companyCode != null and companyCode !=''">
                    and GCM.companyCode = #{companyCode}
                </if>
                <!--根据保单号查询-->
                <if test="policyno != null and policyno != ''">
                    and GCM.policyno like concat(concat('%',#{policyno}),'%')
                </if>
                <!--保险编码-->
                <if test="productcode != null and productcode != ''">
                    and GCM.PRODUCTCODE =#{productcode}
                </if>
                <!--根据计划名称查询-->
                <if test="personcasualtieslimit != null ">
                    and GCE.personcasualtieslimit = #{personcasualtieslimit}
                </if>
                <!--根据申报单号查询-->
                <if test="endorno != null and endorno != ''">
                    and GCM.endorno like concat(concat('%',#{endorno}),'%')
                </if>
                <!--根据到期日期起止查询-->
                <if test=" startdate != null and  enddate != null">
                    AND to_date(to_char(GCM.enddate,'yyyy-mm-dd'),'yyyy-mm-dd')
                    between to_date(to_char(#{startdate} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                    and to_date(to_char(#{enddate} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                </if>
                <!--根据生效日期起止查询-->
                <if test="validdate != null and  validdateEnd != null">
                    AND to_date(to_char(GCM.validdate,'yyyy-mm-dd'),'yyyy-mm-dd')
                    between to_date(to_char(#{validdate} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                    and to_date(to_char(#{validdateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                </if>
                <!--根据申报日期起止查询-->
                <if test="acceptdate != null and  acceptdateEnd != null">
                    AND to_date(to_char(GCM.acceptdate,'yyyy-mm-dd'),'yyyy-mm-dd')
                    between to_date(to_char(#{acceptdate} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                    and to_date(to_char(#{acceptdateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                </if>
                <!--根据申报状态查询-->
                <if test="underwriteind != null and underwriteind != ''">
                    and GCM.underwriteind = #{underwriteind}
                </if>
                <!--根据结算状态查询-->
                <if test="settlestatus != null and settlestatus != ''">
                    and GCM.settlestatus = #{settlestatus}
                </if>
                <!--根据结算单号查询-->
                <if test="settleno != null and settleno != ''">
                    and GCM.settleno like concat(concat('%',#{settleno}),'%')
                </if>
                <if test="department != null and department != ''">
                    and gcl.department = #{department}
                </if>
            </where>

    </select>

    <!--根据申报单号修改结算状态、结算单号-->
    <update id="SettlementUpdate" >
            update gupolicycopymain set settlestatus='1',settleno=#{settleno}
            where endorno = #{endorno}
    </update>
    <!--根据查到的金额去得到总金额-->
    <select id="selectGupolicycopymain" resultMap="Settlement">
        select changegrosspremium from gupolicycopymain where endorno = #{endorno}
    </select>

    <resultMap id="ShowGupolicycopymain" type="ins.channel.gupolicycopymain.po.Gupolicycopymain">
        <result property="policyNo" column="policyNo"/>
        <result property="endorseqno" column="endorseqno"/>
        <result property="endorNo" column="endorNo"/>
        <result property="settleNo" column="settleNo"/>
        <result property="changegrosspremium" column="changegrosspremium"/>
        <result property="settleStatus" column="settleStatus"/>
        <result property="settlefee" column="settlefee"/>
        <result property="companyname" column="companyname"/>
        <result property="companycode" column="companycode"/>
     </resultMap>

    <select id="ShowEndorseqnoPolicynoEndorno" resultMap="ShowGupolicycopymain">
        select
        *
        from (select
            policyNo,<!--保单号 -->
            endorNo,<!--申报单号 -->
            settleno,<!--结算单号 -->
            changegrosspremium,<!--申报金额 -->
            settleStatus,<!--结算状态 -->
            settlefee,<!--已结算金额 -->
            companyname,<!--关联机构 -->
            companycode,<!--关联机构代码-->
            endorseqno <!--保单序号-->
             from GupolicyCopyMain t where t.settlestatus='1'
              order by t.acceptdate desc)
        where rownum &lt;=10


    </select>

    <!--结算状态为2-->
    <update id="GupolicycopymainUpd" >
            update gupolicycopymain set settleStatus=#{settleStatus},settlefee=#{settlefee}
            where policyNo = #{policyNo} and endorNo = #{endorNo} and endorSeqNo = #{endorseqno}
    </update>
    <!--根据前端返回的申报单号查询数据-->
    <select id="selectByStell" resultMap="ShowGupolicycopymain">
        select * from gupolicycopymain where endorNo = #{endorno}
        <!--关联机构权限校验-->
        <if test="companyCodes != null and companyCodes.size() &gt; 0">
            and companyCode IN
            <foreach collection="companyCodes" item="comCode" index="index" open="(" close=")" separator=",">
                &apos;${comCode}&apos;
                <!--  ${comCode}-->
            </foreach>
        </if>
    </select>

    <update id="updateCompanyCodeAndCompanyName">
        update GupolicycopyMain a set a.companycode=#{companycode},a.companyname =#{companyname},a.PROJECTMANAGERCODE =#{projectmanagercode},a.PROJECTMANAGERNAME=#{projectmanagername},a.UPDATESYSDATE = #{updatesysdate}
        where  policyNo = #{policyNo}
    </update>

    <update id="updateNullValues">
        update GupolicycopyMain a set a.companycode=null,a.companyname =null,a.PROJECTMANAGERCODE =null,a.PROJECTMANAGERNAME=null,a.UPDATESYSDATE = #{updatesysdate}
        where  policyNo = #{policyNo}
    </update>

    <delete id="deleteByPolicyNo" parameterType="string">
        delete from GupolicycopyMain where policyNo=#{policyNo}
    </delete>


</mapper>
