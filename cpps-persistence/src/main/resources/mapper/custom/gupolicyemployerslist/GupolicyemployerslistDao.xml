<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicyemployerslist.dao.GupolicyemployerslistDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.gupolicyemployerslist.dao.GupolicyemployerslistDao"/>
    <!-- 请在下方添加自定义配置-->
    <select id="selectAllOccupationNamesByPolicyNo" resultType="java.lang.String">
		SELECT WMSYS.WM_CONCAT(DISTINCT(GUEL.OCCUPATIONNAME))
		FROM   GUPOLICYEMPLOYERSLIST GUEL
		WHERE  GUEL.POLICYNO = #{policyNo}
	</select>

    <select id="selectByCondition" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity"/>
    </select>

    <select id="searchByPolicyNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from GUPOLICYEMPLOYERSLIST
        <where>
            <if test="policyNo != null and policyNo != ''" >
                and POLICYNO = #{policyNo}
            </if>
            <if test="targetflag != null and targetflag != ''" >
                and TARGETFLAG != #{targetflag}
            </if>
            <if test="empname != null and empname != ''" >
                and EMPNAME = #{empname}
            </if>
            <if test="empidentifynumber != null and empidentifynumber != ''" >
                and EMPIDENTIFYNUMBER = #{empidentifynumber}
            </if>
            <if test="department != null and department != ''">
               and  DEPARTMENT = #{department}
            </if>
            <if test="itemNo != null ">
               and  ITEMNO = #{itemNo}
            </if>
        </where>
        order by enddate desc
    </select>

    <resultMap id="PageQueryResultMap" type="ins.channel.gupolicyemployerslist.vo.EmployerslistQueryRespVo">
        <result column="POLICYNO" property="policyNo" />
        <result column="PLANNAME" property="planName" />
        <result column="LISTSEQNO" property="listseqno" />
        <result column="EMPNAME" property="empname" />
        <result column="EMPSEX" property="empsex" />
        <result column="EMPBIRTHDAY" property="empbirthday" />
        <result column="EMPIDENTIFYTYPE" property="empidentifytype" />
        <result column="EMPIDENTIFYNUMBER" property="empidentifynumber" />
        <result column="OCCUPATIONCODE" property="occupationCode" />
        <result column="OCCUPATIONNAME" property="occupationname" />
        <result column="OCCUPATIONLEVEL" property="occupationlevel" />
        <result column="ENTRYDATE" property="entrydate" />
        <result column="RESIGNATIONDATE" property="resignationdate" />
        <result column="EFFECTIVEDATE" property="effectivedate" />
        <result column="ENDDATE" property="endDate" />
        <result column="MONTHPAY" property="monthPay" />
        <result column="PERSONCASUALTIESLIMIT" property="personcasualtieslimit" />
        <result column="ITEMNO" property="itemNo" />
    </resultMap>
    <select id="queryPolicyEmployersListByPage" resultMap="PageQueryResultMap">
        SELECT GUM.POLICYNO, --<!--保单号码-->
        GUEL.LISTSEQNO, --<!--人员编号-->
        GUEL.EMPNAME,--<!--雇员姓名-->
        GUEL.EMPSEX,--<!--雇员性别-->
        GUEL.EMPBIRTHDAY,--<!--生日-->
        GUEL.EMPIDENTIFYTYPE,--<!--雇员证件类型-->
        GUEL.EMPIDENTIFYNUMBER,--<!--雇员证件号-->
        GUEL.OCCUPATIONCODE,--<!--职业代码-->
        GUEL.OCCUPATIONNAME,--<!--职业名称-->
        GUEL.OCCUPATIONLEVEL,--<!--职业等级-->
        GUEL.ENTRYDATE,--<!--入职日期-->
        SYSDATE AS  RESIGNATIONDATE,--<!--离职日期-->
        GUEL.EFFECTIVEDATE,--<!--生效日期-->
        GUEL.ENDDATE,--<!--终保日期-->
        GUEL.MONTHPAY,--<!--约定月薪-->
        GUEP.PERSONCASUALTIESLIMIT,--<!--伤亡限额-->
        GUEL.PROJECT,
        GUEL.homeaddress,
        GUEL.hometel,
        guep.itemNo
        FROM GUPOLICYMAIN GUM,GUPOLICYEMPLOYERSPLAN   GUEP,gupolicyemployerslist GUEL
        <where>
            GUEP.POLICYNO=GUM.POLICYNO
            AND  GUEL.POLICYNO=GUM.POLICYNO
            and guep.itemNo = guel.itemNo
            AND GUM.VALIDIND = '1'
            AND GUEP.Targetcalculate = '1'
            AND GUEL.TARGETFLAG != 'D'
            <if test="policyNo != null and policyNo != ''"><!--保单号码-->
                and GUM.POLICYNO like concat(concat('%',#{policyNo}),'%')
            </if>
            <if test="empname != null and empname != ''"><!--雇员姓名-->
                and GUEL.EMPNAME like concat(concat('%', #{empname}),'%')
            </if>
            <if test="empidentifytype != null and empidentifytype != ''"><!--雇员证件类型-->
                and GUEL.EMPIDENTIFYTYPE  = #{empidentifytype}
            </if>
            <if test="empidentifynumber != null and empidentifynumber != ''"><!--雇员证件号-->
                and GUEL.EMPIDENTIFYNUMBER like concat(concat('%', #{empidentifynumber}),'%')
            </if>
            <if test="entrydatestart != null and entrydateend != null"><!--入职日期-->
                AND to_date(to_char(GUEL.ENTRYDATE,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{entrydatestart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{entrydateend} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>
            <if test="effectivedatestart != null and effectivedateend != null"><!--生效日期-->
                AND to_date(to_char(GUEL.EFFECTIVEDATE,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{effectivedatestart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{effectivedateend} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>
            <if test="endDatestart != null and endDateend != null"><!--终保日期-->
                AND to_date(to_char(GUEL.ENDDATE,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{endDatestart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{endDateend} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>
            <if test="department != null and department != ''">
                and  GUEL.department = #{department}
            </if>
            <if test="itemNo != null and itemNo != ''">
                and GUEP.itemNo = #{itemNo}
            </if>
           <!-- <if test="acceptdatestart != null and acceptdateend != null">&lt;!&ndash;申报日期&ndash;&gt;
                AND to_date(to_char(GUM.ACCEPTDATE,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{acceptdatestart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{acceptdateend} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>-->
        </where>
        ORDER  BY GUEL.LISTSEQNO
    </select>

    <delete id="deleteByPolicyNo" parameterType="string">
        delete from GUPOLICYEMPLOYERSLIST where policyNo=#{policyNo}
    </delete>

</mapper>
