<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicymain.dao.GupolicymainDao">
	<!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
	<cache-ref  namespace="ins.channel.gupolicymain.dao.GupolicymainDao"/>
    <!-- 请在下方添加自定义配置-->
    <select id="selectByCondition" resultType="ins.channel.gupolicymain.po.Gupolicymain">
		<include refid="Base_Select_By_Entity"/>
	</select>

	<select id="selectAllPolicyNo" resultType="java.lang.String">
		SELECT
			POLICYNO
		FROM  GUPOLICYMAIN
	</select>

	<select id="selectCountByPolicyNo" resultType="java.lang.Integer">
		SELECT
			count(1)
		FROM  GUPOLICYMAIN
		WHERE
		POLICYNO = #{policyno}
	</select>

	<update id="invalidMainCompany" parameterType="ins.channel.gupolicymain.po.Gupolicymain">
		update GUPOLICYMAIN
		set
		COMPANYCODE=#{companycode},
		COMPANYNAME=#{companyname},
		PROJECTMANAGERCODE = #{projectmanagercode},
		PROJECTMANAGERNAME = #{projectmanagername},
		UPDATESYSDATE=#{updatesysdate}
		where ID = #{id }
	</update>

	<!-- 通用查询结果对象-->
	<resultMap id="ReqResultMap" type="ins.channel.gupolicymain.vo.PolicyMainRespVo">
		<result column="POLICYNO" property="policyNo"/>
		<result column="SURVEYIND" property="surveyind"/>
		<result column="APPLINAME" property="appliName"/>
		<result column="COMPANYNAME" property="companyName"/>
		<result column="INSUREDNAME" property="insuredname"/>
		<result column="SALESMANNAME" property="salesManName"/>
		<result column="EFFECTIVEDATE" property="effectiveDate"/>
		<result column="ENDDATE" property="endDate"/>
		<result column="VALIDIND" property="validind"/>
		<result column="PLANNAME" property="planName"/>
		<result column="PERSONCASUALTIESLIMIT" property="personcasualtieslimit"/>
		<result column="PERSONMEDICALLIMIT" property="personmedicallimit"/>
		<result column="PRODUCTCODE" property="productcode"/>
		<result column="SCHEMECODE" property="schemecode"/>
		<result column="ITEMNO" property="itemNo"/>
	</resultMap>

<!--	保单查询功能条件分页查询-->
	<select id="pageQueryPolicyByCondition" resultMap="ReqResultMap">
		SELECT GUM.POLICYNO,<!--保单号码-->
		GUM.APPLINAME,<!--投保公司-投保人名称-->
		GUM.COMPANYNAME,<!--关联机构-->
		GUMB.SALESMANNAME,<!--业务员-->
		GUEL.EFFECTIVEDATE,<!--生效日期-->
		GUEL.ENDDATE,<!--到期日期-->
		GUM.VALIDIND<!--有效标志 1有效 0无效-->
		FROM GUPOLICYMAIN GUM
		LEFT JOIN GUPOLICYMAINBASIC GUMB ON GUMB.POLICYNO=GUM.POLICYNO
		LEFT JOIN GUPOLICYEMPLOYERSLIST GUEL ON GUEL.POLICYNO=GUM.POLICYNO
		<where>
			<if test="policyNo != null and policyNo != ''"><!--保单号码-->
				and GUM.policyNo like concat(concat('%',#{policyNo}),'%')
			</if>
			<if test="companyCode != null and companyCode != ''"><!--关联机构代码-->
				and GUM.companyCode = #{companyCode}
			</if>
			<if test="effectiveDateStart != null and effectiveDateEnd != null"><!--生效日期-->
				AND to_date(to_char(GUEL.effectiveDate,'yyyy-mm-dd'),'yyyy-mm-dd')
				between to_date(to_char(#{effectiveDateStart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
				and to_date(to_char(#{effectiveDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
			</if>
		</where>
		ORDER BY GUM.VALIDIND DESC,GUM.INPUTDATE DESC
	</select>

	<select id="queryPolicyByPage" resultMap="ReqResultMap">
		SELECT GUM.POLICYNO,<!--保单号码-->
		GUM.SURVEYIND,<!--创新业务标识-->
		GUM.APPLINAME,<!--投保人(投保公司)名称-->
		cast(GUEP.PERSONCASUALTIESLIMIT  as varchar2(20)) as PLANNAME,<!--伤亡限额作为计划名称的判断条件-->
		GUM.STARTDATE,<!--起保日期-->
		GUM.ENDDATE,<!--终保日期-->
		GUEP.PERSONCASUALTIESLIMIT,<!--伤亡限额-->
		GUEP.PERSONMEDICALLIMIT,<!--医疗限额-->
		GUM.INSUREDNAME,<!--被保险人姓名-->
		GUM.PRODUCTCODE<!--险种编码-->,
		GUEP.itemno
		FROM GUPOLICYMAIN GUM,GUPOLICYEMPLOYERSPLAN   GUEP
		<where>
			GUEP.POLICYNO=GUM.POLICYNO
			AND GUM.VALIDIND = '1'
			and GUEP.TARGETCALCULATE ='1'
			<if test="policyNo != null and policyNo != ''"><!--保单号码-->
				and GUM.POLICYNO like concat(concat('%',#{policyNo}),'%')
			</if>
			<if test="companyCode != null and companyCode != ''"><!--关联机构代码-->
				and GUM.COMPANYCODE = #{companyCode}
			</if>
			<if test="projectManagerCode != null and projectManagerCode != ''"><!--关联机构代码-->
				and GUM.projectManagerCode = #{projectManagerCode}
			</if>
			<if test="appliCode != null and appliCode != ''"><!--投保人(投保公司)代码-->
				and GUM.APPLICODE like concat(concat('%',#{appliCode}),'%')
			</if>
			<if test="productcode != null and productcode != ''"><!--保险险种-->
				and GUM.PRODUCTCODE = #{productcode}
			</if>
			<if test="startDateStart != null and startDateEnd != null"><!--起保日期-->
				AND to_date(to_char(GUM.STARTDATE,'yyyy-mm-dd'),'yyyy-mm-dd')
				between to_date(to_char(#{startDateStart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
				and to_date(to_char(#{startDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
			</if>
			<if test="endDateStrat != null and endDateEnd != null"><!--起保日期-->
				AND to_date(to_char(GUM.ENDDATE,'yyyy-mm-dd'),'yyyy-mm-dd')
				between to_date(to_char(#{endDateStrat} ,'yyyy-mm-dd'),'yyyy-mm-dd')
				and to_date(to_char(#{endDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
			</if>
			<if test="companyCodes != null and companyCodes.size() &gt; 0">
				and GUM.COMPANYCODE IN
				<foreach collection="companyCodes" item="comCode" index="index" open="(" close=")" separator=",">
					&apos;${comCode}&apos;
				</foreach>
			</if>
			or
			GUEP.POLICYNO=GUM.POLICYNO
			AND GUM.VALIDIND = '1'
			and GUEP.TARGETCALCULATE ='1'
			and GUM.projectManagerCode = '0'
			<if test="policyNo != null and policyNo != ''"><!--保单号码-->
				and GUM.POLICYNO like concat(concat('%',#{policyNo}),'%')
			</if>
			<if test="companyCode != null and companyCode != ''"><!--关联机构代码-->
				and GUM.COMPANYCODE = #{companyCode}
			</if>

			<if test="appliCode != null and appliCode != ''"><!--投保人(投保公司)代码-->
				and GUM.APPLICODE like concat(concat('%',#{appliCode}),'%')
			</if>
			<if test="productcode != null and productcode != ''"><!--保险险种-->
				and GUM.PRODUCTCODE = #{productcode}
			</if>
			<if test="startDateStart != null and startDateEnd != null"><!--起保日期-->
				AND to_date(to_char(GUM.STARTDATE,'yyyy-mm-dd'),'yyyy-mm-dd')
				between to_date(to_char(#{startDateStart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
				and to_date(to_char(#{startDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
			</if>
			<if test="endDateStrat != null and endDateEnd != null"><!--起保日期-->
				AND to_date(to_char(GUM.ENDDATE,'yyyy-mm-dd'),'yyyy-mm-dd')
				between to_date(to_char(#{endDateStrat} ,'yyyy-mm-dd'),'yyyy-mm-dd')
				and to_date(to_char(#{endDateEnd} ,'yyyy-mm-dd'),'yyyy-mm-dd')
			</if>
			<if test="companyCodes != null and companyCodes.size() &gt; 0">
				and GUM.COMPANYCODE IN
				<foreach collection="companyCodes" item="comCode" index="index" open="(" close=")" separator=",">
					&apos;${comCode}&apos;
				</foreach>
			</if>
		</where>
		ORDER BY GUM.VALIDIND DESC,GUM.INPUTDATE DESC,GUM.POLICYNO
	</select>

    <select id="selectForDeclarationAdd" resultMap="ReqResultMap">
		SELECT GUM.POLICYNO,<!--保单号码-->
		GUM.APPLINAME,<!--投保人(投保公司)名称-->
		GUM.COMPANYNAME,<!--关联机构-->
		GUM.PRODUCTCODE,<!--保险险种-->
		GUM.STARTDATE,<!--起保日期-->
		GUM.ENDDATE,<!--终保日期-->
		GUEP.PERSONCASUALTIESLIMIT,<!--伤亡限额 用于判断计划名称-->
		GUEP.SCHEMECODE<!--方案代码-->,
		guep.itemNo
		FROM GUPOLICYMAIN GUM,GUPOLICYEMPLOYERSPLAN GUEP
		where
			GUEP.POLICYNO=GUM.POLICYNO
        AND GUM.VALIDIND = '1'
        AND GUM.POLICYNO =#{policyNo}
        and guep.itemNo = #{itemNo}
</select>

	<!-- 保险凭证打印结果对象-->
	<resultMap id="queryPolicyVoucherInfoResultMap" type="ins.channel.gupolicymain.vo.PolicyVoucherInfoVo">
		<result column="APPLINAME" property="insuredCompany"/>
		<result column="RISK_CNAME" property="kindName"/>
		<result column="POLICYNO" property="policyNo"/>
		<result column="INSUREDNAME" property="appliName"/>
		<result column="POSTADDRESS" property="insuredAddress"/>
		<result column="STARTDATE" property="startDate"/>
		<result column="ENDDATE" property="endDate"/>
		<result column="PRODUCTCODE" property="productcode"/>
		<result column="INSURANCECOMPANYCODE" property="insurancecompanycode"/>
	</resultMap>

	<!-- 保险凭证打印功能条件分页查询-->
	<select id="queryPolicyVoucherInfo" resultMap="queryPolicyVoucherInfoResultMap">
		SELECT
		GUM.APPLINAME,<!--投保公司-投保人名称-->
		GUM.INSUREDNAME,
		GGR.RISK_CNAME,
		GUM.POLICYNO,<!--保单号码-->
		GSCM.POSTADDRESS,
		GUM.STARTDATE,<!--生效日期-->
		GUM.ENDDATE,<!--到期日期-->
		GUM.PRODUCTCODE,
		GUM.INSURANCECOMPANYCODE
		FROM GUPOLICYMAIN GUM
		LEFT JOIN GSCLIENTMAIN GSCM ON GUM.INSUREDCODE = GSCM.CLIENTCODE
		LEFT JOIN GGRISK GGR ON GGR.RISK_CODE = GUM.PRODUCTCODE
		WHERE ROWNUM=1 and GUM.policyNo = #{policyNo}
	</select>



	<!--修改结算金额-->
	<update id="UpdateSettlefee">
		update gupolicymain set settlefee=#{settlefee} where policyno=#{policyNo}
	</update>

	<!-- 申报查询默认保单号查询结果对象-->
	<resultMap id="searchPolicyNoMap" type="ins.channel.gupolicycopymain.vo.DeclarationRespVo">
		<result column="POLICYNO" property="policyNo"/>

	</resultMap>
	<!--申报查询默认保单号-->
	<select id="searchPolicyNo" resultMap="searchPolicyNoMap">
		SELECT GUCM.POLICYNO<!--保单号码-->
		FROM GUPOLICYMAIN GUCM
		<where>
			<!--关联机构权限校验-->
			<if test="companyCodes != null and companyCodes.size() &gt; 0">
				and GUCM.companyCode IN
				<foreach collection="companyCodes" item="comCode" index="index" open="(" close=")" separator=",">
					&apos;${comCode}&apos;
				</foreach>
			</if>
		</where>
	</select>

	<delete id="deleteByPolicyNo" parameterType="string">
		delete from GUPOLICYMAIN where policyNo = #{policyNo}
	</delete>


</mapper>
