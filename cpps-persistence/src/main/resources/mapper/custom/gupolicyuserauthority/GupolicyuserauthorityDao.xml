<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.gupolicyuserauthority.dao.GupolicyuserauthorityDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.gupolicyuserauthority.dao.GupolicyuserauthorityDao"/>
    <!-- 请在下方添加自定义配置-->

    <sql id="ReqColumnList">
		USER_CODE,
		COMPANY_CODE,
		POLICYNO,
		APPLINAME,
        authStatus
		RISKCODE,
		CHANNELCODE,
		INSURANCECOMPANYCODE,
		SURVEYIND,
		VALIDIND,
		FLAG,
		REMARK,
		INPUTDATE,
		UPDATESYSDATE
	</sql>

    <!-- 通用查询结果对象-->
    <resultMap id="ReqResultMap" type="ins.channel.gupolicyuserauthority.vo.PolicyuserauthorityRespVo">
        <result column="POLICYNO" property="policyNo"/>
        <result column="COMPANYCODE" property="companyCode"/>
        <result column="STARTDATE" property="startDate"/>
        <result column="ENDDATE" property="endDate"/>
        <result column="APPLINAME" property="appliName"/>
        <result column="AUTHSTATUS" property="authStatus"/>
    </resultMap>


    <select id="pageByCondition" resultMap="ReqResultMap">
        SELECT
        T1.POLICYNO as POLICYNO,  <!-- 保单号-->
        T1.APPLINAME as APPLINAME,  <!--投保人名称-->
        T1.COMPANYCODE as COMPANYCODE,  <!--关联机构代码-->
        T1.STARTDATE as STARTDATE,  <!--起保日期-->
        T1.ENDDATE as ENDDATE,  <!-- 终保日期-->
        DECODE(T1.COMPANYCODE, NULL, 0, 1) AS AUTHSTATUS  <!--授权状态-->
        FROM GUPOLICYMAIN T1
        LEFT JOIN GUPOLICYUSERAUTHORITY T2
        ON T1.POLICYNO = T2.POLICYNO
        <where>
            <if test="policyNo != null and policyNo != ''">
                and (T1.POLICYNO like concat(concat('%',#{policyNo}),'%'))
            </if>
            <if test="appliName != null and appliName != ''">
                and (T1.APPLINAME like concat(concat('%',#{appliName}),'%'))
            </if>
            <if test="startDatestart != null and startDateend != null">
                AND to_date(to_char(t1.STARTDATE,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{startDatestart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{startDateend} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>
            <if test="endDatestart != null and endDateend != null">
                AND to_date(to_char(t1.ENDDATE,'yyyy-mm-dd'),'yyyy-mm-dd')
                between to_date(to_char(#{endDatestart} ,'yyyy-mm-dd'),'yyyy-mm-dd')
                and to_date(to_char(#{endDateend} ,'yyyy-mm-dd'),'yyyy-mm-dd')
            </if>
            <if test="authStatus == '0'.toString()">
                AND T1.COMPANYCODE IS NULL
            </if>
            <if test="authStatus == '1'.toString()">
                AND T1.COMPANYCODE IS NOT NULL
            </if>
        </where>
        ORDER BY T1.STARTDATE
    </select>

    <sql id="Select_By_Entity">
        select
        <include refid="Base_Column_List"/>
        from GUPOLICYUSERAUTHORITY
        <where>
            <include refid="Select_By_Entity_Where"/>
        </where>
    </sql>

    <sql id="Select_By_Entity_Where">
        <if test="policyNo != null and policyNo != ''">
            and POLICYNO = #{policyNo}
        </if>
        <if test="policynoList != null and policynoList.size() != ''">
            and POLICYNO in
            <foreach item="item" index="index" collection="policynoList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="selectByCondition" resultType="ins.channel.gupolicyuserauthority.po.Gupolicyuserauthority">
        <include refid="Select_By_Entity"/>
    </select>
</mapper>
