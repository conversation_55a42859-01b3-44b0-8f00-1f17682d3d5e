<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.paycomcodedefine.dao.GppaycomcodedefineDao">
	<!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
	<cache-ref  namespace="ins.channel.paycomcodedefine.dao.GppaycomcodedefineDao"/>
	<!-- 请在下方添加自定义配置-->

	<select id="searchPage" resultMap="BaseResultMap" parameterType="ins.channel.paycomcodedefine.po.GppaycomcodedefineSearch">
		select
		<include refid="Base_Column_List" />
		from GPPAYCOMCODEDEFINE
		<where>
			<if test="paymentComcode != null and paymentComcode != ''" >
				and PAYMENT_COMCODE like concat('%',concat(#{paymentComcode},'%'))
			</if>
			<if test="departmentCode != null and departmentCode != ''">
				and department_Code = #{departmentCode}
			</if>
			<if test="validInd != null and validInd != ''">
				and VALID_IND = #{validInd}
			</if>
		</where>
		order by MODIFIED_TIME desc
	</select>

	<resultMap id="PaycomcodeForSelect" type="ins.channel.paycomcodedefine.po.GgpaycomcodedefineSelect">
		<result column="PAYMENT_COMCODE" property="paymentComcode" />
		<result column="OFCENTER_CNAME" property="ofcenterCname" />
	</resultMap>
	<!-- COMPANY_CODE ,COMPANY_CNAME 进行分页查询 -->
	<select id="paycomcodeForSelectPage" resultMap="PaycomcodeForSelect" parameterType="map">
		select
		PAYMENT_COMCODE,OFCENTER_CNAME
		from GPPAYCOMCODEDEFINE
		<where>
			VALID_IND = '1'
			<if test="queryInfo != null and queryInfo != ''">
				and (PAYMENT_COMCODE like concat(concat('%',#{queryInfo}),'%')
				or OFCENTER_CNAME like concat(concat('%',#{queryInfo}),'%'))
			</if>
			<if test="paymentCodes != null and paymentCodes.size() &gt; 0">
				and PAYMENT_COMCODE IN
				<foreach collection="paymentCodes" item="comCode" open="(" close=")" separator=",">
					&apos;${comCode}&apos;
				</foreach>
			</if>
		</where>
	</select>

	<!-- COMPANY_CODE ,COMPANY_CNAME 进行分页查询 -->
	<select id="paycomcodeForSelectNoLimit" resultMap="PaycomcodeForSelect">
		select
		PAYMENT_COMCODE,OFCENTER_CNAME
		from GPPAYCOMCODEDEFINE
		<where>
			VALID_IND = '1'
		</where>
	</select>

	<select id="queryuserpaymentcompanyInfo" resultType="ins.channel.power.vo.UserpaymentcompanyVo">
		select B.PAYMENT_COMCODE,B.OFCENTER_CNAME  AS PAYMENT_COMPANYNAME,A.USER_CODE,A.USER_CNAME, A.DEFAULT_IND,case when A.VALID_IND='1' then '1' else '0' END AS FLAG
  		from GPPAYCOMCODEDEFINE B left join SAAUSERPAYMENTCOMPANY A
  			on A.PAYMENT_COMCODE = B.PAYMENT_COMCODE and A.VALID_IND = B.VALID_IND and A.VALID_IND = '1' and A.USER_CODE = #{userCode}
            order by B.PAYMENT_COMCODE
	</select>

	<select id="queryPaymentComListByCodeSet" resultType="ins.channel.paycomcodedefine.po.Gppaycomcodedefine">
		select
		<include refid="Base_Column_List"/>
		from GPPAYCOMCODEDEFINE
		where 1=1
		<if test="addSet != null and addSet.size() != 0" >
			and  PAYMENT_COMCODE in
			<foreach item="item" index="index" collection="addSet" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="selectByDeparmentCode" resultType="java.lang.String" parameterType="map">
		select PAYMENT_COMCODE from GPPAYCOMCODEDEFINE where
		PAYMENT_COMCODE in
		<foreach item="item" index="index" collection="companyCode" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<resultMap id="BaseCodeResultMap" type="ins.channel.baseCode.po.BaseCode">
		<result column="CODE_TYPE" property="codeType" />
		<result column="CODE_CODE" property="codeCode" />
		<result column="CODE_NAME" property="codeName" />
	</resultMap>
	<!--Modify By Zhoutaoyu 查询所有收付机构信息(供基础码表查询使用) 2019/11/20-->
	<select id="queryAll" resultMap="BaseCodeResultMap">
		SELECT T.Payment_Comcode AS CODE_CODE,
       				  T.Ofcenter_Cname AS CODE_NAME,
        			  'paymentCompanyCode' AS CODE_TYPE
  		FROM gppaycomcodedefine T
 		WHERE T.VALID_IND = '1'
	</select>
</mapper>
