<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.paycomref.dao.GppaycomrefDao">
	<!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
	<cache-ref namespace="ins.channel.paycomref.dao.GppaycomrefDao"/>
	<!-- 请在下方添加自定义配置-->
	<!-- 按对象查询一页记录（多条记录） -->
	<select id="searchPage" resultMap="BaseResultMap" parameterType="ins.channel.paycomref.po.Gppaycomref">
		select
		<include refid="Base_Column_List"/>
		from GPPAYCOMREF
		<where>
			<if test="departmentCode != null and departmentCode != ''">
				and DEPARTMENT_CODE = #{departmentCode}
			</if>
			<if test="userType != null and userType != ''">
				and USER_TYPE = #{userType}
			</if>
			<if test="paymentComcode != null and paymentComcode != ''">
				and PAYMENT_COMCODE = #{paymentComcode}
			</if>
			<if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
				and MODIFIED_TIME between to_date(concat(#{startTime},' 00:00:00'),'YYYY-MM-DD HH24:MI:SS') and
				to_date(concat(#{endTime},' 23:59:59'),'YYYY-MM-DD HH24:MI:SS')
			</if>
		</where>
	</select>

	<select id="selectByDepartmentCode" resultMap="BaseResultMap" parameterType="ins.channel.paycomref.po.Gppaycomref">
		select
		<include refid="Base_Column_List"/>
		from GPPAYCOMREF
		where
		DEPARTMENT_CODE = #{departmentCode}
		and USER_TYPE = #{userType}
	</select>

	<delete id="deleteByDepartMentCodes" parameterType="java.util.List">
		delete
		GPPAYCOMREF
		where
		DEPARTMENT_CODE in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<select id="selectByCompanyCode" resultMap="BaseResultMap" parameterType="ins.channel.paycomref.po.Gppaycomref">
		select
		<include refid="Base_Column_List"/>
		from GPPAYCOMREF
		where
		DEPARTMENT_CODE = #{param1}
	</select>
	<select id="selectByPaymentCode" resultMap="BaseResultMap" parameterType="ins.channel.paycomref.po.Gppaycomref">
		select
		<include refid="Base_Column_List"/>
		from GPPAYCOMREF
		where
		PAYMENT_COMCODE = #{param1}
	</select>
</mapper>
