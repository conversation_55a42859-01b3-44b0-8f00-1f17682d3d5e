<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ins.channel.power.dao.SaaGradeDao">

    <select id="selectMaxId" resultType="java.lang.Long">
        select max(ID) from SAAGRADE
    </select>

    <select id="queryGradeInfo" resultType="ins.channel.power.vo.SaagradetaskVo">
        select t.id as TASKID,t.taskcode,t.parentcode,t.taskcname,case when t2.VALIDSTATUS ='1' then '1' else '0' END AS FLAG  from SAATASK t
left join SAAGRADETASK t2
on t.id = t2.taskid
and t2.gradeid = #{param1} where t.VALIDFLAG='1' order by t.id
    </select>

    <select id="selectNameById" resultType="java.lang.String">
   select GRADECNAME from SAAGRADE where ID = #{param1}
    </select>

    <select id="querySubGrade" resultType="ins.channel.power.po.SaaGrade">
        SELECT * FROM SAAGRADE
        WHERE VALIDSTATUS = '1'
        <if test="gradeLevel !='9'.toString() ">
            and substr(id,1,1) >= #{gradeLevel}
        </if>
        <if test=" gradeLevel !='1'.toString() and  gradeLevel !='9'.toString() ">
            and id <![CDATA[<> ]]> '9999'
        </if>
    </select>

    <select id="selectAll" resultType="ins.channel.power.po.SaaGrade">
        SELECT
        <include refid="Base_Column_List"/>
        FROM SAAGRADE order by ID
    </select>

    <select id="selectByCondition" resultType="ins.channel.power.po.SaaGrade" parameterType="map">
        SELECT
        <include refid="Base_Column_List"/>
        FROM SAAGRADE
        <where>
            <include refid="Base_Select_By_Entity_Where"/>
        </where>
    </select>

    <sql id="Select_By_Entity_Where">
        <if test="id != null and id != ''" >
            and ID like '%'||  #{id}||'%'
        </if>
        <if test="gradecname != null and gradecname != ''" >
            and GRADECNAME like '%'|| #{gradecname}||'%'
        </if>
        <if test="validstatus != null and validstatus != ''" >
            and VALIDSTATUS = #{validstatus}
        </if>
        <if test="userInd != null and userInd != ''" >
            and USERIND = #{userInd}
        </if>
    </sql>


    <select id="querySaaGradeListBySet" resultType="ins.channel.power.po.SaaGrade">
        select
        <include refid="Base_Column_List"/>
        from SAAGRADE
        where 1=1
        <if test="addSet != null and addSet.size() != 0">
            and ID in
            <foreach item="item" index="index" collection="addSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryUserInfo" resultType="ins.channel.power.vo.SaausergradeVo">
    select t.id as GRADEID,t.gradecname,case when t2.VALIDSTATUS ='1'   then '1' else '0' END AS FLAG  from SAAGRADE t
    left join SAAUSERGRADE t2
    on t.id = t2.GRADEID and t2.USERCODE = #{param1}
    where t.VALIDSTATUS='1'
      <if test="param2 != null and param2 != ''">
          and t.USERIND = #{param2}
      </if>
    order by t.id
</select>

    <select id="selectNotDiff" resultType="java.lang.Integer">
        select COUNT(*) from SAAGRADE
        <where>
            GRADECNAME =#{gradecname} and ID != #{id}
        </where>
    </select>

    <select id="searchPage" resultType="ins.channel.power.po.SaaGrade">
        SELECT
        <include refid="Base_Column_List"/>
        FROM SAAGRADE
        <where>
            <include refid="Select_By_Entity_Where"/>
        </where>
    </select>
</mapper>