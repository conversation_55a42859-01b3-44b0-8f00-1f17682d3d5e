<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ins.channel.power.dao.SaagradetaskDao">

    <!-- 通用查询结果列-->
    <sql id="BaseColumnList">
		 ID,GRADEID, TASKID, CREATORCODE, UPDATERCODE,
		 VALIDSTATUS, INSERTTIMEFORHIS, OPERATETIMEFORHIS
	</sql>

    <select id="selectMaxId" resultType="java.lang.Long">
        select max (ID) from SAAGRADETASK
    </select>

    <select id="querySaaTaskCodeListByGradeId" resultType="java.lang.String">
        select
        SAATASK.taskCode
        from
        SAAGRADETASK,SAATASK
        <where>
            SAAGRADETASK.taskId = SAATASK.Id and SAAGRADETASK.gradeId = #{gradeId} and SAAGRADETASK.validStatus = '1'
        </where>
    </select>

    <select id="queryAllGradeTaskByGradeIdAndTaskId" resultType="ins.channel.power.po.Saagradetask">
        select
        <include refid="BaseColumnList"/>
        from
        SAAGRADETASK
        <where>
            gradeId = #{gradeId} and taskId = #{taskId}
        </where>
    </select>

    <update id="validSingleGradeTask">
        update
        SAAGRADETASK
        set validStatus = '1',operateTimeForHis = <include refid="mybatis.common.Base_Current" />
        <where>
            gradeId = #{gradeId} and taskId = #{taskId} and validStatus = '0'
        </where>
    </update>

    <update id="invalidGradeTask">
        update
        SAAGRADETASK
        set validStatus = '0',operateTimeForHis = <include refid="mybatis.common.Base_Current" />
        <where>
            gradeId = #{gradeId} and validStatus = '1' and
            taskId in
            <foreach item="item" index="index" collection="taskIdSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="invalidGradeTaskByTaskId">
        update
        SAAGRADETASK
        set validStatus = #{validStatus},operateTimeForHis = <include refid="mybatis.common.Base_Current" />,UPDATERCODE = #{updaterCode}
        <where>
            taskId = #{taskid} and validStatus = '1'
        </where>
    </update>

    <update id="updateSelectiveByGradeId"  parameterType="ins.channel.power.po.Saagradetask">
        update SAAGRADETASK
        <set>
            <if test="updaterCode != null" >
                UPDATERCODE=#{updaterCode},
            </if>
            <if test="validStatus != null" >
                VALIDSTATUS=#{validStatus},
            </if>
            OPERATETIMEFORHIS = <include refid="mybatis.common.Base_Current" />,
        </set>
        where GRADEID = #{gradeid}	</update>
</mapper>