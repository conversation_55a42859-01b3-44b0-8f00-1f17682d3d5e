<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ins.channel.power.dao.SaaTaskDao">

    <select id="queryTaskCodeSetByTaskIdSet" resultType="java.lang.String">
        select
        taskCode
        from SAATASK
        where 1=1
        <if test="taskIdSet != null and taskIdSet.size() != 0">
            and id in
            <foreach item="item" index="index" collection="taskIdSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="taskIdSet != null and taskIdSet.size() == 0">
            and 1=2
        </if>

    </select>

    <select id="querySaaTaskListByTaskCodeSet" resultType="ins.channel.power.po.SaaTask">
        select
        <include refid="BaseColumnList"/>
        from SAATASK
        where 1=1
        <if test="taskCodeSet != null and taskCodeSet.size() != 0">
            and taskCode in
            <foreach item="item" index="index" collection="taskCodeSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="SelectByEntityWhere">
        <if test="id != null">
            and ID = #{id}
        </if>
        <if test="system != null">
            and SYSTEM = #{system}
        </if>
        <if test="taskcode != null">
            and TASKCODE = #{taskcode}
        </if>
        <if test="parentcode != null">
            and PARENTCODE = #{parentcode}
        </if>
        <if test="taskcname != null">
            and TASKCNAME = #{taskcname}
        </if>
        <if test="taskename != null">
            and TASKENAME = #{taskename}
        </if>
        <if test="url != null">
            and URL = #{url}
        </if>
        <if test="creatorcode != null">
            and CREATORCODE = #{creatorcode}
        </if>
        <if test="createtime != null">
            and CREATETIME = #{createtime}
        </if>
        <if test="validflag != null">
            and VALIDFLAG = #{validflag}
        </if>
        <if test="remark != null">
            and REMARK = #{remark}
        </if>
        <if test="flag != null">
            and FLAG = #{flag}
        </if>
        <if test="taskNum != null and taskNum != ''">
            and TASK_NUM = #{taskNum}
        </if>
        and PARENTCODE <![CDATA[<>]]>TASKCODE
    </sql>
    <select id="selectByCondition" resultType="ins.channel.power.po.SaaTask" parameterType="map">
        SELECT
        <include refid="BaseColumnList"/>
        FROM SAATASK
        <where>
            <include refid="SelectByEntityWhere"/>
        </where>
    </select>

    <select id="selectAll" resultType="ins.channel.power.po.SaaTask">
        SELECT
        <include refid="BaseColumnList"/>
        FROM SAATASK
        <where>
            <include refid="SelectByEntityWhere"/>
        </where>
        order by ID
    </select>

    <select id="selectMaxId" resultType="java.lang.Long">
        select max(ID) from SAATASK
    </select>

    <select id="selectParentNameByCode" resultType="java.lang.String">
        select TASKCNAME as parentname from SAATASK
        WHERE TASKCODE = #{parentcode}
    </select>

    <select id="selectAllTaskCode" resultType="java.lang.String">
        select TASKCODE from SAATASK where VALIDFLAG = '1'
    </select>

    <select id="selectNotDiff" resultType="java.lang.Integer">
        select COUNT(*) from SAATASK
        <where>
            <if test="id != null">
                and ID != #{id}
            </if>
            <if test="taskcode != null">
                and TASKCODE = #{taskcode}
            </if>
            <if test="taskNum != null and taskNum != ''">
                and TASK_NUM = #{taskNum}
            </if>
        </where>
    </select>
</mapper>