<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ins.channel.power.dao.SaaUserGradeTaskDao">


    <update id="validGradeTaskByGradeAndTask">
        update
        SAAUSERGRADETASK
        set validStatus = '1',operateTimeForHis = sysdate
        <where>
            gradeId = #{gradeId} and validStatus = '0' and
            taskCode in
            <foreach item="item" index="index" collection="taskCodeSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="invalidGradeTaskByGradeAndTask">
        update
        SAAUSERGRADETASK
        set validStatus = '0',operateTimeForHis = sysdate
        <where>
            gradeId = #{gradeId} and validStatus = '1' and
            taskCode in
            <foreach item="item" index="index" collection="taskCodeSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

</mapper>