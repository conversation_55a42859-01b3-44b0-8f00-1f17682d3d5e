<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.power.dao.SaausergradeDao">
    <!-- 请在下方添加自定义配置-->
    <select id="queryAllUserCode" resultType="java.lang.String">
        select distinct a.USERCODE from SAAUSERGRADE a
        where a.GRADEID in #{list}
    </select>

    <select id="queryRoleList" resultType="java.lang.String">
        select a.GRADEID from SAAUSERGRADE a
        where a.USERCODE = #{userCode}
    </select>


    <update id="invalidGradeByUserCode">
        update
        SAAUSERGRADE
        set validStatus = '0',operateTimeForHis =
        <include refid="mybatis.common.Base_Current"/>
        where
            userCode = #{userCode}
            and validStatus = '1'
    </update>

    <select id="queryEmail" resultType="java.lang.String">
        select DISTINCT EMAIL from T_OPR_INFO t1  inner join SAAUSERGRADE t2
                                                              on t1.USER_CODE = t2.USERCODE
        where t2.GRADEID = #{gradeId} and t1.EMAIL is not null
    </select>

    <select id="getNotifyToByComcode" resultType="map">
        select c.user_code, C.email
        from SAAUSERPERMITDATA A
            inner join saausergrade B
                ON A.Usergradeid = b.id
                   and b.gradeid like '%01'
            inner join T_OPR_INFO C
                on b.usercode = c.user_code
        where A.Datavalue2 = #{comcode}
  </select>

    <select id="getNotifyCCByComcode" resultType="map">
        select c.user_code,C.email
        from SAAUSERPERMITDATA A
            inner join saausergrade B
                ON A.Usergradeid = b.id
                   and b.gradeid like '%02'
            inner join T_OPR_INFO C
                on b.usercode = c.user_code
        where A.Datavalue2 = #{comcode}
    </select>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByGradeId" parameterType="ins.channel.power.po.Saausergrade">
        update SAAUSERGRADE
        <set>
            <if test="updaterCode != null">
                UPDATERCODE=#{updaterCode},
            </if>
            <if test="validStatus != null">
                VALIDSTATUS=#{validStatus},
            </if>
            OPERATETIMEFORHIS =<include refid="mybatis.common.Base_Current"/>,
        </set>
        where GRADEID = #{gradeid}
    </update>

    <select id="queryGradeIdListByUserCode" resultType="java.lang.Long">
        select
        GRADEID
        from
        SAAUSERGRADE
        <where>
            USERCODE =#{userCode} and SAAUSERGRADE.validStatus = '1'
        </where>
    </select>

    <select id="queryUserGradeByUserCodeAndGradeId" resultType="ins.channel.power.po.Saausergrade">
        select
        <include refid="Base_Column_List"/>
        from
        SAAUSERGRADE
        <where>
            usercode = #{userCode} and gradeid = #{gradeid}
        </where>
    </select>

    <select id="selectMaxId" resultType="java.lang.Long">
        select max (ID) from SAAUSERGRADE
    </select>

    <update id="validSingleGradeTask">
        update
        SAAUSERGRADE
        set validStatus = '1',operateTimeForHis =
        <include refid="mybatis.common.Base_Current"/>
        <where>
            usercode = #{userCode} and gradeId = #{gradeId} and validStatus = '0'
        </where>
    </update>

    <update id="invalidGradeTask">
        update
        SAAUSERGRADE
        set validStatus = '0',operateTimeForHis =
        <include refid="mybatis.common.Base_Current"/>
        <where>
            usercode = #{userCode} and validStatus = '1' and
            gradeId in
            <foreach item="item" index="index" collection="gradeIdSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>
    <!-- 按主键删除一条记录 -->
    <delete id="deleteDataByUserCode" parameterType="map">
		delete from SAAUSERGRADE
		where usercode = #{param1}
	</delete>
</mapper>