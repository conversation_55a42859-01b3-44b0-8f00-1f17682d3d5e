<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.power.dao.SaauserpaymentcompanyDao">
    <!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
    <cache-ref namespace="ins.channel.power.dao.SaauserpaymentcompanyDao"/>
    <!-- 请在下方添加自定义配置-->
    <select id="queryByUserCode" resultType="ins.channel.power.po.Saauserpaymentcompany" parameterType="map">
        select
        <include refid="Base_Column_List"/>
        from SAAUSERPAYMENTCOMPANY
        <where>
            <include refid="Base_Select_By_Entity_Where"/>
        </where>
    </select>

    <select id="queryPaymentComcodeListByUserCode" resultType="java.lang.String">
        select
        PAYMENT_COMCODE
        from
        SAAUSERPAYMENTCOMPANY
        <where>
            USER_CODE =#{userCode} and VALID_IND = '1'
        </where>
    </select>

    <select id="queryByUserCodeAndPaymentComcode" resultType="ins.channel.power.po.Saauserpaymentcompany">
        select
        <include refid="Base_Column_List"/>
        from
        SAAUSERPAYMENTCOMPANY
        <where>
            USER_CODE=#{userCode} and PAYMENT_COMCODE=#{paymentComcode}
        </where>
    </select>

    <update id="updateOldDefaultCom">
        update
        SAAUSERPAYMENTCOMPANY
        set DEFAULT_IND = '0',MODIFIED_TIME=<include refid="mybatis.common.Base_Current" />
        where
            USER_CODE = #{param1} and DEFAULT_IND = '1'
    </update>

    <select id="selectMaxId" resultType="java.lang.Long">
		SELECT MAX (to_number(T . ID))
        FROM SAAUSERPAYMENTCOMPANY T
	</select>

    <update id="updatePaymentCom" parameterType="map">
        update SAAUSERPAYMENTCOMPANY
        set
            MODIFIED_TIME=<include refid="mybatis.common.Base_Current" />, DEFAULT_IND=#{defaultInd}, VALID_IND=#{validInd}
        where
            USER_CODE=#{userCode} and PAYMENT_COMCODE=#{paymentComcode}
    </update>

    <select id="queryuserpaymentComListBySet" resultType="ins.channel.power.po.Saauserpaymentcompany">
        select
        <include refid="Base_Column_List"/>
        from SAAUSERPAYMENTCOMPANY
        where 1=1
        and USER_CODE=#{userCode}
        <if test="plusSet != null and plusSet.size() != 0" >
            and  PAYMENT_COMCODE in
            <foreach item="item" index="index" collection="plusSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <delete id="deleteDataByUserCode">
               DELETE FROM SAAUSERPAYMENTCOMPANY
        WHERE user_Code = #{param1}
    </delete>
</mapper>
