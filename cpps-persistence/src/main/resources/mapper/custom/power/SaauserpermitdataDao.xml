<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.power.dao.SaauserpermitdataDao">
    <!-- 请在下方添加自定义配置-->

    <!--<select id="queryAllUserCode" resultType="java.lang.String">
       select distinct a.USERCODE from SAAUSERPERMITDATA a
       where a.DATAVALUE2 companyCodeCode}
    </select>-->

    <select id="queryAllValue2" resultType="java.lang.String">
        select a.DATAVALUE2 from SAAUSERPERMITDATA a
        where a.USERCODE in
        <foreach collection="list" item="item" index="idex" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryValue2" resultType="java.lang.String">
        select a.DATAVALUE2 from SAAUSERPERMITDATA a
        where a.USERCODE = #{userCode}
    </select>

    <select id="queryUserCode" resultType="java.lang.String">
        select a.USERCODE from SAAUSERPERMITDATA a left
        join SAAUSERGRADE b on a.USERGRADEID = b.ID
        where a.DATAVALUE2 in
        <foreach collection="stringList" item="item1" index="index" open="(" separator="," close=")">
            #{item1}
        </foreach>
        and b.GRADEID in
        <foreach collection="roleLists" item="item2" index="index" open="(" separator="," close=")">
            #{item2}
        </foreach>
    </select>

    <update id="invalidDataByUserCode">
        update
        SAAUSERPERMITDATA
        set VALIDFLAG = '0',UPDATETIME=<include refid="mybatis.common.Base_Current"/>,operateTimeForHis =
        <include refid="mybatis.common.Base_Current"/>
        where
        userCode = #{userCode}
        and VALIDFLAG = '1'
    </update>

    <select id="queryAllUserCode" resultType="java.lang.String">
        select distinct a.USERCODE from SAAUSERPERMITDATA a
        where a.DATAVALUE2 in
        <foreach collection="list" item="item" index="idex" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryUserGradeOrgInfo" resultType="ins.channel.company.po.Ggcompany">
        select t1.* from SAAUSERPERMITDATA t left join GGCOMPANY t1
        on t1.COMPANY_CODE = t.DATAVALUE2
        left join SAAUSERGRADE t2
        on t.USERGRADEID = t2.ID
        where t.USERCODE=#{userCode} and t2.GRADEID =#{gradeid} and t1.VALID_IND = '1'
    </select>

    <select id="queryComcodeListByUserCode" resultType="java.lang.String">
        select
        DATAVALUE2
        from
        SAAUSERPERMITDATA
        <where>
            USERCODE =#{userCode} and VALIDFLAG = '1'
        </where>
    </select>

    <select id="queryUserPermitdataByUserCodeAndComCode" resultType="ins.channel.power.po.Saauserpermitdata">
        select
        *
        from
        SAAUSERPERMITDATA
        <where>
            USERCODE =#{userCode} and DATAVALUE2=#{comCode}
        </where>
    </select>

    <select id="selectMaxId" resultType="java.lang.Long">
        select max(ID) from SAAUSERPERMITDATA
    </select>

    <update id="validSingleUserpermitdata">
        update
        SAAUSERPERMITDATA
        set VALIDFLAG = '1',UPDATETIME=<include refid="mybatis.common.Base_Current" />,operateTimeForHis = <include refid="mybatis.common.Base_Current" />
        <where>
            userCode = #{userCode} and DATAVALUE2 = #{comCode} and VALIDFLAG = '0'
        </where>
    </update>

    <update id="invalidvalidSingleUserpermitdata">
        update
        SAAUSERPERMITDATA
        set VALIDFLAG = '0',UPDATETIME=<include refid="mybatis.common.Base_Current" />,operateTimeForHis = <include refid="mybatis.common.Base_Current" />
        <where>
            userCode = #{userCode} and VALIDFLAG = '1' and
            DATAVALUE2 in
            <foreach item="item" index="index" collection="comCodeSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <select id="queryGgCompanyByLoginUser" resultType="ins.channel.power.vo.UserpermitdataVo">
        SELECT
	      t.USERCODE,t1.COMPANY_CODE AS comCode,t1.COMPANY_CNAME AS companyCname
        FROM SAAUSERPERMITDATA t LEFT JOIN GGCOMPANY t1 ON t1.COMPANY_CODE = t .DATAVALUE2
        WHERE t .USERCODE = #{userCode} AND t .VALIDFLAG = t1.VALID_IND AND t .VALIDFLAG = '1' order by comCode
    </select>
</mapper>