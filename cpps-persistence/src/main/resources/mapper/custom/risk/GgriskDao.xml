<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.risk.dao.GgriskDao">
	<!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
	<cache-ref  namespace="ins.channel.risk.dao.GgriskDao"/>
	<!-- 请在下方添加自定义配置-->

	<select id="searchPage" resultMap="BaseResultMap" parameterType="ins.channel.risk.po.Ggrisk">
		select
		<include refid="Base_Column_List" />
		from GGRISK
		<where>
			<if test="riskCode != null and riskCode != ''" >
				and RISK_CODE = #{riskCode}
			</if>
			<if test="riskClass != null and riskClass != ''" >
				and RISK_CLASS = #{riskClass}
			</if>
			<if test="validInd != null and validInd != ''">
				and VALID_IND = #{validInd}
			</if>
			<if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
				and MODIFIED_TIME between to_date(concat(#{startTime},' 00:00:00'),'YYYY-MM-DD HH24:MI:SS') and
				to_date(concat(#{endTime},' 23:59:59'),'YYYY-MM-DD HH24:MI:SS')
			</if>
		</where>
	</select>

	<!-- 完整插入一条记录-->
	<insert id="insertAuto"  parameterType="ins.channel.risk.po.Ggrisk">
		insert into GGRISK (
			RISK_CODE,
			RISK_CNAME,
			RISK_TNAME,
			RISK_ENAME,
			RISK_CLASS,
			OPENCOVER_IND,
			VALIDDATE,
			INVALIDDATE,
			VALID_IND,
			REMARK,
			FLAG,
			CREATE_TIME,
			MODIFIED_TIME
		) values (
					 #{riskCode},
					 #{riskCname},
					 #{riskTname},
					 #{riskEname},
					 #{riskClass},
					 #{opencoverInd},
					 #{validDate},
					 #{invalidDate},
					 #{validInd},
					 #{remark},
					 #{flag},
					 #{createTime},
					 #{modifiedTime}
				 )
	</insert>
	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelectiveAuto"  parameterType="ins.channel.risk.po.Ggrisk">
		insert into GGRISK
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="riskCode != null" >
				RISK_CODE,
			</if>
			<if test="riskCname != null" >
				RISK_CNAME,
			</if>
			<if test="riskTname != null" >
				RISK_TNAME,
			</if>
			<if test="riskEname != null" >
				RISK_ENAME,
			</if>
			<if test="riskClass != null" >
				RISK_CLASS,
			</if>
			<if test="opencoverInd != null" >
				OPENCOVER_IND,
			</if>
			<if test="validDate != null" >
				VALIDDATE,
			</if>
			<if test="invalidDate != null" >
				INVALIDDATE,
			</if>
			<if test="validInd != null" >
				VALID_IND,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="createTime != null" >
				CREATE_TIME,
			</if>
			<if test="modifiedTime != null" >
				MODIFIED_TIME
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
		<if test="riskCode != null" >
			#{riskCode},
		</if>
		<if test="riskCname != null" >
			#{riskCname},
		</if>
		<if test="riskTname != null" >
			#{riskTname},
		</if>
		<if test="riskEname != null" >
			#{riskEname},
		</if>
		<if test="riskClass != null" >
			#{riskClass},
		</if>
		<if test="opencoverInd != null" >
			#{opencoverInd},
		</if>
		<if test="validDate != null" >
			#{validDate},
		</if>
		<if test="invalidDate != null" >
			#{invalidDate},
		</if>
		<if test="validInd != null" >
			#{validInd},
		</if>
		<if test="remark != null" >
			#{remark},
		</if>
		<if test="flag != null" >
			#{flag},
		</if>
		<if test="createTime != null" >
			#{createTime},
		</if>
		<if test="modifiedTime != null" >
			#{modifiedTime}
		</if>
	</trim>
	</insert>

	<!-- 将CodeCode转换成相应文字 -->
	<select id="translate" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
		<include refid="Base_Column_List"/>
		from GGRISK
		<where>
			VALID_IND = '1'
			and risk_code = #{param1}
		</where>
	</select>

	<resultMap id="RiskInfoForSelect" type="ins.channel.risk.po.GgriskSelect">
		<result column="risk_CODE" property="riskCode" />
		<result column="risk_CNAME" property="riskCname" />
	</resultMap>
	<!-- 仅查询riskrisk ,riskType 进行分页查询 -->
	<select id="riskInfoForSelectPage" resultMap="RiskInfoForSelect" parameterType="java.lang.String">
		select
		risk_CODE,risk_CNAME
		from GGRISK
		<where>
			VALID_IND = '1'
			<if test="queryInfo != null and queryInfo != ''">
				and (risk_CODE like concat(concat('%',#{queryInfo}),'%')
				or risk_CNAME like concat(concat('%',#{queryInfo}),'%'))
			</if>
		</where>
	</select>

	<select id="riskInfoForSelect" resultMap="RiskInfoForSelect" parameterType="java.lang.String">
		select
		risk_CODE,risk_CNAME
		from GGRISK
		<where>
			VALID_IND = '1'
			<if test="riskCode != null and riskCode != ''">
				and risk_CODE = #{riskCode}
			</if>
			<if test="riskCname != null and riskCname != ''">
				and risk_Cname = #{riskCname}
			</if>
		</where>
	</select>

	<resultMap id="BaseCodeResultMap" type="ins.channel.baseCode.po.BaseCode">
		<result column="CODE_TYPE" property="codeType" />
		<result column="CODE_CODE" property="codeCode" />
		<result column="CODE_NAME" property="codeName" />
	</resultMap>
	<!--Modify By Zhoutaoyu 查询所有险种信息(供基础码表查询使用) 2019/11/23-->
	<select id="queryAll" resultMap="BaseCodeResultMap">
		SELECT T.RISK_CODE AS CODE_CODE,
		T.RISK_CNAME AS CODE_NAME,
		'risk' AS CODE_TYPE
		FROM GGRISK T
		WHERE T.VALID_IND = '1'
	</select>

	<resultMap id="CodeInfoForSelect" type="ins.channel.code.po.GgcodeSelect">
		<result column="RISK_CODE" property="codeCode" />
		<result column="RISK_CNAME" property="codeCname" />
	</resultMap>
	<select id="riskForSelectAll" resultMap="CodeInfoForSelect">
		SELECT T.RISK_CODE,
		T.RISK_CNAME
		FROM GGRISK T
		WHERE T.VALID_IND = '1'
	</select>
</mapper>
