<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.user.dao.GguserDao">
	<!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
	<cache-ref  namespace="ins.channel.user.dao.GguserDao"/>
	<!-- 请在下方添加自定义配置-->
	<!--分页模糊查询-->
	<select id="pageByCondition" resultMap="BaseResultMap" parameterType="ins.channel.user.po.Gguser">
		select
		<include refid="Base_Column_List" />
		from GGUSER
		<where>
			<if test="userCode != null and userCode != ''" >
				and USER_CODE like '%'|| #{userCode}||'%'
			</if>
			<if test="userCname != null and userCname != ''" >
				and USER_CNAME like '%'|| #{userCname}||'%'
			</if>
			<if test="validInd != null and validInd != ''" >
				and VALID_IND = #{validInd}
			</if>
			<if test="userInd != null and userInd != ''" >
				and USER_IND = #{userInd}
			</if>
		</where>
	</select>



	<!--Modify By zhoajie 查询所有码表(供基础码表查询使用) 2019/11/20-->
	<resultMap id="BaseCodeResultMap" type="ins.channel.baseCode.po.BaseCode">
		<result column="CODE_TYPE" property="codeType" />
		<result column="CODE_CODE" property="codeCode" />
		<result column="CODE_NAME" property="codeName" />
	</resultMap>

	<select id="queryAll" resultMap="BaseCodeResultMap">
		select  'userCode'   as  codeType,
		  user_code    as  codeCode,
		  user_cname   as  codeName
		from  GGUSER
		WHERE VALID_IND = '1'
    </select>

    <select id="selectByCondition" resultType="ins.channel.user.po.Gguser" parameterType="map">
        SELECT
        <include refid="Base_Column_List"/>
        FROM GGUSER
        <where>
            <include refid="Base_Select_By_Entity_Where"/>
        </where>
    </select>

	<select id="searchUserListByCompanyCode" resultType="ins.channel.user.po.Gguser" parameterType="string">
        SELECT
        <include refid="Base_Column_List"/>
        FROM GGUSER
        where USER_CODE in (select USERCODE from SAAUSERPERMITDATA where DATAVALUE2 = #{companyCode})
    </select>

</mapper>
