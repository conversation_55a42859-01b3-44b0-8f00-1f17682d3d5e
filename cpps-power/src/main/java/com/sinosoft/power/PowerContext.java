package com.sinosoft.power;


import com.sinosoft.power.common.util.MybatisUtil;
import com.sinosoft.power.common.util.SpringContextUtil;
import com.sinosoft.power.service.*;
import com.sinosoft.power.service.service.impl.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;

/**
 * @author: yancun
 * @create: 2018/12/19 13:49
 * @description:权限上下文
 */
public class PowerContext {
    @Getter
    @Setter
    private SqlSession sqlSessionTemplate;

    private static PowerContext powerContext;

    /**
     * 创建当前线程的PowerContext 采用线程安全的单例模式
     *
     * @return PowerContext
     */
    public static PowerContext getInstance() {
        if (powerContext == null) {
            synchronized (PowerContext.class) {
                if (powerContext == null) {
                    powerContext = new PowerContext();
                }
            }
        }
        return powerContext;

    }

    private PowerContext() {
        initSession();
    }

    private void initSession() {
        //获取主工程的erp数据源
        if (SpringContextUtil.isContextExists()) {

            sqlSessionTemplate = (SqlSessionTemplate) SpringContextUtil.getBean("sqlSessionTemplate");

        } else {
            //本地测试专用
            this.initLocalSession(MybatisUtil.getSessionFactory(null));
        }
    }

    private void initLocalSession(SqlSessionFactory sessionFactory) {
        this.sqlSessionTemplate = sessionFactory.openSession(true);
    }

    public PowerService getPowerService() {
        return new PowerServiceImpl(this.sqlSessionTemplate);
    }

    public SaaDeployService getSaaDeployService() {
        return new SaaDeployServiceImpl(this.sqlSessionTemplate);
    }

    public FilterPowerService getFilterPowerService() {
        return new FilterPowerServiceImpl(this.sqlSessionTemplate);
    }

    public SaaPowerDaoService getSaaPowerDaoService() {
        return new SaaPowerDaoServiceImpl(this.sqlSessionTemplate);
    }

    public CompanyService getCompanyService() {
        return new CompanyService(this.sqlSessionTemplate);
    }

    public SaaUserService getUserService() {
        return new SaaUserService(this.sqlSessionTemplate);
    }

    public SaaTaskService getSaaTaskService() {
        return new SaaTaskServiceImpl(this.sqlSessionTemplate);
    }


}
