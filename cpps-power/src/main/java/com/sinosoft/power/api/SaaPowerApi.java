package com.sinosoft.power.api;

import com.sinosoft.power.PowerContext;
import com.sinosoft.power.common.exception.PowerException;
import com.sinosoft.power.po.*;
import com.sinosoft.power.service.*;
import com.sinosoft.power.util.PowerTaskCode;
import com.sinosoft.power.vo.CompanyVo;
import com.sinosoft.power.vo.ExportUserPowerInfoVo;
import com.sinosoft.power.vo.PowerKeyWord;
import com.sinosoft.power.vo.SysPowerUserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.sinosoft.power.util.PowerConstant.*;

/**
 * @author: yancun
 * @Date: 2018/12/17 14:20
 * @Version:1.0
 * @Description:权限独立Api,将权限相关的代码独立出来，此Api是对外调用的入口 *
 * 具体用法:
 * 例1:校验某个人是否具有案件查询的功能权限
 * boolean flag = SaaPowerApi.getInstance().checkPower("0000000000",CNPowerTaskCode.CNCLAIM_MENU_CASEQUERY);
 * <p>
 * 例2:根据用户code查询用户信息
 * SysPowerUserVo userInfo = SaaPowerApi.getInstance().findUserByUserCode("0000000000");
 */
@Slf4j
public class SaaPowerApi {

    /**
     * 权限上下文,当主工程调用权限子工程时，子过程会先判断当前的spring容器
     * 如果有，操作的数据库是主工程的，否则是此工程的。
     */
    private PowerContext powerContext;

    private static SaaPowerApi powerApi;

    /**
     * 生成权限Api实例，外界调用时请调用此方法,采用线程安全的单例模式
     */
    public static SaaPowerApi getInstance() {
        if (powerApi == null) {
            synchronized (SaaPowerApi.class) {
                if (powerApi == null) {
                    powerApi = new SaaPowerApi();
                }
            }
        }
        return powerApi;
    }

    /**
     * 私有构造器,生成权限上下文实例，采用主工程的erp数据源,
     * 请在主工程暴露一个erpSqlSessionTemplate的bean，类型是sqlSession
     */
    private SaaPowerApi() {
        powerContext = PowerContext.getInstance();
    }

    /**
     * 获取LpPowerService实例
     *
     * @return PowerService
     */
    public PowerService getPowerService() {
        return powerContext.getPowerService();
    }

    /**
     * 获取SaaDeployService实例
     *
     * @return SaaDeployService
     */
    public SaaDeployService getSaaDeployService() {
        return powerContext.getSaaDeployService();
    }

    /**
     * 获取FilterPowerService实例
     *
     * @return FilterPowerService
     */
    public FilterPowerService getFilterPowerService() {
        return powerContext.getFilterPowerService();
    }

    /**
     * 获取SaaPowerDaoService实例
     *
     * @return SaaPowerDaoService
     */
    public SaaPowerDaoService getPowerDaoService() {
        return powerContext.getSaaPowerDaoService();
    }

    /**
     * 获取CompanyService实例
     *
     * @return CompanyService
     */
    public CompanyService getCompanyService() {
        return powerContext.getCompanyService();
    }

    /**
     * 获取SaaUserService实例
     *
     * @return SaaUserService
     */
    public SaaUserService getUserService() {
        return powerContext.getUserService();
    }

    /**
     * 获取SaaTaskService实例
     *
     * @return SaaTaskService
     */
    public SaaTaskService getSaaTaskService() {
        return powerContext.getSaaTaskService();
    }


    /**
     * 功能描述: 校验用户是否具有系统维护权限
     *
     * @param: userCode
     * @param: powerTaskCode
     * @return: boolean
     */
    public void checkSysPower(String userCode, String powerTaskCode) {
        this.getPowerService().checkSysPower(userCode, powerTaskCode);
    }


    //*********************以下提供一些公共方法****************************


    /**
     * 工作流相关权限校验，数据权限sql的拼装
     */
    public Map<String, String> addPower(String userCode, String taskCode) {
        Map<String, String> powerMap = new HashMap();
        List<Long> gradeList = new ArrayList();
        Set<Long> powerList = new HashSet<>();
        new ArrayList();
        if (userCode.equals("0000000000")) {
            powerMap.put(POWER_CLASS, SQL_EQUALS);
            powerMap.put(POWER_COM, SQL_EQUALS);
            return powerMap;
        } else {
            List<SaaUserGradeTask> list = getPowerDaoService().queryTaskListByUserCodeAndTaskCode(userCode, taskCode);
            Iterator var13 = list.iterator();

            while (var13.hasNext()) {
                SaaUserGradeTask saaUserGradeTask = (SaaUserGradeTask) var13.next();
                gradeList.add(saaUserGradeTask.getGradeId());
            }

            if (gradeList.isEmpty() || gradeList.size() <= 0) {
                throw new PowerException("校验权限失败！");
            }
            Set<Long> gradeIdSet = new HashSet<>(gradeList);
            List<SaaUserGrade> saaUserGradeBpmList = getPowerDaoService().queryGradeByUserCodeAndGradeIdSet(userCode, gradeIdSet);

            for (SaaUserGrade saaUserGradeBpm : saaUserGradeBpmList) {
                powerList.add(saaUserGradeBpm.getId());
            }
            if (!powerList.isEmpty()) {
                String classPerSql;
                List<SaaUserPermitData> com = getPowerDaoService().queryUserAllPermitData(userCode, "COM", powerList);
                classPerSql = this.getComPerRange(com);
                powerMap.put(POWER_COM, classPerSql);
                if (PowerTaskCode.CLAIM_REGIST_PREREGIST.equals(taskCode)) {
                    powerMap.put(POWER_CLASS, SQL_EQUALS);
                } else {
                    List<SaaUserPermitData> risk = getPowerDaoService().queryUserAllPermitData(userCode, "CLASS", powerList);
                    classPerSql = this.getRiskPerRange(risk);
                    powerMap.put(POWER_CLASS, classPerSql);
                }

            }
            return powerMap;
        }
    }

    private String getComPerRange(List<SaaUserPermitData> list) {
        StringBuilder builder = new StringBuilder();
        StringBuilder resBuilder = new StringBuilder();
        if (!list.isEmpty()) {
            resBuilder.append(" ( ");
            Iterator iterator = list.iterator();

            while (iterator.hasNext()) {
                SaaUserPermitData saaUserPermitData = (SaaUserPermitData) iterator.next();
                builder.append("or upperPath like '");
                builder.append(saaUserPermitData.getDataValue1());
                builder.append("%' ");
            }

            resBuilder.append(builder.toString().substring(builder.toString().indexOf("or") + 2));
            resBuilder.append(" )");
        } else {
            resBuilder.append(" (");
            builder.append(SQL_NOT_EQUALS);
            builder.append(") ");
            resBuilder.append(builder.toString());
        }

        return resBuilder.toString();
    }

    private String getRiskPerRange(List<SaaUserPermitData> list) {
        StringBuilder builder = new StringBuilder();
        StringBuilder resBuilder = new StringBuilder();
        if (!list.isEmpty()) {
            resBuilder.append(" (  ");
            Iterator iterator;
            SaaUserPermitData saaUserPermitData;
            iterator = list.iterator();
            while (iterator.hasNext()) {
                saaUserPermitData = (SaaUserPermitData) iterator.next();
                builder.append("or ");
                builder.append("classCode");
                if ("%".equals(saaUserPermitData.getDataValue1())) {
                    builder.append(" like '");
                } else {
                    builder.append(" = '");
                }
                builder.append(saaUserPermitData.getDataValue1());
                builder.append("' ");
            }
            //解决无保单报案,classCode为空的问题
            builder.append("or classCode is null");
            resBuilder.append(builder.toString().substring(builder.toString().indexOf("or") + 2));
            resBuilder.append(" )");
        } else {
            resBuilder.append(" (");
            builder.append(SQL_NOT_EQUALS);
            builder.append(") ");
            resBuilder.append(builder.toString());
        }

        return resBuilder.toString();
    }

    /**
     * 工作流相关功能权限的校验
     *
     * @param userCode
     * @param taskCode
     * @return
     */
    public boolean checkBpmPower(String userCode, String taskCode) {
        if ("0000000000".equals(userCode)) {
            return true;
        }
        List<SaaUserGrade> saaUserGrades = getPowerDaoService().queryGradeByUserCodeAndGradeId(userCode, (Long) null);
        if (CollectionUtils.isEmpty(saaUserGrades)) {
            return false;
        } else {
            List<SaaUserGradeTask> lists = getPowerDaoService().queryTaskListByUserCodeAndTaskCode(userCode, taskCode);
            if (CollectionUtils.isEmpty(lists)) {
                return false;
            }
            for (SaaUserGradeTask list : lists) {
                for (SaaUserGrade saaUserGrade : saaUserGrades) {
                    if (saaUserGrade.getGradeId().equals(list.getGradeId())) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 功能描述: 校验用户是否具有某种权限，有返回true，没有返回false
     *
     * @param: userCode
     * @param: powerTaskCode  建议传CNPowerTaskCode
     * @return: boolean
     */
    public boolean checkPower(String userCode, String powerTaskCode) {
        return getPowerService().checkPower(userCode, powerTaskCode);
    }

    /**
     *  根据系统来源获取不同系统下的功能权限
     * @param systemCode
     * @return
     */
    public List<SaaTask> getSaaTaskBySystem(String systemCode,String id){
        return getPowerDaoService().getSaaTaskBySystem(systemCode,id);
    }

    /**
     *  根据系统来源获取不同系统下的功能权限
     * @param systemCode
     * @return
     */
    public List<SaaTask> getTaskListBySystem(String systemCode){
        return getPowerDaoService().getTaskListBySystem(systemCode);
    }

    /**
     * 功能描述: 查询用户下有相应权限的岗位
     *
     * @param: param
     * @return: List<SaaUserGrade>
     */
    public List<SaaUserGrade> findUserGradeByUserCodeAndTaskCode(Map<String, String> param) {
        return getPowerDaoService().findUserGradeByUserCodeAndTaskCode(param);
    }

    /**
     * 功能描述:查询具有相应条件的数据权限表（saauserpermitdata）的所有值
     *
     * @param: factorcode：因子编码
     * taskCode:权限编码
     * userCode：用户编码
     * validStatus：状态
     * @return: List<SaaUserPermitData>
     */
    public List<SaaUserPermitData> queryUserPermitDataByCondition(Map<String, String> param) {
        return getPowerDaoService().queryUserPermitDataByCondition(param);
    }

    /**
     * 功能描述: 根据用户代码和权限代码查询用户下的具有某权限的有效数据权限表的datavalue2
     *
     * @param:userCode 用户编码
     * @param:taskcode 权限编码
     * @return: List<String>
     */
    public List<String> getPermitComcode(String userCode, String taskcode) {
        return getPowerDaoService().getPermitComcode(userCode, taskcode);
    }

    /**
     * 功能描述:
     *
     * @param: userCode 用户编码
     * @param: factorCode 因子编码
     * @return: List
     */
    public List<SaaUserPermitData> queryPermitDataByUserCodeAndFactorCode(String userCode, String factorCode, String userGradeId) {
        return getPowerDaoService().queryPermitDataByUserCodeAndFactorCode(userCode, factorCode, userGradeId);
    }


    /**
     * 功能描述:根据人员代码获取该人员的权限机构代码
     *
     * @param userCode 用户代码
     * @return
     */
    public String selectUserPermitComByUserCode(String userCode) {
        return getPowerService().selectUserPermitComByUserCode(userCode);
    }

    /**
     * 功能描述:根据人员代码查询该人员具有的taskCode集合
     *
     * @param: userCode  用户代码
     * @return:
     */
    public List<String> queryTaskCodeListByUser(String userCode) {
        return getPowerService().queryTaskCodeListByUser(userCode);
    }

    /**
     * 功能描述:通过机构，险种，板块，功能代码，用户判断此用户是否有此数据权限
     *
     * @param: powerKeyWord
     * @param: userCode
     * @return:
     */
    public boolean assertUserDataPower(PowerKeyWord powerKeyWord, String userCode) {
        return getPowerService().assertUserDataPower(powerKeyWord, userCode);
    }

    /**
     * 功能描述:获取某机构下的拥有某节点权限的所有人员列表
     *
     * @param:
     * @return:
     */
    public List<SysPowerUserVo> queryPowerUserList(PowerKeyWord powerKeyWord, String loginUserCode) {
        return getPowerService().queryPowerUserList(powerKeyWord, loginUserCode);
    }

    /**
     * 功能描述:校验传入的taskCode用户是否具备
     *
     * @param:
     * @return:
     */
    public boolean validTaskCode(String taskCode, String userCode) {
        return getPowerService().validTaskCode(taskCode, userCode);
    }

    /**
     * 功能描述:获取用户右侧菜单按钮权限
     *
     * @param:
     * @return:
     */
    public List<String> queryTaskCodeByUserCode(String userCode) {
        return getSaaDeployService().queryTaskCodeByUserCode(userCode);
    }

    /**
     * 功能描述:数据权限校验
     *
     * @param:
     * @return:
     */
    public boolean validateDataPower(String userCode, String taskCode, String comCode, String classCode) {
        return getPowerService().validateDataPower(userCode, taskCode, comCode, classCode);
    }

    /**
     * 功能描述:根据用户代码查询用户金额
     *
     * @param:
     * @return:
     */
    public List<SaaUserAmount> queryUserAmountByUserCode(String userCode) {
        return getPowerDaoService().queryUserAmountByUserCode(userCode);
    }

    /**
     * 功能描述:根据用户代码和权限代码查询用户金额，taskCode可以为空
     *
     * @param:
     * @return:
     */
    public SaaUserAmount queryUserAmout(String userCode, String taskCode) {
        return getPowerDaoService().queryUserAmout(userCode, taskCode);
    }

    /**
     * 功能描述:查询所有的功能权限
     *
     * @param:
     * @return:
     */
    public List<SaaTask> selectAllTask() {
        return getPowerDaoService().selectAll();
    }

    /**
     * 功能描述: 查询岗位下的权限
     *
     * @param:
     * @return:
     */
    public List<SaaTask> selectTaskByGradeId(String id) {
        return getPowerDaoService().selectByGradeId(id);
    }

    /**
     * 功能描述:根据用户代码查询用户具有的有效权限代码的集合
     *
     * @param:
     * @return:
     */
    public List<String> queryGradeTaskCodeListByUserCode(String userCode) {
        return getPowerDaoService().queryGradeTaskCodeListByUserCode(userCode);
    }

    /**
     * 功能描述:查询用户的用户岗位下所具有的权限集合。
     *
     * @param:
     * @return:
     */
    public List<SaaUserGradeTask> queryTaskListByUserCodeAndTaskCode(String userCode, String taskCode) {
        return getPowerDaoService().queryTaskListByUserCodeAndTaskCode(userCode, taskCode);
    }

    public SaaUserAmount queryUserAmoutByCodeAndRisk(String userCode, String taskCode,
                                                     String riskCode, String taskParentCode) {
        return getPowerDaoService().queryUserAmoutByCodeAndRisk(userCode, taskCode, riskCode, taskParentCode);
    }

    /**
     * 功能描述:根据险种、机构、理算金额、人员权限、金额权限查询出所有用户金额
     *
     * @param: map
     * @return: List<SaaUserAmount>
     */
    public List<SaaUserAmount> queryUserAmountByUndwrtPower(Map<String, String> map) {
        return getPowerDaoService().queryUserAmountByUndwrtPower(map);
    }

    /**
     * @Description:查询某个人是否具有某个功能权限并且具有某个数据权限
     * @param:map userCode 用户代码 必填
     * taskCode  权限代码 可填
     * factorCode  权限因子 可填
     * classCodeSet 数据权限代码  可填
     * @return:List<SaaUserGradeTask>
     */
    public List<SaaUserGradeTask> queryGradeTaskByCondtion(Map<String, Object> map) {
        return getPowerDaoService().queryGradeTaskByCondtion(map);

    }

    /**
     * @Description:用户权限清单导出Api
     * @param: map
     * @return: List<ExportUserPowerInfoVo>
     */
    public List<ExportUserPowerInfoVo> exportUserPowerInfo(Map<String, Object> map) {

        List<ExportUserPowerInfoVo> userPowerInfoList = getPowerDaoService().exportPowerUserInfo(map);
        //查询岗位下的数据权限，包括机构和险类
        if (!CollectionUtils.isEmpty(userPowerInfoList)) {

            for (ExportUserPowerInfoVo powerInfo : userPowerInfoList) {

                if (StringUtils.isNotEmpty(powerInfo.getUserGradeId())) {

                    //组装机构权限信息，用,分割
                    List<SaaUserPermitData> comDataList = getPowerDaoService().queryPermitDataByUserCodeAndFactorCode(powerInfo.getUserCode(), FACTOR_COM, powerInfo.getUserGradeId());
                    powerInfo.setPermitCom(assemblePermitData(comDataList, FACTOR_COM));

                    //组装险类权限信息，用,分割
                    List<SaaUserPermitData> classDataList = getPowerDaoService().queryPermitDataByUserCodeAndFactorCode(powerInfo.getUserCode(), FACTOR_CLASS, powerInfo.getUserGradeId());
                    powerInfo.setPermitClass(assemblePermitData(classDataList, FACTOR_CLASS));
                }
            }
        }
        return userPowerInfoList;
    }

    //组装机构、险类权限信息  用逗号分隔
    private String assemblePermitData(List<SaaUserPermitData> dataList, String factor) {
        if (CollectionUtils.isEmpty(dataList)) {
            return StringUtils.EMPTY;
        }
        StringBuilder sb = new StringBuilder();
        int length = dataList.size();
        int i = 0;
        for (SaaUserPermitData classData : dataList) {
            if (FACTOR_CLASS.equals(factor)) {
                sb.append(classData.getDataValue1());
            } else if (FACTOR_COM.equals(factor)) {
                sb.append(classData.getDataValue2());
            }
            if (i < length - 1) {
                sb.append(",");
                i++;
            }
        }
        return sb.toString();
    }

    /**
     * @param :Map<String,String> params
     * @param :params中            taskCode 为权限代码  comCode查出的人是否属于这个机构(非必传) companyCode 需要数据权限校验的机构
     * @param :params中            classCode 险类 amountTaskCode 项目类别(自动、人工) amountTaskParentCode(权限类别) amount 金额
     * @param :loginUserCode      当前登录人
     * @Description:找出险类、机构数据权限和险类金额权限都满足的人
     * @return:List<SysPowerUserVo>
     */
    public List<SysPowerUserVo> queryCondtionPowerUserList(Map<String, String> params, String loginUserCode) {
        List<SysPowerUserVo> powerUserVoList = new ArrayList<>();
        List<SysPowerUserVo> userList = getFilterPowerService().queryUserListByParams(params, loginUserCode);
        Map<String, String> map = new HashMap<>();
        map.put(AMOUNT_TASKCODE, params.get(AMOUNT_TASKCODE));
        map.put(AMOUNT_TASKPARENTCODE, params.get(AMOUNT_TASKPARENTCODE));
        map.put(CLASS_CODE, params.get(CLASS_CODE));
        map.put(AMOUNT, params.get(AMOUNT));

        if (!CollectionUtils.isEmpty(userList)) {
            for (SysPowerUserVo userVo : userList) {
                map.put(USER_CODE, userVo.getUserCode());
                List<SaaUserAmount> amountList = getPowerDaoService().selectUserAmountPermit(map);
                if (!CollectionUtils.isEmpty(amountList)) {
                    powerUserVoList.add(userVo);
                }
                map.remove(USER_CODE);
            }
        }
        return powerUserVoList;

    }
    //***************************以下向外提供机构Api******************************

    /**
     * @Description:同步单个机构信息,网关调用
     */
    public String syncComData(CompanyVo companyVo) {
        return getCompanyService().syncComData(companyVo);
    }

    /**
     * @Description:同步全量机构信息,网关调用
     */
    public String handFullSyncCompanyData() {
        return getCompanyService().handFullSyncCompanyData();
    }

    /**
     * @Description:根据comCode查询机构信息
     * @param:
     * @return:PrpDCompany
     */
    public PrpDCompany queryCompanyBycomCode(String comCode) {
        return getCompanyService().querycompanyBycomCode(comCode);
    }

    /**
     * @Description:根据机构代码查询upperpath
     */
    public String queryCompanyUpperPath(String comCode) {
        return getCompanyService().queryCompanyUpperPath(comCode);
    }


    /**
     * @param comCode
     * @return String
     * @Description:联合printComName 和comCname作为查询结果
     */
    public String findContactComNameByComCode(String comCode) {
        return getCompanyService().findContactComNameByComCode(comCode);
    }

    /**
     * @Description:查询所有的comCode
     * @return:List<String>
     */
    public List<String> queryComCodeAll() {
        return getCompanyService().queryComCodeAll();
    }

    /**
     * @Description:根据comCode查询所有子comcode
     * @param:comcode
     * @return:list
     */
    public List<String> queryPriorComCodeList(String comCode) {
        return getCompanyService().queryPriorComCodeList(comCode);
    }

    /**
     * 查询用户对应comCode的机构信息
     * @param userCode
     * @return PrpDCompany
     */
    public PrpDCompany queryCompanyByUserCode(String userCode){
        return getCompanyService().queryCompanyByUserCode(userCode);
    }

    /**
     * 查当前机构代码的所属分公司代码
     * @param comCode 机构代码
     * @param comLevel 用户等级
     * @return list
     */
    public List<PrpDCompany>  queryPriorCompanyByComLevel(String comCode,String comLevel){
        return getCompanyService().queryPriorCompanyByComLevel(comCode,comLevel);

    }

    /**
     * @Description:查询局部机构信息 by zuoyanhua 供查询机构使用
     */
    public List<CompanyVo> queryAllCompanyVo() {
        return getCompanyService().queryAllData();
    }

    //***************************以下向外提供用户Api******************************


    /**
     * @param 用户代码
     * @Description:根据用户代码查询用户信息
     * @return: userVo
     */
    public SysPowerUserVo findUserByUserCode(String userCode) {
        return getUserService().findUserByUserCode(userCode);
    }


    /**
     * 方法描述：根据机构(包括子机构)下的用户 有案件处理权限的人
     *
     * @param comCode
     * @return
     */
    public List<SysPowerUserVo> queryUserByComCode(String comCode) {
        return getUserService().queryUserByComCode(comCode);
    }

    /**
     * 方法描述：根据机构(包括子机构)下所有的用户
     *
     * @param comCode
     * @return
     */
    public List<SysPowerUserVo> querySecondUserByComCode(String comCode) {
        return getUserService().querySecondUserByComCode(comCode);
    }
    /**
     * 查询具有某功能权限并且有某机构权限的所有人
     * @param makeCom 非必传 机构权限
     * @param taskCode 非必传 功能权限
     */
    public List<SysPowerUserVo> queryComCondtionPowerUserList(String makeCom,String taskCode){
        return getUserService().queryComCondtionPowerUserList(makeCom,taskCode);
    }

    /**
     * 方法描述：获取机构下所有的用户
     *
     * @param comCode
     * @return
     */
    public List<SysPowerUserVo> queryAllUserByComCode(String comCode) {
        return getUserService().queryAllUserByComCode(comCode);
    }

    /**
     * 方法描述：根据机构具有某一权限的用户
     *
     * @param comCode
     * @return
     */
    public List<SysPowerUserVo> querySecondComUser(String comCode, String taskCode) {
        return getUserService().querySecondComUser(comCode, taskCode);
    }

    /**
     * 方法描述：获取机构下有某一对应任务权限的用户
     *
     * @param map
     * @return
     */
    public List<SysPowerUserVo> selectPermissionComUser(Map map) {
        return getUserService().selectPermissionComUser(map);
    }

    /**
     * @Description:查找固定条件的人
     * @param:
     * @return:
     */
    public List<Gguser> queryUserByParam(String userCode, String taskCode) {
        return getUserService().queryUserByParam(userCode, taskCode);
    }

    public List<SaaTask> selectUserTask(String userCode){
        return getSaaTaskService().selectUserTask(userCode);
    }

}
