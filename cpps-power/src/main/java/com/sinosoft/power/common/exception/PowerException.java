package com.sinosoft.power.common.exception;

import ins.framework.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.Enumeration;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

import com.sinosoft.power.common.util.CommonUtil;

/**
 * @author: yancun
 * @date: 2018/12/17 16:13
 * @description:权限异常类
 */
@Slf4j
public class PowerException extends BusinessException {
    private static final long serialVersionUID = 1L;

    //错误信息
    private String errorMsg;

    private static Map<String, String> errMsgMap = new ConcurrentHashMap<>();

    public PowerException(String message) {
        super(message, false);
    }

    /**
     * errorCode  异常代码
     * flag  是否显示异常信息
     */
    public PowerException(String errorCode, Boolean flag) {
        String msg;
        if (errMsgMap == null || errMsgMap.get(errorCode) == null || "".equals(errMsgMap.get(errorCode))) {
            initMsgMap();
        }
        msg = errMsgMap.get(errorCode);
        if (flag && msg != null && !"".equals(msg)) {
            PowerException powerException = new PowerException(msg);
            powerException.setErrorMsg(msg);
            throw powerException;
        } else {
            throw new PowerException(errorCode);
        }
    }

    /**
     * errorCode  异常代码
     * addtionalMessage  拼入异常信息后的信息
     * flag  是否显示异常信息
     */
    public PowerException(String errorCode, String addtionalMessage, Boolean flag) {
        String msg;
        if (errMsgMap == null || errMsgMap.get(errorCode) == null || "".equals(errMsgMap.get(errorCode))) {
            initMsgMap();
        }
        msg = errMsgMap.get(errorCode);
        if (addtionalMessage != null && !"".equals(addtionalMessage)) {
            msg = msg + " " + addtionalMessage;
        }
        if (flag && msg != null && !"".equals(msg)) {
            throw new PowerException(msg, true);
        } else {
            throw new PowerException(errorCode, true);
        }
    }

    public static synchronized void initMsgMap() {
        if (!errMsgMap.isEmpty()) {
            return;
        }
        Properties p = new Properties();
        InputStream is = null;
        Reader reader = null;
        try {
            is = PowerException.class.getClassLoader().getResourceAsStream("Exception.properties");
            reader = new InputStreamReader(is, "UTF-8");
            p.load(reader);
            log.info("异常信息配置文件加载开始...");
            Enumeration<Object> enumer = p.keys();
            while (enumer.hasMoreElements()) {
                String key = (String) enumer.nextElement();
                String value = p.getProperty(key);
                errMsgMap.put(key, value);
                log.info("异常信息配置 {}:{}", key, value);
            }
            log.info("异常信息配置文件加载结束...");
        } catch (IOException e) {
            log.error("PowerException initMsgMap error :{}", CommonUtil.getStackTrace(e));
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (IOException e) {
                log.error("PowerException initMsgMap error :{}", CommonUtil.getStackTrace(e));
            }
        }
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }


}
