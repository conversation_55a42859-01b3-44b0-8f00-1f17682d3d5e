package com.sinosoft.power.common.util;

/**
 * <AUTHOR>
 * @date 2018/11/9 10:29
 */
public class BarConstant {

    public static final String STATUS="0";
    public static final String SHOWSTATUS="1";
    /**
     * @description 每个按钮和其状态值
     * <AUTHOR>
     * @date 2018/11/9 10:34
     * @return
    */
    public enum BarType{
        rsb1("rsb1","1"), //案件详细信息- rsb1
        rsb2("rsb2","1"),//保单信息- rsb2
        rsb3("rsb3","1"),//单证上传- rsb3
        rsb4("rsb4","1"),//单证查看- rsb4
        rsb5("rsb5","1"),//单证打印- rsb5
        rsb6("rsb6","1"),//发起调查- rsb6
        rsb7("rsb7","1"),//发起跟踪- rsb7
        rsb8("rsb8","1"),//发起垫付- rsb8
        rsb9("rsb9","1"),//发起医疗审核- rsb9
        rsb10("rsb10","1"),//申请报案注销- rsb10-代码里逻辑添加
        rsb11("rsb11","1"),//申请特殊赔案- rsb11-代码里逻辑添加
        rsb12("rsb12","1"),//申请估损调整- rsb12
        rsb13("rsb13","1"),//申请改派- rsb13
        rsb14("rsb14","1"),//大案管理- rsb14
        rsb15("rsb15","1"),//案件联系人- rsb15
        rsb16("rsb16","1"),//案件补充说明- rsb16
        rsb17("rsb17","1"),//风险信息提示- rsb17
        rsb18("rsb18","1"),//申请结案- rsb18
        rsb19("Rsb19","1");//反洗钱- rsb19
        private String taskCode;
        private String status;
        BarType(String taskCode, String status){
            this.taskCode=taskCode;
            this.status=status;
        }
        public String getTaskCode(){
            return taskCode;
        }
        public String getStatus(){
            return status;
        }
    }



}
