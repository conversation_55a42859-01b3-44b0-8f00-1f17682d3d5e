package com.sinosoft.power.common.util;
/**
 * 基础数据类型
 * <AUTHOR> 20170724
 *
 */
public class CommonDataType {
	/** 证件类型 */
	public static final String CERTIFY = "certify";
	/** 费用 **/
	public static final String CHARGE = "Charge";
	/** 机构 **/
	public static final String COM = "com";
	/** 币别 **/
	public static final String CURRENCY = "Currency";
	/** 巨灾代码 **/
	public static final String DISASTER_CODE = "DisasterCode";
	/** 险类 **/
	public static final String INSURANCE = "insurance";
	/** 险种 **/
	public static final String RISK = "risk";
	/** 计数单位 **/
	public static final String UNIT = "unit";
	/** 出险原因 modify by dukedi 2018-11-03 拼接caseName时转换出险原因代码**/
	public static final String DAMAGE = "DamageReason";
	/** 疾病原因 add by dukedi 2018-11-03 拼接caseName时转换疾病原因代码**/
	public static final String ILLNESSREASON = "IllnessReason";
	/** 意外原因 add by dukedi 2018-11-03 拼接caseName时转换意外原因代码**/
	public static final String ACCIDENTREASON = "InjuryExternalReason";
	/** 险别 **/
	public static final String RISKKIND = "riskKind";
	/** 一级巨灾 **/
	public static final String FIRST_DISASTER = "firstDisaster";
	/** 二级巨灾 **/
	public static final String SECOND_DISASTER = "secondDisaster";
	/** 银行大类 **/
	public static final String BANK = "bank";
	/** 开户行 **/
	public static final String ACC_BANK = "accbank";
	/** 地址 **/
	public static final String ADDRESS = "address";
	/** 省 **/
	public static final String PROVINCE = "province";
	/** 市 **/
	public static final String CITY = "city";
	/** 省 **/
	public static final String SPROVINCE = "Province";
	/** 市 **/
	public static final String SCITY = "City";
	/** 县 **/
	public static final String COUNTYCODE = "CountyCode";
	/**被保人**/
	public static final String INSUREDNAME = "insured";
	/**医院**/
	public static final String HOSPITAL = "hospital";
	/**鉴定机构**/
	public static final String IDENTIFY_COM = "identifyCom";
	/**行业**/
	public static final String WORK = "work";
	/**高级审核等级**/
	public static final String APPR_LV = "apprLv";
	/**费用收付系统代码**/
	public static final String CHARGE_CODE = "chargeCode";
	/**费用收付系统代码**/
	public static final String MAIM = "maim";
	/**支付对象**/
	public static final String PAY_OBJECT = "payObject";
	/**查询二三级机构**/
	public static final String COM_ST = "companyST";
	/**报案注销原因**/
	public static final String REGIST_CANCEL="RegistCancelNew";
	/**赔款方式*/
	public static final String COMP_TYPE = "COMPTYPE";
	/**账户性质*/
	public static final String ACC_NATURE = "ACCNATURE";
	/**支付方式*/
	public static final String PAY_TYPE = "PAYTYPE";
	/**给付类型*/
	public static final String PAYMENT_TYPE = "PaymentType";
	/**查询外省分支机构**/
	public static final String COM_TP = "companyTP";
	/**查询外省分支机构**/
	public static final String COM_TPOUR = "companyTPOUR";
	/**查询除总公司外的所有机构**/
	public static final String COM_OUR = "companyOUR";
	/**查询本机构向下的所有机构*/
    public static final String COM_CURRENT = "comCURRENT";
	/**人员**/
	public static final String USER = "user";
	/**节点**/
	public static final String BPM_NODE = "bpmNode";
	/**受伤部位**/
	public static final String INJ_DIAG = "InjuredDiagnosis";
	/**受伤部位**/
	public static final String COLLEGIATE = "collegiate";
	/**权限**/
	public static final String SAATASK = "SaaTask";
	/**职业**/
	public static final String CAREER = "career";
	/**支付例外原因**/
	public static final String PAY_REASON = "UnusualPayReason";
	/**查询相关权限下所有机构**/
	public static final String COM_QX = "companyQX";
	/**工作流预配节点**/
	public static final String PREPAY = "PrePay";
	/**工作核赔配节点**/
	public static final String UNDWRT = "Undwrt";
	/**工作核报案登记/报案节点**/
	public static final String REGIST = "Regist";
	/**工作核理算节点**/
	public static final String ADJUSTMENT = "Adjustment";
	/**查询相关权限下所有机构**/
	public static final String COM_REGIST = "companyRegist";
	/**金额权限类型*/
	public static final String TASKCODE = "TaskCode";
	/**任务大类*/
	public static final String TASKCATALOG = "TaskCatalog";
	/**任务池*/
	public static final String TASKPOOL = "TaskPool";

	public static final String CLASSCODE = "ClassCode";
}
