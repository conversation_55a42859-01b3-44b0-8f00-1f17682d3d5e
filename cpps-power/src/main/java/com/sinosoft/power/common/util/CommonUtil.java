package com.sinosoft.power.common.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
 

/**
 * 系统的公共方法
 * 
 * <AUTHOR>
 * 
 */
public class CommonUtil {
	private static Logger logger = LoggerFactory.getLogger(CommonUtil.class);

	private static SecureRandom secureRandom;

	static {
		try {
			secureRandom = SecureRandom.getInstance("SHA1PRNG");
		} catch (NoSuchAlgorithmException e) {
			logger.error("random generate error! {}", CommonUtil.getStackTrace(e));
		}
	}

	/**
	 * 功能描述：清除list中的空元素
	 *
	 * @param list
	 * <AUTHOR> 20170606
	 */
	public static void clearNullList(List list) {
		if (list == null) {
			return;
		}
		int size = list.size() - 1;
		for (; size >= 0; size--) {
			if (list.get(size) == null) {
				list.remove(size);
			}
		}
	}

	/**
	 * 功能描述：输出异常中的中文信息
	 *
	 * <AUTHOR>
	 */
	public static String getCHNFromException(String exceptionMsg) {
		String returnMsg = "";
		if (!isNullString(exceptionMsg)) {
			String regEx = "[\\u4e00-\\u9fa5]";
			Pattern p = Pattern.compile(regEx);
			Matcher m = p.matcher(exceptionMsg);
			StringBuffer sb = new StringBuffer();
			while (m.find()) {
				returnMsg = sb.append(m.group()).toString();
			}
		}
		return returnMsg;
	}

	/**
	 * 功能描述：清除list中没有的对象（null与没有内容的对象）（慎用）
	 *
	 * @param list
	 * <AUTHOR>
	 */
	public static void cleanList(List list) {
		if (list == null) {
			return;
		}
		int size = list.size() - 1;
		for (; size >= 0; size--) {
			Object obj = list.get(size);
			if (obj == null) {
				list.remove(size);
				continue;
			}
			boolean flag = true;
			for (Field f : obj.getClass().getDeclaredFields()) {
				f.setAccessible(true);
				try {
					if (!("serialVersionUID").equals(f.getName())) {
						if (f.get(obj) != null) {
							flag = false;
							break;
						}
					}
				} catch (Exception e) {
					continue;
				}
			}
			if (flag) {
				list.remove(size);
			}
		}
	}

	/**
	 * 生成uuid
	 *
	 * @return
	 */
	public static String getUuid() {
		String uuid = UUID.randomUUID().toString(); //获取UUID并转化为String对象
		uuid = uuid.replace("-", "");               //因为UUID本身为32位只是生成时多了“-”，所以将它们去掉就可
		return uuid;
	}


	public static String getUpperUuid() {
		return UUID.randomUUID().toString().toUpperCase().replace("-", "");
	}

	/**
	 * 功能描述：将double类型转成string类型
	 *
	 * @param param
	 * <AUTHOR> 20170722
	 */
	public static String doubleToString(Double param) {
		String result = new DecimalFormat("#").format(param);
		return result;
	}

	/**
	 * 功能描述：判断集合是否没空
	 * 如果list.size() 大于0，返回false；
	 * 反之，返回true
	 *
	 * @param list
	 * @return
	 * <AUTHOR> 20170606
	 */
	public static boolean isEmptyList(List list) {
		boolean isNull = false;
		if (list == null || list.size() == 0) {
			isNull = true;
		}
		return isNull;
	}

	/**
	 * 功能描述：集合 转字符串
	 *
	 * @param stringList
	 * @return String
	 * <AUTHOR> 20190506
	 */
	public static String listToString(List<String> stringList) {
		if (stringList == null) {
			return null;
		}
		StringBuilder result = new StringBuilder();
		boolean flag = false;
		for (String string : stringList) {
			if (flag) {
				result.append(",");
			} else {
				flag = true;
			}
			result.append(string);
		}
		return result.toString();
	}

	/**
	 * 把字符串str的第pos个字符的位置设成字符c 
	 * CommonUtil.setCharAtPos(null, 0,'1');     return "1"
	 * CommonUtil.setCharAtPos("", 2, '1');      return "  1"
	 * CommonUtil.setCharAtPos("2222", 0, '1');  return "1222"
	 * CommonUtil.setCharAtPos("2222", 2, '1');  return "2212"
	 * CommonUtil.setCharAtPos("2222", 3, '1');  return "2221"
	 * CommonUtil.setCharAtPos("2222", 4, '1');  return "22221"
	 * CommonUtil.setCharAtPos("2222", 5, '1');  return "2222 1"
	 * 
	 * @param str
	 * @param pos
	 * @param c
	 * @return
	 */
	public static String setCharAtPos(String str, int pos, char c) {
		if (str == null) {
			str = "";
		}
		if (pos > 100) {
			throw new IllegalArgumentException("非法参数,pos不能大于100");
		}
		if (pos < 0) {
			return str;
		} else if (pos < str.length()) {
			return str.substring(0, pos) + c + str.substring(pos + 1);
		} else if (pos == str.length()) {
			return str.substring(0, pos) + c;

		} else {
			// 先把str补齐空格
			StringBuilder buf = new StringBuilder(str);
			for (int i = str.length(); i < pos; i++) {
				buf.append(' ');
			}
			buf.append(c);
			return buf.toString();
		}
	}
	
	/**
	 * 获取输入字符串指定位置的字符
	 * @param str 输入字符串
	 * @param pos 指定的位置
	 * @return 如果没有则返回空字符
	 */
	public static char getCharAtPos(String str, int pos) {
		return str!=null&&str.length()>pos?str.charAt(pos):' ';
	}
	
	/**
	 * 比较输入的字符串1和字符串2是否相等
	 * 
	 * @param str1
	 *            ：字符串1
	 * @param str2
	 *            ：字符串2
	 * @return 相等：true，不相等：false
	 */
	public static boolean equals(String str1, String str2) {
		if (str1 == str2) {
			return true;
		}
		// null 和 空串在系统中认为相等
		str1 = (str1 == null?"":str1.trim());
		str2 = (str2 == null?"":str2.trim());
		return str1.equals(str2);
	}
	/**
	 * 对字符串去除空格，如果字符串为null，则返回""
	 * @param str ：输入字符串
	 * @return ：trim后结果
	 */
	public static String trim(String str) {
		if (null == str) {
			return "";
		}
		return str.trim();
	}
	
	/**
	 * 判断是否基本对象（即无需处理的对象）
	 * @param obj
	 * @return
	 */
	public static boolean isBaseObject(Object obj){
		if(obj instanceof String
				||obj instanceof Number
				||obj instanceof Character
				||obj instanceof Boolean
				||obj instanceof Byte
				||obj instanceof Date
				||obj.getClass().isPrimitive()
				){
			return true;
		}
		return false;
	}
	
	/**
	 * 方法描述：是否是空字符串
	 * 
	 * */
	public static boolean isNullString(String str){
		if(str == null){ return true;}
		if("".equals(str.trim())) {return true;}
		return false;
	}
	
	/**
	 * 方法描述：字符串转long
	 * 
	 * */
	public static Long parsrStringToLong(String str){
		if(isNullString(str)) {return null;}
		return Long.parseLong(str);
	}
	
	/**
     * 将异常堆栈转换为字符串
     * @param aThrowable 异常
     * @return String
     */
	public static String getStackTrace(Throwable aThrowable) {
		final Writer result = new StringWriter();
		final PrintWriter printWriter = new PrintWriter(result);
		aThrowable.printStackTrace(printWriter);
		return result.toString();
	}
	
	/**
	 * Double转string
	 * */
	public static String convertDoubleToString(Double d){
		if(isNullString(d.toString())){
			return "";
		}
		String str = doubleToString(d);
		if(str.indexOf('.')>-1){
			return str.substring(0, str.indexOf('.'));
		}
		return str;
	}

	static Map<String,String> mappingCodeMap= new HashMap<String,String>();

	/**
	 * 获取映射数据
	 * @param dataCode
	 * @return
	 */
	public static  String getMappingCode(String dataCode){
		return mappingCodeMap.get(dataCode);
	}

	public static int nextInt(int bound){
        int randomNumber = secureRandom.nextInt(bound);
        return randomNumber;
    }

    public static double nextDouble(){
        double randomNumber = secureRandom.nextDouble();
        return randomNumber;
    }

	/**
	 * 将字符串转换成List<String>
	 * 字符串为：'A','B','C'或者A,B,C
	 * 转换成List<String>
	 * @param str
	 * @return
	 */
	public static List<String> convertList(String str){
		List<String> list = null;
		if(StringUtils.isNotBlank(str)){
			if(str.startsWith("'")){
				list = Arrays.asList(str.replaceAll("'", "").split(","));
			}else{
				list = Arrays.asList(str.split(","));
			}
		}
		return list;
	}

	/**
	 * 处理bigdecimal类型
	 * @param value
	 * @return
	 */
	public static BigDecimal convertBigDecimal(BigDecimal value){
		if(value==null){
			value=new BigDecimal(0);
		}
		return value;
	}

	/**
	 * 小于0转换为0
	 * @param value
	 * @return
	 */
	public static BigDecimal convertNegaNumber(BigDecimal value){
		if(value.compareTo(BigDecimal.ZERO)<0){
			value=new BigDecimal(0);
		}
		return value;
	}

	/**
	 * 将BigDecimal转换成Long类型
	 * @param number
	 * @return
	 * Create by MaWenlong
	 * date : 2014-5-27
	 */
	public static Long bigDecimal2Long(BigDecimal number){
		Long num = 0L;
		if(number != null){
			num = Long.valueOf(number.longValue());
		}
		return num;
	}

	/**
	 * 将string类型转换为bigdecimal类型
	 * @param value
	 * @return
	 */
	public static BigDecimal convertStringToBigDecimal(String value){
		BigDecimal target=new BigDecimal(0);
		if(value!=null&&!"".equals(value)){
			target=new BigDecimal(value);
		}
		return target;
	}

    /**
     * 将BigDecimal转换成Integer类型
     * @param number
     * @return
     * Create by MaWenlong
     * date : 2014-5-27
     */
    public static Integer bigDecimal2Integer(BigDecimal number){
        Integer num = 0;
        if(number != null){
            num = Integer.valueOf(number.intValue());
        }
        return num;
    }

	/**
	 * 将bigdecimal转换成double
	 *
	 * @param b
	 * @return
	 */
	public static double convertBigDecimalToDouble(BigDecimal b) {
		double d = 0d;
		if (b != null) {
			d = b.doubleValue();
		}
		return d;
	}

	/**
	 * 判断两个bigdecimal类型的数字是否相等
	 *
	 * @param b1
	 * @param b2
	 * @return
	 * <AUTHOR>
	 * @date : 2014-6-29
	 */
	public static boolean compareEq(BigDecimal b1, BigDecimal b2) {
		boolean flag = false;
		if(b1 == null && b2 == null){
			flag = true;
		} else {
			if (b1 != null && b1.compareTo(b2) == 0) {
				flag = true;
			}
		}
		return flag;
	}

	/**
	 * 将Double转换为BigDecimal
	 *
	 * @param d
	 * @return Create by MaWenlong date : 2014-6-5
	 */
	public static BigDecimal convertDouble2BigDecimal(Double d) {
		BigDecimal b = BigDecimal.ZERO;
		if (d != null) {
			b = BigDecimal.valueOf(d);
		}
		return b;
	}

	/**
	 * 将BigDecimal类型进行四舍五入
	 *
	 * @param bigDecimal
	 * @return
	 */
	public static BigDecimal scaleBigDecimal(BigDecimal bigDecimal) {
		if (bigDecimal == null) {
			bigDecimal = BigDecimal.ZERO;
		}
		bigDecimal = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
		return bigDecimal;
	}

	/**
	 * 两个bigdecimal数据相加，返回恒不为空
	 *
	 * @param b1
	 * @param b2
	 * @return
	 * <AUTHOR>
	 */
	public static BigDecimal bigDecimalAdd(BigDecimal b1, BigDecimal b2) {
		if (b1 == null) {
			b1 = BigDecimal.ZERO;
		}
		if (b2 == null) {
			b2 = BigDecimal.ZERO;
		}
		return b1.add(b2);
	}

	/**
	 * 两个bigdecimal数据相减，返回恒不为空
	 *
	 * @param b1 被减数
	 * @param b2 减数
	 * @return
	 * <AUTHOR>
	 */
	public static BigDecimal bigDecimalSubtract(BigDecimal b1, BigDecimal b2) {
		if (b1 == null) {
			b1 = BigDecimal.ZERO;
		}
		if (b2 == null) {
			b2 = BigDecimal.ZERO;
		}
		return b1.subtract(b2);
	}


	/**
	 *  两个bigdecimal数据乘，返回恒不为空
	 * @param b1
	 * @param b2
	 * @return
	 */
	public static BigDecimal bigDecimalMultiply(BigDecimal b1, BigDecimal b2) {
		if (b1 == null) {
			b1 = BigDecimal.ZERO;
		}
		if (b2 == null) {
			b2 = BigDecimal.ZERO;
		}
		return b1.multiply(b2);
	}

	/**
	 *  两个bigdecimal数据除，返回恒不为空
	 * @param b1
	 * @param b2
	 * @return
	 */
	public static BigDecimal bigDecimalDivide(BigDecimal b1, BigDecimal b2) {
		if (b1 == null) {
			b1 = BigDecimal.ZERO;
		}
		if (b2 == null) {
			b2 = BigDecimal.ZERO;
		}
		return b1.divide(b2);
	}
}
