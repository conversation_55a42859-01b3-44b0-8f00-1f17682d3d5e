package com.sinosoft.power.common.util;

import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * @author: yancun
 * @create: 2018/12/19 14:04
 * @description:读取Mybatis的配置文件
 */
public class MybatisUtil {
    public static SqlSessionFactory getSessionFactory(Properties properties){
        SqlSessionFactory factory;
        try {
            String resource = "mybatis.cfg.xml";
            InputStream is = MybatisUtil.class.getClassLoader().getResourceAsStream(resource);
            factory = new SqlSessionFactoryBuilder().build(is);
            if(is!=null){
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } finally {

        }
        return factory;
    }

}
