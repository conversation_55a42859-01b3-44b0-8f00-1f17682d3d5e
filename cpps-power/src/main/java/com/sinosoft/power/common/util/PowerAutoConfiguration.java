package com.sinosoft.power.common.util;


import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: yancun
 * @create: 2018/12/19 10:13
 * @description: spring boot 自动注入
 */

@Configuration
public class PowerAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public SpringContextUtil springContextUtil() {
        return new SpringContextUtil();
    }
}
