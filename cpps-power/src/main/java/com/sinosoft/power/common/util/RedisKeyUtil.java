package com.sinosoft.power.common.util;

/**
 * <AUTHOR> 2018-10-10
 */
public class RedisKeyUtil {

    /**redis中还未保存数据*/
    public static final String NON_EXISTENT = "0";
    /**redis中已经保存数据*/
    public static final String EXISTENT = "1";
    /**redis抛出异常*/
    public static final String ERROR = "2";
    /**Redis分隔符*/
    public static final String REDIS_SEPARTOR = ":";
    /**String分隔符*/
    public static final String STRING_SEPARTOR = "|";
    /**用户前缀*/
    public static final String USER = "user";
    public static final String USER_PREFIX = USER + REDIS_SEPARTOR;
    /**数据权限前缀*/
    public static final String USER_KEY_DATA_POWER = "dataPower";
    /**岗位Id列表*/
    public static final String USER_KEY_GRADE_ID_LIST = "gradeIdList";
    /**功能代码列表*/
    public static final String USER_KEY_TASKCODE_LIST = "taskCodeList";
    /**菜单列表代码*/
    public static final String USER_KEY_MENU_LIST = "menuList";

    public static final String COMMONDATA = "CommonData";

    public static final String SYMBOL =":";
    //权限基础数据缓存key值前缀
    public static final String REDISSYSTEM_POWER = "POWER";

    public class RedisData {

        /**全量机构树*/
        public static final String COMPANY = "redisData_company";
        /**查询二、三级机构*/
        public static final String COMPANYDATA = "redisData_companyData";
        /**查询外省分支机构*/
        public static final String OTHERPROVINCESCOMPANY = "redisData_otherProvincesCompany";
        /**查询除总公司外的所有机构*/
        public static final String OTHERPROVINCEOUR = "redisData_otherProvincesOur";
        /**查询本机构向下的所有机构*/
        public static final String OTHERCOMCURRENT = "redisData_otherComCurrent";

        public static final String DAMAGE = "redisData_damage";

        public static final String COMMONDATA = "redisData_commonData_";
        /**查询二级机构下的所有三级机构*/
        public static final String THIRDCOMPANY = "redisData_thirdCompany";
        /**查询二级机构下的所有三级机构*/
        public static final String THIRDCOMPANYS = "redisData_thirdCompanys";
        /**省下的所有城市列表*/
        public static final String CITYLIST = "redisData_cityList";
        /**省下的所有城市列表*/
        public static final String AREALIST = "redisData_areaList";
        /**根据dataName查询医院等级*/
        public static final String HOSPITALGRADE = "redisData_hospitalGrade";
        /**根据dataName查询医院等级*/
        public static final String HOSPITAL = "redisData_hospital";
        /**分级查询险类险种*/
        public static final String CLASSCODEANDRISKCODES = "redisData_classCodeAndRiskCodes";
        /**分级展示公司信息*/
        public static final String CENTERCOMANDTHIRDCOM = "redisData_centerComAndThirdCom";
        /**以分级方式查询所有的省市区信息*/
        public static final String AREA = "redisData_area";
        /**根据银行代码、市代码查询开户行列表*/
        public static final String ACCBANKS = "redisData_accBanks";
        /**根据联行号获取开户行信息*/
        public static final String ACCBANK = "redisData_accBank";
        /**根据银行名称  省 市查询开户行的信息*/
        public static final String ACCBANKNAME = "redisData_AccBankName";
        /**查询职业列表*/
        public static final String CAREER = "redisData_career";
        /**根据市代码查询鉴定机构列表*/
        public static final String IDENTIFYCOM = "redisData_identifyCom";
        /**根据市代码查询鉴定机构列表*/
        public static final String MAINITEM = "redisData_maimItem";
        /**翻译险别代码*/
        public static final String TRANSFERKIND = "redisData_transferKind";
        /**翻译险别代码*/
        public static final String TRANSFERDATA = "redisData_transferData";
        /**翻译人员代码*/
        public static final String TRANSFERUSERCODE = "redisData_transferUserCode";
        /**常规数据字典翻译*/
        public static final String TRANSFERCOMMONCODE = "redisData_transferCommonCode";
        /**出险原因翻译*/
        public static final String TRANSFERDAMAGECODE = "redisData_transferDamageCode";
        /**险别条款翻译*/
        public static final String TRANSFERKINDCODE = "redisData_transferKindCode";
        /**查询所有的省市区信息*/
        public static final String PROVINCESANDCITYSANDCOUNTYS = "redisData_provincesAndCitysAndCountys";

    }

    public class SaaRedisData {

        public static final String KEY = "SaaRedisData_";

    }

}
