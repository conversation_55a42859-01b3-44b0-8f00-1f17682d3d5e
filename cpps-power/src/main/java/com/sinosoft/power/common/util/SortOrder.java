package com.sinosoft.power.common.util;

import com.sinosoft.power.common.exception.PowerException;

/**
 * <AUTHOR>
 * @create 2018-10-24 16:20
 */
public enum SortOrder {
    ASC {
        @Override
        public String toString() {
            return "asc";
        }
    },
    DESC {
        @Override
        public String toString() {
            return "desc";
        }
    };

    private SortOrder() {
    }

    public static SortOrder fromString(String str){
        if (ASC.toString().equalsIgnoreCase(str)){
            return ASC;
        }else if(DESC.toString().equalsIgnoreCase(str)){
            return DESC;
        }else{
            throw new PowerException("260008", true);
        }
    }
}
