package com.sinosoft.power.common.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * @author: yancun
 * @create: 2018/12/19 09:57
 * @description:spring 容器上下文
 */
public class SpringContextUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;
    @Override
    public void setApplicationContext(ApplicationContext applicationContext)
            throws BeansException {
        SpringContextUtil.applicationContext = applicationContext;
    }
    public static Object getBean(String beanName){
        return SpringContextUtil.applicationContext.getBean(beanName);
    }
    public static boolean isContextExists(){
        if(null != applicationContext){
            return true;
        }else{
            return false;
        }
    }

    public static String getActiveProfile() {
        if(isContextExists()){
            return SpringContextUtil.applicationContext.getEnvironment().getActiveProfiles()[0];
        }
        return StringUtils.EMPTY;
    }

    public static void main(String[] args){}


}
