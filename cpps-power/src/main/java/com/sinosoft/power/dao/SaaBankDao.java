package com.sinosoft.power.dao;

import com.sinosoft.power.po.PrpDBank;
import ins.framework.mybatis.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @author: yancun
 * @create: 2018/12/22 15:06
 * @description:
 */
public interface SaaBankDao extends MybatisBaseDao<PrpDBank, String> {


    PrpDBank queryByBankCode(Map<String, Object> params);

    PrpDBank queryByBankName(Map<String, Object> params);

    List<PrpDBank> queryBanks();
    //根据银行
    PrpDBank searchByBankCode(@Param("bankCode") String bankCode);

}
