package com.sinosoft.power.dao;

import com.sinosoft.power.po.CommonData;
import com.sinosoft.power.vo.CommonDataVo;
import ins.framework.mybatis.MybatisBaseDao;

import java.util.List;

/**
 * @author: yancun
 * @create: 2018/12/22 15:31
 * @description:
 */
public interface SaaCommonDataDao extends MybatisBaseDao<CommonData, Double> {


    List<CommonData> queryDataByDataType(CommonDataVo vo);

    List<CommonData> queryTaskPool();

    CommonData transferData(CommonDataVo vo);


}
