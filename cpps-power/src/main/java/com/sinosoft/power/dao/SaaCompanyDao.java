package com.sinosoft.power.dao;

import com.sinosoft.power.po.PrpDCompany;
import com.sinosoft.power.po.SaaUserPermitData;
import ins.framework.mybatis.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 表PRP_D_COMPANY对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 */
public interface SaaCompanyDao extends MybatisBaseDao<PrpDCompany, String> {

	PrpDCompany queryByComCode(Map<String, Object> param);

	PrpDCompany querycompanyBycomCode(String comCode);


	List<PrpDCompany> queryLikeComCodes(Map<String, String> param);

	List<PrpDCompany> queryAll();

	List<PrpDCompany> queryAllTreeData();

//	List<String> queryByParam(String param);

	List<PrpDCompany> querySecondThirdCompany();

	List<PrpDCompany> queryCenterComByFlag();

	List<PrpDCompany> queryThirdCompany(String comCode);

	/**
	 * 查询外省分支机构
	 */
	List<PrpDCompany> queryOtherProvincesCompany(String comNo);

	/**
	 * 查询外省分支机构
	 */
	List<PrpDCompany> queryOtherProvincesCompanyOur();

	/*根据UpperPath查询当前机构的分公司*/
	List<PrpDCompany> queryCompanyByUpperPath(@Param("comCodeSet") List<String> comCodeSet);

	PrpDCompany queryUpperThreeCompany(String comCode);

	/**
	 * 查询除总公司外的所有机构
	 */
	List<PrpDCompany> queryOtherProvincesOur(String comNo);

	/**
	 * 查询本机构向下的所有机构
	 */
	List<PrpDCompany> queryOtherComCurrent(String comNo);

	List<PrpDCompany> queryByUpperComCode(String upperComCode);

	List<String> queryUpperComCodeList(@Param("companyCode") String comCode);

	Set<String> queryCompanyUpperPathSetFromComCodeSet(@Param("comCodeSet") Set<String> comCodeSet);

	PrpDCompany queryCompanyUpperPath(@Param("companyCode") String comCode);

	int deleteTrnData(@Param("companyCode") String comCode);

	int insertTrnData(@Param("companyCode") String comCode);

	int updateUpperPathByTrnData(@Param("companyCode") String comCode);

	/**
	 * 查询相应权限下所有机构
	 */
	List<PrpDCompany> queryAuthorityCom(@Param("saaUserPermitDataList") List<SaaUserPermitData> saaUserPermitDataList);

	/**
	 * 查询相应权限下某二级机构下的所有机构
	 */
	List<PrpDCompany> queryAuthorityComForRedisCom(@Param("saaUserPermitDataList") List<SaaUserPermitData> saaUserPermitDataList, @Param("companyCode") String comCode);

	/**
	 * 查询相应权限下所有机构
	 */
	List<String> queryAuthorityComString(@Param("stringlist") List<String> stringlist);

	List<String> queryAuthorityComStringOne(@Param("makeCome") String makeCome);

	List<String> queryAuthorityComStringupperPath(@Param("makeCome") String makeCome);

	List<PrpDCompany> queryRegistHandleCom(@Param("comNo") String comNo);

	List<String> getAllBranchCompany();

	List<PrpDCompany> queryHavePowerComCode(@Param(value = "upperPath") String upperPath, @Param(value = "classCode") String classCode, @Param(value = "taskCode") String taskCode);

	List<PrpDCompany> queryComCode(@Param(value = "upperPath") String upperPath, @Param(value = "taskCode") String taskCode);

	/**
	 * 新增机构取其上级机构的所有父机构的集合
	 */
	List<PrpDCompany> findPrpdCompanysByCode(@Param("companyCode") String comCode);

	String findContactComNameByComCode(@Param("companyCode") String comCode);

	/**
	 * @Description:查询所有的comCode
	 * @return:List<String>
	 */
	List<String> queryComCodeAll();

	/**
	 * @Description:根据comCode查询所有子comcode
	 * @param:comcode
	 * @return:list
	 */
	List<String> queryPriorComCodeList(@Param("companyCode") String comCode);

	/**
	 * 根据用户代码查询对应comCode的机构信息
	 */
	PrpDCompany queryCompanyByUserCode(@Param("userCode") String userCode);

	List<PrpDCompany> queryPriorCompanyByComLevel(@Param("companyCode") String comCode, @Param("comLevel") String comLevel);

    List<PrpDCompany> queryAllData();


    String queryComFlag(String comCode);
}