package com.sinosoft.power.dao;

import ins.framework.mybatis.MybatisBaseDao;
import com.sinosoft.power.po.SaaGrade;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * 表SAA_GRADE对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
public interface SaaGradeDao extends MybatisBaseDao<SaaGrade, Long> {


	public List<SaaGrade> findAllGrade();

    List<SaaGrade> findGrade(SaaGrade saaGrade);


    List<SaaGrade> queryGradeListByUserCode(@Param("userCode") String userCode);

    SaaGrade queryGradeByGradeCName(@Param("gradeCName") String gradeCName);

    void invalidGrade(@Param("gradeId") Long gradeId);
}