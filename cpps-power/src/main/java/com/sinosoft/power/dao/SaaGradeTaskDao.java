package com.sinosoft.power.dao;

import ins.framework.mybatis.MybatisBaseDao;
import com.sinosoft.power.po.SaaGradeTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 *
 * 表SAA_GRADE_TASK对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
public interface SaaGradeTaskDao extends MybatisBaseDao<SaaGradeTask, Long> {

    List<String> querySaaTaskCodeListByGradeId(@Param("gradeId") Long gradeId);

    SaaGradeTask queryAllGradeTaskByGradeIdAndTaskId(@Param("gradeId") Long gradeId, @Param("taskId") Long taskId);

    void invalidGradeTaskByGradeId(@Param("gradeId") Long gradeId);

    void invalidGradeTask(@Param("gradeId") Long gradeId, @Param("taskIdSet") Set<Long> taskIdSet);

    void validGradeTask(@Param("gradeId") Long gradeId, @Param("taskIdList") Set<Long> taskIdList);

    void validSingleGradeTask(@Param("gradeId") Long gradeId, @Param("taskId") Long taskId);

    Set<SaaGradeTask> querySaaGradeTask(@Param("gradeId") Long gradeId);

}