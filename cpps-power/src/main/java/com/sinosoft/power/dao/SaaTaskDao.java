package com.sinosoft.power.dao;

import ins.framework.mybatis.MybatisBaseDao;
import com.sinosoft.power.po.SaaTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 *
 * 表SAA_TASK对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
public interface SaaTaskDao extends MybatisBaseDao<SaaTask, Double> {
    
    public List<String> selectUserTaskCode(String userCode);

    public List<SaaTask> selectUserTask(String userCode);

    /**
     * 根据 taskCode 查询已经配置的用户
     * @param taskCode
     * @return
     */
    public List<String> selectUserByTaskCode(String taskCode);

    List<SaaTask> selectAll();

    List<SaaTask> selectByGradeId(String id);

    List<SaaTask> querySaaTaskListByTaskCodeSet(@Param("taskCodeSet") Set<String> taskCodeSet);

    List<SaaTask> querySaaTaskByTaskName(@Param("taskCNameSet") Set<String> taskCNameSet);

    Set<String> queryTaskIdSetByTaskCodeSet(@Param("taskCodeSet") Set<String> taskCodeSet);

    Set<String> queryTaskCodeSetByTaskIdSet(@Param("taskIdSet") Set<Long> taskIdSet);

    List<SaaTask> queryAllSaaTaskCodeListByGradeId(@Param("gradeId") Long gradeId);

    List<SaaTask> getSaaTaskBySystem(@Param("system") String systemCode, @Param("id") String id);

    List<SaaTask> getTaskListBySystem(@Param("system") String systemCode);
}