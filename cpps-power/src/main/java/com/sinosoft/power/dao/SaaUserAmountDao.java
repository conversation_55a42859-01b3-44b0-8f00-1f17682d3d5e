package com.sinosoft.power.dao;

import com.sinosoft.power.po.SaaUserAmount;
import org.apache.ibatis.annotations.Mapper;

import ins.framework.mybatis.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * 表_SAA_USER_AMOUNT对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface SaaUserAmountDao extends MybatisBaseDao<SaaUserAmount, Long> {

    List<SaaUserAmount> queryUserAmountByUserCode(String userCode);

    SaaUserAmount queryUserAmout(@Param("userCode") String userCode, @Param("taskCode") String taskCode);

    SaaUserAmount queryUserAmoutByCodeAndRisk(@Param("userCode") String userCode, @Param("taskCode") String taskCode,
                                              @Param("classCode") String classCode, @Param("taskParentCode") String taskParentCode);

    List<SaaUserAmount> queryUserAmountByUndwrtPower(Map<String, String> map);

    /**
     *判断某个人的金额权限
     */
    List<SaaUserAmount> selectUserAmountPermit(Map<String, String> map);



}