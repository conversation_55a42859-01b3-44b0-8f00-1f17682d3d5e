package com.sinosoft.power.dao;

import com.sinosoft.power.po.Gguser;
import ins.framework.mybatis.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 表PRP_D_USER对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 */
public interface SaaUserDao extends MybatisBaseDao<Gguser, String> {
    List<Gguser> selectAll();

    List<Gguser> selectUserByComCode(String comCode);

    List<Gguser> selectSecondUserByComCode(String comCode);

    List<Gguser> querySecondComUser(String comCode, String taskCode);

    //获取一些机构下有某一对应任务权限的用户
    List<Gguser> selectPermissionComUser(Map<String, String> map);

    //获取一些机构下所有用户
    List<Gguser> selectAllUserByComCode(String comCode);

    //vo做参数查循 返回list集合
    List<Gguser> queryPagesUserList(Gguser user);

    //map做参数查询 原来的写法
    List<Gguser> queryPageUserList(Map map);

    List<Gguser> queryUserByParam(List<String> list);

    Gguser queryUserByUserCode(@Param("userCode") String userCode);

    Gguser queryUserByConditions(@Param("userCode") String userCode, @Param("companyCode") String comCode);

    List<Gguser> queryComCondtionPowerUserList(@Param("companyCode") String makeCom, @Param("taskCode") String taskCode);

    void updateGradeByUserCode(@Param("list") List<String> list, @Param("defaultGradeId") Long defaultGradeId);

    void invalidGradeComByUserCode(@Param("list") List<String> list);

    void updateComByUserCode(@Param("list") List<String> list, @Param("defaultCom") String defaultCom);

    void invalidDefaultComByUserCodeAndGradeId(@Param("list") List<String> list, @Param("gradeId") Long gradeId, @Param("upperPath") String upperPath);


}