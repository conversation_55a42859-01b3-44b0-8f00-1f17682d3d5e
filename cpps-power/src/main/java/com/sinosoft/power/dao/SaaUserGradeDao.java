package com.sinosoft.power.dao;

import com.sinosoft.power.vo.ExportUserPowerInfoVo;
import ins.framework.mybatis.MybatisBaseDao;
import com.sinosoft.power.po.SaaUserGrade;
import com.sinosoft.power.vo.UserGradeMapperTaskVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 表SAA_USER_GRADE对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 */
public interface SaaUserGradeDao extends MybatisBaseDao<SaaUserGrade, Long> {

    List<SaaUserGrade> findUserGrade(@Param("userCode") String userCode);

    List<SaaUserGrade> findUserAllValidGrade(@Param("userCode") String userCode);

    List<SaaUserGrade> findUserAllGrade(@Param("userCode") String userCode);

    List<SaaUserGrade> findUserGradeByUserCodeAndTaskCode(Map<String, String> param);

    List<SaaUserGrade> findUserGradeByGradeIds(List<String> gradeIds);

    List<SaaUserGrade> findAllUserGrade();

    List<SaaUserGrade> queryGradeByUserCodeAndGradeIdSet(@Param("userCode") String userCode, @Param("gradeIdSet") Set<Long> gradeIdSet);

    List<UserGradeMapperTaskVo> findUserGradeIdTaskMapper();

    List<SaaUserGrade> queryGradeByUserCodeAndGradeId(@Param("userCode") String userCode, @Param("gradeId") Long gradeId);

    List<String> findUserGradeIdTaskMapperByUserCode(String userCode);

//    public void deleteUserGradeByUserCode(String userCode);

//    void deleteUserGradeByUserCodeAndGradeId(@Param("userCode") String userCode, @Param("gradeIds") List<Integer> gradeIds);

    void invalidGradeByUserCode(@Param("userCode") String userCode);

    // Modify By ZhouTaoyu      Reason:批量更新参数映射问题,需要用Map接收  time:2019/09/23
//    void invalidUserGrade(@Param("userCode") String userCode, @Param("gradeIdSet") Set<Long> gradeIdSet);
    void invalidUserGrade(Map<String,Object> map);

    void invalidUserGradeForOne(@Param("gradeId") Long gradeId);

    void validGrade(@Param("userCode") String userCode, @Param("gradeIdSet") Set<Long> gradeIdSet);

    // Modify By ZhouTaoyu      Reason:批量更新参数映射问题,需要用Map接收  time:2019/09/23
//    void validUserGrade(@Param("userCode") String userCode, @Param("gradeId") Long gradeId);
    void validUserGrade(Map<String,Object> map);

    SaaUserGrade queryValidGradeByUserCodeAndGradeId(@Param("userCode") String userCode, @Param("gradeId") Long gradeId);
    SaaUserGrade queryValidGradeByUserCode(@Param("userCode") String userCode);
    /**查询用户维护权限的配置信息*/
    SaaUserGrade querySysGradeByUserCodeAndGradeId(@Param("userCode") String userCode, @Param("taskCode") String taskCode);

    List<SaaUserGrade> queryValidGradeListByGradeId(@Param("gradeId") Long gradeId);

    SaaUserGrade queryAllGradeByUserCodeAndGradeId(@Param("userCode") String userCode, @Param("gradeId") Long gradeId);

    List<SaaUserGrade> queryGradeListByUserCodeAndGradeId(@Param("userCode") String userCode, @Param("gradeIdSet") Set<Long> gradeIdSet);

    Set<Long> queryGradeIdByUserCode(String userCode);

    /**
     * @Description: 用户权限导出excel用
     * @param
     * @return  List<ExportUserPowerInfoVo>
     */
    List<ExportUserPowerInfoVo> exportPowerUserInfo(Map<String, Object> param);

    List<String> queryUserCodeByGradeAndCom(@Param("gradeId") Long gradeId, @Param("companyCode") String comCode);

    // Add By ZhouTaoyu      Reason:无序列,手动实现主键自增  time:2019/09/23
    Long selectMaxId();
}