package com.sinosoft.power.dao;

import ins.framework.mybatis.MybatisBaseDao;
import com.sinosoft.power.po.SaaUserGradeTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 * 表SAA_USER_GRADE_TASK对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface SaaUserGradeTaskDao extends MybatisBaseDao<SaaUserGradeTask, Long> {

    List<SaaUserGradeTask> queryTaskListByUserCodeAndGradeId(@Param("userCode") String userCode, @Param("gradeId") String gradeId);

    List<SaaUserGradeTask> queryTaskListByUserCode(@Param("userCode") String userCode);

    List<SaaUserGradeTask> queryAllTaskListByUserCodeAndGradeId(@Param("userCode") String userCode, @Param("gradeId") Long gradeId);

    List<SaaUserGradeTask> queryTaskListByUserCodeAndTaskCode(@Param("userCode") String userCode, @Param("taskCode") String taskCode);

    List<String> queryGradeTaskCodeListByUserCode(@Param("userCode") String userCode);

    int queryUserGradeTaskCountByGradeId(@Param("gradeId") Long gradeId);

    void invalidGradeTaskByUserCode(@Param("userCode") String userCode);

    void invalidGradeTaskByUserAndGrade(@Param("userCode") String userCode, @Param("gradeId") Long gradeId);

    void invalidGradeTaskByGrade(@Param("gradeId") Long gradeId);

    void invalidUserGradeTask(@Param("userCode") String userCode, @Param("gradeIdSet") Set<Long> gradeIdSet);

    void validGradeTask(@Param("userCode") String userCode, @Param("gradeId") Long gradeId, @Param("taskCodeList") List taskCodeList);

    void validGradeTaskByUserAndGrade(@Param("userCode") String userCode, @Param("gradeId") Long gradeId, @Param("taskCodeSet") Set<String> taskCodeSet);

    void validGradeTaskByGradeAndTask(@Param("gradeId") Long gradeId, @Param("taskCodeSet") Set<String> taskCodeSet);

    void invalidGradeTaskByGradeAndTask(@Param("gradeId") Long gradeId, @Param("taskCodeSet") Set<String> taskCodeSet);

    List<String> queryUserCodeListByTaskCode(@Param("taskCode") String taskCode);

    List<String> queryTaskCodeListByUserCode(@Param("userCode") String userCode);

    Set<Long> queryTaskIdByGradeId(@Param("gradeId") Long gradeId, @Param("userCode") String userCode);

    /**
     * @Description:查询某个人是否具有某个功能权限并且具有某个数据权限
     * @param map
     * @return
     */
    List<SaaUserGradeTask> queryGradeTaskByCondtion(Map<String, Object> map);

}