package com.sinosoft.power.dao;

import com.sinosoft.power.po.SaaUserPermitData;
import ins.framework.mybatis.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 表SAA_USER_PERMIT_DATA对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 */
public interface SaaUserPermitDataDao extends MybatisBaseDao<SaaUserPermitData, Double> {

    List<SaaUserPermitData> selectAll();

    //    public List<SaaUserPermitData> selectAllByUserCode(String userCode);
    List<SaaUserPermitData> selectUserPermitComByUserCode(String userCode);

    List<SaaUserPermitData> selectUserPermitByParam(Map<String, String> param);

    void deleteDataByUserCode(String userCode);

    void deleteDataByGrade(@Param("gradeId") Long gradeId);

    void deleteDataByGradeAndComcode(@Param("gradeId") Long gradeId, @Param("companyCode") String comCode);

    void deleteDataByUserCodeAndGradeId(@Param("userCode") String userCode, @Param("gradeIds") List<Integer> gradeIds);

    void deleteDataByUserCodeAndGradeIdSet(@Param("userCode") String userCode, @Param("gradeIds") Set<Long> gradeIds);

    List<SaaUserPermitData> selectAllByUserGradeId(String userGradeId);

    Set<String> loadPermitDataSetByUserAndUserGradeId(@Param("userCode") String userCode, @Param("userGradeId") Long userGradeId, @Param("factorCode") String factorCode);

    Set<String> loadPermitDataValue2SetByUserAndUserGradeId(@Param("userCode") String userCode, @Param("userGradeId") Long userGradeId, @Param("factorCode") String factorCode);

    void deleteDataByUserAndUserGradeId(@Param("userCode") String userCode, @Param("userGradeId") Long userGradeId, @Param("upperPathSet") Set<String> upperPahtSet);

    void deleteAllUserGradeData(@Param("userCode") String userCode, @Param("userGradeId") Long userGradeId, @Param("factorCode") String factorCode);

    List<SaaUserPermitData> queryUserPermitData(@Param("userCode") String userCode, @Param("factorCode") String factorCode, @Param("userGradeId") String userGradeId);

    List<SaaUserPermitData> queryUserAllPermitData(@Param("userCode") String userCode, @Param("factorCode") String factorCode, @Param("userGradeIdSet") Set<Long> userGradeIdSet);

    List<SaaUserPermitData> queryUserGradePermitData(@Param("userCode") String userCode, @Param("userGradeId") Long userGradeId);

    //查询相应权限下所有机构
    List<SaaUserPermitData> queryUserPermitDataByCondition(Map<String, String> param);

    List<String> queryDataValue1ByParamString(Map<String, String> param);

    List<String> getPermitComcode(String userCode, String taskcode);

    Set<String> selectUserPermitDataByFactor(@Param("userCode") String userCode, @Param("factorCode") String factorCode);

}