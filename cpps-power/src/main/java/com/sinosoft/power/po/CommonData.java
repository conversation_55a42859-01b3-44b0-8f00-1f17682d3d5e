package com.sinosoft.power.po;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表COMMONDATA的PO对象<br/>
 * 对应表名：COMMONDATA,备注：公共数据表
 *
 */
@Data
public class CommonData implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：ID,备注：Id */
	private Long id;
	/** 对应字段：DATATYPE,备注：数据类型 */
	private String dataType;
	/** 对应字段：DATACODE,备注：数据代码 */
	private String dataCode;
	/** 对应字段：DATANAME,备注：数据名称 */
	private String dataName;
	/** 对应字段：PRETYPE,备注：父节点类型 */
	private String preType;
	/** 对应字段：PRECODE,备注：父节点代码 */
	private String preCode;
    /** 对应字段：MAPPERDATACODE,备注：父节点代码 */
    private String mapperDataCode;
    private String validStatus;

}
