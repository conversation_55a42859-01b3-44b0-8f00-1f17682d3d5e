package com.sinosoft.power.po;

import java.io.Serializable;

import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表PRPDADDRESS的PO对象<br/>
 * 对应表名：PRPDADDRESS
 *
 */
@Data
public class PrpDAddress implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：CODE */
	private String code;
	/** 对应字段：NAME */
	private String name;
	/** 对应字段：PARENT */
	private String parent;
	/** 对应字段：VALIDSTATUS */
	private String validStatus;

}
