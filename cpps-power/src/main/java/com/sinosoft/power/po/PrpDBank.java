package com.sinosoft.power.po;

import java.io.Serializable;

import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表PRPDBANK的PO对象<br/>
 * 对应表名：PRPDBANK
 *
 */
@Data
public class PrpDBank implements Serializable {
	//表结构修改后手动更新PO  liuxinran  ********
	private static final long serialVersionUID = 1L;
	/** 对应字段：BANKCODE,备注：银行编码 */
	private String bankCode;
	/** 对应字段：BANKNAME,备注：银行名称 */
	private String bankName;
	/** 对应字段：BANKUID,备注：主键ID */
	private String bankUid;
	/** 对应字段：INSERTTIMEFORHIS,备注：系统插入时间 */
	private String InsertTimeForHis;
	/** 对应字段：REMARK,备注：备注信息 */
	private String remark;

}
