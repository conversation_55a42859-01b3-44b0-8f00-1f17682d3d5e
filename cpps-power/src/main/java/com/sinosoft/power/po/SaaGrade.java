package com.sinosoft.power.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表SAAGRADE的PO对象<br/>
 * 对应表名：SAAGRADE
 *
 */
@Data
public class SaaGrade implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：ID */
	private Long id;
	/** 对应字段：SYSTEMCODE */
	private String systemCode;
	/** 对应字段：GRADECNAME */
	private String gradeCName;
	/** 对应字段：GRADEENAME */
	private String gradeEName;
	/** 对应字段：COMCODE */
	private String comCode;
	/** 对应字段：CREATORCODE */
	private String creatorCode;
	/** 对应字段：UPDATERCODE */
	private String updaterCode;
	/** 对应字段：VALIDSTATUS */
	private String validStatus;
	/** 对应字段：INSERTTIMEFORHIS */
	private Date insertTimeForHis;
	/** 对应字段：OPERATETIMEFORHIS */
	private Date operateTimeForHis;
	/** 开关 */
	private String numflag;
	/** 职级 */
	private String userInd;

}
