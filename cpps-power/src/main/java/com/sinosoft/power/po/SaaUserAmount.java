package com.sinosoft.power.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表_SAA_USER_AMOUNT的PO对象<br/>
 * 对应表名：_SAA_USER_AMOUNT
 *
 */
@Data
public class SaaUserAmount implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：ID */
	private Long id;
	/** 对应字段：SYSTEMCODE */
	private String systemCode;
	/** 对应字段：USER_CODE */
	private String userCode;
	/** 对应字段：USER_NAME */
	private String userName;
	/** 对应字段：COM_CODE */
	private String comCode;
	/** 对应字段：TASK_CODE */
	private String taskCode;
	/** 对应字段：VALUE_LOWER */
	private BigDecimal valueLower;
	/** 对应字段：VALUE_UPPER */
	private BigDecimal valueUpper;
	/** 对应字段：DEAD_LINE */
	private Date deadLine;
	/** 对应字段：START_DATE */
	private Date startDate;
	/** 对应字段：TASK_PARENT_CODE */
	private String taskParentCode;
	/** 对应字段：INSERT_TIME_FOR_HIS */
	private Date insertTimeForHis;
	/**
	 * modify ADD riskCode zuoyanhua
	 */
	private String classCode;
	/** 对应字段：OPERATE_TIME_FOR_HIS */
	private Date operateTimeForHis;
}
