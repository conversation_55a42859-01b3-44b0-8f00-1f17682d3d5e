package com.sinosoft.power.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表SAAUSERGRADE的PO对象<br/>
 * 对应表名：SAAUSERGRADE
 *
 */
@Data
public class SaaUserGrade implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：ID */
	private Long id;
	/** 对应字段：USERCODE */
	private String userCode;
	/** 对应字段：GRADEID */
	private Long gradeId;
	/** 对应字段：CREATORCODE */
	private String creatorCode;
	/** 对应字段：UPDATERCODE */
	private String updaterCode;
	/** 对应字段：VALIDSTATUS */
	private String validStatus;
	/** 对应字段：INSERTTIMEFORHIS */
	private Date insertTimeForHis;
	/** 对应字段：OPERATETIMEFORHIS */
	private Date operateTimeForHis;

}
