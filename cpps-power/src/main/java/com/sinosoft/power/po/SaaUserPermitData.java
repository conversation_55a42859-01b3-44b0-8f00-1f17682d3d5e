package com.sinosoft.power.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表SAAUSERPERMITDATA的PO对象<br/>
 * 对应表名：SAAUSERPERMITDATA
 *
 */
@Data
public class SaaUserPermitData implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：ID */
	private Long id;
	/** 对应字段：SYSTEMCODE */
	private String systemCode;
	/** 对应字段：USERCODE */
	private String userCode;
	/** 对应字段：COMCODE */
	private String comCode;
	/** 对应字段：USERGRADEID */
	private Long userGradeId;
	/** 对应字段：FACTORGROUP */
	private Double factorGroup;
	/** 对应字段：factorcode */
	private String factorcode;
	/** 对应字段：DATAOPER */
	private String dataOper;
	/** 对应字段：DATAVALUE1 */
	private String dataValue1;
	/** 对应字段：DATAVALUE2 */
	private String dataValue2;
	/** 对应字段：CREATECODE */
	private String createCode;
	/** 对应字段：CREATETIME */
	private Date createTime;
	/** 对应字段：UPDATECODE */
	private String updateCode;
	/** 对应字段：UPDATETIME */
	private Date updateTime;
	/** 对应字段：VALIDFLAG */
	private String validFlag;
	/** 对应字段：REMARK */
	private String remark;
	/** 对应字段：FLAG */
	private String flag;
	/** 对应字段：SYNFLAG */
	private String synFlag;

}
