package com.sinosoft.power.redis;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import static  com.sinosoft.power.common.util.RedisKeyUtil.STRING_SEPARTOR;

import java.util.*;

/**
 * @author: yancun
 * @create: 2018/12/22 10:37
 * @description:
 */
public class RedisUtil {

    public static List<String> string2List(String value){
        if (StringUtils.isBlank(value)){
            throw new IllegalArgumentException();
        }
        String[] itemArr = StringUtils.split(value,STRING_SEPARTOR);
        List result = Arrays.asList(itemArr);
        return result;
    }

    public static Set<String> string2Set(String value){
        if (StringUtils.isBlank(value)){
            throw new IllegalArgumentException();
        }
        String[] itemArr = StringUtils.split(value,STRING_SEPARTOR);
        Set<String> resultSet = new HashSet<>();
        resultSet.addAll(Arrays.asList(itemArr));
        return resultSet;
    }

    public static String collection2String(Collection<String> valueSet){
        StringBuilder stringBuilder = new StringBuilder();
        if (CollectionUtils.isEmpty(valueSet)){
            throw new IllegalArgumentException();
        }
        for (String value : valueSet){
            stringBuilder.append(value).append(STRING_SEPARTOR);
        }
        stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        return stringBuilder.toString();
    }

    public static void main(String[] args) {
        String value = "1|2|2";
        Set list = string2Set(value);
        System.out.println(list);
        String newValue = collection2String(list);
        System.out.println(newValue);
    }

}
