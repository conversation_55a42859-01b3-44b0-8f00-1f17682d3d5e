package com.sinosoft.power.redis;

import java.util.ArrayList;
import java.util.List;

import static com.sinosoft.power.common.util.RedisKeyUtil.*;

/**
 * @author: yancun
 * @create: 2018/12/21 18:07
 * @description:
 */
public class SaaRedisService {

    private SaaRedisTemplateUtil templateUtil;

    public static SaaRedisService getInstance(){
        return new SaaRedisService();
    }

    private SaaRedisService(){
        templateUtil = SaaRedisTemplateUtil.getInstance();
    }



    /**
     * 根据powerKey设置用户数据权限进缓存
     *
     * @param userCode
     * @param dataType
     * @param dataKey
     * @param dataValue 0:无权限 1:有权限
     */
    public void setUserDataByTypeKeyAndValue(String userCode, String dataType, String dataKey, String dataValue) {
        templateUtil.hmSet(USER_PREFIX + userCode, dataType + REDIS_SEPARTOR + dataKey, dataValue);
    }

    /**
     * 从缓存获取用户的powerKey数据权限
     *
     * @param userCode
     * @param dataType
     * @param dataKey
     * @return 0:无权限 1:有权限
     */
    public String getUserDataByKeyTypeAndValue(String userCode, String dataType, String dataKey) {
        String value = (String) templateUtil.hmGet(USER_PREFIX + userCode, dataType + REDIS_SEPARTOR + dataKey);
        return value;
    }

    /**
     * 获取用户指定类型的数据
     *
     * @param userCode
     * @param dataKey
     * @return
     */
    public String getUserDataByKey(String userCode, String dataKey) {
        String value = (String) templateUtil.hmGet(USER_PREFIX + userCode, dataKey);
        return value;
    }

    /**
     * 设置用户指定类型的数据
     *
     * @param userCode
     * @param dataType
     * @param dataValue
     * @return
     */
    public void setUserDataByTypeAndValue(String userCode, String dataType, String dataValue) {
        templateUtil.hmSet(USER_PREFIX + userCode, dataType, dataValue);
    }

    /**
     * 将用户所属的缓存失效(用户的权限修改后，需要调用该方法)
     * @param userCode
     */
    public void invalidUserData(String userCode) {
        templateUtil.reMoveOne(USER_PREFIX + userCode);
    }

    /**
     * 将用户列表所属的缓存失效(用户的权限修改后，需要调用该方法)
     * @param userCodeList
     */

    public void invalidUserListData(List<String> userCodeList) {
        List<String> keyList = new ArrayList<>();
        for(String userCode : userCodeList){
            keyList.add(USER_PREFIX + userCode);
        }
        String[] strings = new String[keyList.size()];
        templateUtil.remove(keyList.toArray(strings));
    }



}
