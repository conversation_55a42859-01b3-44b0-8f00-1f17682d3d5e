package com.sinosoft.power.redis;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinosoft.power.common.util.RedisKeyUtil;
import com.sinosoft.power.common.util.SpringContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import redis.clients.jedis.JedisPoolConfig;

import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @author: yancun
 * @create: 2018/12/21 17:41
 * @description:
 */
public class SaaRedisTemplateUtil<K, V, HK, HV> {
    private Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private RedisTemplate<K, V> redisTemplate;


    private SaaRedisTemplateUtil() {
        initRedis();
    }

    public static SaaRedisTemplateUtil getInstance() {
        return new SaaRedisTemplateUtil();
    }

    private void initLocalRedis() {

        Properties properties = new Properties();
        InputStream inputStream = SaaRedisTemplateUtil.class.getClassLoader().getResourceAsStream("redis-config.properties");
        String hostName;
        int port;
        String password;

        try {
            properties.load(inputStream);
            hostName = properties.getProperty("redis.hostname");
            port = Integer.valueOf(properties.getProperty("redis.port"));
            password = properties.getProperty("redis.password");
        } catch (Exception e) {
            throw new IllegalArgumentException("Redis数据源配置异常，请检查!");
        }


        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();

        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory();
        jedisConnectionFactory.setHostName(hostName);
        jedisConnectionFactory.setPort(port);
        jedisConnectionFactory.setPassword(password);
        jedisConnectionFactory.setUsePool(true);
        jedisConnectionFactory.setPoolConfig(jedisPoolConfig);
        jedisConnectionFactory.afterPropertiesSet();

        redisTemplate = new RedisTemplate<>();

        redisTemplate.setConnectionFactory(jedisConnectionFactory);

        //使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<Object>(Object.class);

        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        serializer.setObjectMapper(mapper);

        redisTemplate.setValueSerializer(serializer);
        //使用StringRedisSerializer来序列化和反序列化redis的key值
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.afterPropertiesSet();
        if(inputStream!=null){
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private void initRedis() {
        if (SpringContextUtil.isContextExists()) {
            this.redisTemplate = (RedisTemplate<K, V>) SpringContextUtil.getBean("redisTemplate");
        } else {
            this.initLocalRedis();
        }
    }

    /**
     * 写入缓存
     *
     * @param key
     * @param value
     * @return
     */

    public boolean set(final K key, V value) {
        boolean result = false;
        try {
            ValueOperations<K, V> operations = redisTemplate.opsForValue();
            operations.set(key, value);
            result = true;
        } catch (Exception e) {
            LOGGER.error("写入缓存失败：{}", getStackTrace(e));
        }
        return result;
    }

    /**
     * 写入缓存设置时效时间
     *
     * @param key
     * @param value
     * @param expireTime
     * @return
     */
    public boolean set(final K key, V value, Long expireTime) {
        boolean result = false;
        try {
            ValueOperations<K, V> operations = redisTemplate.opsForValue();
            operations.set(key, value);
            redisTemplate.expire(key, expireTime, TimeUnit.SECONDS);
            result = true;
        } catch (Exception e) {
            LOGGER.error("写入缓存设置时效时间失败：{}", getStackTrace(e));
        }
        return result;
    }

    /**
     * 批量删除对应的value
     *
     * @param keys
     */
    public void remove(final String... keys) {
        for (String key : keys) {
            reMoveOne((K) key);
        }
    }

    /**
     * 批量删除key
     * 根据一定的规则模糊匹配删除数据
     *
     * @param pattern
     */
    public void removePattern(final K pattern) {
        Set<K> keys = redisTemplate.keys(pattern);
        if (keys.size() > 0) {
            redisTemplate.delete(keys);
        }
    }

    /**
     * 删除对应的value
     *
     * @param key
     */
    public void reMoveOne(final K key) {
        if (exists(key)) {
            redisTemplate.delete(key);
        }
    }

    /**
     * 判断缓存中是否有对应的value
     *
     * @param key
     * @return
     */
    public boolean exists(final K key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 读取缓存
     *
     * @param key
     * @return
     */
    public V get(K key) {
        V result;
        ValueOperations<K, V> operations = redisTemplate.opsForValue();
        result = operations.get(key);
        return result;
    }

    /**
     * 读取缓存
     *
     * @param key
     * @return
     */
    public V getC(K key) {
        V result;
        ValueOperations<K, V> operations = redisTemplate.opsForValue();
        result = operations.get(key);
        return result;
    }

    /**
     * 哈希 添加
     *
     * @param key
     * @param hashKey
     * @param value
     */
    public void hmSet(K key, HK hashKey, HV value) {
        HashOperations<K, HK, HV> hash = redisTemplate.opsForHash();
        hash.put(key, hashKey, value);
    }

    /**
     * 哈希 删除
     *
     * @param key
     * @param hashKey
     */
    public void removeHSet(K key, HK hashKey) {
        HashOperations<K, HK, HV> hash = redisTemplate.opsForHash();
        hash.delete(key, hashKey);
    }

    /**
     * 哈希 大小
     *
     * @param key
     */
    public long hSize(K key) {
        HashOperations<K, HK, HV> hash = redisTemplate.opsForHash();
        return hash.size(key);
    }

    /**
     * 哈希 entry
     *
     * @param key
     */
    public Map<HK, HV> hEntry(K key) {
        HashOperations<K, HK, HV> hash = redisTemplate.opsForHash();
        return hash.entries(key);
    }

    /**
     * 哈希获取数据
     *
     * @param key
     * @param hashKey
     * @return
     */
    public HV hmGet(K key, HK hashKey) {
        HashOperations<K, HK, HV> hash = redisTemplate.opsForHash();
        return hash.get(key, hashKey);
    }

    /**
     * 列表添加
     *
     * @param k
     * @param v
     */
    public void lPush(K k, V v) {
        ListOperations<K, V> list = redisTemplate.opsForList();
        list.rightPush(k, v);
    }

    /**
     * 列表获取
     *
     * @param k
     * @param l
     * @param l1
     * @return
     */
    public List<V> lRange(K k, long l, long l1) {
        ListOperations<K, V> list = redisTemplate.opsForList();
        return list.range(k, l, l1);
    }

    /**
     * 判断是否存在key，或redis报错
     *
     * @param key
     * @param hashKey
     * @return
     */
    public String existKey(final K key, final HK hashKey) {
        String flag = new String();
        try {
            boolean haveKey = exists(key);
            if (haveKey) {
                Object result = hmGet(key, hashKey);
                if (null == result) {
                    flag = RedisKeyUtil.NON_EXISTENT;
                } else {
                    flag = RedisKeyUtil.EXISTENT;
                }
            } else {
                flag = RedisKeyUtil.NON_EXISTENT;
            }
        } catch (Exception e) {
            flag = RedisKeyUtil.ERROR;
        }
        return flag;
    }

    /**
     * @Description:校验key是否存在
     * @param:key
     * @return:存在返回1，不存在返回0，异常返回2
     */
    public String existKey(final K key) {
        String str = "";
        try {
            boolean existFlag = exists(key);
            if (existFlag) {
                str = RedisKeyUtil.EXISTENT;
            } else {
                str = RedisKeyUtil.NON_EXISTENT;
            }
        } catch (Exception e) {
            str = RedisKeyUtil.ERROR;
        }
        return str;
    }


    /**
     * 集合添加
     *
     * @param key
     * @param value
     */
    public void add(K key, V value) {
        SetOperations<K, V> set = redisTemplate.opsForSet();
        set.add(key, value);
    }

    /**
     * 集合获取
     *
     * @param key
     * @return
     */
    public Set<V> setMembers(K key) {
        SetOperations<K, V> set = redisTemplate.opsForSet();
        return set.members(key);
    }

    /**
     * 有序集合添加
     *
     * @param key
     * @param value
     * @param score
     */
    public void zAdd(K key, V value, double score) {
        ZSetOperations<K, V> zset = redisTemplate.opsForZSet();
        zset.add(key, value, score);
    }

    /**
     * 有序集合删除
     *
     * @param key
     * @param value
     */
    public void zRemove(K key, V value) {
        ZSetOperations<K, V> zset = redisTemplate.opsForZSet();
        zset.remove(key, value);
    }

    /**
     * 有序集合大小
     *
     * @param key
     */
    public long zSize(K key) {
        ZSetOperations<K, V> zset = redisTemplate.opsForZSet();
        return zset.size(key);
    }

    /**
     * 有序集合获取 顺序
     *
     * @param key
     * @param min
     * @param max
     * @return
     */
    public Set<V> rangeByScore(K key, double min, double max) {
        ZSetOperations<K, V> zset = redisTemplate.opsForZSet();
        return zset.rangeByScore(key, min, max);
    }

    /**
     * 有序集合获取 逆序
     *
     * @param key
     * @param min
     * @param max
     * @return
     */
    public Set<V> reverseRangeByScore(K key, double min, double max) {
        ZSetOperations<K, V> zset = redisTemplate.opsForZSet();
        return zset.reverseRangeByScore(key, min, max);
    }

    /**
     * 获取分布式锁
     */
    public boolean getDistributeLock(K lockKey, Long timeOut) {
        boolean result;
        try {
            ValueOperations<K, V> operations = redisTemplate.opsForValue();
            result = operations.setIfAbsent(lockKey, null);
            redisTemplate.expire(lockKey, timeOut, TimeUnit.SECONDS);
            if (result) {
                LOGGER.info("已获取到 {} 对应的锁!", lockKey);
            } else {
                LOGGER.info("未获取到{}任务执行锁,锁已被集群中其他服务获取！", lockKey);
            }
        } catch (Exception e) {
            LOGGER.error("Redis操作异常!\n{}", getStackTrace(e));
            result = true;
        }
        return result;
    }

    /**
     * 释放分布式锁
     */
    public boolean releaseDistributeLock(K lockKey) {
        boolean result;
        try {
            redisTemplate.delete(lockKey);
            result = true;
        } catch (Exception e) {
            LOGGER.error("Redis操作异常!\n{}", getStackTrace(e));
            result = false;
        }
        return result;
    }

    private String getStackTrace(Throwable aThrowable) {
        final Writer result = new StringWriter();
        final PrintWriter printWriter = new PrintWriter(result);
        aThrowable.printStackTrace(printWriter);
        return result.toString();
    }


}
