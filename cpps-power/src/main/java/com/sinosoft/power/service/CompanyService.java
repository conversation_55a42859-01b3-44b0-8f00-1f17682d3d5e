package com.sinosoft.power.service;

import com.sinosoft.power.dao.SaaCompanyDao;
import com.sinosoft.power.po.PrpDCompany;
import com.sinosoft.power.po.SaaUserPermitData;
import static com.sinosoft.power.util.PowerConstant.*;
import com.sinosoft.power.vo.CompanyVo;
import ins.framework.utils.Beans;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: yancun
 * @create: 2019/2/20 16:13
 * @description:机构服务类，供powerApi调用
 */
public class CompanyService {
    private Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private SqlSession session;

    private SaaCompanyDao companyDao;

    public CompanyService(SqlSession session){
        this.session = session;
        companyDao = session.getMapper(SaaCompanyDao.class);
    }

    /**
     *@Description:同步单个机构信息,网关调用
     */
    public String syncComData(CompanyVo companyVo) {
        String status = VALID; //status值为0代表成功，1代表失败
        int rows = 0;
        String comCode = companyVo.getComCode().substring(0,4) + "0000";
        String comFlag = companyDao.queryComFlag(comCode);

        //组装机构po对象
        PrpDCompany prpdcompany = new PrpDCompany();
        prpdcompany.setComCode(companyVo.getComCode());
        prpdcompany.setComCName(companyVo.getComCName());
        prpdcompany.setComEName(companyVo.getComEName());
        prpdcompany.setComLevel(companyVo.getComLevel());
        prpdcompany.setComType(companyVo.getComType());
        prpdcompany.setCenterFlag(companyVo.getCenterFlag());
        prpdcompany.setUpperComCode(companyVo.getUpperComCode());
        prpdcompany.setValidStatus(companyVo.getValidStatus());
        prpdcompany.setComFlag(comFlag);

        String v_comlevel = "";//机构级别
        String v_upperpath = "";
        boolean isAddFlag = false;//是否是新增机构
        List<PrpDCompany> companyList = companyDao.findPrpdCompanysByCode(prpdcompany.getComCode());
        //新增机构取其上级机构的所有父机构的集合进行赋值
        if (companyList == null || companyList.size() == 0) {
            isAddFlag = true;
            companyList = companyDao.findPrpdCompanysByCode(prpdcompany.getUpperComCode());
        }
        for (PrpDCompany prpDCompany : companyList) {
            if (v_upperpath == "") {
                v_upperpath = prpDCompany.getComCode();
            } else {
                v_upperpath = v_upperpath + "/" + prpDCompany.getComCode();
            }
        }
        v_comlevel = companyList.size() + "";
        //新增机构的机构等级和上层路径的赋值
        if (isAddFlag) {
            v_comlevel = String.valueOf(companyList.size() + 1);
            v_upperpath = v_upperpath + "/" + prpdcompany.getComCode();
        }
        prpdcompany.setComLevel(v_comlevel);
        prpdcompany.setUpperPath(v_upperpath);

        //插入或更新数据，再更新上传路径
        if (companyDao.selectByPrimaryKey(companyVo.getComCode()) != null) { //记录已存在则更新记录
            prpdcompany.setOperateTimeForHis(new Date(System.currentTimeMillis()));
            rows = companyDao.updateSelectiveByPrimaryKey(prpdcompany);
            //记录有更新时
            if (rows > 0) {
                status = INVALID;
            }
        } else { //记录不存在则插入记录
            prpdcompany.setInsertTimeForHis(new Date(System.currentTimeMillis()));
            rows = companyDao.insertSelective(prpdcompany);
            //记录成功插入时
            if (rows > 0) {
                status = INVALID;
            }
        }
        return status;
    }

    /**
     *@Description:同步全量机构信息,网关调用
     */
    public String handFullSyncCompanyData() {
        String status = VALID; //status值为0代表成功，1代表失败
        //查询出所有的有效机构
        List<PrpDCompany> companyList = companyDao.queryAll();
        for (PrpDCompany prpDCompany : companyList) {
            CompanyVo companyVo = new CompanyVo();
            companyVo.setComCode(prpDCompany.getComCode());
            companyVo.setComCName(prpDCompany.getComCName());
            companyVo.setComEName(prpDCompany.getComEName());
            companyVo.setComLevel(prpDCompany.getComLevel());
            companyVo.setComType(prpDCompany.getComType());
            companyVo.setCenterFlag(prpDCompany.getCenterFlag());
            companyVo.setUpperComCode(prpDCompany.getUpperComCode());
            companyVo.setValidStatus(prpDCompany.getValidStatus());
            status = this.syncComData(companyVo);
        }
        return status;
    }

    /**
     *@Description:查询部分机构信息,comCode,comCName
     */
    public List<CompanyVo> queryAllData() {
        List<CompanyVo> companyVos = new ArrayList<>();
        List<PrpDCompany> companyList = companyDao.queryAllData();
        CompanyVo companyVo =new CompanyVo();
        for(PrpDCompany company:companyList){
            Beans.copy().from(company).to(companyVo);
            companyVos.add(companyVo);
        }
        return companyVos;
    }


    /**
     *@Description:根据comCode查询机构信息
     *@param:
     *@return:PrpDCompany
     */
    public PrpDCompany querycompanyBycomCode(String comCode){
        return companyDao.querycompanyBycomCode(comCode);
    }

    /**
     *@Description:根据comCode查询机构upperpath
     *@param:
     *@return:PrpDCompany
     */
    public String queryCompanyUpperPath(String comCode){
        if(StringUtils.isNotEmpty(comCode)) {
            PrpDCompany company  = companyDao.queryCompanyUpperPath(comCode);
            if(company != null){
                return company.getUpperPath();
            }
        }
        return StringUtils.EMPTY;
    }


    /**
     *@Description:根据主键查询机构信息  去掉
     *@param:
     *@return:PrpDCompany
     */
    public PrpDCompany selectByPrimaryKey(String comCode){
        return companyDao.selectByPrimaryKey(comCode);

    }

    public List<PrpDCompany> queryLikeComCodes(Map<String, String> param){
        return companyDao.queryLikeComCodes(param);
    }

    public List<PrpDCompany> queryAll(){
        return companyDao.queryAll();
    }

    public List<PrpDCompany> queryAllTreeData(){
        return companyDao.queryAllTreeData();
    }

//	List<String> queryByParam(String param);

    public List<PrpDCompany> querySecondThirdCompany(){
        return companyDao.querySecondThirdCompany();
    }

    public List<PrpDCompany> queryCenterComByFlag(){
        return companyDao.queryCenterComByFlag();
    }

    public List<PrpDCompany> queryThirdCompany(String comCode){
        return companyDao.queryThirdCompany(comCode);
    }

    /**查询外省分支机构*/
    public List<PrpDCompany> queryOtherProvincesCompany(String comNo){
        return companyDao.queryOtherProvincesCompany(comNo);
    }

    /**查询外省分支机构*/
    public List<PrpDCompany> queryOtherProvincesCompanyOur(){
        return companyDao.queryOtherProvincesCompanyOur();
    }

    public PrpDCompany queryUpperThreeCompany(String comCode){
        return companyDao.queryUpperThreeCompany(comCode);
    }

    /**查询除总公司外的所有机构*/
    public List<PrpDCompany> queryOtherProvincesOur(String comNo){
        return companyDao.queryOtherProvincesOur(comNo);
    }

    /**查询本机构向下的所有机构*/
    public List<PrpDCompany> queryOtherComCurrent(String comNo){
        return companyDao.queryOtherComCurrent(comNo);
    }


    /*根据UpperPath查询当前机构的分公司*/
    public List<PrpDCompany> queryCompanyByUpperPath(List<String> comCodeSet){
        return companyDao.queryCompanyByUpperPath(comCodeSet);
    }

    /**查询相应权限下所有机构*/
    public List<PrpDCompany> queryAuthorityCom( List<SaaUserPermitData> saaUserPermitDataList){
        return companyDao.queryAuthorityCom(saaUserPermitDataList);
    }

    /**查询相应权限下某二级机构下的所有机构*/
    public List<PrpDCompany> queryAuthorityComForRedisCom(List<SaaUserPermitData> saaUserPermitDataList, String comCode){
        return companyDao.queryAuthorityComForRedisCom(saaUserPermitDataList,comCode);
    }

    /**查询相应权限下所有机构*/
    public List<String> queryAuthorityComString(List<String> stringlist){
        return companyDao.queryAuthorityComString(stringlist);
    }


    public List<String> queryAuthorityComStringupperPath(String makeCome){
        return companyDao.queryAuthorityComStringupperPath(makeCome);
    }

    public List<PrpDCompany> queryRegistHandleCom(String comNo){
        return companyDao.queryRegistHandleCom(comNo);
    }

    public List<String> getAllBranchCompany(){
        return companyDao.getAllBranchCompany();
    }

    public List<PrpDCompany> queryHavePowerComCode(String upperPath,String classCode,String taskCode){
        return companyDao.queryHavePowerComCode(upperPath,classCode,taskCode);
    }

    public List<PrpDCompany> queryComCode(String upperPath,String taskCode){
        return companyDao.queryComCode(upperPath,taskCode);
    }

    /**
     * 新增机构取其上级机构的所有父机构的集合
     */
    public List<PrpDCompany> findPrpdCompanysByCode(String comCode){
        return companyDao.findPrpdCompanysByCode(comCode);
    }

    /**
     * @Description:联合printComName 和comCname作为查询结果
     * @param comCode
     * @return String
     */
    public String findContactComNameByComCode(String comCode){
        return companyDao.findContactComNameByComCode(comCode);
    }

    /**
     * @Description:查询所有的comCode
     * @return:List<String>
     */
    public List<String> queryComCodeAll(){
        return companyDao.queryComCodeAll();
    }


    /**
     * @Description:根据comCode查询所有子comcode
     * @param:comcode
     * @return:list
     */
    public List<String> queryPriorComCodeList( String comCode){
        return companyDao.queryPriorComCodeList(comCode);
    }

    /**
     * 查询用户对应comCode的机构信息
     * @param userCode
     * @return
     */
    public PrpDCompany queryCompanyByUserCode(String userCode){
        if(StringUtils.isEmpty(userCode)){
            return null;
        }
        return companyDao.queryCompanyByUserCode(userCode);
    }

    /**
     * 查当前机构代码的所属分公司代码
     * @param comCode 机构代码
     * @param comLevel 用户等级
     * @return list
     */
    public List<PrpDCompany>  queryPriorCompanyByComLevel(String comCode,String comLevel){
        if(StringUtils.isNotEmpty(comCode) && StringUtils.isNotEmpty(comLevel)) {
            return companyDao.queryPriorCompanyByComLevel(comCode,comLevel);
        }
        return null;
    }
}
