package com.sinosoft.power.service;


import com.sinosoft.power.vo.MenulistVo;
import com.sinosoft.power.vo.RequestKeyWordVo;
import com.sinosoft.power.vo.SysPowerUserVo;


import java.util.*;

public interface FilterPowerService {

    /**
     *@Description:获取某机构下的拥有某节点权限的所有人员列表
     *@param:  map
     *@param:  currentUserCode
     *@return: list
     */
    List<SysPowerUserVo> queryUserListByParams(Map<String, String> params, String currentUserCode);

    /**
     *@Description:获取用户下的权限集合
     *@param:  userCode 用户编码
     *@return: set taskcodes
     */
    Set<String> gainUserTaskCodes(String userCode);

    /**
     *@Description:权限校验url和获取参数的映射关系
     *@param:
     *@return:map
     */
    Map<String, RequestKeyWordVo> gainRegistNoMapper();

    /**
     * @Description:获得有相应权限的菜单名称
     * @param userCode
     * @return set
     */
    Set<String> gainPowerMenuByTaskCodes(String userCode);
    /**
     *@Description:获取所有的菜单列表
     *@param:
     *@return:list
     */
    List<MenulistVo> gainAllMenulist();

}
