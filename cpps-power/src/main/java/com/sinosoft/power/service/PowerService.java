package com.sinosoft.power.service;


import com.sinosoft.power.po.*;
import com.sinosoft.power.vo.*;


import java.util.*;

/**
 * @author: yancun
 * @create: 2018/12/19 14:20
 * @description:
 */
public interface PowerService {
    /**
     *@Description:判断某个人是否具有某个权限
     *@param:userCode 用户代码
     *@param:powerTaskCode 权限代码
     *@return:boolean
     */
    boolean checkPower(String userCode, String powerTaskCode);

    /**
     * @Description:权限拼接SQL 暂时未用到
     * @param userCode 用户代码
     * @param taskCode 权限代码
     * @return map
     */
    Map<String, String> addPower(String userCode, String taskCode);

    /**
     * @Description:查询用户岗位下的险类树  先从缓存中取，没有取码表 type为ClassCode
     * @param:userCode 用户代码
     * @param:gradeId  岗位id
     * @return:list
     */
    List<ComTreeNode> queryClassTree(String userCode, Long gradeId);

    /**
     * @Description: 查询用户的功能权限 给bpm使用
     * @param: userCode
     * @param: taskCode
     * @param: tableName
     * @return:string
     */
    String addPower(String userCode, String taskCode, String tableName);

    /**
     * @Description:获取某机构下的拥有某节点权限的所有人员列表
     * @param:powerKeyWord
     * @param:loginUserCode 当前登录的人员代码
     * @return: list
     */
    List<SysPowerUserVo> queryPowerUserList(PowerKeyWord powerKeyWord, String loginUserCode);

    /**
     * @Description:通过机构，险种，板块，功能代码，用户判断此用户是否有此数据权限
     * @param:powerKeyWord
     * @param:userCode
     * @return:boolean
     */
    boolean assertUserDataPower(PowerKeyWord powerKeyWord, String userCode) ;

    /**
     * @Description:查询用户下所有的权限列表
     * @param:userCode 用户代码
     * @return:list
     */
    List<String> queryTaskCodeListByUser(String userCode);

    /**
     * @Description:获取用户岗位功能权限
     * @param:userCode 用户代码
     * @return:list
     */
    List<String> queryGradeTaskCodeListByUser(String userCode);

    /**
     * @Description:获取全量的用户
     * @param:
     * @return: list
     */
    List<SysPowerUserVo> queryAllUserList();

    /**
     * @Description:查询用户维护并分页
     * @param: dataTablePageVo
     * @param: queryparams
     * @param: companyCode
     * @rturn: DataTablePageVo
     */
//    DataTablePageVo queryPageUserList(DataTablePageVo dataTablePageVo, String queryparams, String makeCom);

    /**
     * @Description:根据用户代码获取用户信息
     * @param:userCode 用户代码
     * @return: SysPowerUserVo
     */
    SysPowerUserVo findUserByUserCode(String userCode);

    /**
     * @Description:校验传入的taskCode用户是否具备
     * @param: taskCode 权限代码
     * @param: userCode 用户代码
     * @return:boolean
     */
    boolean validTaskCode(String taskCode, String userCode);

    /**
     * @Description:校验用户的数据权限
     * @param:userCode 用户代码
     * @return:boolean
     */
    boolean validateDataPower(String userCode, String taskCode, String comCode, String classCode);

    Map<String, Map<String, Map<String, List<SaaUserPermitData>>>> groupUserPermitData();

    Map<String, Boolean> validDataOperPower(SaaUserPermitData userPermitData, String factorValue, String factorCode);

    List<SaaGrade> queryUserGradeList(String userCode);

    String selectUserPermitComByUserCode(String userCode);

    String queryUserGradeComTree(String userCode, Long gradeId);

//    DataTablePageVo queryPageGradeList(DataTablePageVo dataTablePageVo, String queryparams);

    void setStatus(List<ComTreeNode> comNodeTreeList);

    List<ComTreeNode> queryComTree(String userCode, String gradeId, String comCode, String loginComCode, String loginUserCode);

    Set<String> querySelectComCodeByUser(String userCode, Long gradeId);

    Set<String> selectUserPermitDataByFactor(String userCode, String factorCode);

    Set<String> selectUserPermitCom(String userCode);

    void buildAllUpperPath();

    String buildUpperPath(String comCode);

    Boolean checkPower(String userCode, String taskCode, String claimCom, String registNo);

    Set<String> querySelectClassByUser(String userCode, Long gradeId);

    UserInParamVo querySingleUser(String userCode);

    /**
     * @Description:校验用户是否具有系统维护权限
     * @param: userCode 用户代码
     * @param: powerTaskCode  权限代码
     * @return:void
     */
    void checkSysPower(String userCode, String powerTaskCode);

//    DataTablePageVo queryPageUsersList(DataTablePageVo dataTablePageVo, String queryparams, String makeCom);

    /**
     * @Description:保存用户信息
     * @param: vo 用户信息
     * @return: boolean
     */
    //Boolean saveMessage(UserInParamVo vo);

}
