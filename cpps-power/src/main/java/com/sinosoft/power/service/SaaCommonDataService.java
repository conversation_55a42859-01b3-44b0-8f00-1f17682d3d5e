package com.sinosoft.power.service;

import com.sinosoft.power.vo.CommonDataVo;

import java.util.List;

/**
 * @author: yancun
 * @create: 2018/12/22 14:39
 * @description:
 */
public interface SaaCommonDataService {
    /**
     * @Descirption:根据数据类型查询码表信息
     * @param dataType
     * @return list
     */
    List<CommonDataVo> queryCommonData(String dataType);

    /**
     * @Descirption:根据传入条件查询码表信息
     * @param vo
     * @return list
     */
    List<CommonDataVo> queryCommonData(CommonDataVo vo);


    String transferData(CommonDataVo vo);

}
