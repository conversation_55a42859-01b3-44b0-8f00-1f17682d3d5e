package com.sinosoft.power.service;


import com.sinosoft.power.po.SaaTask;
import com.sinosoft.power.vo.DataTablePageVo;
import com.sinosoft.power.vo.GradeApiVo;
import com.sinosoft.power.vo.PowerVo;
import com.sinosoft.power.vo.SaaClassDataVo;
import com.sinosoft.power.vo.SaaComDataVo;
import com.sinosoft.power.vo.SaaUserAmountVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: yancun
 * @create: 2018/12/19 14:20
 * @description:
 */
public interface SaaDeployService {

    void initCompleteUserGrade(PowerVo vo);

    /**
     *@Description: 初始化用户岗位信息
     * @param vo
     */
    void initUserGrade(PowerVo vo);
    /**
     *@Description: 删除用户岗位
     * @param vo
     */
    void deletePersonGrade(PowerVo vo);
    /**
     *@Description: 保存用户岗位
     * @param vo
     * @param: currentUserCode 当前登录人员代码
     */
    Set<String> saveUserGrade(PowerVo vo, String currentUserCode, Long defaultGradeId);

    /**
     * @Description:初始化人员金额信息
     * @para: dataTablePageVo 分页信息
     * @para: queryparams 查询条件
     * @return:DataTablePageVo
     */
//    DataTablePageVo initUserAmount(DataTablePageVo dataTablePageVo, String queryparams);
    /**
     *@Description: 保存用户金额
     *@param:saaUserAmountVo
     *@return:int
     */
    int saveUserAmount(SaaUserAmountVo saaUserAmountVo);
    /**
     *@Description: 更新用户金额
     *@param:saaUserAmountVo
     *@return:int
     */
    int updateUserAmount(SaaUserAmountVo saaUserAmountVo);
    /**
     *@Description: 删除用户金额
     *@param:saaUserAmountVo
     *@return:int
     */
    int deleteUserAmount(SaaUserAmountVo saaUserAmountVo);

    Map<String, Map<String, Double>> initTaskUserGradeMapper();
    /**
     *@Description: 查询岗位下的权限代码
     *@param:id
     *@return:list
     */
    List<SaaTask> getTaskByGradeId(String id);
    /**
     *@Description: 查询用户岗位下的权限代码
     *@param:id
     *@param:userCode
     *@return:list
     */
    List<SaaTask> getGradeTaskByUserAndGrade(String id, String userCode);

    /**
     * @Description:删除岗位
     * @param: gradeId
     */
    void deleteGrade(Long gradeId);
    /**
     * @Description:保存岗位
     * @param: gradeApiVo
     * @param: userCode
     * @param: companyCode
     */
    void saveGrade(GradeApiVo gradeApiVo, String userCode, String makeCom);
    /**
     * @Description:保存岗位权限
     * @param: gradeApiVo
     */
    void saveGradeTask(GradeApiVo gradeApiVo);
    /**
     * @Description:保存岗位权限
     * @param: gradeApiVo
     * @param: userCode
     */
    void updateGradeTask(GradeApiVo gradeApiVo, String userCode);

    /**
     * @Description:更新用户岗位权限
     * @param: gradeApiVo
     */
    void updateUserGradeTask(GradeApiVo gradeApiVo);

    /**
     *@Description:用户岗位功能权限配置界面，获取人员岗位列表
     *@param:userCode 用户代码
     *@return:DataTablePageVo
     */
//    DataTablePageVo queryGradeListByUserCode(DataTablePageVo dataTablePageVo, String userCode);

    Set<String> saveUserComData(SaaComDataVo saaComDataVo, String currentUserCode, String defaultComCode);

    void saveUserClassData(SaaClassDataVo saaClassDataVo, String currentUserCode);

    List<String> queryTaskCodeByUserCode(String userCode);
}
