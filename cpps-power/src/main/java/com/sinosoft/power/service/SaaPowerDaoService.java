package com.sinosoft.power.service;

import com.sinosoft.power.po.*;
import com.sinosoft.power.vo.ExportUserPowerInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: yancun
 * @create: 2018/12/21 10:53
 * @description: 向外提供权限相应可以操作dao层的service
 */
public interface SaaPowerDaoService {

    List<SaaUserGrade> findUserGradeByUserCodeAndTaskCode(Map<String, String> param);

    List<String> queryDataValue1ByParamString(Map<String, String> param);

    List<SaaUserPermitData> queryUserPermitDataByCondition(Map<String, String> param);

    List<String> getPermitComcode(String userCode, String taskcode);

    List<SaaUserPermitData> selectUserPermitByParam(Map<String, String> param);

    List<SaaUserPermitData> queryPermitDataByUserCodeAndFactorCode(@Param("userCode") String userCode, @Param("factorCode") String factorCode, @Param("userGradeId") String userGradeId);

    List<SaaUserPermitData> selectAllByUserGradeId(String userGradeId);

    List<SaaUserPermitData> selectUserPermitComByUserCode(String userCode);

    List<SaaUserAmount> queryUserAmountByUserCode(String userCode);

    SaaUserAmount queryUserAmout(@Param("userCode") String userCode, @Param("taskCode") String taskCode);

    List<SaaTask> selectAll();

    List<SaaUserGrade> queryGradeByUserCodeAndGradeIdSet(@Param("userCode") String userCode, @Param("gradeIdSet") Set<Long> gradeIdSet);

    List<SaaTask> selectByGradeId(String id);

    List<String> queryGradeTaskCodeListByUserCode(@Param("userCode") String userCode);

    List<SaaUserGradeTask> queryTaskListByUserCodeAndTaskCode(@Param("userCode") String userCode, @Param("taskCode") String taskCode);

    List<String> queryTaskCodeListByUserCode(@Param("userCode") String userCode);

    List<SaaUserPermitData> queryUserAllPermitData(@Param("userCode") String userCode, @Param("factorCode") String factorCode, @Param("userGradeIdSet") Set<Long> userGradeIdSet);

    List<SaaUserGrade> queryGradeByUserCodeAndGradeId(@Param("userCode") String userCode, @Param("gradeId") Long gradeId);

    SaaUserAmount queryUserAmoutByCodeAndRisk(@Param("userCode") String userCode, @Param("taskCode") String taskCode,
                                              @Param("classCode") String classCode, @Param("taskParentCode") String taskParentCode);

    List<SaaUserAmount> queryUserAmountByUndwrtPower(Map<String, String> map);

    /**
     *@Description:查询某个人是否具有某个功能权限并且具有某个数据权限
     *@param:map  userCode 用户代码 必填
     *            taskCode  权限代码 可填
     *            factorCode  权限因子 可填
     *            classCodeSet 数据权限代码  可填
     *@return:List<SaaUserGradeTask>
     */
    List<SaaUserGradeTask> queryGradeTaskByCondtion(Map<String, Object> map);

    List<Gguser> queryPageUserList(Map map);

    List<ExportUserPowerInfoVo> exportPowerUserInfo(Map<String, Object> param);

    /**
     *判断某个人的金额权限
     */
    List<SaaUserAmount> selectUserAmountPermit(Map<String, String> map);

    List<SaaTask> getSaaTaskBySystem(String systemCode, String id);

    List<SaaTask> getTaskListBySystem(String systemCode);

}
