package com.sinosoft.power.service;

import com.alibaba.fastjson.JSON;
//import com.github.pagehelper.Page;
//import com.github.pagehelper.PageHelper;
import com.sinosoft.power.common.exception.PowerException;
import com.sinosoft.power.common.util.CommonUtil;
import com.sinosoft.power.common.util.UserConstant;
import com.sinosoft.power.dao.SaaCompanyDao;
import com.sinosoft.power.dao.SaaUserDao;
import com.sinosoft.power.po.Gguser;
import com.sinosoft.power.po.SaaUserGrade;
import com.sinosoft.power.po.SaaUserPermitData;
import com.sinosoft.power.service.service.impl.SaaPowerDaoServiceImpl;
import com.sinosoft.power.util.PowerTaskCode;
import com.sinosoft.power.vo.DataTablePageVo;
import com.sinosoft.power.vo.SysPowerUserVo;
import com.sinosoft.power.vo.UserInParamVo;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: yancun
 * @create: 2018/12/18 14:38
 * @description:
 */

public class SaaUserService {

    private SqlSession session;

    private SaaUserDao saaUserDao;

    private SaaCompanyDao saaCompanyDao;

    private SaaPowerDaoService  saaPowerDaoService;

    public SaaUserService(SqlSession session){
        this.session  = session;
        saaUserDao =session.getMapper(SaaUserDao.class);
        saaCompanyDao =session.getMapper(SaaCompanyDao.class);
        saaPowerDaoService = new SaaPowerDaoServiceImpl(session);
    }

    /**
     * 方法描述：根据机构(包括子机构)下的用户
     * @param comCode
     * @return
     */
    public List<SysPowerUserVo> queryUserByComCode(String comCode){
        List<Gguser> users = saaUserDao.selectUserByComCode(comCode);
        List<SysPowerUserVo> sysUserVos = new ArrayList<>(users.size());
        for(Gguser user : users){
            SysPowerUserVo vo = new SysPowerUserVo();
            BeanUtils.copyProperties(user,vo);
            sysUserVos.add(vo);
        }
        return sysUserVos;
    }

    /**
     * 方法描述：根据机构(包括子机构)下所有的用户
     * @param comCode
     * @return
     */
    public List<SysPowerUserVo> querySecondUserByComCode(String comCode){
        List<Gguser> users = saaUserDao.selectSecondUserByComCode(comCode);
        List<SysPowerUserVo> sysUserVos = new ArrayList<>(users.size());
        for(Gguser user : users){
            SysPowerUserVo vo = new SysPowerUserVo();
            BeanUtils.copyProperties(user,vo);
            sysUserVos.add(vo);
        }
        return sysUserVos;
    }

    /**
     * 方法描述：获取机构下所有的用户
     * @param comCode
     * @return
     */
    public List<SysPowerUserVo> queryAllUserByComCode(String comCode){
        List<Gguser> users = saaUserDao.selectAllUserByComCode(comCode);
        List<SysPowerUserVo> sysUserVos = new ArrayList<>(users.size());
        for(Gguser user : users){
            SysPowerUserVo vo = new SysPowerUserVo();
            BeanUtils.copyProperties(user,vo);
            sysUserVos.add(vo);
        }
        return sysUserVos;
    }

    /**
     * 方法描述：根据机构具有某一权限的用户
     * @param comCode
     * @return
     */
    public List<SysPowerUserVo> querySecondComUser(String comCode,String taskCode){
        List<Gguser> users = saaUserDao.querySecondComUser(comCode,taskCode);
        List<SysPowerUserVo> sysUserVos = new ArrayList<>(users.size());
        for(Gguser user : users){
            SysPowerUserVo vo = new SysPowerUserVo();
            BeanUtils.copyProperties(user,vo);
            sysUserVos.add(vo);
        }
        return sysUserVos;
    }

    /**
     * 方法描述：获取机构下有某一对应任务权限的用户
     * @param map
     * @return
     */
    public List<SysPowerUserVo> selectPermissionComUser(Map map){
        List<Gguser> users = saaUserDao.selectPermissionComUser(map);
        List<SysPowerUserVo> sysUserVos = new ArrayList<>(users.size());
        for(Gguser user : users){
            SysPowerUserVo vo = new SysPowerUserVo();
            BeanUtils.copyProperties(user,vo);
            sysUserVos.add(vo);
        }
        return sysUserVos;
    }


    public SysPowerUserVo findUserByConditions(String userCode, String comCode) {
        return transData(saaUserDao.queryUserByConditions(userCode,comCode));
    }

    private SysPowerUserVo transData(Gguser Gguser){
        SysPowerUserVo vo = new SysPowerUserVo();
        if(Gguser != null) {
            BeanUtils.copyProperties(Gguser,vo);
        }
        return vo;
    }

    /**
     * 查询用户维护并分页
     * @param dataTablePageVo
     * @param queryparams
     * @return
     */
    /*public DataTablePageVo queryPageUserList(DataTablePageVo dataTablePageVo, String queryparams,String loginUserCode){
        Map<String,Object> param =new HashMap<>();
        SysPowerUserVo sysUser = new SysPowerUserVo();
        if(StringUtils.isNotBlank(queryparams)){
            sysUser = JSON.parseObject(queryparams, SysPowerUserVo.class);
        }
        if(StringUtils.isNotBlank(sysUser.getUserCode())){
            param.put(UserConstant.USER_USER_CODE,sysUser.getUserCode());
        }
        if(StringUtils.isNotBlank(sysUser.getUserName())){
            param.put(UserConstant.USER_USER_NAME,sysUser.getUserName());
        }
        if(StringUtils.isNotBlank(sysUser.getComCode())){
            param.put(UserConstant.USER_COMPANY_CODE,sysUser.getComCode());
        }
        if(StringUtils.isNotBlank(sysUser.getTaskId())){
            param.put(UserConstant.TASK_TASK_ID,sysUser.getTaskId());
        }
        if(!CommonUtil.isEmptyList(getComPermitData(loginUserCode))){
            param.put(UserConstant.COMPANY_PERMIT_DATA,getComPermitData(loginUserCode));
        }
        if(StringUtils.isNotBlank(sysUser.getGradeId())){
            param.put(UserConstant.USER_GRADE_ID, sysUser.getGradeId());
        }
        //PageHelper分页插件
        String orderBy = dataTablePageVo.getOrderField() == null ? UserConstant.USER_USER_CODE: dataTablePageVo.getOrderField();
        if(dataTablePageVo.getOrder() != null){
            orderBy += " "  + dataTablePageVo.getOrder();
        }
        int pageNum = dataTablePageVo.getDataTablePageStart();
        int pageSize = dataTablePageVo.getDataTablePageSize();
        PageHelper.offsetPage(pageNum,pageSize);
        PageHelper.orderBy(orderBy);
        List<Gguser> users = saaUserDao.queryPageUserList(param);
        //获取总记录数
        long totalNum = ((Page)users).getTotal();
        List<SysPowerUserVo> sysPowerUserVos = new ArrayList<>(users.size());
        for(Gguser user : users){
            SysPowerUserVo vo = new SysPowerUserVo();
            vo.setUserCode(user.getUserCode());
            vo.setUserName(user.getUserCname());
            vo.setMakeCom(user.getCompanyCode());
            vo.setValidStatus(user.getValidInd());
            sysPowerUserVos.add(vo);
        }
        dataTablePageVo.setData(sysPowerUserVos);
        dataTablePageVo.setITotalDisplayRecords(totalNum);
        dataTablePageVo.setITotalRecords(totalNum);
        return dataTablePageVo;
    }*/


    /**
     * @description获取当前登录用户的机构权限
     *
     */
    public  List<SaaUserPermitData> getComPermitData(String loginUserCode){
        if(StringUtils.equals(loginUserCode,"0000000000")){
            List<SaaUserPermitData> permitDataList = new ArrayList<>();
            SaaUserPermitData permitData = new SaaUserPermitData();
            permitData.setDataValue1("31000000");
            permitDataList.add(permitData);
            return permitDataList;
        }
        Map<String,String> map = new HashMap<>();
        map.put("userCode",loginUserCode);
        map.put("taskCode", PowerTaskCode.DHLP_MENU_SYSTEM);
        map.put("validStatus","1");
        map.put("factorcode","COM");
       return  saaPowerDaoService.queryUserPermitDataByCondition(map);
    }

    /**
     * 方法描述：获取全量的用户
     * */
    public List<SysPowerUserVo> queryAllUserList(){
        List<Gguser> users = saaUserDao.selectAll();
        List<SysPowerUserVo> sysPowerUserVos = new ArrayList<>(users.size());
        for(Gguser user : users){
            SysPowerUserVo vo = new SysPowerUserVo();
            vo.setUserCode(user.getUserCode());
            vo.setUserName(user.getUserCname());
            vo.setMakeCom(user.getCompanyCode());
            sysPowerUserVos.add(vo);
        }
        return sysPowerUserVos;
    }

    /**
     * @Description:根据用户代码查询用户信息
     * @param userCode
     * @return: userVo
     */
    public SysPowerUserVo findUserByUserCode(String userCode) {
        Gguser user =saaUserDao.queryUserByUserCode(userCode);
        return transData(user);
    }

    /**
     * @description查询用户个人信息
     * <AUTHOR>
     */
    public Gguser querySingleUserByComCode(String userCode) {
        Gguser user =saaUserDao.queryUserByUserCode(userCode);
        return user;
    }

    /**
     * @Description 查询用户维护并分页
     * <AUTHOR>
     * @param dataTablePageVo
     * @param queryparams
     * @return
     */
    /*public DataTablePageVo queryPagesUserList(DataTablePageVo dataTablePageVo,String queryparams,String loginUserCode){
        Map<String,Object> param =new HashMap<>();
        UserInParamVo paramVo = new UserInParamVo();
        if(StringUtils.isNotBlank(queryparams)){
            paramVo = JSON.parseObject(queryparams, UserInParamVo.class);
        }
        if(StringUtils.isNotBlank(paramVo.getUserCode())){
            param.put(UserConstant.USER_USER_CODE,paramVo.getUserCode());
        }
        if(StringUtils.isNotBlank(paramVo.getUserName())){
            param.put(UserConstant.USER_USER_NAME,paramVo.getUserName());
        }
        if(StringUtils.isNotBlank(paramVo.getComCode())){
            param.put(UserConstant.USER_COMPANY_CODE,paramVo.getComCode());
        }
        if(StringUtils.isNotBlank(paramVo.getValidStatus())){
            param.put(UserConstant.USER_validStatus,paramVo.getValidStatus());
        }
        if(StringUtils.isNotBlank(paramVo.getTaskId())){
            param.put(UserConstant.TASK_TASK_ID,paramVo.getTaskId());
        }
        if(!CommonUtil.isEmptyList(getComPermitData(loginUserCode))){
            param.put(UserConstant.COMPANY_PERMIT_DATA,getComPermitData(loginUserCode));
        }
        if(StringUtils.isNotBlank(paramVo.getGradeId())){
            param.put(UserConstant.USER_GRADE_ID, paramVo.getGradeId());
        }
        //PageHelper分页插件
        String orderBy = dataTablePageVo.getOrderField() == null ? "userCode": dataTablePageVo.getOrderField();
        if(dataTablePageVo.getOrder() != null){
            orderBy += " "  + dataTablePageVo.getOrder();
        }
        int pageNum = dataTablePageVo.getDataTablePageStart();
        int pageSize = dataTablePageVo.getDataTablePageSize();
        PageHelper.offsetPage(pageNum,pageSize);
        PageHelper.orderBy(orderBy);

        List<Gguser> users = saaUserDao.queryPageUserList(param);
        if(users==null){
            throw new PowerException("740033", true);
        }
        //获取总记录数
        long totalNum = ((Page)users).getTotal();
        List<UserInParamVo> UserVos = new ArrayList<>(users.size());
        for(Gguser user : users){
            UserInParamVo vo = new UserInParamVo();
            vo.setUserCode(user.getUserCode());
            vo.setUserName(user.getUserCname());
            vo.setComCode(user.getCompanyCode());
            vo.setValidStatus(user.getValidInd());
            UserVos.add(vo);
        }
        dataTablePageVo.setData(UserVos);
        dataTablePageVo.setITotalDisplayRecords(totalNum);
        dataTablePageVo.setITotalRecords(totalNum);
        return dataTablePageVo;
    }*/

    /**
     * @description更新数据库数据
     * <AUTHOR>
     */
    public void updateMessage(Gguser vo) {
        saaUserDao.updateSelectiveByPrimaryKey(vo);
    }

    /**
     * @Description:查找固定条件的人
     * @param:
     * @return:
     */
    public List<Gguser> queryUserByParam(String userCode, String taskCode){
        List<String> sqlParam = this.queryComCodeByUserCodeAndGradId(userCode,taskCode);
        List<Gguser> userLists = saaUserDao.queryUserByParam(sqlParam);
        return userLists;
    }


    public List<String> queryComCodeByUserCodeAndGradId(String userCode,String taskCode){
        List<SaaUserPermitData> userPermitDataList = new ArrayList<>();
        Map<String,String> gradeParam = new HashMap<>();
        gradeParam.put("userCode",userCode);
        gradeParam.put("taskCode",taskCode);
        List<SaaUserGrade> userGradeList = saaPowerDaoService.findUserGradeByUserCodeAndTaskCode(gradeParam);
        if(userGradeList != null && userGradeList.size()>0){
            for (SaaUserGrade saaUserGrade : userGradeList){
                Integer Gradeid = saaUserGrade.getId().intValue();
                userPermitDataList.addAll(saaPowerDaoService.selectAllByUserGradeId(Gradeid.toString()));
            }
            if(userPermitDataList == null || userPermitDataList.size()==0){
                userPermitDataList = saaPowerDaoService.selectUserPermitComByUserCode(userCode);
            }
        }else{
            userPermitDataList = saaPowerDaoService.selectUserPermitComByUserCode(userCode);
        }
        List<String> list = new ArrayList<>();
        for(SaaUserPermitData saaUserPermitData : userPermitDataList){
            list.add(saaUserPermitData.getDataValue1());
        }
        return list;
    }

    /**
     * 查询具有某功能权限并且有某机构权限的所有人
     * @param makeCom 非必传 机构权限
     * @param taskCode 非必传 功能权限
     */
    public List<SysPowerUserVo> queryComCondtionPowerUserList(String makeCom,String taskCode){
        List<SysPowerUserVo> powerUserVoList = new ArrayList<>();
        List<Gguser> userList = saaUserDao.queryComCondtionPowerUserList(makeCom,taskCode);
        for(Gguser user : userList){
            SysPowerUserVo vo = new SysPowerUserVo();
            vo.setUserCode(user.getUserCode());
            vo.setUserName(user.getUserCname());
            vo.setMakeCom(user.getCompanyCode());
            powerUserVoList.add(vo);
        }
       return powerUserVoList;
    }

}
