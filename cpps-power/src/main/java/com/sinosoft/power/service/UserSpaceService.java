package com.sinosoft.power.service;


import com.sinosoft.power.dao.SaaUserShortcutDao;
import com.sinosoft.power.po.UserShortcut;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;



public class UserSpaceService {
    private SqlSession session;
	private SaaUserShortcutDao userShortcutDao;

	public UserSpaceService(SqlSession session){
       this.session = session;
		userShortcutDao = session.getMapper(SaaUserShortcutDao.class);
	}

	/**
	 * 方法描述：更新用户快捷功能菜单
	 * */
	public String updateUserShortcutList(String userCode,String listStr){
		UserShortcut userShortcut = userShortcutDao.selectByPrimaryKey(userCode);
		if(userShortcut==null) {
			UserShortcut userShortcutTemp = new UserShortcut();
			userShortcutTemp.setUserCode(userCode);
			userShortcutTemp.setTaskList(listStr);
			userShortcutDao.insertSelective(userShortcutTemp);
		}else{
			userShortcut.setTaskList(listStr);
			userShortcutDao.updateSelectiveByPrimaryKey(userShortcut);
		}
		return listStr;
	}
	

}
