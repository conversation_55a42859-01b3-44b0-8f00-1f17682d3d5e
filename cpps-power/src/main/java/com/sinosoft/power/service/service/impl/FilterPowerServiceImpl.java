package com.sinosoft.power.service.service.impl;

import com.sinosoft.power.redis.RedisUtil;
import com.sinosoft.power.redis.SaaRedisService;
import com.sinosoft.power.service.FilterPowerService;
import com.sinosoft.power.service.PowerService;
import com.sinosoft.power.util.CNPowerTaskCode;
import com.sinosoft.power.util.PowerTaskCode;
import com.sinosoft.power.vo.MenulistVo;
import com.sinosoft.power.vo.PowerKeyWord;
import com.sinosoft.power.vo.RequestKeyWordVo;
import com.sinosoft.power.vo.SysPowerUserVo;
import org.apache.ibatis.session.SqlSession;

import java.util.*;

import static com.sinosoft.power.common.util.RedisKeyUtil.USER_KEY_MENU_LIST;

/**
 * 功能描述:权限过滤服务类
 *
 * <AUTHOR>
 * @date: 2018/12/24 8:56
 */
public class FilterPowerServiceImpl implements FilterPowerService {


    private SqlSession session;

    private PowerService powerService;

    public FilterPowerServiceImpl(SqlSession session) {
        this.session = session;
        powerService = new PowerServiceImpl(session);
    }


    /***************************↓↓↓↓↓↓↓↓↓   对外公开    ↓↓↓↓↓↓↓↓↓********************************/



    /**
     * 方法描述：获取某机构下的拥有某节点权限的所有人员列表
     *
     * @param params 参数
     * @return
     * <AUTHOR> 20170802
     */
    @Override
    public List<SysPowerUserVo> queryUserListByParams(Map<String, String> params, String currentUserCode) {
        if (null == params || params.isEmpty()) {
            return powerService.queryAllUserList();
        }
        //组织powerKeyWord数据
        PowerKeyWord powerKeyWord = new PowerKeyWord();
        powerKeyWord.setComCode(params.get("companyCode"));
        powerKeyWord.setMakeCom(params.get("companyCode"));
        powerKeyWord.setRiskCode(params.get("riskCode"));
        powerKeyWord.setTaskCode(params.get("taskCode"));
        powerKeyWord.setPlateCode(params.get("plateCode"));
        powerKeyWord.setClassCode(params.get("classCode"));
        return powerService.queryPowerUserList(powerKeyWord, currentUserCode);


    }

    /**
     * 方法描述：初始化人员功能权限列表
     *
     * @param userCode
     * @return
     * <AUTHOR> 20170801
     */
    @Override
    public Set<String> gainUserTaskCodes(String userCode) {
        return new HashSet<>(powerService.queryTaskCodeListByUser(userCode));
    }

    /**
     * 获取用户岗位功能权限
     *
     * @param userCode
     * @return
     */
    public Set<String> gainUserGradeTaskCodeSet(String userCode) {
        Set<String> userTaskCodes = new HashSet<>();
        List<String> taskCodeList = powerService.queryGradeTaskCodeListByUser(userCode);
        for (String taskCode : taskCodeList) {
            userTaskCodes.add(taskCode);
        }
        return userTaskCodes;
    }

    /**
     * 方法描述：初始化人员菜单列表
     *
     * @param userCode
     * @return
     * <AUTHOR> 20170808
     */
    @Override
    public Set<String> gainPowerMenuByTaskCodes(String userCode) {
        Set<String> userMenus = new HashSet<>();
        SaaRedisService redisService = SaaRedisService.getInstance();
        String taskCodeList = redisService.getUserDataByKey(userCode, USER_KEY_MENU_LIST);
        if (taskCodeList != null) {
            userMenus = RedisUtil.string2Set(taskCodeList);
            return userMenus;
        }

        //菜单功能对应
        Map<String, Set<String>> allMenuMapperTaskCode = this.gainMenuPowerMapper();
        //查询UserGradeTask表
        Set<String> userTaskCodes = this.gainUserGradeTaskCodeSet(userCode);
        for (String taskCode : userTaskCodes) {
            if (allMenuMapperTaskCode.containsKey(taskCode)) {
                userMenus.addAll(allMenuMapperTaskCode.get(taskCode));
            }
        }
        Set<String> commonMenu = new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("我的任务");
                add("我的关注");
                add("我的提醒");
                add("单证收集");
                add("任务池");
            }
        };
        userMenus.addAll(commonMenu);
        String menuListValue = RedisUtil.collection2String(userMenus);
        redisService.setUserDataByTypeAndValue(userCode, USER_KEY_MENU_LIST, menuListValue);
        return userMenus;

    }

    /**
     * 功能描述:获取所有的菜单
     *
     * @date: 2018/12/20 19:39
     * @param:
     * @return:
     */
    @Override
    public List<MenulistVo> gainAllMenulist() {
        List<MenulistVo> menulistVoList = new ArrayList<MenulistVo>() {
            private static final long serialVersionUID = 1L;

            {
                add(new MenulistVo("任务中心", "", "", "", "", new ArrayList<MenulistVo>() {
                    private static final long serialVersionUID = 1L;

                    {
                        add(new MenulistVo("我的任务", "ic_myTask.png", "ic_woderenwu.png", "main.html#/common/mytask", "", null));
//                        add(new MenulistVo("委托任务", "ic_entrustmentTask.png","ic_weiturenwu.png",  "main.html#/common/myConsigner", "", null));
                        add(new MenulistVo("我的关注", "ic_caseFollow.png", "ic_wodeguanzhu.png", "main.html#/common/mytaskheart", "", null));
//                        						add(new MenulistVo("流程查询","fa-sitemap","main.html",null));
//                        add(new MenulistVo("平级移交", "ic_levelTransfer.png","ic_pingjiyijiao.png","main.html#/common/case_transfer", "", null)) ;
                        add(new MenulistVo("我的提醒", "ic_myReminding.png", "ic_wodetixing.png", "main.html#/home/<USER>", "", null));
//                        						add(new MenulistVo("并案任务","fa-tasks","main.html#/compensate_merge/task_search",null));
//                        add(new MenulistVo("报案查询", "ic_reportInquiries.png ","ic_baoanchaxun.png","main.html#/common/registSearch", "", null));
                        add(new MenulistVo("任务池", "ic_taskTool.png", "ic_renwuchi.png", "main.html#/common/taskPoolSearch", "", null));
                        add(new MenulistVo("任务查询", "ic_taskSearch.png", "ic_renwuchaxun.png", "main.html#/common/task_search", "", null));
                        add(new MenulistVo("强制改派", "ic_taskSearch.png", "ic_renwuchaxun.png", "main.html#/schedul/schedul_redistribute_compel", "", null));
                        add(new MenulistVo("任务监控", "ic_renwujiankong4.png", "ic_renwujiankong.png", "main.html#/taskMonitor/taskMonitor_main", "", null));


                    }
                }));

                add(new MenulistVo("案件中心", "", "", "", "", new ArrayList<MenulistVo>() {
                    private static final long serialVersionUID = 1L;

                    {
                        add(new MenulistVo("案件查询", "ic_caseQuery.png", "ic_anjianchaxun.png", "main.html#/common/case_search", "", null));
                      /*
                      注释 by zuoyanhua 20190108 一期上线 删除
                      add(new MenulistVo("报案登记", "ic_registrationCases.png", "ic_baoandengji.png", "main.html#/regist/policy/policy_search", "", null));
                      */
//                        add(new MenulistVo("退票处理", "fa-pencil", "", "main.html", "", null));
                        //						add(new MenulistVo("赔案监控","fa-eye","main.html",null));
//                        add(new MenulistVo("强制立案提醒", "ic_mandatoryFilingReminder.png", "ic_qiangzhiliantixing.png","main.html#/aml/claimForcePrompt", "", null));
//                        add(new MenulistVo("收回赔款申请","ic_claimForCompensation.png","ic_shouhuipeikuanshenqing.png", "main.html#/compensatepayback/search", "", null));
//                        add(new MenulistVo("巨灾补录", "ic_catastropheSupplement.png", "ic_juzaibulu.png",  "main.html#/disasterModify/disaster_query", "", null));//新增--zhanghui20170830
                        //						add(new MenulistVo("赔案监控","fa-search","main.html#/casemonitor/monitor_query",null));//新增--zhanghui20170901
//                        add(new MenulistVo("重开赔案申请", "fa-search", "", "main.html#/common/case_search", "", null));//liuwenyan20170905
                        //						add(new MenulistVo("保单查询","fa-search","main.html#/policy/policy_search",null));//新增保单查询
                        add(new MenulistVo("批量导入", "ic_batchImport.png", "ic_piliangdaoru.png", "main.html#/batch/batch_query", "", null));
                        add(new MenulistVo("批次查询", "ic_batchQuery.png", "ic_picichaxun.png", "main.html#/batch/batchNo_search", "", null));
//                        add(new MenulistVo("结算单支付", "ic_settlementPayment.png", "ic_jiesuandanzhifu.png", "main.html#/statement/search", "<i data-toggle='tooltip' title='结算单支付仅对于第三方机构委托所产生的费用计算书，按照约定时间段进行打包支付' style='color: #00A2E9;' class='fa  fa-exclamation-circle'></i>", null));
//                        add(new MenulistVo("清单查询", "ic_listQuery.png","ic_qingdanchaxun.png",  "main.html#/listing/listing_query", "", null));
                        add(new MenulistVo("单证收集", "ic_collectionDocuments.png", "ic_danzhengshouji.png", "main.html#/certifylist/certify_list_query", "", null));
                    }
                }));

                add(new MenulistVo("报表单证", "", "", "", "", new ArrayList<MenulistVo>() {
                    private static final long serialVersionUID = 1L;

                    {
                        add(new MenulistVo("单证打印", "ic_printingSurveyReport.png", "ic_chakanbaogaodayin.png", "main.html#/printcertify/printcertify", "", null));
                        //add(new MenulistVo("计算书打印","ic_calculationBookPrinting.png","ic_jisuanshudayin.png","main.html#/printcertify/printcertify?printType=compensateReport", "", null));
                        //						add(new MenulistVo("关键环节打印","fa-file","main.html#/printcertify/printcertify?printType=keyProcessReport",null));
                        //						add(new MenulistVo("理赔报告打印", "ic_claimReportPrinting","ic_lipeibaogaodayin","main.html#/printcertify/printcertify?printType=claimReport",null);
                        //						add(new MenulistVo("定损单打印","fa-file","main.html#/printcertify/printcertify?printType=dlossReport",null));
                        //						add(new MenulistVo("分摊赔款通知书打印","fa-file","main.html#/printcertify/printcertify?printType=reparationsnoteReport",null));
                        //						add(new MenulistVo("公估委托打印","fa-file","main.html#/printcertify/printcertify?printType=agencyinfoReport",null));
                        //						add(new MenulistVo("公估申请表打印","fa-file","main.html#/printcertify/printcertify?printType=agecnyapplicationReport",null));
                        //						add(new MenulistVo("赔款收据打印","fa-file","main.html#/printcertify/printcertify?printType=paymentReceiptReport",null));
                        //add(new MenulistVo("拒赔通知书打印", "ic_claimsRejectedPtint.png", "ic_jupeitongzhi.png", "main.html#/printcertify/printcertify?printType=refusePayReport", "", null));
                        //add(new MenulistVo("结案报告打印", "ic_closingReport Printing.png", "ic_jieanbaogao.png", "main.html#/printcertify/printcertify?printType=endCaseReport", "", null));
                    }
                }));
                add(new MenulistVo("系统维护", "", "", "", "", new ArrayList<MenulistVo>() {
                    private static final long serialVersionUID = 1L;

                    {
                        add(new MenulistVo("用户信息维护", "ic_yonghuxinxiwei.png", "ic_yonghuxinxiweihu.png", "main.html#/saa/user_init", "", null));
                        add(new MenulistVo("个性化权限配置", "ic_persPermisConfig.png", "ic_gexinghuaquanxian.png", "main.html#/saa/saa_edit", "", null));
                        add(new MenulistVo("个性化岗位配置", "ic_persJobPlacement.png", "ic_gexinghuagangwei.png", "main.html#/saa/saa_grade_edit", "", null));
//                        add(new MenulistVo("参数配置定义", "ic_parameterConfigurationDefinition.png","ic_canshupeizhidingyi.png","main.html#/config/configdefine_query", "", null));
//                        add(new MenulistVo("第三方机构维护","ic_thirdPartyMaintenance.png", "ic_disanfangjigouweihu.png","main.html#/agencydirectory/search", "", null));
                        //						add(new MenulistVo("短信模板维护", "ic_smsTemplateMaintenance","ic_duanxinmobanweihu","main.html#/smstemplate/search",null))
//                        add(new MenulistVo("参数配置取值", "ic_parameterConfigurationValue.png","ic_canshupeizhiquzhi.png","main.html#/config/configvalue_query", "", null));
//					    add(new MenulistVo("常用收款人维护", "ic_commonPayeeMaintenance.png","ic_changyongshoukuanrenweihu.png","main.html#/paycustom/search", "", null));
//                        add(new MenulistVo("案均金额维护", "ic_averageAmountMaintenance.png", "ic_anjunjineweihu.png","main.html#/claimavgset/upload", "", null));

				/*		施催海修改：
						修改前		add(new MenulistVo("代码缓存清空", "ic_codeCacheEmpty.png","ic_daimahuancunqingkong.png","clearMemorySuccess", "", null));
						*/
                        add(new MenulistVo("代码缓存清空", "ic_codeCacheEmpty.png", "ic_daimahuancunqingkong.png", "trash", "", null));

//                        add(new MenulistVo("医院维护", "ic_hospitalMaintenance.png", "ic_yiyuanweihu.png", "main.html#/hospital/hospital_query", "", null));
//                        add(new MenulistVo("简易赔案险种配置", "ic_simClaimInsurConfig.png","ic_jianyipeian.png",  "main.html#/simplecase/simplecase_config", "", null));
//                        add(new MenulistVo("知识库分类维护", "ic_ClassifyMainteknowlBase.png","ic_zhishikufenlei.png",  "main.html#/knowledgebase/knowledge_classify_query", "", null));
//                        add(new MenulistVo("合议人员维护", "ic_maintenColleStaff.png ", "ic_heyirenyuan.png", "main.html#/collegiate/search", "", null));
//                        add(new MenulistVo("黑名单维护", "ic_blacklistMaintenance.png", "ic_heimingdanweihu.png", "main.html#/configmanagement/blacklist_maintenance", "", null));
                        //						add(new MenulistVo("消保数据传送","fa-cog","main.html#/cnsumerProtection/query",null));
                        add(new MenulistVo("数据刷新", "ic_shujushuaxin4.png", "ic_shujushuaxin.png", "dataUpdate", "", null));
                        add(new MenulistVo("数据初始化", "ic_shujushushihua4.png", "ic_shujushushihua.png", "dataInit", "", null));
                        add(new MenulistVo("巨灾代码维护", "ic_catastroCodeMaint.png","ic_juzaidaima.png",  "main.html#/catastropheManagement/catastropheCaseInquiry", "", null));
//						add(new MenulistVo("时效配置", "ic_persPermisConfig.png","ic_gexinghuaquanxian.png",  "main.html#/timeliness/timeliness_edit", "", null));
//						add(new MenulistVo("时效查询", "ic_persPermisConfig.png","ic_gexinghuaquanxian.png",  "main.html#/timeliness/timeliness_query", "", null));
                    }
                }));
                add(new MenulistVo("其他", "", "", "", "", new ArrayList<MenulistVo>() {
                    private static final long serialVersionUID = 1L;

                    {
//                        add(new MenulistVo("知识库", "ic_knowledgeBase.png", "ic_zhishiku.png", "main.html#/knowledgebase/knowledge_base_query", "", null));

					/*	施催海修改
					原 add(new MenulistVo("消息发布", "ic_messageRelease.png", "ic_xiaoxifabu.png", "createmessage", "", null));
					 改为
                 	add(new MenulistVo("消息发布", "ic_messageRelease.png", "ic_xiaoxifabu.png", "envelope", "", null)); */

                        //add(new MenulistVo("消息发布", "ic_messageRelease.png", "ic_xiaoxifabu.png", "envelope", "", null));
//                        add(new MenulistVo("文档下载", "ic_documentDownload.png", "ic_wendangxiazai.png", "main.html#/filedownload/filedownload_main", "", null));
//                        add(new MenulistVo("排班管理", "ic_workforceManagement.png", "ic_paibanguanli.png", "main.html#/arrangeduty/arrangeduty_query", "", null));
                    }
                }));
            }
        };
        return menulistVoList;
    }

    /**
     * 方法描述：菜单和功能代码的映射关系
     */
    private Map<String, Set<String>> gainMenuPowerMapper() {
        Map<String, Set<String>> allMenuMapperTaskCode = new HashMap<>();
        allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_PAJK, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("赔案监控");
            }
        });
//		allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_PBGL,new HashSet<String>(){
//			private static final long serialVersionUID = 1L;
//			{
//				add("排班管理");
//			}
//		});
//		allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_PJYJ,new HashSet<String>(){
//			private static final long serialVersionUID = 1L;
//			{
//				add("平级移交");
//			}
//		});
//		allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_SPJYJ,new HashSet<String>(){
//			private static final long serialVersionUID = 1L;
//			{
//				add("平级移交");
//			}
//		});
//		allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_JZBL,new HashSet<String>(){
//			private static final long serialVersionUID = 1L;
//			{
//				add("巨灾补录");
//			}
//		});
//		allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_XXFB,new HashSet<String>(){
//			private static final long serialVersionUID = 1L;
//			{
//				add("消息发布");
//			}
//		});
//		allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_ZSK,new HashSet<String>(){
//			private static final long serialVersionUID = 1L;
//			{
//				add("知识库");
//			}
//		});
        allMenuMapperTaskCode.put(PowerTaskCode.DHLP_REGIST_OPERATE, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
              /*
               注释 by zuoyanhua 20190108 一期上线 删除
              add("报案登记");
              */
                add("报案查询");
            }
        });

        allMenuMapperTaskCode.put(CNPowerTaskCode.CLAIMMANAGE_CASECENTER_CASEREGIST, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                /*
                 注释 by zuoyanhua 20190108 一期上线 删除
                add("报案登记");
                */
            }
        });

        allMenuMapperTaskCode.put(PowerTaskCode.DHLP_CASEMAIN_OPERATE, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("强制立案提醒");
                add("并案任务");
            }
        });
        allMenuMapperTaskCode.put(PowerTaskCode.DHLP_COMPENSATE_OPERATE, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("并案任务");
            }
        });
//		allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_HNMEDICAL,new HashSet<String>(){
//			private static final long serialVersionUID = 1L;
//			{
//				add("海南医疗自动化");
//			}
//		});
        allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_SYSTEM, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
//				add("短信模板维护");
//				add("知识库分类维护");
//				add("参数配置定义");
//				add("参数配置取值");
//				add("第三方机构维护");
//				add("合议人员维护");
//				add("常用收款人维护");
//				add("案均金额维护");
                add("用户信息维护");
                add("代码缓存清空");
				add("巨灾代码维护");
                add("个性化权限配置");
                add("个性化岗位配置");
//				add("简易赔案险种配置");
//				add("医院维护");
//				add("保单查询");
//				add("消保数据传送");
//				add("黑名单维护");


                add("数据刷新");
                add("数据初始化");

//				add("时效配置");
//				add("时效查询");
                add("任务监控");

            }
        });
//		allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_SHPKSQ,new HashSet<String>(){
//			private static final long serialVersionUID = 1L;
//			{
//				add("收回赔款申请");
//			}
//		});
        allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_CERTIFY, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("单证打印");
//				add("计算书打印");
//				add("关键环节打印");
//				add("理赔报告打印");
//				add("定损单打印");
//				add("公估委托打印");
//				add("公估申请表打印");
//				add("赔款收据打印");
//				add("分摊赔款通知书打印");
//				add("拒赔通知书打印");
//				add("结案报告打印");
            }
        });
        allMenuMapperTaskCode.put(CNPowerTaskCode.ASSIST_CERTIFYPRINT_HANDLE, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("单证打印");
            }
        });

        allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_BATCH, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("批量导入");
                add("批次查询");
            }
        });

        allMenuMapperTaskCode.put(CNPowerTaskCode.CLAIMMANAGE_CASECENTER_BATCHIMPORT, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("批量导入");

            }
        });

        allMenuMapperTaskCode.put(CNPowerTaskCode.CLAIMMANAGE_CASECENTER_BATCHNOQUERY, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("批次查询");

            }
        });


        allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_CLAIMLIST, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("清单查询");
            }
        });
//		allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_STATEMENT,new HashSet<String>(){
//			private static final long serialVersionUID = 1L;
//			{
//				add("结算单支付");
//			}
//		});
        allMenuMapperTaskCode.put(PowerTaskCode.DHLP_MENU_DOCCOLLECT, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("单证收集");
            }
        });
        allMenuMapperTaskCode.put(CNPowerTaskCode.ASSIST_DOCCOLLECT_HANDLE, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("单证收集");
            }
        });

        allMenuMapperTaskCode.put(PowerTaskCode.CLAIM_MENU_FORCEASSIGN, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("强制改派");
            }
        });
        allMenuMapperTaskCode.put(CNPowerTaskCode.CLAIMMANAGE_TASKCENTER_FORCEASSIGN, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("强制改派");
            }
        });

        allMenuMapperTaskCode.put(PowerTaskCode.CLAIM_MENU_TASKQUERY, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("任务查询");
            }
        });
        allMenuMapperTaskCode.put(CNPowerTaskCode.CLAIMMANAGE_TASKCENTER_TASKQUERY, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("任务查询");
            }
        });

        allMenuMapperTaskCode.put(PowerTaskCode.CLAIM_MENU_CASEQUERY, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("案件查询");
            }
        });

        allMenuMapperTaskCode.put(CNPowerTaskCode.CLAIMMANAGE_CASECENTER_REGISTQUERY, new HashSet<String>() {
            private static final long serialVersionUID = 1L;

            {
                add("案件查询");
            }
        });


        return allMenuMapperTaskCode;
    }

    /**
     * 方法描述：权限校验url和获取参数的映射关系
     */
    @Override
    public Map<String, RequestKeyWordVo> gainRegistNoMapper() {
        return new HashMap<String, RequestKeyWordVo>() {
            private static final long serialVersionUID = 1L;

            {
                //报案
                Map<String, Map<String, String>> taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_REGIST_OPERATE, null);
                put("/regist/initRegist", new RequestKeyWordVo("registNo", "taskId"));
                put("/regist/saveRegist", new RequestKeyWordVo("registNo", "taskId", taskCodeMap));
                put("/regist/submitRegist", new RequestKeyWordVo("registNo", "taskId", taskCodeMap));

                //调度
                taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_SCHEDUL_OPERATE, null);
                put("/schedule/initSchedule", new RequestKeyWordVo("registNo", "taskId"));
                put("/schedule/saveSchedule", new RequestKeyWordVo("registNo", "taskId", taskCodeMap, true));

                //查勘
                taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_CHECK_OPERATE, null);
                put("/check/initCheckPropLoss", new RequestKeyWordVo("registNo", "taskId"));
                put("/check/saveCheckPropLoss", new RequestKeyWordVo("registNo", "taskId", taskCodeMap, true));
                put("/check/submitCheckPropLoss", new RequestKeyWordVo("registNo", "taskId", taskCodeMap, true));

                //立案 功能代码是案件处理
                taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_CASEMAIN_OPERATE, null);
                put("/aml/initClaim", new RequestKeyWordVo("registno", "taskId"));
                put("/aml/saveClaim", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));
                put("/aml/submitClaim", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));

                //理算
                taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_COMPENSATE_OPERATE, null);
                put("/compensate/initCompensate", new RequestKeyWordVo("registno", "taskId"));
                put("/compensate/saveCompensate", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));
                put("/compensate/submitCompensate", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));

                //人伤
                taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_DLOSSPERS_OPERATE, null);
                put("/dlosspers/initInjured", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));
                put("/dlosspers/saveInjured", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));

                //预赔 功能代码是案件处理
                taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_CASEMAIN_OPERATE, null);
                put("/prepay/init", new RequestKeyWordVo("registno", "taskId"));
                put("/prepay/save", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));
                put("/prepay/submit", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));

                //重开赔案 功能代码是案件处理
                taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_CASEMAIN_OPERATE, null);
                put("/reclaim/initReclaim", new RequestKeyWordVo("registno", "taskId"));
                put("/reclaim/saveReclaim", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));
                put("/reclaim/submitReclaim", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));

                //追偿
                taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_REPLEVY_OPERATE, null);
                put("/replevy/initReplevy", new RequestKeyWordVo("registno", "taskId"));
                put("/replevy/saveReplevy", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));
                put("/replevy/submitReplevy", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));

                //退票
                taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_RETURNTICKET_OPERATE, null);
                put("/returnticket/initReturn", new RequestKeyWordVo("registno", "taskId"));
                put("/returnticket/saveNewPayee", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));
                put("/returnticket/submitNewPayee", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));

                //调查
                taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_SURVEY_OPERATE, null);
                put("/surveyApi/initSurveyApply", new RequestKeyWordVo("registno", "taskId"));
                put("/surveyApi/saveSurveyApplyDeal", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));
                put("/surveyApi/submitSurveyApply", new RequestKeyWordVo("registno", "taskId"));

                //调查审核
                taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_SURVEYAUDIT_OPERATE, null);
                put("/surveyApi/initSurveyAudit", new RequestKeyWordVo("registno", "taskId"));
                put("/surveyApi/saveSurveyAudit", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));
                put("/surveyApi/submitSurveyAudit", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));

                //核赔
                taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_UNDWRT_ONE_OPERATE, null);
                taskCodeMap.put(PowerTaskCode.DHLP_UNDWRT_TWO_OPERATE, null);
                taskCodeMap.put(PowerTaskCode.DHLP_UNDWRT_THREE_OPERATE, null);
                taskCodeMap.put(PowerTaskCode.DHLP_UNDWRT_FOUR_OPERATE, null);
                put("/undwrt/init", new RequestKeyWordVo("registno", "taskId"));
                put("/undwrt/save", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));
                put("/undwrt/submit", new RequestKeyWordVo("registno", "taskId", taskCodeMap, true));

                //结案
                taskCodeMap = new HashMap<String, Map<String, String>>();
                taskCodeMap.put(PowerTaskCode.DHLP_ENDCASE_OPERATE, null);
                put("/endcase/init", new RequestKeyWordVo("registno", "taskId"));
                put("/endcase/endCase", new RequestKeyWordVo("registno", "taskId"));

                //高级审核
//				taskCodeMap = new HashMap<String,Map<String,String>>();
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_ONE_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_TWO_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_THREE_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_FOUR_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_FIVE_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_SIX_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_SEVEN_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_EIGHT_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_NINE_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_TEN_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_ELEVEN_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_TWELVE_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_THIRTEEN_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_FOURTEEN_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_FIFTEEN_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_SIXTEEN_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_SEVENTEEN_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_EIGHTEEN_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_NINETEEN_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_TWENTY_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_TWENTYONE_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_TWENTYTWO_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_TWENTYTHREE_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_TWENTYFOUR_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_APPROVE_TWENTYFIVE_OPERATE, null);
//				taskCodeMap.put(PowerTaskCode.DHLP_TASKMOVE_APPROVE_OPERATE, null);
//				put("/approve/initApprovePageForPub",new RequestKeyWordVo("registno","taskId"));
//				put("/approve/initApprovePageForSpec",new RequestKeyWordVo("registno","taskId"));
//				put("/approve/saveApproveInfo",new RequestKeyWordVo("registno","taskId",taskCodeMap,true));
//				put("/approve/submitApproveInfo",new RequestKeyWordVo("registno","taskId",taskCodeMap,true));

            }
        };
    }

}
