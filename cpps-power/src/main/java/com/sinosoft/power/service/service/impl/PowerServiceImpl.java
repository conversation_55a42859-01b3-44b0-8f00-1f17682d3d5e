package com.sinosoft.power.service.service.impl;

import com.alibaba.fastjson.JSON;
//import com.github.pagehelper.Page;
//import com.github.pagehelper.PageHelper;
import com.sinosoft.power.common.exception.PowerException;
import com.sinosoft.power.common.util.CommonUtil;
import com.sinosoft.power.dao.SaaCompanyDao;
import com.sinosoft.power.dao.SaaGradeDao;
import com.sinosoft.power.dao.SaaTaskDao;
import com.sinosoft.power.dao.SaaUserGradeDao;
import com.sinosoft.power.dao.SaaUserGradeTaskDao;
import com.sinosoft.power.dao.SaaUserPermitDataDao;
import com.sinosoft.power.po.PrpDCompany;
import com.sinosoft.power.po.SaaGrade;
import com.sinosoft.power.po.SaaUserGrade;
import com.sinosoft.power.po.SaaUserGradeTask;
import com.sinosoft.power.po.SaaUserPermitData;
import com.sinosoft.power.po.Gguser;
import com.sinosoft.power.redis.RedisUtil;
import com.sinosoft.power.redis.SaaRedisService;
import com.sinosoft.power.service.PowerService;
import com.sinosoft.power.service.SaaCommonDataService;
import com.sinosoft.power.service.SaaUserService;
import com.sinosoft.power.util.PowerConstant;
import com.sinosoft.power.vo.ComTreeNode;
import com.sinosoft.power.vo.CommonDataVo;
import com.sinosoft.power.vo.DataTablePageVo;
import com.sinosoft.power.vo.PowerKeyWord;
import com.sinosoft.power.vo.SaaGradeVo;
import com.sinosoft.power.vo.SysPowerUserVo;
import com.sinosoft.power.vo.UserInParamVo;
import ins.framework.utils.Beans;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.sinosoft.power.common.util.RedisKeyUtil.REDIS_SEPARTOR;
import static com.sinosoft.power.common.util.RedisKeyUtil.USER_KEY_DATA_POWER;
import static com.sinosoft.power.common.util.RedisKeyUtil.USER_KEY_TASKCODE_LIST;
import static com.sinosoft.power.util.PowerConstant.FACTOR_CLASS;
import static com.sinosoft.power.util.PowerConstant.FACTOR_COM;


public class PowerServiceImpl implements PowerService {
    private Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private SqlSession session;
    private SaaTaskDao saaTaskDao;
    private SaaUserGradeTaskDao saaUserGradeTaskDao;
    private SaaUserGradeDao saaUserGradeDao;
    private SaaUserPermitDataDao saaUserPermitDataDao;
    private SaaGradeDao saaGradeDao;
    private SaaCompanyDao saaCompanyDao;
    private SaaUserService saaUserService;
    private SaaCommonDataService commonDataService;

    public PowerServiceImpl(SqlSession session) {
        this.session = session;
        saaTaskDao = session.getMapper(SaaTaskDao.class);
        saaUserGradeTaskDao = session.getMapper(SaaUserGradeTaskDao.class);
        saaUserGradeDao = session.getMapper(SaaUserGradeDao.class);
        saaUserPermitDataDao = session.getMapper(SaaUserPermitDataDao.class);
        saaGradeDao = session.getMapper(SaaGradeDao.class);
        saaCompanyDao = session.getMapper(SaaCompanyDao.class);
        saaUserService = new SaaUserService(session);
        commonDataService = new SaaCommonDataServiceImpl(session);
    }

    /**
     * 校验用户权限
     *
     * @param userCode
     * @param powerTaskCode 功能权限代码 aml.regist.insert
     * @return
     */
    @Override
    public boolean checkPower(String userCode, String powerTaskCode) {
        if (StringUtils.equals(userCode, "0000000000")) {
            return true;
        }
        List<SaaUserGradeTask> saaUserGradeTaskList = saaUserGradeTaskDao.queryTaskListByUserCodeAndTaskCode(userCode, powerTaskCode);
        if (!saaUserGradeTaskList.isEmpty()) {
            return true;
        }
        return false;
    }

    /**
     * 检查是否有系统维护权限
     *
     * @param userCode      用户编码
     * @param powerTaskCode 权限编码
     */
    @Override
    public void checkSysPower(String userCode, String powerTaskCode) {
        List<String> taskCodeList = saaUserGradeTaskDao.queryTaskCodeListByUserCode(userCode);
        if (!taskCodeList.contains(powerTaskCode)) {
            throw new PowerException("740022", true);
        }

    }

    /**
     * 获取业务权限范围
     *
     * @param userCode
     * @param taskCode
     * @return
     */
    @Override
    public Map<String, String> addPower(String userCode, String taskCode) {
        Map<String, String> powerMap = new HashMap<>();

        List<SaaUserPermitData> userPermitComDataList = new ArrayList<>();
        List<SaaUserPermitData> userPermitClassDataList = new ArrayList<>();
        Set<Long> userGradeIdSet = new HashSet<>();
        List<SaaUserGradeTask> saaUserGradeTaskList = saaUserGradeTaskDao.queryTaskListByUserCodeAndTaskCode(userCode, taskCode);
        if (!CollectionUtils.isEmpty(saaUserGradeTaskList)) {
            for (SaaUserGradeTask saaUserGradeTask : saaUserGradeTaskList) {
                SaaUserGrade saaUserGrade = saaUserGradeDao.queryValidGradeByUserCodeAndGradeId(userCode, saaUserGradeTask.getGradeId());
                userGradeIdSet.add(saaUserGrade.getId());
            }
            userPermitComDataList.addAll(saaUserPermitDataDao.queryUserAllPermitData(userCode, "COM", userGradeIdSet));
            userPermitClassDataList.addAll(saaUserPermitDataDao.queryUserAllPermitData(userCode, "CLASS", userGradeIdSet));
        }

        String comPowerSql = null;
        if (!CollectionUtils.isEmpty(userPermitComDataList)) {
            StringBuilder comPowerSqlBuffer = new StringBuilder();
            for (int i = 0; i < userPermitComDataList.size(); i++) {
                SaaUserPermitData userPermitData = userPermitComDataList.get(i);
                if (i == 0) {
                    comPowerSqlBuffer.append(" upperPath ").append(userPermitData.getDataOper()).append(" '").append(userPermitData.getDataValue1()).append("%").append("' ");
                } else {
                    comPowerSqlBuffer.append(" or upperPath ").append(userPermitData.getDataOper()).append(" '").append(userPermitData.getDataValue1()).append("%").append("' ");
                }
            }
            comPowerSql = comPowerSqlBuffer.toString();
        }

        String classPowerSql = null;
        if (!CollectionUtils.isEmpty(userPermitClassDataList)) {
            StringBuilder classPowerSqlBuffer = new StringBuilder();
            for (int i = 0; i < userPermitClassDataList.size(); i++) {
                SaaUserPermitData userPermitData = userPermitComDataList.get(i);
                if (i == 0) {
                    classPowerSqlBuffer.append(" classCode ").append(userPermitData.getDataOper()).append(" (").append(userPermitData.getDataValue1()).append(") ");
                } else {
                    classPowerSqlBuffer.append(" or classCode ").append(userPermitData.getDataOper()).append(" (").append(userPermitData.getDataValue1()).append(") ");
                }
            }
            classPowerSql = classPowerSqlBuffer.toString();
        }

        powerMap.put("comPower", comPowerSql);
        powerMap.put("classPower", classPowerSql);

        return powerMap;
    }



    /**
     * 生成权限拼接SQL
     *
     * @param userCode
     * @param taskCode
     * @param tableName 主表的表名 t_edf_task
     * @return
     */
    @Override
    public String addPower(String userCode, String taskCode, String tableName) {
        StringBuilder powerSql = new StringBuilder();
        Map<String, String> powerMap = addPower(userCode, taskCode);
        String comCodePower;
        String classCodePower;
        if (null != powerMap) {
            comCodePower = powerMap.get("comPower");
            classCodePower = powerMap.get("classPower");
        } else {
            throw new PowerException("获取人员配置的(" + taskCode + ")权限异常或者你没有该功能的权限");
        }
        if (null != comCodePower && comCodePower.length() > 0) {
            powerSql.append("  exists (select 1 from  prpdCompany where  " + tableName + ".companyCode = companyCode and " + comCodePower + " ) ");
        }
        if (null != classCodePower && classCodePower.length() > 0) {
            powerSql.append("  and " + classCodePower + " ");
        }
        return powerSql.toString();
    }


    /**
     * 方法描述：根据人员代码获取该人员的权限机构
     */
    @Override
    public String selectUserPermitComByUserCode(String userCode) {
        if (!StringUtils.isBlank(userCode)) {
            List<SaaUserPermitData> listPermitdata = saaUserPermitDataDao.selectUserPermitComByUserCode(userCode);
            StringBuilder comCodes = new StringBuilder();
            if (listPermitdata != null && listPermitdata.size() > 0) {
                for (SaaUserPermitData saaUserPermitData : listPermitdata) {
                    String comCode = saaUserPermitData.getDataValue1();
                    if (!StringUtils.isBlank(comCode)) {
                        if ("%".equals(comCode.trim())) {
                            comCodes.delete(0, comCodes.length());
                            break;
                        } else {
                            comCode = comCode.split("%")[0];
                        }
                        comCodes.append(comCode).append(";");
                    }
                }
                return comCodes.toString();
            }
        }
        return null;
    }

    /**
     * 方法描述：通过机构，险种，板块，功能代码获取拥有此相关权限的人员列表
     *
     * @param loginUserCode 当前登录用户的编码
     */
    @Override
    public List<SysPowerUserVo> queryPowerUserList(PowerKeyWord powerKeyWord, String loginUserCode) {
        Set<SysPowerUserVo> userVos = new HashSet<>();
        Map<String, String> factorCodeValue = new HashMap<>();
        if (!StringUtils.isBlank(powerKeyWord.getClassCode())) {
            factorCodeValue.put("CLASSCODE", powerKeyWord.getClassCode());
        }
        if (!StringUtils.isBlank(powerKeyWord.getComCode())) {
            factorCodeValue.put("COMCODE", powerKeyWord.getMakeCom());
        }
        /*if(!StringUtils.isBlank(powerKeyWord.getPlateCode())){
            factorCodeValue.put("PLATECODE",powerKeyWord.getPlateCode());
        }*/
        String taskCode = powerKeyWord.getTaskCode();
//        long startTime1 = System.currentTimeMillis();
        List<String> userCodeList = saaUserGradeTaskDao.queryUserCodeListByTaskCode(taskCode);
//        long endTime1 = System.currentTimeMillis();    //获取结束时间
//		long a = endTime1 - startTime1;
//		System.out.println("获取拥有功能代码的用户：" + a + "ms"); 
//		long startTime2 = System.currentTimeMillis();
        boolean adminFlag = "0000000000".equals(loginUserCode);
        for (String userCode : userCodeList) {
//        	long startTime3 = System.currentTimeMillis();
//            factorCodeValue.put("USERCODE",userCode);
            if ("0000000000".equals(userCode) && !adminFlag) {
                continue;
            }
            boolean authFlag = validateDataPower(userCode, taskCode, powerKeyWord.getMakeCom(), powerKeyWord.getClassCode());
            if (authFlag) {
//                SysPowerUserVo vo = userService.findUserByUserCode(userCode);
                SysPowerUserVo vo = saaUserService.findUserByConditions(userCode, powerKeyWord.getComCode());
                if (vo != null && !CommonUtil.isNullString(vo.getUserCode())) {
                    userVos.add(vo);
                }
            }
//            long endTime3 = System.currentTimeMillis();    //获取结束时间
//            long c = endTime1 - startTime1;
//    		System.out.println("获取拥有功能代码的用户：" + c + "ms"); 
        }
//        long endTime2 = System.currentTimeMillis();    //获取结束时间
//		long b = endTime2 - startTime2;
//		System.out.println("权限校验：" + b + "ms");
        List<SysPowerUserVo> result = new ArrayList<>(userVos);
        return result;
    }

    /**
     * 方法描述：通过机构，险种，板块，功能代码，用户判断此用户是否有此权限
     */
    @Override
    public boolean assertUserDataPower(PowerKeyWord powerKeyWord, String userCode) {
        boolean authFlag;
        String taskCode = powerKeyWord.getTaskCode();
        //验证功能权限
        if (!validTaskCode(taskCode, userCode)) {
            return false;
        }
        authFlag = validateDataPower(userCode, taskCode, powerKeyWord.getMakeCom(), powerKeyWord.getClassCode());
        return authFlag;
    }

    /**
     * 方法描述：获取用户的所有功能代码
     */
    @Override
    public List<String> queryTaskCodeListByUser(String userCode) {
        return saaTaskDao.selectUserTaskCode(userCode);
    }

    /**
     * 方法描述：获取用户岗位下的所有功能代码
     */
    @Override
    public List<String> queryGradeTaskCodeListByUser(String userCode) {
        List<String> taskCodeList;
        SaaRedisService saaRedisService = SaaRedisService.getInstance();
        String taskCodeListStr = saaRedisService.getUserDataByKey(userCode, USER_KEY_TASKCODE_LIST);
        if (taskCodeListStr != null) {
            taskCodeList = RedisUtil.string2List(taskCodeListStr);
            LOGGER.info("用户岗位功能redis不为null,user:{},值为taskCodeList:{}", userCode, taskCodeList);
            return taskCodeList;

        }
        taskCodeList = saaUserGradeTaskDao.queryGradeTaskCodeListByUserCode(userCode);
        String taskCodeListValue = RedisUtil.collection2String(taskCodeList);
        saaRedisService.setUserDataByTypeAndValue(userCode, USER_KEY_TASKCODE_LIST, taskCodeListValue);
        return taskCodeList;
    }


    /**
     * 方法描述：获取全量的用户
     */
    @Override
    public List<SysPowerUserVo> queryAllUserList() {
        return saaUserService.queryAllUserList();
    }

    /*@Override
    public DataTablePageVo queryPageUserList(DataTablePageVo dataTablePageVo, String queryparams, String loginUserCode) {
        return saaUserService.queryPageUserList(dataTablePageVo, queryparams, loginUserCode);
    }*/

    /*@Override
    public DataTablePageVo queryPageGradeList(DataTablePageVo dataTablePageVo, String queryparams) {
        SaaGrade saaGrade = new SaaGrade();
        if (org.apache.commons.lang.StringUtils.isNotBlank(queryparams)) {
            saaGrade = JSON.parseObject(queryparams, SaaGrade.class);
        }
        String orderBy = dataTablePageVo.getOrderField() == null ? "id" : dataTablePageVo.getOrderField();
        if (dataTablePageVo.getOrder() != null) {
            orderBy += " " + dataTablePageVo.getOrder();
        }
        int pageNum = dataTablePageVo.getDataTablePageStart();
        int pageSize = dataTablePageVo.getDataTablePageSize();
        PageHelper.offsetPage(pageNum, pageSize);
        PageHelper.orderBy(orderBy);
        List<SaaGrade> saaGradeList = saaGradeDao.findGrade(saaGrade);
        long totalNum = ((Page) saaGradeList).getTotal();
        List<SaaGradeVo> saaGradeVos = new ArrayList<>();
        for (SaaGrade saaGrade1 : saaGradeList) {
            SaaGradeVo saaGradeVo = new SaaGradeVo();
            Beans.copy().from(saaGrade1).to(saaGradeVo);
            saaGradeVos.add(saaGradeVo);
        }
        dataTablePageVo.setData(saaGradeVos);
        dataTablePageVo.setITotalDisplayRecords(totalNum);
        dataTablePageVo.setITotalRecords(totalNum);
        return dataTablePageVo;
    }*/


    /**
     * 方法描述：通过用户代码获取用户对象
     */
    @Override
    public SysPowerUserVo findUserByUserCode(String userCode) {
        return saaUserService.findUserByUserCode(userCode);
    }

    /**
     * 验证用户的权限task是否存在权限
     *
     * @param taskCode
     * @param userCode
     * @return true 有权限， false 无权限
     */
    @Override
    public boolean validTaskCode(String taskCode, String userCode) {
        return queryGradeTaskCodeListByUser(userCode).contains(taskCode);
    }

    /**
     * 验证数据权限，因为权限的设计是通过sql 的形式，通过sql拼装，对配置的表的相关字段拼装sql条件，进行数据查询条件的控制
     * 现在根据  sql 控制判断条件 in = like
     *
     * @param taskCode
     * @param userCode
     * @return true 有权限， false 无权限
     */
    @Override
    public boolean validateDataPower(String userCode, String taskCode, String comCode, String classCode) {
        SaaRedisService saaRedisService = SaaRedisService.getInstance();
        String dataPowerKey = taskCode + REDIS_SEPARTOR + comCode + REDIS_SEPARTOR + classCode;
        String validStatus = saaRedisService.getUserDataByKeyTypeAndValue(userCode, USER_KEY_DATA_POWER, dataPowerKey);
        if (validStatus != null) {
            return StringUtils.equals(validStatus, PowerConstant.VALID);
        }

        //没有命中缓存则查库
        boolean validResult = validateDataPowerByDB(userCode, taskCode, comCode, classCode);

        if (validResult) {
            saaRedisService.setUserDataByTypeKeyAndValue(userCode, USER_KEY_DATA_POWER, dataPowerKey, PowerConstant.VALID);
            return true;
        }
        saaRedisService.setUserDataByTypeKeyAndValue(userCode, USER_KEY_DATA_POWER, dataPowerKey, PowerConstant.INVALID);
        return false;
    }


    /**
     * 验证数据权限，因为权限的设计是通过sql 的形式，通过sql拼装，对配置的表的相关字段拼装sql条件，进行数据查询条件的控制
     * 现在根据  sql 控制判断条件 in = like
     *
     * @param taskCode
     * @param userCode
     * @return true 有权限， false 无权限
     */
    private boolean validateDataPowerByDB(String userCode, String taskCode, String comCode, String classCode) {
        Map<String, String> factorCodeMap = new HashMap<>();
        factorCodeMap.put(FACTOR_COM, comCode);
        factorCodeMap.put(FACTOR_CLASS, classCode);

        Set<Long> gradeIdSet = new HashSet<>();
        List<SaaUserGradeTask> saaUserGradeTaskList = saaUserGradeTaskDao.queryTaskListByUserCodeAndTaskCode(userCode, taskCode);
        for (SaaUserGradeTask saaUserGradeTask : saaUserGradeTaskList) {
            gradeIdSet.add(saaUserGradeTask.getGradeId());
        }
        if (gradeIdSet.isEmpty()) {
            return false;
        }
        List<SaaUserGrade> saaUserGradeList = saaUserGradeDao.queryGradeListByUserCodeAndGradeId(userCode, gradeIdSet);
        Set<Long> userGradeIdSet = new HashSet<>();
        for (SaaUserGrade saaUserGrade : saaUserGradeList) {
            userGradeIdSet.add(saaUserGrade.getId());
        }
        if (userGradeIdSet.isEmpty()) {
            return false;
        }

        Boolean classFlag = false;
        Boolean comFlag = false;
        if (factorCodeMap.get(FACTOR_COM) == null) {
            comFlag = true;
        }
        if (factorCodeMap.get(FACTOR_CLASS) == null) {
            classFlag = true;
        }

        //循环因子
        for (String factorCode : factorCodeMap.keySet()) {
            List<SaaUserPermitData> saaUserPermitDataList = saaUserPermitDataDao.queryUserAllPermitData(userCode, factorCode, userGradeIdSet);
            if (saaUserPermitDataList.isEmpty()) {
                return false;
            }
            String factorValue = factorCodeMap.get(factorCode);

            if (StringUtils.equals(factorCode, FACTOR_COM)) {

                LOGGER.info("validateDataPowerByDB 传入的comCode{}"+factorValue);

                if(StringUtils.isEmpty(factorValue)){
                    throw new PowerException("740006", true);
                }

                PrpDCompany company = saaCompanyDao.queryCompanyUpperPath(factorValue);
                if(company == null){
                    LOGGER.info("数据权限校验 查出的机构信息为null");
                    throw new PowerException("740007","机构因子为:"+factorValue, true);
                }
                //TODO 仅作提示用,后期接口组完善后可以去掉此提示 add by chenjunzhuang 20180913
                if (null == company.getUpperPath()) {
                    throw new PowerException("740002", true);
                }
                LOGGER.info("数据权限校验 查出的upperpath为{}"+company.getUpperPath());
                factorValue = company.getUpperPath();
            }
            for (SaaUserPermitData saaUserPermitData : saaUserPermitDataList) {
                Map<String, Boolean> authFlag = validDataOperPower(saaUserPermitData, factorValue, factorCode);
                if (authFlag.get(FACTOR_CLASS) != null && authFlag.get(FACTOR_CLASS)) {
                    classFlag = true;
                }
                if (authFlag.get(FACTOR_COM) != null && authFlag.get(FACTOR_COM)) {
                    comFlag = true;
                }
            }
        }
        if (classFlag && comFlag) {
            return true;
        }
        return false;
    }

    /**
     * 对所有的用户权限分组处理
     */
    @Override
    public Map<String, Map<String, Map<String, List<SaaUserPermitData>>>> groupUserPermitData() {
        List<SaaUserPermitData> allPermitDataList = saaUserPermitDataDao.selectAll();
        //   用户                             权限因子                                     权限分组                            权限信息
        //  userCode , map<FactorCode, map <FactorGroup,SaaUserPermitData> >
        Map<String, Map<String, Map<String, List<SaaUserPermitData>>>> userPermitDataMap = new HashMap<>();
        Map<String, Map<String, List<SaaUserPermitData>>> userPermitDataFactorCodeMap = new HashMap<>();
        Map<String, List<SaaUserPermitData>> userPermitDataFactorGroupMap = new HashMap<>();
        List<SaaUserPermitData> permitDataList = new ArrayList<>();
        //分组
        for (SaaUserPermitData permitData : allPermitDataList) {
            if (permitData.getUserGradeId() != null) {
                continue;
            }
            userPermitDataFactorCodeMap = userPermitDataMap.get(permitData.getUserCode());

            if (null == userPermitDataFactorCodeMap) {//第一条，创建分组
                userPermitDataFactorCodeMap = new HashMap<>();
                userPermitDataFactorGroupMap = new HashMap<>();
                permitDataList = new ArrayList<>();

                permitDataList.add(permitData);
                userPermitDataFactorGroupMap.put(CommonUtil.convertDoubleToString(permitData.getFactorGroup()), permitDataList);
                if (permitData.getFactorcode() != null) {
                    userPermitDataFactorCodeMap.put(permitData.getFactorcode(), userPermitDataFactorGroupMap);
                    userPermitDataMap.put(permitData.getUserCode(), userPermitDataFactorCodeMap);
                }
                continue;
            }
            userPermitDataFactorGroupMap = userPermitDataFactorCodeMap.get(permitData.getFactorcode());

            if (null == userPermitDataFactorGroupMap || userPermitDataFactorGroupMap.isEmpty()) {//第一条，创建分组
                userPermitDataFactorGroupMap = new HashMap<>();
                permitDataList = new ArrayList<>();

                permitDataList.add(permitData);
                userPermitDataFactorGroupMap.put(CommonUtil.convertDoubleToString(permitData.getFactorGroup()), permitDataList);
                if (permitData.getFactorcode() != null) {
                    userPermitDataFactorCodeMap.put(permitData.getFactorcode(), userPermitDataFactorGroupMap);
                }
                continue;

            }
            permitDataList = userPermitDataFactorGroupMap.get(CommonUtil.convertDoubleToString(permitData.getFactorGroup()));
            String key = permitData.getUserCode() + permitData.getUserGradeId() + permitData.getFactorcode() + permitData.getFactorGroup();
            if (null == permitDataList || permitDataList.isEmpty()) {//第一条，创建分组
                permitDataList = new ArrayList<>();

                permitDataList.add(permitData);
                userPermitDataFactorGroupMap.put(CommonUtil.convertDoubleToString(permitData.getFactorGroup()), permitDataList);
                continue;
            } else {
                String keyTemp = permitDataList.get(0).getUserCode() + permitDataList.get(0).getUserGradeId() + permitDataList.get(0).getFactorcode() + permitDataList.get(0).getFactorGroup();
                if (key.equals(keyTemp)) {
                    permitDataList.add(permitData);
                    continue;
                }
            }
        }
        return userPermitDataMap;
    }

    /**
     * 验证值 是否匹配 userPermitData 配置，支持 in like  = not in  4种操作符
     *
     * @param userPermitData
     * @param factorValue
     * @return
     */
    @Override
    public Map<String, Boolean> validDataOperPower(SaaUserPermitData userPermitData, String factorValue, String factorCode) {
        Boolean flag = false;
        Map<String, Boolean> authFlag = new HashMap<>();
        if ("in".equals(userPermitData.getDataOper())) {
            String dataValue = userPermitData.getDataValue1();
            if(StringUtils.isNotEmpty(factorValue)&&
                    factorValue.equals(dataValue)) {
                flag = true;
            }
        }

        if ("like".equals(userPermitData.getDataOper())) {
            String dataValue = userPermitData.getDataValue1();
            if (dataValue.contains("%")) {
                dataValue = dataValue.substring(0, dataValue.indexOf("%"));
            }
            if (factorValue.startsWith(dataValue)) {
                flag = true;
            }
        }
        
        /*if("not in".equals(userPermitData.getDataOper())){
            String[] dataValues = userPermitData.getDataValue1().split(",");
            for(String s : dataValues){
                if(!s.equals(userPermitData.getDataValue1())){
                    break;
                }
            } 
            authFlag = true;
        }
        if("=".equals(userPermitData.getDataOper())){
            if(factorValue.equals(userPermitData.getDataValue1())){
                authFlag = true;
            }
        }*/
        authFlag.put(factorCode, flag);
        return authFlag;
    }

    @Override
    public List<SaaGrade> queryUserGradeList(String userCode) {
        List<SaaGrade> saaGradeList = saaGradeDao.queryGradeListByUserCode(userCode);
        return saaGradeList;
    }

    @Override
    public String queryUserGradeComTree(String userCode, Long gradeId) {
        SaaUserGrade saaUserGrade = saaUserGradeDao.queryValidGradeByUserCodeAndGradeId(userCode, gradeId);
        if (saaUserGrade == null) {
            throw new PowerException("740003", true);
        }
        Long userGradeId = saaUserGrade.getId();
        List<SaaUserPermitData> saaUserPermitDataList = saaUserPermitDataDao.queryUserGradePermitData(userCode, userGradeId);
        if (CollectionUtils.isEmpty(saaUserPermitDataList)) {
            throw new PowerException("740004", true);
        }
        StringBuilder result = new StringBuilder();
        for (SaaUserPermitData saaUserPermitData : saaUserPermitDataList) {
            String comCode = genComCode(saaUserPermitData);
            result.append(comCode).append(",");
        }
        result.deleteCharAt(result.length() - 1);
        return result.toString();
    }

    private String genComCode(SaaUserPermitData saaUserPermitData) {
        String upperPath = saaUserPermitData.getDataValue1();
        if (StringUtils.contains(upperPath, "/")) {
            upperPath = StringUtils.substringAfterLast(upperPath, "/");
        }
        return upperPath;
    }

    /**
     * 将无下属机构的公司state设置为open
     *
     * @param comNodeTreeList
     */
    @Override
    public void setStatus(List<ComTreeNode> comNodeTreeList) {
        for (ComTreeNode comTreeNode : comNodeTreeList) {
            if (comTreeNode.getChildren() == null || comTreeNode.getChildren().size() == 0) {
                comTreeNode.setState(PowerConstant.NodeState.OPEN);
            } else {
                this.setStatus(comTreeNode.getChildren());
            }
        }
    }

    /**
     * 异步机构树
     *
     * @param userCode
     * @param gradeId
     * @param comCode
     * @param loginComCode 当期登录用户的机构编码
     * @return
     */
    @Override
    public List<ComTreeNode> queryComTree(String userCode, String gradeId, String comCode, String loginComCode, String loginUserCode) {
        List<PrpDCompany> comList = new ArrayList<>();
        Set<String> selectComCodeSet = querySelectComCodeByUser(userCode, CommonUtil.parsrStringToLong(gradeId));
        if (StringUtils.isNotEmpty(comCode)) {
            comList = saaCompanyDao.queryByUpperComCode(comCode);
        } else {
            Map<String, Object> map = new HashMap<>();
            PrpDCompany com =null;
            //如果是总公司或总公司直属，可操作所有
            if (CommonUtil.equals(loginComCode, "31000000") || CommonUtil.equals(loginComCode, "31030000") || CommonUtil.equals(loginComCode, "31990000")) {
                map.put("companyCode", "31000000");
                com = saaCompanyDao.queryByComCode(map);
                com.setUpperComCode(null);
                comList.add(com);
            }else {
                //查询用户配置系统维护岗的UserGradeId
                SaaUserGrade saaUserGrade = saaUserGradeDao.
                        querySysGradeByUserCodeAndGradeId(loginUserCode,PowerConstant.SYS_MANAGECODE );
                if (saaUserGrade == null) {
                    throw new PowerException("740003", true);
                }
                Long userGradeId = saaUserGrade.getId();
                //查询给该用户所配的系统维护的机构权限
                Set<String> loadUpperPathSet = saaUserPermitDataDao.loadPermitDataSetByUserAndUserGradeId(loginUserCode, userGradeId, PowerConstant.FACTOR_COM);
                Set<String> addSet = new HashSet<>(loadUpperPathSet);
                if (!CollectionUtils.isEmpty(addSet)) {
                    for (String upperPath : addSet) {//根据配置的机构配置查询
                        String curryComCode = StringUtils.contains(upperPath, "/") ? StringUtils.substringAfterLast(upperPath, "/") : upperPath;
                            map.put("companyCode", curryComCode);
                            com = saaCompanyDao.queryByComCode(map);
                            com.setUpperComCode(null);
                            comList.add(com);
                    }
                }
            }
        }
        List<ComTreeNode> comNodeList = new ArrayList<>();
        for (PrpDCompany company : comList) {
            ComTreeNode comNode = new ComTreeNode();
            comNode.setComCode(company.getComCode());
            comNode.setComCName(company.getComCName());
            comNode.setUpperComCode(company.getUpperComCode());
            if (selectComCodeSet.contains(company.getComCode())) {
                //已选择的机构
                comNode.setChecked(true);
            } else {
                comNode.setChecked(false);
            }
            comNodeList.add(comNode);
        }
        for (ComTreeNode com : comNodeList) {
            comList = saaCompanyDao.queryByUpperComCode(com.getComCode());
            if (comList != null && comList.size() > 0) {
                com.setState(PowerConstant.NodeState.CLOSED);
            }
        }
        return comNodeList;
    }

    /**
     * 查询用户和岗位下有权限的机构
     *
     * @param userCode
     * @param gradeId
     * @return
     */
    @Override
    public Set<String> querySelectComCodeByUser(String userCode, Long gradeId) {
        Set<String> selectComCodeSet = new HashSet<>();
        SaaUserGrade saaUserGrade = saaUserGradeDao.queryValidGradeByUserCodeAndGradeId(userCode, gradeId);
        if (saaUserGrade == null) {
            throw new PowerException("740003", true);
        }
        Long userGradeId = saaUserGrade.getId();
        List<SaaUserPermitData> saaUserPermitDataList = saaUserPermitDataDao.queryUserGradePermitData(userCode, userGradeId);
        if (CollectionUtils.isEmpty(saaUserPermitDataList)) {
            return selectComCodeSet;
        }
        for (SaaUserPermitData saaUserPermitData : saaUserPermitDataList) {
            //直接获取comcode
            String comCode = saaUserPermitData.getDataValue2();
            //String companyCode = genComCode(saaUserPermitData);
            selectComCodeSet.add(comCode);
        }

        return selectComCodeSet;
    }

    public Set<String> selectUserPermitDataByFactor(String userCode, String factorCode) {
        Assert.hasLength(userCode, "参数不能为空");
        Assert.hasLength(factorCode, "参数不能为空");
        return saaUserPermitDataDao.selectUserPermitDataByFactor(userCode, factorCode);
    }

    public Set<String> selectUserPermitCom(String userCode) {
        Assert.hasLength(userCode, "参数不能为空");
        return saaUserPermitDataDao.selectUserPermitDataByFactor(userCode, PowerConstant.FACTOR_COM);
    }

    @Override
    public void buildAllUpperPath() {
        List<PrpDCompany> companyList = saaCompanyDao.queryAll();
        for (PrpDCompany company : companyList) {
            String upperPath = buildUpperPath(company.getComCode());
            company.setUpperPath(upperPath);
            saaCompanyDao.updateSelectiveByPrimaryKey(company);
        }
    }

    @Override
    public String buildUpperPath(String comCode) {
        List<String> comCodeList = saaCompanyDao.queryUpperComCodeList(comCode);
        Collections.reverse(comCodeList);
        String upperPath = convertListToString(comCodeList);
        return upperPath;
    }

    private String convertListToString(List<String> dataList) {
        StringBuilder stringBuilder = new StringBuilder();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (String data : dataList) {
                stringBuilder.append(data).append("/");
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        }
        return stringBuilder.toString();
    }

    /**
     * 获取用户数据权限所有险类树
     *
     * @return
     */
    @Override
    public List<ComTreeNode> queryClassTree(String userCode, Long gradeId) {
        List<CommonDataVo> commonDataVos = commonDataService.queryCommonData("ClassCode");
        Set<String> selectedClass = querySelectClassByUser(userCode, gradeId);
        List<ComTreeNode> comTreeNodes = new ArrayList<>();
        for (CommonDataVo commonDataVo : commonDataVos) {
            ComTreeNode comTreeNode = new ComTreeNode();
            comTreeNode.setComCode(commonDataVo.getDataCode());
            comTreeNode.setComCName(commonDataVo.getDataName());
            if (selectedClass.contains(commonDataVo.getDataCode())) {
                comTreeNode.setChecked(true);
            } else {
                comTreeNode.setChecked(false);
            }
            comTreeNodes.add(comTreeNode);
        }
        //添加总险类选择框
        ComTreeNode className = new ComTreeNode();
        className.setComCode("0");
        className.setComCName("险类");
        className.setState(PowerConstant.NodeState.CLOSED);
        className.setChildren(comTreeNodes);
        List<ComTreeNode> comTreeNode = new ArrayList<>();
        comTreeNode.add(className);
        return comTreeNode;
    }

    /**
     * 获取用户数据权限下已有的险类
     *
     * @param userCode
     * @param gradeId
     * @return
     */
    @Override
    public Set<String> querySelectClassByUser(String userCode, Long gradeId) {
        Set<String> selectClassSet = new HashSet<>();
        SaaUserGrade saaUserGrade = saaUserGradeDao.queryValidGradeByUserCodeAndGradeId(userCode, gradeId);
        if (saaUserGrade == null) {
            throw new PowerException("740003", true);
        }
        Long userGradeId = saaUserGrade.getId();
        List<SaaUserPermitData> saaUserPermitDataList = saaUserPermitDataDao.queryUserGradePermitData(userCode, userGradeId);
        if (CollectionUtils.isEmpty(saaUserPermitDataList)) {
            return selectClassSet;
        }
        for (SaaUserPermitData saaUserPermitData : saaUserPermitDataList) {
            if (PowerConstant.FACTOR_CLASS.equals(saaUserPermitData.getFactorcode())) {
                String classCode = saaUserPermitData.getDataValue1();
                if("H,I,M,1,2,3".contains(classCode)){
                    classCode = "36000";
                }
                selectClassSet.add(classCode);
            }
        }
        return selectClassSet;
    }

    /**
     * 查询人员是否有调重结注权限
     *
     * @param userCode
     * @return
     */
    @Override
    public Boolean checkPower(String userCode, String taskCode, String claimCom, String registNo) {

        Map<String, String> map = new HashMap<>();
        map.put("userCode", userCode);
        map.put("taskCode", taskCode);
        map.put("validStatus", "1");
        map.put("factorcode", "COM");
        List<SaaUserPermitData> saaUserPermitDataList = saaUserPermitDataDao.queryUserPermitDataByCondition(map);
        if (CommonUtil.isEmptyList(saaUserPermitDataList)) {
            return false;
        }
        PrpDCompany company = saaCompanyDao.queryCompanyUpperPath(claimCom);
        for(SaaUserPermitData userPermitData : saaUserPermitDataList){
            String upperPath = userPermitData.getDataValue1();
            if (StringUtils.startsWith(company.getUpperPath(),upperPath)){
                return true;
            }
        }
        return false;
    }

    /**
     * @param userCode
     * @return
     * @description 查询个人用户信息
     * <AUTHOR>
     */
    @Override
    public UserInParamVo querySingleUser(String userCode) {
        UserInParamVo userVo = new UserInParamVo();
        Gguser Gguser = saaUserService.querySingleUserByComCode(userCode);
        Beans.copy().from(Gguser).to(userVo);
        return userVo;
    }

    /**
     * @param dataTablePageVo
     * @param queryparams
     * @return
     * <AUTHOR>
     */
    /*@Override
    public DataTablePageVo queryPageUsersList(DataTablePageVo dataTablePageVo, String queryparams,String loginUserCode) {
        return saaUserService.queryPagesUserList(dataTablePageVo, queryparams,loginUserCode);
    }*/

    /**
     * @param vo
     * @return
     * <AUTHOR>
     */
    //@Override
    //public Boolean saveMessage(UserInParamVo vo) {
    //    Boolean flag = false;
    //    //先查一遍看是否有用户信息 再更新
    //    try {
    //        String userCode = vo.getUserCode();
    //        Gguser Gguser = saaUserService.querySingleUserByComCode(userCode);
    //            Gguser.setClaimWorkTel(vo.getClaimWorkTel());
    //            Gguser.setEmailAddress(vo.getEmailAddress());
    //        saaUserService.updateMessage(Gguser);
    //        flag = true;
    //    } catch (Exception e) {
    //        throw new PowerException("740033", true);
    //    }
    //    return flag;
    //}
}
