package com.sinosoft.power.service.service.impl;

import com.sinosoft.power.common.util.CommonDataType;
import com.sinosoft.power.common.util.RedisKeyUtil;
import com.sinosoft.power.dao.SaaAddressDao;
import com.sinosoft.power.dao.SaaBankDao;
import com.sinosoft.power.dao.SaaCommonDataDao;
import com.sinosoft.power.dao.SaaCompanyDao;
import com.sinosoft.power.po.CommonData;
import com.sinosoft.power.po.PrpDAddress;
import com.sinosoft.power.po.PrpDBank;
import com.sinosoft.power.po.PrpDCompany;
import com.sinosoft.power.redis.SaaRedisTemplateUtil;
import com.sinosoft.power.service.SaaCommonDataService;
import com.sinosoft.power.vo.AddressVo;
import com.sinosoft.power.vo.CommonDataVo;
import ins.framework.utils.Beans;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: yancun
 * @create: 2018/12/22 14:41
 * @description:
 */
public class SaaCommonDataServiceImpl implements SaaCommonDataService {

    private SqlSession session;

    private SaaCompanyDao companyDao;

    private SaaBankDao bankDao;

    private SaaAddressDao addressDao;

    private SaaCommonDataDao commonDataDao;

    public SaaCommonDataServiceImpl(SqlSession session) {
        this.session = session;
        companyDao = session.getMapper(SaaCompanyDao.class);
        bankDao = session.getMapper(SaaBankDao.class);
        addressDao = session.getMapper(SaaAddressDao.class);
        commonDataDao = session.getMapper(SaaCommonDataDao.class);
    }

    /**
     * 功能描述：根据数据类型查询数据集合
     * 调用点：
     *
     * <AUTHOR> ********
     * @param dataType 数据类型
     * @return 数据集合
     */
    @Override
    public List<CommonDataVo> queryCommonData(String dataType) {
        return this.queryCommonData(dataType, null, null);
    }


    /**
     * 功能描述：根据数据类型,上级数据类型以及上级数据代码查询数据集合
     * 调用点：
     * @param dataType 数据类型
     * @param preType 上级数据类型
     * @param preCode 上级数据代码
     * @return 数据集合
     */
    public List<CommonDataVo> queryCommonData(String dataType, String preType, String preCode) {
        CommonDataVo vo = new CommonDataVo();
        vo.setDataType(dataType);
        vo.setPreType(preType);
        vo.setPreCode(preCode);
       return this.queryCommonData(vo);
    }


    @Override
    @Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
    public List<CommonDataVo> queryCommonData(CommonDataVo vo) {
        String dataType = vo.getDataType();
        String preCode = vo.getPreCode();
        String preType = vo.getPreType();
        SaaRedisTemplateUtil redisService = SaaRedisTemplateUtil.getInstance();

        //是否存key
        String haveCommonData = null;
        if (StringUtils.isNotBlank(preCode)) {
            haveCommonData = redisService.existKey(RedisKeyUtil.REDISSYSTEM_POWER + RedisKeyUtil.COMMONDATA + RedisKeyUtil.SYMBOL + dataType + RedisKeyUtil.SYMBOL + preCode);
        } else {
            haveCommonData = redisService.existKey(RedisKeyUtil.REDISSYSTEM_POWER + RedisKeyUtil.COMMONDATA + RedisKeyUtil.SYMBOL + dataType);
        }
        //获取缓存
        List<CommonDataVo> voList = new ArrayList<>();
        if (RedisKeyUtil.EXISTENT.equals(haveCommonData)) {
            if (StringUtils.isNotBlank(preCode)) {
                Map<String, String> rsMap = redisService.hEntry(RedisKeyUtil.REDISSYSTEM_POWER + RedisKeyUtil.COMMONDATA + RedisKeyUtil.SYMBOL + dataType + RedisKeyUtil.SYMBOL + preCode);
                for (Map.Entry<String, String> str : rsMap.entrySet()) {
                    CommonDataVo dataVo = new CommonDataVo();
                    dataVo.setDataCode(str.getKey());
                    dataVo.setDataName(str.getValue());
                    voList.add(dataVo);
                }
                return  voList;
            } else {
                //没有preCode
                Map<String, String> rsMap = redisService.hEntry(RedisKeyUtil.REDISSYSTEM_POWER + RedisKeyUtil.COMMONDATA + RedisKeyUtil.SYMBOL + dataType);
                for (Map.Entry<String, String> str : rsMap.entrySet()) {
                    CommonDataVo dataVo = new CommonDataVo();
                    //获取preCode 和dataCode
                    String[] dataKey = str.getKey().split(":");
                    dataVo.setPreCode(dataKey[0]);
                    dataVo.setDataCode(dataKey[1]);
                    dataVo.setDataName(str.getValue());
                    voList.add(dataVo);
                }
               return  voList;
            }

        } else {
            if (vo.getDataType() != null && vo.getDataType().equals(CommonDataType.COM)) {
                List<PrpDCompany> comPoList = new ArrayList<>();
                if (StringUtils.isBlank(vo.getPreCode())) {
                    comPoList = companyDao.queryAll();
                } else {
                    comPoList = companyDao.queryThirdCompany(vo.getPreCode());
                }
                for (PrpDCompany comPo : comPoList) {
                    CommonDataVo commonDataVo = new CommonDataVo();
                    commonDataVo.setDataType(CommonDataType.COM);
                    commonDataVo.setDataCode(comPo.getComCode());
                    commonDataVo.setDataName(comPo.getComCName());
                    voList.add(commonDataVo);
                }
            } else if (vo.getDataType() != null && vo.getDataType().equals(CommonDataType.BANK)) {
                List<PrpDBank> bankPoList = bankDao.queryBanks();
                for (PrpDBank bankPo : bankPoList) {
                    CommonDataVo commonDataVo = new CommonDataVo();
                    commonDataVo.setDataType(CommonDataType.BANK);
                    commonDataVo.setDataCode(bankPo.getBankCode());
                    commonDataVo.setDataName(bankPo.getBankName());
                    voList.add(commonDataVo);
                }
            } else if (vo.getDataType() != null && vo.getDataType().equals(CommonDataType.ADDRESS)) {
                voList = this.queryAddresses();
            } else if (vo.getDataType() != null && vo.getDataType().equals(CommonDataType.PROVINCE)) {
                List<PrpDAddress> provs = addressDao.queryProvincials();
                for (PrpDAddress prov : provs) {
                    CommonDataVo commonDataVo = new CommonDataVo();
                    commonDataVo.setDataType(CommonDataType.PROVINCE);
                    commonDataVo.setDataCode(prov.getCode());
                    commonDataVo.setDataName(prov.getName());
                    voList.add(commonDataVo);
                }
            } else if (CommonDataType.TASKCATALOG.equals(dataType) && CommonDataType.TASKPOOL.equals(preType)) {
                List<CommonData> poList = commonDataDao.queryTaskPool();
                for (CommonData po : poList) {
                    CommonDataVo commonDataVo = new CommonDataVo();
                    Beans.copy().from(po).to(commonDataVo);
                    voList.add(commonDataVo);
                }
            } else {
                List<CommonData> poList = commonDataDao.queryDataByDataType(vo);
                for (CommonData po : poList) {
                    CommonDataVo commonDataVo = new CommonDataVo();
                    Beans.copy().from(po).to(commonDataVo);
                    voList.add(commonDataVo);
                }
            }
            if (!RedisKeyUtil.ERROR.equals(haveCommonData)) {
                for (CommonDataVo v : voList) {
                    if (StringUtils.isNotBlank(preCode)) {
                        redisService.hmSet(RedisKeyUtil.REDISSYSTEM_POWER + RedisKeyUtil.COMMONDATA + RedisKeyUtil.SYMBOL + dataType + RedisKeyUtil.SYMBOL + preCode, v.getDataCode(), v.getDataName());
                    } else {
                        redisService.hmSet(RedisKeyUtil.REDISSYSTEM_POWER + RedisKeyUtil.COMMONDATA + RedisKeyUtil.SYMBOL + dataType, v.getPreCode() + RedisKeyUtil.SYMBOL + v.getDataCode(), v.getDataName());
                    }
                }
            }
        }

        return voList;

    }


    /**
     * 功能描述：查询所有市
     * <AUTHOR> ********
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
    public List<CommonDataVo> queryAddresses() {
        List<CommonDataVo> results = new ArrayList<>();
        List<PrpDAddress> provs = addressDao.queryProvincials();
        for (PrpDAddress prov : provs) {
            AddressVo vo = new AddressVo();
            vo.setParent(prov.getCode());
            List<PrpDAddress> cities = addressDao.queryCities(vo);
            for (PrpDAddress city : cities) {
                CommonDataVo result1 = new CommonDataVo();
                result1.setDataType(CommonDataType.ACC_BANK);
                result1.setDataCode(city.getCode());
                result1.setDataName(prov.getName() + "-" + city.getName());
                results.add(result1);
            }

        }
        return results;
    }

    /**
     * @Description:代码转换
     * @param vo
     * @return string
     */
    @Override
   public String transferData(CommonDataVo vo) {
       CommonDataVo commonDataVo = new CommonDataVo();
       CommonData po = commonDataDao.transferData(vo);
       Beans.copy().from(po).to(commonDataVo);
       return commonDataVo.getDataName() == null ? "" : commonDataVo.getDataName();

   }
}
