package com.sinosoft.power.service.service.impl;

import com.alibaba.fastjson.JSON;
//import com.github.pagehelper.Page;
//import com.github.pagehelper.PageHelper;
import com.sinosoft.power.common.exception.PowerException;
import com.sinosoft.power.common.util.BarConstant;
import com.sinosoft.power.common.util.CommonDataType;
import com.sinosoft.power.common.util.CommonUtil;
import com.sinosoft.power.common.util.GradeConstant;
import com.sinosoft.power.dao.SaaCompanyDao;
import com.sinosoft.power.dao.SaaGradeDao;
import com.sinosoft.power.dao.SaaGradeTaskDao;
import com.sinosoft.power.dao.SaaTaskDao;
import com.sinosoft.power.dao.SaaUserAmountDao;
import com.sinosoft.power.dao.SaaUserDao;
import com.sinosoft.power.dao.SaaUserGradeDao;
import com.sinosoft.power.dao.SaaUserGradeTaskDao;
import com.sinosoft.power.dao.SaaUserPermitDataDao;
import com.sinosoft.power.po.*;
import com.sinosoft.power.redis.SaaRedisService;
import com.sinosoft.power.service.SaaCommonDataService;
import com.sinosoft.power.service.SaaDeployService;
import com.sinosoft.power.service.UserSpaceService;
import com.sinosoft.power.util.PowerConstant;
import com.sinosoft.power.util.PowerTaskCode;
import com.sinosoft.power.util.SaaAmountUtil;
import com.sinosoft.power.vo.CommonDataVo;
import com.sinosoft.power.vo.DataTablePageVo;
import com.sinosoft.power.vo.GradeApiVo;
import com.sinosoft.power.vo.PowerVo;
import com.sinosoft.power.vo.SaaClassDataVo;
import com.sinosoft.power.vo.SaaComDataVo;
import com.sinosoft.power.vo.SaaGradeVo;
import com.sinosoft.power.vo.SaaTaskVo;
import com.sinosoft.power.vo.SaaUserAmountVo;
import com.sinosoft.power.vo.SaaUserGradeTaskVo;
import com.sinosoft.power.vo.SaaUserGradeVo;
import com.sinosoft.power.vo.UserGradeMapperTaskVo;
import ins.framework.utils.Beans;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

//import ins.aml.common.util.CodeTransferUtil;

public class SaaDeployServiceImpl implements SaaDeployService {
    private SqlSession session;

    private SaaTaskDao saaTaskDao;
    private SaaGradeDao saaGradeDao;
    private SaaUserGradeDao saaUserGradeDao;
    private SaaGradeTaskDao saaGradeTaskDao;
    private SaaUserPermitDataDao saaUserPermitDataDao;
    private SaaUserAmountDao saaUserAmountDao;
    private SaaUserGradeTaskDao saaUserGradeTaskDao;
    private SaaCompanyDao companyDao;
    private UserSpaceService userSpaceService;
    private SaaCommonDataService commonDataService;
    private SaaUserDao saaUserDao;

    public SaaDeployServiceImpl(SqlSession session){
        this.session = session;
        saaTaskDao = session.getMapper(SaaTaskDao.class);
        saaGradeDao =session.getMapper(SaaGradeDao.class);
        saaUserGradeDao =session.getMapper(SaaUserGradeDao.class);
        saaGradeTaskDao =session.getMapper(SaaGradeTaskDao.class);
        saaUserPermitDataDao =session.getMapper(SaaUserPermitDataDao.class);
        saaUserAmountDao =session.getMapper(SaaUserAmountDao.class);
        saaUserGradeTaskDao = session.getMapper(SaaUserGradeTaskDao.class);
        companyDao =session.getMapper(SaaCompanyDao.class);
        userSpaceService = new UserSpaceService(session);
        commonDataService = new SaaCommonDataServiceImpl(session);
        saaUserDao=session.getMapper(SaaUserDao.class);
    }


    /**
     * 初始化已配置的人员岗位
     */
    @Override
    public void initCompleteUserGrade(PowerVo vo) {
        List<SaaUserGrade> saaUserGradeList = saaUserGradeDao.findAllUserGrade();
        Set<String> userCodeSet = new HashSet<>();
        List<SaaUserGradeVo> saaUserGradeVoList = new ArrayList<>();
        if (!CommonUtil.isEmptyList(saaUserGradeList)) {
            for (SaaUserGrade saaUserGrade : saaUserGradeList) {
                if (userCodeSet.contains(saaUserGrade.getUserCode())) {
                    continue;
                }
                userCodeSet.add(saaUserGrade.getUserCode());
                SaaUserGradeVo saaUserGradeVo = new SaaUserGradeVo();
                Beans.copy().from(saaUserGrade).to(saaUserGradeVo);
                //saaUserGradeVo.setUserName(CodeTransferUtil.transferUserCode(saaUserGradeVo.getUserCode()));
                saaUserGradeVoList.add(saaUserGradeVo);
            }
        }
        vo.setSaaUserGradeVoList(saaUserGradeVoList);
    }


    /**
     * 初始化人员岗位
     */
    @Override
    public void initUserGrade(PowerVo vo) {
        List<SaaUserGrade> saaUserGradeList = null;
        if (!CommonUtil.isNullString(vo.getUserCode())) {
            saaUserGradeList = saaUserGradeDao.findUserGrade(vo.getUserCode());
        }
        List<SaaGrade> saaGradeList = saaGradeDao.findAllGrade();
        List<SaaGradeVo> saaGradeVoList = new ArrayList<>();
        for (SaaGrade saaGrade : saaGradeList) {
            SaaGradeVo saaGradeVo = new SaaGradeVo();
            Beans.copy().from(saaGrade).to(saaGradeVo);
            saaGradeVoList.add(saaGradeVo);
        }
        List<SaaUserGradeVo> saaUserGradeVoList = new ArrayList<>();
        if (!CommonUtil.isEmptyList(saaUserGradeList)) {
            for (SaaUserGrade saaUserGrade : saaUserGradeList) {
                SaaUserGradeVo saaUserGradeVo = new SaaUserGradeVo();
                Beans.copy().from(saaUserGrade).to(saaUserGradeVo);
                saaUserGradeVoList.add(saaUserGradeVo);
            }
        }
        vo.setSaaGradeVoList(saaGradeVoList);
        vo.setSaaUserGradeVoList(saaUserGradeVoList);
    }

    /**
     * 删除人员岗位
     */
    @Override
    public void deletePersonGrade(PowerVo vo) {
        if (CommonUtil.isNullString(vo.getUserCode())) {
            throw new PowerException("740005", true);
        }
    	/*if(!"0000000000".equals(HttpRequestUtil.getUserCode())){
    		throw new PowerException("您无权操作",false);
    	}*/
//    	saaUserGradeDao.deleteUserGradeByUserCode(vo.getUserCode());
//		saaUserPermitDataDao.deleteDataByUserCode(vo.getUserCode());
    }

    /**
     * 保存人员岗位 并将岗位的功能保存到用户岗位功能表
     */
    @Override
    @Transactional
    public Set<String> saveUserGrade(PowerVo vo, String currentUserCode, Long defaultGradeId) {
        String userCode = vo.getUserCode();
        Set<String> oldNameList =new HashSet<>();
        //清除人员权限缓存
        //SaaRedisService redis = SaaRedisService.getInstance();
        //redis.invalidUserData(userCode);
        List<SaaUserGradeVo> saaUserGradeVoList = vo.getSaaUserGradeVoList();
        Set<Long> gradeIdSet = new HashSet<>();
        if(!CommonUtil.isEmptyList(saaUserGradeVoList)) {
            for (SaaUserGradeVo saaUserGradeVo : saaUserGradeVoList) {
				if(null != saaUserGradeVo.getGradeId()){
                	gradeIdSet.add(saaUserGradeVo.getGradeId());
				}
            }
        }

        //页面获取岗位用户岗位集合
        Set<Long> pageGradeIdSet = gradeIdSet;
        //checkGrade(pageGradeIdSet,userCode);
        //数据库保存用户岗位集合
        List<SaaUserGrade> saaUserGradeList = saaUserGradeDao.findUserGrade(userCode);
        Set<Long> loadGradeIdSet = new HashSet<>();
        if(!CommonUtil.isEmptyList(saaUserGradeList)) {
            for (SaaUserGrade saaUserGrade : saaUserGradeList) {
                loadGradeIdSet.add(saaUserGrade.getGradeId());
            }
        }

        //页面增加的岗位集合
        Set<Long> addSet = new HashSet<>(pageGradeIdSet);
        addSet.removeAll(loadGradeIdSet);
        if (!CollectionUtils.isEmpty(addSet)) {
            for (Long gradeId : addSet) {
                SaaUserGrade saaUserGrade = saaUserGradeDao.queryAllGradeByUserCodeAndGradeId(userCode, gradeId);
                if (saaUserGrade == null) {
                    //没有则添加
                    saaUserGrade = new SaaUserGrade();
                    saaUserGrade.setUserCode(userCode);
                    saaUserGrade.setCreatorCode(currentUserCode);
                    saaUserGrade.setUpdaterCode(currentUserCode);
                    saaUserGrade.setGradeId(gradeId);
                    saaUserGrade.setValidStatus(PowerConstant.VALID);

                    // Add By ZhouTaoyu      Reason:无序列,手动实现主键自增  time:2019/09/23
                    Long id = 1L;
                    Long maxId = saaUserGradeDao.selectMaxId();
                    if (maxId != null) {
                        id = maxId + 1L;
                    }
                    saaUserGrade.setId(id);
                    // TODO  插入时间为null,待解决
                  //  saaUserGrade.setInsertTimeForHis(new Date());
                    saaUserGradeDao.insertSelective(saaUserGrade);
                } else {
                    //有则修改
                    // Modify By ZhouTaoyu      Reason:批量更新参数映射问题,需要用Map接收  time:2019/09/23
                    Map<String,Object> map = new HashMap<String, Object>();
                    map.put("userCode", userCode);
                    map.put("gradeId", gradeId);
                    saaUserGradeDao.validUserGrade(map);
                    //saaUserGradeDao.validUserGrade(userCode, gradeId);
                }
                //考虑到后期用户变多导致插入数据太多,不用这个表应该没有影响,因为taskcode是针对GRADE的,所以使用SAAGRADETASK可以满足  xufan 2019/7/6
                //List<SaaUserGradeTask> saaUserGradeTaskList = saaUserGradeTaskDao.queryAllTaskListByUserCodeAndGradeId(userCode, gradeId);
                ////插入时包括已经被置为无效的数据也得插入，否则后续岗位将其修改为有效时会无法更新
                //List<SaaTask> saaTasks = saaTaskDao.queryAllSaaTaskCodeListByGradeId(gradeId);
                //if (saaUserGradeTaskList.isEmpty()) {
                //    //没有则添加
                //    if (!CollectionUtils.isEmpty(saaTasks)) {
                //        for (SaaTask saaTask : saaTasks) {
                //            SaaUserGradeTask saaUserGradeTask = new SaaUserGradeTask();
                //            saaUserGradeTask.setUserCode(userCode);
                //            saaUserGradeTask.setGradeId(gradeId);
                //            saaUserGradeTask.setTaskCode(saaTask.getTaskCode());
                //            saaUserGradeTask.setCreatorCode(currentUserCode);
                //            saaUserGradeTask.setUpdaterCode(currentUserCode);
                //            saaUserGradeTask.setOperateTimeForHis(new Date());
                //            saaUserGradeTask.setValidStatus(saaTask.getValidFlag());
                //            saaUserGradeTaskDao.insertSelective(saaUserGradeTask);
                //        }
                //    }
                //} else {
                //    //有则修改
                //    List<String> taskCodeList = saaGradeTaskDao.querySaaTaskCodeListByGradeId(gradeId);
                //    saaUserGradeTaskDao.validGradeTask(userCode, gradeId, taskCodeList);
                //}
            }
        }

        //页面减少的岗位集合
        Set<Long> plusSet = new HashSet<>(loadGradeIdSet);
        plusSet.removeAll(pageGradeIdSet);
        if (!CollectionUtils.isEmpty(plusSet)) {
            // Modify By ZhouTaoyu      Reason:批量更新参数映射问题,需要用Map接收  time:2019/09/23
            Map<String,Object> map = new HashMap<String, Object>();
            map.put("userCode", userCode);
            map.put("gradeIdSet", plusSet);
            saaUserGradeDao.invalidUserGrade(map);
//            saaUserGradeDao.invalidUserGrade(userCode, plusSet);
            //考虑到后期用户变多导致插入数据太多,不用这个表应该没有影响,因为taskcode是针对GRADE的,所以使用SAAGRADETASK可以满足  xufan 2019/7/6
//            saaUserGradeTaskDao.invalidUserGradeTask(userCode, plusSet);
            //删除岗位时，应将岗位下的数据权限也删掉
            // TODO
            //saaUserPermitDataDao.deleteDataByUserCodeAndGradeIdSet(userCode,plusSet);
        }
        //用户的权限变了就要把快捷菜单清空
        //userSpaceService.updateUserShortcutList(userCode, "");
        return oldNameList;
    }

    /**
     * 分页查询金额额度
     *
     * @param dataTablePageVo
     * @param queryparams
     * @return
     */
    /*@Override
    public DataTablePageVo initUserAmount(DataTablePageVo dataTablePageVo, String queryparams) {
        PowerVo PowerVo = new PowerVo();
        if (StringUtils.isNotBlank(queryparams)) {
            PowerVo = JSON.parseObject(queryparams, PowerVo.class);
        }
        //PageHelper分页插件
        String orderBy = dataTablePageVo.getOrderField() == null ? "userCode" : dataTablePageVo.getOrderField();
        if (dataTablePageVo.getOrder() != null) {
            orderBy += " " + dataTablePageVo.getOrder();
        }
        int pageNum = dataTablePageVo.getDataTablePageStart();
        int pageSize = dataTablePageVo.getDataTablePageSize();
        PageHelper.offsetPage(pageNum, pageSize);
        PageHelper.orderBy(orderBy);
        List<SaaUserAmount> list = saaUserAmountDao.queryUserAmountByUserCode(PowerVo.getUserCode());
        long totalNum = ((Page) list).getTotal();
        Set<String> classCodeSet = new HashSet<>();
        List<SaaUserAmountVo> saaUserAmountVos = new ArrayList<>();
        for (SaaUserAmount saaUserAmount : list) {
            String classCode = saaUserAmount.getClassCode();
            String[] codes = classCode.split(",");
            for (int i =0;i<codes.length; i++) {

                if (SaaAmountUtil.NX_CLASSCODES.contains(codes[i])) {
                    classCodeSet.add(SaaAmountUtil.NX_CLASSCODE);
                } else {
                    classCodeSet.add(codes[i].toString());
                }
            }
        String Classcodes =CommonUtil.listToString(new ArrayList<>(classCodeSet));
            saaUserAmount.setClassCode(Classcodes);
            SaaUserAmountVo saaUserAmountVo = new SaaUserAmountVo();
            Beans.copy().from(saaUserAmount).to(saaUserAmountVo);
            saaUserAmountVos.add(saaUserAmountVo);
        }
        dataTablePageVo.setData(saaUserAmountVos);
        dataTablePageVo.setITotalDisplayRecords(totalNum);
        dataTablePageVo.setITotalRecords(totalNum);
        return dataTablePageVo;
    }*/

    /**
     * 新增用户金额权限
     *
     * @param saaUserAmountVo
     * @return
     */
    @Override
    public int saveUserAmount(SaaUserAmountVo saaUserAmountVo) {
        List<SaaUserAmount> saaUserAmounts = saaUserAmountDao.queryUserAmountByUserCode(saaUserAmountVo.getUserCode());
        for(SaaUserAmount saa :saaUserAmounts){
            if(saa.getTaskCode().equals(saaUserAmountVo.getTaskCode())){
                String taskCode = saaUserAmountVo.getTaskCode();
                CommonDataVo commonDataVo = new CommonDataVo();
                commonDataVo.setDataType(CommonDataType.TASKCODE);
                commonDataVo.setDataCode(taskCode);
                String taskName = commonDataService.transferData(commonDataVo);
                throw new PowerException("该用户已经存在"+taskName+"权限！",false);
            }
        }
        SaaUserAmount saaUserAmount = new SaaUserAmount();
        Beans.copy().from(saaUserAmountVo).to(saaUserAmount);
        if(StringUtils.isEmpty(saaUserAmount.getSystemCode())){
            saaUserAmount.setSystemCode(PowerConstant.CN_SYSTEMCODE);
        }
        return saaUserAmountDao.insertSelective(saaUserAmount);
    }

    /**
     * 修改用户金额权限
     *
     * @param saaUserAmountVo
     * @return
     */
    @Override
    public int updateUserAmount(SaaUserAmountVo saaUserAmountVo) {
        List<SaaUserAmount> saaUserAmounts = saaUserAmountDao.queryUserAmountByUserCode(saaUserAmountVo.getUserCode());
        for(SaaUserAmount saa :saaUserAmounts){
            if(saa.getId().equals(saaUserAmountVo.getId())){
                continue;
            }
            if(saa.getTaskCode().equals(saaUserAmountVo.getTaskCode())){
                String taskCode = saaUserAmountVo.getTaskCode();
                CommonDataVo commonDataVo = new CommonDataVo();
                commonDataVo.setDataType(CommonDataType.TASKCODE);
                commonDataVo.setDataCode(taskCode);
                String taskName = commonDataService.transferData(commonDataVo);
                throw new PowerException("该用户已经存在"+taskName+"权限！",false);
            }
        }
        SaaUserAmount saaUserAmount = new SaaUserAmount();
        Beans.copy().from(saaUserAmountVo).to(saaUserAmount);
        return saaUserAmountDao.updateSelectiveByPrimaryKey(saaUserAmount);
    }

    /**
     * 删除用户金额权限
     *
     * @param saaUserAmountVo
     * @return
     */
    @Override
    public int deleteUserAmount(SaaUserAmountVo saaUserAmountVo) {
        SaaUserAmount saaUserAmount = new SaaUserAmount();
        Beans.copy().from(saaUserAmountVo).to(saaUserAmount);
        return saaUserAmountDao.deleteByPrimaryKey(saaUserAmount.getId());
    }

    private String transferGrade(Double userGradeId) {
        if ("999999".equals(CommonUtil.convertDoubleToString(userGradeId))) {return "通用权限";}
        List<UserGradeMapperTaskVo> userGradeTaskMapperList = saaUserGradeDao.findUserGradeIdTaskMapper();
        for (UserGradeMapperTaskVo userGradeMapperTaskVo : userGradeTaskMapperList) {
            if (CommonUtil.convertDoubleToString(userGradeMapperTaskVo.getUserGradeId()).equals(CommonUtil.convertDoubleToString(userGradeId))) {
                return userGradeMapperTaskVo.getGradeName();
            }
        }
        return "";
    }

    /**
     * 对所有的用户的taskCode和userGradeId进行对应
     */
    @Override
    public Map<String, Map<String, Double>> initTaskUserGradeMapper() {
        List<UserGradeMapperTaskVo> userGradeTaskMapperList = saaUserGradeDao.findUserGradeIdTaskMapper();
        Map<String, Map<String, Double>> resultMap = new HashMap<>();
        Map<String, Double> gradeTaskMap = new HashMap<>();
        Double userGradeId = null;
        for (UserGradeMapperTaskVo userGradeMapperTaskVo : userGradeTaskMapperList) {
            gradeTaskMap = resultMap.get(userGradeMapperTaskVo.getUserCode());
            if (null == gradeTaskMap) {
                gradeTaskMap = new HashMap<>();
                gradeTaskMap.put(userGradeMapperTaskVo.getTaskCode(), userGradeMapperTaskVo.getUserGradeId());
                resultMap.put(userGradeMapperTaskVo.getUserCode(), gradeTaskMap);
                continue;
            }
            userGradeId = gradeTaskMap.get(userGradeMapperTaskVo.getTaskCode());
            if (null == userGradeId) {
                gradeTaskMap.put(userGradeMapperTaskVo.getTaskCode(), userGradeMapperTaskVo.getUserGradeId());
                continue;
            }

        }
        return resultMap;
    }

    /**
     * 根据岗位ID查询岗位所有权限
     *
     * @param id
     * @return
     */
    @Override
    public List<SaaTask> getTaskByGradeId(String id) {
        List<SaaTask> saaTaskList = saaTaskDao.selectByGradeId(id);
        return saaTaskList;
    }

    /**
     * 根据用户代码和岗位id查询岗位所有的权限
     *
     * @param id
     * @return
     */
    @Override
    public List<SaaTask> getGradeTaskByUserAndGrade(String id, String userCode) {
        //考虑到后期用户变多导致插入数据太多,不用这个表应该没有影响,因为taskcode是针对GRADE的,所以使用SAAGRADETASK可以满足  xufan 2019/7/6
        List<SaaUserGradeTask> saaUserGradeTaskList = saaUserGradeTaskDao.queryTaskListByUserCodeAndGradeId(userCode, id);
        if (saaUserGradeTaskList.isEmpty()) {
            throw new PowerException("740013", true);
        }
        Set<String> taskCodeSet = new HashSet<>();
        for (SaaUserGradeTask saaUserGradeTask : saaUserGradeTaskList) {
            taskCodeSet.add(saaUserGradeTask.getTaskCode());
        }
        List<SaaTask> saaTaskList = saaTaskDao.querySaaTaskListByTaskCodeSet(taskCodeSet);
        return saaTaskList;
    }

    /**
     * 根据id删除岗位信息
     *
     * @param gradeId
     * @return
     */
    @Override
    public void deleteGrade(Long gradeId) {
        if (gradeId == null) {
            throw new PowerException("740014", true);
        }
        //考虑到后期用户变多导致插入数据太多,不用这个表应该没有影响,因为taskcode是针对GRADE的,所以使用SAAGRADETASK可以满足  xufan 2019/7/6
        int gradeUseCount = saaUserGradeTaskDao.queryUserGradeTaskCountByGradeId(gradeId);
        if (gradeUseCount > 0) {
            throw new PowerException("740015", true);
        }
        saaGradeDao.invalidGrade(gradeId);
    }

    /**
     * 保存岗位 同时把所有功能赋予岗位功能
     *
     * @param gradeApiVo
     * @param userCode
     * @param makeCom
     */
    @Override
    public void saveGrade(GradeApiVo gradeApiVo,String userCode,String makeCom) {
        String gradeCName = gradeApiVo.getSaaGradeVo().getGradeCName();
        //判断是否有该岗位
        SaaGrade haveGrade = saaGradeDao.queryGradeByGradeCName(gradeCName);
        if (haveGrade != null) {
            throw new PowerException("740016", true);
        }

        SaaGrade saaGrade = new SaaGrade();
        saaGrade.setGradeCName(gradeCName);
        saaGrade.setComCode(makeCom);
        saaGrade.setCreatorCode(userCode);
        saaGrade.setUpdaterCode(userCode);
        saaGrade.setOperateTimeForHis(new Date());
        saaGrade.setValidStatus(PowerConstant.VALID);
        saaGradeDao.insertSelective(saaGrade);
    }

    /**
     * 保存用户岗位功能
     *
     * @param gradeApiVo
     */
    @Override
    public void saveGradeTask(GradeApiVo gradeApiVo) {
        List<SaaUserGradeTaskVo> saaUserGradeTaskVoList = gradeApiVo.getSaaUserGradeTaskVoList();
    }

    /**
     * 更新岗位功能 同步更新已配置该岗位的用户功能权限
     *
     * @param gradeApiVo
     */
    @Override
    public void updateGradeTask(GradeApiVo gradeApiVo,String userCode) {
        SaaGradeVo saaGradeVo = gradeApiVo.getSaaGradeVo();
        SaaGrade saaGrade = new SaaGrade();

        Beans.copy().from(saaGradeVo).to(saaGrade);
        Long gradeId = saaGrade.getId();
        String gradeCName = saaGrade.getGradeCName();
        if (StringUtils.isBlank(gradeCName)) {
            throw new PowerException("740017", true);
        }
        //判断是否有该岗位
        SaaGrade haveGrade = saaGradeDao.queryGradeByGradeCName(gradeCName);

        SaaGrade originGrade = saaGradeDao.selectByPrimaryKey(gradeId);

        if (haveGrade != null && !originGrade.getGradeCName().equals(gradeCName)) {
            throw new PowerException("740016", true);
        }
        List<SaaTaskVo> saaTaskVoList = gradeApiVo.getSaaTaskVoList();
        if (CollectionUtils.isEmpty(saaTaskVoList)) {
            throw new PowerException("740018", true);
        }

        saaGradeDao.updateSelectiveByPrimaryKey(saaGrade);

        Set<Long> taskIdSet = new HashSet<>();
        for (SaaTaskVo saaTaskVo : saaTaskVoList) {
            taskIdSet.add(saaTaskVo.getId());
        }

        //理算权限和核赔权限互斥
        //checkTask(taskIdSet);

        //岗位功能修改后同步用户权限
        updateGradeTask(gradeId, taskIdSet,userCode);
    }

    /**
     * 岗位功能修改后同步用户权限
     *
     * @param gradeId
     * @param taskIdSet
     * @param userCode
     */
    private void updateGradeTask(Long gradeId, Set<Long> taskIdSet,String userCode) {
        //页面获取岗位taskCode集合
        Set<String> pageTaskCodeSet = saaTaskDao.queryTaskCodeSetByTaskIdSet(taskIdSet);

        //数据库保存岗位taskCode集合
        List<String> gradeTaskCodeList = saaGradeTaskDao.querySaaTaskCodeListByGradeId(gradeId);
        Set<String> loadTaskCodeSet = new HashSet<>(gradeTaskCodeList);

        //已经配置了该权限的人员list
        List<SaaUserGrade> saaUserGradeList = saaUserGradeDao.queryValidGradeListByGradeId(gradeId);

        //清除权限缓存
        //2019/6/6 徐帆注释
     /*   if(!CommonUtil.isEmptyList(saaUserGradeList)) {
            Set<String> userCodeSet = new HashSet<>();
            for (SaaUserGrade saaUserGrade : saaUserGradeList) {
                userCodeSet.add(saaUserGrade.getUserCode());
            }
            List<String> userCodeList = new ArrayList<>(userCodeSet);
            SaaRedisService.getInstance().invalidUserListData(userCodeList);
        }*/

        //页面增加的权限集合
        Set<String> addSet = new HashSet<>(pageTaskCodeSet);
        addSet.removeAll(loadTaskCodeSet);
        if (!CollectionUtils.isEmpty(addSet)) {
            List<SaaTask> addSaaTaskList = saaTaskDao.querySaaTaskListByTaskCodeSet(addSet);
            for (SaaTask saaTask : addSaaTaskList) {
                SaaGradeTask saaGradeTask = saaGradeTaskDao.queryAllGradeTaskByGradeIdAndTaskId(gradeId, saaTask.getId());
                if (saaGradeTask == null) {
                    saaGradeTask = new SaaGradeTask();
                    saaGradeTask.setGradeId(gradeId);
                    saaGradeTask.setTaskId(saaTask.getId());
                    saaGradeTask.setCreatorCode(userCode);
                    saaGradeTask.setUpdaterCode(userCode);
                    saaGradeTask.setValidStatus(PowerConstant.VALID);
                    saaGradeTask.setSystemCode(saaTask.getSystem());
                    saaGradeTaskDao.insertSelective(saaGradeTask);

                    //将岗位增加的功能权限，赋予用户
                    for(SaaUserGrade saaUserGrade : saaUserGradeList) {
                        SaaUserGradeTask saaUserGradeTask = new SaaUserGradeTask();
                        saaUserGradeTask.setUserCode(saaUserGrade.getUserCode());
                        saaUserGradeTask.setGradeId(gradeId);
                        saaUserGradeTask.setTaskCode(saaTask.getTaskCode());
                        saaUserGradeTask.setCreatorCode(userCode);
                        saaUserGradeTask.setUpdaterCode(userCode);
                        saaUserGradeTask.setOperateTimeForHis(new Date());
                        saaUserGradeTask.setValidStatus(PowerConstant.VALID);
                        //考虑到后期用户变多导致插入数据太多,不用这个表应该没有影响,因为taskcode是针对GRADE的,所以使用SAAGRADETASK可以满足  xufan 2019/7/6
                        saaUserGradeTaskDao.insertSelective(saaUserGradeTask);
                        //用户的权限变了就要把快捷菜单清空
                        userSpaceService.updateUserShortcutList(saaUserGrade.getUserCode(),"");
                    }
                } else {
                    saaGradeTaskDao.validSingleGradeTask(gradeId, saaTask.getId());
                    //将岗位增加的功能权限，置为有效
                    Set<String> taskCodeSet = new HashSet<>();
                    taskCodeSet.add(saaTask.getTaskCode());
                    //考虑到后期用户变多导致插入数据太多,不用这个表应该没有影响,因为taskcode是针对GRADE的,所以使用SAAGRADETASK可以满足  xufan 2019/7/6
                    saaUserGradeTaskDao.validGradeTaskByGradeAndTask(gradeId,taskCodeSet);
                }
            }
        }

        //页面减少的权限集合
        Set<String> plusSet = new HashSet<>(loadTaskCodeSet);
        plusSet.removeAll(pageTaskCodeSet);
        if (!CollectionUtils.isEmpty(plusSet)) {
            //将岗位减少的功能权限，从用户权限中移除，置位无效
            //考虑到后期用户变多导致插入数据太多,不用这个表应该没有影响,因为taskcode是针对GRADE的,所以使用SAAGRADETASK可以满足  xufan 2019/7/6
            saaUserGradeTaskDao.invalidGradeTaskByGradeAndTask(gradeId, plusSet);
            //将岗位权限置位无效
            List<SaaTask> plusSaaTaskList = saaTaskDao.querySaaTaskListByTaskCodeSet(plusSet);
            Set<Long> plusTaskIdSet = new HashSet<>();
            for (SaaTask saaTask : plusSaaTaskList) {
                plusTaskIdSet.add(saaTask.getId());
            }
            saaGradeTaskDao.invalidGradeTask(gradeId, plusTaskIdSet);
        }
    }


    /**
     * 更新用户岗位功能功能
     *
     * @param gradeApiVo
     */
    @Override
    public void updateUserGradeTask(GradeApiVo gradeApiVo) {
        String userCode = gradeApiVo.getUserCode();
        //清除人员权限缓存
        SaaRedisService.getInstance().invalidUserData(userCode);

        Long gradeId = gradeApiVo.getSaaGradeVo().getId();
        List<SaaTaskVo> saaTaskVoList = gradeApiVo.getSaaTaskVoList();
        if (CollectionUtils.isEmpty(saaTaskVoList)) {
            throw new PowerException("740020", true);
        }

        List<String> taskCodeList = saaGradeTaskDao.querySaaTaskCodeListByGradeId(gradeId);
        Set<String> gradeTaskCodeSet = new HashSet<>(taskCodeList);
        Set<Long> taskIdSet = new HashSet<>();
        for (SaaTaskVo saaTaskVo : gradeApiVo.getSaaTaskVoList()) {
            taskIdSet.add(saaTaskVo.getId());
        }
        Set<String> saaTaskList = saaTaskDao.queryTaskCodeSetByTaskIdSet(taskIdSet);
        Set<String> taskCodeSet = new HashSet<>();
        for (String saaTask : saaTaskList) {
            if (!gradeTaskCodeSet.contains(saaTask)) {
                throw new PowerException("740021", true);
            }
            taskCodeSet.add(saaTask);
        }
        //将用户的岗位功能置为无效
        //考虑到后期用户变多导致插入数据太多,不用这个表应该没有影响,因为taskcode是针对GRADE的,所以使用SAAGRADETASK可以满足  xufan 2019/7/6
        saaUserGradeTaskDao.invalidGradeTaskByUserAndGrade(userCode, gradeId);
        //将岗位选择的功能置为有效
        if (!taskCodeSet.isEmpty()) {
            //考虑到后期用户变多导致插入数据太多,不用这个表应该没有影响,因为taskcode是针对GRADE的,所以使用SAAGRADETASK可以满足  xufan 2019/7/6
            saaUserGradeTaskDao.validGradeTaskByUserAndGrade(userCode, gradeId, taskCodeSet);
        }
        //用户的权限变了就要把快捷菜单清空
        userSpaceService.updateUserShortcutList(userCode, "");
    }

    /**
     * 用户岗位功能权限配置界面，获取人员岗位列表
     *
     * @param dataTablePageVo
     * @param userCode
     * @return
     */
    /*@Override
    public DataTablePageVo queryGradeListByUserCode(DataTablePageVo dataTablePageVo, String userCode) {
        String orderField = dataTablePageVo.getOrderField();
        if (StringUtils.equals(orderField, "userGradeId")) {
            orderField = "ID";
        } else {
            orderField = "GRADECNAME";
        }
        String orderBy = "SAAGRADE." + orderField + " " + dataTablePageVo.getOrder();
        int pageNum = dataTablePageVo.getDataTablePageStart();
        int pageSize = dataTablePageVo.getDataTablePageSize();
        PageHelper.offsetPage(pageNum, pageSize);
        PageHelper.orderBy(orderBy);
        List<SaaGrade> saaGradeList = saaGradeDao.queryGradeListByUserCode(userCode);
        long totalNum = ((Page) saaGradeList).getTotal();
        List<SaaGradeVo> saaGradeVoList = new ArrayList<>();
        for (SaaGrade saaGrade : saaGradeList) {
            SaaGradeVo saaGradeVo = new SaaGradeVo();
            BeanUtils.copyProperties(saaGrade, saaGradeVo);
            saaGradeVoList.add(saaGradeVo);
        }
        dataTablePageVo.setData(saaGradeVoList);
        dataTablePageVo.setITotalDisplayRecords(totalNum);
        dataTablePageVo.setITotalRecords(totalNum);
        return dataTablePageVo;
    }*/

    /**
     * 保存人员机构数据权限
     *
     * @param saaComDataVo
     */
    @Override
    @Transactional
    public Set<String> saveUserComData(SaaComDataVo saaComDataVo,String currentUserCode, String defaultComCode) {
        Set<String> oldNameList =new HashSet<>();
        String userCode = saaComDataVo.getUserCode();
        //清除人员权限缓存
//        SaaRedisService.getInstance().invalidUserData(userCode);
        Long gradeId = saaComDataVo.getGradeId();
        SaaUserGrade saaUserGrade = saaUserGradeDao.queryValidGradeByUserCodeAndGradeId(userCode, gradeId);
        Long userGradeId = saaUserGrade.getId();
        String[] comCodeArr;
        String comCodeList = saaComDataVo.getComCodeList();
        if (StringUtils.isBlank(comCodeList)) {
            saaUserPermitDataDao.deleteAllUserGradeData(userCode, saaUserGrade.getId(), PowerConstant.FACTOR_COM);
            return oldNameList;
        }
        comCodeArr = StringUtils.split(comCodeList, ",");
        Set<String> pageComCodeSet = new HashSet<>(Arrays.asList(comCodeArr));
        Set<String> oldComCodeSet = saaUserPermitDataDao.loadPermitDataSetByUserAndUserGradeId(userCode, userGradeId, PowerConstant.FACTOR_COM);
        //页面增加的权限集合
        Set<String> addSet = new HashSet<>(pageComCodeSet);
        addSet.removeAll(oldComCodeSet);
        if (!CollectionUtils.isEmpty(addSet)) {
            //将岗位增加的功能权限，赋予用户，置位有效
            for (String comCode : addSet) {
                SaaUserPermitData saaUserPermitData = new SaaUserPermitData();
                saaUserPermitData.setComCode(saaComDataVo.getMakeCom());
                saaUserPermitData.setSystemCode(PowerConstant.SYSTEMCODE);
                saaUserPermitData.setCreateCode(currentUserCode);
                saaUserPermitData.setValidFlag("1");
                saaUserPermitData.setCreateTime(new Date());
                saaUserPermitData.setUserCode(saaComDataVo.getUserCode());
                saaUserPermitData.setUserGradeId(userGradeId);
                saaUserPermitData.setDataOper("like");
                saaUserPermitData.setDataValue1(comCode);
                //将comCode保存到DataValue2
//                String comCode = StringUtils.contains(comCode, "/") ? StringUtils.substringAfterLast(comCode, "/") : comCode;
                saaUserPermitData.setDataValue2(comCode);
                saaUserPermitData.setFactorcode(PowerConstant.FACTOR_COM);
                saaUserPermitDataDao.insertSelective(saaUserPermitData);
            }
        }

        //页面减少的权限集合
        Set<String> plusSet = new HashSet<>(oldComCodeSet);
        plusSet.removeAll(pageComCodeSet);
        if (!CollectionUtils.isEmpty(plusSet)) {
            //将岗位减少的功能权限，从用户权限中移除，置位无效
            saaUserPermitDataDao.deleteDataByUserAndUserGradeId(userCode, userGradeId, plusSet);
        }
        return oldNameList;
    }

    /**
     * 保存人员险类数据权限
     *
     * @param saaClassDataVo
     */
    @Override
    public void saveUserClassData(SaaClassDataVo saaClassDataVo,String currentUserCode) {
        String userCode = saaClassDataVo.getUserCode();
        //清除人员权限缓存
        SaaRedisService.getInstance().invalidUserData(userCode);

        Long gradeId = saaClassDataVo.getGradeId();
        SaaUserGrade saaUserGrade = saaUserGradeDao.queryValidGradeByUserCodeAndGradeId(userCode, gradeId);
        Long userGradeId = saaUserGrade.getId();
        String comCodeList = saaClassDataVo.getClassCodeList();
        if (StringUtils.isBlank(comCodeList)) {
            saaUserPermitDataDao.deleteAllUserGradeData(userCode, userGradeId, PowerConstant.FACTOR_CLASS);
            return;
        }
        String[] classCodeArr;
        String classCodeList = saaClassDataVo.getClassCodeList();
        classCodeArr = StringUtils.split(classCodeList, ",");
        Set<String> classCodeSet = new HashSet<>(Arrays.asList(classCodeArr));
        Set<String> loadClassCodeSet = saaUserPermitDataDao.loadPermitDataSetByUserAndUserGradeId(userCode, userGradeId, PowerConstant.FACTOR_CLASS);
        //增加的险类
        Set<String> addSet = new HashSet<>(classCodeSet);
        addSet.removeAll(loadClassCodeSet);
        if (!CollectionUtils.isEmpty(addSet)) {
            for (String classCode : addSet) {
                SaaUserPermitData saaUserPermitData = new SaaUserPermitData();
                saaUserPermitData.setComCode(saaClassDataVo.getMakeCom());
                saaUserPermitData.setSystemCode(PowerConstant.CYX_SYSTEMCODE);
                saaUserPermitData.setCreateCode(currentUserCode);
                saaUserPermitData.setValidFlag("1");
                saaUserPermitData.setCreateTime(new Date());
                saaUserPermitData.setUserCode(saaClassDataVo.getUserCode());
                saaUserPermitData.setUserGradeId(userGradeId);
                saaUserPermitData.setDataOper("in");
                saaUserPermitData.setDataValue1(classCode);
                saaUserPermitData.setFactorcode(PowerConstant.FACTOR_CLASS);
                saaUserPermitDataDao.insertSelective(saaUserPermitData);
            }
        }
        //减少的险类
        Set<String> plusSet = new HashSet<>(loadClassCodeSet);
        plusSet.removeAll(classCodeSet);
        if (!CollectionUtils.isEmpty(plusSet)) {
            //将岗位减少的功能权限，从用户权限中移除，置为无效
            saaUserPermitDataDao.deleteDataByUserAndUserGradeId(userCode, userGradeId, plusSet);
        }
    }


    /**
     * @description 获取用户是否可点击右侧侧边栏按钮权限
     * <AUTHOR>
     * @date 2018/11/9 10:17
     * @return java.util.
    */
    @Override
    public List<String> queryTaskCodeByUserCode(String userCode){
        List<SaaUserGradeTask> list=saaUserGradeTaskDao.queryTaskListByUserCode(userCode);
        Map<String,String> map=new HashMap<>();
        List<String> taskCodeList=new ArrayList<>();
        List<String> taskStatusList=new ArrayList<>();
        for(SaaUserGradeTask s:list){
            taskCodeList.add(s.getTaskCode());
        }
        map.put(BarConstant.BarType.rsb1.getTaskCode(),BarConstant.SHOWSTATUS);
        map.put(BarConstant.BarType.rsb2.getTaskCode(),BarConstant.SHOWSTATUS);
        map.put(BarConstant.BarType.rsb3.getTaskCode(),BarConstant.SHOWSTATUS);
        map.put(BarConstant.BarType.rsb4.getTaskCode(),BarConstant.SHOWSTATUS);
        map.put(BarConstant.BarType.rsb13.getTaskCode(),BarConstant.SHOWSTATUS);
        map.put(BarConstant.BarType.rsb14.getTaskCode(),BarConstant.SHOWSTATUS);
        map.put(BarConstant.BarType.rsb15.getTaskCode(),BarConstant.SHOWSTATUS);
        map.put(BarConstant.BarType.rsb16.getTaskCode(),BarConstant.SHOWSTATUS);
        map.put(BarConstant.BarType.rsb17.getTaskCode(),BarConstant.SHOWSTATUS);
        map.put(BarConstant.BarType.rsb19.getTaskCode(),BarConstant.SHOWSTATUS);
       if(taskCodeList.contains(PowerTaskCode.DHLP_MENU_CERTIFY)){
            map.put(BarConstant.BarType.rsb5.getTaskCode(),BarConstant.BarType.rsb5.getStatus());
        } else{
                map.put(BarConstant.BarType.rsb5.getTaskCode(),BarConstant.STATUS);
            }
        if(taskCodeList.contains(PowerTaskCode.DHLP_COMPENSATE_OPERATE)){
            map.put(BarConstant.BarType.rsb6.getTaskCode(),BarConstant.BarType.rsb6.getStatus());
            map.put(BarConstant.BarType.rsb7.getTaskCode(),BarConstant.BarType.rsb7.getStatus());
            map.put(BarConstant.BarType.rsb8.getTaskCode(),BarConstant.BarType.rsb8.getStatus());
            map.put(BarConstant.BarType.rsb9.getTaskCode(),BarConstant.BarType.rsb9.getStatus());
        }else{
            map.put(BarConstant.BarType.rsb6.getTaskCode(),BarConstant.STATUS);
            map.put(BarConstant.BarType.rsb7.getTaskCode(),BarConstant.STATUS);
            map.put(BarConstant.BarType.rsb8.getTaskCode(),BarConstant.STATUS);
            map.put(BarConstant.BarType.rsb9.getTaskCode(),BarConstant.STATUS);
        }
        if(taskCodeList.contains(PowerTaskCode.REGIST_CANCEL_APPLY)){
            map.put(BarConstant.BarType.rsb10.getTaskCode(),BarConstant.BarType.rsb10.getStatus());
        }else{
            map.put(BarConstant.BarType.rsb10.getTaskCode(),BarConstant.STATUS);
        }
        if(taskCodeList.contains(PowerTaskCode.SPECIAL_CASE_APPLY)){
            map.put(BarConstant.BarType.rsb11.getTaskCode(),BarConstant.BarType.rsb11.getStatus());
        }else{
            map.put(BarConstant.BarType.rsb11.getTaskCode(),BarConstant.STATUS);
        }
        if(taskCodeList.contains(PowerTaskCode.CLAIM_ESTIMATE_APPLY)){
            map.put(BarConstant.BarType.rsb12.getTaskCode(),BarConstant.BarType.rsb12.getStatus());
        }else{
            map.put(BarConstant.BarType.rsb12.getTaskCode(),BarConstant.STATUS);
        }
        if(taskCodeList.contains(PowerTaskCode.DHLP_ENDCASE_OPERATE)){
            map.put(BarConstant.BarType.rsb18.getTaskCode(),BarConstant.BarType.rsb18.getStatus());
        }else{
            map.put(BarConstant.BarType.rsb18.getTaskCode(),BarConstant.STATUS);
        }
        for(String s:map.keySet()){
            if(map.get(s).equals(BarConstant.STATUS)){
                taskStatusList.add(s);
            }
        }
        return taskStatusList;
    }
}
