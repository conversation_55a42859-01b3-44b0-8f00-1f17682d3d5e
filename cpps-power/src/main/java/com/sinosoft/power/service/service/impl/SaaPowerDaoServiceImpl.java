package com.sinosoft.power.service.service.impl;

import com.sinosoft.power.dao.*;
import com.sinosoft.power.po.*;
import com.sinosoft.power.service.SaaPowerDaoService;
import com.sinosoft.power.vo.ExportUserPowerInfoVo;
import org.apache.ibatis.session.SqlSession;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: yancun
 * @create: 2018/12/21 11:12
 * @description:
 */
public class SaaPowerDaoServiceImpl implements SaaPowerDaoService {
    private SqlSession sqlSession;

    public SaaPowerDaoServiceImpl(SqlSession sqlSession) {
        this.sqlSession = sqlSession;
    }

    @Override
    public List<SaaUserGrade> findUserGradeByUserCodeAndTaskCode(Map<String, String> param) {
        return this.sqlSession.getMapper(SaaUserGradeDao.class).findUserGradeByUserCodeAndTaskCode(param);
    }

    @Override
    public List<SaaUserGrade>  queryGradeByUserCodeAndGradeIdSet( String userCode,  Set<Long> gradeIdSet){
        return this.sqlSession.getMapper(SaaUserGradeDao.class).queryGradeByUserCodeAndGradeIdSet(userCode,gradeIdSet);
    }

    @Override
    public List<String> queryDataValue1ByParamString(Map<String, String> param) {
        return this.sqlSession.getMapper(SaaUserPermitDataDao.class).queryDataValue1ByParamString(param);
    }

    @Override
    public List<SaaUserPermitData> queryUserPermitDataByCondition(Map<String, String> param){
        return this.sqlSession.getMapper(SaaUserPermitDataDao.class).queryUserPermitDataByCondition(param);
    }

    @Override
    public List<String> getPermitComcode(String userCode, String taskcode) {
        return this.sqlSession.getMapper(SaaUserPermitDataDao.class).getPermitComcode(userCode, taskcode);
    }

    @Override
    public List<SaaUserPermitData> selectUserPermitByParam(Map<String, String> param) {
        return this.sqlSession.getMapper(SaaUserPermitDataDao.class).selectUserPermitByParam(param);
    }

    @Override
    public List<SaaUserPermitData> queryPermitDataByUserCodeAndFactorCode(String userCode, String factorCode,String  userGradeId) {
        return this.sqlSession.getMapper(SaaUserPermitDataDao.class).queryUserPermitData(userCode, factorCode,userGradeId);
    }

    @Override
    public List<SaaUserPermitData> selectAllByUserGradeId(String userGradeId) {
        return this.sqlSession.getMapper(SaaUserPermitDataDao.class).selectAllByUserGradeId(userGradeId);
    }

    @Override
    public List<SaaUserPermitData> selectUserPermitComByUserCode(String userCode) {
        return this.sqlSession.getMapper(SaaUserPermitDataDao.class).selectUserPermitComByUserCode(userCode);
    }

    @Override
    public SaaUserAmount queryUserAmout(String userCode, String taskCode) {
        return this.sqlSession.getMapper(SaaUserAmountDao.class).queryUserAmout(userCode, taskCode);
    }

    @Override
    public List<SaaUserAmount> queryUserAmountByUserCode(String userCode){
        return this.sqlSession.getMapper(SaaUserAmountDao.class).queryUserAmountByUserCode(userCode);
    }

    @Override
    public List<SaaTask> selectAll() {
        return this.sqlSession.getMapper(SaaTaskDao.class).selectAll();
    }

    @Override
    public List<SaaTask> selectByGradeId(String id) {
        return this.sqlSession.getMapper(SaaTaskDao.class).selectByGradeId(id);
    }

    @Override
    public List<SaaTask> getSaaTaskBySystem(String systemCode,String id){
        return this.sqlSession.getMapper(SaaTaskDao.class).getSaaTaskBySystem(systemCode,id);
    }

    @Override
    public List<String> queryGradeTaskCodeListByUserCode(String userCode) {
        return this.sqlSession.getMapper(SaaUserGradeTaskDao.class).queryGradeTaskCodeListByUserCode(userCode);
    }

    @Override
    public List<SaaUserGradeTask> queryTaskListByUserCodeAndTaskCode(String userCode, String taskCode) {
        return this.sqlSession.getMapper(SaaUserGradeTaskDao.class).queryTaskListByUserCodeAndTaskCode(userCode, taskCode);
    }

    @Override
    public List<String> queryTaskCodeListByUserCode(String userCode) {
        return this.sqlSession.getMapper(SaaUserGradeTaskDao.class).queryTaskCodeListByUserCode(userCode);
    }

    @Override
    public List<SaaUserPermitData> queryUserAllPermitData(String userCode, String factorCode, Set<Long> userGradeIdSet){
        return this.sqlSession.getMapper(SaaUserPermitDataDao.class).queryUserAllPermitData(userCode,factorCode,userGradeIdSet);
    }

    @Override
    public List<SaaUserGrade> queryGradeByUserCodeAndGradeId( String userCode,  Long gradeId){
        return  this.sqlSession.getMapper(SaaUserGradeDao.class).queryGradeByUserCodeAndGradeId(userCode,gradeId);
    }
    @Override
    public SaaUserAmount queryUserAmoutByCodeAndRisk( String userCode,  String taskCode,
                                                      String classCode,  String taskParentCode){
        return   this.sqlSession.getMapper(SaaUserAmountDao.class).queryUserAmoutByCodeAndRisk(userCode,taskCode,classCode,taskParentCode);
    }

    @Override
    public List<SaaUserAmount> queryUserAmountByUndwrtPower(Map<String,String> map){
        return   this.sqlSession.getMapper(SaaUserAmountDao.class).queryUserAmountByUndwrtPower(map);
    }

    @Override
    public List<SaaUserGradeTask> queryGradeTaskByCondtion(Map<String,Object> map){
        return this.sqlSession.getMapper(SaaUserGradeTaskDao.class).queryGradeTaskByCondtion(map);
    }


    @Override
    public List<Gguser> queryPageUserList(Map map){
        return this.sqlSession.getMapper(SaaUserDao.class).queryPageUserList(map);
    }

    /**
     * @Description:用户权限清单导出
     * @param
     * @return:
     */
    @Override
    public List<ExportUserPowerInfoVo> exportPowerUserInfo(Map<String,Object> param){
        return this.sqlSession.getMapper(SaaUserGradeDao.class).exportPowerUserInfo(param);
    }

    /**
     *判断某个人的金额权限
     */
    @Override
    public List<SaaUserAmount> selectUserAmountPermit(Map<String,String> map){
        return this.sqlSession.getMapper(SaaUserAmountDao.class).selectUserAmountPermit(map);

    }

    @Override
    public List<SaaTask> getTaskListBySystem(String systemCode){
        return this.sqlSession.getMapper(SaaTaskDao.class).getTaskListBySystem(systemCode);
    }

}
