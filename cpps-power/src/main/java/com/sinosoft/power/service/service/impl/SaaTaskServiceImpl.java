package com.sinosoft.power.service.service.impl;

import com.sinosoft.power.dao.SaaTaskDao;
import com.sinosoft.power.po.SaaTask;
import com.sinosoft.power.service.SaaTaskService;
import org.apache.ibatis.session.SqlSession;

import java.util.List;

public class SaaTaskServiceImpl implements SaaTaskService {
    private SqlSession sqlSession;

    public SaaTaskServiceImpl(SqlSession sqlSession){
        this.sqlSession = sqlSession;
    }
    @Override
    public List<SaaTask> selectUserTask(String userCode) {
        return this.sqlSession.getMapper(SaaTaskDao.class).selectUserTask(userCode);
    }
}
