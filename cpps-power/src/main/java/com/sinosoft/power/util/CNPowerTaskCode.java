package com.sinosoft.power.util;

/**
 *功能描述: 功能权限常量类 对应表saaTask
 *@author: yancun 2018-12-07
 *
 */
public class CNPowerTaskCode {

    /**特殊赔案审核-中支*/
    public static final String CLAIMMANAGE_APPROVE_SPECIALCASEAUDIT_MA = "aml.claimManage.approve.specialcaseaudit.ma";
    /**特殊赔案审核-中支-查看*/
    public static final String CLAIMMANAGE_APPROVE_SPECIALCASEAUDIT_MA_VIEW = "aml.claimManage.approve.specialcaseaudit.ma.view";
    /**特殊赔案审核-分*/
    public static final String CLAIMMANAGE_APPROVE_SPECIALCASEAUDIT_BA = "aml.claimManage.approve.specialcaseaudit.ba";
    /**特殊赔案审核-分-查看*/
    public static final String CLAIMMANAGE_APPROVE_SPECIALCASEAUDIT_BA_VIEW = "aml.claimManage.approve.specialcaseaudit.ba.view";
    /**特殊赔案审核-总*/
    public static final String CLAIMMANAGE_APPROVE_SPECIALCASEAUDIT_HA = "aml.claimManage.approve.specialcaseaudit.ha";
    /**特殊赔案审核-总-查看*/
    public static final String CLAIMMANAGE_APPROVE_SPECIALCASEAUDIT_HA_VIEW = "aml.claimManage.approve.specialcaseaudit.ha.view";
    /**重开赔案审核-中支*/
    public static final String CLAIMMANAGE_APPROVE_RECASEAUDIT_MA = "aml.claimManage.approve.recaseaudit.ma";
    /**重开赔案审核-中支-查看*/
    public static final String CLAIMMANAGE_APPROVE_RECASEAUDIT_MA_VIEW = "aml.claimManage.approve.recaseaudit.ma.view";
    /**重开赔案审核-分*/
    public static final String CLAIMMANAGE_APPROVE_RECASEAUDIT_BA = "aml.claimManage.approve.recaseaudit.ba";
    /**重开赔案审核-分-查看*/
    public static final String CLAIMMANAGE_APPROVE_RECASEAUDIT_BA_VIEW = "aml.claimManage.approve.recaseaudit.ba.view";
    /**重开赔案审核-总*/
    public static final String CLAIMMANAGE_APPROVE_RECASEAUDIT_HA = "aml.claimManage.approve.recaseaudit.ha";
    /**重开赔案审核-总-查看*/
    public static final String CLAIMMANAGE_APPROVE_RECASEAUDIT_HA_VIEW = "aml.claimManage.approve.recaseaudit.ha.view";
    /**追偿审核-中支*/
    public static final String CLAIMMANAGE_APPROVE_REPLEVYAPPROVE_MA = "aml.claimManage.approve.replevyapprove.ma";
    /**追偿审核-中支-查看*/
    public static final String CLAIMMANAGE_APPROVE_REPLEVYAPPROVE_MA_VIEW = "aml.claimManage.approve.replevyapprove.ma.view";
    /**追偿审核-分*/
    public static final String CLAIMMANAGE_APPROVE_REPLEVYAPPROVE_BA = "aml.claimManage.approve.replevyapprove.ba";
    /**追偿审核-分-查看*/
    public static final String CLAIMMANAGE_APPROVE_REPLEVYAPPROVE_BA_VIEW = "aml.claimManage.approve.replevyapprove.ba.view";
    /**追偿审核-总*/
    public static final String CLAIMMANAGE_APPROVE_REPLEVYAPPROVE_HA = "aml.claimManage.approve.replevyapprove.ha";
    /**追偿审核-总-查看*/
    public static final String CLAIMMANAGE_APPROVE_REPLEVYAPPROVE_HA_VIEW = "aml.claimManage.approve.replevyapprove.ha.view";
    /**损余物资处理审核-中支*/
    public static final String CLAIMMANAGE_APPROVE_LOSSRECOVERY_MA = "aml.claimManage.approve.lossrecovery.ma";
    /**损余物资处理审核-中支-查看*/
    public static final String CLAIMMANAGE_APPROVE_LOSSRECOVERY_MA_VIEW = "aml.claimManage.approve.lossrecovery.ma.view";
    /**损余物资处理审核-分*/
    public static final String CLAIMMANAGE_APPROVE_LOSSRECOVERY_BA = "aml.claimManage.approve.lossrecovery.ba";
    /**损余物资处理审核-分-查看*/
    public static final String CLAIMMANAGE_APPROVE_LOSSRECOVERY_BA_VIEW = "aml.claimManage.approve.lossrecovery.ba.view";
    /**损余物资处理审核-总*/
    public static final String CLAIMMANAGE_APPROVE_LOSSRECOVERY_HA = "aml.claimManage.approve.lossrecovery.ha";
    /**损余物资处理审核-总-查看*/
    public static final String CLAIMMANAGE_APPROVE_LOSSRECOVERY_HA_VIEW = "aml.claimManage.approve.lossrecovery.ha.view";
    /**担保申请审核-中支*/
    public static final String CLAIMMANAGE_APPROVE_GUARANTEE_MA = "aml.claimManage.approve.guarantee.ma";
    /**担保申请审核-中支-查看*/
    public static final String CLAIMMANAGE_APPROVE_GUARANTEE_MA_VIEW = "aml.claimManage.approve.guarantee.ma.view";
    /**担保申请审核-分*/
    public static final String CLAIMMANAGE_APPROVE_GUARANTEE_BA = "aml.claimManage.approve.guarantee.ba";
    /**担保申请审核-分-查看*/
    public static final String CLAIMMANAGE_APPROVE_GUARANTEE_BA_VIEW = "aml.claimManage.approve.guarantee.ba.view";
    /**担保申请审核-总*/
    public static final String CLAIMMANAGE_APPROVE_GUARANTEE_HA = "aml.claimManage.approve.guarantee.ha";
    /**担保申请审核-总-查看*/
    public static final String CLAIMMANAGE_APPROVE_GUARANTEE_HA_VIEW = "aml.claimManage.approve.guarantee.ha.view";
    /**案件巨灾编码维护审核-中支*/
    public static final String CLAIMMANAGE_APPROVE_DISASTERCODE_MA = "aml.claimManage.approve.disastercode.ma";
    /**案件巨灾编码维护审核-中支-查看*/
    public static final String CLAIMMANAGE_APPROVE_DISASTERCODE_MA_VIEW = "aml.claimManage.approve.disastercode.ma.view";
    /**案件巨灾编码维护审核-分*/
    public static final String CLAIMMANAGE_APPROVE_DISASTERCODE_BA = "aml.claimManage.approve.disastercode.ba";
    /**案件巨灾编码维护审核-分-查看	*/
    public static final String CLAIMMANAGE_APPROVE_DISASTERCODE_BA_VIEW = "aml.claimManage.approve.disastercode.ba.view";
    /**案件巨灾编码维护审核-总*/
    public static final String CLAIMMANAGE_APPROVE_DISASTERCODE_HA = "aml.claimManage.approve.disastercode.ha";
    /**案件巨灾编码维护审核-总-查看*/
    public static final String CLAIMMANAGE_APPROVE_DISASTERCODE_HA_VIEW = "aml.claimManage.approve.disastercode.ha.view";
    /**批次确认处理*/
    public static final String ASSIST_BATCHIMPORT_BATCHNOCONFIRM_HANDLE = "aml.assist.batchimport.batchnoconfirm.handle";
    /**批次确认-查看*/
    public static final String ASSIST_BATCHIMPORT_BATCHNOCONFIRM_VIEW = "aml.assist.batchimport.batchnoconfirm.view";
    /**质检处理	*/
    public static final String ASSIST_QUALITYINSPECTION_HANDLE = "aml.assist.qualityinspection.handle";
    /**质检处理-查看*/
    public static final String ASSIST_QUALITYINSPECTION_HANDLE_VIEW = "aml.assist.qualityinspection.handle.view";
    /**质检确认*/
    public static final String ASSIST_QUALITYINSPECTION_CONFIRM = "aml.assist.qualityinspection.confirm";
    /**质检确认-查看*/
    public static final String ASSIST_QUALITYINSPECTION_CONFIRM_VIEW = "aml.assist.qualityinspection.confirm.view";
    /**发起调查*/
    public static final String TRIGGER_INVESTIGATE_LAUNCH = "aml.trigger.investigate.launch";
    /**调查处理	*/
    public static final String TRIGGER_INVESTIGATE_HANDLE = "aml.trigger.investigate.handle";
    /**调查处理-查看*/
    public static final String TRIGGER_INVESTIGATE_HANDLE_VIEW = "aml.trigger.investigate.handle.view";
    /**发起诉讼/仲裁*/
    public static final String TRIGGER_LAWSUITORARBITRATION_LAUNCH = "aml.trigger.lawsuitOrarbitration.launch";
    /**诉讼/仲裁处理*/
    public static final String TRIGGER_LAWSUITORARBITRATION_HANDLE = "aml.trigger.lawsuitOrarbitration.handle";
    /**诉讼/仲裁处理-查看*/
    public static final String TRIGGER_LAWSUITORARBITRATION_HANDLE_VIEW = "aml.trigger.lawsuitOrarbitration.handle.view";
    /**诉讼/仲裁审核-中支*/
    public static final String TRIGGER_LAWSUITORARBITRATION_AUDIT_MA = "aml.trigger.lawsuitOrarbitration.audit.ma";
    /**诉讼/仲裁审核-中支-查看*/
    public static final String TRIGGER_LAWSUITORARBITRATION_AUDIT_MA_VIEW = "aml.trigger.lawsuitOrarbitration.audit.ma.view";
    /**诉讼/仲裁审核-分*/
    public static final String TRIGGER_LAWSUITORARBITRATION_AUDIT_BA = "aml.trigger.lawsuitOrarbitration.audit.ba";
    /**诉讼/仲裁审核-分-查看*/
    public static final String TRIGGER_LAWSUITORARBITRATION_AUDIT_BA_VIEW = "aml.trigger.lawsuitOrarbitration.audit.ba.view";
    /**诉讼/仲裁审核-总*/
    public static final String TRIGGER_LAWSUITORARBITRATION_AUDIT_FA = "aml.trigger.lawsuitOrarbitration.audit.fa";
    /**诉讼/仲裁审核-总-查看*/
    public static final String TRIGGER_LAWSUITORARBITRATION_AUDIT_FA_VIEW = "aml.trigger.lawsuitOrarbitration.audit.fa.view";
    /**发起会商*/
    public static final String TRIGGER_CONSULTATION_LAUNCH = "aml.trigger.consultation.launch";
    /**会商处理	*/
    public static final String TRIGGER_CONSULTATION_HANDLE = "aml.trigger.consultation.handle";
    /**会商处理-查看*/
    public static final String TRIGGER_CONSULTATION_HANDLE_VIEW = "aml.trigger.consultation.handle.view";
    /**会商反馈*/
    public static final String TRIGGER_CONSULTATION_FEEDBACK = "aml.trigger.consultation.feedback";
    /**会商反馈-查看*/
    public static final String TRIGGER_CONSULTATION_FEEDBACK_VIEW = "aml.trigger.consultation.feedback.view";
    /**发起承保反馈*/
    public static final String TRIGGER_UNDWRTFEEDBACK_LAUNCH = "aml.trigger.undwrtfeedback.launch";
    /**承保反馈处理*/
    public static final String TRIGGER_UNDWRTFEEDBACK_HANDLE = "aml.trigger.undwrtfeedback.handle";
    /**承保反馈-查看*/
    public static final String TRIGGER_UNDWRTFEEDBACK_HANDLE_VIEW = "aml.trigger.undwrtfeedback.handle.view";
    /**承保反馈审核*/
    public static final String TRIGGER_UNDWRTFEEDBACK_AUDIT = "aml.trigger.undwrtfeedback.audit";
    /**承保反馈审核-查看*/
    public static final String TRIGGER_UNDWRTFEEDBACK_AUDIT_VIEW = "aml.trigger.undwrtfeedback.audit.view";
    /**发起未决跟踪*/
    public static final String TRIGGER_PENDINGTRACK_LAUNCH = "aml.trigger.pendingtrack.launch";
    /**未决跟踪处理	*/
    public static final String TRIGGER_PENDINGTRACK_HANDLE = "aml.trigger.pendingtrack.handle";
    /**未决跟踪-查看*/
    public static final String TRIGGER_PENDINGTRACK_HANDLE_VIEW = "aml.trigger.pendingtrack.handle.view";
    /**客户身份资料收集*/
    public static final String ASSIST_ANTIMONEY_CUSTOMINFOCOLLECTION = "aml.assist.antimoney.custominfocollection";
    /**反洗钱黑名单处理*/
    public static final String ASSIST_ANTIMONEY_BLACKLIST = "aml.assist.antimoney.blacklist";
    /**赔款支付对象登记处理*/
    public static final String ASSIST_PAYINFOCHECKIN_HANDLE = "aml.assist.payinfocheckin.handle";
    /**单证收集处理*/
    public static final String ASSIST_DOCCOLLECT_HANDLE = "aml.assist.docCollect.handle";
    /**单证打印*/
    public static final String ASSIST_CERTIFYPRINT_HANDLE = "aml.assist.certifyprint.handle";
    /**理赔日志查看*/
    public static final String ASSIST_CLAIMNOTE_HANDLE = "aml.assist.claimnote.handle";
    /**报案登记	*/
    public static final String CLAIMMANAGE_CASECENTER_CASEREGIST = "aml.claimManage.casecenter.caseregist";
    /**报案-查看*/
    public static final String CLAIMMANAGE_CASECENTER_REGISTQUERY = "aml.menu.casequery";
    /**结案处理*/
    public static final String CLAIMMANAGE_CASECENTER_ENDCASE = "aml.claimManage.casecenter.endcase";
    /**结案-查看*/
    public static final String CLAIMMANAGE_CASECENTER_ENDCASE_VIEW = "aml.claimManage.casecenter.endcase.view";
    /**批量导入	*/
    public static final String CLAIMMANAGE_CASECENTER_BATCHIMPORT = "aml.claimManage.casecenter.batchimport";
    /**批次查询*/
    public static final String CLAIMMANAGE_CASECENTER_BATCHNOQUERY = "aml.claimManage.casecenter.batchnoquery";
    /**案件巨灾编码维护处理	*/
    public static final String CLAIMMANAGE_CASECENTER_DISASTERCODE = "aml.claimManage.casecenter.disastercode";
    /**案件巨灾编码维护-查看*/
    public static final String CLAIMMANAGE_CASECENTER_DISASTERCODE_VIEW = "aml.claimManage.casecenter.disastercode.view";
    /**任务查询*/
    public static final String CLAIMMANAGE_TASKCENTER_TASKQUERY = "aml.claimManage.taskcenter.taskquery";
    /**我的任务*/
    public static final String CLAIMMANAGE_TASKCENTER_MYTASK = "aml.claimManage.taskcenter.mytask";
    /**强制改派*/
    public static final String CLAIMMANAGE_TASKCENTER_FORCEASSIGN = "aml.claimManage.taskcenter.forceassign";
    /**任务委托授权*/
    public static final String CLAIMMANAGE_TASKCENTER_TASKCONSIGN = "aml.claimManage.taskcenter.taskconsign";
    /**质检任务发起*/
    public static final String CLAIMMANAGE_TASKCENTER_QUALITYINSPECTION_APPLY = "aml.claimManage.taskcenter.qualityinspection.apply";
    /**单证打印*/
    public static final String ASSIST_REPORTERTIFY_ERTIFYPRINT = "aml.assist.reportertify.ertifyprint";
    /**电子台账*/
    public static final String ASSIST_REPORTERTIFY_ELECTRONICACCOUNT = "aml.assist.reportertify.electronicaccount";
    /**用户信息维护	*/
    public static final String SYSTEMCONFIG_TASKCONFIG_USERINFO = "aml.systemconfig.taskconfig.userinfo";
    /**用户信息-查看*/
    public static final String SYSTEMCONFIG_TASKCONFIG_USERINFO_VIEW = "aml.systemconfig.taskconfig.userinfo.view";
    /**岗位管理*/
    public static final String SYSTEMCONFIG_TASKCONFIG_GRADE = "aml.systemconfig.taskconfig.grade";
    /**岗位管理-查看*/
    public static final String SYSTEMCONFIG_TASKCONFIG_GRADE_VIEW = "aml.systemconfig.taskconfig.grade.view";
    /**总公司用户权限配置*/
    public static final String SYSTEMCONFIG_TASKCONFIG_GATHERUSER = "aml.systemconfig.taskconfig.gatheruser";
    /**总公司用户权限-查看*/
    public static final String SYSTEMCONFIG_TASKCONFIG_GATHERUSER_VIEW = "aml.systemconfig.taskconfig.gatheruser.view";
    /**分公司用户权限配置*/
    public static final String SYSTEMCONFIG_TASKCONFIG_BRANCHUSER = "aml.systemconfig.taskconfig.branchuser";
    /**分公司用户权限-查看*/
    public static final String SYSTEMCONFIG_TASKCONFIG_BRANCHUSER_VIEW = "aml.systemconfig.taskconfig.branchuser.view";
    /**用户金额权限配置*/
    public static final String SYSTEMCONFIG_TASKCONFIG_AMOUNTAUTHORIZED = "aml.systemconfig.taskconfig.amountauthorized";
    /**用户金额权限-查看*/
    public static final String SYSTEMCONFIG_TASKCONFIG_AMOUNTAUTHORIZED_VIEW = "aml.systemconfig.taskconfig.amountauthorized.view";
    /**律师机构/律师维护*/
    public static final String SYSTEMCONFIG_THIRDPARTYCOM_LAWFIRMORLAWYER = "aml.systemconfig.thirdpartycom.lawfirmOrlawyer";
    /**律师机构/律师维护-查看*/
    public static final String SYSTEMCONFIG_THIRDPARTYCOM_LAWFIRMORLAWYER_VIEW = "aml.systemconfig.thirdpartycom.lawfirmOrlawyer.view";
    /**公估机构维护*/
    public static final String SYSTEMCONFIG_THIRDPARTYCOM_LOSSADJUSTING = "aml.systemconfig.thirdpartycom.lossadjusting";
    /**公估机构维护-查看*/
    public static final String SYSTEMCONFIG_THIRDPARTYCOM_LOSSADJUSTING_VIEW = "aml.systemconfig.thirdpartycom.lossadjusting.view";
    /**专家维护	*/
    public static final String SYSTEMCONFIG_THIRDPARTYCOM_PROFESSOR = "aml.systemconfig.thirdpartycom.professor";
    /**专家维护-查看*/
    public static final String SYSTEMCONFIG_THIRDPARTYCOM_PROFESSOR_VIEW = "aml.systemconfig.thirdpartycom.professor.view";

    /**质检评分项配置*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_QUALITYINSPECTION_GRADED = "aml.systemconfig.configmanage.qualityinspection.graded";
    /**质检评分项配置-查看*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_QUALITYINSPECTION_GRADED_VIEW = "aml.systemconfig.configmanage.qualityinspection.graded.view";
    /**黑名单配置*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_BLACKLIST = "aml.systemconfig.configmanage.blacklist";
    /**黑名单配置-查看*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_BLACKLIST_VIEW = "aml.systemconfig.configmanage.blacklist.view";
    /**时效配置*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_AGING = "aml.systemconfig.configmanage.aging";
    /**时效配置-查看*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_AGING_VIEW = "aml.systemconfig.configmanage.aging.view";
    /**简易赔案规则配置*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_SIMPLECASERULE = "aml.systemconfig.configmanage.simplecaserule";
    /**简易赔案规则配置-查看*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_SIMPLECASERULE_VIEW = "aml.systemconfig.configmanage.simplecaserule.view";
    /**会商触发规则配置*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_CONSULTATIONTRIGGERRULE = "aml.systemconfig.configmanage.consultationtriggerrule";
    /**会商触发规则配置-查看*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_CONSULTATIONTRIGGERRULE_VIEW = "aml.systemconfig.configmanage.consultationtriggerrule.view";
    /**未决跟踪任务权限配置*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_PENDINGTRACKTASK = "aml.systemconfig.configmanage.pendingtracktask";
    /**未决跟踪任务权限配置-查看*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_PENDINGTRACKTASK_VIEW = "aml.systemconfig.configmanage.pendingtracktask.view";
    /**第三方机构评价配置*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_THIRDPARTYCOMASSESS = "aml.systemconfig.configmanage.thirdpartycomassess";
    /**第三方机构评价配置-查看*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_THIRDPARTYCOMASSESS_VIEW = "aml.systemconfig.configmanage.thirdpartycomassess.view";
    /**巨灾编码配置*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_DISASTERCODE = "aml.systemconfig.configmanage.disastercode";
    /**巨灾编码配置-查看*/
    public static final String SYSTEMCONFIG_CONFIGMANAGE_DISASTERCODE_VIEW = "aml.systemconfig.configmanage.disastercode.view";
    /**无保单转有保单处理*/
    public static final String CLAIMMANAGE_CHECK_TEMPPOLICYTOHASPOLICY = "aml.claimManage.check.temppolicytohaspolicy";
    /**查勘处理*/
    public static final String CLAIMMANAGE_CHECK_HANDLE = "aml.claimManage.check.handle";
    /**查勘-查看*/
    public static final String CLAIMMANAGE_CHECK_VIEW = "aml.claimManage.check.view";
    /**查勘新增*/
    public static final String CLAIMMANAGE_CHECK_ADD = "aml.claimManage.check.add";
    /**立案处理*/
    public static final String CLAIMMANAGE_HANDLE = "aml.claimManage.aml.handle";
    /**立案-查看*/
    public static final String CLAIMMANAGE_VIEW = "aml.claimManage.aml.view";
    /**估损调整*/
    public static final String CLAIMMANAGE_ADJUSTCLAIMKIND = "aml.claimManage.aml.adjustclaimkind";
    /**特殊赔案申请*/
    public static final String CLAIMMANAGE_SPECIALCASEAPPLY = "aml.claimManage.aml.specialcaseapply";
    /**车辆定损*/
    public static final String CLAIMMANAGE_CAR_CARLOSSAPPROVAL = "aml.claimManage.car.carlossapproval";
    /**车辆定损-查看*/
    public static final String CLAIMMANAGE_CAR_CARLOSSAPPROVAL_VIEW = "aml.claimManage.car.carlossapproval.view";
    /**车辆报价*/
    public static final String CLAIMMANAGE_CAR_OFFER = "aml.claimManage.car.offer";
    /**车辆报价-查看*/
    public static final String CLAIMMANAGE_CAR_OFFER_VIEW = "aml.claimManage.car.offer.view";
    /**总公司调度处理*/
    public static final String CLAIMMANAGE_SCHEDULE_HANDLE_HA = "aml.claimManage.schedule.handle.ha";
    /**分公司调度处理*/
    public static final String CLAIMMANAGE_SCHEDULE_HANDLE_BA = "aml.claimManage.schedule.handle.ba";
    /**调度-查看*/
    public static final String CLAIMMANAGE_SCHEDULE_VIEW = "aml.claimManage.schedule.view";
    /**车辆核损-分公司*/
    public static final String CLAIMMANAGE_CAR_CARVERIFY_BA = "aml.claimManage.car.carverify.ba";
    /**车辆核损-分-查看*/
    public static final String CLAIMMANAGE_CAR_CARVERIFY_BA_VIEW = "aml.claimManage.car.carverify.ba.view";
    /**车辆核损-总公司一级*/
    public static final String CLAIMMANAGE_CAR_CARVERIFY_HA = "aml.claimManage.car.carverify.ha";
    /**车辆核损-总公司一级-查看*/
    public static final String CLAIMMANAGE_CAR_CARVERIFY_HA_VIEW = "aml.claimManage.car.carverify.ha.view";
    /**车辆核损-总公司二级*/
    public static final String CLAIMMANAGE_CAR_CARVERIFY_HB = "aml.claimManage.car.carverify.hb";
    /**车辆核损-总公司二级-查看*/
    public static final String CLAIMMANAGE_CAR_CARVERIFY_HB_VIEW = "aml.claimManage.car.carverify.hb.view";
    /**财产定损*/
    public static final String CLAIMMANAGE_PROP_PROPLOSSAPPROVAL = "aml.claimManage.prop.proplossapproval";
    /**财产定损-查看*/
    public static final String CLAIMMANAGE_PROP_PROPLOSSAPPROVAL_VIEW = "aml.claimManage.prop.proplossapproval.view";
    /**人伤跟踪*/
    public static final String CLAIMMANAGE_PERSON_PERSONTRACE = "aml.claimManage.person.persontrace";
    /**人伤跟踪-查看*/
    public static final String CLAIMMANAGE_PERSON_PERSONTRACE_VIEW = "aml.claimManage.person.persontrace.view";
    /**人伤审核-分*/
    public static final String CLAIMMANAGE_PERSON_PERSONVERIFY_BA = "aml.claimManage.person.personverify.ba";
    /**人伤审核-分-查看*/
    public static final String CLAIMMANAGE_PERSON_PERSONVERIFY_BA_VIEW = "aml.claimManage.person.personverify.ba.view";
    /**人伤审核-总*/
    public static final String CLAIMMANAGE_PERSON_PERSONVERIFY_HA = "aml.claimManage.person.personverify.ha";
    /**人伤审核-总-查看*/
    public static final String CLAIMMANAGE_PERSON_PERSONVERIFY_HA_VIEW = "aml.claimManage.person.personverify.ha.view";
    /**发起人伤专项调查*/
    public static final String CLAIMMANAGE_PERSON_PERSONARTICLEINVESTIGATE_LAUNCH = "aml.claimManage.person.personarticleinvestigate.launch";
    /**人伤专项调查处理*/
    public static final String CLAIMMANAGE_PERSON_PERSONARTICLEINVESTIGATE_HANDLE = "aml.claimManage.person.personarticleinvestigate.handle";
    /**人伤专项调查查看*/
    public static final String CLAIMMANAGE_PERSON_PERSONARTICLEINVESTIGATE_VIEW = "aml.claimManage.person.personarticleinvestigate.view";
    /**人伤专项调查审核	*/
    public static final String CLAIMMANAGE_PERSON_PERSONARTICLEINVESTIGATE_AUDIT = "aml.claimManage.person.personarticleinvestigate.audit";
    /**人伤专项调查审核-查看*/
    public static final String CLAIMMANAGE_PERSON_PERSONARTICLEINVESTIGATE_AUDIT_VIEW = "aml.claimManage.person.personarticleinvestigate.audit.view";
    /**理算处理*/
    public static final String CLAIMMANAGE_COMPENSATE_HANDLE = "aml.claimManage.compensate.handle";
    /**理算-查看*/
    public static final String CLAIMMANAGE_COMPENSATE_VIEW = "aml.claimManage.compensate.view";
    /**发起预付*/
    public static final String CLAIMMANAGE_PREPAID_LAUNCH = "aml.claimManage.prepaid.launch";
    /**预付处理*/
    public static final String CLAIMMANAGE_PREPAID_HANDLE = "aml.claimManage.prepaid.handle";
    /**预付-查看*/
    public static final String CLAIMMANAGE_PREPAID_VIEW = "aml.claimManage.prepaid.view";
    /**发起预赔*/
    public static final String CLAIMMANAGE_PREPAY_LAUNCH = "aml.claimManage.prepay.launch";
    /**预赔处理	*/
    public static final String CLAIMMANAGE_PREPAY_HANDLE = "aml.claimManage.prepay.handle";
    /**预赔-查看*/
    public static final String CLAIMMANAGE_PREPAY_VIEW = "aml.claimManage.prepay.view";
    /**核赔-中支一级*/
    public static final String CLAIMMANAGE_UNDWRT_MA = "aml.claimManage.undwrt.ma";
    /**核赔-中支一级-查看*/
    public static final String CLAIMMANAGE_UNDWRT_MA_VIEW = "aml.claimManage.undwrt.ma.view";
    /**核赔-中支二级*/
    public static final String CLAIMMANAGE_UNDWRT_MB = "aml.claimManage.undwrt.mb";
    /**核赔-中支二级-查看*/
    public static final String CLAIMMANAGE_UNDWRT_MB_VIEW = "aml.claimManage.undwrt.mb.view";
    /**核赔-中支三级*/
    public static final String CLAIMMANAGE_UNDWRT_MC = "aml.claimManage.undwrt.mc";
    /**核赔-中支三级-查看*/
    public static final String CLAIMMANAGE_UNDWRT_MC_VIEW = "aml.claimManage.undwrt.mc.view";
    /**核赔-分公司四级*/
    public static final String CLAIMMANAGE_UNDWRT_BA = "aml.claimManage.undwrt.ba";
    /**核赔-分公司四级-查看	*/
    public static final String CLAIMMANAGE_UNDWRT_BA_VIEW = "aml.claimManage.undwrt.ba.view";
    /**核赔-分公司六级*/
    public static final String CLAIMMANAGE_UNDWRT_BC = "aml.claimManage.undwrt.bc";
    /**核赔-分公司六级-查看*/
    public static final String CLAIMMANAGE_UNDWRT_BC_VIEW = "aml.claimManage.undwrt.bc.view";
    /**核赔-总公司七级*/
    public static final String CLAIMMANAGE_UNDWRT_HA = "aml.claimManage.undwrt.ha";
    /**核赔-总公司七级-查看*/
    public static final String CLAIMMANAGE_UNDWRT_HA_VIEW = "aml.claimManage.undwrt.ha.view";
    /**核赔-总公司八级*/
    public static final String CLAIMMANAGE_UNDWRT_HB = "aml.claimManage.undwrt.hb";
    /**核赔-总公司八级-查看*/
    public static final String CLAIMMANAGE_UNDWRT_HB_VIEW = "aml.claimManage.undwrt.hb.view";
    /**核赔-总公司九级*/
    public static final String CLAIMMANAGE_UNDWRT_HC = "aml.claimManage.undwrt.hc";
    /**核赔-总公司九级-查看*/
    public static final String CLAIMMANAGE_UNDWRT_HC_VIEW = "aml.claimManage.undwrt.hc.view";
    /**发起简易赔案*/
    public static final String CLAIMMANAGE_SIMPLECASE_LAUNCH = "aml.claimManage.simplecase.launch";
    /**简易赔案处理	*/
    public static final String CLAIMMANAGE_SIMPLECASE_HANDLE = "aml.claimManage.simplecase.handle";
    /**简易赔案-查看*/
    public static final String CLAIMMANAGE_SIMPLECASE_VIEW = "aml.claimManage.simplecase.view";
    /**支付信息修改处理*/
    public static final String CLAIMMANAGE_PAYINFO_HANDLE ="aml.claimManage.payinfo.handle";
    /**支付信息修改-查看*/
    public static final String CLAIMMANAGE_PAYINFO_VIEW = "aml.claimManage.payinfo.view";
    /**重开赔案申请*/
    public static final String CLAIMMANAGE_RECASE_APPLY = "aml.claimManage.recase.apply";
    /**发起损余物资登记*/
    public static final String TRIGGER_LOSSRECOVERY_CHECKIN_LAUNCH = "aml.trigger.lossrecovery.checkin.launch";
    /**损余物资登记*/
    public static final String TRIGGER_LOSSRECOVERY_CHECKIN = "aml.trigger.lossrecovery.checkin";
    /**损余物资登记-查看*/
    public static final String TRIGGER_LOSSRECOVERY_CHECKIN_VIEW = "aml.trigger.lossrecovery.checkin.view";
    /**损余物资处理*/
    public static final String TRIGGER_LOSSRECOVERY_HANDLE = "aml.trigger.lossrecovery.handle";
    /**损余物资处理-查看*/
    public static final String TRIGGER_LOSSRECOVERY_HANDLE_VIEW = "aml.trigger.lossrecovery.handle.view";
    /**发起担保申请*/
    public static final String CLAIMMANAGE_GUARANTEE_LAUNCH = "aml.claimManage.guarantee.launch";
    /**担保申请处理*/
    public static final String CLAIMMANAGE_GUARANTEE_HANDLE = "aml.claimManage.guarantee.handle";
    /**担保申请-查看*/
    public static final String CLAIMMANAGE_GUARANTEE_VIEW = "aml.claimManage.guarantee.view";
    /**发起担保效力变更*/
    public static final String CLAIMMANAGE_GUARANTEE_EFFECTCHANGE_LAUNCH = "aml.claimManage.guarantee.effectchange.launch";
    /**担保效力变更	*/
    public static final String CLAIMMANAGE_GUARANTEE_EFFECTCHANGE_HANDLE = "aml.claimManage.guarantee.effectchange.handle";
    /**担保效力变更-查看*/
    public static final String CLAIMMANAGE_GUARANTEE_EFFECTCHANGE_VIEW = "aml.claimManage.guarantee.effectchange.view";
    /**发起追偿*/
    public static final String CLAIMMANAGE_REPLEVY_LAUNCH = "aml.claimManage.replevy.launch";
    /**追偿处理*/
    public static final String CLAIMMANAGE_REPLEVY_HANDLE = "aml.claimManage.replevy.handle";
    /**追偿-查看*/
    public static final String CLAIMMANAGE_REPLEVY_VIEW = "aml.claimManage.replevy.view";
    /**第三方机构聘请申请*/
    public static final String ASSIST_AGENCY_APPLY = "aml.assist.agency.apply";
    /**第三方机构聘请-查看*/
    public static final String ASSIST_AGENCY_VIEW = "aml.assist.agency.view";
    /**第三方机构评价*/
    public static final String ASSIST_AGENCY_APPR = "aml.assist.agency.appr";
    /**第三方机构评价-查看*/
    public static final String ASSIST_AGENCY_APPR_VIEW = "aml.assist.agency.appr.view";
    /**超额立案审核-中支*/
    public static final String CLAIMMANAGE_APPROVE_CASEOVERTOAPPROVE_MA = "aml.claimManage.approve.caseovertoapprove.ma";
    /**超额立案审核-中支-查看*/
    public static final String CLAIMMANAGE_APPROVE_CASEOVERTOAPPROVE_MA_VIEW = "aml.claimManage.approve.caseovertoapprove.ma.view";
    /**超额立案审核-分*/
    public static final String CLAIMMANAGE_APPROVE_CASEOVERTOAPPROVE_BA = "aml.claimManage.approve.caseovertoapprove.ba";
    /**超额立案审核-分-查看*/
    public static final String CLAIMMANAGE_APPROVE_CASEOVERTOAPPROVE_BA_VIEW = "aml.claimManage.approve.caseovertoapprove.ba.view";
    /**超额立案审核-总*/
    public static final String CLAIMMANAGE_APPROVE_CASEOVERTOAPPROVE_HA = "aml.claimManage.approve.caseovertoapprove.ha";
    /**超额立案审核-总-查看*/
    public static final String CLAIMMANAGE_APPROVE_CASEOVERTOAPPROVE_HA_VIEW = "aml.claimManage.approve.caseovertoapprove.ha.view";
    /**估损调整审核-中支	*/
    public static final String CLAIMMANAGE_APPROVE_ADJUSTCLAIMKINDAUDIT_MA = "aml.claimManage.approve.adjustclaimkindaudit.ma";
    /**估损调整审核-中支-查看*/
    public static final String CLAIMMANAGE_APPROVE_ADJUSTCLAIMKINDAUDIT_MA_VIEW = "aml.claimManage.approve.adjustclaimkindaudit.ma.view";
    /**估损调整审核-分*/
    public static final String CLAIMMANAGE_APPROVE_ADJUSTCLAIMKINDAUDIT_BA = "aml.claimManage.approve.adjustclaimkindaudit.ba";
    /**估损调整审核-分-查看*/
    public static final String CLAIMMANAGE_APPROVE_ADJUSTCLAIMKINDAUDIT_BA_VIEW = "aml.claimManage.approve.adjustclaimkindaudit.ba.view";
    /**估损调整审核-总*/
    public static final String CLAIMMANAGE_APPROVE_ADJUSTCLAIMKINDAUDIT_HA = "aml.claimManage.approve.adjustclaimkindaudit.ha";
    /**估损调整审核-总-查看*/
    public static final String CLAIMMANAGE_APPROVE_ADJUSTCLAIMKINDAUDIT_HA_VIEW = "aml.claimManage.approve.adjustclaimkindaudit.ha.view";
    /**核赔-分公司五级	*/
    public static final String CLAIMMANAGE_UNDWRT_BB = "aml.claimManage.undwrt.bb";
    /**核赔-分公司五级-查看*/
    public static final String CLAIMMANAGE_UNDWRT_BB_VIEW = "aml.claimManage.undwrt.bb.view";
    /**系统维护*/
    public static final String DHLP_MENU_SYSTEM = "aml.menu.system";
    /**案件查询*/
    public static final String CLAIM_MENU_CASEQUERY = "aml.menu.casequery";

    /**第三方机构维护*/
    public static final String SYSTEMCONFIG_THIRDPARTYCOM_THIRDPARTYMAINTENANCE = "aml.systemconfig.configmanage.thirdpartymaintenance";

    /**第三方机构维护-查看*/
    public static final String SYSTEMCONFIG_THIRDPARTYCOM_THIRDPARTYMAINTENANCE_VIEW = "aml.systemconfig.configmanage.thirdpartymaintenance.view";


}
