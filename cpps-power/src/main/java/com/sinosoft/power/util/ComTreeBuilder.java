package com.sinosoft.power.util;

import com.sinosoft.power.vo.ComTreeNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-08-30 17:26
 */
public class ComTreeBuilder {

    /**
     * 构造树形结构(递归)
     * @param comTreeNodeList
     * @return
     */
    public static List<ComTreeNode> build(List<ComTreeNode> comTreeNodeList) {
        List<ComTreeNode> treeNodes = new ArrayList<>();
        for (ComTreeNode treeNode : comTreeNodeList) {
            if (StringUtils.isBlank(treeNode.getUpperComCode())) {
                treeNodes.add(findChildren(treeNode, comTreeNodeList));
            }
        }
        return treeNodes;
    }

    public static ComTreeNode findChildren(ComTreeNode treeNode, List<ComTreeNode> treeNodes) {
        for (ComTreeNode it : treeNodes) {
            if (treeNode.getComCode().equals(it.getUpperComCode())) {
                if (treeNode.getChildren() == null) {
                    treeNode.setChildren(new ArrayList<>());
                }
                treeNode.getChildren().add(findChildren(it, treeNodes));
            }
        }
        return treeNode;
    }

}
