package com.sinosoft.power.util;

/**
 * <AUTHOR>
 * @create 2018-08-27 15:11
 */
public class PowerConstant {

    public static final String VALID = "1";

    public static final String INVALID = "0";

    //权限因子 机构
    public static final String FACTOR_COM = "COM";

    //权限因子 险类
    public static final String FACTOR_CLASS = "CLASS";

    public static final String SQL_EQUALS = "(1=1)";

    public static final String SQL_NOT_EQUALS = "1=2";

    public static final String POWER_CLASS = "classPower";

    public static final String POWER_COM = "comPower";

    public static final String AMOUNT_TASKCODE = "amountTaskCode";

    public static final String AMOUNT_TASKPARENTCODE = "amountTaskParentCode";

    public static final String CLASS_CODE = "classCode";

    public static final String AMOUNT = "amount";

    public static final String USER_CODE = "userCode";

    public static final String SYSTEMCODE = "channel";

    public static final String CN_SYSTEMCODE = "property_claim";

    public static final String CYX_SYSTEMCODE = "CYXClaim";
    //系统管理岗配置Code
    public static final String SYS_MANAGECODE ="aml.menu.system";

    /**
     * 树形状态
     */
    public static final class NodeState {
        /**打开*/
        public static final String OPEN = "open";
        /**关闭*/
        public static final String CLOSED = "closed";
    }
}
