package com.sinosoft.power.util;

public class PowerTaskCode {

	/**赔案监控  */
	public static final String DHLP_MENU_PAJK = "aml.menu.casecheckview";//新增--zhanghui20170901
	/**高级平级移交   */
	public static final String DHLP_MENU_SPJYJ = "aml.menu.suppertaskmove";

	/**报案节点操作权限**/
	public static final String DHLP_REGIST_OPERATE="aml.regist.insert";
//	调度岗	aml.schedule.insert
	/**调度操作权限**/
	public static final String DHLP_SCHEDUL_OPERATE="aml.schedule.insert";
//	查勘岗	aml.check.insert
	/**查勘操作权限**/
	public static final String DHLP_CHECK_OPERATE="aml.check.insert";
//	人伤跟踪岗	aml.certainpersonloss.first
	/**人伤跟踪操作权限**/
	public static final String DHLP_DLOSSPERS_OPERATE = "aml.certainpersonloss.first";
//	人伤核损岗	aml.auditingsonloss
	/**人伤核损操作权限*/
	public static final String DHLP_DLOSSPERSUNDWRT_OPERATE = "aml.auditingsonloss";
//	案件处理岗	aml.aml.edit
	/**案件处理操作权限**/
	public static final String DHLP_CASEMAIN_OPERATE="aml.aml.edit";
//	理算岗	aml.compensate.dispose
	/**理算操作权限**/
	public static final String DHLP_COMPENSATE_OPERATE="aml.compensate.dispose";
//	结案岗	aml.endcase.insert
	/**结案操作权限**/
	public static final String DHLP_ENDCASE_OPERATE="aml.endcase.insert";
//	追偿任务处理岗	aml.replevy.dispose
	/**追偿操作权限**/
	public static final String DHLP_REPLEVY_OPERATE="aml.replevy.dispose";
//	重开赔案处理岗	aml.menu.casemonitor
	/**重开赔案申请*/
	public static final String DHLP_MENU_CKPA = "aml.menu.recaseapply";//新增--liuwenyan20170905
	/**重开赔案分公司审核*/
	public static final String DHLP_RECASE_UNDWRTQURT = "aml.recase.undwrtqurt";//新增
	/**重开赔案总公司审核*/
	public static final String DHLP_RECASE_UNDWRTEAD = "aml.recase.undwrthead";//新增--liuwenyan20170905
//	调查岗	aml.research.dispose
	/**调查操作权限**/
	public static final String DHLP_SURVEY_OPERATE="aml.research.dispose";
	/*调查审核操作权限*/
	public static final String DHLP_SURVEYAUDIT_OPERATE="aml.researchAudit.dispose";
	/**
	 * 跟踪操作权限
	 **/
	public static final String DHLP_TRACE_OPERATE = "aml.trace.dispose";
	/*跟踪审核操作权限*/
	public static final String DHLP_TRACEAUDIT_OPERATE = "aml.traceAudit.dispose";
	/**
	 * 改派操作权限
	 **/
	public static final String DHLP_ASSIGN_OPERATE = "aml.assign.dispose";

//	系统维护岗	aml.menu.system
	/**系统维护权限**/
	public static final String DHLP_MENU_SYSTEM="aml.menu.system";
//	收回赔款岗	aml.menu.compensatepayback
//	/**收回赔款申请   */
//	public static final String DHLP_MENU_SHPKSQ = "aml.menu.compensatepayback";
//	单证打印岗	aml.menu.certify
	/**单证打印  */
	public static final String DHLP_MENU_CERTIFY = "aml.menu.certify";
//	退票处理岗	aml.menu.backticket
	/**退票操作权限**/
	public static final String DHLP_RETURNTICKET_OPERATE="aml.menu.backticket";
//	核赔一级岗	aml.undwrt.one
	/**核赔一级操作权限**/
	public static final String DHLP_UNDWRT_ONE_OPERATE="aml.undwrt.one";
//	核赔二级岗	aml.undwrt.two
	/**核赔二级操作权限**/
	public static final String DHLP_UNDWRT_TWO_OPERATE="aml.undwrt.two";
//	核赔三级岗	aml.undwrt.three
	/**核赔三级操作权限**/
	public static final String DHLP_UNDWRT_THREE_OPERATE="aml.undwrt.three";
//	核赔四级岗	aml.undwrt.four
	/**核赔四级操作权限**/
	public static final String DHLP_UNDWRT_FOUR_OPERATE="aml.undwrt.four";

	/**移交审核操作权限**/
	public static final String DHLP_MENU_CLAIMLIST="aml.menu.claimList";
	/**批量导入与批次查询岗*/
	public static final String DHLP_MENU_BATCH = "aml.batchUpload";

//	public static final String DHLP_MENU_STATEMENT="aml.menu.statement";
	/**单证收集按钮*/
	public static final String DHLP_MENU_DOCCOLLECT="aml.menu.doccollect";

	/**医疗审核权限**/
	public static final String DHLP_MENU_MEDICAL="aml.menu.medical";

	/**特殊赔案申请权限**/
	public static final String SPECIAL_CASE_APPLY="aml.specialcase.apply";

	/**特殊赔案分公司审核权限**/
	public static final String SPECIAL_CASE_UNDWRT_QURT="aml.specialcase.undwrtqurt";

	/**特殊赔案总公司权限**/
	public static final String SPECIAL_CASE_UNDWRT_HEAD="aml.specialcase.undwrthead";

	/**垫付权限**/
	public static final String DHLP_MENU_PREPAID="aml.menu.prepaid";

	/**任务监控*/
	public static final String CLAIM_MYPLATFORM_TASKMONITOR = "aml.MyPlatform.TaskMonitor";
	/**报案注销*/
	public static final String REGIST_CANCEL_APPLY = "regist.cancel.apply";
	/**报案注销审核*/
	public static final String REGIST_CANCEL_UNDWRT = "regist.cancel.undwrt";
	/**强制改派权限*/
	public static final String CLAIM_MENU_FORCEASSIGN = "aml.menu.forceassign";
	/**调估申请*/
	public static final String CLAIM_ESTIMATE_APPLY = "aml.estimate.apply";
	/**调估审核*/
	public static final String CLAIM_ESTIMATE_UNDWRT = "aml.estimate.undwrt";
	/**任务查询*/
	public static final String CLAIM_MENU_TASKQUERY = "aml.menu.taskquery";
	/**案件查询*/
	public static final String CLAIM_MENU_CASEQUERY = "aml.menu.casequery";
	/**预报案*/
	public static final String CLAIM_REGIST_PREREGIST = "aml.regist.preRegist";
}
