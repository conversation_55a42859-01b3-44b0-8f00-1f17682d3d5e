package com.sinosoft.power.util;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-08-30 17:17
 */
public class TreeBuilder {

    /**
     * 构造树形结构(递归)
     * @param treeNodes
     * @return
     */
    public static List<TreeNode> build(List<TreeNode> treeNodes) {
        List<TreeNode> trees = new ArrayList<TreeNode>();
        for (TreeNode treeNode : treeNodes) {
            if ("NULL".equals(treeNode.getPid())) {
                trees.add(findChildren(treeNode, treeNodes));
            }
        }
        return trees;
    }

    public static TreeNode findChildren(TreeNode treeNode, List<TreeNode> treeNodes) {
        for (TreeNode it : treeNodes) {
            if (treeNode.getId().equals(it.getPid())) {
                if (treeNode.getChildren() == null) {
                    treeNode.setChildren(new ArrayList<>());
                }
                treeNode.getChildren().add(findChildren(it, treeNodes));
            }
        }
        return treeNode;
    }


}
