package com.sinosoft.power.util;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-08-30 17:16
 */
@Data
public class TreeNode {

    private String id;
    private String pid;
    private String name;
    private List<TreeNode> children;

    public TreeNode(String id, String name, String pid) {
        this.id = id;
        this.name = name;
        this.pid = pid;
    }

    public TreeNode(String id, String name, TreeNode parent) {
        this.id = id;
        this.name = name;
        this.pid = parent.getId();
    }

}
