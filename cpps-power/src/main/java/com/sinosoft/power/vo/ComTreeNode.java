package com.sinosoft.power.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-08-30 16:03
 */
@Data
public class ComTreeNode {

    //机构代码
    @JsonProperty("id")
    private String comCode;

    //机构名称
    @JsonProperty("text")
    private String comCName;

    //上级机构代码
    @JsonIgnore
    private String upperComCode;

    //机构代码路径
    @JsonIgnore
    private String upperPath;

    //是否选择
    private boolean checked;

    //节点状态，open 或 closed
    private String state;

    //机构子节点
    private List<ComTreeNode> children;

}
