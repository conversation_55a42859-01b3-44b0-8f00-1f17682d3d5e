package com.sinosoft.power.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis工具自动生成。表PRPDCOMPANY的VO对象<br/>
 * 对应表名：PRPDCOMPANY
 *
 */
@Data
public class CompanyVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：COMCODE */
	private String comCode;
	/** 对应字段：COMCNAME */
	private String comCName;
	/** 对应字段：COMENAME */
	private String comEName;
	/** 对应字段：ADDRESSCNAME */
	private String addressCName;
	/** 对应字段：ADDRESSENAME */
	private String addressEName;
	/** 对应字段：POSTCODE */
	private String postCode;
	/** 对应字段：PHONENUMBER */
	private String phoneNumber;
	/** 对应字段：TAXNUMBER */
	private String taxNumber;
	/** 对应字段：FAXNUMBER */
	private String faxNumber;
	/** 对应字段：UPPERCOMCODE */
	private String upperComCode;
	/** 对应字段：INSURERNAME */
	private String insurerName;
	/** 对应字段：COMATTRIBUTE */
	private String comAttribute;
	/** 对应字段：COMTYPE */
	private String comType;
	/** 对应字段：COMLEVEL */
	private String comLevel;
	/** 对应字段：MANAGER */
	private String manager;
	/** 对应字段：ACCOUNTLEADER */
	private String accountLeader;
	/** 对应字段：CASHIER */
	private String cashier;
	/** 对应字段：ACCOUNTANT */
	private String accountant;
	/** 对应字段：REMARK */
	private String remark;
	/** 对应字段：NEWCOMCODE */
	private String newComCode;
	/** 对应字段：VALIDSTATUS */
	private String validStatus;
	/** 对应字段：ACNTUNIT */
	private String acntunit;
	/** 对应字段：ARTICLECODE */
	private String articleCode;
	/** 对应字段：ACCCODE */
	private String acccode;
	/** 对应字段：CENTERFLAG */
	private String centerFlag;
	/** 对应字段：OUTERPAYCODE */
	private String outerPayCode;
	/** 对应字段：INNERPAYCODE */
	private String innerPayCode;
	/** 对应字段：FLAG */
	private String flag;
	/** 对应字段：WEBADDRESS */
	private String webAddress;
	/** 对应字段：SERVICEPHONE */
	private String servicePhone;
	/** 对应字段：REPORTPHONE */
	private String reportPhone;
	/** 对应字段：AGENTCODE */
	private String agentCode;
	/** 对应字段：AGREEMENTNO */
	private String agreementNo;
	/** 对应字段：SYSAREACODE */
	private String sysAreaCode;
	/** 对应字段：COMBVISITRATE */
	private String combvisitRate;
	/** 对应字段：PRINTCOMNAME */
	private String printComName;
	/** 对应字段：PRINTADDRESS */
	private String printAddress;
	/** 对应字段：PRINGPOSTCODE */
	private String pringPostCode;
	/** 对应字段：SALESCHANNELCODE */
	private String saleSchannelCode;
	/** 对应字段：COMFLAG */
	private String comFlag;
	/** 对应字段：UPPERPATH */
	private String upperPath;
	/** 对应字段：AREACODE */
	private String areaCode;
	/** 对应字段：INSERTTIMEFORHIS,备注：系统插入时间 */
	private Date insertTimeForHis;
	/** 对应字段：OPERATETIMEFORHIS,备注：系统更新时间 */
	private Date operateTimeForHis;


}
