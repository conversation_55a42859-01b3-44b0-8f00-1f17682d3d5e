package com.sinosoft.power.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.sinosoft.power.common.util.SortOrder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DataTablePageVo {
    /***当前页数***/
    private Integer dataTablePageNum=1;
	/***开始下标***/
	private Integer dataTablePageStart=0;
	/***每页条数**/
	private Integer dataTablePageSize=10;
	/***总条数**/
	@JsonSerialize(using=ToStringSerializer.class)
	private Long iTotalDisplayRecords=0L;
	/***总条数**/
	@JsonSerialize(using=ToStringSerializer.class)
	private Long iTotalRecords=0L;
	/***当前页数据**/
	private List<?> data=new ArrayList();
	/***当前页数据**/
	private String dataString;
	/***当前排序的列**/
	private String orderField;
	/***排序表示（ASC,DESC）**/
	private SortOrder order;
	/***高亮字段**/
	private List<String> highLight;
	
}
