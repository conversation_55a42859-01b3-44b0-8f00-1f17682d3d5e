package com.sinosoft.power.vo;

import lombok.Data;

import java.util.Date;

/**
 * @author: yancun
 * @create: 2019/1/8 19:48
 * @description:用户权限清单导出Vo
 */
@Data
public class ExportUserPowerInfoVo {
    private static final long serialVersionUID = 1L;

    /** 对应字段：USERGRADEID */
    private String userGradeId;
    /** 对应字段：USERCODE */
    private String userCode;
    /** 对应字段：USERNAME */
    private String userName;
    /** 对应字段：COMCODE */
    private String comCode;
    /** 对应字段：COMNAME */
    private String comName;
    /** 对应字段：GRADECNAME */
    private String gradeName;
    /** 对应字段：PERMITCOM */
    private String permitCom;
    /** 对应字段：PERMITCLASS */
    private String permitClass;
    /** 对应字段：creatorcode */
    private String creatorCode;
    /** 对应字段：OPERATETIMEFORHIS */
    private Date insertTimeForHis;
    /** 对应字段：OPERATETIMEFORHIS */
    private String updaterCode;
    /** 对应字段：OPERATETIMEFORHIS */
    private Date operateTimeForHis;


}
