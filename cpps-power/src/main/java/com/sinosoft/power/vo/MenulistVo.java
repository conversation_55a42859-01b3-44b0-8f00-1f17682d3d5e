package com.sinosoft.power.vo;

import lombok.Data;

import java.util.List;

@Data
public class MenulistVo {
	private String name;
	
	private String img;

	private String minImg;

	private String url;

	private String extend;
	
	private List<MenulistVo> childMenu;
	
	public MenulistVo(){}

	public MenulistVo(String name,String img,String minImg,String url,String extend,List<MenulistVo> childMenu){
		this.name = name;
		this.img = img;
		this.minImg = minImg;
		this.url = url;
		this.extend = extend;
		this.childMenu = childMenu;
	}
}
