package com.sinosoft.power.vo;

import lombok.Data;

@Data
public class PowerCheckConfig {
	/* 需要校验的url ，如：/editRegist.do */
	private String url;
	/* 需要校验的功能代码 */
	private String taskcodes;
	/* 数据源，可以是一个hql，得到校验的目标对象 */
	private String dataSrc;
	/* 对象的机构属性 */
	private String comcodeFields;
	/* 对象的险种属性 */
	private String riskcodeFields;
	/* 对象的板块属性 */
	private String platecodeFields;
	/* 对象的mainno属性，用于校验该用户是否参与过流程 */
	private String mainNoFields;
	/* 对象的ignoreFieldName属性，需要忽略的字段名 */
	private String ignoreFieldName;
	/* 对象的ignoreFieldValue属性，需要忽略的字段值 */
	private String ignoreFieldValue;
	/* 忽略标志，如果请求参数中存在忽略标志，不校验 */
	private String ignoreFlag;
}
