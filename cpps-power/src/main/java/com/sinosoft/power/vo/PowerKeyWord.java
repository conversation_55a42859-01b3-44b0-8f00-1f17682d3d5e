package com.sinosoft.power.vo;

import java.io.Serializable;

import lombok.Data;

@Data
public class PowerKeyWord  implements Serializable{
	private static final long serialVersionUID = 1L;

    /**险类*/
    private String classCode;

	/**险种*/
	private String riskCode;
	
	/**板块*/
	private String plateCode;
	
	/**所选机构*/
	private String comCode;

	/**保单归属机构*/
	private String makeCom;
	
	/**功能代码*/
	private String taskCode;
}
