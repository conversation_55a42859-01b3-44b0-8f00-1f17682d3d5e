package com.sinosoft.power.vo;

import lombok.Data;

import java.util.Map;

@Data
public class RequestKeyWordVo {
	private String registNoMapper;
	private String taskNoMapper;
	//是否要进行任务校验
	private Boolean taskFlag;
	//是否要进行功能代码校验
	private Boolean functionFlag;
	//是否要进行数据权限校验
	private Boolean dataFlag;
	/****       TaskCode  url参数Parameter  url参数值***/
	private Map<String,Map<String,String>> taskCode;
	public RequestKeyWordVo(String registNoMapper, String taskNoMapper){
		this.registNoMapper = registNoMapper;
		this.taskNoMapper = taskNoMapper;
		this.taskFlag = true;
		this.functionFlag = false;
		this.dataFlag = false;
	}
	public RequestKeyWordVo(String registNoMapper, String taskNoMapper, Map<String,Map<String,String>> taskCode){
		this.registNoMapper = registNoMapper;
		this.taskNoMapper = taskNoMapper;
		this.taskCode = taskCode;
		this.taskFlag = true;
		this.functionFlag = true;
		this.dataFlag = false;
	}
	public RequestKeyWordVo(String registNoMapper, String taskNoMapper, Map<String,Map<String,String>> taskCode,
                            Boolean dataFlag){
		this.registNoMapper = registNoMapper;
		this.taskNoMapper = taskNoMapper;
		this.taskCode = taskCode;
		this.taskFlag = true;
		this.functionFlag = true;
		this.dataFlag = dataFlag;
	}
	public RequestKeyWordVo(String registNoMapper, String taskNoMapper, Map<String,Map<String,String>> taskCode
			, Boolean taskFlag, Boolean functionFlag, Boolean dataFlag){
		this.registNoMapper = registNoMapper;
		this.taskNoMapper = taskNoMapper;
		this.taskCode = taskCode;
		this.taskFlag = taskFlag;
		this.functionFlag = functionFlag;
		this.dataFlag = dataFlag;
	}
}
