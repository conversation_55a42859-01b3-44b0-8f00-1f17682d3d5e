package com.sinosoft.power.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis工具自动生成。表SAATASK的VO对象<br/>
 * 对应表名：SAATASK
 *
 */
@Data
public class SaaTaskVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：ID */
	private Long id;
	/** 对应字段：SYSTEMCODE */
	private String systemCode;
	/** 对应字段：TASKCODE */
	private String taskCode;
	/** 对应字段：PARENTCODE */
	private String parentCode;
	/** 对应字段：TASKCNAME */
	private String taskCName;
	/** 对应字段：TASKENAME */
	private String taskEName;
	/** 对应字段：URL */
	private String url;
	/** 对应字段：CREATECODE */
	private String createCode;
	/** 对应字段：CREATETIME */
	private Date createTime;
	/** 对应字段：VALIDFLAG */
	private String validFlag;
	/** 对应字段：REMARK */
	private String remark;
	/** 对应字段：FLAG */
	private String flag;
	/** 对应字段：SYNFLAG */
	private String synFlag;

}
