package com.sinosoft.power.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

//import io.swagger.annotations.ApiModel;

/**
 * 通过ins-framework-mybatis工具自动生成。表sys_user的VO对象<br/>
 * 对应表名：sys_user,备注：系统用户表
 */
@Data
//@ApiModel(value = "系统用户对象")
public class SysPowerUserVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 对应字段：Id
	 */
	private Long id;
	/**
	 * 对应字段：user_code
	 */
	private String userCode;
	/**
	 * 对应字段：user_name
	 */
	private String userName;
	/**
	 * 对应字段：COMCODE
	 */
	private String comCode;
	/**
	 * 机构
	 */
	private String makeCom;
	/**
	 * 机构名
	 */
	private String cMakeCom;
	/**
	 * 对应字段：email,备注：电子邮件
	 */
	private String email;
	/**
	 * 对应字段：MOBILE
	 */
	private String mobile;
	/**
	 * 对应字段：PHONE
	 */
	private String phone;
	/**
	 * 对应字段：mobile_phone,备注：移动电话
	 */
	private String mobilePhone;
	/**
	 * 对应字段：password
	 */
	private String password;
	/**
	 * 对应字段：salt
	 */
	private String salt;
	/**
	 * 对应字段：second_validate
	 */
	private String secondValidate;
	/**
	 * 对应字段：ga_code
	 */
	private String gaCode;
	/**
	 * 对应字段：question
	 */
	private String question;
	/**
	 * 对应字段：answer
	 */
	private String answer;
	/**
	 * 对应字段：sex,备注：性别
	 */
	private String sex;
	/**
	 * 对应字段：birthday,备注：生日
	 */
	private Date birthday;
	/**
	 * 对应字段：reg_time
	 */
	private Date regTime;
	/**
	 * 对应字段：last_login_failed
	 */
	private Long lastLoginFailed;
	/**
	 * 对应字段：last_time
	 */
	private Date lastTime;
	/**
	 * 对应字段：last_ip
	 */
	private String lastIp;
	/**
	 * 对应字段：msn
	 */
	private String msn;
	/**
	 * 对应字段：qq
	 */
	private String qq;
	/**
	 * 对应字段：office_phone
	 */
	private String officePhone;
	/**
	 * 对应字段：home_phone
	 */
	private String homePhone;
	/**
	 * 对应字段：checked
	 */
	private String checked;
	/**
	 * 对应字段：age
	 */
	private String age;
	/**
	 * 对应字段：Operators
	 */
	private String operators;
	/**
	 * 对应字段：PASSWORD_SET_DATE
	 */
	private Date passwordSetDate;
	/**
	 * 对应字段：PASSWORD_EXPIRE_DATE
	 */
	private Date passwordExpireDate;
	/**
	 * 对应字段：ADDRESS
	 */
	private String address;
	/**
	 * 对应字段：POSTCODE
	 */
	private String postcode;
	/**
	 * 对应字段：VALID_STATUS
	 */
	private String validStatus;
	/**
	 * 对应字段：Version
	 */
	private Long version;
	/**
	 * 对应字段：Insert_Time_For_His
	 */
	private Date insertTimeForHis;
	/**
	 * 对应字段：Operate_Time_For_His
	 */
	private Date operateTimeForHis;
	/**
	 * 对应字段: taskId
	 */
	private String taskId;
	/**
	 * 对应字段: gradeId
	 */
	private String gradeId;
	/**
	 * 提供理赔作业电话
	 */
	private String claimWorkTel;

	/** 对应字段：DEFAULT_GRADEID,备注：默认角色 */
	private Long defaultGradeid;
	/** 对应字段：DEFAULT_COM,备注：默认操作机构 */
	private String defaultCom;
}
