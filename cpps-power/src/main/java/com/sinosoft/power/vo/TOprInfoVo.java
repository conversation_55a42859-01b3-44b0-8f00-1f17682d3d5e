package com.sinosoft.power.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，表T_OPR_INFO的VO对象<br/>
 * 对应表名：T_OPR_INFO,备注：用户信息表
 *
 */
@Data
public class TOprInfoVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：USER_CODE,备注：人员代码 */
	private String userCode;
	/** 对应字段：USER_NAME,备注：人员名称 */
	private String userName;
	/** 对应字段：USER_ENAME,备注：属性员工英文名称 */
	private String userEname;
	/** 对应字段：PASSWORD,备注：密码 */
	private String password;
	/** 对应字段：SEAL,备注：属性印鉴 */
	private String seal;
	/** 对应字段：PASSWORD_SET_DATE,备注：密码设置时间 */
	private Date passwordSetDate;
	/** 对应字段：PASSWORD_EXPIRE_DATE,备注：密码失效时间 */
	private Date passwordExpireDate;
	/** 对应字段：COMCODE,备注：审核部门 */
	private String comCode;
	/** 对应字段：MAKECOM,备注：出单机构代码 */
	private String makeCom;
	/** 对应字段：ACCOUNT_CODE,备注：账号代码 */
	private String accountCode;
	/** 对应字段：PHONE,备注：电话号码 */
	private String phone;
	/** 对应字段：MOBILE,备注：手机号码 */
	private String mobile;
	/** 对应字段：ADDRESS,备注：地址 */
	private String address;
	/** 对应字段：POSTCODE,备注：邮政编码 */
	private String postCode;
	/** 对应字段：EMAIL,备注：邮箱 */
	private String email;
	/** 对应字段：USER_FLAG,备注：员工标志 */
	private String userFlag;
	/** 对应字段：NEW_USER_CODE,备注：新人员代码 */
	private String newUserCode;
	/** 对应字段：VALID_STATUS,备注：有效标志 */
	private String validStatus;
	/** 对应字段：FLAG,备注：标志 */
	private String flag;
	/** 对应字段：PASSWORD1,备注：密码1 */
	private String password1;
	/** 对应字段：PASSWORD2,备注：密码2 */
	private String password2;
	/** 对应字段：PASSWORD3,备注：密码3 */
	private String password3;
	/** 对应字段：PASSWORD4,备注：密码4 */
	private String password4;
	/** 对应字段：DEFEAT_LOGTIMES,备注：错误登录次数 */
	private BigDecimal defeatLogtimes;
	/** 对应字段：LOCKTIME,备注：锁定时间 */
	private String locktime;
	/** 对应字段：EXTRANETSINGLEFLAG,备注：外网出单标志 */
	private String extranetsingleflag;
	/** 对应字段：AGENT_ACCOUNT_FLAG,备注：代理账户标志 */
	private String agentAccountFlag;
	/** 对应字段：VOCATION_CODE,备注：执业证号 */
	private String vocationCode;
	/** 对应字段：CREATE_DATE,备注：系统人员创建时间 */
	private Date createDate;
	/** 对应字段：PARTIN_TIME,备注：进本公司工作时间 */
	private Date partinTime;
	/** 对应字段：SALE_SEQ_FLAG,备注：是否销售序列人员 */
	private String saleSeqFlag;
	/** 对应字段：POSITION_CODE,备注：岗位代码 */
	private String positionCode;
	/** 对应字段：POSITION_NAME,备注：岗位名称 */
	private String positionName;
	/** 对应字段：POSITION_REMARK,备注：岗位描述 */
	private String positionRemark;
	/** 对应字段：TITLE_CODE,备注：职衔代码 */
	private String titleCode;
	/** 对应字段：TITLE_NAME,备注：职衔名称 */
	private String titleName;
	/** 对应字段：IDENTIFY_NUMBER,备注：身份证号 */
	private String identifyNumber;
	/** 对应字段：DEFAULT_GRADEID,备注：默认角色 */
	private Long defaultGradeid;
	/** 对应字段：DEFAULT_COM,备注：默认操作机构 */
	private String defaultCom;

}
