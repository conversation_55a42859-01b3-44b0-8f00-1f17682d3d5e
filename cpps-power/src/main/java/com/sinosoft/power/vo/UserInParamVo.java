package com.sinosoft.power.vo;

import lombok.Data;

@Data
public class UserInParamVo {
    /**
     * 用户代码
     */
    private String userCode;
    /**
     * 用户名称
     */
    private String userName;

    /**
     * 归属机构
     */
    private String comCode;

    /**
     * 有效状态
     */
    private String validStatus;

    /**
     * 理赔工作电话
     */
    private String claimWorkTel;
    /**
     * 电子邮箱地址
     */
    private String emailAddress;

    /**
     * 身份证号码
     */
    private String identifyNumber;
    /**
     * 固定电话
     */
    private String mobile;
    /**
     * 手机
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 地址
     */
    private String address;

    /**
     * 任务id  add zuoyanhua 20190108
     */
    private String taskId;
    /**
     * 岗位id add zuoyanhua 20190108
     */
    private String gradeId;
}
