<?xml version="1.0" encoding="UTF-8"?>

<!-- Logback configuration. See http://logback.qos.ch/manual/index.html -->
<configuration scan="true" scanPeriod="10 seconds">
	<!--<include resource="org/springframework/boot/logging/logback/base.xml" 
		/> -->

	<!--定义日志文件的存储地址和前缀名 -->
	<property name="LOG_NAME" value="power_rule" />
	<property name="LOG_PREFIX" value="${LOG_ROOT:-logs}/${springAppName}" />

    <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%-5p] [%F:%L] - %m%n</pattern>
        </encoder>
    </appender>

    <!-- 一般信息按照每天生成日志文件 -->
    <appender name="fileInfoLog"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PREFIX}/power/${LOG_NAME}.log</File>
        <!-- 过滤日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>  <!-- 如果命中就禁止这条日志 -->
            <onMismatch>ACCEPT</onMismatch>  <!-- 如果没有命中就使用这条规则 -->
        </filter>
        <rollingPolicy
                class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 每天一归档 -->
            <fileNamePattern>${LOG_PREFIX}/power/${LOG_NAME}_%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
        <encoder>
            <Pattern>%d{yyyy-MM-dd HH:mm:ss} [%-5p] [%F:%L] - %m%n</Pattern>
        </encoder>
    </appender>

    <!-- 一般信息按照每天生成日志文件 -->
    <appender name="fileErrorLog"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PREFIX}/power/${LOG_NAME}_error.log</File>
        <!-- 过滤日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy
                class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 每天一归档 -->
            <fileNamePattern>${LOG_PREFIX}/power/${LOG_NAME}_error_%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
        <encoder>
            <Pattern>%d{yyyy-MM-dd HH:mm:ss} [%-5p] [%F:%L] - %m%n</Pattern>
        </encoder>
    </appender>

    <!-- 打印JDBC日志 仅用于本地测试-->
    <!--<logger name="com.sinosoft.bpm.rule.savelog.log" level="DEBUG" additivity="false">
        <appender-ref ref="fileInfoLog"/>
        <appender-ref ref="consoleLog"/>
    </logger>-->

    <!-- 打印数据源日志 仅用于本地测试-->
    <!--<logger name="org.springframework.jdbc.datasource" level="DEBUG" additivity="false">
        <appender-ref ref="fileInfoLog"/>
        <appender-ref ref="consoleLog"/>
    </logger>-->

    <!-- 日志输出级别 -->
    <root level="INFO">
        <appender-ref ref="fileInfoLog"/>
        <appender-ref ref="fileErrorLog"/>
        &lt;!&ndash;仅用于本地测试&ndash;&gt;
        <appender-ref ref="consoleLog"/>
    </root>
</configuration>