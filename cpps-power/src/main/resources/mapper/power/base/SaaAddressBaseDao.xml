<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaAddressDao">
    <!-- 默认开启二级缓存,使用Least Recently Used（LRU，最近最少使用的）算法来收回 -->
    <!-- <cache/> -->
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.sinosoft.power.po.PrpDAddress">
        <id column="CODE" property="code"/>
        <result column="NAME" property="name"/>
        <result column="PARENT" property="parent"/>
        <result column="VALIDSTATUS" property="validStatus"/>
    </resultMap>


    <!-- 通用查询结果列-->
    <sql id="BaseColumnList">
		 CODE, NAME, PARENT, VALIDSTATUS AS validStatus
	</sql>

    <!-- 通用查询结果列-->
    <sql id="BaseStabdardColumnList">
		CODETYPE, CODECODE, CODECNAME,CODEENAME,PARENTCODE,CHILDCODE, VALIDSTATUS AS validStatus
	</sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="BaseSelectByEntityWhere">
        <if test="code != null" >
            and CODE = #{code}
        </if>
        <if test="name != null" >
            and NAME = #{name}
        </if>
        <if test="parent != null" >
            and PARENT = #{parent}
        </if>
        <if test="validStatus != null" >
            and VALIDSTATUS = #{validStatus}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="BaseSelectByEntity">
        select
        <include refid="BaseColumnList" />
        from PRPDADDRESS
        <where>
            <include refid="BaseSelectByEntityWhere" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from PRPDADDRESS
        where CODE = #{param1}
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from PRPDADDRESS
        where CODE in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap" parameterType="com.sinosoft.power.po.PrpDAddress">
        <include refid="BaseSelectByEntity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey" parameterType="map">
		delete from PRPDADDRESS
		where CODE = #{param1}
	</delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys" parameterType="map">
        delete from PRPDADDRESS
        where CODE in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" parameterType="com.sinosoft.power.po.PrpDAddress">
		insert into PRPDADDRESS (CODE, NAME, PARENT, VALIDSTATUS)
		values(#{code}, #{name}, #{parent}, #{validStatus})
	</insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" parameterType="com.sinosoft.power.po.PrpDAddress">
        insert into PRPDADDRESS
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="code != null" >
                CODE,
            </if>
            <if test="name != null" >
                NAME,
            </if>
            <if test="parent != null" >
                PARENT,
            </if>
            <if test="validStatus != null" >
                VALIDSTATUS,
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="code != null" >
            #{code},
        </if>
        <if test="name != null" >
            #{name},
        </if>
        <if test="parent != null" >
            #{parent},
        </if>
        <if test="validStatus != null" >
            #{validStatus},
        </if>
    </trim>
    </insert>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" parameterType="com.sinosoft.power.po.PrpDAddress">
        update PRPDADDRESS
        <set>
            <if test="name != null" >
                NAME=#{name},
            </if>
            <if test="parent != null" >
                PARENT=#{parent},
            </if>
            <if test="validStatus != null" >
                VALIDSTATUS=#{validStatus},
            </if>
        </set>
        where CODE = #{code}
    </update>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.power.po.PrpDAddress">
		update PRPDADDRESS
		set NAME=#{name},
			PARENT=#{parent},
			VALIDSTATUS=#{validStatus}
		where CODE = #{code}
	</update>

</mapper>
