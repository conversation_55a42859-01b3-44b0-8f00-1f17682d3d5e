<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaBankDao">
    <!-- 默认开启二级缓存,使用Least Recently Used（LRU，最近最少使用的）算法来收回 -->
    <!-- <cache/> -->
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.sinosoft.power.po.PrpDBank">
        <id column="BANKCODE" property="bankCode"/>
        <result column="BANKNAME" property="bankName"/>
        <result column="INSERTTIMEFORHIS" property="insertTimeForHis"/>
        <result column="REMARK" property="remark"/>
        <result column="BANKUID" property="bankUid"/>
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="BaseColumnList">
		 BANKCODE AS bankCode, BANKNAME AS bankName, INSERTTIMEFORHIS AS insertTimeForHis,  REMARK AS remark,
		 BANKUID AS bankUid
	</sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="BaseSelectByEntityWhere">
        <if test="bankCode != null" >
            and BANKCODE = #{bankCode}
        </if>
        <if test="bankName != null" >
            and BANKNAME = #{bankName}
        </if>
        <if test="insertTimeForHis != null" >
            and INSERTTIMEFORHIS = #{insertTimeForHis}
        </if>
        <if test="remark != null" >
            and REMARK = #{remark}
        </if>
        <if test="bankUid != null" >
            and BANKUID = #{bankUid}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="BaseSelectByEntity">
        select
        <include refid="BaseColumnList" />
        from PRPDBANK
        <where>
            <include refid="BaseSelectByEntityWhere" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from PRPDBANK
        where BANKUID = #{param1}
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from PRPDBANK
        where BANKUID in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap" parameterType="com.sinosoft.power.po.PrpDBank">
        <include refid="BaseSelectByEntity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey" parameterType="map">
		delete from PRPDBANK
		where BANKUID = #{param1}
	</delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys" parameterType="map">
        delete from PRPDBANK
        where BANKUID in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" parameterType="com.sinosoft.power.po.PrpDBank">
		insert into PRPDBANK (BANKCODE, BANKNAME, INSERTTIMEFORHIS, REMARK, BANKUID)
		values(#{bankCode}, #{bankName}, #{insertTimeForHis}, #{remark}, #{bankUid})
	</insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" parameterType="com.sinosoft.power.po.PrpDBank">
        insert into PRPDBANK
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="bankCode != null" >
                BANKCODE,
            </if>
            <if test="bankName != null" >
                BANKNAME,
            </if>
            <if test="insertTimeForHis != null" >
                INSERTTIMEFORHIS,
            </if>
            <if test="remark != null" >
                REMARK,
            </if>
            <if test="bankUid != null" >
                BANKUID,
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="bankCode != null" >
            #{bankCode},
        </if>
        <if test="bankName != null" >
            #{bankName},
        </if>
        <if test="insertTimeForHis != null" >
            #{insertTimeForHis},
        </if>
        <if test="remark != null" >
            #{remark},
        </if>
        <if test="bankUid != null" >
            #{bankUid},
        </if>
    </trim>
    </insert>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" parameterType="com.sinosoft.power.po.PrpDBank">
        update PRPDBANK
        <set>
            <if test="bankName != null" >
                BANKNAME=#{bankName},
            </if>
            <if test="insertTimeForHis != null" >
                INSERTTIMEFORHIS = #{insertTimeForHis}
            </if>
            <if test="remark != null" >
                REMARK = #{remark}
            </if>
            <if test="bankCode != null" >
                BANKCODE = #{bankCode}
            </if>
        </set>
        where BANKUID = #{bankUid}
    </update>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.power.po.PrpDBank">
		update PRPDBANK
		set BANKNAME=#{bankName},
			BANKCODE = #{bankCode},
			INSERTTIMEFORHIS = #{insertTimeForHis},
			REMARK = #{remark}
		where BANKUID = #{bankUid}
	</update>

</mapper>
