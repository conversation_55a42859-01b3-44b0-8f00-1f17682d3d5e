<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaCommonDataDao">
    <!-- 默认开启二级缓存,使用Least Recently Used（LRU，最近最少使用的）算法来收回 -->
    <!-- <cache/> -->
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.sinosoft.power.po.CommonData">
        <id column="ID" property="id"/>
        <result column="DATATYPE" property="dataType"/>
        <result column="DATACODE" property="dataCode"/>
        <result column="DATANAME" property="dataName"/>
        <result column="PRETYPE" property="preType"/>
        <result column="PRECODE" property="preCode"/>
        <result column="MAPPERDATACODE" property="mapperDataCode"/>
        <result column="VALIDSTATUS" property="validStatus"/>
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="BaseColumnList">
		 ID, DATATYPE AS dataType, DATACODE AS dataCode, DATANAME AS dataName, PRETYPE AS preType,
		 PRECODE AS preCode,MAPPERDATACODE AS mapperDataCode,VALIDSTATUS AS validStatus
	</sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="BaseSelectByEntityWhere">
        <if test="id != null" >
            and ID = #{id}
        </if>
        <if test="dataType != null" >
            and DATATYPE = #{dataType}
        </if>
        <if test="dataCode != null" >
            and DATACODE = #{dataCode}
        </if>
        <if test="dataName != null" >
            and DATANAME = #{dataName}
        </if>
        <if test="preType != null" >
            and PRETYPE = #{preType}
        </if>
        <if test="preCode != null" >
            and PRECODE = #{preCode}
        </if>
        <if test="mapperDataCode != null" >
            and MAPPERDATACODE = #{mapperDataCode}
        </if>
        <if test="validStatus != null" >
            and VALIDSTATUS = #{validStatus}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="BaseSelectByEntity">
        select
        <include refid="BaseColumnList" />
        from COMMONDATA
        <where>
            <include refid="BaseSelectByEntityWhere" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from COMMONDATA
        where ID = #{param1}
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from COMMONDATA
        where ID in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap" parameterType="com.sinosoft.power.po.CommonData">
        <include refid="BaseSelectByEntity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey" parameterType="map">
		delete from COMMONDATA
		where ID = #{param1}
	</delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys" parameterType="map">
        delete from COMMONDATA
        where ID in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" parameterType="com.sinosoft.power.po.CommonData">
		insert into COMMONDATA (ID, DATATYPE, DATACODE, DATANAME, PRETYPE,
			PRECODE)
		values(#{id}, #{dataType}, #{dataCode}, #{dataName}, #{preType},
			#{preCode})
	</insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" parameterType="com.sinosoft.power.po.CommonData">
        insert into COMMONDATA
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                ID,
            </if>
            <if test="dataType != null" >
                DATATYPE,
            </if>
            <if test="dataCode != null" >
                DATACODE,
            </if>
            <if test="dataName != null" >
                DATANAME,
            </if>
            <if test="preType != null" >
                PRETYPE,
            </if>
            <if test="preCode != null" >
                PRECODE,
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="id != null" >
            #{id},
        </if>
        <if test="dataType != null" >
            #{dataType},
        </if>
        <if test="dataCode != null" >
            #{dataCode},
        </if>
        <if test="dataName != null" >
            #{dataName},
        </if>
        <if test="preType != null" >
            #{preType},
        </if>
        <if test="preCode != null" >
            #{preCode},
        </if>
    </trim>
    </insert>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" parameterType="com.sinosoft.power.po.CommonData">
        update COMMONDATA
        <set>
            <if test="dataType != null" >
                DATATYPE=#{dataType},
            </if>
            <if test="dataCode != null" >
                DATACODE=#{dataCode},
            </if>
            <if test="dataName != null" >
                DATANAME=#{dataName},
            </if>
            <if test="preType != null" >
                PRETYPE=#{preType},
            </if>
            <if test="preCode != null" >
                PRECODE=#{preCode},
            </if>
        </set>
        where ID = #{id}
    </update>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.power.po.CommonData">
		update COMMONDATA
		set DATATYPE=#{dataType},
			DATACODE=#{dataCode},
			DATANAME=#{dataName},
			PRETYPE=#{preType},
			PRECODE=#{preCode}
		where ID = #{id}
	</update>

</mapper>