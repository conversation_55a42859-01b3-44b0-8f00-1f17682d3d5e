<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaCompanyDao">
	<!-- 默认开启二级缓存,使用Least Recently Used（LRU，最近最少使用的）算法来收回 -->
	<!-- <cache/> -->
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.sinosoft.power.po.PrpDCompany">
		<id column="COMCODE" property="companyCode"/>
		<result column="COMCNAME" property="comCName"/>
		<result column="COMENAME" property="comEName"/>
		<result column="ADDRESSCNAME" property="addressCName"/>
		<result column="ADDRESSENAME" property="addressEName"/>
		<result column="POSTCODE" property="postCode"/>
		<result column="PHONENUMBER" property="phoneNumber"/>
		<result column="TAXNUMBER" property="taxNumber"/>
		<result column="FAXNUMBER" property="faxNumber"/>
		<result column="UPPERCOMCODE" property="upperComCode"/>
		<result column="INSURERNAME" property="insurerName"/>
		<result column="COMATTRIBUTE" property="comAttribute"/>
		<result column="COMTYPE" property="comType"/>
		<result column="COMLEVEL" property="comLevel"/>
		<result column="MANAGER" property="manager"/>
		<result column="ACCOUNTLEADER" property="accountLeader"/>
		<result column="CASHIER" property="cashier"/>
		<result column="ACCOUNTANT" property="accountant"/>
		<result column="REMARK" property="remark"/>
		<result column="NEWCOMCODE" property="newComCode"/>
		<result column="VALIDSTATUS" property="validStatus"/>
		<result column="ACNTUNIT" property="acntunit"/>
		<result column="ARTICLECODE" property="articleCode"/>
		<result column="ACCCODE" property="acccode"/>
		<result column="CENTERFLAG" property="centerFlag"/>
		<result column="OUTERPAYCODE" property="outerPayCode"/>
		<result column="INNERPAYCODE" property="innerPayCode"/>
		<result column="FLAG" property="flag"/>
		<result column="WEBADDRESS" property="webAddress"/>
		<result column="SERVICEPHONE" property="servicePhone"/>
		<result column="REPORTPHONE" property="reportPhone"/>
		<result column="AGENTCODE" property="agentCode"/>
		<result column="AGREEMENTNO" property="agreementNo"/>
		<result column="SYSAREACODE" property="sysAreaCode"/>
		<result column="COMBVISITRATE" property="combvisitRate"/>
		<result column="PRINTCOMNAME" property="printComName"/>
		<result column="PRINTADDRESS" property="printAddress"/>
		<result column="PRINGPOSTCODE" property="pringPostCode"/>
		<result column="SALESCHANNELCODE" property="saleSchannelCode"/>
		<result column="INSERTTIMEFORHIS" property="insertTimeForHis"/>
		<result column="OPERATETIMEFORHIS" property="operateTimeForHis"/>
		<result column="COMFLAG" property="comFlag"/>
		<result column="UPPERPATH" property="upperPath"/>
		<result column="AREACODE" property="areaCode"/>
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="BaseColumnList">
		COMCODE AS companyCode, COMCNAME AS comCName, COMENAME AS comEName, ADDRESSCNAME AS addressCName,
		ADDRESSENAME AS addressEName, POSTCODE AS postCode, PHONENUMBER AS phoneNumber, TAXNUMBER AS taxNumber, FAXNUMBER AS faxNumber,
		UPPERCOMCODE AS upperComCode, INSURERNAME AS insurerName, COMATTRIBUTE AS comAttribute, COMTYPE AS comType, COMLEVEL AS comLevel,
		MANAGER, ACCOUNTLEADER AS accountLeader, CASHIER, ACCOUNTANT, REMARK,
		NEWCOMCODE AS newComCode, VALIDSTATUS AS validStatus, ACNTUNIT, ARTICLECODE AS articleCode, ACCCODE,
		CENTERFLAG AS centerFlag, OUTERPAYCODE AS outerPayCode, INNERPAYCODE AS innerPayCode, FLAG, WEBADDRESS AS webAddress,
		SERVICEPHONE AS servicePhone, REPORTPHONE AS reportPhone, AGENTCODE AS agentCode, AGREEMENTNO AS agreementNo, SYSAREACODE AS sysAreaCode,
		COMBVISITRATE AS combvisitRate, PRINTCOMNAME AS printComName, PRINTADDRESS AS printAddress, PRINGPOSTCODE AS pringPostCode, SALESCHANNELCODE AS saleSchannelCode,
		INSERTTIMEFORHIS AS insertTimeForHis, OPERATETIMEFORHIS AS operateTimeForHis,COMFLAG AS comFlag, UPPERPATH AS upperPath, AREACODE AS areaCode
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="BaseSelectByEntityWhere">
		<if test="companyCode != null" >
			and COMCODE = #{companyCode}
		</if>
		<if test="comCName != null" >
			and COMCNAME = #{comCName}
		</if>
		<if test="comEName != null" >
			and COMENAME = #{comEName}
		</if>
		<if test="addressCName != null" >
			and ADDRESSCNAME = #{addressCName}
		</if>
		<if test="addressEName != null" >
			and ADDRESSENAME = #{addressEName}
		</if>
		<if test="postCode != null" >
			and POSTCODE = #{postCode}
		</if>
		<if test="phoneNumber != null" >
			and PHONENUMBER = #{phoneNumber}
		</if>
		<if test="taxNumber != null" >
			and TAXNUMBER = #{taxNumber}
		</if>
		<if test="faxNumber != null" >
			and FAXNUMBER = #{faxNumber}
		</if>
		<if test="upperComCode != null" >
			and UPPERCOMCODE = #{upperComCode}
		</if>
		<if test="insurerName != null" >
			and INSURERNAME = #{insurerName}
		</if>
		<if test="comAttribute != null" >
			and COMATTRIBUTE = #{comAttribute}
		</if>
		<if test="comType != null" >
			and COMTYPE = #{comType}
		</if>
		<if test="comLevel != null" >
			and COMLEVEL = #{comLevel}
		</if>
		<if test="manager != null" >
			and MANAGER = #{manager}
		</if>
		<if test="accountLeader != null" >
			and ACCOUNTLEADER = #{accountLeader}
		</if>
		<if test="cashier != null" >
			and CASHIER = #{cashier}
		</if>
		<if test="accountant != null" >
			and ACCOUNTANT = #{accountant}
		</if>
		<if test="remark != null" >
			and REMARK = #{remark}
		</if>
		<if test="newComCode != null" >
			and NEWCOMCODE = #{newComCode}
		</if>
		<if test="validStatus != null" >
			and VALIDSTATUS = #{validStatus}
		</if>
		<if test="acntunit != null" >
			and ACNTUNIT = #{acntunit}
		</if>
		<if test="articleCode != null" >
			and ARTICLECODE = #{articleCode}
		</if>
		<if test="acccode != null" >
			and ACCCODE = #{acccode}
		</if>
		<if test="centerFlag != null" >
			and CENTERFLAG = #{centerFlag}
		</if>
		<if test="outerPayCode != null" >
			and OUTERPAYCODE = #{outerPayCode}
		</if>
		<if test="innerPayCode != null" >
			and INNERPAYCODE = #{innerPayCode}
		</if>
		<if test="flag != null" >
			and FLAG = #{flag}
		</if>
		<if test="webAddress != null" >
			and WEBADDRESS = #{webAddress}
		</if>
		<if test="servicePhone != null" >
			and SERVICEPHONE = #{servicePhone}
		</if>
		<if test="reportPhone != null" >
			and REPORTPHONE = #{reportPhone}
		</if>
		<if test="agentCode != null" >
			and AGENTCODE = #{agentCode}
		</if>
		<if test="agreementNo != null" >
			and AGREEMENTNO = #{agreementNo}
		</if>
		<if test="sysAreaCode != null" >
			and SYSAREACODE = #{sysAreaCode}
		</if>
		<if test="combvisitRate != null" >
			and COMBVISITRATE = #{combvisitRate}
		</if>
		<if test="printComName != null" >
			and PRINTCOMNAME = #{printComName}
		</if>
		<if test="printAddress != null" >
			and PRINTADDRESS = #{printAddress}
		</if>
		<if test="pringPostCode != null" >
			and PRINGPOSTCODE = #{pringPostCode}
		</if>
		<if test="saleSchannelCode != null" >
			and SALESCHANNELCODE = #{saleSchannelCode}
		</if>
		<if test="insertTimeForHis != null" >
			and INSERTTIMEFORHIS = #{insertTimeForHis}
		</if>
		<if test="operateTimeForHis != null" >
			and OPERATETIMEFORHIS = #{operateTimeForHis}
		</if>
		<if test="comFlag != null" >
			and COMFLAG = #{comFlag}
		</if>
		<if test="upperPath != null" >
			and UPPERPATH = #{upperPath}
		</if>
		<if test="areaCode != null" >
			and AREACODE = #{areaCode}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="BaseSelectByEntity">
		select
		<include refid="BaseColumnList" />
		from PRPDCOMPANY
		<where>
			<include refid="BaseSelectByEntityWhere" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="BaseColumnList" />
		from PRPDCOMPANY
		where COMCODE = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="BaseColumnList" />
		from PRPDCOMPANY
		where COMCODE in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.sinosoft.power.po.PrpDCompany">
		<include refid="BaseSelectByEntity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from PRPDCOMPANY
		where COMCODE = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from PRPDCOMPANY
		where COMCODE in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.sinosoft.power.po.PrpDCompany">
		insert into PRPDCOMPANY (COMCODE, COMCNAME, COMENAME, ADDRESSCNAME,
								 ADDRESSENAME, POSTCODE, PHONENUMBER, TAXNUMBER, FAXNUMBER,
								 UPPERCOMCODE, INSURERNAME, COMATTRIBUTE, COMTYPE, COMLEVEL,
								 MANAGER, ACCOUNTLEADER, CASHIER, ACCOUNTANT, REMARK,
								 NEWCOMCODE, VALIDSTATUS, ACNTUNIT, ARTICLECODE, ACCCODE,
								 CENTERFLAG, OUTERPAYCODE, INNERPAYCODE, FLAG, WEBADDRESS,
								 SERVICEPHONE, REPORTPHONE, AGENTCODE, AGREEMENTNO, SYSAREACODE,
								 COMBVISITRATE, PRINTCOMNAME, PRINTADDRESS, PRINGPOSTCODE, SALESCHANNELCODE,
								 COMFLAG,UPPERPATH,AREACODE
		)
		values(#{companyCode}, #{comCName}, #{comEName}, #{addressCName},
						   #{addressEName}, #{postCode}, #{phoneNumber}, #{taxNumber}, #{faxNumber},
						   #{upperComCode}, #{insurerName}, #{comAttribute}, #{comType}, #{comLevel},
															#{manager}, #{accountLeader}, #{cashier}, #{accountant}, #{remark},
															#{newComCode}, #{validStatus}, #{acntunit}, #{articleCode}, #{acccode},
																						   #{centerFlag}, #{outerPayCode}, #{innerPayCode}, #{flag}, #{webAddress},
																						   #{servicePhone}, #{reportPhone}, #{agentCode}, #{agreementNo}, #{sysAreaCode},
																															#{combvisitRate}, #{printComName}, #{printAddress}, #{pringPostCode}, #{saleSchannelCode},
																															#{comFlag},#{upperPath},#{areaCode}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.sinosoft.power.po.PrpDCompany">
		insert into PRPDCOMPANY
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="companyCode != null" >
				COMCODE,
			</if>
			<if test="comCName != null" >
				COMCNAME,
			</if>
			<if test="comEName != null" >
				COMENAME,
			</if>
			<if test="addressCName != null" >
				ADDRESSCNAME,
			</if>
			<if test="addressEName != null" >
				ADDRESSENAME,
			</if>
			<if test="postCode != null" >
				POSTCODE,
			</if>
			<if test="phoneNumber != null" >
				PHONENUMBER,
			</if>
			<if test="taxNumber != null" >
				TAXNUMBER,
			</if>
			<if test="faxNumber != null" >
				FAXNUMBER,
			</if>
			<if test="upperComCode != null" >
				UPPERCOMCODE,
			</if>
			<if test="insurerName != null" >
				INSURERNAME,
			</if>
			<if test="comAttribute != null" >
				COMATTRIBUTE,
			</if>
			<if test="comType != null" >
				COMTYPE,
			</if>
			<if test="comLevel != null" >
				COMLEVEL,
			</if>
			<if test="manager != null" >
				MANAGER,
			</if>
			<if test="accountLeader != null" >
				ACCOUNTLEADER,
			</if>
			<if test="cashier != null" >
				CASHIER,
			</if>
			<if test="accountant != null" >
				ACCOUNTANT,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="newComCode != null" >
				NEWCOMCODE,
			</if>
			<if test="validStatus != null" >
				VALIDSTATUS,
			</if>
			<if test="acntunit != null" >
				ACNTUNIT,
			</if>
			<if test="articleCode != null" >
				ARTICLECODE,
			</if>
			<if test="acccode != null" >
				ACCCODE,
			</if>
			<if test="centerFlag != null" >
				CENTERFLAG,
			</if>
			<if test="outerPayCode != null" >
				OUTERPAYCODE,
			</if>
			<if test="innerPayCode != null" >
				INNERPAYCODE,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="webAddress != null" >
				WEBADDRESS,
			</if>
			<if test="servicePhone != null" >
				SERVICEPHONE,
			</if>
			<if test="reportPhone != null" >
				REPORTPHONE,
			</if>
			<if test="agentCode != null" >
				AGENTCODE,
			</if>
			<if test="agreementNo != null" >
				AGREEMENTNO,
			</if>
			<if test="sysAreaCode != null" >
				SYSAREACODE,
			</if>
			<if test="combvisitRate != null" >
				COMBVISITRATE,
			</if>
			<if test="printComName != null" >
				PRINTCOMNAME,
			</if>
			<if test="printAddress != null" >
				PRINTADDRESS,
			</if>
			<if test="pringPostCode != null" >
				PRINGPOSTCODE,
			</if>
			<if test="saleSchannelCode != null" >
				SALESCHANNELCODE,
			</if>
			<if test="insertTimeForHis != null" >
				INSERTTIMEFORHIS,
			</if>
			<if test="operateTimeForHis != null" >
				OPERATETIMEFORHIS,
			</if>
			<if test="comFlag != null" >
				COMFLAG,
			</if>
			<if test="upperPath != null" >
				UPPERPATH,
			</if>
			<if test="areaCode != null" >
				AREACODE,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
		<if test="companyCode != null" >
			#{companyCode},
		</if>
		<if test="comCName != null" >
			#{comCName},
		</if>
		<if test="comEName != null" >
			#{comEName},
		</if>
		<if test="addressCName != null" >
			#{addressCName},
		</if>
		<if test="addressEName != null" >
			#{addressEName},
		</if>
		<if test="postCode != null" >
			#{postCode},
		</if>
		<if test="phoneNumber != null" >
			#{phoneNumber},
		</if>
		<if test="taxNumber != null" >
			#{taxNumber},
		</if>
		<if test="faxNumber != null" >
			#{faxNumber},
		</if>
		<if test="upperComCode != null" >
			#{upperComCode},
		</if>
		<if test="insurerName != null" >
			#{insurerName},
		</if>
		<if test="comAttribute != null" >
			#{comAttribute},
		</if>
		<if test="comType != null" >
			#{comType},
		</if>
		<if test="comLevel != null" >
			#{comLevel},
		</if>
		<if test="manager != null" >
			#{manager},
		</if>
		<if test="accountLeader != null" >
			#{accountLeader},
		</if>
		<if test="cashier != null" >
			#{cashier},
		</if>
		<if test="accountant != null" >
			#{accountant},
		</if>
		<if test="remark != null" >
			#{remark},
		</if>
		<if test="newComCode != null" >
			#{newComCode},
		</if>
		<if test="validStatus != null" >
			#{validStatus},
		</if>
		<if test="acntunit != null" >
			#{acntunit},
		</if>
		<if test="articleCode != null" >
			#{articleCode},
		</if>
		<if test="acccode != null" >
			#{acccode},
		</if>
		<if test="centerFlag != null" >
			#{centerFlag},
		</if>
		<if test="outerPayCode != null" >
			#{outerPayCode},
		</if>
		<if test="innerPayCode != null" >
			#{innerPayCode},
		</if>
		<if test="flag != null" >
			#{flag},
		</if>
		<if test="webAddress != null" >
			#{webAddress},
		</if>
		<if test="servicePhone != null" >
			#{servicePhone},
		</if>
		<if test="reportPhone != null" >
			#{reportPhone},
		</if>
		<if test="agentCode != null" >
			#{agentCode},
		</if>
		<if test="agreementNo != null" >
			#{agreementNo},
		</if>
		<if test="sysAreaCode != null" >
			#{sysAreaCode},
		</if>
		<if test="combvisitRate != null" >
			#{combvisitRate},
		</if>
		<if test="printComName != null" >
			#{printComName},
		</if>
		<if test="printAddress != null" >
			#{printAddress},
		</if>
		<if test="pringPostCode != null" >
			#{pringPostCode},
		</if>
		<if test="saleSchannelCode != null" >
			#{saleSchannelCode},
		</if>
		<if test="insertTimeForHis != null" >
			#{insertTimeForHis},
		</if>
		<if test="operateTimeForHis != null" >
			#{operateTimeForHis},
		</if>
		<if test="comFlag != null" >
			#{comFlag},
		</if>
		<if test="upperPath != null" >
			#{upperPath},
		</if>
		<if test="areaCode != null" >
			#{areaCode},
		</if>
	</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.sinosoft.power.po.PrpDCompany">
		update PRPDCOMPANY
		<set>
			<if test="comCName != null" >
				COMCNAME=#{comCName},
			</if>
			<if test="comEName != null" >
				COMENAME=#{comEName},
			</if>
			<if test="addressCName != null" >
				ADDRESSCNAME=#{addressCName},
			</if>
			<if test="addressEName != null" >
				ADDRESSENAME=#{addressEName},
			</if>
			<if test="postCode != null" >
				POSTCODE=#{postCode},
			</if>
			<if test="phoneNumber != null" >
				PHONENUMBER=#{phoneNumber},
			</if>
			<if test="taxNumber != null" >
				TAXNUMBER=#{taxNumber},
			</if>
			<if test="faxNumber != null" >
				FAXNUMBER=#{faxNumber},
			</if>
			<if test="upperComCode != null" >
				UPPERCOMCODE=#{upperComCode},
			</if>
			<if test="insurerName != null" >
				INSURERNAME=#{insurerName},
			</if>
			<if test="comAttribute != null" >
				COMATTRIBUTE=#{comAttribute},
			</if>
			<if test="comType != null" >
				COMTYPE=#{comType},
			</if>
			<if test="comLevel != null" >
				COMLEVEL=#{comLevel},
			</if>
			<if test="manager != null" >
				MANAGER=#{manager},
			</if>
			<if test="accountLeader != null" >
				ACCOUNTLEADER=#{accountLeader},
			</if>
			<if test="cashier != null" >
				CASHIER=#{cashier},
			</if>
			<if test="accountant != null" >
				ACCOUNTANT=#{accountant},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="newComCode != null" >
				NEWCOMCODE=#{newComCode},
			</if>
			<if test="validStatus != null" >
				VALIDSTATUS=#{validStatus},
			</if>
			<if test="acntunit != null" >
				ACNTUNIT=#{acntunit},
			</if>
			<if test="articleCode != null" >
				ARTICLECODE=#{articleCode},
			</if>
			<if test="acccode != null" >
				ACCCODE=#{acccode},
			</if>
			<if test="centerFlag != null" >
				CENTERFLAG=#{centerFlag},
			</if>
			<if test="outerPayCode != null" >
				OUTERPAYCODE=#{outerPayCode},
			</if>
			<if test="innerPayCode != null" >
				INNERPAYCODE=#{innerPayCode},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="webAddress != null" >
				WEBADDRESS=#{webAddress},
			</if>
			<if test="servicePhone != null" >
				SERVICEPHONE=#{servicePhone},
			</if>
			<if test="reportPhone != null" >
				REPORTPHONE=#{reportPhone},
			</if>
			<if test="agentCode != null" >
				AGENTCODE=#{agentCode},
			</if>
			<if test="agreementNo != null" >
				AGREEMENTNO=#{agreementNo},
			</if>
			<if test="sysAreaCode != null" >
				SYSAREACODE=#{sysAreaCode},
			</if>
			<if test="combvisitRate != null" >
				COMBVISITRATE=#{combvisitRate},
			</if>
			<if test="printComName != null" >
				PRINTCOMNAME=#{printComName},
			</if>
			<if test="printAddress != null" >
				PRINTADDRESS=#{printAddress},
			</if>
			<if test="pringPostCode != null" >
				PRINGPOSTCODE=#{pringPostCode},
			</if>
			<if test="saleSchannelCode != null" >
				SALESCHANNELCODE=#{saleSchannelCode},
			</if>
			<if test="insertTimeForHis != null" >
				INSERTTIMEFORHIS=#{insertTimeForHis},
			</if>
			<if test="operateTimeForHis != null" >
				OPERATETIMEFORHIS=#{operateTimeForHis},
			</if>
			<if test="comFlag != null" >
				COMFLAG=#{comFlag},
			</if>
			<if test="upperPath != null" >
				UPPERPATH=#{upperPath},
			</if>
			<if test="areaCode != null" >
				AREACODE=#{areaCode},
			</if>
		</set>
		where COMCODE = #{companyCode}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.sinosoft.power.po.PrpDCompany">
		update PRPDCOMPANY
		set COMCNAME=#{comCName},
			COMENAME=#{comEName},
			ADDRESSCNAME=#{addressCName},
			ADDRESSENAME=#{addressEName},
			POSTCODE=#{postCode},
			PHONENUMBER=#{phoneNumber},
			TAXNUMBER=#{taxNumber},
			FAXNUMBER=#{faxNumber},
			UPPERCOMCODE=#{upperComCode},
			INSURERNAME=#{insurerName},
			COMATTRIBUTE=#{comAttribute},
			COMTYPE=#{comType},
			COMLEVEL=#{comLevel},
			MANAGER=#{manager},
			ACCOUNTLEADER=#{accountLeader},
			CASHIER=#{cashier},
			ACCOUNTANT=#{accountant},
			REMARK=#{remark},
			NEWCOMCODE=#{newComCode},
			VALIDSTATUS=#{validStatus},
			ACNTUNIT=#{acntunit},
			ARTICLECODE=#{articleCode},
			ACCCODE=#{acccode},
			CENTERFLAG=#{centerFlag},
			OUTERPAYCODE=#{outerPayCode},
			INNERPAYCODE=#{innerPayCode},
			FLAG=#{flag},
			WEBADDRESS=#{webAddress},
			SERVICEPHONE=#{servicePhone},
			REPORTPHONE=#{reportPhone},
			AGENTCODE=#{agentCode},
			AGREEMENTNO=#{agreementNo},
			SYSAREACODE=#{sysAreaCode},
			COMBVISITRATE=#{combvisitRate},
			PRINTCOMNAME=#{printComName},
			PRINTADDRESS=#{printAddress},
			PRINGPOSTCODE=#{pringPostCode},
			SALESCHANNELCODE=#{saleSchannelCode},
			COMFLAG=#{comFlag},
			UPPERPATH=#{upperPath},
			AREACODE=#{areaCode}
		where COMCODE=#{companyCode},
	</update>

</mapper>
