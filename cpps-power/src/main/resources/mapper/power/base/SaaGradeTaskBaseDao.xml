<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaGradeTaskDao">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.sinosoft.power.po.SaaGradeTask">
		 <id column="ID" property="id"/>
		 <result column="SYSTEMCODE" property="systemCode"/>
		 <result column="GRADEID" property="gradeId"/>
		 <result column="TASKID" property="taskId"/> 
		 <result column="CREATORCODE" property="creatorCode"/> 
		 <result column="UPDATERCODE" property="updaterCode"/> 
		 <result column="VALIDSTATUS" property="validStatus"/> 
		 <result column="INSERTTIMEFORHIS" property="insertTimeForHis"/> 
		 <result column="OPERATETIMEFORHIS" property="operateTimeForHis"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="BaseColumnList">
		 ID, SYSTEMCODE,GRADEID, TASKID, CREATORCODE, UPDATERCODE,
		 VALIDSTATUS, INSERTTIMEFORHIS, OPERATETIMEFORHIS
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="BaseSelectByEntityWhere">
		<if test="id != null" >
			and ID = #{id}
		</if>
        <if test="systemCode != null" >
            and SYSTEMCODE = #{systemCode}
        </if>
		<if test="gradeId != null" >
			and GRADEID = #{gradeId}
		</if>
		<if test="taskId != null" >
			and TASKID = #{taskId}
		</if>
		<if test="creatorCode != null" >
			and CREATORCODE = #{creatorCode}
		</if>
		<if test="updaterCode != null" >
			and UPDATERCODE = #{updaterCode}
		</if>
		<if test="validStatus != null" >
			and VALIDSTATUS = #{validStatus}
		</if>
		<if test="insertTimeForHis != null" >
			and INSERTTIMEFORHIS = #{insertTimeForHis}
		</if>
		<if test="operateTimeForHis != null" >
			and OPERATETIMEFORHIS = #{operateTimeForHis}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="BaseSelectByEntity">
		select
			<include refid="BaseColumnList" />
		from SAAGRADETASK
		<where>
			<include refid="BaseSelectByEntityWhere" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="BaseColumnList" />
		from SAAGRADETASK
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="BaseColumnList" />
		from SAAGRADETASK
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.sinosoft.power.po.SaaGradeTask">
		<include refid="BaseSelectByEntity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from SAAGRADETASK
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from SAAGRADETASK
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.sinosoft.power.po.SaaGradeTask">
		insert into SAAGRADETASK (ID,SYSTEMCODE, GRADEID, TASKID, CREATORCODE, UPDATERCODE,
			VALIDSTATUS, OPERATETIMEFORHIS)
		values(#{id}, #{systemCode},#{gradeId}, #{taskId}, #{creatorCode}, #{updaterCode},
			#{validStatus}, sysdate)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.sinosoft.power.po.SaaGradeTask">
		insert into SAAGRADETASK
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
            <if test="systemCode != null" >
                SYSTEMCODE,
            </if>
			<if test="gradeId != null" >
				GRADEID,
			</if>
			<if test="taskId != null" >
				TASKID,
			</if>
			<if test="creatorCode != null" >
				CREATORCODE,
			</if>
			<if test="updaterCode != null" >
				UPDATERCODE,
			</if>
			<if test="validStatus != null" >
				VALIDSTATUS,
			</if>
			OPERATETIMEFORHIS,
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
            <if test="systemCode != null" >
                #{systemCode},
            </if>
			<if test="gradeId != null" >
				#{gradeId},
			</if>
			<if test="taskId != null" >
				#{taskId},
			</if>
			<if test="creatorCode != null" >
				#{creatorCode},
			</if>
			<if test="updaterCode != null" >
				#{updaterCode},
			</if>
			<if test="validStatus != null" >
				#{validStatus},
			</if>
			sysdate,
		</trim>
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT SEQ_SAAGRADETASK.NEXTVAL FROM DUAL
        </selectKey>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.sinosoft.power.po.SaaGradeTask">
		update SAAGRADETASK
		<set>
            <if test="systemCode != null" >
                SYSTEMCODE = #{systemCode}
            </if>
			<if test="gradeId != null" >
				GRADEID=#{gradeId},
			</if>
			<if test="taskId != null" >
				TASKID=#{taskId},
			</if>
			<if test="creatorCode != null" >
				CREATORCODE=#{creatorCode},
			</if>
			<if test="updaterCode != null" >
				UPDATERCODE=#{updaterCode},
			</if>
			<if test="validStatus != null" >
				VALIDSTATUS=#{validStatus},
			</if>
			OPERATETIMEFORHIS=sysdate,
		</set>
		where ID = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.sinosoft.power.po.SaaGradeTask">
		update SAAGRADETASK
		set SYSTEMCODE=#{systemCode},
            GRADEID=#{gradeId},
			TASKID=#{taskId},
			CREATORCODE=#{creatorCode},
			UPDATERCODE=#{updaterCode},
			VALIDSTATUS=#{validStatus},
			OPERATETIMEFORHIS=sysdate
		where ID = #{id}
	</update>

</mapper>