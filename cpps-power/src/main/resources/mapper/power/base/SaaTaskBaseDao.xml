<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaTaskDao">
	<!-- 默认开启二级缓存,使用Least Recently Used（LRU，最近最少使用的）算法来收回 -->
<!-- 	<cache/> -->
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.sinosoft.power.po.SaaTask">
		 <id column="ID" property="id"/>
		 <result column="SYSTEM" property="system"/>
		 <result column="TASKCODE" property="taskCode"/>
		 <result column="PARENTCODE" property="parentCode"/> 
		 <result column="TASKCNAME" property="taskCName"/> 
		 <result column="TASKENAME" property="taskEName"/> 
		 <result column="URL" property="url"/> 
		 <result column="CREATECODE" property="createCode"/>
		 <result column="CREATETIME" property="createTime"/> 
		 <result column="VALIDFLAG" property="validFlag"/> 
		 <result column="REMARK" property="remark"/> 
		 <result column="FLAG" property="flag"/> 
		 <result column="SYNFLAG" property="synFlag"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="BaseColumnList">
		 ID,SYSTEM, TASKCODE AS taskCode, PARENTCODE AS parentCode, TASKCNAME AS taskCName, TASKENAME AS taskEName,
		 URL, CREATECODE AS createCode, CREATETIME AS createTime, VALIDFLAG AS validFlag,
		 REMARK, FLAG, SYNFLAG AS synFlag
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="BaseSelectByEntityWhere">
		<if test="id != null" >
			and ID = #{id}
		</if>
        <if test="system != null" >
            and SYSTEM = #{system}
        </if>
		<if test="taskCode != null" >
			and TASKCODE = #{taskCode}
		</if>
		<if test="parentCode != null" >
			and PARENTCODE = #{parentCode}
		</if>
		<if test="taskCName != null" >
			and TASKCNAME = #{taskCName}
		</if>
		<if test="taskEName != null" >
			and TASKENAME = #{taskEName}
		</if>
		<if test="url != null" >
			and URL = #{url}
		</if>
		<if test="createCode != null" >
			and CREATECODE = #{createCode}
		</if>
		<if test="createTime != null" >
			and CREATETIME = #{createTime}
		</if>
		<if test="validFlag != null" >
			and VALIDFLAG = #{validFlag}
		</if>
		<if test="remark != null" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null" >
			and FLAG = #{flag}
		</if>
		<if test="synFlag != null" >
			and SYNFLAG = #{synFlag}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="BaseSelectByEntity">
		select
			<include refid="BaseColumnList" />
		from SAATASK
		<where>
			<include refid="BaseSelectByEntityWhere" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="BaseColumnList" />
		from SAATASK
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="BaseColumnList" />
		from SAATASK
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.sinosoft.power.po.SaaTask">
		<include refid="BaseSelectByEntity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from SAATASK
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from SAATASK
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.sinosoft.power.po.SaaTask">
		insert into SAATASK (ID, SYSTEM,TASKCODE, PARENTCODE, TASKCNAME, TASKENAME,
			URL, CREATECODE, CREATETIME, VALIDFLAG,
			REMARK, FLAG, SYNFLAG)
		values(#{id},#{system}, #{taskCode}, #{parentCode}, #{taskCName}, #{taskEName},
			#{url}, #{createCode}, #{createTime}, #{validFlag},
			#{remark}, #{flag}, #{synFlag})
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.sinosoft.power.po.SaaTask">
		insert into SAATASK
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
            <if test="system != null" >
                SYSTEM,
            </if>
			<if test="taskCode != null" >
				TASKCODE,
			</if>
			<if test="parentCode != null" >
				PARENTCODE,
			</if>
			<if test="taskCName != null" >
				TASKCNAME,
			</if>
			<if test="taskEName != null" >
				TASKENAME,
			</if>
			<if test="url != null" >
				URL,
			</if>
			<if test="createCode != null" >
				CREATECODE,
			</if>
			<if test="createTime != null" >
				CREATETIME,
			</if>
			<if test="validFlag != null" >
				VALIDFLAG,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="synFlag != null" >
				SYNFLAG,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
            <if test="system != null" >
                #{system},
            </if>
			<if test="taskCode != null" >
				#{taskCode},
			</if>
			<if test="parentCode != null" >
				#{parentCode},
			</if>
			<if test="taskCName != null" >
				#{taskCName},
			</if>
			<if test="taskEName != null" >
				#{taskEName},
			</if>
			<if test="url != null" >
				#{url},
			</if>
			<if test="createCode != null" >
				#{createCode},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="synFlag != null" >
				#{synFlag},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.sinosoft.power.po.SaaTask">
		update SAATASK
		<set>
            <if test="system != null" >
                SYSTEM = #{systemCode}
            </if>
			<if test="taskCode != null" >
				TASKCODE=#{taskCode},
			</if>
			<if test="parentCode != null" >
				PARENTCODE=#{parentCode},
			</if>
			<if test="taskCName != null" >
				TASKCNAME=#{taskCName},
			</if>
			<if test="taskEName != null" >
				TASKENAME=#{taskEName},
			</if>
			<if test="url != null" >
				URL=#{url},
			</if>
			<if test="createCode != null" >
				CREATECODE=#{createCode},
			</if>
			<if test="createTime != null" >
				CREATETIME=#{createTime},
			</if>
			<if test="validFlag != null" >
				VALIDFLAG=#{validFlag},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="synFlag != null" >
				SYNFLAG=#{synFlag},
			</if>
		</set>
		where ID = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.sinosoft.power.po.SaaTask">
		update SAATASK
		set SYSTEM=#{system},
            TASKCODE=#{taskCode},
			PARENTCODE=#{parentCode},
			TASKCNAME=#{taskCName},
			TASKENAME=#{taskEName},
			URL=#{url},
			CREATECODE=#{createCode},
			CREATETIME=#{createTime},
			VALIDFLAG=#{validFlag},
			REMARK=#{remark},
			FLAG=#{flag},
			SYNFLAG=#{synFlag}
		where ID = #{id}
	</update>

</mapper>
