<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaUserAmountDao">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.sinosoft.power.po.SaaUserAmount">
		 <id column="ID" property="id"/>
		 <result column="SYSTEMCODE" property="systemCode"/>
		 <result column="USERCODE" property="userCode"/>
		 <result column="USERNAME" property="userName"/> 
		 <result column="COMCODE" property="companyCode"/>
		 <result column="TASKCODE" property="taskCode"/> 
		 <result column="VALUELOWER" property="valueLower"/> 
		 <result column="VALUEUPPER" property="valueUpper"/> 
		 <result column="DEADLINE" property="deadLine"/> 
		 <result column="STARTDATE" property="startDate"/> 
		 <result column="TASKPARENTCODE" property="taskParentCode"/> 
		 <result column="INSERTTIMEFORHIS" property="insertTimeForHis"/>
		 <result column="CLASSCODE" property="classCode"/>
		 <result column="OPERATETIMEFORHIS" property="operateTimeForHis"/>
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="BaseColumnList">
		 ID, SYSTEMCODE,USERCODE, USERNAME, COMCODE, TASKCODE,
		 VALUELOWER, VALUEUPPER, DEADLINE, STARTDATE, TASKPARENTCODE,
		 INSERTTIMEFORHIS,CLASSCODE, OPERATETIMEFORHIS
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="BaseSelectByEntityWhere">
		<if test="id != null" >
			and ID = #{id}
		</if>
        <if test="systemCode != null" >
            and SYSTEMCODE = #{systemCode}
        </if>
		<if test="userCode != null" >
			and USERCODE = #{userCode}
		</if>
		<if test="userName != null" >
			and USERNAME = #{userName}
		</if>
		<if test="companyCode != null" >
			and COMCODE = #{companyCode}
		</if>
		<if test="taskCode != null" >
			and TASKCODE = #{taskCode}
		</if>
		<if test="valueLower != null" >
			and VALUELOWER = #{valueLower}
		</if>
		<if test="valueUpper != null" >
			and VALUEUPPER = #{valueUpper}
		</if>
		<if test="deadLine != null" >
			and DEADLINE = #{deadLine}
		</if>
		<if test="startDate != null" >
			and STARTDATE = #{startDate}
		</if>
		<if test="taskParentCode != null" >
			and TASKPARENTCODE = #{taskParentCode}
		</if>
		<if test="insertTimeForHis != null" >
			and INSERTTIMEFORHIS = #{insertTimeForHis}
		</if>
		<if test="operateTimeForHis != null" >
			and OPERATETIMEFORHIS = #{operateTimeForHis}
		</if>
		<if test="classCode != null">
			and CLASSCODE = #{classCode}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="BaseSelectByEntity">
		select
			<include refid="BaseColumnList" />
		from SAAUSERAMOUNT
		<where>
			<include refid="BaseSelectByEntityWhere" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="BaseColumnList" />
		from SAAUSERAMOUNT
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="BaseColumnList" />
		from SAAUSERAMOUNT
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.sinosoft.power.po.SaaUserAmount">
		<include refid="BaseSelectByEntity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from SAAUSERAMOUNT
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from SAAUSERAMOUNT
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.sinosoft.power.po.SaaUserAmount">
		insert into SAAUSERAMOUNT (ID, SYSTEMCODE,USERCODE, USERNAME, COMCODE, TASKCODE,
			VALUELOWER, VALUEUPPER, DEADLINE, STARTDATE, TASKPARENTCODE, 
			OPERATETIMEFORHIS,CLASSCODE)
		values(#{id}, #{systemCode},#{userCode}, #{userName}, #{companyCode}, #{taskCode},
			#{valueLower}, #{valueUpper}, #{deadLine}, #{startDate}, #{taskParentCode}, 
			#{classCode},sysdate)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.sinosoft.power.po.SaaUserAmount">
		insert into SAAUSERAMOUNT
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
            <if test="systemCode != null" >
                SYSTEMCODE,
            </if>
			<if test="userCode != null" >
				USERCODE,
			</if>
			<if test="userName != null" >
				USERNAME,
			</if>
			<if test="companyCode != null" >
				COMCODE,
			</if>
			<if test="taskCode != null" >
				TASKCODE,
			</if>
			<if test="valueLower != null" >
				VALUELOWER,
			</if>
			<if test="valueUpper != null" >
				VALUEUPPER,
			</if>
			<if test="deadLine != null" >
				DEADLINE,
			</if>
			<if test="startDate != null" >
				STARTDATE,
			</if>
			<if test="taskParentCode != null" >
				TASKPARENTCODE,
			</if>
			<if test="classCode != null">
				CLASSCODE,
			</if>
				OPERATETIMEFORHIS,
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
            <if test="systemCode != null" >
                #{systemCode},
            </if>
			<if test="userCode != null" >
				#{userCode},
			</if>
			<if test="userName != null" >
				#{userName},
			</if>
			<if test="companyCode != null" >
				#{companyCode},
			</if>
			<if test="taskCode != null" >
				#{taskCode},
			</if>
			<if test="valueLower != null" >
				#{valueLower},
			</if>
			<if test="valueUpper != null" >
				#{valueUpper},
			</if>
			<if test="deadLine != null" >
				#{deadLine},
			</if>
			<if test="startDate != null" >
				#{startDate},
			</if>
			<if test="taskParentCode != null" >
				#{taskParentCode},
			</if>
			<if test="classCode != null">
				#{classCode},
			</if>
				sysdate,
		</trim>
		<selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
			SELECT SEQ_SAAUSERAMOUNT.NEXTVAL FROM DUAL
		</selectKey>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.sinosoft.power.po.SaaUserAmount">
		update SAAUSERAMOUNT
		<set>
            <if test="systemCode != null" >
                SYSTEMCODE = #{systemCode}
            </if>
			<if test="userCode != null" >
				USERCODE=#{userCode},
			</if>
			<if test="userName != null" >
				USERNAME=#{userName},
			</if>
			<if test="companyCode != null" >
				COMCODE=#{companyCode},
			</if>
			<if test="taskCode != null" >
				TASKCODE=#{taskCode},
			</if>
			<if test="valueLower != null" >
				VALUELOWER=#{valueLower},
			</if>
			<if test="valueUpper != null" >
				VALUEUPPER=#{valueUpper},
			</if>
			<if test="deadLine != null" >
				DEADLINE=#{deadLine},
			</if>
			<if test="startDate != null" >
				STARTDATE=#{startDate},
			</if>
			<if test="taskParentCode != null" >
				TASKPARENTCODE=#{taskParentCode},
			</if>
			<if test="classCode != null">
				CLASSCODE = #{classCode},
			</if>
			OPERATETIMEFORHIS=sysdate,
		</set>
		where ID = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.sinosoft.power.po.SaaUserAmount">
		update SAAUSERAMOUNT
		set SYSTEMCODE=#{systemCode},
            USERCODE=#{userCode},
			USERNAME=#{userName},
			COMCODE=#{companyCode},
			TASKCODE=#{taskCode},
			VALUELOWER=#{valueLower},
			VALUEUPPER=#{valueUpper},
			DEADLINE=#{deadLine},
			STARTDATE=#{startDate},
			TASKPARENTCODE=#{taskParentCode},
			CLASSCODE=#{classCode},
			OPERATETIMEFORHIS=sysdate
		where ID = #{id}
	</update>

</mapper>