<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaUserPermitDataDao">
	<!-- 默认开启二级缓存,使用Least Recently Used（LRU，最近最少使用的）算法来收回 -->
<!-- 	<cache/> -->
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.sinosoft.power.po.SaaUserPermitData">
		 <id column="ID" property="id"/> 
		 <result column="SYSTEMCODE" property="systemCode"/> 
		 <result column="USERCODE" property="userCode"/> 
		 <result column="COMCODE" property="companyCode"/>
		 <result column="USERGRADEID" property="userGradeId"/> 
		 <result column="FACTORGROUP" property="factorGroup"/> 
		 <result column="FACTORCODE" property="factorcode"/> 
		 <result column="DATAOPER" property="dataOper"/> 
		 <result column="DATAVALUE1" property="dataValue1"/> 
		 <result column="DATAVALUE2" property="dataValue2"/> 
		 <result column="CREATECODE" property="createCode"/> 
		 <result column="CREATETIME" property="createTime"/> 
		 <result column="UPDATECODE" property="updateCode"/> 
		 <result column="UPDATETIME" property="updateTime"/> 
		 <result column="VALIDFLAG" property="validFlag"/> 
		 <result column="REMARK" property="remark"/> 
		 <result column="FLAG" property="flag"/> 
		 <result column="SYNFLAG" property="synFlag"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="BaseColumnList">
		 ID, SYSTEMCODE AS systemCode, USERCODE AS userCode, COMCODE AS companyCode, USERGRADEID AS userGradeId,
		 FACTORGROUP AS factorGroup, FACTORCODE AS factorcode, DATAOPER AS dataOper, DATAVALUE1 AS dataValue1, DATAVALUE2 AS dataValue2,
		 CREATECODE AS createCode, CREATETIME AS createTime, UPDATECODE AS updateCode, UPDATETIME AS updateTime, VALIDFLAG AS validFlag,
		 REMARK, FLAG, SYNFLAG AS synFlag
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="BaseSelectByEntityWhere">
		<if test="id != null" >
			and ID = #{id}
		</if>
		<if test="systemCode != null" >
			and SYSTEMCODE = #{systemCode}
		</if>
		<if test="userCode != null" >
			and USERCODE = #{userCode}
		</if>
		<if test="companyCode != null" >
			and COMCODE = #{companyCode}
		</if>
		<if test="userGradeId != null" >
			and USERGRADEID = #{userGradeId}
		</if>
		<if test="factorGroup != null" >
			and FACTORGROUP = #{factorGroup}
		</if>
		<if test="factorcode != null" >
			and FACTORCODE = #{factorcode}
		</if>
		<if test="dataOper != null" >
			and DATAOPER = #{dataOper}
		</if>
		<if test="dataValue1 != null" >
			and DATAVALUE1 = #{dataValue1}
		</if>
		<if test="dataValue2 != null" >
			and DATAVALUE2 = #{dataValue2}
		</if>
		<if test="createCode != null" >
			and CREATECODE = #{createCode}
		</if>
		<if test="createTime != null" >
			and CREATETIME = #{createTime}
		</if>
		<if test="updateCode != null" >
			and UPDATECODE = #{updateCode}
		</if>
		<if test="updateTime != null" >
			and UPDATETIME = #{updateTime}
		</if>
		<if test="validFlag != null" >
			and VALIDFLAG = #{validFlag}
		</if>
		<if test="remark != null" >
			and REMARK = #{remark}
		</if>
		<if test="flag != null" >
			and FLAG = #{flag}
		</if>
		<if test="synFlag != null" >
			and SYNFLAG = #{synFlag}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="BaseSelectByEntity">
		select
			<include refid="BaseColumnList" />
		from SAAUSERPERMITDATA
		<where>
			<include refid="BaseSelectByEntityWhere" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="BaseColumnList" />
		from SAAUSERPERMITDATA
		where ID = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="BaseColumnList" />
		from SAAUSERPERMITDATA
		where ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.sinosoft.power.po.SaaUserPermitData">
		<include refid="BaseSelectByEntity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from SAAUSERPERMITDATA
		where ID = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from SAAUSERPERMITDATA
		where ID in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.sinosoft.power.po.SaaUserPermitData">
		insert into SAAUSERPERMITDATA (ID, SYSTEMCODE, USERCODE, COMCODE, USERGRADEID, 
			FACTORGROUP, FACTORCODE, DATAOPER, DATAVALUE1, DATAVALUE2, 
			CREATECODE, CREATETIME, UPDATECODE, UPDATETIME, VALIDFLAG, 
			REMARK, FLAG, SYNFLAG)
		values(#{id}, #{systemCode}, #{userCode}, #{companyCode}, #{userGradeId},
			#{factorGroup}, #{factorcode}, #{dataOper}, #{dataValue1}, #{dataValue2}, 
			#{createCode}, #{createTime}, #{updateCode}, #{updateTime}, #{validFlag}, 
			#{remark}, #{flag}, #{synFlag})
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.sinosoft.power.po.SaaUserPermitData">
		insert into SAAUSERPERMITDATA
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				ID,
			</if>
			<if test="systemCode != null" >
				SYSTEMCODE,
			</if>
			<if test="userCode != null" >
				USERCODE,
			</if>
			<if test="companyCode != null" >
				COMCODE,
			</if>
			<if test="userGradeId != null" >
				USERGRADEID,
			</if>
			<if test="factorGroup != null" >
				FACTORGROUP,
			</if>
			<if test="factorcode != null" >
				FACTORCODE,
			</if>
			<if test="dataOper != null" >
				DATAOPER,
			</if>
			<if test="dataValue1 != null" >
				DATAVALUE1,
			</if>
			<if test="dataValue2 != null" >
				DATAVALUE2,
			</if>
			<if test="createCode != null" >
				CREATECODE,
			</if>
			<if test="createTime != null" >
				CREATETIME,
			</if>
			<if test="updateCode != null" >
				UPDATECODE,
			</if>
			<if test="updateTime != null" >
				UPDATETIME,
			</if>
			<if test="validFlag != null" >
				VALIDFLAG,
			</if>
			<if test="remark != null" >
				REMARK,
			</if>
			<if test="flag != null" >
				FLAG,
			</if>
			<if test="synFlag != null" >
				SYNFLAG,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="systemCode != null" >
				#{systemCode},
			</if>
			<if test="userCode != null" >
				#{userCode},
			</if>
			<if test="companyCode != null" >
				#{companyCode},
			</if>
			<if test="userGradeId != null" >
				#{userGradeId},
			</if>
			<if test="factorGroup != null" >
				#{factorGroup},
			</if>
			<if test="factorcode != null" >
				#{factorcode},
			</if>
			<if test="dataOper != null" >
				#{dataOper},
			</if>
			<if test="dataValue1 != null" >
				#{dataValue1},
			</if>
			<if test="dataValue2 != null" >
				#{dataValue2},
			</if>
			<if test="createCode != null" >
				#{createCode},
			</if>
			<if test="createTime != null" >
				#{createTime},
			</if>
			<if test="updateCode != null" >
				#{updateCode},
			</if>
			<if test="updateTime != null" >
				#{updateTime},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="remark != null" >
				#{remark},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="synFlag != null" >
				#{synFlag},
			</if>
		</trim>
		<selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT SEQ_SAAUSERPERMITDATA.NEXTVAL FROM DUAL
		</selectKey>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.sinosoft.power.po.SaaUserPermitData">
		update SAAUSERPERMITDATA
		<set>
			<if test="systemCode != null" >
				SYSTEMCODE=#{systemCode},
			</if>
			<if test="userCode != null" >
				USERCODE=#{userCode},
			</if>
			<if test="companyCode != null" >
				COMCODE=#{companyCode},
			</if>
			<if test="userGradeId != null" >
				USERGRADEID=#{userGradeId},
			</if>
			<if test="factorGroup != null" >
				FACTORGROUP=#{factorGroup},
			</if>
			<if test="factorcode != null" >
				FACTORCODE=#{factorcode},
			</if>
			<if test="dataOper != null" >
				DATAOPER=#{dataOper},
			</if>
			<if test="dataValue1 != null" >
				DATAVALUE1=#{dataValue1},
			</if>
			<if test="dataValue2 != null" >
				DATAVALUE2=#{dataValue2},
			</if>
			<if test="createCode != null" >
				CREATECODE=#{createCode},
			</if>
			<if test="createTime != null" >
				CREATETIME=#{createTime},
			</if>
			<if test="updateCode != null" >
				UPDATECODE=#{updateCode},
			</if>
			<if test="updateTime != null" >
				UPDATETIME=#{updateTime},
			</if>
			<if test="validFlag != null" >
				VALIDFLAG=#{validFlag},
			</if>
			<if test="remark != null" >
				REMARK=#{remark},
			</if>
			<if test="flag != null" >
				FLAG=#{flag},
			</if>
			<if test="synFlag != null" >
				SYNFLAG=#{synFlag},
			</if>
		</set>
		where ID = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.sinosoft.power.po.SaaUserPermitData">
		update SAAUSERPERMITDATA
		set SYSTEMCODE=#{systemCode},
			USERCODE=#{userCode},
			COMCODE=#{companyCode},
			USERGRADEID=#{userGradeId},
			FACTORGROUP=#{factorGroup},
			FACTORCODE=#{factorcode},
			DATAOPER=#{dataOper},
			DATAVALUE1=#{dataValue1},
			DATAVALUE2=#{dataValue2},
			CREATECODE=#{createCode},
			CREATETIME=#{createTime},
			UPDATECODE=#{updateCode},
			UPDATETIME=#{updateTime},
			VALIDFLAG=#{validFlag},
			REMARK=#{remark},
			FLAG=#{flag},
			SYNFLAG=#{synFlag}
		where ID = #{id}
	</update>

</mapper>
