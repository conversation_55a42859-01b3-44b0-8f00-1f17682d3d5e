<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaUserShortcutDao">
	<!-- 默认开启二级缓存,使用Least Recently Used（LRU，最近最少使用的）算法来收回 -->
	<!-- <cache/> -->
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.sinosoft.power.po.UserShortcut">
		 <id column="USERCODE" property="userCode"/> 
		 <result column="TASKLIST" property="taskList"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="BaseColumnList">
		 USERCODE AS userCode, TASKLIST AS taskList
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="BaseSelectByEntityWhere">
		<if test="userCode != null" >
			and USERCODE = #{userCode}
		</if>
		<if test="taskList != null" >
			and TASKLIST = #{taskList}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="BaseSelectByEntity">
		select
			<include refid="BaseColumnList" />
		from USERSHORTCUT
		<where>
			<include refid="BaseSelectByEntityWhere" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="BaseColumnList" />
		from USERSHORTCUT
		where USERCODE = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="BaseColumnList" />
		from USERSHORTCUT
		where USERCODE in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.sinosoft.power.po.UserShortcut">
		<include refid="BaseSelectByEntity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from USERSHORTCUT
		where USERCODE = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from USERSHORTCUT
		where USERCODE in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.sinosoft.power.po.UserShortcut">
		insert into USERSHORTCUT (USERCODE, TASKLIST)
		values(#{userCode}, #{taskList})
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.sinosoft.power.po.UserShortcut">
		insert into USERSHORTCUT
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="userCode != null" >
				USERCODE,
			</if>
			<if test="taskList != null" >
				TASKLIST,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="userCode != null" >
				#{userCode},
			</if>
			<if test="taskList != null" >
				#{taskList},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.sinosoft.power.po.UserShortcut">
		update USERSHORTCUT
		<set>
			<if test="taskList != null" >
				TASKLIST=#{taskList},
			</if>
		</set>
		where USERCODE = #{userCode}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.sinosoft.power.po.UserShortcut">
		update USERSHORTCUT
		set TASKLIST=#{taskList}
		where USERCODE = #{userCode}
	</update>

</mapper>
