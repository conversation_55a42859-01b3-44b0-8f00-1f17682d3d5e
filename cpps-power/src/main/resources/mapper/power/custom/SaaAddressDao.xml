<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaAddressDao">

    <!-- 查询所有城市 -->
    <select id="queryProvincials" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from PRPDADDRESS
        where PARENT is null
        order by CODE asc
    </select>

    <!-- 按省查城市 -->
    <select id="queryCities" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from PRPDADDRESS
        where PARENT = #{parent}
        order by CODE asc
    </select>




</mapper>