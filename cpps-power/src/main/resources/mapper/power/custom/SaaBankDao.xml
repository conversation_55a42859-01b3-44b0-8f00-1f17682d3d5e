<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaBankDao">
    <!-- 请在下方添加自定义配置-->
    <!-- 按BankCode查询1条记录 -->
    <select id="queryByBankCode" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from PRPDBANK
        where
        BANKCODE = #{bankCode}
    </select>

    <!-- 按BankName查询1条记录 -->
    <select id="queryByBankName" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from PRPDBANK
        where
        BANKNAME = #{bankName}
    </select>

    <!-- 查询所有银行大类 -->
    <select id="queryBanks" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from PRPDBANK
        order by bankUid
    </select>

    <select id="searchByBankCode" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from PRPDBANK
        where
        BANKUID = #{bankCode}
    </select>



</mapper>