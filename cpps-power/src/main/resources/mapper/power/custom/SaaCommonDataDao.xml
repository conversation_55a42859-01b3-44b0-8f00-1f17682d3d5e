<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaCommonDataDao">
    <!-- 请在下方添加自定义配置-->
    <!-- 按数据类型查询 -->
    <select id="queryDataByDataType" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from COMMONDATA
        <where>
            DATATYPE=#{dataType}
            <if test="preType != null" >
                and PRETYPE = #{preType}
            </if>
            <if test="preCode != null" >
                and PRECODE = #{preCode}
            </if>
            <if test="validStatus != null" >
                and VALIDSTATUS = 1
            </if>
        </where>
        order by ID desc
    </select>



    <select id="queryTaskPool" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumnList" />
        from COMMONDATA
        WHERE DATATYPE = 'TaskPoolParent'
        ORDER BY PRECODE
    </select>


    <!-- 按数据代码查询 -->
    <select id="transferData" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from COMMONDATA
        where
        DATATYPE = #{dataType}
        and
        DATACODE = #{dataCode}
        <if test="preCode != null" >
            and PRECODE = #{preCode}
        </if>
    </select>

</mapper>