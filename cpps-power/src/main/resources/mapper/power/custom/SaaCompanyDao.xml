<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaCompanyDao">
	<!-- 请在下方添加自定义配置-->


	<sql id="MyColumnList">
		company.COMCODE AS companyCode, company.COMCNAME AS comCName, company.COMENAME AS comEName, company.ADDRESSCNAME AS addressCName,
		company.ADDRESSENAME AS addressEName, company.POSTCODE AS postCode, company.PHONENUMBER AS phoneNumber, company.TAXNUMBER AS taxNumber, company.FAXNUMBER AS faxNumber,
		company.UPPERCOMCODE AS upperComCode, company.INSURERNAME AS insurerName, company.COMATTRIBUTE AS comAttribute, company.COMTYPE AS comType, company.COMLEVEL AS comLevel,
		company.MANAGER AS manager, company.ACCOUNTLEADER AS accountLeader, company.CASHIER AS cashier, company.ACCOUNTANT AS accountant, company.REMARK AS remark,
		company.NEWCOMCODE AS newComCode, company.VALIDSTATUS AS validStatus, company.ACNTUNIT AS acntnit, company.ARTICLECODE AS articleCode, company.ACCCODE AS acccode,
		company.CENTERFLAG AS centerFlag, company.OUTERPAYCODE AS outerPayCode, company.INNERPAYCODE AS innerPayCode, company.FLAG AS flag, company.WEBADDRESS AS webAddress,
		company.SERVICEPHONE AS servicePhone, company.REPORTPHONE AS reportPhone, company.AGENTCODE AS agentCode, company.AGREEMENTNO AS agreementNo, company.SYSAREACODE AS sysAreaCode,
		company.COMBVISITRATE AS combvisitRate, company.PRINTCOMNAME AS printComName, company.PRINTADDRESS AS printAddress, company.PRINGPOSTCODE AS pringPostCode, company.SALESCHANNELCODE AS saleSchannelCode,
		company.INSERTTIMEFORHIS AS insertTimeForHis, company.OPERATETIMEFORHIS AS operateTimeForHis,company.COMFLAG AS comFlag, company.UPPERPATH AS upperPath, company.AREACODE AS areaCode
	</sql>

	<!-- 按comCode查询一条记录 -->
	<select id="querycompanyBycomCode" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="BaseColumnList" />
		from PRPDCOMPANY
		where COMCODE = #{companyCode}
	</select>

	<select id="queryByComCode" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="BaseColumnList" />
		from PRPDCOMPANY
		where COMCODE = #{companyCode}
	</select>


	<!--<select id="queryByParam" resultType="String" parameterType="string">-->
	<!--select-->
	<!--COMCODEcompanyCodeCode-->
	<!--from PRPDCOMPANY-->
	<!--where ${value}-->
	<!--</select>-->

	<select id="queryLikeComCodes" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="BaseColumnList" />
		from PRPDCOMPANY
		<where>
			<if test="comCodeLike != null">
				AND COMCODE LIKE #{comCodeLike}
			</if>
			<if test="companyCode != null">
				AND COMCODE=#{companyCode}
			</if>
		</where>
	</select>

	<!-- 查询所有记录 -->
	<select id="queryAll" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="BaseColumnList" />
		from PRPDCOMPANY
	</select>

	<!-- 查询局部记录 -->
	<select id="queryAllData" resultMap="BaseResultMap" parameterType="map">
		select p.companyCode,p.comcname
		from PRPDCOMPANY p
	</select>

	<select id="queryAllTreeData" resultMap="BaseResultMap" parameterType="map">
		select
			COMCODE AS companyCode,
			COMCNAME AS comCName,
			UPPERCOMCODE AS upperComCode
		from PRPDCOMPANY
	</select>

	<!-- 查询所有记录 -->
	<select id="queryCenterComByFlag" resultMap="BaseResultMap">
		select
		<include refid="BaseColumnList" />
		from PRPDCOMPANY
		WHERE
		COMLEVEL = '2'
	</select>

	<!-- 查询二三级机构编码 -->
	<select id="querySecondThirdCompany" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="BaseColumnList" />
		from PRPDCOMPANY
		where
		(centerflag = '0' or comcode = '00020000') and companyCode not like '10000000000000000'
	</select>
	<!-- 查询二级机构下的所有三级机构 -->
	<!--<select id="queryThirdCompany" resultMap="BaseResultMap" parameterType="map">-->
	<!--select-->
	<!--<include refid="BaseColumnList" />-->
	<!--from PRPDCOMPANY-->
	<!--where centerflag = '1' and comcode not like '00020000'-->
	<!--start with comcode = #{param1}  connect by nocycle prior comcode = uppercomcode-->
	<!--</select>-->
	<select id="queryThirdCompany" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="BaseColumnList" />
		from PRPDCOMPANY
		where UPPERCOMCODE = #{param1} and COMLEVEL = '3'
	</select>
	<!-- 功能描述 -->
	<select id="queryUpperThreeCompany" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="BaseColumnList" />
		from PRPDCOMPANY
		WHERE centerflag = '1' AND comcode NOT LIKE '00020000'
		START WITH comcode = #{param1} CONNECT BY NOCYCLE PRIOR comcode =  uppercomcode
	</select>

	<!-- 查询外省分支机构 -->
	<!--<select id="queryOtherProvincesCompany" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="BaseColumnList" />
		from PRPDCOMPANY
		where  CENTERFLAG='0' and UPPERCOMCODE= '10000000000000000' and comcode not like  #{comNo}
	</select>-->

	<!-- 查询外省分支机构 -->
	<select id="queryOtherProvincesCompany" resultMap="BaseResultMap" parameterType="map">
		SELECT <include refid="BaseColumnList" />
		FROM PRPDCOMPANY c
		WHERE c.COMLEVEL = '2' AND c.COMCODE not like concat(#{comNo},'%') and validStatus='1'
	</select>

	<!-- 查询省级分支机构 -->
	<select id="queryOtherProvincesCompanyOur" resultMap="BaseResultMap" parameterType="map">
		SELECT <include refid="BaseColumnList" />
		FROM PRPDCOMPANY c
		WHERE c.COMLEVEL = '2' and validStatus='1'
	</select>

	<!-- 查询除总公司外的所有机构 -->
	<select id="queryOtherProvincesOur" resultMap="BaseResultMap" parameterType="map">
		SELECT <include refid="BaseColumnList" />
		FROM PRPDCOMPANY c
		where  c.COMCODE like concat(#{comNo},'%')
	</select>

	<select id="queryOtherComCurrent" resultMap="BaseResultMap" parameterType="map">
		SELECT <include refid="BaseColumnList" />
		FROM PRPDCOMPANY c
		where c.UPPERPATH like concat('%',concat(#{comNO},'%'))
		<!--<where>-->
		<!--<if test="comNO != null and comNO != ''">-->
		<!--and c.UPPERPATH like concat('%',concat(#{comNO},'%'))-->
		<!--</if>-->
		<!--</where>-->
	</select>


	<select id="queryCompanyByUpperPath" resultMap="BaseResultMap" parameterType="string">
		select
		<include refid="BaseColumnList"/>
		from PRPDCOMPANY
		<where>
			companyCode in
			<foreach item="item" index="index" collection="comCodeSet" open="(" separator="," close=")">
				#{item}
			</foreach>
		</where>

	</select>


	<select id="queryUpperComCodeList" resultType="string" parameterType="string">
		select
			comcode
		from PRPDCOMPANY
			 START WITH COMCODE = #{companyCode}
		CONNECT BY COMCODE = PRIOR UPPERCOMCODE
	</select>

	<select id="queryCompanyUpperPathSetFromComCodeSet" resultType="string" parameterType="string">
		select
		upperPath
		from PRPDCOMPANY
		<where>
			companyCode in
			<foreach item="item" index="index" collection="comCodeSet" open="(" separator="," close=")">
				#{item}
			</foreach>
		</where>

	</select>

	<select id="queryCompanyUpperPath" resultMap="BaseResultMap" parameterType="string">
		select
		<include refid="BaseColumnList" />
		from PRPDCOMPANY
		<where>
			companyCode = #{companyCode}
		</where>
	</select>

	<delete id="deleteTrnData" parameterType="string">
		delete from PRPDCOMPANY_TRN WHERE COMCODE = #{companyCode}
	</delete>

	<insert id="insertTrnData" parameterType="string">
		INSERT INTO PRPDCOMPANY_TRN(COMCODE, UPPERPATH)
			SELECT COMCODE, SUBSTR(SYS_CONNECT_BY_PATH(COMCODE, '/'), 2) UPPERPATH
			FROM PRPDCOMPANY T
			WHERE T.COMCODE = #{companyCode}
		START WITH T.COMCODE = '31000000'
		CONNECT BY PRIOR T.COMCODE = T.UPPERCOMCODE
		AND PRIOR T.COMCODE != T.COMCODE
	</insert>

	<update id="updateUpperPathByTrnData"  parameterType="string">
		UPDATE PRPDCOMPANY COMP
		SET UPPERPATH =
		(SELECT UPPERPATH
		 FROM PRPDCOMPANY_TRN TRN
		 WHERE COMP.COMCODE = TRN.COMCODE)
		WHERE COMP.COMCODE = #{companyCode}
	</update>


	<select id="queryAuthorityCom" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="BaseColumnList"/>
		from PRPDCOMPANY
		<where>
			<foreach item="item" index="index" collection="saaUserPermitDataList" open=" " separator=" or " close=" ">
				upperPath like concat(#{item.dataValue1},'%')
			</foreach>
		</where>
	</select>

	<select id="queryAuthorityComForRedisCom" resultMap="BaseResultMap">
		select
		<include refid="BaseColumnList"/>
		from PRPDCOMPANY WHERE upperPath LIKE concat('%',concat(#{companyCode},'%')) AND
		<foreach item="item" index="index" collection="saaUserPermitDataList" open="(" separator=" or " close=")">
			upperPath like concat('%',concat(#{item.dataValue1},'%'))
		</foreach>
	</select>

	<select id="queryAuthorityComString" resultType="string" parameterType="map">
		select
		companyCode
		from PRPDCOMPANY
		<where>
			<foreach item="item" index="index" collection="stringlist" open=" " separator=" or " close=" ">
				upperPath like concat(#{item},'%')
			</foreach>
		</where>
	</select>

	<select id="queryAuthorityComStringOne" resultType="string" parameterType="map">
		select
		companyCode
		from PRPDCOMPANY
		<where>
			upperPath like concat(#{makeCome},'%')
		</where>
	</select>

	<select id="getAllBranchCompany" resultType="string" parameterType="string">
		select
		companyCode
		from PRPDCOMPANY
		<where>
			comLevel = '2' and validStatus='1'
		</where>
	</select>

	<select id="queryAuthorityComStringupperPath" resultType="string" parameterType="string">
		select
		upperPath
		from PRPDCOMPANY
		<where>
			companyCode = #{makeCome}
		</where>
	</select>

	<select id="queryByUpperComCode" resultMap="BaseResultMap" parameterType="string">
		select
			COMCODE AS companyCode,
			COMCNAME AS comCName,
			UPPERCOMCODE AS upperComCode
		FROM PRPDCOMPANY
		WHERE UPPERCOMCODE = #{upperComCode}
			  AND COMCODE != '31000000'
	</select>

	<select id="queryRegistHandleCom" resultMap="BaseResultMap" parameterType="java.lang.String">
		SELECT <include refid="BaseColumnList" />
		FROM PRPDCOMPANY c
		WHERE c.UPPERCOMCODE like concat(#{comNo},'%') and validStatus='1'
	</select>

	<select id="queryHavePowerComCode" resultMap="BaseResultMap" parameterType="map">
		SELECT <include refid="BaseColumnList" />
		FROM prpdcompany com
		WHERE exists
		(SELECT 1
		FROM saaUserPermitData upd
		WHERE INSTR(#{upperPath}, upd.datavalue1, 1, 1) > 0
		AND upd.FACTORCODE = 'COM'
		AND com.comcode = upd.comcode
		AND upd.USERCODE != '0000000000'
		AND exists(SELECT 1
		FROM SAAUSERGRADE ug, SAAUSERGRADETASK ugt
		WHERE upd.USERGRADEID = ug.ID
		AND ug.GRADEID = ugt.GRADEID
		AND ugt.TASKCODE = #{taskCode}
		AND ug.VALIDSTATUS = '1'
		AND ugt.VALIDSTATUS = '1')
		AND exists (SELECT 1
		FROM T_OPR_INFO use
		WHERE use.USERCODE = upd.USERCODE
		AND upd.USERCODE != '0000000000'
		AND use.VALIDSTATUS = '1')
		)
		AND exists
		(SELECT 1
		FROM saaUserPermitData upd
		WHERE upd.DATAVALUE1 = #{classCode}
		AND upd.FACTORCODE = 'CLASS'
		AND com.comcode = upd.comcode
		AND upd.USERCODE != '0000000000'
		AND exists(SELECT 1
		FROM SAAUSERGRADE ug, SAAUSERGRADETASK ugt
		WHERE upd.USERGRADEID = ug.ID
		AND ug.GRADEID = ugt.GRADEID
		AND ugt.TASKCODE = #{taskCode}
		AND ug.VALIDSTATUS = '1'
		AND ugt.VALIDSTATUS = '1'
		)
		AND exists(SELECT 1
		FROM T_OPR_INFO use
		WHERE use.USERCODE = upd.USERCODE
		AND upd.USERCODE != '0000000000'
		AND use.VALIDSTATUS = '1')
		)

	</select>

	<select id="queryComCode" resultMap="BaseResultMap" parameterType="string">
		SELECT <include refid="BaseColumnList" />
		FROM prpdcompany com
		WHERE EXISTS(
		SELECT 1
		FROM SAAUSERGRADETASK ugt
		WHERE ugt.TASKCODE = #{taskCode}
		AND exists(
		SELECT 1
		FROM T_OPR_INFO use
		WHERE use.USERCODE = ugt.USERCODE
		AND use.COMCODE = com.COMCODE
		)
		AND exists(
		SELECT 1
		FROM T_OPR_INFO use
		WHERE use.USERCODE = ugt.USERCODE
		AND ugt.USERCODE != '0000000000'
		AND use.VALIDSTATUS = '1'
		)
		)
		and com.UPPERPATH like concat(#{upperPath},'%')
	</select>

	<select id="findPrpdCompanysByCode" resultMap="BaseResultMap" parameterType="string">
		select
			COMCODE, COMCNAME, level lev
		from PRPDCOMPANY
			 start with COMCODE in(#{companyCode})
		Connect By COMCODE = Prior UPPERCOMCODE
		AND UPPERCOMCODE != Prior COMCODE
		order by level desc
	</select>

	<select id="findContactComName" resultType="string" parameterType="string">
		select
		concat(com.printComName,com.comcname)
		from PRPDCOMPANY com
		<where>
			companyCode = #{companyCode}
		</where>

	</select>


	<select id="queryComCodeAll" resultType="string">
		select
			distinct t.comcode
		from prpdcompany t
	</select>

	<select id="queryCompanyByUserCode" resultMap="BaseResultMap" parameterType="string">
		select
		<include refid="MyColumnList" />
		from PRPDCOMPANY company,T_OPR_INFO use
		where use.COMCODE = company.COMCODE
		AND use.USERCODE = #{userCode}
	</select>



	<select id="queryPriorComCodeList" resultType="string">
		select comcode from PrpDcompany
							start with comcode = #{companyCode} connect by nocycle prior comcode = uppercomcode

	</select>
	<select id="queryPriorCompanyByComLevel" resultMap="BaseResultMap" parameterType="map">
		select *
		FROM (
				 SELECT *
				 FROM prpdcompany
					  start with comcode = #{companyCode}
								 connect by comcode = Prior uppercomcode
				 and Prior comcode != uppercomcode) COM
		WHERE COM.comLevel = #{comLevel}

	</select>

	<select id="queryComFlag" resultType="string" parameterType="string">
		SELECT COMFLAG
		FROM PRPLCOMFLAG c
		where  c.COMCODE = #{companyCode}
	</select>
</mapper>