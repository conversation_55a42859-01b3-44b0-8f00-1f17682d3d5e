<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaGradeDao">
    <!-- 请在下方添加自定义配置-->
    <select id="findAllGrade" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from SAAGRADE
        <where>
            validStatus = '1'
        </where>
    </select>

    <select id="findGrade" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from SAAGRADE
        <where>
            validStatus = '1'
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="gradeCName != null">
                and gradeCName like concat(#{gradeCName},'%')
            </if>
        </where>
    </select>

    <select id="queryGradeListByUserCode" resultMap="BaseResultMap">
        select
        DISTINCT SAAGRADE.ID,SAAGRADE.GRADECNAME
        from SAAUSERGRADETASK,SAAGRADE
        <where>
            SAAUSERGRADETASK.gradeId = SAAGRADE.id and SAAUSERGRADETASK.USERCODE = #{userCode} and SAAUSERGRADETASK.validStatus
            = '1'
        </where>
    </select>

    <select id="queryGradeByGradeCName" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from SAAGRADE
        <where>
            validStatus = '1' and GRADECNAME = #{gradeCName}
        </where>
    </select>

    <update id="invalidGrade" parameterType="long">
        update
        SAAGRADE
        set validStatus = '0',operateTimeForHis = sysdate
        <where>
            id = #{gradeId}
        </where>
    </update>

</mapper>