<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaGradeTaskDao">
    <!-- 请在下方添加自定义配置-->
    <update id="validGradeTask" parameterType="map">
        update
        SAAGRADETASK
        set validStatus = '1',operateTimeForHis = sysdate
        <where>
            gradeId = #{gradeId} and validStatus = '0' and
            taskId in
            <foreach item="item" index="index" collection="taskIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="validSingleGradeTask" parameterType="map">
        update
        SAAGRADETASK
        set validStatus = '1',operateTimeForHis = sysdate
        <where>
            gradeId = #{gradeId} and taskId = #{taskId} and validStatus = '0'
        </where>
    </update>

    <update id="invalidGradeTaskByGradeId" parameterType="map">
        update
        SAAGRADETASK
        set validStatus = '0',operateTimeForHis = sysdate
        <where>
            gradeId = #{gradeId} and validStatus = '1'
        </where>
    </update>

    <update id="invalidGradeTask" parameterType="map">
        update
        SAAGRADETASK
        set validStatus = '0',operateTimeForHis = sysdate
        <where>
            gradeId = #{gradeId} and validStatus = '1' and
            taskId in
            <foreach item="item" index="index" collection="taskIdSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <select id="querySaaTaskCodeListByGradeId" resultType="string" parameterType="map">
        select
        SAATASK.taskCode
        from
        SAAGRADETASK,SAATASK
        <where>
            SAAGRADETASK.taskId = SAATASK.Id and SAAGRADETASK.gradeId = #{gradeId} and SAAGRADETASK.validStatus = '1'
        </where>
    </select>

    <select id="queryAllGradeTaskByGradeIdAndTaskId" resultMap="BaseResultMap" parameterType="map">
        select
         <include refid="BaseColumnList"/>
        from
        SAAGRADETASK
        <where>
            gradeId = #{gradeId} and taskId = #{taskId}
        </where>
    </select>

    <select id="querySaaGradeTask" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList" />
        from SAAGRADETASK
        where GRADEID=#{gradeId}
        and VALIDSTATUS = '1'
    </select>

</mapper>