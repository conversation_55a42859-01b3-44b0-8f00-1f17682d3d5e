<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaTaskDao">
  <!-- 请在下方添加自定义配置-->

	<!-- 通用查询结果列-->
	<sql id="JoinColumnList">
		 SAATASK.ID as ID,
		 SAATASK.SYSTEM AS system,
		 SAATASK.TASKCODE AS taskCode,
		 SAATASK.PARENTCODE AS parentCode,
		 SAATASK.TASKCNAME AS taskCName,
		 SAATASK.TASKENAME AS taskEName,
		 SAATASK.URL as URL,
		 SAATASK.CREATECODE AS createCode,
		 SAATASK.CREATETIME AS createTime,
		 SAATASK.VALIDFLAG AS validFlag,
		 SAATASK.REMARK,
		 SAATASK.FLAG,
		 SAATASK.SYNFLAG AS synFlag
	</sql>

	<select id="selectUserTask" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="JoinColumnList" />
		from SAATASK,SAAGRADETASK,SAAUSERGRADE
		where SAATASK.ID = SAAGRADETASK.TASKID
		and SAAUSERGRADE.GRADEID=SAAGRADETASK.GRADEID
		and SAAUSERGRADE.USERCODE = #{param1}
	</select>

	<select id="selectUserTaskCode" resultType="String" parameterType="map">
		select
			DISTINCT SAATASK.TASKCODE AS taskCode
		from SAATASK,SAAGRADETASK,SAAUSERGRADE
		where SAATASK.ID = SAAGRADETASK.TASKID
		and SAAUSERGRADE.GRADEID=SAAGRADETASK.GRADEID
		AND SAATASK.VALIDFLAG = '1'
        AND SAATASK.VALIDFLAG = SAAGRADETASK.VALIDSTATUS
        AND SAATASK.VALIDFLAG = SAAUSERGRADE.VALIDSTATUS
		and SAAUSERGRADE.USERCODE = #{param1}
	</select>

	<select id="selectUserByTaskCode" resultType="String" parameterType="map">
		select
			DISTINCT SAAUSERGRADE.USERCODE AS userCode
		from SAATASK,SAAGRADETASK,SAAUSERGRADE
		where SAATASK.ID = SAAGRADETASK.TASKID
		and SAAUSERGRADE.GRADEID=SAAGRADETASK.GRADEID
		and SAATASK.TASKCODE = #{param1}
	</select>

	<select id="selectAll" resultMap="BaseResultMap">
		SELECT
		<include refid="BaseColumnList" />
		FROM SAATASK
		order by ID
	</select>

	<select id="selectByGradeId" resultMap="BaseResultMap">
		SELECT
		<include refid="JoinColumnList" />
		FROM SAATASK, SAAGRADE, SAAGRADETASK
		WHERE SAATASK.ID = SAAGRADETASK.TASKID AND SAAGRADETASK.GRADEID = SAAGRADE.ID
		AND SAAGRADE.id = #{id} AND SAAGRADETASK.VALIDSTATUS = '1'
		order by SAATASK.id
	</select>

    <select id="querySaaTaskListByTaskCodeSet" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAATASK
        <where>
            taskCode in
            <foreach item="item" index="index" collection="taskCodeSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="querySaaTaskByTaskName" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAATASK
        <where>
            TASKCNAME in
            <foreach item="item" index="index" collection="taskCNameSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="queryTaskIdSetByTaskCodeSet" resultType="string" parameterType="string">
        select
        id
        from SAATASK
        <where>
            taskCode in
            <foreach item="item" index="index" collection="taskCodeSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="queryTaskCodeSetByTaskIdSet" resultType="string" parameterType="string">
        select
        taskCode
        from SAATASK
        <where>
            id in
            <foreach item="item" index="index" collection="taskIdSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

	<select id="queryAllSaaTaskCodeListByGradeId" resultMap="BaseResultMap" parameterType="map">
		select
		t.taskCode as taskCode,gt.VALIDSTATUS AS  validFlag
		from
		SAAGRADETASK gt,SAATASK t
		<where>
			gt.taskId = t.Id and gt.gradeId = #{gradeId}
		</where>
	</select>

	<select id="getSaaTaskBySystem" resultMap="BaseResultMap" parameterType="map">
		SELECT
		<include refid="JoinColumnList" />
		FROM SAATASK, SAAGRADE, SAAGRADETASK
		WHERE SAATASK.ID = SAAGRADETASK.TASKID AND SAAGRADETASK.GRADEID = SAAGRADE.ID
		AND SAAGRADETASK.VALIDSTATUS = '1'
		<if test="system != null and system != ''" >
			AND SAATASK.SYSTEM = #{system}
		</if>
		<if test="id != null and id != ''" >
			AND SAAGRADE.id = #{id}
		</if>
		order by SAATASK.id
	</select>

	<select id="getTaskListBySystem"  resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="BaseColumnList"/>
		from SAATASK
		<where>
			SYSTEM = #{system}
		</where>
        order by id
	</select>
	

</mapper>