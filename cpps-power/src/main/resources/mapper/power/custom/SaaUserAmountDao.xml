<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaUserAmountDao">
  <!-- 请在下方添加自定义配置-->

  <select id="queryUserAmountByUserCode" resultMap="BaseResultMap" parameterType="string">
    SELECT
    <include refid="BaseColumnList" />
    FROM SAAUSERAMOUNT
    WHERE USERCODE = #{userCode}
  </select>

  <select id="queryUserAmout" resultMap="BaseResultMap" parameterType="string">
    SELECT
    <include refid="BaseColumnList" />
    FROM SAAUSERAMOUNT
    <where>
      USERCODE = #{userCode}
      <if test="taskCode != null and taskCode != ''" >
        AND TASKCODE = #{taskCode}
      </if>
    </where>

  </select>

    <select id="queryUserAmoutByCodeAndRisk" resultMap="BaseResultMap" parameterType="string">
        SELECT
        <include refid="BaseColumnList"/>
        FROM SAAUSERAMOUNT
        <where>
            USERCODE = #{userCode}
            AND TASKCODE = #{taskCode}
            AND TASKPARENTCODE = #{taskParentCode}
            AND (CLASSCODE  like concat(concat(concat('%',#{classCode}),','),'%')
            OR  CLASSCODE = 'P')
            AND sysdate >= STARTDATE
            AND DEADLINE >= sysdate
        </where>

    </select>

    <select id="queryUserAmountByUndwrtPower" resultMap="BaseResultMap" parameterType="map">
        SELECT
        DISTINCT  A.ID, A.USERCODE, A.USERNAME, A.COMCODE, A.TASKPARENTCODE, A.TASKCODE,
        A.VALUELOWER, A.VALUEUPPER, A.STARTDATE, A.DEADLINE,
        A.INSERTTIMEFORHIS, A.OPERATETIMEFORHIS, A.CLASSCODE
        FROM SAAUSERAMOUNT A
        INNER JOIN SAAUSERGRADETASK B ON B.USERCODE = A.USERCODE
        AND B.TASKCODE = #{powerTaskCode}
        <where>
            <if test="classCode != null and classCode != ''" >
             AND A.CLASSCODE like concat(concat(concat('%',#{classCode}),','),'%')
            </if>
            <if test="taskCode != null and taskCode != ''" >
            AND A.TASKCODE = #{taskCode}
            </if>
            <if test="taskParentCode != null and taskParentCode != ''" >
            AND A.TASKPARENTCODE = #{taskParentCode}
            </if>
            <if test="amount != null and amount != ''" >
            AND A.VALUEUPPER >= #{amount}
            AND #{amount} >= A.VALUELOWER
            </if>
            <if test="companyCode != null and companyCode != ''" >
            AND A.COMCODE = #{companyCode}
            </if>
            AND A.DEADLINE >= SYSDATE
            AND SYSDATE >= A.STARTDATE
        </where>

    </select>

<select id="selectUserAmountPermit" resultMap="BaseResultMap" parameterType="map">
    SELECT
    <include refid="BaseColumnList"/>
    FROM SAAUSERAMOUNT t
    <where>
        t.userCode = #{userCode}
        AND TASKCODE = #{amountTaskCode}
        AND TASKPARENTCODE = #{amountTaskParentCode}
        AND (CLASSCODE  like concat(concat(concat('%',#{classCode}),','),'%')
        OR  CLASSCODE = 'P')
        <if test="amount != null and amount != ''" >
            AND t.VALUEUPPER >= #{amount}
            AND #{amount} >= t.VALUELOWER
        </if>
        AND t.DEADLINE >= SYSDATE
        AND SYSDATE >= t.STARTDATE
    </where>

</select>

</mapper>