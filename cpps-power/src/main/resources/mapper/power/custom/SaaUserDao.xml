<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaUserDao">
  <!-- 请在下方添加自定义配置-->
	<sql id="ColumnList">
		use.USER_CODE AS userCode, use.USER_NAME AS userName,  use.COMCODE AS companyCode, use.MAKECOM AS companyCode,  use.VALID_STATUS AS validStatus
	</sql>

	<select id="selectAll" resultMap="BaseResultMap">
		select
			<include refid="Base_Column_List" />
		from T_OPR_INFO
	</select>
	<select id="selectUserByComCode" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from T_OPR_INFO t1
		 where companyCode in
-- 		(select f.comcode from prpdcompany f, (select getChildLst(#{param1}) as childlist)  k where locate(f.comcode,(k.childlist)))
		(select comcode from PrpDcompany start with comcode = #{param1} connect by nocycle prior comcode = uppercomcode)
		 and exists(select
			DISTINCT SAAUSERGRADE.USERCODE AS userCode
		from SAATASK,SAAGRADETASK,SAAUSERGRADE
		where t1.userCode = SAAUSERGRADE.userCode and  SAATASK.ID = SAAGRADETASK.TASKID
		and SAAUSERGRADE.GRADEID=SAAGRADETASK.GRADEID
		and SAATASK.TASKCODE = 'claim.claim.edit'
		)
	</select>
	<select id="selectSecondUserByComCode" resultMap="BaseResultMap" parameterType="java.lang.String">
		SELECT
		<include refid="Base_Column_List" ></include>
		FROM T_OPR_INFO T1
		WHERE COMCODE IN
		(SELECT COMCODE FROM PRPDCOMPANY START WITH COMCODE = #{companyCode} CONNECT BY NOCYCLE PRIOR COMCODE = UPPERCOMCODE)
	</select>
	<select id="selectPermissionComUser" resultMap="BaseResultMap" parameterType="map">
		SELECT
		<include refid="Base_Column_List" ></include>
		FROM T_OPR_INFO T1
		WHERE COMCODE IN
		(SELECT COMCODE FROM PRPDCOMPANY START WITH COMCODE IN (#{companyCode}) CONNECT BY NOCYCLE PRIOR COMCODE = UPPERCOMCODE)
		AND EXISTS(SELECT
		DISTINCT SAAUSERGRADE.USERCODE AS USERCODE
		FROM SAATASK,SAAGRADETASK,SAAUSERGRADE
		WHERE T1.USERCODE = SAAUSERGRADE.USERCODE AND  SAATASK.ID = SAAGRADETASK.TASKID
		AND SAAUSERGRADE.GRADEID=SAAGRADETASK.GRADEID
		AND SAATASK.TASKCODE = #{taskCode}
		)
	</select>
	<select id="selectAllUserByComCode" resultMap="BaseResultMap" parameterType="java.lang.String">
		SELECT
		<include refid="Base_Column_List" ></include>
		FROM T_OPR_INFO T1
		WHERE COMCODE IN
		(SELECT COMCODE FROM PRPDCOMPANY START WITH COMCODE IN (#{companyCode}) CONNECT BY NOCYCLE PRIOR COMCODE = UPPERCOMCODE)
	</select>
	<select id="querySecondComUser" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from T_OPR_INFO t1
		 where companyCode in
-- 		(select f.comcode from prpdcompany f, (select getChildLst(#{param1}) as childlist)  k where locate(f.comcode,(k.childlist)))
		(select comcode from PrpDcompany start with comcode = #{param1} connect by nocycle prior comcode = uppercomcode)
		 and exists(select
			DISTINCT SAAUSERGRADE.USERCODE AS userCode
		from SAATASK,SAAGRADETASK,SAAUSERGRADE
		where t1.userCode = SAAUSERGRADE.userCode and  SAATASK.ID = SAAGRADETASK.TASKID
		and SAAUSERGRADE.GRADEID=SAAGRADETASK.GRADEID
		and SAATASK.TASKCODE = #{param2}
		)
	</select>

	<select id="queryUserByParam" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from T_OPR_INFO
		<where>
			1=0
			<foreach item="item" index="index" collection="list"  separator=" ">
				or COMCODE in (SELECT COMCODE FROM PRPDCOMPANY WHERE UPPERPATH LIKE concat(#{item},'%'))
			</foreach>
		</where>
	</select>

	<select id="queryPageUserList" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid = "ColumnList" />
		from T_OPR_INFO use
		LEFT JOIN PrpdComPany comPany ON use.companyCode = comPany.companyCode
		<where>
			<choose>
				<when test="queryComCodeList != null and queryComCodeList.size() > 0">
					<foreach item="item" index="index" collection="queryComCodeList" open="(" separator=" or " close=")">
						company.upperPath like concat(#{item.dataValue1},'%')
					</foreach>
				</when>
				<otherwise>
					1=0
				</otherwise>
			</choose>

			<if test="userCode != null and userCode != '' ">
				and use.userCode = #{userCode}
			</if>
			<if test="userName != null and userName != '' ">
				and use.userName = #{userName}
			</if>
			<if test="companyCode != null and companyCode != '' ">
				and use.companyCode = #{companyCode}
			</if>

			<if test="validStatus != null and validStatus != '' ">
			and use.validStatus = #{validStatus}
		</if>

			<if test="taskId != null and taskId != '' ">
				and userCode in(
				select
				DISTINCT task.USERCODE
				from  SAAUSERGRADETASK task
				where task.TASKCODE in(
				select TASKCODE FROM SAATASK WHERE ID = #{taskId}
				) and task.VALIDSTATUS = '1')
			</if>
            <if test="gradeId != null and gradeId != ''">
				and userCode in(
				select
				DISTINCT grade.USERCODE
				from SAAUSERGRADE grade
				where grade.GRADEID = #{gradeId}
				and grade.VALIDSTATUS = '1'
				)

			</if>


		</where>
	</select>
	<select id="queryUserByUserCode" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from T_OPR_INFO
		where USER_CODE = #{param1} and VALID_STATUS='1'
	</select>

    <select id="queryUserByConditions" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_OPR_INFO
        where USER_CODE = #{param1}
		and VALID_STATUS = '1'
        <if test="companyCode != null and companyCode != '' ">
            and companyCode = #{companyCode}
        </if>
    </select>
	<select id="queryPagesUserList" resultType="com.sinosoft.power.po.Gguser"
			parameterType="com.sinosoft.power.po.Gguser">
		select
		<include refid="Base_Column_List" />
		from T_OPR_INFO
		<where>
		<if test="userCode != null and userCode != '' ">
			and userCode = #{userCode}
		</if>

		<if test="userName != null and userName != '' ">
			and userName = #{userName}
		</if>

		<if test="companyCode != null and companyCode != '' ">
		and companyCode = #{companyCode}
		</if>

		<if test="validStatus != null and validStatus != '' ">
			and validStatus = #{validStatus}
		</if>
		</where>
	</select>

	<select id="queryComCondtionPowerUserList" resultMap="BaseResultMap">
		select
		<include refid="ColumnList" />
		from T_OPR_INFO use
		LEFT JOIN PrpdComPany comPany ON use.companyCode = comPany.companyCode
		<where>
			<if test="companyCode != null and companyCode != '' ">
				and company.upperPath like concat(#{companyCode},'%')
			</if>

			<if test="taskCode != null and taskCode != '' ">
				and use.userCode in(
				select
				DISTINCT task.USERCODE
				from  SAAUSERGRADETASK task
				where task.TASKCODE in(
				select TASKCODE FROM SAATASK WHERE taskCode = #{taskCode}
				) and task.VALIDSTATUS = '1')
			</if>

		</where>
	</select>

	<update id="updateGradeByUserCode">
		update
		T_OPR_INFO
		set DEFAULT_GRADEID = #{defaultGradeId}
		<where>
			VALID_STATUS = '1' and USER_CODE in
			<foreach item="userCode" index="index" collection="list" open="(" separator="," close=")">
				#{userCode}
			</foreach>
		</where>
	</update>

	<update id="updateComByUserCode">
		update
		T_OPR_INFO
		set DEFAULT_COM = #{defaultCom}
		<where>
			VALID_STATUS = '1' and USER_CODE in
			<foreach item="userCode" index="index" collection="list" open="(" separator="," close=")">
				#{userCode}
			</foreach>
		</where>

	</update>

	<update id="invalidGradeComByUserCode">
		update
		T_OPR_INFO
		set DEFAULT_GRADEID = null ,DEFAULT_COM = null
		<where>
			VALID_STATUS = '1' and USER_CODE in
			<foreach item="userCode" index="index" collection="list" open="(" separator="," close=")">
				#{userCode}
			</foreach>
		</where>
	</update>

	<update id="invalidDefaultComByUserCodeAndGradeId">
		update
		T_OPR_INFO
		set DEFAULT_COM = null
		<where>
			VALID_STATUS = '1' and DEFAULT_GRADEID = #{gradeId} and DEFAULT_COM = #{upperPath}  and USER_CODE in
			<foreach item="userCode" index="index" collection="list" open="(" separator="," close=")">
				#{userCode}
			</foreach>
		</where>
	</update>

</mapper>