<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaUserGradeDao">
    <resultMap id="UserGradeTaskResultMap" type="com.sinosoft.power.vo.UserGradeMapperTaskVo">
        <result column="userGradeId" property="userGradeId"/>
        <result column="taskCode" property="taskCode"/>
        <result column="gradeName" property="gradeName"/>
        <result column="userCode" property="userCode"/>
    </resultMap>

    <resultMap id="ExportInfoResultMap" type="com.sinosoft.power.vo.ExportUserPowerInfoVo">
        <result column="ID" property="userGradeId"/>
        <result column="userCode" property="userCode"/>
        <result column="userName" property="userName"/>
        <result column="GRADECNAME" property="gradeName"/>
        <result column="COMCODE" property="comCode"/>
        <result column="COMCNAME" property="comName"/>
        <result column="CREATORCODE" property="creatorCode"/>
        <result column="INSERTTIMEFORHIS" property="insertTimeForHis"/>
        <result column="UPDATERCODE" property="updaterCode"/>
        <result column="OPERATETIMEFORHIS" property="operateTimeForHis"/>
    </resultMap>

    <!-- 请在下方添加自定义配置-->
    <select id="findUserGrade" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADE
        <where>
            userCode = #{userCode} and validStatus = '1'
        </where>
    </select>

    <select id="findUserAllValidGrade" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADE
        <where>
            userCode = #{userCode} and validStatus = '1'
        </where>
    </select>

    <select id="findUserAllGrade" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADE
        <where>
            userCode = #{userCode}
        </where>
    </select>

    <select id="findUserGradeByUserCodeAndTaskCode" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADE
        <where>
            validStatus = '1'
            <if test="userCode != null">
                AND USERCODE = #{userCode}
            </if>
            <!--反洗钱不用SAAUSERGRADETASK 因为如果修改角色权限的话会对这张表修改几千次或上万次-->
            <if test="taskCode != null">
                AND GRADEID in (SELECT gradeId FROM SAAUSERGRADETASK WHERE taskCode = #{taskCode})
            </if>
        </where>
    </select>

    <select id="findUserGradeByGradeIds" resultMap="BaseResultMap" parameterType="java.util.List">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADE
        <where>
            validStatus = '1' and gradeId IN
            <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="findAllUserGrade" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADE
    </select>

    <select id="findUserGradeIdTaskMapper" resultMap="UserGradeTaskResultMap">
        SELECT
            SAAUSERGRADE.ID       AS userGradeId,
            SAATASK.TASKCODE      AS taskCode,
            SAAUSERGRADE.USERCODE AS userCode,
            SAAGRADE.GRADECNAME   AS gradeName
        FROM SAATASK, SAAGRADETASK, SAAUSERGRADE, SAAGRADE
        WHERE SAATASK.ID = SAAGRADETASK.TASKID
              AND SAAUSERGRADE.GRADEID = SAAGRADETASK.GRADEID
              AND SAAGRADE.ID = SAAUSERGRADE.GRADEID
    </select>

    <select id="findUserGradeIdTaskMapperByUserCode" resultType="string" parameterType="map">
        select
        taskCode
        from SAAUSERGRADETASK
        <where>
            validStatus = '1' and userCode = #{userCode}
        </where>
    </select>

    <select id="queryGradeIdByUserCode" resultType="Long" parameterType="string">
        SELECT GRADEID
        FROM saausergrade
        WHERE USERCODE = #{userCode} AND VALIDSTATUS = '1'
    </select>

    <!--<delete id="deleteUserGradeByUserCode" parameterType="map">
        delete from SAAUSERGRADE
        where userCode = #{param1}
    </delete>-->


    <!--<delete id="deleteUserGradeByUserCodeAndGradeId"  parameterType="map">
        DELETE
        FROM SAAUSERGRADE
        WHERE userCode = #{userCode}
        <if test="gradeIds != null" >
            AND
            GRADEID NOT in <foreach item="item" index="index" collection="gradeIds"
                                    open="(" separator="," close=")">#{item}</foreach>
        </if>
    </delete>-->

    <update id="invalidGradeByUserCode" parameterType="map">
        update
        SAAUSERGRADE
        set validStatus = '0',operateTimeForHis = sysdate
        <where>
            userCode = #{userCode} and validStatus = '1'
        </where>
    </update>

    <update id="invalidUserGrade" parameterType="map">
        update
        SAAUSERGRADE
        set validStatus = '0',operateTimeForHis = sysdate
        <where>
            userCode = #{userCode} and validStatus = '1' and
            gradeId in
            <foreach item="item" index="index" collection="gradeIdSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="validGrade" parameterType="map">
        update
        SAAUSERGRADE
        set validStatus = '1',operateTimeForHis = sysdate
        <where>
            userCode = #{userCode} and validStatus = '0' and
            gradeId in
            <foreach item="item" index="index" collection="gradeIdSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="validUserGrade" parameterType="map">
        update
        SAAUSERGRADE
        set validStatus = '1',operateTimeForHis = sysdate
        <where>
            userCode = #{userCode} and gradeId = #{gradeId} and validStatus = '0'
        </where>
    </update>

    <select id="queryValidGradeByUserCodeAndGradeId" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADE
        <where>
            userCode = #{userCode} and gradeId =#{gradeId} and validStatus = '1'
        </where>
    </select>

    <select id="queryValidGradeByUserCode" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADE
        <where>
            userCode = #{userCode}  and validStatus = '1'
        </where>
    </select>

    <select id="querySysGradeByUserCodeAndGradeId" resultMap="BaseResultMap" parameterType="map">
        select
         sug.Id
        from SAAUSERGRADE sug,SAAGRADETASK st,SAATASK s
        where
        sug.gradeID= st.GRADEID
        and s.id =st.TASKID
        and s.TASKCODE =#{taskCode}
        and userCode =#{userCode}
    </select>

    <select id="queryValidGradeListByGradeId" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADE
        <where>
            gradeId =#{gradeId} and validStatus = '1'
        </where>
    </select>

    <select id="queryAllGradeByUserCodeAndGradeId" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADE
        <where>
            userCode = #{userCode} and gradeId =#{gradeId}
        </where>
    </select>

    <select id="queryGradeListByUserCodeAndGradeId" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADE
        <where>
            userCode = #{userCode} and validStatus = '1' and gradeId in
            <foreach item="item" index="index" collection="gradeIdSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="queryGradeByUserCodeAndGradeId" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADE tk
        <where>
            userCode = #{userCode} and validStatus = '1'
            <if test="gradeId !=null">
                and gradeId =#{gradeId}
            </if>
        </where>
    </select>

    <select id="queryGradeByUserCodeAndGradeIdSet" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADE tk
        <where>
            userCode = #{userCode} and validStatus = '1'
            <if test="gradeIdSet !=null and gradeIdSet.size() != 0">
                and gradeId in
                <foreach item="gradeId" index="index" collection="gradeIdSet" open="(" separator="," close=")">
                    #{gradeId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="exportPowerUserInfo"  resultMap="ExportInfoResultMap" parameterType="map">

       select   t.ID ,
           s.USERCODE ,
           s.USERNAME ,
           s.COMCODE,
           c.COMCNAME,
           b.GRADECNAME ,
           t.CREATORCODE,
           t.INSERTTIMEFORHIS,
           t.UPDATERCODE,
           t.OPERATETIMEFORHIS
        from   T_OPR_INFO s
            left join SAAUSERGRADE t  on t.USERCODE = s.USERCODE
            left join PRPDCOMPANY c on c.COMCODE = s.COMCODE
            left join SAAGRADE b on b.ID = t.GRADEID
        <where>
            <choose>
                <when test="queryComCodeList != null and queryComCodeList.size() > 0">
                    <foreach item="item" index="index" collection="queryComCodeList" open="(" separator=" or " close=")">
                        c.upperPath like concat(#{item.dataValue1},'%')
                    </foreach>
                </when>
                <otherwise>
                    1=0
                </otherwise>
            </choose>

            <if test="userCode != null and userCode != '' ">
                and s.userCode = #{userCode}
            </if>
            <if test="userName != null and userName != '' ">
                and s.userName = #{userName}
            </if>
            <if test="companyCode != null and companyCode != '' ">
                and s.companyCode = #{companyCode}
            </if>

            <if test="validStatus != null and validStatus != '' ">
                and s.validStatus = #{validStatus}
            </if>

            <if test="taskId != null and taskId != '' ">
                and s.userCode in(
                select
                DISTINCT task.USERCODE
                from  SAAUSERGRADETASK task
                where task.TASKCODE in(
                select TASKCODE FROM SAATASK WHERE ID = #{taskId}
                ) and task.VALIDSTATUS = '1')
            </if>
            <if test="gradeId != null and gradeId != ''">
                and s.userCode in(
                select
                DISTINCT grade.USERCODE
                from SAAUSERGRADE grade
                where grade.GRADEID = #{gradeId}
                )

            </if>
        </where>
        order by  t.USERCODE ,t.GRADEID





    </select>

    <update id="invalidUserGradeForOne">
        update
        SAAUSERGRADE
        set validStatus = '0',operateTimeForHis = sysdate
        <where>
            validStatus = '1' and
            gradeId =#{gradeId}
        </where>

    </update>


    <select id="queryUserCodeByGradeAndCom" resultType="java.lang.String">
        select T1.USERCODE from SAAUSERPERMITDATA T1
        Left join SAAUSERGRADE  T2
        on T1.USERGRADEID = T2.ID
        WHERE T1.DATAVALUE2 = #{companyCode} AND T2.GRADEID=#{gradeId}
    </select>

    <!--Add By ZhouTaoyu      Reason:无序列,手动实现主键自增  time:2019/09/23-->
    <select id="selectMaxId" resultType="java.lang.Long">
        select max (ID) from SAAUSERGRADE
    </select>
</mapper>