<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaUserGradeTaskDao">
    <!-- 请在下方添加自定义配置-->

    <select id="queryTaskListByUserCodeAndGradeId" resultMap="BaseResultMap" parameterType="string">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADETASK
        <where>
            userCode = #{userCode} and gradeId = #{gradeId} and validStatus = '1'
        </where>
    </select>

    <select id="queryTaskListByUserCode" resultMap="BaseResultMap" parameterType="string">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADETASK
        <where>
            userCode = #{userCode} and validStatus = '1'
        </where>
    </select>

    <select id="queryAllTaskListByUserCodeAndGradeId" resultMap="BaseResultMap" parameterType="string">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADETASK
        <where>
            userCode = #{userCode} and gradeId = #{gradeId}
        </where>
    </select>

    <select id="queryTaskListByUserCodeAndTaskCode" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERGRADETASK
        <where>
            userCode = #{userCode} and taskCode = #{taskCode} and validStatus = '1'
        </where>
    </select>

    <select id="queryGradeTaskCodeListByUserCode" resultType="string" parameterType="map">
        select
        DISTINCT taskCode
        from
        SAAUSERGRADETASK
        <where>
            userCode = #{userCode} and validStatus = '1'
        </where>
    </select>

    <select id="queryUserGradeTaskCountByGradeId" resultType="int" parameterType="map">
        select
        count(*)
        from
        SAAUSERGRADETASK
        <where>
            gradeId = #{gradeId} and validStatus = '1'
        </where>
    </select>

    <update id="invalidGradeTaskByUserCode" parameterType="map">
        update
        SAAUSERGRADETASK
        set validStatus = '0',operateTimeForHis = sysdate
        <where>
            userCode = #{userCode} and validStatus = '1'
        </where>
    </update>

    <update id="validGradeTask" parameterType="map">
        update
        SAAUSERGRADETASK
        set validStatus = '1',operateTimeForHis = sysdate
        <where>
            userCode = #{userCode} and validStatus = '0' and gradeId = #{gradeId} and taskCode in
            <foreach item="item" index="index" collection="taskCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="invalidGradeTaskByUserAndGrade" parameterType="map">
        update
        SAAUSERGRADETASK
        set validStatus = '0',operateTimeForHis = sysdate
        <where>
            userCode = #{userCode} AND gradeId = #{gradeId} and validStatus = '1'
        </where>
    </update>

    <update id="invalidUserGradeTask" parameterType="map">
        update
        SAAUSERGRADETASK
        set validStatus = '0',operateTimeForHis = sysdate
        <where>
            userCode = #{userCode} and validStatus = '1' and
            gradeId in
            <foreach item="item" index="index" collection="gradeIdSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="validGradeTaskByUserAndGrade" parameterType="map">
        update
        SAAUSERGRADETASK
        set validStatus = '1',operateTimeForHis = sysdate
        <where>
            userCode = #{userCode} AND gradeId = #{gradeId} and validStatus = '0' and
            taskCode in
            <foreach item="item" index="index" collection="taskCodeSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="validGradeTaskByGradeAndTask" parameterType="map">
        update
        SAAUSERGRADETASK
        set validStatus = '1',operateTimeForHis = sysdate
        <where>
            gradeId = #{gradeId} and validStatus = '0' and
            taskCode in
            <foreach item="item" index="index" collection="taskCodeSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="invalidGradeTaskByGradeAndTask" parameterType="map">
        update
        SAAUSERGRADETASK
        set validStatus = '0',operateTimeForHis = sysdate
        <where>
            gradeId = #{gradeId} and validStatus = '1' and
            taskCode in
            <foreach item="item" index="index" collection="taskCodeSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <select id="queryUserCodeListByTaskCode" resultType="string" parameterType="string">
        select
        userCode
        from SAAUSERGRADETASK
        <where>
            taskCode = #{taskCode} and validStatus = '1'
        </where>
    </select>

    <select id="queryTaskCodeListByUserCode" resultType="string" parameterType="string">
        select
        taskCode
        from SAAUSERGRADETASK
        <where>
            userCode = #{userCode} and validStatus = '1'
        </where>
    </select>

    <select id="queryTaskIdByGradeId" resultType="Long" parameterType="map">
        select DISTINCT t.id
        from SAAUSERGRADETASK ugt, SAATASK t
        WHERE ugt.GRADEID = #{gradeId}
              AND ugt.USERCODE = #{userCode}
              AND ugt.TASKCODE = t.TASKCODE
              AND ugt.VALIDSTATUS = '1'
    </select>

    <select id="queryGradeTaskByCondtion" resultMap="BaseResultMap" parameterType="map">
        SELECT
        t.ID, t.USERCODE, t.GRADEID, t.TASKCODE, t.CREATORCODE,
        t.UPDATERCODE, t.VALIDSTATUS, t.INSERTTIMEFORHIS, t.OPERATETIMEFORHIS
        from SAAUSERGRADETASK t
        right join SAAUSERPERMITDATA s
        on s.USERCODE = t.USERCODE
        left join SAAUSERGRADE b on b.id = s.USERGRADEID
        <where>
            t.USERCODE= #{userCode}
            <if test="taskCode != null and taskCode != ''">
                AND t.TASKCODE = #{taskCode}
            </if>
            AND t.validStatus = '1'
            AND s.VALIDFLAG = '1'
            <if test="factorCode != null and factorCode != ''">
            AND FACTORCODE = #{factorCode}
            </if>
            <if test="classCodeSet != null">
               AND  DATAVALUE1 in
                <foreach item="classCode" index="index" collection="classCodeSet" open="(" separator="," close=")">
                    #{classCode}
                </foreach>
            </if>
            ORDER BY T.ID
            </where>
    </select>

    <update id="invalidGradeTaskByGrade">
        update
        SAAUSERGRADETASK
        set validStatus = '0',operateTimeForHis = sysdate
        <where>
         gradeId = #{gradeId} and validStatus = '1'
        </where>
    </update>


</mapper>