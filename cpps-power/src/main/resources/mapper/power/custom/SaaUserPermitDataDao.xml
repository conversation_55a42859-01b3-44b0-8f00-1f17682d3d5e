<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="com.sinosoft.power.dao.SaaUserPermitDataDao">
    <!-- 请在下方添加自定义配置-->
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERPERMITDATA
    </select>
    <delete id="deleteDataByUserCode" parameterType="map">
        DELETE FROM SAAUSERPERMITDATA
        WHERE userCode = #{param1}
    </delete>

    <select id="selectUserPermitComByUserCode" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERPERMITDATA p where p.usercode=#{param1} and p.factorcode='COM' and p.usergradeid is NULL
    </select>

    <select id="selectUserPermitDataByFactor" resultType="string">
        SELECT DISTINCT DATAVALUE2 FROM SAAUSERPERMITDATA p where p.usercode=#{userCode} and p.factorcode=#{factorCode} and p.validflag='1'
    </select>

    <select id="selectUserPermitByParam" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERPERMITDATA p
        <where>
            <if test="userCode != null">
                AND p.usercode=#{userCode}
            </if>
            <if test="factorCode != null">
                AND p.factorcode=#{factorCode}
            </if>
            <if test="userGradeId != null">
                AND p.usergradeid=#{userGradeId}
            </if>
            <if test="userGradeId == null">
                AND p.usergradeid IS NULL
            </if>
        </where>
    </select>

    <select id="selectAllByUserGradeId" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERPERMITDATA p where p.usergradeid=#{param1} and p.factorcode='COM'
    </select>

    <delete id="deleteDataByUserCodeAndGradeId" parameterType="map">
        DELETE
        FROM SAAUSERPERMITDATA
        WHERE usercode = #{userCode}
        <if test="gradeIds != null">
            AND
            usergradeid NOT in (
            SELECT t.ID FROM saausergrade t WHERE t.GRADEID in
            <foreach item="item" index="index" collection="gradeIds"
                     open="(" separator="," close=")">#{item}
            </foreach>
            )
        </if>
        and usergradeid is NOT NULL
    </delete>

    <delete id="deleteDataByUserCodeAndGradeIdSet" parameterType="map">
        DELETE
        FROM SAAUSERPERMITDATA
        WHERE usercode = #{userCode}
        <if test="gradeIds != null">
            AND
            usergradeid  in (
            SELECT t.ID FROM saausergrade t WHERE t.GRADEID in
            <foreach item="item" index="index" collection="gradeIds"
                     open="(" separator="," close=")">#{item}
            </foreach>
            )
        </if>
        and usergradeid is NOT NULL
    </delete>


    <select id="loadPermitDataSetByUserAndUserGradeId" resultType="string" parameterType="map">
        select
        datavalue1
        from SAAUSERPERMITDATA
        <where>
            userCode = #{userCode} and userGradeId = #{userGradeId} and factorCode = #{factorCode}
        </where>
    </select>

    <delete id="deleteDataByUserAndUserGradeId" parameterType="map">
        delete
        from SAAUSERPERMITDATA
        <where>
            userCode = #{userCode} and userGradeId = #{userGradeId} and dataValue2 in
            <foreach item="item" index="index" collection="upperPathSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </delete>

    <delete id="deleteAllUserGradeData" parameterType="map">
        delete
        from SAAUSERPERMITDATA
        <where>
            userCode = #{userCode} and userGradeId = #{userGradeId} and factorCode = #{factorCode}
        </where>
    </delete>

    <select id="queryUserPermitData" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERPERMITDATA
        <where>
            userCode = #{userCode} and factorCode = #{factorCode}
            <if test="userGradeId != null and userGradeId !=''">
                and   userGradeId = #{userGradeId}
            </if>
            and validflag = '1'
        </where>
    </select>

    <select id="queryUserAllPermitData" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERPERMITDATA
        <where>
            userCode = #{userCode} and factorCode = #{factorCode} and userGradeId in
            <foreach item="item" index="index" collection="userGradeIdSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="queryUserGradePermitData" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERPERMITDATA
        <where>
            userCode = #{userCode} and userGradeId = #{userGradeId}

        </where>
    </select>

    <select id="queryUserPermitDataByCondition" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="BaseColumnList"/>
        from SAAUSERPERMITDATA SADATA
        <where>
            SADATA.FACTORCODE = #{factorcode} and exists (
            select 1 from SAAUSERGRADE SAGRADE,SAAUSERGRADETASK SAGRADETASK where SADATA.USERGRADEID = SAGRADE.ID
            and SAGRADE.GRADEID = SAGRADETASK.GRADEID
            and SAGRADE.USERCODE = SAGRADETASK.USERCODE
            and SAGRADETASK.TASKCODE = #{taskCode}
            and SAGRADETASK.USERCODE = #{userCode}
            and SAGRADE.VALIDSTATUS = #{validStatus}
            and SAGRADETASK.VALIDSTATUS = #{validStatus} )
        </where>
    </select>

    <select id="queryDataValue1ByParamString" resultType="string" parameterType="map">
        select
        SADATA.datavalue1
        from SAAUSERPERMITDATA SADATA
        <where>
            SADATA.FACTORCODE = #{factorcode} and exists (
            select 1 from SAAUSERGRADE SAGRADE,SAAUSERGRADETASK SAGRADETASK where SADATA.USERGRADEID = SAGRADE.ID
            and SAGRADE.GRADEID = SAGRADETASK.GRADEID
            and SAGRADE.USERCODE = SAGRADETASK.USERCODE
            and SAGRADETASK.TASKCODE = #{taskCode}
            and SAGRADETASK.USERCODE = #{userCode}
            and SAGRADE.VALIDSTATUS = #{validStatus}
            and SAGRADETASK.VALIDSTATUS = #{validStatus} )
        </where>
    </select>

    <select id="queryDataValue1ByParam" resultMap="BaseResultMap" parameterType="string">
    select s.DATAVALUE2 from SAAUSERPERMITDATA s
    left join SAAUSERGRADE s1
    on s.usergradeid = s1.id
    left join SAAUSERGRADETASK s2
    on s1.gradeid = s2.gradeid
    where s1.usercode = #{param1} and s2.usercode = #{param1} and  s2.taskcode=#{param2} and s2.validstatus=1
  </select>
    <select id="loadPermitDataValue2SetByUserAndUserGradeId"  resultType="string" parameterType="map">
        select
        DATAVALUE2
        from SAAUSERPERMITDATA
        <where>
            userCode = #{userCode} and userGradeId = #{userGradeId} and factorCode = #{factorCode}
        </where>

    </select>

    <delete id="deleteDataByGrade">
        DELETE
        FROM SAAUSERPERMITDATA
        WHERE
            usergradeid  in (
            SELECT t.ID FROM saausergrade t WHERE t.GRADEID =#{gradeId}
            )
        and usergradeid is NOT NULL
    </delete>

    <delete id="deleteDataByGradeAndComcode">
         DELETE
        FROM SAAUSERPERMITDATA
        WHERE
            usergradeid  in (
            SELECT t.ID FROM saausergrade t WHERE t.GRADEID =#{gradeId}
            )
        and usergradeid is NOT NULL
        and DATAVALUE2 = #{companyCode}
    </delete>

</mapper>