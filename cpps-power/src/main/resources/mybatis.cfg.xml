<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="logImpl" value="STDOUT_LOGGING" />
        <setting name="jdbcTypeForNull" value="NULL"/>
    </settings>
    <environments default="development">
        <environment id="development">
            <transactionManager type="JDBC" />
            <!-- 配置数据库连接信息 -->
            <dataSource type="POOLED">
                <property name="driver" value="oracle.jdbc.driver.OracleDriver" />
                <property name="url" value="*********************************************" />
                <property name="username" value="amlbusi" />
                <property name="password" value="ccic1234" />
               <!-- <property name="driver" value="oracle.jdbc.driver.OracleDriver" />
                <property name="url" value="***********************************" />
                <property name="username" value="CYXCLMBUSI" />
                <property name="password" value="ccicabcd" />-->
            </dataSource>
        </environment>
    </environments>

    <mappers>
        <mapper resource="mapper/power/base/SaaCompanyBaseDao.xml"/>
        <mapper resource="mapper/power/base/SaaGradeBaseDao.xml"/>
        <mapper resource="mapper/power/base/SaaGradeTaskBaseDao.xml"/>
        <mapper resource="mapper/power/base/SaaTaskBaseDao.xml"/>
        <mapper resource="mapper/power/base/SaaUserAmountBaseDao.xml"/>
        <mapper resource="mapper/power/base/SaaUserGradeBaseDao.xml"/>
        <mapper resource="mapper/power/base/SaaUserGradeTaskBaseDao.xml"/>
        <mapper resource="mapper/power/base/SaaUserPermitDataBaseDao.xml"/>
        <mapper resource="mapper/power/base/SaaUserBaseDao.xml"/>
        <mapper resource="mapper/power/base/SaaAddressBaseDao.xml"/>
        <mapper resource="mapper/power/base/SaaBankBaseDao.xml"/>
        <mapper resource="mapper/power/base/SaaCommonDataBaseDao.xml"/>
        <mapper resource="mapper/power/base/SaaUserShortcutBaseDao.xml"/>



        <mapper resource="mapper/power/custom/SaaCompanyDao.xml"/>
        <mapper resource="mapper/power/custom/SaaGradeDao.xml"/>
        <mapper resource="mapper/power/custom/SaaGradeTaskDao.xml"/>
        <mapper resource="mapper/power/custom/SaaTaskDao.xml"/>
        <mapper resource="mapper/power/custom/SaaUserAmountDao.xml"/>
        <mapper resource="mapper/power/custom/SaaUserGradeDao.xml"/>
        <mapper resource="mapper/power/custom/SaaUserGradeTaskDao.xml"/>
        <mapper resource="mapper/power/custom/SaaUserPermitDataDao.xml"/>
        <mapper resource="mapper/power/custom/SaaUserDao.xml"/>
        <mapper resource="mapper/power/custom/SaaAddressDao.xml"/>
        <mapper resource="mapper/power/custom/SaaBankDao.xml"/>
        <mapper resource="mapper/power/custom/SaaCommonDataDao.xml"/>
        <mapper resource="mapper/power/custom/SaaUserShortcutDao.xml"/>


    </mappers>


</configuration>