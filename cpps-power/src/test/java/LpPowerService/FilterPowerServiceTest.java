package LpPowerService;

import com.sinosoft.power.api.SaaPowerApi;
import com.sinosoft.power.po.PrpDCompany;
import com.sinosoft.power.po.SaaTask;
import com.sinosoft.power.service.SaaTaskService;
import com.sinosoft.power.util.CNPowerTaskCode;
import com.sinosoft.power.vo.CompanyVo;
import com.sinosoft.power.vo.SysPowerUserVo;
import org.junit.Test;

import java.util.*;

/**
 * @author: yancun
 * @create: 2018/12/22 16:35
 * @description:
 */
public class FilterPowerServiceTest {


    @Test
    public void Testddd(){
        SaaPowerApi api = SaaPowerApi.getInstance();

        Map<String, String> map = new HashMap<>();
        map.put("userCode", "0000000000");
        map.put("taskCode", CNPowerTaskCode.CLAIM_MENU_CASEQUERY);
        map.put("validStatus", "1");
        map.put("factorcode", "COM");
        map.put("factorcode", "CLASS");
        List<String> stringlist = api.getPowerDaoService().queryDataValue1ByParamString(map);
        Set<String> stringSet = new HashSet<>(stringlist);
        System.out.println("stringlist is"+stringSet);


    }
    @Test
    public void Testddd2(){
        SaaPowerApi api = SaaPowerApi.getInstance();
        CompanyVo vo  = new CompanyVo();
        vo.setComCode("31019998");
        vo.setComCName("高档的说法都是");
        String userList= api.syncComData(vo);
        System.out.println("userList is"+userList);


    }


}
