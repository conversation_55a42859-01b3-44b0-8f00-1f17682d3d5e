package LpPowerService;

import com.sinosoft.power.api.SaaPowerApi;
import com.sinosoft.power.service.PowerService;
import com.sinosoft.power.vo.ComTreeNode;
import com.sinosoft.power.vo.PowerKeyWord;
import org.junit.Test;

import java.util.List;

/*
 * @author: yancun
 * @create: 2018/12/22 10:45
 * @description:

**/
public class PowerServiceTest {


    @Test
    public void queryGradeTaskCodeListByUser(){

        SaaPowerApi api =  SaaPowerApi.getInstance();
        PowerService service = api.getPowerService();
        List<String> lists =  service.queryGradeTaskCodeListByUser("0000000000");
        System.out.println("开始打印用户岗位下所有功能代码");
        for(String list :lists){
            System.out.println(list);
        }
        System.out.println("打印结束");

    }

    //用户的功能权限校验
    @Test
    public void validateDataPower(){
        SaaPowerApi api =  SaaPowerApi.getInstance();
        PowerService service = api.getPowerService();
        String userCode = "8000039332";
        String gradeId= "10143";
        /*List<ComTreeNode> list =service.queryClassTree(userCode,gradeId);
        for(ComTreeNode node:list){
            System.out.println("机构数据："+node.getComCode()
                    +":"+node.getChildren());
        }*/
        String comCode ="";
        String loginComCode ="52010103";
        List<ComTreeNode> list2 = service.queryComTree(userCode,gradeId,null,null,null);
        for(ComTreeNode node:list2){
            System.out.println("机构数据："+node.getComCode()
                    +":"+node.getChildren());
        }
    }

    @Test
    public void  queryPowerUserList(){
        SaaPowerApi api =  SaaPowerApi.getInstance();
        PowerKeyWord key = new PowerKeyWord();
        key.setComCode("31000000");
        key.setTaskCode("aml.menu.casequery");

        String loginUserCode = "0000000000";


    }





}
