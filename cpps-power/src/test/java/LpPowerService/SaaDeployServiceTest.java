package LpPowerService;

import com.sinosoft.power.api.SaaPowerApi;
import com.sinosoft.power.common.util.MybatisUtil;
import com.sinosoft.power.dao.SaaTaskDao;
import com.sinosoft.power.po.SaaTask;
import com.sinosoft.power.service.SaaDeployService;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.Test;

import java.util.List;

/**
 * @author: yancun
 * @create: 2018/12/22 16:35
 * @description:
 */
public class SaaDeployServiceTest {


    @Test
    public void gainUserGradeTaskCodeSet(){
        SqlSessionFactory sessionFactory = MybatisUtil.getSessionFactory(null);
        SqlSession sqlSession = sessionFactory.openSession(true);
        SaaTaskDao taskDao = sqlSession.getMapper(SaaTaskDao.class);
        List<SaaTask> taskList =  taskDao.queryAllSaaTaskCodeListByGradeId(10155L);
        System.out.print("taskList is" +taskList);

        

    }


}
