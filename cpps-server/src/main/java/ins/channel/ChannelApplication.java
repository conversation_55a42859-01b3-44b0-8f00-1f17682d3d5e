package ins.channel;

import ins.platform.PlatformAutoConfiguration;
import ins.platform.filter.LogServletFilter;
import ins.platform.filter.SessionFilter;
import ins.platform.utils.RandomValidateCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import tk.mybatis.spring.annotation.MapperScan;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootApplication
@EnableAspectJAutoProxy
@Import({PlatformAutoConfiguration.class, RandomValidateCode.class})
@MapperScan({"ins.channel.power.dao"})
public class ChannelApplication {

	@Value("${filter.session.excluded-url}")
	private String excludedUrl;

	public static void main(String[] args) {
		SpringApplication.run(ChannelApplication.class, args);
	}

	@Autowired
	private SessionFilter sessionFilter;
	@Autowired
	private LogServletFilter logServletFilter;

	@Bean
	public SessionFilter getSessionFilter(){
		return new SessionFilter();
	}
	@Bean
	public LogServletFilter getLogServletFilter(){
		return new LogServletFilter();
	}

	@Bean
	public FilterRegistrationBean sessionFilterBean() {
		FilterRegistrationBean sessionFilterBean = new FilterRegistrationBean();
		sessionFilterBean.setFilter(sessionFilter);
		Map<String, String> params = new HashMap<String, String>();
		params.put("excludedUrl", excludedUrl);
		sessionFilterBean.setInitParameters(params);
		List<String> urlPatterns = new ArrayList<String>();
		urlPatterns.add("/*");
		sessionFilterBean.setUrlPatterns(urlPatterns);
		sessionFilterBean.setOrder(4);
		return sessionFilterBean;
	}
	@Bean
	public FilterRegistrationBean logServletFilterBean() {
		FilterRegistrationBean logServletFilterBean = new FilterRegistrationBean();
		logServletFilterBean.setFilter(logServletFilter);
		List<String> urlPatterns = new ArrayList<String>();
		urlPatterns.add("/*");
		logServletFilterBean.setUrlPatterns(urlPatterns);
		logServletFilterBean.setOrder(5);
		return logServletFilterBean;
	}

}
