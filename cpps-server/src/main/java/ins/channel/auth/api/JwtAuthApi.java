package ins.channel.auth.api;

import ins.channel.auth.service.AuthService;
import ins.channel.auth.vo.JwtAuthenticationRequest;
import ins.channel.auth.vo.JwtAuthenticationResponse;
import ins.framework.exception.BusinessException;
import ins.platform.common.ResponseVo;
import ins.platform.utils.RandomValidateCode;
import ins.platform.utils.SessionHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/api/auth")
@Api(tags = "Auth", description = "用户认证服务")
@Slf4j
public class JwtAuthApi {

    @Autowired
    private AuthService authService;

    @Autowired
    private RandomValidateCode validate;

    @ApiOperation("登录")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public ResponseVo<JwtAuthenticationResponse> login(@RequestBody JwtAuthenticationRequest jwtAuthenticationRequest,HttpServletRequest request) {
        Object validateCode = request.getSession().getAttribute("validateCode");
        log.info(""+validateCode);
        log.info(jwtAuthenticationRequest.getValidateCode());
        if(validateCode == null || jwtAuthenticationRequest.getValidateCode() == null || !validateCode.toString().toLowerCase().equals(jwtAuthenticationRequest.getValidateCode().toLowerCase())){
            throw new BusinessException("验证码输入错误");
        }
        request.getSession().removeAttribute("validateCode");
        JwtAuthenticationResponse jwtAuthenticationResponse = authService.login(jwtAuthenticationRequest.getUsercode(), jwtAuthenticationRequest.getPassword());
        return ResponseVo.ok(jwtAuthenticationResponse);
    }

    @RequestMapping(value = "/getImage", method = RequestMethod.GET)
    public void validateCode(HttpServletRequest request, HttpServletResponse response){
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);
        validate.getImage(request, response);
    }

    @ApiOperation("刷新Token")
    @GetMapping(value = "/refresh")
    public ResponseVo<JwtAuthenticationResponse> refresh() throws Exception {
        String token = SessionHelper.getAuthToken();
        JwtAuthenticationResponse jwtAuthenticationResponse = authService.refresh(token);
        return ResponseVo.ok(jwtAuthenticationResponse);
    }

    /*@ApiOperation("登出")
    @GetMapping(value = "/logOut")
    public ResponseVo<Void> logOut() throws Exception {
        //记录退出时间
        authService.logOut(SessionHelper.getAuthToken());
        return ResponseVo.ok();
    }*/

}
