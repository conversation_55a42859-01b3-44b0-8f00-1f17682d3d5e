package ins.channel.auth.service;

import com.sinosoft.power.api.SaaPowerApi;
import com.sinosoft.power.po.SaaUserGrade;
import com.sinosoft.power.po.SaaUserPermitData;
import com.sinosoft.power.util.PowerConstant;
import ins.framework.exception.BusinessException;
import ins.channel.auth.vo.JwtAuthenticationResponse;
import ins.channel.power.dao.SaauserpermitdataDao;
import ins.channel.power.po.Saauserpermitdata;
import ins.channel.user.service.GguserService;
import ins.channel.user.vo.GguserVo;
import ins.platform.common.SysUser;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.JwtAuthHelper;
import ins.platform.utils.PasswordUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class AuthService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private JwtAuthHelper authHelper;

    @Autowired
    private GguserService userService;
    @Autowired
    private SaauserpermitdataDao saauserpermitdataDao;

    @Transactional
    public JwtAuthenticationResponse login(String usercode, String password) {
        String md5Password = PasswordUtils.encodePassword(password);
        SaaPowerApi saaApi = SaaPowerApi.getInstance();
        GguserVo userVo = userService.queryOne(usercode);
        if (Objects.isNull(userVo)) {
            throw new BusinessException("用户不存在!");
        }
        if ("0".equals(userVo.getValidInd())) {
            throw new BusinessException("用户已失效");
        }
        if (!md5Password.equals(userVo.getPassword())) {
            throw new BusinessException("密码不正确");
        }
        List<SaaUserGrade> gradeList = saaApi.getPowerDaoService().queryGradeByUserCodeAndGradeIdSet(userVo.getUserCode(), null);
        if (gradeList == null || gradeList.isEmpty()) {
            throw new BusinessException("用户没有配置岗位，不允许登录");
        }
        //Modify By Zhoutaoyu 用户业务权限校验 2019/11/20
        List<String> list = saauserpermitdataDao.queryComcodeListByUserCode(userVo.getUserCode());
        if (list == null || list.isEmpty()) {
            throw new BusinessException("用户没有配置关联机构，不允许登录");
        }
        SysUser sysUser = BeanCopyUtils.clone(userVo, SysUser.class);
        sysUser.setLoginTime(new Date());
        final String token = authHelper.generateToken(sysUser);
        List<String> gradeTaskCodeList = saaApi.getPowerService().queryTaskCodeListByUser(sysUser.getUserCode());
        //Modify By Zhoutaoyu 用户角色功能菜单校验 2019/11/20
        if (gradeTaskCodeList == null || gradeTaskCodeList.isEmpty()) {
            throw new BusinessException("用户没有配置功能菜单，不允许登录");
        }
        JwtAuthenticationResponse jwtAuthenticationResponse = new JwtAuthenticationResponse();
        jwtAuthenticationResponse.setUserCode(sysUser.getUserCode());
        jwtAuthenticationResponse.setUserName(sysUser.getUserCname());
        jwtAuthenticationResponse.setComCode(sysUser.getCompanyCode());
        jwtAuthenticationResponse.setComCName(sysUser.getComCName());
        jwtAuthenticationResponse.setLoginTime(sysUser.getLoginTime());
        jwtAuthenticationResponse.setTaskList(gradeTaskCodeList);
        jwtAuthenticationResponse.setToken(token);
        //Modify By zhoutaoyu 登录时返回所属公司名称、代码以及用户职级,供前端分页查询时展示使用 20210222
        jwtAuthenticationResponse.setTeamManager(sysUser.getTeamManager());
        jwtAuthenticationResponse.setOuterCode(sysUser.getOuterCode());
        jwtAuthenticationResponse.setUserInd(sysUser.getUserInd());
        jwtAuthenticationResponse.setDepartment(sysUser.getDepartment());
        logger.info("{}用户登录成功,token:{}", usercode, token);
        return jwtAuthenticationResponse;
    }

    @Transactional
    public JwtAuthenticationResponse refresh(String oldToken) {
        JwtAuthenticationResponse jwtAuthenticationResponse = null;
        if (authHelper.canTokenBeRefreshed(oldToken)) {
            String newToken = authHelper.refreshToken(oldToken);
            SysUser sysUser = authHelper.getSysUser(newToken);
            List<String> gradeTaskCodeList = SaaPowerApi.getInstance().getPowerService().queryTaskCodeListByUser(sysUser.getUserCode());
            jwtAuthenticationResponse = new JwtAuthenticationResponse();
            jwtAuthenticationResponse.setUserCode(sysUser.getUserCode());
            jwtAuthenticationResponse.setUserName(sysUser.getUserCname());
            jwtAuthenticationResponse.setComCode(sysUser.getCompanyCode());
            jwtAuthenticationResponse.setComCName(sysUser.getComCName());
            jwtAuthenticationResponse.setTaskList(gradeTaskCodeList);
            jwtAuthenticationResponse.setLoginTime(sysUser.getLoginTime());
            jwtAuthenticationResponse.setTeamManager(sysUser.getTeamManager());
            jwtAuthenticationResponse.setOuterCode(sysUser.getOuterCode());
            jwtAuthenticationResponse.setUserInd(sysUser.getUserInd());
            jwtAuthenticationResponse.setToken(newToken);
            logger.info("{}用户续约token成功,新token:{}", sysUser.getUserCode(), newToken);
        } else {
            logger.info("续约token失败,token已失效!");
        }
        return jwtAuthenticationResponse;
    }

    @Transactional
    public void logOut(String token) {
//        SysUser sysUser = jwtTokenUtil.getSysUser(token);
        //记录当前角色机构登出时间
//        saveAuthLogOutTime(userCode,companyCode,gradeid,logintime,new Date());
    }

    /**
     * 记录登出时间
     * @param userCode
     * @param companyCode
     * @param gradeid
     * @param logintime
     * @param now
     */
    /*@Transactional
    public void saveAuthLogOutTime(String userCode,String companyCode, Long gradeid, Date logintime,Date now) {
        //记录当前角色机构登出时间
        //去掉毫秒
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str = df.format(logintime);
        Date newDate = null;
        try {
            newDate = df.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Example example = new Example(TOprLogtrace.class);
        Example.Criteria and = example.and();
        and.andEqualTo("userCode",userCode);
        and.andEqualTo("companyCode",companyCode);
        and.andEqualTo("gradeid",gradeid);
        and.andEqualTo("logintime",newDate);
        TOprLogtrace tOprLogtrace = new TOprLogtrace();
        tOprLogtrace.setLogouttime(now);
        double logontime = now.getTime()-logintime.getTime();
        BigDecimal bigDecimal = new BigDecimal(logontime / 60000);
        //登陆时长保留两位小数
        logontime = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        tOprLogtrace.setLogontime(logontime);
        tOprLogtraceDao.updateByExampleSelective(tOprLogtrace,example);
    }*/

}
