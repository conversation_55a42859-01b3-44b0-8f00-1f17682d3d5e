package ins.channel.auth.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("登录认证请求信息")
public class  JwtAuthenticationRequest {

    @ApiModelProperty(value = "用户工号",required = true,position = 1,example = "0000000000")
    private String usercode;

    @ApiModelProperty(value = "密码",required = true,position = 2,example = "0000")
    private String password;

    @ApiModelProperty(value = "验证码",required = true,position = 2,example = "0000")
    private String validateCode;

}
