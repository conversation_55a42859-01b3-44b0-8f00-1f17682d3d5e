package ins.channel.auth.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("登录认证响应信息")
public class JwtAuthenticationResponse {

    private String userCode;

    private String userName;

    private String token;

    private String email;

    private String comCode;

    private String comCName;

    private List<String> taskList;

    private Date loginTime;

    //Modify By zhoutaoyu 登录时返回所属公司名称、代码以及用户职级,供前端分页查询时展示使用 20210222
    @ApiModelProperty("所属公司名称")
    private String teamManager;
    @ApiModelProperty("所属公司代码")
    private String outerCode;
    @ApiModelProperty("职级 1:管理员 2:业务管理岗 3:业务员 codeType ='UserRank'")
    private String userInd;

    @ApiModelProperty("所属部门")
    private String department;
}
