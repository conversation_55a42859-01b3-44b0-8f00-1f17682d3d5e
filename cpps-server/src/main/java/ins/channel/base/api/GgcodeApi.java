package ins.channel.base.api;

import ins.channel.base.service.GgcodeService;
import ins.channel.base.service.GgriskService;
import ins.channel.base.vo.*;
import ins.channel.code.vo.GgcodeVo;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/ggcode")
@Api(tags = "GgcodeApi", description = "代码配置表")
public class GgcodeApi{

    @Autowired
    private GgcodeService service;
    @Autowired
    private GgriskService ggriskService;

    @ApiOperation(value = "分页查询代码信息")
    @PostMapping(value = "/searchPage")
    public ResponseVo<PageResult<GgcodeVo>> search(@Valid @ModelAttribute GgcodeSearchRequestVo ggcodeSearchRequestVo) {
        PageResult<GgcodeVo> result = service.search(ggcodeSearchRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "保存代码信息")
    @PostMapping(value = "/saveCodeInfo")
    public ResponseVo<Integer> save(@Valid @RequestBody GgcodeSaveRequestVo ggcodeSaveRequestVo) {
        int result = service.create(ggcodeSaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "更新代码信息")
    @PostMapping(value = "/updateCodeInfo")
    public ResponseVo<Integer> update(@Valid @RequestBody GgcodeSaveRequestVo ggcodeSaveRequestVo) {
        int result = service.update(ggcodeSaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "根据主键查询一条数据")
    @PostMapping(value = "/selectOne")
    public ResponseVo<GgcodeVo> selectOne(@RequestBody String gid) {
        GgcodeVo result = service.queryOne(gid);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "删除代码信息")
    @PostMapping(value = "/deleteOne")
    public ResponseVo<Integer> deleteOne(@RequestBody String gid) {
        int result = service.delete(gid);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "分页查询 代码值 供下拉框使用")
    @PostMapping(value = "/codeInfoForSelectPage")
    public ResponseVo<PageResult<GgcodeSelectResponseVo>> codeInfoForSelectPage(GgcodeSelectRequestVo codeInfo) {
        PageResult<GgcodeSelectResponseVo> result = service.codeInfoForSelectPage(codeInfo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "查询所有险种信息供下拉框使用")
    @PostMapping(value = "/riskForSelectAll")
    public ResponseVo<List<GgcodeSelectResponseVo>> riskForSelectAll() {
        List<GgcodeSelectResponseVo> result = ggriskService.riskForSelectAll();
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "通过 代码类型 查询对应所有 业务代码")
    @PostMapping(value = "/codeInfoForSelect")
    public ResponseVo<List<GgcodeSelectResponseVo>> codeInfoForSelect(@RequestBody String codeType) {
        List<GgcodeSelectResponseVo> result = service.codeInfoForSelect(codeType);
        return ResponseVo.ok(result);
    }
}