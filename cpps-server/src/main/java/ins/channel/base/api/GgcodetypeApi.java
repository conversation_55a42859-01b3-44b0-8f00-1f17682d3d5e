package ins.channel.base.api;

import ins.channel.base.service.GgcodetypeService;
import ins.channel.base.vo.*;
import ins.channel.codetype.vo.GgcodetypeVo;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/ggcodetypeApi")
@Api(tags = "GgcodetypeApi", description = "代码类型配置表")
public class GgcodetypeApi {

    @Autowired
    private GgcodetypeService service;

    @ApiOperation(value = "分页查询代码类型")
    @PostMapping(value = "/searchPage")
    public ResponseVo<PageResult<GgcodetypeVo>> search(@Valid @ModelAttribute GgcodetypeSearchRequestVo ggcodetypeSearchRequestVo) {
        PageResult<GgcodetypeVo> result = service.search(ggcodetypeSearchRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "保存代码类型")
    @PostMapping(value = "/saveCodeType")
    public ResponseVo<Integer> save(@Valid @RequestBody GgcodetypeSaveRequestVo ggcodetypeSaveRequestVo) {
        int result = service.create(ggcodetypeSaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "更新代码类型")
    @PostMapping(value = "/updateCodeType")
    public ResponseVo<Integer> update(@Valid @RequestBody GgcodetypeSaveRequestVo ggcodetypeSaveRequestVo) {
        int result = service.update(ggcodetypeSaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "根据主键查询一条数据")
    @PostMapping(value = "/selectOne")
    public ResponseVo<GgcodetypeVo> selectOne(@RequestBody String gid) {
        GgcodetypeVo result = service.queryOne(gid);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "删除代码类型")
    @PostMapping(value = "/deleteOne")
    public ResponseVo<Integer> deleteOne(@RequestBody String gid) {
        int result = service.delete(gid);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "分页查询 代码值 供下拉框使用")
    @PostMapping(value = "/codeTypeForSelectPage")
    public ResponseVo<PageResult<GgcodetypeSelectResponseVo>> codeInfoForSelectPage(GgcodetypeSelectRequestVo codeInfo) {
        PageResult<GgcodetypeSelectResponseVo> result = service.codeTypeForSelectPage(codeInfo);
        return ResponseVo.ok(result);
    }
}