package ins.channel.base.api;

import ins.channel.base.service.GgcompanyService;
import ins.channel.base.vo.*;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/ggcompany")
@Api(tags = "GgcompanyApi", description = "关联机构信息")
public class GgcompanyApi {

    @Autowired
    private GgcompanyService service;

//
//    @ApiOperation(value = "分页查询机构信息")
//    @PostMapping(value = "/searchPage")
//    public ResponseVo<PageResult<GgcompanyVo>> search(@Valid @RequestBody GgcompanySearchRequestVo ggcompanySearchRequestVo) {
//        PageResult<GgcompanyVo> result = service.search(ggcompanySearchRequestVo);
//        return ResponseVo.ok(result);
//    }

    @ApiOperation(value = "分页查询机构信息")
    @PostMapping(value = "/searchPage")
//    @PostMapping(value = "/searchByRelatedPage")
    public ResponseVo<PageResult<CompanyOperateRequestVo>> searchPage(@Valid @RequestBody GgcompanySearchRequestVo ggcompanySearchRequestVo) {
        PageResult<CompanyOperateRequestVo> result = service.searchPage(ggcompanySearchRequestVo);
        return ResponseVo.ok(result);
    }
//
//    @ApiOperation(value = "根据机构代码查询 上 级机构")
//    @PostMapping(value = "/selectUpperComInfo")
//    public ResponseVo<List<GgcompanyLevelSelectVo>> selectUpperComInfo(@RequestBody String comcode) {
//        List<GgcompanyLevelSelectVo> result = service.selectUpperComInfo(comcode);
//        return ResponseVo.ok(result);
//    }
//
//    @ApiOperation(value = "根据机构代码查询 下 级机构")
//    @PostMapping(value = "/selectUnderComInfo")
//    public ResponseVo<List<GgcompanyLevelSelectVo>> selectUnderComInfo(@RequestBody String comcode) {
//        List<GgcompanyLevelSelectVo> result = service.selectUnderComInfo(comcode);
//        return ResponseVo.ok(result);
//    }
//

//    @ApiOperation(value = "分页查询 机构代码 供下拉框使用")
//    @PostMapping(value = "/companyForSelectPage")
//    public ResponseVo<PageResult<GgcompanySelectResponseVo>> codeInfoForSelectPage(GgcompanySelectRequestVo companyInfo) {
//        PageResult<GgcompanySelectResponseVo> result = service.codeTypeForSelectPage(companyInfo);
//        return ResponseVo.ok(result);
//    }

    @ApiOperation(value = "查询登录用户有权限的机构代码,供下拉框使用")
    @PostMapping(value = "/companyForSelect")
    public ResponseVo<List<GgcompanySelectResponseVo>> companyForSelect(@RequestBody GgcompanySelectRequestVo companyInfo) {
        List<GgcompanySelectResponseVo> result = service.companyForSelect(companyInfo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "查询所有机构代码与名称(无权限控制),供下拉框使用")
    @PostMapping(value = "/companyForSelectAll")
    public ResponseVo<List<GgcompanySelectResponseVo>> companyForSelectAll() {
        List<GgcompanySelectResponseVo> result = service.companyForSelectAll();
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "根据机构代码查询待修改的关联机构")
    @PostMapping(value = "/selectOne")
    public ResponseVo<CompanyOperateRequestVo> selectOne(@RequestBody GgcompanySearchRequestVo comcode) {
        CompanyOperateRequestVo result = service.queryOne(comcode);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "新增关联机构")
    @PostMapping(value = "/save")
    public ResponseVo<String> save(@RequestBody CompanyOperateRequestVo vo) {
        String result = service.create(vo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "修改关联机构")
    @PostMapping("/modifyCompany")
    public ResponseVo<Void> modifyCompany(@RequestBody CompanyOperateRequestVo vo) {
        service.modifyCompany(vo);
        return ResponseVo.ok();
    }
}