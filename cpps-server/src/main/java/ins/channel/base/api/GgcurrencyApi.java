package ins.channel.base.api;

import ins.channel.base.service.GgcurrencyService;
import ins.channel.base.vo.GgcurrencySelectRequestVo;
import ins.channel.base.vo.GgcurrencySelectResponseVo;
import ins.channel.base.vo.GgcurrencySaveRequestVo;
import ins.channel.base.vo.GgcurrencySearchRequestVo;
import ins.channel.currency.vo.GgcurrencyVo;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/ggcurrency")
@Api(tags = "GgcurrencyApi", description = "币别信息")
public class GgcurrencyApi {

    @Autowired
    private GgcurrencyService service;

    @ApiOperation(value = "分页查询币别信息")
    @PostMapping(value = "/searchPage")
    public ResponseVo<PageResult<GgcurrencyVo>> search(@Valid @ModelAttribute GgcurrencySearchRequestVo ggcurrencySearchRequestVo) {
        PageResult<GgcurrencyVo> result = service.search(ggcurrencySearchRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "保存币别信息")
    @PostMapping(value = "/saveGgcurrency")
    public ResponseVo<Integer> save(@Valid @RequestBody GgcurrencySaveRequestVo ggcurrencySaveRequestVo) {
        int result = service.create(ggcurrencySaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "更新币别信息")
    @PostMapping(value = "/updateGgcurrency")
    public ResponseVo<Integer> update(@Valid @RequestBody GgcurrencySaveRequestVo ggcurrencySaveRequestVo) {
        int result = service.update(ggcurrencySaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "根据主键查询一条数据")
    @PostMapping(value = "/selectOne")
    public ResponseVo<GgcurrencyVo> selectOne(@RequestBody String gid) {
        GgcurrencyVo result = service.queryOne(gid);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "删除币别信息")
    @PostMapping(value = "/deleteOne")
    public ResponseVo<Integer> deleteOne(@RequestBody String gid) {
        int result = service.delete(gid);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "分页查询 币别 供下拉框使用")
    @PostMapping(value = "/currnecyForSelectPage")
    public ResponseVo<PageResult<GgcurrencySelectResponseVo>> codeInfoForSelectPage(GgcurrencySelectRequestVo codeInfo) {
        PageResult<GgcurrencySelectResponseVo> result = service.codeTypeForSelectPage(codeInfo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "查询所有 币别")
    @PostMapping(value = "/currnecyForSelect")
    public ResponseVo<List<GgcurrencySelectResponseVo>> codeInfoForSelectPage() {
        List<GgcurrencySelectResponseVo> result = service.codeTypeForSelect();
        return ResponseVo.ok(result);
    }
}