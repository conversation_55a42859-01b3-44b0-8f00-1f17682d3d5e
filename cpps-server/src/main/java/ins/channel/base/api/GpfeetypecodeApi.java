package ins.channel.base.api;

import ins.channel.base.service.GpfeetypecodeService;
import ins.channel.base.vo.GpfeetypecodeSaveRequestVo;
import ins.channel.base.vo.GpfeetypecodeSearchRequestVo;
import ins.channel.feetypecode.vo.GpfeetypecodeVo;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/gpfeetypecode")
@Api(tags = "GpfeetypecodeApi", description = "费用类型与计算符号对应表")
public class GpfeetypecodeApi{

    @Autowired
    private GpfeetypecodeService service;

    @ApiOperation(value = "分页查询费用类型与计算符号对应")
    @PostMapping(value = "/searchPage")
    public ResponseVo<PageResult<GpfeetypecodeVo>> search(@Valid @ModelAttribute GpfeetypecodeSearchRequestVo gpfeetypecodeSearchRequestVo) {
        PageResult<GpfeetypecodeVo> result = service.search(gpfeetypecodeSearchRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "保存费用类型与计算符号对应")
    @PostMapping(value = "/saveFeeTypeCode")
    public ResponseVo<Integer> save(@Valid @RequestBody GpfeetypecodeSaveRequestVo gpfeetypecodeSaveRequestVo) {
        int result = service.create(gpfeetypecodeSaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "更新费用类型与计算符号对应")
    @PostMapping(value = "/updateFeeTypeCode")
    public ResponseVo<Integer> update(@Valid @RequestBody GpfeetypecodeSaveRequestVo gpfeetypecodeSaveRequestVo) {
        int result = service.update(gpfeetypecodeSaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "根据主键查询一条数据")
    @PostMapping(value = "/selectOne")
    public ResponseVo<GpfeetypecodeVo> selectOne(@RequestBody String gid) {
        GpfeetypecodeVo result = service.queryOne(gid);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "删除费用类型与计算符号对应")
    @PostMapping(value = "/deleteOne")
    public ResponseVo<Integer> deleteOne(@RequestBody String gid) {
        int result = service.delete(gid);
        return ResponseVo.ok(result);
    }
}