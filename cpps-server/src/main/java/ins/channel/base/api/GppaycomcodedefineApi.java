package ins.channel.base.api;

import ins.channel.base.service.GppaycomcodedefineService;
import ins.channel.base.vo.GppaycomcodeSelectRequestVo;
import ins.channel.base.vo.GppaycomcodeSelectResponseVo;
import ins.channel.base.vo.GppaycomcodedefineSaveRequestVo;
import ins.channel.base.vo.GppaycomcodedefineSearchRequestVo;
import ins.channel.paycomcodedefine.vo.GppaycomcodedefineVo;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/gppaycomcodedefine")
@Api(tags = "GppaycomcodedefineApi", description = "收付机构定义表")
public class GppaycomcodedefineApi {

    @Autowired
    private GppaycomcodedefineService service;

    @ApiOperation(value = "分页查询收付机构定义")
    @PostMapping(value = "/searchPage")
    public ResponseVo<PageResult<GppaycomcodedefineVo>> search(@Valid @ModelAttribute GppaycomcodedefineSearchRequestVo gppaycomcodedefineSearchRequestVo) {
        PageResult<GppaycomcodedefineVo> result = service.search(gppaycomcodedefineSearchRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "出单机构转换为收付机构")
    @PostMapping(value = "/saveCodeDefine")
    public ResponseVo<Integer> save(@Valid @RequestBody List<String> companycodes) {
        int result = service.create(companycodes);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "更新收付机构定义")
    @PostMapping(value = "/updateCodeDefine")
    public ResponseVo<Integer> update(@Valid @RequestBody GppaycomcodedefineSaveRequestVo gpfeetypecodeSaveRequestVo) {
        int result = service.update(gpfeetypecodeSaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "根据主键查询一条数据")
    @PostMapping(value = "/selectOne")
    public ResponseVo<GppaycomcodedefineVo> selectOne(@RequestBody String gid) {
        GppaycomcodedefineVo result = service.queryOne(gid);
        return ResponseVo.ok(result);
    }

    /*@ApiOperation(value = "删除收付机构定义")
    @PostMapping(value = "/deleteOne")
    public ResponseVo<Integer> deleteOne(@RequestBody String gid) {
        int result = service.delete(gid);
        return ResponseVo.ok(result);
    }*/

    @ApiOperation(value = "分页查询 收付机构 供下拉框使用")
    @PostMapping(value = "/paycomcodeForSelectPage")
    public ResponseVo<PageResult<GppaycomcodeSelectResponseVo>> codeInfoForSelectPage(GppaycomcodeSelectRequestVo codeInfo) {
        PageResult<GppaycomcodeSelectResponseVo> result = service.codeTypeForSelectPage(codeInfo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "分页查询 收付机构 供下拉框使用")
    @PostMapping(value = "/paycomcodeForSelectNoLimit")
    public ResponseVo<List<GppaycomcodeSelectResponseVo>> paycomcodeForSelectNoLimit() {
        List<GppaycomcodeSelectResponseVo> result = service.paycomcodeForSelectNoLimit();
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "删除收付机构")
    @PostMapping(value = "/deleteOne")
    public ResponseVo<String> deleteOne(@RequestBody String paymentComcode) {
        service.delete(paymentComcode);
        return ResponseVo.ok("撤销收付机构权限成功");
    }

}