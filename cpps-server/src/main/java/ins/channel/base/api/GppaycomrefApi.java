package ins.channel.base.api;

import ins.channel.base.service.GppaycomrefService;
import ins.channel.base.vo.GppaycomrefRelatedInfoSaveRequestVo;
import ins.channel.base.vo.GppaycomrefSaveRequestVo;
import ins.channel.base.vo.GppaycomrefSearchRequestVo;
import ins.channel.paycomref.vo.GppaycomrefVo;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/gppaycomref")
@Api(tags = "GppaycomrefApi", description = "归属机构与收付机构关联")
public class GppaycomrefApi{

    @Autowired
    private GppaycomrefService service;

    @ApiOperation(value = "分页查询归属机构与收付机构关联")
    @PostMapping(value = "/searchPage")
    public ResponseVo<PageResult<GppaycomrefVo>> search(@Valid @ModelAttribute GppaycomrefSearchRequestVo gppaycomrefSearchRequestVo) {
        PageResult<GppaycomrefVo> result = service.search(gppaycomrefSearchRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "更新归属机构与收付机构关联")
    @PostMapping(value = "/updatePayComRef")
    public ResponseVo<Integer> update(@Valid @RequestBody GppaycomrefSaveRequestVo gppaycomrefSaveRequestVo) {
        int result = service.update(gppaycomrefSaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "更新归属机构与收付机构关联")
    @PostMapping(value = "/saveRelatedInfo")
    public ResponseVo<String> saveRelatedInfo(@Valid @RequestBody GppaycomrefRelatedInfoSaveRequestVo vo) {
        service.saveRelatedInfo(vo);
        return ResponseVo.ok("success");
    }

    @ApiOperation(value = "更新归属机构与收付机构关联")
    @PostMapping(value = "/queryRelatedInfo")
    public ResponseVo<List<GppaycomrefVo>> queryRelatedInfo(@Valid @RequestBody String companyCode) {
        List<GppaycomrefVo>  result = service.queryRelatedInfo(companyCode);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "根据出单机构查询收付机构")
    @PostMapping(value = "/queryPaymentByCompany")
    public ResponseVo<GppaycomrefVo> queryPaymentByCompany(@Valid @RequestBody GppaycomrefVo gppaycomrefVo) {
        GppaycomrefVo  result = service.queryOne(gppaycomrefVo);
        return ResponseVo.ok(result);
    }

}