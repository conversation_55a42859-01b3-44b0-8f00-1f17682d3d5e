package ins.channel.base.service;


import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.base.vo.GgchannelSelectRequestVo;
import ins.channel.base.vo.GgchannelSelectResponseVo;
import ins.channel.base.vo.GgchannelSerachRequestVo;
import ins.channel.channel.dao.GgchannelDao;
import ins.channel.channel.po.Ggchannel;
import ins.channel.channel.po.GgchannelSelect;
import ins.channel.channel.vo.GgchannelVo;
import ins.platform.common.CrudService;
import ins.platform.common.PageResult;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;

@Service
@Slf4j
@Transactional
public class GgchannelService implements CrudService<GgchannelVo,String> {
    @Autowired
    private GgchannelDao ggchannelDao;

    @Override
    public String create(GgchannelVo vo) {
        Assert.notNull(vo, "Object must have value");
        ggchannelDao.insertSelective(BeanCopyUtils.clone(vo, Ggchannel.class));
        return vo.getChannelCode();
    }

    @Override
    public int update(GgchannelVo vo) {
        Assert.notNull(vo, "对象不能为null");
        return ggchannelDao.updateSelectiveByPrimaryKey(BeanCopyUtils.clone(vo, Ggchannel.class));
    }

    @Override
    public GgchannelVo selectByPrimaryKey(String id) {
        Assert.hasText(id, "参数不能为空");
        return BeanCopyUtils.clone(ggchannelDao.selectByPrimaryKey(id), GgchannelVo.class);
    }

    @Override
    public int delete(List<String> ids) {
        Assert.notNull(ids, "对象不能为null");
        Assert.notEmpty(ids, "列表不能为空");
        return ggchannelDao.deleteBatchByPrimaryKeys(ids);
    }

    @Override
    public PageResult<GgchannelVo> search(GgchannelVo vo) {
        PageParam pageParam = PageHelper.getPageParam(vo);
        Page<Ggchannel> ggchannels = ggchannelDao.selectPage(pageParam, BeanCopyUtils.clone(vo, Ggchannel.class));
        return PageHelper.convert(pageParam, ggchannels, GgchannelVo.class);
    }

    /**
     * 分页模糊查询渠道类型
     * @param ggchannelSerachRequestVo
     * @return
     */
    public PageResult<GgchannelVo> pageByCondition(GgchannelSerachRequestVo ggchannelSerachRequestVo) {
        PageParam pageParam = PageHelper.getPageParam(ggchannelSerachRequestVo);
        Page<Ggchannel> ggchannels = ggchannelDao.pageByCondition(pageParam, BeanCopyUtils.clone(ggchannelSerachRequestVo, Ggchannel.class));
        return PageHelper.convert(pageParam, ggchannels, GgchannelVo.class);
    }

    /**
     * 将CodeCode 翻译成相应的语言
     * @param type C:汉语  T：繁体  E：英语
     *
     */
    public String translate(String channelCode, String type){
        Ggchannel ggchannel = ggchannelDao.translate(channelCode);
        if(ggchannel != null) {
            switch (type) {
                case "C":
                    return ggchannel.getChannelCname();
                case "T":
                    return ggchannel.getChannelTname();
                case "E":
                    return ggchannel.getChannelEname();
                default:
                    return "";
            }
        }
        return "";
    }

    /**
     * 查询下拉框的值
     */
    public PageResult<GgchannelSelectResponseVo> channelForSelectPage(GgchannelSelectRequestVo queryInfo) {
        PageParam pageParam = PageHelper.getPageParam(queryInfo);
        Page<GgchannelSelect> results = ggchannelDao.channelForSelectPage(pageParam, queryInfo.getQueryInfo());
        return PageHelper.convert(pageParam, results, GgchannelSelectResponseVo.class);
    }
}