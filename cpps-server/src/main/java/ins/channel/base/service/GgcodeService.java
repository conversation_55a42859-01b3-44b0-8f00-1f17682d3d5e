package ins.channel.base.service;


import ins.channel.base.vo.GgcodeSaveRequestVo;
import ins.channel.base.vo.GgcodeSearchRequestVo;
import ins.channel.base.vo.GgcodeSelectRequestVo;
import ins.channel.base.vo.GgcodeSelectResponseVo;
import ins.channel.code.dao.GgcodeDao;
import ins.channel.code.po.Ggcode;
import ins.channel.code.po.GgcodeSearch;
import ins.channel.code.po.GgcodeSelect;
import ins.channel.code.vo.GgcodeVo;
import ins.framework.common.ResultPage;
import ins.framework.exception.BusinessException;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.framework.mybatis.util.Pages;
import ins.platform.common.PageResult;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
@Transactional
public class GgcodeService{
    @Autowired
    private GgcodeDao ggcodeDao;

    public int create(GgcodeSaveRequestVo vo) {
        Ggcode ggcode = BeanCopyUtils.clone(vo, Ggcode.class);
        int count = 0;
        try{
            count = ggcodeDao.insertSelectiveAuto(ggcode);
        }catch (DuplicateKeyException e){
            throw new BusinessException("代码配置信息重复，不允许录入");
        }catch (Exception e){
            log.error("保存代码配置信息错误{}",e);
            throw new BusinessException(e.getMessage());
        }
        return count;
    }

    /**
     * 更新数据
     * @param vo
     * @return
     */
    public int update(GgcodeSaveRequestVo vo) {
        int count = 0;
        try{
            count = ggcodeDao.updateSelectiveByPrimaryKey(BeanCopyUtils.clone(vo, Ggcode.class));
        }catch (DuplicateKeyException e){
            throw new BusinessException("代码配置信息重复，不允许录入");
        }catch (Exception e){
            log.error("保存代码配置信息错误{}",e);
            throw new BusinessException(e.getMessage());
        }
        return count;
    }

    /**
     * 查询单条数据
     * @param gid
     * @return
     */
    public GgcodeVo queryOne(String gid) {
        return BeanCopyUtils.clone(ggcodeDao.selectByPrimaryKey(gid),GgcodeVo.class);
    }

    /**
     * 根据主键删除数据
     * @param id
     * @return
     */
    public int delete(String id) {
        return ggcodeDao.deleteByPrimaryKey(id);
    }

    /**
     * 前端根据实际业务情况来进行分页查询分页查询
     * @param vo
     * @return
     */
    public PageResult<GgcodeVo> search(GgcodeSearchRequestVo vo) {
        PageParam pageParam = PageHelper.getPageParam(vo);
        Page<Ggcode> results = ggcodeDao.searchPage(pageParam, BeanCopyUtils.clone(vo, GgcodeSearch.class));
        return PageHelper.convert(pageParam, results, GgcodeVo.class);
    }

    /**
     *  根据对象属性查询
     * @param ggcodeVo
     * @return
     */
    public List<GgcodeVo> findGgcodeVoList(GgcodeVo ggcodeVo){
        PageParam pageParam = new PageParam();
        Page<Ggcode> page = ggcodeDao.selectPage(pageParam, BeanCopyUtils.clone(ggcodeVo, Ggcode.class));
        ResultPage<GgcodeVo> resultPage = Pages.convert(pageParam, page, GgcodeVo.class);
        return resultPage.getData();
    }


    /**
     * 查询下拉框的值 分页
     */
    public PageResult<GgcodeSelectResponseVo> codeInfoForSelectPage(GgcodeSelectRequestVo queryInfo) {
        PageParam pageParam = PageHelper.getPageParam(queryInfo);
        Page<GgcodeSelect> results = ggcodeDao.codeInfoForSelectPage(pageParam, queryInfo.getQueryInfo());
        return PageHelper.convert(pageParam, results, GgcodeSelectResponseVo.class);
    }


    /**
     * 查询下拉框的所有值
     */
    public List<GgcodeSelectResponseVo> codeInfoForSelect(String codeType) {
        List<GgcodeSelect> results = ggcodeDao.codeInfoForSelect(codeType);
        return BeanCopyUtils.cloneList(results,GgcodeSelectResponseVo.class);
    }

    /**
     * 将CodeCode 翻译成相应的语言
     * @param type C:汉语  T：繁体  E：英语
     *
     */
    public String translate(String codeType,String codeCode, String type){
        Ggcode ggcode = ggcodeDao.translate(codeType,codeCode);
        if(ggcode != null) {
            switch (type) {
                case "C":
                    return ggcode.getCodeCname();
                case "T":
                    return ggcode.getCodeTname();
                case "E":
                    return ggcode.getCodeEname();
                default:
                    return "";
            }
        }
        return "";
    }

}
