package ins.channel.base.service;

import ins.framework.exception.BusinessException;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.base.vo.GgcodetypeSaveRequestVo;
import ins.channel.base.vo.GgcodetypeSearchRequestVo;
import ins.channel.base.vo.GgcodetypeSelectRequestVo;
import ins.channel.base.vo.GgcodetypeSelectResponseVo;
import ins.channel.codetype.dao.GgcodetypeDao;
import ins.channel.codetype.po.Ggcodetype;
import ins.channel.codetype.po.GgcodetypeSearch;
import ins.channel.codetype.po.GgcodetypeSelect;
import ins.channel.codetype.vo.GgcodetypeVo;
import ins.platform.common.PageResult;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@Transactional
public class GgcodetypeService {
    @Autowired
    private GgcodetypeDao ggcodetypeDao;

    public int create(GgcodetypeSaveRequestVo vo) {
        vo.setCodeType(vo.getCodeType().trim());
        Ggcodetype ggcodetype = BeanCopyUtils.clone(vo, Ggcodetype.class);
        int count = 0;
        try{
            count = ggcodetypeDao.insertSelectiveAuto(ggcodetype);
        }catch (DuplicateKeyException e){
            throw new BusinessException("代码类型信息重复，不允许录入");
        }catch (Exception e){
            log.error("保存代码类型信息错误{}",e);
            throw new BusinessException(e.getMessage());
        }
        return count;
    }

    /**
     * 更新数据
     * @param vo
     * @return
     */
    public int update(GgcodetypeSaveRequestVo vo) {
        int count = 0;
        try{
            vo.setCodeType(vo.getCodeType().trim());
            count = ggcodetypeDao.updateSelectiveByPrimaryKey(BeanCopyUtils.clone(vo, Ggcodetype.class));
        }catch (DuplicateKeyException e){
            throw new BusinessException("代码类型信息重复，不允许修改");
        }catch (Exception e){
            log.error("保存代码类型信息错误{}",e);
            throw new BusinessException(e.getMessage());
        }
        return count;
    }

    /**
     * 查询单条数据
     * @param gid
     * @return
     */
    public GgcodetypeVo queryOne(String gid) {
        return BeanCopyUtils.clone(ggcodetypeDao.selectByPrimaryKey(gid),GgcodetypeVo.class);
    }

    /**
     * 根据主键删除数据
     * @param id
     * @return
     */
    public int delete(String id) {
        return ggcodetypeDao.deleteByPrimaryKey(id);
    }

    /**
     * 前端根据实际业务情况来进行分页查询分页查询
     * @param vo
     * @return
     */
    public PageResult<GgcodetypeVo> search(GgcodetypeSearchRequestVo vo) {
        PageParam pageParam = PageHelper.getPageParam(vo);
        Page<Ggcodetype> gpcodetypes = ggcodetypeDao.searchPage(pageParam, BeanCopyUtils.clone(vo, GgcodetypeSearch.class));
        return PageHelper.convert(pageParam, gpcodetypes, GgcodetypeVo.class);
    }

    /**
     * 查询下拉框的值
     */
    public PageResult<GgcodetypeSelectResponseVo> codeTypeForSelectPage(GgcodetypeSelectRequestVo queryInfo) {
        PageParam pageParam = PageHelper.getPageParam(queryInfo);
        Page<GgcodetypeSelect> results = ggcodetypeDao.codeTypeForSelectPage(pageParam, queryInfo.getQueryInfo());
        return PageHelper.convert(pageParam, results, GgcodetypeSelectResponseVo.class);
    }
}