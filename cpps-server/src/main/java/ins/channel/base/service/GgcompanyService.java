package ins.channel.base.service;

import ins.channel.power.dao.SaauserpermitdataDao;
import ins.channel.power.po.Saauserpermitdata;
import ins.framework.exception.BusinessException;
import ins.framework.exception.PermissionException;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.base.vo.*;
import ins.channel.company.dao.GgcompanyDao;
import ins.channel.company.po.Ggcompany;
import ins.channel.company.po.GgcompanySearch;
import ins.channel.company.po.GgcompanySelect;
import ins.channel.company.vo.GgcompanyVo;
import ins.platform.common.PageResult;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.PageHelper;
import ins.platform.utils.SessionHelper;
import ins.platform.utils.UserPermitDataHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.*;

@Service
@Slf4j
@Transactional
public class GgcompanyService {
    @Autowired
    private GgcompanyDao ggcompanyDao;
    @Autowired
    private SaauserpermitdataDao saauserpermitdataDao;

    /**
     * 查询单条数据
     *
     * @param comcode
     * @return
     */
    public CompanyOperateRequestVo queryOne(GgcompanySearchRequestVo comcode) {
        return BeanCopyUtils.clone(ggcompanyDao.selectByPrimaryKey(comcode.getCompanyCode()), CompanyOperateRequestVo.class);
    }

    /**
     * 查询上级机构信息
     *
     * @param comcode
     * @return
     */
    public List<GgcompanyLevelSelectVo> selectUpperComInfo(String comcode) {
        return BeanCopyUtils.cloneList(ggcompanyDao.selectUpperComInfo(comcode), GgcompanyLevelSelectVo.class);
    }

    /**
     * 查询下级机构信息
     *
     * @param comcode
     * @return
     */
    public List<GgcompanyLevelSelectVo> selectUnderComInfo(String comcode) {
        return BeanCopyUtils.cloneList(ggcompanyDao.selectUnderComInfo(comcode), GgcompanyLevelSelectVo.class);
    }

    /**
     * 模糊查询机构信息
     *
     * @param ggcompanySerachRequestVo
     * @return
     */
    public PageResult<GgcompanyVo> pageByCondition(GgcompanySerachRequestVo ggcompanySerachRequestVo) {
        PageParam pageParam = PageHelper.getPageParam(ggcompanySerachRequestVo);
        Page<Ggcompany> results = ggcompanyDao.pageByCondition(pageParam, BeanCopyUtils.clone(ggcompanySerachRequestVo, Ggcompany.class));
        return PageHelper.convert(pageParam, results, GgcompanyVo.class);
    }

    /**
     * 查询下拉框的值
     */
    public List<GgcompanySelectResponseVo> companyForSelect(GgcompanySelectRequestVo companyInfo) {
        // 数据权限：操作机构
        Set<String> permitComs = UserPermitDataHelper.getUserPermitCom();
        if (StringUtils.isNotBlank(companyInfo.getCompanyCode())) { // 如果查询参数中指定了机构，直接做权限校验
            if (!permitComs.contains(companyInfo.getCompanyCode())) {
                throw new PermissionException("没有该机构操作权限");
            }
        } else {
            companyInfo.setCompanyCodes(permitComs);
        }
        List<GgcompanySelect> results = ggcompanyDao.companyForSelect(companyInfo.getQueryInfo(), permitComs);
        return BeanCopyUtils.cloneList(results, GgcompanySelectResponseVo.class);
    }

    /**
     * 查询所有机构代码与名称,供下拉框使用
     */
    public List<GgcompanySelectResponseVo> companyForSelectAll() {
        List<GgcompanySelect> results = ggcompanyDao.companyForSelectAll();
        return BeanCopyUtils.cloneList(results, GgcompanySelectResponseVo.class);
    }

    /**
     * 前端根据实际业务情况来进行分页查询
     *
     * @param vo
     * @return
     */
//    public PageResult<GgcompanyVo> search(GgcompanySearchRequestVo vo) {
//        PageParam pageParam = PageHelper.getPageParam(vo);
//        Page<Ggcompany> results = ggcompanyDao.searchPage(pageParam, BeanCopyUtils.clone(vo, GgcompanySearch.class));
//        for (Ggcompany ggcompany : results) {
//            if (StringUtils.isBlank(ggcompany.getPaymentComcode())) {
//                ggcompany.setPaymentComcode("");
//            }
//        }
//        return PageHelper.convert(pageParam, results, GgcompanyVo.class);
//    }

    /**
     * 前端根据实际业务情况来进行分页查询
     *
     * @param vo
     * @return
     */
    public PageResult<CompanyOperateRequestVo> searchPage(GgcompanySearchRequestVo vo) {
        PageParam pageParam = PageHelper.getPageParam(vo);
        GgcompanySearch clone = BeanCopyUtils.clone(vo, GgcompanySearch.class);
        //总公司不允许修改删除,查询时直接排除
        clone.setComLevel("com_head");
        Page<Ggcompany> results = ggcompanyDao.searchPage(pageParam, clone);
        return PageHelper.convert(pageParam, results, CompanyOperateRequestVo.class);
    }

    public String create(CompanyOperateRequestVo vo) {
        int i = 0;
        Assert.hasText(vo.getCompanyCode(), "机构代码不能为空!");
        Assert.hasText(vo.getCompanyCname(), "机构名称不能为空!");
        Assert.hasText(vo.getUpperCompanyCode(), "上级机构代码不能为空!");
        //判断是否有该机构
        Ggcompany ggcompany = new Ggcompany();
        ggcompany.setCompanyCode(vo.getCompanyCode());
        List<Ggcompany> companyCodeList = ggcompanyDao.selectByCondition(ggcompany);
        if (!CollectionUtils.isEmpty(companyCodeList)) {
            throw new BusinessException("机构代码已存在!");
        }
        ggcompany.setCompanyCode(vo.getUpperCompanyCode());
        List<Ggcompany> upperCompanyList = ggcompanyDao.selectByCondition(ggcompany);
        if (CollectionUtils.isEmpty(upperCompanyList)) {
            throw new BusinessException("无效的上级机构代码!");
        }
        ggcompany.setCompanyCode(null);
        ggcompany.setCompanyCname(vo.getCompanyCname());
        List<Ggcompany> companyNameList = ggcompanyDao.selectByCondition(ggcompany);
        if (!CollectionUtils.isEmpty(companyNameList)) {
            throw new BusinessException("机构名称已存在!");
        }
        //添加机构
        Ggcompany clone = BeanCopyUtils.clone(vo, Ggcompany.class);

        clone.setValidInd("1");
        clone.setCreatorCode(SessionHelper.getLoginUser().getUserCode());
        //TODO 机构类型 1:机构;2部门  默认为2
        clone.setComType("2");
        //TODO com_level 机构级别  com_head 总公司；com_branch分公司 com_support支公司 ；com_business 业务部门；dep_2二级部门; dep_3三级部门; dep_4四级部门
        clone.setComLevel("com_branch");
        clone.setCreateTime(new Date());
        try {
            i = ggcompanyDao.insertSelective(clone);
        } catch (BusinessException e) {
            log.info(MessageFormat.format("新增关联机构异常:{0}", e.getMessage()));
        }
        return i == 1 ? "保存成功!" : "保存失败!网络连接异常,请稍后再试或联系运维人员处理";
    }

//    public Map<String, String> queryUpperNameByUpperCode(String upperCompanyCode,Integer flag) {
//        Ggcompany ggcompany  = ggcompanyDao.selectUpperNameByUpperCode(upperCompanyCode);
//        if (ggcompany==null) {
//            throw new BusinessException("机构不存在!");
//        }
//        Map<String, String> resultMap = new HashMap<>();
//        if (flag == 0) {
//            resultMap.put("upperCompanyName", ggcompany.getCompanyCname());
//        } else {
//            resultMap.put("companyCname", ggcompany.getCompanyCname());
//        }
//        return resultMap;
//    }

    public void modifyCompany(CompanyOperateRequestVo vo) {
        Assert.notNull(vo.getCompanyCode(), "机构代码不能为空!");
        Assert.notNull(vo.getCompanyCname(), "机构名称不能为空!");
        Assert.notNull(vo.getUpperCompanyCode(), "上级机构代码不能为空!");
        Assert.notNull(vo.getValidInd(), "有效状态不能为空!");
        Ggcompany ggcompany = new Ggcompany();
        ggcompany.setCompanyCode(vo.getCompanyCode());
        List<Ggcompany> ggcompanyList = ggcompanyDao.selectByCondition(ggcompany);
        if (CollectionUtils.isEmpty(ggcompanyList)) {
            throw new BusinessException("机构不存在!");
        }

        ggcompany.setAddressCname(vo.getAddressCname());
        ggcompany.setInsurerCname(vo.getInsurerCname());
        ggcompany.setPhoneNumber(vo.getPhoneNumber());
        ggcompany.setEmail(vo.getEmail());
        ggcompany.setRemark(vo.getRemark());
        ggcompany.setValidInd(vo.getValidInd());
        ggcompany.setUpdaterCode(SessionHelper.getLoginUser().getUserCode());
        ggcompany.setUpdateTime(new Date());
        ggcompanyDao.updateSelectiveByPrimaryKey(ggcompany);

        if ("0".equals(vo.getValidInd())) {
            //如果机构修改为无效,将用户关联机构表中对应的记录置为无效
            Saauserpermitdata saauserpermitdata = new Saauserpermitdata();
            Set<String> plusSet = new HashSet<>();
            plusSet.add(vo.getCompanyCode());
            Map<String, Object> map = new HashMap<>();
            map.put("userCode", SessionHelper.getLoginUser().getUserCode());
            map.put("comCodeSet", plusSet);
            saauserpermitdataDao.invalidvalidSingleUserpermitdata(map);
        }
    }
}