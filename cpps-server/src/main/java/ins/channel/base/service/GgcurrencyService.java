package ins.channel.base.service;

import ins.framework.exception.BusinessException;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.base.vo.GgcurrencySaveRequestVo;
import ins.channel.base.vo.GgcurrencySearchRequestVo;
import ins.channel.base.vo.GgcurrencySelectRequestVo;
import ins.channel.base.vo.GgcurrencySelectResponseVo;
import ins.channel.currency.dao.GgcurrencyDao;
import ins.channel.currency.po.Ggcurrency;
import ins.channel.currency.po.GgcurrencySearch;
import ins.channel.currency.po.GgcurrencySelect;
import ins.channel.currency.vo.GgcurrencyVo;
import ins.platform.common.PageResult;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
@Transactional
public class GgcurrencyService {
    @Autowired
    private GgcurrencyDao ggcurrencyDao;


    public int create(GgcurrencySaveRequestVo vo) {
        Ggcurrency ggcurrency = BeanCopyUtils.clone(vo, Ggcurrency.class);
        int count = 0;
        try{
            count = ggcurrencyDao.insertSelectiveAuto(ggcurrency);
        }catch (DuplicateKeyException e){
            throw new BusinessException("币别信息重复，不允许录入");
        }catch (Exception e){
            log.error("保存币别信息错误{}",e);
            throw new BusinessException(e.getMessage());
        }
        return count;
    }

    /**
     * 更新数据
     * @param vo
     * @return
     */
    public int update(GgcurrencySaveRequestVo vo) {
        int count = 0;
        try{
            count = ggcurrencyDao.updateSelectiveByPrimaryKey(BeanCopyUtils.clone(vo, Ggcurrency.class));
        }catch (DuplicateKeyException e){
            throw new BusinessException("币别信息重复，不允许录入");
        }catch (Exception e){
            log.error("保存币别信息错误{}",e);
            throw new BusinessException(e.getMessage());
        }
        return count;
    }

    /**
     * 查询单条数据
     * @param gid
     * @return
     */
    public GgcurrencyVo queryOne(String gid) {
        return BeanCopyUtils.clone(ggcurrencyDao.selectByPrimaryKey(gid),GgcurrencyVo.class);
    }

    /**
     * 根据主键删除数据
     * @param id
     * @return
     */
    public int delete(String id) {
        return ggcurrencyDao.deleteByPrimaryKey(id);
    }

    /**
     * 前端根据实际业务情况来进行分页查询分页查询
     * @param vo
     * @return
     */
    public PageResult<GgcurrencyVo> search(GgcurrencySearchRequestVo vo) {
        PageParam pageParam = PageHelper.getPageParam(vo);
        Page<Ggcurrency> results = ggcurrencyDao.searchPage(pageParam, BeanCopyUtils.clone(vo, GgcurrencySearch.class) );
        return PageHelper.convert(pageParam, results, GgcurrencyVo.class);
    }

    /**
     * 查询下拉框的值
     */
    public PageResult<GgcurrencySelectResponseVo> codeTypeForSelectPage(GgcurrencySelectRequestVo queryInfo) {
        PageParam pageParam = PageHelper.getPageParam(queryInfo);
        Page<GgcurrencySelect> results = ggcurrencyDao.currencyForSelectPage(pageParam, queryInfo.getQueryInfo());
        return PageHelper.convert(pageParam, results, GgcurrencySelectResponseVo.class);
    }

    /**
     * 查询下拉框的值
     */
    public List<GgcurrencySelectResponseVo> codeTypeForSelect() {
        List<GgcurrencySelect> results = ggcurrencyDao.currencyForSelect();
        return BeanCopyUtils.cloneList(results,GgcurrencySelectResponseVo.class);
    }
    /**
     * 将CodeCode 翻译成相应的语言
     * @param type C:汉语  T：繁体  E：英语
     *
     */
    public String translate(String codeCode, String type){
        Ggcurrency ggcurrency = ggcurrencyDao.translate(codeCode);
        if(ggcurrency != null) {
            switch (type) {
                case "C":
                    return ggcurrency.getCurrencyCname();
                case "T":
                    return ggcurrency.getCurrencyTname();
                case "E":
                    return ggcurrency.getCurrencyEname();
                default:
                    return "";
            }
        }
        return "";
    }
}