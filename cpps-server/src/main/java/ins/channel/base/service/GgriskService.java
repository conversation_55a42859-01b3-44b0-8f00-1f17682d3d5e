package ins.channel.base.service;


import ins.channel.base.vo.*;
import ins.channel.code.po.GgcodeSelect;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.risk.dao.GgriskDao;
import ins.channel.risk.po.Ggrisk;
import ins.channel.risk.po.GgriskSearch;
import ins.channel.risk.po.GgriskSelect;
import ins.channel.risk.vo.GgriskVo;
import ins.platform.common.PageResult;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
@Transactional
public class GgriskService {
    @Autowired
    private GgriskDao ggriskDao;

    public int create(GgriskSaveRequestVo vo) {
        Ggrisk ggrisk = BeanCopyUtils.clone(vo, Ggrisk.class);
        int count = ggriskDao.insertSelectiveAuto(ggrisk);
        return count;
    }

    /**
     * 更新数据
     * @param vo
     * @return
     */
    public int update(GgriskSaveRequestVo vo) {
        return ggriskDao.updateSelectiveByPrimaryKey(BeanCopyUtils.clone(vo, Ggrisk.class));
    }

    /**
     * 查询单条数据
     * @param gid
     * @return
     */
    public GgriskVo queryOne(String gid) {
        return BeanCopyUtils.clone(ggriskDao.selectByPrimaryKey(gid),GgriskVo.class);
    }

    /**
     * 根据主键删除数据
     * @param id
     * @return
     */
    public int delete(String id) {
        return ggriskDao.deleteByPrimaryKey(id);
    }

    /**
     * 前端根据实际业务情况来进行分页查询分页查询
     * @param vo
     * @return
     */
    public PageResult<GgriskVo> search(GgriskSearchRequestVo vo) {
        PageParam pageParam = PageHelper.getPageParam(vo);
        Page<Ggrisk> results = ggriskDao.searchPage(pageParam, BeanCopyUtils.clone(vo, GgriskSearch.class));
        return PageHelper.convert(pageParam, results, GgriskVo.class);
    }

    /**
     * 查询下拉框的值
     */
    public PageResult<GgriskSelectVo> riskInfoForSelectPage(GgriskSelectRequestVo queryInfo) {
        PageParam pageParam = PageHelper.getPageParam(queryInfo);
        Page<GgriskSelect> results = ggriskDao.riskInfoForSelectPage(pageParam, queryInfo.getQueryInfo());
        return PageHelper.convert(pageParam, results, GgriskSelectVo.class);
    }

    /**
     * 根据查询条件返回所有值，不进行分页
     */
    public List<GgriskSelectVo> riskInfoForSelect(GgriskSelectVo ggriskSelectVo) {
        List<GgriskSelect> results = ggriskDao.riskInfoForSelect(ggriskSelectVo.getRiskCode(),ggriskSelectVo.getRiskCname() );
        return BeanCopyUtils.cloneList(results, GgriskSelectVo.class);
    }


    /**
     * 将CodeCode 翻译成相应的语言
     * @param type C:汉语  T：繁体  E：英语
     *
     */
    public String translate(String riskCode, String type){
        Ggrisk ggrisk = ggriskDao.translate(riskCode);
        if(ggrisk != null) {
            switch (type) {
                case "C":
                    return ggrisk.getRiskCname();
                case "T":
                    return ggrisk.getRiskTname();
                case "E":
                    return ggrisk.getRiskEname();
                default:
                    return "";
            }
        }
        return "";
    }

    /**
     * @description: 查询所有险种信息供下拉框使用
     * @param:
     * @author: zhoutaoyu
     * @date: 2021/2/9
     * @return:
     **/
    public List<GgcodeSelectResponseVo> riskForSelectAll() {
        List<GgcodeSelect> ggcodeSelectList = ggriskDao.riskForSelectAll();
        return BeanCopyUtils.cloneList(ggcodeSelectList, GgcodeSelectResponseVo.class);
    }
}