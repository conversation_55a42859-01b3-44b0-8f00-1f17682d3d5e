package ins.channel.base.service;

import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.base.vo.GpfeetypecodeSaveRequestVo;
import ins.channel.base.vo.GpfeetypecodeSearchRequestVo;
import ins.channel.feetypecode.dao.GpfeetypecodeDao;
import ins.channel.feetypecode.po.Gpfeetypecode;
import ins.channel.feetypecode.po.GpfeetypecodeSearch;
import ins.channel.feetypecode.vo.GpfeetypecodeVo;
import ins.platform.common.PageResult;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@Transactional
public class GpfeetypecodeService {
    @Autowired
    private GpfeetypecodeDao gpfeetypecodeDao;

    public int create(GpfeetypecodeSaveRequestVo vo) {
        Gpfeetypecode ggcode = BeanCopyUtils.clone(vo, Gpfeetypecode.class);
        int count = gpfeetypecodeDao.insertSelective(ggcode);
        return count;
    }

    /**
     * 更新数据
     * @param vo
     * @return
     */
    public int update(GpfeetypecodeSaveRequestVo vo) {
        return gpfeetypecodeDao.updateSelectiveByPrimaryKey(BeanCopyUtils.clone(vo, Gpfeetypecode.class));
    }

    /**
     * 查询单条数据
     * @param gid
     * @return
     */
    public GpfeetypecodeVo queryOne(String gid) {
        return BeanCopyUtils.clone(gpfeetypecodeDao.selectByPrimaryKey(gid),GpfeetypecodeVo.class);
    }

    /**
     * 根据主键删除数据
     * @param id
     * @return
     */
    public int delete(String id) {
        return gpfeetypecodeDao.deleteByPrimaryKey(id);
    }

    /**
     * 前端根据实际业务情况来进行分页查询分页查询
     * @param vo
     * @return
     */
    public PageResult<GpfeetypecodeVo> search(GpfeetypecodeSearchRequestVo vo) {
        PageParam pageParam = PageHelper.getPageParam(vo);
        Page<Gpfeetypecode> results = gpfeetypecodeDao.searchPage(pageParam, BeanCopyUtils.clone(vo, GpfeetypecodeSearch.class));
        return PageHelper.convert(pageParam, results, GpfeetypecodeVo.class);
    }
}