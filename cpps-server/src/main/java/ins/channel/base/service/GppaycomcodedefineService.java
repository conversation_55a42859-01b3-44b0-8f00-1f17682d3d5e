package ins.channel.base.service;


import ins.framework.exception.BusinessException;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.base.vo.GppaycomcodeSelectRequestVo;
import ins.channel.base.vo.GppaycomcodeSelectResponseVo;
import ins.channel.base.vo.GppaycomcodedefineSaveRequestVo;
import ins.channel.base.vo.GppaycomcodedefineSearchRequestVo;
import ins.channel.company.dao.GgcompanyDao;
import ins.channel.company.po.Ggcompany;
import ins.channel.paycomcodedefine.dao.GppaycomcodedefineDao;
import ins.channel.paycomcodedefine.po.GgpaycomcodedefineSelect;
import ins.channel.paycomcodedefine.po.Gppaycomcodedefine;
import ins.channel.paycomcodedefine.po.GppaycomcodedefineSearch;
import ins.channel.paycomcodedefine.vo.GppaycomcodedefineVo;
import ins.channel.paycomref.dao.GppaycomrefDao;
import ins.channel.paycomref.po.Gppaycomref;
import ins.channel.power.service.PowerService;
import ins.channel.power.vo.SaauserpaymentcompanyVo;
import ins.platform.common.PageResult;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.PageHelper;
import ins.platform.utils.SessionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
@Slf4j
@Transactional
public class GppaycomcodedefineService {
    @Autowired
    private GppaycomcodedefineDao gppaycomcodedefineDao;
    @Autowired
    private GgcompanyDao ggcompanyDao;
    @Autowired
    private GppaycomrefDao gppaycomrefDao;

    @Autowired
    private PowerService powerService;


    public int create(List<String> companyCodes) {

        List<String> existsComcode = gppaycomcodedefineDao.selectByDeparmentCode(companyCodes);
        if(existsComcode.size() > 0){
            String returnMessage = "";
            for (String comcode : existsComcode) {
                returnMessage += comcode + ",";
            }
            throw new BusinessException("以下机构已转为收付机构，请核对后选择！"+returnMessage.substring(0,returnMessage.length()-1));
        }

        for (String companyCode : companyCodes) {
            Ggcompany ggcompany = ggcompanyDao.selectCompanyInfo(companyCode);
            if(ggcompany != null){
                Gppaycomcodedefine gppaycomcodedefine = new Gppaycomcodedefine();
                gppaycomcodedefine.setPaymentComcode(ggcompany.getCompanyCode());
                gppaycomcodedefine.setDepartmentCode(ggcompany.getCompanyCode());
                gppaycomcodedefine.setOfcenterCname(ggcompany.getCompanyCname());
                gppaycomcodedefine.setOfcenterTname(ggcompany.getCompanyTname());
                gppaycomcodedefine.setOfcenterEname(ggcompany.getCompanyEname());
                gppaycomcodedefine.setValidInd("1");
                gppaycomcodedefine.setCreateCode(SessionHelper.getLoginUser().getUserCode());
                gppaycomcodedefine.setModifiedCode(SessionHelper.getLoginUser().getUserCode());
                gppaycomcodedefineDao.insertSelective(gppaycomcodedefine);
            }
        }
        return 1;
    }

    /**
     * 更新数据
     * @param vo
     * @return
     */
    public int update(GppaycomcodedefineSaveRequestVo vo) {
        int count = 0;
        try{
            Gppaycomcodedefine gppaycomcodedefine = BeanCopyUtils.clone(vo, Gppaycomcodedefine.class);
            gppaycomcodedefine.setPaymentComcode(vo.getDepartmentCode());
            count = gppaycomcodedefineDao.updateSelectiveByPrimaryKey(gppaycomcodedefine);
        }catch (DuplicateKeyException e){
            throw new BusinessException("收付机构重复，不允许录入");
        }catch (Exception e){
            log.error("保存收付机构错误{}",e);
            throw new BusinessException(e.getMessage());
        }
        return count;
    }

    /**
     * 查询单条数据
     * @param gid
     * @return
     */
    public GppaycomcodedefineVo queryOne(String gid) {
        return BeanCopyUtils.clone(gppaycomcodedefineDao.selectByPrimaryKey(gid),GppaycomcodedefineVo.class);
    }

    /**
     * 根据主键删除数据
     * @param paymentCode
     * @return
     */
    public int delete(String paymentCode) {
        List<Gppaycomref> relatedinfo = gppaycomrefDao.selectByPaymentCode(paymentCode);
        if(relatedinfo.size() > 0){
            throw new BusinessException(paymentCode+"下存在出单机构不允许撤销收付机构权限！");
        }
        return gppaycomcodedefineDao.deleteByPrimaryKey(paymentCode);
    }

    /**
     * 前端根据实际业务情况来进行分页查询分页查询
     * @param vo
     * @return
     */
    public PageResult<GppaycomcodedefineVo> search(GppaycomcodedefineSearchRequestVo vo) {
        PageParam pageParam = PageHelper.getPageParam(vo);
        Page<Gppaycomcodedefine> results = gppaycomcodedefineDao.searchPage(pageParam, BeanCopyUtils.clone(vo, GppaycomcodedefineSearch.class));
        return PageHelper.convert(pageParam, results, GppaycomcodedefineVo.class);
    }

    /**
     * 查询下拉框的值
     */
    public PageResult<GppaycomcodeSelectResponseVo> codeTypeForSelectPage(GppaycomcodeSelectRequestVo queryInfo) {
        String userCode = SessionHelper.getLoginUser().getUserCode();
        List<SaauserpaymentcompanyVo> saauserpaymentcompanyVos = powerService.queryPaymentCompanyByUserCode(userCode);
        if(saauserpaymentcompanyVos == null || saauserpaymentcompanyVos.size() <= 0){
            throw new BusinessException("当前用户无对应收付机构！");
        }
        Set<String> comCodes = new HashSet<>();
        for (SaauserpaymentcompanyVo saauserpaymentcompanyVo : saauserpaymentcompanyVos) {
            comCodes.add(saauserpaymentcompanyVo.getPaymentComcode());
        }
        PageParam pageParam = PageHelper.getPageParam(queryInfo);
        Page<GgpaycomcodedefineSelect> results = gppaycomcodedefineDao.paycomcodeForSelectPage(pageParam, queryInfo.getQueryInfo(), comCodes);
        return PageHelper.convert(pageParam, results, GppaycomcodeSelectResponseVo.class);
    }

    /**
     * 查询下拉框的值  不加权限
     */
    public List<GppaycomcodeSelectResponseVo> paycomcodeForSelectNoLimit() {
        List<GgpaycomcodedefineSelect> results = gppaycomcodedefineDao.paycomcodeForSelectNoLimit();
        return BeanCopyUtils.cloneList(results,GppaycomcodeSelectResponseVo.class);
    }

}