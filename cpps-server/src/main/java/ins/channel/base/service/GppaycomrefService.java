package ins.channel.base.service;

import ins.framework.exception.BusinessException;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.base.vo.GppaycomrefRelatedInfoSaveRequestVo;
import ins.channel.base.vo.GppaycomrefRelatedInfoVo;
import ins.channel.base.vo.GppaycomrefSaveRequestVo;
import ins.channel.base.vo.GppaycomrefSearchRequestVo;
import ins.channel.paycomref.dao.GppaycomrefDao;
import ins.channel.paycomref.po.Gppaycomref;
import ins.channel.paycomref.po.GppaycomrefKey;
import ins.channel.paycomref.po.GppaycomrefSearch;
import ins.channel.paycomref.vo.GppaycomrefKeyVo;
import ins.channel.paycomref.vo.GppaycomrefVo;
import ins.platform.common.PageResult;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
@Transactional
public class GppaycomrefService{
    @Autowired
    private GppaycomrefDao gppaycomrefDao;

    /**
     * 更新数据
     * @param vo
     * @return
     */
    public int update(GppaycomrefSaveRequestVo vo) {
        return gppaycomrefDao.updateSelectiveByPrimaryKey(BeanCopyUtils.clone(vo, Gppaycomref.class));
    }


    /**
     * 前端根据实际业务情况来进行分页查询分页查询
     * @param vo
     * @return
     */
    public PageResult<GppaycomrefVo> search(GppaycomrefSearchRequestVo vo) {
        PageParam pageParam = PageHelper.getPageParam(vo);
        Page<Gppaycomref> results = gppaycomrefDao.searchPage(pageParam, BeanCopyUtils.clone(vo, GppaycomrefSearch.class));
        return PageHelper.convert(pageParam, results, GppaycomrefVo.class);
    }

    /**
     * 根据根据一个机构代码查询存在的关联关系
     */
    public List<GppaycomrefVo> queryRelatedInfo(String departmentCode){
        List<Gppaycomref> gppaycomrefs = gppaycomrefDao.selectByCompanyCode(departmentCode);
        return BeanCopyUtils.cloneList(gppaycomrefs,GppaycomrefVo.class);
    }

    /**
     * 根据所传递信息，先删除，后插入！
     */
    public void saveRelatedInfo(GppaycomrefRelatedInfoSaveRequestVo gppaycomrefRelatedInfoSaveRequestVo){
        List<String> companyList = gppaycomrefRelatedInfoSaveRequestVo.getCompanyList();
        if(companyList == null || companyList.size() == 0){
            throw new BusinessException("出单机构不允许为空，请核对后重新录入！");
        }
        gppaycomrefDao.deleteByDepartMentCodes(companyList);

        // 插入：
        List<GppaycomrefRelatedInfoVo> list = gppaycomrefRelatedInfoSaveRequestVo.getRelatedData();
        for (GppaycomrefRelatedInfoVo gppaycomrefRelatedInfoVo : list) {
            for (String compayCode : companyList) {
                Gppaycomref gppaycomref = new Gppaycomref();
                gppaycomref.setUserType(gppaycomrefRelatedInfoVo.getUserTypeCode());
                gppaycomref.setPaymentComcode(gppaycomrefRelatedInfoVo.getPaymentComcode());
                gppaycomref.setDepartmentCode(compayCode);
                gppaycomrefDao.insert(gppaycomref);
            }
        }
    }

    /**
     * 根据主键查询
     * @param gppaycomrefKeyVo
     * @return
     */
    public GppaycomrefVo queryOne(GppaycomrefKeyVo gppaycomrefKeyVo){
        Gppaycomref gppaycomref = gppaycomrefDao.selectByPrimaryKey(BeanCopyUtils.clone(gppaycomrefKeyVo,GppaycomrefKey.class));
        return BeanCopyUtils.clone(gppaycomref, GppaycomrefVo.class);
    }
}