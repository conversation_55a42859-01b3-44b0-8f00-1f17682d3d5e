package ins.channel.base.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

import ins.framework.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DateUtils {

    public static final String PATTERN_DATE = "yyyy-MM-dd";
    public static final String PATTERN_DATE_COMPACT = "yyyyMMdd";
    public static final String PATTERN_DATE_TIME = "yyyy-MM-dd HH:mm:ss";
    public static final String PATTERN_DATE_TIME_COMPACT = "yyyyMMddHHmmss";
    // ADD BY LIYUNZHOU BEGIN
    // 京东代付返回的是cst格式日期
    private static final String PATTERN_DATE_CST_UK = "EEE MMM dd HH:mm:ss Z yyyy";
    // 防止返回中文类型的格式
    private static final String PATTERN_DATE_CST_CN = "yyyy'年' MM'月' dd'日' EEE HH:mm:ss Z";
    public static final String PATTERN_DATE_UTC= "yyyy-MM-dd'T'HH:mm:ss'Z'";
    // ADD BY LIYUNZHOU END

    public static Date parseDate(String str) {
        return parse(str, PATTERN_DATE);
    }

    public static Date parseDateCompact(String str) {
        return parse(str, PATTERN_DATE_COMPACT);
    }

    public static Date parseDatetime(String str) {
        return parse(str, PATTERN_DATE_TIME);
    }

    public static Date parseDatetimeCompact(String str) {
        return parse(str, PATTERN_DATE_TIME_COMPACT);
    }

    public static Date parse(String str, String pattern) {
        Date result;
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            result = sdf.parse(str);
        }catch (Exception e){
            log.error("日期格式解析错误：{}",e);
            throw new BusinessException("日期格式解析错误！");
        }
        return result;
    }

    public static String formatDate(Date date) {
        return format(date, PATTERN_DATE);
    }

    public static String formatDateCompact(Date date) {
        return format(date, PATTERN_DATE_COMPACT);
    }

    public static String formatDatetime(Date date) {
        return format(date, PATTERN_DATE_TIME);
    }

    public static String formatDatetimeCompact(Date date) {
        return format(date, PATTERN_DATE_TIME_COMPACT);
    }

    public static String format(Date date, String pattern) {
        String result;
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            result = sdf.format(date);
        }catch (Exception e){
            log.error("日期格式解析错误：{}",e);
            throw new BusinessException("日期格式解析错误！");
        }
        return result;
    }

    /**
     * 获取第一天日期
     * 1:获取当月第一天的日期
     * 2:获取当年第一天的数据
     * 其他：返回当前时间
     */
    public static Date getFirstDate(int type){
        Date result = new Date();
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH)+1;

        switch (type){
            case 1:
                result = parseDate(year+"-"+formatNumber(month,2)+"-01");
                break;
            case 2:
                result = parseDate(year+"-01-01");
                break;
            default:
                new Date();
        }
        return result;
    }

    /**
     * 将数字前面补0，
     * @param number
     * @param len 想要得到结果长度
     * @return
     */
    private static String formatNumber(int number, int len){
        String result = number+"";
        if(len <= result.length() ){
            return result;
        }else {
            for (int i = 0; i < len - result.length(); i++) {
                result = "0" + result;
            }
        }
        return result;
    }
    
    /**
     * Add by LiyunZhou
     * 京东代付返回的是cst格式的日期格式需要转换类型
     * 将CST格式的日期转换为标准的Date
     * @param cstDateStr
     * @return
     */
	public static Date parseCSTDate(String cstDateStr) {
		try {
			SimpleDateFormat sdf_uk = new SimpleDateFormat(PATTERN_DATE_CST_UK, Locale.UK);
			return sdf_uk.parse(cstDateStr);
		} catch (ParseException e) {
			SimpleDateFormat sdf_cn = new SimpleDateFormat(PATTERN_DATE_CST_CN, Locale.CHINA);
			try {
				return sdf_cn.parse(cstDateStr);
			} catch (ParseException e1) {
				System.out.println("CST日期格式转换出错，原因：" + e.getMessage());
				return null;
			}
		}
	}
	
	/**
	 * 获得周一到周日的数字
	 * @param date
	 * @return
	 */
	public static int getWeekDay(Date date) {
		// 周一至周五的校验
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		int weekday = calendar.get(Calendar.DAY_OF_WEEK);
		return weekday;
	}
	
	/**
	 * 判断当前时间是否是给定的时间范围内
	 * 
	 * @param nowTime
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public static boolean rangesCalendar(Date nowTime, Date beginTime, Date endTime) {
		Calendar date = Calendar.getInstance();
		date.setTime(nowTime);

		Calendar begin = Calendar.getInstance();
		begin.setTime(beginTime);

		Calendar end = Calendar.getInstance();
		end.setTime(endTime);

		if (date.after(begin) && date.before(end)) {
			return true;
		} else {
			return false;
		}
	}
	
	/**
	 * 是否属于工作日的9-17点
	 * @param date
	 * @return
	 */
	public static boolean isWorkDay(Date date) {
		int weekDay = getWeekDay(date);
		if (weekDay == 1 || weekDay == 7) {
			return false;
		}
		// 09:00-17:00的校验
		String timeFormat = "HH:mm";
		SimpleDateFormat dateFormat = new SimpleDateFormat(timeFormat);// 设置日期格式
		Date now = null; // 当前时间
		Date beginTime = null;// 开始时间
		Date endTime = null;// 终止时间
		try {
			now = dateFormat.parse(dateFormat.format(date));
			beginTime = dateFormat.parse("09:00");
			endTime = dateFormat.parse("17:00");
		} catch (Exception e) {
			e.printStackTrace();
		}
		if(!rangesCalendar(now, beginTime, endTime)) {
			return false;
		}
		return true;
	}

    /**
     * 校验开始日期和结束日期,有一个则必须都有
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param msg 日期字段提示中文
     * @Return 空字符串为通过,错误信息
     * <AUTHOR>
     * @date 2021年02月08日 16:06:44
     */
    public static String checkDateStartEnd(Date startDate, Date endDate, String msg){
        if(startDate!=null){
            if(endDate!=null){
                if(startDate.compareTo(endDate)>0){
                    return String.format("%s的开始日期不能早于结束日期");
                }
                return "";
            }else{
                return String.format("请选择%s的结束日期",msg);
            }
        }else{
            if(endDate!=null){
                return String.format("请选择%s的开始日期",msg);
            }else{
                return "";
            }
        }
    }

    public static void main(String[] args) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Date endDate = null; //这里时间可以自己定
        try {
            endDate = format.parse("2012-05-12 15:16:00");
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Date startDate = null; //这里时间可以自己定
        try {
            startDate = format.parse("2011-05-12 15:16:00");
        } catch (ParseException e) {
            e.printStackTrace();
        }
        System.out.println(startDate.compareTo(endDate)); //判断,如果时间在这时间后,就执行后面操作
    }
}
