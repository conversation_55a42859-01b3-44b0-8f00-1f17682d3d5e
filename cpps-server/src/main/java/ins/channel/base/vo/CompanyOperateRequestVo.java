package ins.channel.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 关联机构新增修改入参对象.对应实体描述：机构信息表
 *
 */
@Data
@ApiModel("关联机构新增修改入参对象")
public class CompanyOperateRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：COMPANY_CODE,备注：机构代码 */
	@ApiModelProperty("机构代码")
	private String companyCode;
	/** 对应字段：COMPANY_CNAME,备注：机构名称 */
	@ApiModelProperty("机构名称")
	private String companyCname;
	/** 对应字段：UPPER_COMPANY_CODE,备注：上级机构代码 */
	@ApiModelProperty("上级机构代码")
	private String upperCompanyCode;
	/** 对应字段：ADDRESS_CNAME,备注：地址  */
	@ApiModelProperty("地址")
	private String addressCname;
	/** 对应字段：INSURER_CNAME,备注：联系人名称 */
	@ApiModelProperty("联系人名称")
	private String insurerCname;
	/** 对应字段：PHONE_NUMBER,备注：电话号码 */
	@ApiModelProperty("电话号码")
	private String phoneNumber;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：EMAIL,备注：邮箱 */
	@ApiModelProperty("邮箱")
	private String email;
	/** 对应字段：VALID_IND,备注：有效标示 0-无效 1-有效*/
	@ApiModelProperty("有效标示 0-无效 1-有效")
	private String validInd;
}
