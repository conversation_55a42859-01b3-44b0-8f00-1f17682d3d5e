package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 根据收付机构查询银行账号信息返回信息
 */
@Data
@ApiModel("根据收付机构查询银行账户信息")
public class GfbankInfoRequestVo extends PageBaseDto implements Serializable {

    /** 对应字段：OFCENTER_CODE,备注：利润中心 */
    @ApiModelProperty("利润中心")
    private String ofcenterCode;
    /** 对应字段：OFCENTER_CODE,备注：利润中心 */
    @ApiModelProperty("账户性质")
    private String remark;
    /** 对应字段：OFCENTER_CODE,备注：利润中心 */
    @ApiModelProperty("币别")
    private String currency;

}
