package ins.channel.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 根据收付机构查询银行账号信息返回信息
 */
@Data
@ApiModel("根据收付机构查询账户信息")
public class GfbankInfoResponseVo implements Serializable {
    /** 对应字段：BANK_CODE,备注：银行代码 */
    @ApiModelProperty("银行代码")
    private String bankCode;
    /** 对应字段：BANK_ACCOUNT_CODE,备注：银行账户 */
    @ApiModelProperty("银行账户")
    private String bankAccountCode;
    /** 对应字段：ACCOUNT_CNAME,备注：银行账户名称（中文） */
    @ApiModelProperty("银行账户名称（中文）")
    private String accountCname;
    /** 对应字段：ACCOUNT_TNAME,备注：银行账户名称（繁体） */
    @ApiModelProperty("银行账户名称（繁体）")
    private String accountTname;
    /** 对应字段：ACCOUNT_ENAME,备注：银行账户名称（英文） */
    @ApiModelProperty("银行账户名称（英文）")
    private String accountEname;
    /** 对应字段：CURRENCY,备注：币别 */
    @ApiModelProperty("币别")
    private String currency;
    /** 对应字段：REMARK,备注：账户性质 0-收入 1-支出 */
    @ApiModelProperty("账户性质 0-收入 1-支出")
    private String accountRemark;
    /** 对应字段：OFCENTER_CODE,备注：利润中心 */
    @ApiModelProperty("利润中心")
    private String ofcenterCode;
    /** 对应字段：OFBANK_ACCOUNT_CODE,备注：银行虚拟代码 */
    @ApiModelProperty("银行虚拟代码")
    private String ofbankAccountCode;
    /** 对应字段：ACCOUNTNO,备注：科目代码 */
    @ApiModelProperty("科目代码")
    private String accountNo;

    /** 对应字段：BANK_CNAME,备注：银行名称（中文） */
    @ApiModelProperty("银行名称（中文）")
    private String bankCname;
    /** 对应字段：BANK_TNAME,备注：银行名称（繁体） */
    @ApiModelProperty("银行名称（繁体）")
    private String bankTname;
    /** 对应字段：BANK_ENAME,备注：银行名称（英文） */
    @ApiModelProperty("银行名称（英文）")
    private String bankEname;
    /** 对应字段：REMARK,备注：备注 */
    @ApiModelProperty("银行备注信息")
    private String bankRemark;

}
