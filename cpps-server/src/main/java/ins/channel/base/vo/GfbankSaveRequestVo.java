package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * GfbankVo对象.对应实体描述：银行信息表
 *
 */
@Data
@ApiModel("GfbankVo对象")
public class GfbankSaveRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：GID */
	@ApiModelProperty()
	private String gid;
	/** 对应字段：BANK_CODE,备注：银行代码 */
	@ApiModelProperty("银行代码")
	@Size(min = 1,max = 3,message = "银行代码 不符合数据校验，最大长度为3")
	@NotBlank(message = "银行代码 不能为空")
	private String bankCode;
	/** 对应字段：BANK_CNAME,备注：银行名称（中文） */
	@ApiModelProperty("银行名称（中文）")
	@Size(min = 1,max = 13,message = "银行名称（中文） 不符合数据校验，最大长度为13")
	@NotBlank(message = "银行名称（中文） 不能为空")
	private String bankCname;
	/** 对应字段：BANK_TNAME,备注：银行名称（繁体） */
	@ApiModelProperty("银行名称（繁体）")
	@Size(min = 0,max = 13,message = "银行名称（繁体） 不符合数据校验，最大长度为13")
	private String bankTname;
	/** 对应字段：BANK_ENAME,备注：银行名称（英文） */
	@ApiModelProperty("银行名称（英文）")
	@Size(min = 0,max = 40,message = "银行名称（英文） 不符合数据校验，最大长度为40")
	private String bankEname;
	/** 对应字段：FLAG,备注：预留标志 */
	@ApiModelProperty("预留标志")
	private String flag;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	@Size(min = 0,max = 255,message = "备注 不符合数据校验，最大长度为200")
	private String remark;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date modifiedTime;
}
