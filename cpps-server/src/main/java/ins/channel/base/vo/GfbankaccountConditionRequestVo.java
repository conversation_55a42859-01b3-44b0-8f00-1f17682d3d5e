package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * GfbankaccountVo对象.对应实体描述：银行账户信息表
 *
 */
@Data
@ApiModel("根据一下字段进行全等查询")
public class GfbankaccountConditionRequestVo implements Serializable {


	/** 对应字段：OFCENTER_CODE,备注：利润中心 */
	@ApiModelProperty("利润中心")
	private String ofcenterCode;
	/** 对应字段：CURRENCY,备注：币别 */
	@ApiModelProperty("币别")
	private String currency;
	@ApiModelProperty("账户性质 0-收入 1-支出")
	//@Pattern(regexp = "[0|1]{0,1}",message = "账户性质不符合数据校验 0-收入 1-支出")
	private String remark;
	@ApiModelProperty("银行代码")
	private String bankCode;
}
