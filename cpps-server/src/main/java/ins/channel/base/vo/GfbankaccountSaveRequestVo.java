package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * GfbankaccountVo对象.对应实体描述：银行账户信息表
 *
 */
@Data
@ApiModel("GfbankaccountSaveRequestVo对象")
public class GfbankaccountSaveRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：GID */
	@ApiModelProperty()
	private String gid;
	/** 对应字段：BANK_CODE,备注：银行代码 */
	@ApiModelProperty("银行代码")
	@Size(min = 1,max = 3,message = "银行代码 不符合数据校验，最大长度为3")
	@NotBlank(message = "银行名称 不能为空")
	private String bankCode;
	/** 对应字段：BANK_ACCOUNT_CODE,备注：银行账户 */
	@Size(min = 1,max = 30,message = "银行账户 不符合数据校验，最大长度为30")
	@NotBlank(message = "银行账户 不能为空")
	@ApiModelProperty("银行账户")
	private String bankAccountCode;
	/** 对应字段：ACCOUNT_CNAME,备注：银行账户名称（中文） */
	@ApiModelProperty("银行账户名称（中文）")
	@Size(min = 1,max = 26,message = "银行账户名称（中文） 不符合数据校验，最大长度为26")
	@NotBlank(message = "银行账户名称（中文） 不能为空")
	private String accountCname;
	/** 对应字段：ACCOUNT_TNAME,备注：银行账户名称（繁体） */
	@ApiModelProperty("银行账户名称（繁体）")
	@Size(min = 0,max = 26,message = "银行账户名称（繁体） 不符合数据校验，最大长度为26")
	private String accountTname;
	/** 对应字段：ACCOUNT_ENAME,备注：银行账户名称（英文） */
	@ApiModelProperty("银行账户名称（英文）")
	@Size(min = 0,max = 80,message = "银行账户名称（英文） 不符合数据校验，最大长度为80")
	private String accountEname;
	/** 对应字段：CURRENCY,备注：币别 */
	@ApiModelProperty("币别")
	@Size(min = 1,max = 3,message = "币别 不符合数据校验，最大长度为3")
	@NotBlank(message = "币别 不能为空")
	private String currency;
	/** 对应字段：FLAG,备注：预留标志 */
	@ApiModelProperty("预留标志")
	private String flag;
	/** 对应字段：REMARK,备注：账户性质 0-收入 1-支出 */
	@ApiModelProperty("账户性质 0-收入 1-支出")
	private List<String> remark;
	/** 对应字段：OFCENTER_CODE,备注：利润中心 */
	@ApiModelProperty("利润中心")
	@Size(min = 0,max = 10,message = "利润中心 不符合数据校验，最大长度为10")
	private String ofcenterCode;
	/** 对应字段：OFBANK_ACCOUNT_CODE,备注：银行虚拟代码 */
	@ApiModelProperty("银行虚拟代码")
	@Size(min = 0,max = 15,message = "银行虚拟代码 不符合数据校验，最大长度为15")
	private String ofbankAccountCode;
	/** 对应字段：ACCOUNTNO,备注：科目代码 */
	@ApiModelProperty("科目代码")
	@Size(min = 0,max = 10,message = "科目代码 不符合数据校验，最大长度为10")
	private String accountNo;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date modifiedTime;
}
