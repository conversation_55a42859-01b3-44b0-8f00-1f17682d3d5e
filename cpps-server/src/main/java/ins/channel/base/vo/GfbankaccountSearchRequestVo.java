package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * GfbankaccountVo对象.对应实体描述：银行账户信息表
 */
@Data
@ApiModel("GfbankaccountVo对象")
public class GfbankaccountSearchRequestVo extends PageBaseDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 对应字段：BANK_CODE,备注：银行代码
     */
    @ApiModelProperty("银行代码")
    private String bankCode;

    /**
     * 对应字段：BANK_ACCOUNT_CODE,备注：银行账户
     */
    @ApiModelProperty("银行账户")
    private String bankAccountCode;

    /**
     * 对应字段：ACCOUNT_CNAME,备注：银行账户名称（中文）
     */
    @ApiModelProperty("银行账户名称（中文）")
    private String accountCname;

    /**
     * 对应字段：REMARK,备注：账户性质 0-收入 1-支出
     */
    @ApiModelProperty("账户性质 0-收入 1-支出")
    private String remark;

    /**
     * 对应字段：OFCENTER_CODE,备注：利润中心
     */
    @ApiModelProperty("利润中心")
    private String ofcenterCode;

    @ApiModelProperty("查询创建日期 开始时间")
    @Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))",
            message = "查询起始日期不符合规定格式 yyyy-MM-dd")
    private String startTime;

    @Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))",
            message = "查询起始日期不符合规定格式 yyyy-MM-dd")
    @ApiModelProperty("查询创建日期 结束时间")
    private String endTime;
}
