package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@Data
@ApiModel("下拉框查询请求VO")
public class GfbankaccountSelectRequestVo extends PageBaseDto {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("查询值")
    private String queryInfo;

}
