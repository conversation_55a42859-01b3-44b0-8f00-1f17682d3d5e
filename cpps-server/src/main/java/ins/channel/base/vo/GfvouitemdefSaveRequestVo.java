package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * GfvouitemdefVo对象.对应实体描述：凭证分录项名称翻译表
 *
 */
@Data
@ApiModel("GfvouitemdefVo对象")
public class GfvouitemdefSaveRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：GID */
	@ApiModelProperty()
	private String gid;
	/** 对应字段：VOU_TYPE,备注：凭证类型 */
	@ApiModelProperty("凭证类型")
	@Size(min = 1,max = 2,message = "凭证类型 不符合数据校验，最大长度为2")
	@NotBlank(message = "凭证类型 不能为空")
	private String vouType;
	/** 对应字段：VOU_ITEM,备注：分录项代码 */
	@ApiModelProperty("分录项代码")
	@Size(min = 1,max = 4,message = "分录项代码 不符合数据校验，最大长度为4")
	@NotBlank(message = "分录项代码 不能为空")
	private String vouItem;
	/** 对应字段：VOU_ITEM_CNAME,备注：分录项简体中文名 */
	@ApiModelProperty("分录项简体中文名")
	@Size(min = 0,max = 33,message = "分录项简体中文名 不符合数据校验，最大长度为33")
	private String vouItemCname;
	/** 对应字段：VOU_ITEM_TNAME,备注：分录项繁体中文名 */
	@ApiModelProperty("分录项繁体中文名")
	@Size(min = 0,max = 33,message = "分录项繁体中文名 不符合数据校验，最大长度为33")
	private String vouItemTname;
	/** 对应字段：VOU_ITEM_ENAME,备注：分录项英文名 */
	@ApiModelProperty("分录项英文名")
	@Size(min = 0,max = 100,message = "分录项英文名 不符合数据校验，最大长度为100")
	private String vouItemEname;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date modifiedTime;
}
