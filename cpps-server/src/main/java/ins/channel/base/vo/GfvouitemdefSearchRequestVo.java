package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * GfvouitemdefVo对象.对应实体描述：凭证分录项名称翻译表
 *
 */
@Data
@ApiModel("GfvouitemdefVo对象")
public class GfvouitemdefSearchRequestVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：VOU_TYPE,备注：凭证类型 */
	@ApiModelProperty("凭证类型")
	private String vouType;
	/** 对应字段：VOU_ITEM,备注：分录项代码 */
	@ApiModelProperty("分录项代码")
	private String vouItem;
	/** 对应字段：VOU_ITEM_CNAME,备注：分录项简体中文名 */
	@ApiModelProperty("分录项简体中文名")
	private String vouItemCname;

	@ApiModelProperty("查询创建日期 开始时间")
	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))" ,
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	private String startTime;

	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))" ,
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	@ApiModelProperty("查询创建日期 结束时间")
	private String endTime;

}
