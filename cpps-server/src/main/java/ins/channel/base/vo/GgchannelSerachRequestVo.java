package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("GgchannelSerachRequestVo对象")
public class GgchannelSerachRequestVo extends PageBaseDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 对应字段：CHANNEL_CODE,备注：渠道大类代码 pk */
    @ApiModelProperty("渠道大类代码 pk")
    private String channelCode;
    /** 对应字段：CHANNEL_CNAME,备注：渠道大类简体名称 */
    @ApiModelProperty("渠道大类简体名称")
    private String channelCname;
    /** 对应字段：CHANNEL_TNAME,备注：渠道大类繁体名称 */
    @ApiModelProperty("渠道大类繁体名称")
    private String channelTname;
    /** 对应字段：CHANNEL_ENAME,备注：渠道大类英文名称 */
    @ApiModelProperty("渠道大类英文名称")
    private String channelEname;

}
