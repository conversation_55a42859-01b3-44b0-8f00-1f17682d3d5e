package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * GgcodeVo对象.对应实体描述：代码配置表
 *
 */
@Data
@ApiModel("查询付款方式VO")
public class GgcodePayWayRequestVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：COMPANY_CODE,备注：机构代码 */
	@ApiModelProperty("机构代码")
	private String companyCode;

	/** ,备注：1 保费 2 出单费 3 手续费 4 赔款 5 再保 6 暂收款 */
	@ApiModelProperty("1 保费 2 出单费 3 手续费 4 赔款 5 再保 6 暂收款")
	private String remark;

	@ApiModelProperty("代码类型")
	private String codeType;

}
