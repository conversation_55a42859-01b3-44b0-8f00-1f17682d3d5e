package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * GgcodeVo对象.对应实体描述：代码配置表
 *
 */
@Data
@ApiModel("GgcodeVo对象")
public class GgcodeSaveRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：GID */
	@ApiModelProperty()
	private String gid;
	/** 对应字段：CODE_TYPE,备注：代码类型 */
	@ApiModelProperty("代码类型")
	@Size(min = 1,max = 50,message = "代码类型 不符合数据校验，最大长度为50")
	@NotBlank(message = "代码类型 不能为空")
	private String codeType;
	/** 对应字段：COMPANY_CODE,备注：机构代码 */
	@ApiModelProperty("机构代码")
	@Size(min = 1,max = 10,message = "机构代码 不符合数据校验，最大长度为10")
	@NotBlank(message = "机构代码 不能为空")
	private String companyCode;
	/** 对应字段：CODE_CODE,备注：业务代码 */
	@ApiModelProperty("业务代码")
	@Size(min = 1,max = 50,message = "业务代码 不符合数据校验，最大长度为50")
	@NotBlank(message = "业务代码 不能为空")
	private String codeCode;
	/** 对应字段：CODE_CNAME,备注：业务代码中文名称 */
	@ApiModelProperty("业务代码中文名称")
	@Size(min = 1,max = 133,message = "业务代码中文名称 不符合数据校验，最大长度为133")
	@NotBlank(message = "业务代码中文名称 不能为空")
	private String codeCname;
	/** 对应字段：CODE_TNAME,备注：业务代码繁体名称 */
	@ApiModelProperty("业务代码繁体名称")
	@Size(min = 0,max = 133,message = "业务代码繁体名称 不符合数据校验，最大长度为133")
	private String codeTname;
	/** 对应字段：CODE_ENAME,备注：业务代码英文名称 */
	@ApiModelProperty("业务代码英文名称")
	@Size(min = 0,max = 400,message = "业务代码英文名称 不符合数据校验，最大长度为400")
	private String codeEname;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	@Size(min = 0,max = 500,message = "备注 不符合数据校验，最大长度为500")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
	/** 对应字段：DISPLAY_NO,备注：显示序号 */
	@ApiModelProperty("显示序号")
	private BigDecimal displayNo;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date modifiedTime;
}
