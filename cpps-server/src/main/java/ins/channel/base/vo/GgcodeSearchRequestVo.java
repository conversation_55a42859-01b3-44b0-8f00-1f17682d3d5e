package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * GgcodeVo对象.对应实体描述：代码配置表
 *
 */
@Data
@ApiModel("GgcodeVo对象")
public class GgcodeSearchRequestVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CODE_TYPE,备注：代码类型 */
	@ApiModelProperty("代码类型")
	private String codeType;
	/** 对应字段：COMPANY_CODE,备注：机构代码 */
	@ApiModelProperty("机构代码")
	private String companyCode;
	/** 对应字段：CODE_CODE,备注：业务代码 */
	@ApiModelProperty("业务代码")
	private String codeCode;

	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
	private String validInd;

}
