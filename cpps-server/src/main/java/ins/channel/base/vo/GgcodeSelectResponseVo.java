package ins.channel.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询下拉框返回VO
 *
 */
@Data
@ApiModel("下拉框模糊查询返回数据")
public class GgcodeSelectResponseVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CODE_CODE,备注：业务代码 */
	@ApiModelProperty("代码值")
	private String codeCode;
	/** 对应字段：CODE_CNAME,备注：业务代码中文名称 */
	@ApiModelProperty("代码名称")
	private String codeCname;
}
