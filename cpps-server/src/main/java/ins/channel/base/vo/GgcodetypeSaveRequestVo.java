package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
@ApiModel("分页查询Vo")
public class GgcodetypeSaveRequestVo {
    private static final long serialVersionUID = 1L;
    /** 对应字段：GID */
    @ApiModelProperty()
    private String gid;
    /** 对应字段：CODE_TYPE,备注：代码类型 */
    @ApiModelProperty("代码类型")
    @Size(min = 1,max = 50,message = "代码类型 不符合数据校验，最大长度为50")
    @NotBlank(message = "代码类型 不能为空")
    private String codeType;

    /** 对应字段：CODE_TYPE_CDESC,备注：代码类型中文描述 */
    @ApiModelProperty("代码类型中文描述")
    @Size(max = 133,message = "代码类型中文描述 不符合数据校验，最大长度为133")
    private String codeTypeCdesc;

    /** 对应字段：CODE_TYPE_TDESC,备注：代码类型繁体中文描述 */
    @ApiModelProperty("代码类型繁体中文描述")
    @Size(max = 133,message = "代码类型繁体中文描述 不符合数据校验，最大长度为133")
    private String codeTypeTdesc;

    /** 对应字段：CODE_TYPE_EDESC,备注：代码类型英文描述 */
    @ApiModelProperty("代码类型英文描述")
    @Size(max = 400,message = "代码类型英文描述 不符合数据校验，最大长度为400")
    private String codeTypeEdesc;

    /** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
    @ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
    @Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
    private String validInd;

    /** 对应字段：REMARK,备注：备注 */
    @ApiModelProperty("备注")
    @Size(max = 500,message = "备注 不符合数据校验，最大长度为500")
    private String remark;

}
