package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;

@Data
@ApiModel("分页查询Vo")
public class GgcodetypeSearchRequestVo extends PageBaseDto {
    private static final long serialVersionUID = 1L;
    /** 对应字段：CODE_TYPE,备注：代码类型 */
    @ApiModelProperty("代码类型")
    private String codeType;

    /** 对应字段：CODE_TYPE_CDESC,备注：代码类型中文描述 */
    @ApiModelProperty("代码类型中文描述")
    private String codeTypeCdesc;

    @ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
    @Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
    private String validInd;

}
