package ins.channel.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
@ApiModel("查询代码类型值请求VO")
public class GgcodetypeSelectResponseVo {
    private static final long serialVersionUID = 1L;

    /** 对应字段：CODE_TYPE,备注：代码类型 */
    @ApiModelProperty("代码类型")
    private String codeType;

    @ApiModelProperty("代码类型中文描述")
    private String codeTypeCdesc;
}
