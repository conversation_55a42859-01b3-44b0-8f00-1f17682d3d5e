package ins.channel.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询下拉框返回VO
 */
@Data
@ApiModel("下拉框模糊查询返回数据")
public class GgcoinsSelectResponseVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：COINS_CODE,备注：共保人代码 pk */
	@ApiModelProperty("共保人代码 pk")
	private String coinsCode;
	/** 对应字段：COINS_FULL_NAME,备注：共保人全称 */
	@ApiModelProperty("共保人全称")
	private String coinsFullName;
}
