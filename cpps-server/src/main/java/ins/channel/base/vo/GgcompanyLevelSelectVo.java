package ins.channel.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("查询上级，下级机构信息Vo")
public class GgcompanyLevelSelectVo {
    /** 对应字段：COMPANY_CODE,备注：机构代码 */
    @ApiModelProperty("机构代码")
    private String companyCode;
    /** 对应字段：COMPANY_CNAME,备注：机构中文描述 */
    @ApiModelProperty("机构中文描述")
    private String companyCname;
    /** 对应字段：COMPANY_TNAME,备注：机构繁体描述 */
    @ApiModelProperty("机构繁体描述")
    private String companyTname;
    /** 对应字段：COMPANY_ENAME,备注：机构英文描述 */
    @ApiModelProperty("机构英文描述")
    private String companyEname;

    /** 对应字段：UPPER_COMPANY_CODE,备注：上级机构代码 */
    @ApiModelProperty("上级机构代码")
    private String upperCompanyCode;

    /** 对应字段：COM_TYPE,备注：机构类型 */
    @ApiModelProperty("机构类型")
    private String comType;
    /** 对应字段：CENTER_IND,备注：核算单位，利润中心 */
    @ApiModelProperty("核算单位，利润中心")
    private String centerInd;
    /** 对应字段：COM_LEVEL,备注：机构级别 */
    @ApiModelProperty("机构级别")
    private String comLevel;
}
