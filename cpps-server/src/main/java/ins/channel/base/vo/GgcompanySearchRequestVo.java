package ins.channel.base.vo;

import ins.framework.mybatis.annotations.Column;
import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * GgcompanySearchRequestVo 对象.对应实体描述：关联机构分页查询入参对象
 *
 */
@Data
@ApiModel("GgcompanySearchRequestVo对象")
public class GgcompanySearchRequestVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CODE_TYPE,备注：代码类型 */
	@ApiModelProperty("关联机构代码")
	private String companyCode;
	/** 对应字段：COMPANY_CODE,备注：机构代码 */
	@ApiModelProperty("关联机构中文名称")
	private String companyCname;
//    /** 对应字段：CODE_TYPE,备注：代码类型 */
//    @ApiModelProperty("收付机构代码")
//    private String paymentCode;
	/** 对应字段：CODE_CODE,备注：业务代码 */
	@ApiModelProperty("上级机构代码")
	private String upperCompanyCode;
//	/** 对应字段：TAX_NUMBER,备注：税务号 */
//	@ApiModelProperty("税务号")
//	private String taxNumber;

	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
	private String validInd;
//	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
//	@ApiModelProperty("是否授予收付机构权限 --** 0 已赋予 --** 1 未赋予")
//	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
//	private String grantFlag;

}
