package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@Data
@ApiModel("下拉框查询请求VO")
public class GgcompanySelectRequestVo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("查询值")
    private String queryInfo;

    @ApiModelProperty("关联机构查询")
    private String companyCode;

    @ApiModelProperty("人员拥有关联机构权限集合")
    private Set<String> companyCodes;
}
