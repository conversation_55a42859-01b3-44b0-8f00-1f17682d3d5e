package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("机构查询Vo")
public class GgcompanySerachRequestVo extends PageBaseDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 对应字段：COMPANY_CODE,备注：机构代码 */
    @ApiModelProperty("机构代码")
    private String companyCode;
    /** 对应字段：COMPANY_CNAME,备注：机构中文描述 */
    @ApiModelProperty("机构中文描述")
    private String companyCname;
    /** 对应字段：COMPANY_TNAME,备注：机构繁体描述 */
    @ApiModelProperty("机构繁体描述")
    private String companyTname;
    /** 对应字段：COMPANY_ENAME,备注：机构英文描述 */
    @ApiModelProperty("机构英文描述")
    private String companyEname;

}
