package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * GgcurrencyVo对象.对应实体描述：GGCurrency-币别代码表
 *
 */
@Data
@ApiModel("GgcurrencyVo对象")
public class GgcurrencySaveRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：GID */
	@ApiModelProperty()
	private String gid;
	/** 对应字段：CURRENCY_CODE,备注：币别代码 */
	@ApiModelProperty("币别代码")
	@Size(min = 1,max = 3,message = "币别代码不符合数据校验，最大长度为3")
	@NotBlank(message = "币别代码 不能为空")
	private String currencyCode;
	/** 对应字段：CURRENCY_CNAME,备注：币别简体中文名称 */
	@ApiModelProperty("币别简体中文名称")
	@Size(min = 0,max = 133,message = "币别简体中文名称不符合数据校验，最大长度为133")
	private String currencyCname;
	/** 对应字段：CURRENCY_TNAME,备注：币别繁体中文名称 */
	@ApiModelProperty("币别繁体中文名称")
	@Size(min = 0,max = 133,message = "币别繁体中文名称 不符合数据校验，最大长度为133")
	private String currencyTname;
	/** 对应字段：CURRENCY_ENAME,备注：币别英文名称 */
	@ApiModelProperty("币别英文名称")
	@Size(min = 0,max = 400,message = "币别英文名称 不符合数据校验，最大长度为400")
	private String currencyEname;
	/** 对应字段：CREATOR_CODE,备注：创建人 */
	@ApiModelProperty("创建人")
	private String creatorCode;
	/** 对应字段：UPDATER_CODE,备注：最后修改人 */
	@ApiModelProperty("最后修改人")
	private String updaterCode;
	/** 对应字段：VALID_DATE,备注：生效日期 */
	@ApiModelProperty("生效日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date validDate;
	/** 对应字段：INVALID_DATE,备注：失效日期 */
	@ApiModelProperty("失效日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date invalidDate;
	/** 对应字段：VALID_IND,备注：有效标志 */
	@ApiModelProperty("有效标志")
	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	@Size(min = 0,max = 4000,message = "备注 不符合数据校验，最大长度为4000")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	private String flag;
}
