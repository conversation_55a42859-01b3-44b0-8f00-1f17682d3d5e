package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * GgcurrencyVo对象.对应实体描述：GGCurrency-币别代码表
 *
 */
@Data
@ApiModel("GgcurrencyVo对象")
public class GgcurrencySearchRequestVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：CURRENCYCODE,备注：币别代码 */
	@ApiModelProperty("币别代码")
	private String currencyCode;

	/** 对应字段：CURRENCYCNAME,备注：币别简体中文名称 */
	@ApiModelProperty("币别简体中文名称")
	private String currencyCname;

	/** 对应字段：VALIDIND,备注：有效标志 */
	@ApiModelProperty("有效标志")
	private String validInd;

	@ApiModelProperty("查询创建日期 开始时间")
	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))" ,
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	private String startTime;

	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))" ,
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	@ApiModelProperty("查询创建日期 结束时间")
	private String endTime;

}
