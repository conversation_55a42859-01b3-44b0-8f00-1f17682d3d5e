package ins.channel.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询下拉框返回VO
 *
 */
@Data
@ApiModel("下拉框模糊查询返回数据")
public class GgcurrencySelectResponseVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：currencyCode,备注：币别代码 */
	@ApiModelProperty("币别代码")
	private String currencyCode;
	/** 对应字段：currencyCname,备注：币别名称 */
	@ApiModelProperty("币别名称")
	private String currencyCname;
}
