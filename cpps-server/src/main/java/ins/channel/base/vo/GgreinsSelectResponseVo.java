package ins.channel.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询下拉框返回VO
 *
 */
@Data
@ApiModel("下拉框模糊查询返回数据")
public class GgreinsSelectResponseVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：REINSCODE,备注：再保人代码 pk */
	@ApiModelProperty("再保人代码 pk")
	private String reinsCode;
	/** 对应字段：LONGNAME,备注：再保人全称 */
	@ApiModelProperty("再保人全称")
	private String longName;
}
