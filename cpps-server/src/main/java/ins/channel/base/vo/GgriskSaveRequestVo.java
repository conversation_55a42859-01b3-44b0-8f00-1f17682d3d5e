package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * GgcodeVo对象.对应实体描述：代码配置表
 *
 */
@Data
@ApiModel("GgcodeVo对象")
public class GgriskSaveRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：GID,备注：pk */
	@ApiModelProperty("pk")
	private String gid;
	/** 对应字段：RISK_CODE,备注：险种代码 */
	@ApiModelProperty("险种代码")
	@Size(min = 1,max = 4,message = "险种代码 不符合数据校验，最大长度为4")
	@NotBlank(message = "险种代码 不能为空")
	private String riskCode;
	/** 对应字段：RISK_CNAME,备注：险种中文名称 */
	@ApiModelProperty("险种中文名称")
	@Size(min = 0,max = 133,message = "险种中文名称 不符合数据校验，最大长度为133")
	private String riskCname;
	/** 对应字段：RISK_TNAME,备注：险种繁体名称 */
	@ApiModelProperty("险种繁体名称")
	@Size(min = 0,max = 133,message = "险种繁体名称 不符合数据校验，最大长度为133")
	private String riskTname;
	/** 对应字段：RISK_ENAME,备注：险种英文名称 */
	@ApiModelProperty("险种英文名称")
	@Size(min = 0,max = 400,message = "险种英文名称 不符合数据校验，最大长度为400")
	private String riskEname;
	/** 对应字段：RISK_CLASS,备注：险类 */
	@ApiModelProperty("险类")
	@Size(min = 0,max = 3,message = "险类 不符合数据校验，最大长度为3")
	private String riskClass;
	/** 对应字段：OPENCOVER_IND,备注：是否预约协议 */
	@ApiModelProperty("是否预约协议")
	@Size(min = 1,max = 1,message = "是否预约协议 不符合数据校验，最大长度为1")
	@NotBlank(message = "是否预约协议 不能为空")
	private String opencoverInd;
	/** 对应字段：VALIDDATE,备注：有效日期 */
	@ApiModelProperty("有效日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date validDate;
	/** 对应字段：INVALIDDATE,备注：失效日期 */
	@ApiModelProperty("失效日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date invalidDate;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	@Size(min = 0,max = 400,message = "备注 不符合数据校验，最大长度为400")
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("标志字段")
	@Size(min = 0,max = 2,message = "标志字段 不符合数据校验，最大长度为2")
	private String flag;
}
