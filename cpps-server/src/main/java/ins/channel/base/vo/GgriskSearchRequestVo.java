package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * GgcodeVo对象.对应实体描述：代码配置表
 *
 */
@Data
@ApiModel("GgcodeVo对象")
public class GgriskSearchRequestVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：RISK_CODE,备注：险种代码 */
	@ApiModelProperty("险种代码")
	private String riskCode;
	/** 对应字段：RISK_CLASS,备注：险类 */
	@ApiModelProperty("险类")
	private String riskClass;

	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
	private String validInd;

	@ApiModelProperty("查询创建日期 开始时间")
	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))" ,
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	private String startTime;

	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))" ,
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	@ApiModelProperty("查询创建日期 结束时间")
	private String endTime;

}
