package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("下拉框查询请求VO")
public class GgriskSelectRequestVo extends PageBaseDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("查询值")
    private String queryInfo;

}
