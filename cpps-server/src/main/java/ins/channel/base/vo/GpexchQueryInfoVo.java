package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * GpexchVo对象.对应实体描述：汇率表
 *
 */
@Data
@ApiModel("查询汇率Vo")
public class GpexchQueryInfoVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：BASE_CURRENCY,备注：基准币别 */
	@ApiModelProperty("基准币别 不允许为空")
	@NotBlank
	private String baseCurrency;

	/** 对应字段：EXCH_CURRENCY,备注：兑换币别 */
	@ApiModelProperty("兑换币别 不允许为空")
	@NotBlank
	private String exchCurrency;

	@ApiModelProperty("兑换汇率 为空：默认当前时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	private Date exchDate;

	@ApiModelProperty("兑换类型 1：日汇率，2：月汇率，3：年汇率 为空默认日汇率")
	private String exchType;

}
