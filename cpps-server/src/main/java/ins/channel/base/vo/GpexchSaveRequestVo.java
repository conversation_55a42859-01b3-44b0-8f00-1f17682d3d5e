package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import ins.platform.utils.excel.annotation.ExcelField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * GpexchVo对象.对应实体描述：汇率表
 *
 */
@Data
@ApiModel("GpexchVo对象")
public class GpexchSaveRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：GID */
	@ApiModelProperty()
	private String gid;
	/** 对应字段：EXCH_DATE,备注：汇率日期 */
	@ApiModelProperty("汇率日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	@ExcelField(title="汇率日期(YYYY-MM-DD)", align=2, sort=10)
	private Date exchDate;
	/** 对应字段：BASE,备注：基准 */
	@ApiModelProperty("基准")
	@ExcelField(title="基准", align=2, sort=20)
	private BigDecimal base;
	/** 对应字段：BASE_CURRENCY,备注：基准币别 */
	@ApiModelProperty("基准币别")
	@Size(min = 1,max = 3,message = "基准币别 不符合数据校验，最大长度为3")
	@NotBlank(message = "基准币别 不能为空")
	@ExcelField(title="基准币别", align=2, sort=30)
	private String baseCurrency;
	/** 对应字段：EXCH_CURRENCY,备注：兑换币别 */
	@ApiModelProperty("兑换币别")
	@Size(min = 1,max = 3,message = "兑换币别 不符合数据校验，最大长度为3")
	@NotBlank(message = "兑换币别 不能为空")
	@ExcelField(title="兑换币别", align=2, sort=40)
	private String exchCurrency;
	/** 对应字段：EXCH_RATE,备注：兑换汇率 */
	@ApiModelProperty("兑换汇率")
	@ExcelField(title="汇率", align=2, sort=50)
	private BigDecimal exchRate;
	/** 对应字段：VALID_DATE,备注：生效日期 */
	/*@ApiModelProperty("生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date validDate;*/
	/** 对应字段：INVALID_DATE,备注：失效日期 */
	/*@ApiModelProperty("失效日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date invalidDate;*/
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
	@ExcelField(title="有效标志 0 无效 1 有效", align=2, sort=60)
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	@Size(min = 0,max = 500,message = "备注 不符合数据校验，最大长度为500")
	@ExcelField(title="备注", align=2, sort=60)
	private String remark;
	/** 对应字段：FLAG,备注：标志字段 */
	@ApiModelProperty("DAC转化标志")
	@Pattern(regexp = "[0|1]{0,1}",message = "DAC转化标志不符合数据校验 0|1")
	@ExcelField(title="DAC转化标志 0-正常汇率 1-DAC转化使用", align=2, sort=60)
	private String flag;
	/** 对应字段：EXCH_TYPE,备注：年/天兑换率标志 */
	@ApiModelProperty("年/天兑换率标志  1-天 2-月 3-年")
	@Size(min = 0,max = 1,message = "年/天兑换率标志 不符合数据校验，最大长度为1")
	@ExcelField(title="年/天兑换率标志  1-天 2-月 3-年", align=2, sort=70)
	private String exchType;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date modifiedTime;
}
