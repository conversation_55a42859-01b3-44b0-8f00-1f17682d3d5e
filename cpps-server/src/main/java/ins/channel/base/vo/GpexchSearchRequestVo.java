package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * GpexchVo对象.对应实体描述：汇率表
 *
 */
@Data
@ApiModel("GpexchVo对象")
public class GpexchSearchRequestVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：BASE_CURRENCY,备注：基准币别 */
	@ApiModelProperty("基准币别")
	private String baseCurrency;

	/** 对应字段：BASE_CURRENCY,备注：兑换币别 */
	@ApiModelProperty("兑换币别")
	private String exchCurrency;

	@ApiModelProperty("查询创建日期 开始时间")
	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))" ,
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	private String exchDate;

	@ApiModelProperty("有效标志")
	private String validInd;

	@ApiModelProperty("DAC转化标志")
	private String flag;

}
