package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * GpfeetypecodeVo对象.对应实体描述：费用类型与计算符号对应表
 *
 */
@Data
@ApiModel("GpfeetypecodeVo对象")
public class GpfeetypecodeSaveRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：FEE_TYPE_CODE,备注：费用类型代码 */
	@ApiModelProperty("费用类型代码")
	@Size(min = 1,max = 10,message = "费用类型代码 不符合数据校验，最大长度为10")
	@NotBlank(message = "费用类型代码 不能为空")
	private String feeTypeCode;
	/** 对应字段：FEE_TYPE_CODE_CNAME,备注：费用类型简体中文名称 */
	@Size(min = 0,max = 333,message = "费用类型简体中文名称 不符合数据校验，最大长度为333")
	@ApiModelProperty("费用类型简体中文名称")
	private String feeTypeCodeCname;
	/** 对应字段：FEE_TYPE_CODE_TNAME,备注：费用类型繁体中文名称 */
	@Size(min = 0,max = 333,message = "费用类型繁体中文名称 不符合数据校验，最大长度为333")
	@ApiModelProperty("费用类型繁体中文名称")
	private String feeTypeCodeTname;
	/** 对应字段：FEE_TYPE_CODE_ENAME,备注：费用类型英文名称 */
	@ApiModelProperty("费用类型英文名称")
	@Size(min = 0,max = 1000,message = "费用类型英文名称 不符合数据校验，最大长度为1000")
	private String feeTypeCodeEname;
	/** 对应字段：CAL_SIGN,备注：计算符号 */
	@ApiModelProperty("计算符号")
	private BigDecimal calSign;
	/** 对应字段：VALIDIND,备注：有效标志 */
	@ApiModelProperty("有效标志")
	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
	private String validind;
	/** 对应字段：FLAG,备注：预留标志 */
	@ApiModelProperty("预留标志")
	private String flag;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	@Size(min = 0,max = 255,message = "备注 不符合数据校验，最大长度为255")
	private String remark;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date modifiedTime;
}
