package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * GpfeetypecodeVo对象.对应实体描述：费用类型与计算符号对应表
 *
 */
@Data
@ApiModel("GpfeetypecodeVo对象")
public class GpfeetypecodeSearchRequestVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：FEE_TYPE_CODE,备注：费用类型代码 */
	@ApiModelProperty("费用类型代码")
	private String feeTypeCode;

	/** 对应字段：FEE_TYPE_CODE_CNAME,备注：费用类型简体中文名称 */
	@ApiModelProperty("费用类型简体中文名称")
	private String feeTypeCodeCname;

	/** 对应字段：VALIDIND,备注：有效标志 */
	@ApiModelProperty("有效标志")
	private String validind;

	@ApiModelProperty("查询创建日期 开始时间")
	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))" ,
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	private String startTime;

	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))" ,
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	@ApiModelProperty("查询创建日期 结束时间")
	private String endTime;

}
