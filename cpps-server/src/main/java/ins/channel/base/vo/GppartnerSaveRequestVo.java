package ins.channel.base.vo;

import ins.framework.mybatis.annotations.Column;
import ins.platform.utils.excel.annotation.ExcelField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("GppartnerSaveRequestVo对象")
public class GppartnerSaveRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("关联交易方ID")
    private String gid;

    /** 对应字段：RELATED_CODE,备注：关联交易方代码 */
    @ApiModelProperty("关联交易方代码")
    @NotBlank(message = "关联交易方代码 不能为空")
    @ExcelField(title="关联交易方代码", align=2, sort=10)
    @Size(min = 1,max = 10,message = "关联交易方代码不符合数据校验，最大长度为10")
    private String relatedCode;

    /** 对应字段：PARTNER_NAME,备注：关联交易方名称 */
    @ApiModelProperty("关联交易方名称")
    @NotBlank(message = "关联交易方名称 不能为空")
    @ExcelField(title="关联交易方名称", align=2, sort=20)
    @Size(min = 1,max = 200,message = "关联交易方名称不符合数据校验，最大长度为200")
    private String partnerName;
    /** 对应字段：OPERATE_CODE,备注：数据维护人员 */
    @ApiModelProperty("数据维护人员代码")
    @Column(name = "OPERATE_CODE", description = "数据维护人员代码")
    private String operateCode;
    /** 对应字段：CITY_NAME,备注：关联交易方所在城市 */
    @ApiModelProperty("关联交易方所在城市")
    @ExcelField(title="关联交易方所在城市", align=2, sort=30)
    @Size(min = 0,max = 200,message = "关联交易方所在城市不符合数据校验，最大长度为200")
    private String cityName;

    /** 对应字段：VALID_IND,备注：有效性 */
    @ApiModelProperty("有效性")
    @NotBlank(message = "有效性 不能为空")
    @Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
    @ExcelField(title="有效标志 0 无效 1 有效", align=2, sort=50)
    private String validInd;

    /** 对应字段：REMARK,备注：备注 */
    @ApiModelProperty("备注")
    @ExcelField(title="备注", align=2, sort=60)
    @Size(min = 0,max = 200,message = "备注 不符合数据校验，最大长度为200")
    private String remark;
}