package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import lombok.Data;

import java.io.Serializable;

@Data
public class GppartnerSearchRequestVo extends PageBaseDto implements Serializable {
    private static final long serialVersionUID = 1L;

     /** 对应字段：PARTNER_NAME,备注：关联交易方名称 */
     private String partnerName;
    /** 对应字段：RELATED_CODE,备注：关联交易方代码 */
    private String relatedCode;
    /** 对应字段：RELATED_CODE,备注：有效性 */
    private String validInd;
}
