package ins.channel.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询下拉框返回VO
 *
 */
@Data
@ApiModel("下拉框模糊查询返回数据")
public class GppaycomcodeSelectResponseVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：paymentComcode,备注：收付机构代码 */
	@ApiModelProperty("收付机构代码")
	private String paymentComcode;
	/** 对应字段：ofcenterCName,备注：收付机构名称 */
	@ApiModelProperty("收付机构名称")
	private String ofcenterCname;
}
