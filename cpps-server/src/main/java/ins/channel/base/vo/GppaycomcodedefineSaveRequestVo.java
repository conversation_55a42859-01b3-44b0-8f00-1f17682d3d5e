package ins.channel.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 *
 * GppaycomcodedefineVo对象.对应实体描述：收付机构定义表
 *
 */
@Data
@ApiModel("GppaycomcodedefineVo对象")
public class GppaycomcodedefineSaveRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：PAYMENT_COMCODE,备注：收付机构 */
	@ApiModelProperty("收付机构")
	@Size(min = 1,max = 20,message = "收付机构 不符合数据校验，最大长度为20")
	@NotBlank(message = "收付机构 不能为空")
	private String paymentComcode;
	/** 对应字段：DEPARTMENT_CODE,备注：出单机构 */
	@ApiModelProperty("出单机构")
	@Size(min = 1,max = 20,message = "出单机构 不符合数据校验，最大长度为20")
	@NotBlank(message = "出单机构 不能为空")
	private String departmentCode;
	/** 对应字段：OFCENTER_CNAME,备注：收付机构中文名称 */
	@ApiModelProperty("收付机构中文名称")
	@Size(min = 0,max = 33,message = "收付机构中文名称 不符合数据校验，最大长度为33")
	private String ofcenterCname;
	/** 对应字段：OFCENTER_TNAME,备注：收付机构繁体名称 */
	@ApiModelProperty("收付机构繁体名称")
	@Size(min = 0,max = 33,message = "收付机构繁体名称 不符合数据校验，最大长度为33")
	private String ofcenterTname;
	/** 对应字段：OFCENTER_ENAME,备注：收付机构英文名称 */
	@ApiModelProperty("收付机构英文名称")
	@Size(min = 0,max = 100,message = "收付机构英文名称 不符合数据校验，最大长度为100")
	private String ofcenterEname;
	/** 对应字段：VALID_IND,备注：有效标志 --** 0 无效 --** 1 有效 */
	@ApiModelProperty("有效标志 --** 0 无效 --** 1 有效")
	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	@Size(min = 0,max = 500,message = "备注 不符合数据校验，最大长度为500")
	private String remark;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("用户类型 1：保费  2：佣金 3：赔款  4：再保")
	private String userType;
}
