package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("GppaycomcodedefineSerachRequestVo对象")
public class GppaycomcodedefineSearchRequestVo extends PageBaseDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 对应字段：PAYMENT_COMCODE,备注：收付机构 */
    @ApiModelProperty("收付机构")
    private String paymentComcode;
    /** 对应字段：DEPARTMENT_CODE,备注：出单机构 */
    @ApiModelProperty("业务归属机构")
    private String departmentCode;

    @ApiModelProperty("有效标示")
    private String validInd;

}
