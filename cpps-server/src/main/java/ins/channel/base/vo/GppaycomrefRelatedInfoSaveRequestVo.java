package ins.channel.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * GppaycomrefVo对象.对应实体描述：归属机构与收付机构关联表
 *
 */
@Data
@ApiModel("GppaycomrefVo 关联关系对象")
public class GppaycomrefRelatedInfoSaveRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;

	List<String> companyList;
	/** 关联关系 */
	List<GppaycomrefRelatedInfoVo> relatedData;
}
