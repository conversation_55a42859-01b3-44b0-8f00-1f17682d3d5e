package ins.channel.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * GppaycomrefVo对象.对应实体描述：归属机构与收付机构关联表
 *
 */
@Data
@ApiModel("GppaycomrefVo对象")
public class GppaycomrefSaveRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：DEPARTMENT_CODE,备注：业务归属机构 */
	@ApiModelProperty("业务归属机构")
	private String departmentCode;
	/** 对应字段：USER_TYPE,备注：1:保费;2:佣金;3:赔款;4:再保 */
	@ApiModelProperty("1:保费;2:佣金;3:赔款;4:再保")
	private String userType;
	/** 对应字段：PAYMENT_COMCODE,备注：收付机构 */
	@ApiModelProperty("收付机构")
	private String paymentComcode;
}
