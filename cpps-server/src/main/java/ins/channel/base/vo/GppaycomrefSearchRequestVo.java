package ins.channel.base.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * GppaycomrefVo对象.对应实体描述：归属机构与收付机构关联表
 *
 */
@Data
@ApiModel("GppaycomrefVo对象")
public class GppaycomrefSearchRequestVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：DEPARTMENT_CODE,备注：业务归属机构 */
	@ApiModelProperty("业务归属机构")
	private String departmentCode;
	/** 对应字段：USER_TYPE,备注：1:保费;2:佣金;3:赔款;4:再保 */
	@ApiModelProperty("1:保费;2:佣金;3:赔款;4:再保")
	private String userType;
	/** 对应字段：PAYMENT_COMCODE,备注：收付机构 */
	@ApiModelProperty("收付机构")
	private String paymentComcode;

	@ApiModelProperty("查询创建日期 开始时间")
	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))" ,
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	private String startTime;

	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))" ,
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	@ApiModelProperty("查询创建日期 结束时间")
	private String endTime;

}
