package ins.channel.baseCode.api;

import ins.channel.baseCode.service.BaseCodeService;
import ins.channel.baseCode.vo.BaseCodeVo;
import ins.channel.policymain.service.PolicyMainService;
import ins.channel.policymain.vo.PolicySyncQueryRespVo;
import ins.channel.policymain.vo.PolicySyncRequestVo;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @apiNote 基础码表查询Api
 * @author: ZhouTaoyu
 * @date: 2019-11-20
 */
@RestController
@RequestMapping("api/baseCode")
@Api(tags = {"BaseCode"}, description = "基础码表查询相关服务")
public class BaseCodeApi {
    @Autowired
    private BaseCodeService baseCodeService;
    @Autowired
    private PolicyMainService policyMainService;

    @PostMapping("queryCodeList")
    @ApiOperation(value = "查询所有基础码表集合")
    public ResponseVo<List<BaseCodeVo>> queryCodeList() {
        List<BaseCodeVo> baseCodeVoList = baseCodeService.queryCodeList();
        return ResponseVo.ok(baseCodeVoList);
    }

    /**
     * @description: 批量/逐笔同步保单集合
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/1/27
     * @return:
     **/
    @PostMapping("syncPolicy")
    @ApiOperation(value = "批量/逐笔同步保单集合")
    public  ResponseVo<PolicySyncQueryRespVo> syncPolicy(@RequestBody PolicySyncRequestVo vo) {
        return ResponseVo.ok(policyMainService.syncPolicy(vo,0));
    }


    /**
     * @description: 根据保单号查询保单状态
     * @param: vo
     * @author: tangyan
     * @date: 2022/3/15
     * @return:
     **/
    @PostMapping("queryPolicyStatus")
    @ApiOperation(value = "根据保单号查询保单状态")
    public ResponseVo<PolicySyncQueryRespVo> queryPolicyStatus(@RequestBody PolicySyncRequestVo vo) {
        return ResponseVo.ok(policyMainService.queryPolicyStatus(vo));
    }
}
