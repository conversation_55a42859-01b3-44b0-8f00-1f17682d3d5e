package ins.channel.baseCode.service;

import ins.channel.baseCode.po.BaseCode;
import ins.channel.baseCode.vo.BaseCodeVo;
import ins.channel.channel.dao.GgchannelDao;
import ins.channel.code.dao.GgcodeDao;
import ins.channel.company.dao.GgcompanyDao;
import ins.channel.gsclientmain.dao.GsclientmainDao;
import ins.channel.risk.dao.GgriskDao;
import ins.channel.user.dao.GguserDao;
import ins.platform.utils.BeanCopyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @apiNote 基础码表查询Service
 * @author: ZhouTaoyu
 * @date: 2019-11-20
 */
@Service
public class BaseCodeService {
    @Autowired
    private GgcodeDao ggcodeDao;
    @Autowired
    private GgcompanyDao ggcompanyDao;
    @Autowired
    private GgriskDao ggriskDao;
    @Autowired
    private GgchannelDao ggchannelDao;
    @Autowired
    private GguserDao gguserDao;
    @Autowired
    private GsclientmainDao gsclientmainDao;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 查询所有基础码表集合
     *
     * @return
     */
    @Transactional(readOnly = true)
    public List<BaseCodeVo> queryCodeList() {
        //初始化基础码表集合
        List<BaseCodeVo> baseCodeVoList = new ArrayList<>();


        //Modifed by zhaojie 2019年12月9日15:04:26
        //begin
        List<BaseCode> ggchannelList = ggchannelDao.queryAll();
        List<BaseCodeVo> ggchannelvoList = BeanCopyUtils.cloneList(ggchannelList, BaseCodeVo.class);
        baseCodeVoList.addAll(ggchannelvoList);

        List<BaseCode> gguserList = gguserDao.queryAll();
        List<BaseCodeVo> gguservoList = BeanCopyUtils.cloneList(gguserList, BaseCodeVo.class);
        baseCodeVoList.addAll(gguservoList);

        //end

        //查询GGCODE码表
        List<BaseCode> ggCodeList = ggcodeDao.queryALLExclusions();
        List<BaseCodeVo> ggCodeVoList = BeanCopyUtils.cloneList(ggCodeList, BaseCodeVo.class);
        baseCodeVoList.addAll(ggCodeVoList);

        //查询GGCOMPANY码表
        List<BaseCode> ggcompanyList = ggcompanyDao.queryAll();
        List<BaseCodeVo> ggcompanyVoList = BeanCopyUtils.cloneList(ggcompanyList, BaseCodeVo.class);
        baseCodeVoList.addAll(ggcompanyVoList);
        //查询GPPAYCOMCODEDEFINE码表
//        List<BaseCode> gppaycompayList = gppaycomcodedefineDao.queryAll();
//        List<BaseCodeVo> gppaycompayVoList = BeanCopyUtils.cloneList(gppaycompayList, BaseCodeVo.class);
//        baseCodeVoList.addAll(gppaycompayVoList);
        //查询ggrisk码表
        List<BaseCode> ggriskList = ggriskDao.queryAll();
        List<BaseCodeVo> ggriskVoList = BeanCopyUtils.cloneList(ggriskList, BaseCodeVo.class);
        baseCodeVoList.addAll(ggriskVoList);
        //Modifed by 周涛宇 2021/02/22  查询GSCLIENTMAIN码表
        //begin
        List<BaseCode> gsclientmainList = gsclientmainDao.queryAll();
        List<BaseCodeVo> gsclientmainvoList = BeanCopyUtils.cloneList(gsclientmainList, BaseCodeVo.class);
        baseCodeVoList.addAll(gsclientmainvoList);
        //end
        return baseCodeVoList;
    }
}
