package ins.channel.claim.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import ins.channel.claim.vo.CallClaimReqVo;
import ins.channel.claim.vo.CallClaimRespVo;
import ins.channel.config.UrlConfig;
import ins.framework.exception.BusinessException;
import ins.framework.utils.Uuids;
import ins.platform.utils.HttpClientUtils;
import ins.platform.utils.HttpResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 调用理赔系统接口
 * 提供调用理赔接口的方法入口
 *
 * <AUTHOR>
 * @date 2021年02月08日 10:51:03
 */
@Service
@Slf4j
public class CallClaimService {

    @Autowired
    private UrlConfig urlConfig;

    /**
     * 查询被保险人赔案接口
     *
     * @param callClaimReqVo 请求报文载体
     * @Return 查询被保险人赔案信息集合
     * <AUTHOR>
     * @date 2021年02月08日 11:07:58
     */
    public List<CallClaimRespVo> queryClaims4Insured(CallClaimReqVo callClaimReqVo) {
        String url = urlConfig.getQueryClaims4Insured();
        log.debug(String.format("申报人员%s-查询被保险人赔案接口URL:%s", callClaimReqVo.getInsuredName(), url));
        String reqJson = JSONObject.toJSONString(callClaimReqVo, SerializerFeature.WriteMapNullValue);
        log.info(String.format("申报人员%s-查询被保险人赔案接口,推送报文:%s", callClaimReqVo, reqJson));
        HttpResponse httpResponse = null;
        try {
            httpResponse = HttpClientUtils.postJson(url, reqJson);
        } catch (Exception e) {
            log.info(String.format("申报人员%s-查询被保险人赔案接口异常:%s", callClaimReqVo.getInsuredName(), e.getMessage()));
            throw new BusinessException(String.format("%s-查询被保险人赔案接口异常:%s", callClaimReqVo.getInsuredName(), e.getMessage()));
        }
        List<CallClaimRespVo> callClaimRespVoList = new ArrayList<>();
        if (httpResponse != null) {
            String jsonStr = httpResponse.getBodyAsString();
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            if ("1".equals(jsonObject.get("code")) ||  "2".equals(jsonObject.get("code"))) {//成功
                log.info(String.format("申报人员%s-查询被保险人赔案接口返回报文状态:成功,报文:%s", callClaimReqVo.getInsuredName(), jsonStr));
                if (jsonObject.get("data") != null) {
                    callClaimRespVoList = JSONObject.parseArray(jsonObject.get("data").toString(), CallClaimRespVo.class);
                }
            } else if ("0".equals(jsonObject.get("code"))) {
                log.info(String.format("申报人员%s-查询被保险人赔案接口返回报文状态:未成功,报文:%s", callClaimReqVo.getInsuredName(), jsonStr));
                throw new BusinessException(String.format("%s-查询赔案记录未成功,报文:%s", callClaimReqVo.getInsuredName(), jsonStr));
            } else {
                log.info(String.format("申报人员%s-查询被保险人赔案接口返回报文code节点缺失,报文:%s", callClaimReqVo.getInsuredName(), jsonStr));
                throw new BusinessException(String.format("%s-查询赔案记录接口返回报文code节点缺失,报文:%s", callClaimReqVo.getInsuredName(), jsonStr));
            }
        } else {
            log.info(String.format("申报人员%s-查询被保险人赔案接口异常,返回报文为null", callClaimReqVo.getInsuredName()));
            throw new BusinessException(String.format("申报人员%s-查询被保险人赔案接口异常,返回报文为null", callClaimReqVo.getInsuredName()));
        }
        return callClaimRespVoList;
    }

}
