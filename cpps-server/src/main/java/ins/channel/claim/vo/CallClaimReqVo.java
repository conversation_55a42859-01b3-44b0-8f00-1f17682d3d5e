package ins.channel.claim.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 调用理赔查询被保险人赔案接口请求参数载体
 * <AUTHOR>
 * @date 2021年02月07日 17:39:22
 */
@Data
@ApiModel("CallClaimReqVo")
public class CallClaimReqVo implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("保单号码")
	private String policyNo;
	@ApiModelProperty("被保险人/投保人名称")
	private String insuredName;
	@ApiModelProperty("证件类型")
	private String identifyType;
	@ApiModelProperty("证件号码")
	private String identifyNumber;
	@ApiModelProperty("来源渠道1移动端2渠道")
	private String sourceFlag="2";
	@ApiModelProperty("1-车，0-非车")
	private String carFlag="0";
	
}
