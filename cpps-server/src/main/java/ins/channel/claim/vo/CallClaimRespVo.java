package ins.channel.claim.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;


/**
 * 调用理赔查询被保险人赔案接口返回参数载体
 * <AUTHOR>
 * @date 2021年02月07日 17:39:22
 */
@Data
@ApiModel("CallClaimRespVo")
public class CallClaimRespVo implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("保单号码")
	private String policyNo;
	@ApiModelProperty("报案号")
	private String registNo;
	@ApiModelProperty("立案号")
	private String claimNo;
	@ApiModelProperty("报案人名称")
	private String reportorName;
	@ApiModelProperty("出险时间")
	private Date damageTime;
	@ApiModelProperty("出险地点")
	private String damageAddress;
	@ApiModelProperty("出险原因")
	private String damageName;
	@ApiModelProperty("出险经过")
	private String damageDesc;
	@ApiModelProperty("赔付金额")
	private BigDecimal sumpaid;
	@ApiModelProperty("案件状态,1-报案、2资料审核、9-赔款支付")
	private String caseStatus;
	@ApiModelProperty("报案电话")
	private String reportorMobile;
	@ApiModelProperty("险种代码")
	private String riskCode;
	@ApiModelProperty("险种名称")
	private String riskName;
	
}
