package ins.channel.codetransfer.api;

import ins.channel.codetransfer.service.GfcodetransferService;
import ins.channel.codetransfer.vo.GfcodetransferSaveRequestVo;
import ins.channel.codetransfer.vo.GfcodetransferSearchRequestVo;
import ins.channel.codetransfer.vo.GfcodetransferKeyVo;
import ins.channel.codetransfer.vo.GfcodetransferVo;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/gfcodetransfer")
@Api(tags = "GfcodetransferApi", description = "转码配置表")
public class GfcodetransferApi {

    @Autowired
    private GfcodetransferService service;

    @ApiOperation(value = "分页查询转码配置")
    @PostMapping(value = "/searchPage")
    public ResponseVo<PageResult<GfcodetransferVo>> search(@Valid @ModelAttribute GfcodetransferSearchRequestVo gfcodetransferSearchRequestVo) {
        PageResult<GfcodetransferVo> result = service.search(gfcodetransferSearchRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "保存转码配置")
    @PostMapping(value = "/saveBankAccount")
    public ResponseVo<Integer> save(@Valid @RequestBody GfcodetransferSaveRequestVo gfcodetransferSaveRequestVo) {
        int result = service.create(gfcodetransferSaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "更新转码配置")
    @PostMapping(value = "/updateBankAccount")
    public ResponseVo<Integer> update(@Valid @RequestBody GfcodetransferSaveRequestVo gfcodetransferSaveRequestVo) {
        int result = service.update(gfcodetransferSaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "根据主键查询一条数据")
    @PostMapping(value = "/selectOne")
    public ResponseVo<GfcodetransferVo> selectOne(@RequestBody GfcodetransferKeyVo keyVo) {
        GfcodetransferVo result = service.queryOne(keyVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "删除转码配置")
    @PostMapping(value = "/deleteOne")
    public ResponseVo<Integer> deleteOne(@RequestBody GfcodetransferKeyVo keyVo) {
        int result = service.delete(keyVo);
        return ResponseVo.ok(result);
    }


}
