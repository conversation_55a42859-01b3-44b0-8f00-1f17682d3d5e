package ins.channel.codetransfer.api;

import ins.channel.codetransfer.service.GfcodetranstypeService;
import ins.channel.codetransfer.vo.GfcodetranstypeSaveRequestVo;
import ins.channel.codetransfer.vo.GfcodetranstypeSearchRequestVo;
import ins.channel.codetranstype.vo.GfcodetranstypeVo;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/gfcodetranstype")
@Api(tags = "GfcodetranstypeApi", description = "转码类型配置表")
public class GfcodetranstypeApi  {

    @Autowired
    private GfcodetranstypeService service;

    @ApiOperation(value = "分页查询转码类型配置")
    @PostMapping(value = "/searchPage")
    public ResponseVo<PageResult<GfcodetranstypeVo>> search(@Valid @ModelAttribute GfcodetranstypeSearchRequestVo gfcodetranstypeSearchRequestVo) {
        PageResult<GfcodetranstypeVo> result = service.search(gfcodetranstypeSearchRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "保存转码类型配置")
    @PostMapping(value = "/saveBankAccount")
    public ResponseVo<Integer> save(@Valid @RequestBody GfcodetranstypeSaveRequestVo gfcodetranstypeSaveRequestVo) {
        int result = service.create(gfcodetranstypeSaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "更新转码类型配置")
    @PostMapping(value = "/updateBankAccount")
    public ResponseVo<Integer> update(@Valid @RequestBody GfcodetranstypeSaveRequestVo gfcodetranstypeSaveRequestVo) {
        int result = service.update(gfcodetranstypeSaveRequestVo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "根据主键查询一条数据")
    @PostMapping(value = "/selectOne")
    public ResponseVo<GfcodetranstypeVo> selectOne(@RequestBody String gid) {
        GfcodetranstypeVo result = service.queryOne(gid);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "删除转码类型配置")
    @PostMapping(value = "/deleteOne")
    public ResponseVo<Integer> deleteOne(@RequestBody String gid) {
        int result = service.delete(gid);
        return ResponseVo.ok(result);
    }
}
