package ins.channel.codetransfer.service;

import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.codetransfer.vo.GfcodetransferSaveRequestVo;
import ins.channel.codetransfer.vo.GfcodetransferSearchRequestVo;
import ins.channel.codetransfer.dao.GfcodetransferDao;
import ins.channel.codetransfer.po.Gfcodetransfer;
import ins.channel.codetransfer.po.GfcodetransferKey;
import ins.channel.codetransfer.po.GfcodetransferSearch;
import ins.channel.codetransfer.vo.GfcodetransferKeyVo;
import ins.channel.codetransfer.vo.GfcodetransferVo;
import ins.platform.common.PageResult;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
@Slf4j
@Transactional
public class GfcodetransferService{
    @Autowired
    private GfcodetransferDao gfcodetransferDao;

    public int create(GfcodetransferSaveRequestVo vo) {
        Gfcodetransfer ggcodetype = BeanCopyUtils.clone(vo, Gfcodetransfer.class);
        int count = gfcodetransferDao.insertSelective(ggcodetype);
        return count;
    }

    /**
     * 更新数据
     * @param vo
     * @return
     */
    public int update(GfcodetransferSaveRequestVo vo) {
        return gfcodetransferDao.updateSelectiveByPrimaryKey(BeanCopyUtils.clone(vo, Gfcodetransfer.class));
    }

    /**
     * 查询单条数据
     * @param keyVo
     * @return
     */
    public GfcodetransferVo queryOne(GfcodetransferKeyVo keyVo) {
        GfcodetransferKey gfcodetransferKey = BeanCopyUtils.clone(keyVo, GfcodetransferKey.class);
        return BeanCopyUtils.clone(gfcodetransferDao.selectByPrimaryKey(gfcodetransferKey),GfcodetransferVo.class);
    }

    /**
     * 根据主键删除数据
     * @param keyVo
     * @return
     */
    public int delete(GfcodetransferKeyVo keyVo) {
        GfcodetransferKey gfcodetransferKey = BeanCopyUtils.clone(keyVo, GfcodetransferKey.class);
        return gfcodetransferDao.deleteByPrimaryKey(gfcodetransferKey);
    }

    /**
     * 前端根据实际业务情况来进行分页查询分页查询
     * @param vo
     * @return
     */
    public PageResult<GfcodetransferVo> search(GfcodetransferSearchRequestVo vo) {
        PageParam pageParam = PageHelper.getPageParam(vo);
        Page<Gfcodetransfer> gpcodetypes = gfcodetransferDao.searchPage(pageParam, BeanCopyUtils.clone(vo, GfcodetransferSearch.class));
        return PageHelper.convert(pageParam, gpcodetypes, GfcodetransferVo.class);
    }
}