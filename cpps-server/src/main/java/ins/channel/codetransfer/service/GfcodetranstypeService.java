package ins.channel.codetransfer.service;

import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.codetransfer.vo.GfcodetranstypeSaveRequestVo;
import ins.channel.codetransfer.vo.GfcodetranstypeSearchRequestVo;
import ins.channel.codetranstype.dao.GfcodetranstypeDao;
import ins.channel.codetranstype.po.Gfcodetranstype;
import ins.channel.codetranstype.po.GfcodetranstypeSearch;
import ins.channel.codetranstype.vo.GfcodetranstypeVo;
import ins.platform.common.PageResult;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@Transactional
public class GfcodetranstypeService {

    @Autowired
    private GfcodetranstypeDao gfcodetranstypeDao;

    public int create(GfcodetranstypeSaveRequestVo vo) {
        Gfcodetranstype ggcodetype = BeanCopyUtils.clone(vo, Gfcodetranstype.class);
        int count = gfcodetranstypeDao.insertSelectiveAuto(ggcodetype);
        return count;
    }

    /**
     * 更新数据
     * @param vo
     * @return
     */
    public int update(GfcodetranstypeSaveRequestVo vo) {
        return gfcodetranstypeDao.updateSelectiveByPrimaryKey(BeanCopyUtils.clone(vo, Gfcodetranstype.class));
    }

    /**
     * 查询单条数据
     * @param gid
     * @return
     */
    public GfcodetranstypeVo queryOne(String gid) {
        return BeanCopyUtils.clone(gfcodetranstypeDao.selectByPrimaryKey(gid),GfcodetranstypeVo.class);
    }

    /**
     * 根据主键删除数据
     * @param id
     * @return
     */
    public int delete(String id) {
        return gfcodetranstypeDao.deleteByPrimaryKey(id);
    }

    /**
     * 前端根据实际业务情况来进行分页查询分页查询
     * @param vo
     * @return
     */
    public PageResult<GfcodetranstypeVo> search(GfcodetranstypeSearchRequestVo vo) {
        PageParam pageParam = PageHelper.getPageParam(vo);
        Page<Gfcodetranstype> gpcodetypes = gfcodetranstypeDao.searchPage(pageParam, BeanCopyUtils.clone(vo, GfcodetranstypeSearch.class));
        return PageHelper.convert(pageParam, gpcodetypes, GfcodetranstypeVo.class);
    }
}
