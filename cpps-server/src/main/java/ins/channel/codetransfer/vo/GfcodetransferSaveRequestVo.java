package ins.channel.codetransfer.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * GfcodetransferVo对象.对应实体描述：转码配置表
 *
 */
@Data
@ApiModel("GfcodetransferVo对象")
public class GfcodetransferSaveRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：TRANS_TYPE,备注：转码类型 */
	@ApiModelProperty("转码类型")
	private String transType;
	/** 对应字段：CODE_CODE,备注：代码值 */
	@ApiModelProperty("代码值")
	private String codeCode;
	/** 对应字段：CODE_NAME,备注：代码名称 */
	@ApiModelProperty("代码名称")
	private String codeName;
	/** 对应字段：TRANS_CODE_CODE,备注：转换的代码值 */
	@ApiModelProperty("转换的代码值")
	private String transCodeCode;
	/** 对应字段：TRANS_CODE_NAME,备注：转换的代码名称 */
	@ApiModelProperty("转换的代码名称")
	private String transCodeName;
	/** 对应字段：VALID_IND,备注：有效状态 0 无效 1 有效 */
	@ApiModelProperty("有效状态 0 无效 1 有效")
	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：预留标志 */
	@ApiModelProperty("预留标志")
	private String flag;
	/** 对应字段：VAILD_DATE,备注：生效时间 */
	@ApiModelProperty("生效时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date vaildDate;
	/** 对应字段：INVALID_DATE,备注：失效时间 */
	@ApiModelProperty("失效时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date invalidDate;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date modifiedTime;
	/** 对应字段：UPDATE_TIMES,备注：修改次数 */
	@ApiModelProperty("修改次数")
	private BigDecimal updateTimes;
}
