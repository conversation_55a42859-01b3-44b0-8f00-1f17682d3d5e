package ins.channel.codetransfer.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * GfcodetransferVo对象.对应实体描述：转码配置表
 *
 */
@Data
@ApiModel("GfcodetransferVo对象")
public class GfcodetransferSearchRequestVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：TRANS_TYPE,备注：转码类型 */
	@ApiModelProperty("转码类型")
	private String transType;
	/** 对应字段：CODE_CODE,备注：代码值 */
	@ApiModelProperty("代码值")
	private String codeCode;
	/** 对应字段：CODE_NAME,备注：代码名称 */
	@ApiModelProperty("代码名称")
	private String codeName;
	/** 对应字段：VALID_IND,备注：有效状态 0 无效 1 有效 */
	@ApiModelProperty("有效状态 0 无效 1 有效")
	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
	private String validInd;

	@ApiModelProperty("查询创建日期 开始时间")
	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))",
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	private String startTime;

	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))",
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	@ApiModelProperty("查询创建日期 结束时间")
	private String endTime;
}
