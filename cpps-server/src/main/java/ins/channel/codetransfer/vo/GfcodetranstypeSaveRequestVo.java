package ins.channel.codetransfer.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * GfcodetranstypeVo对象.对应实体描述：转码类型配置表
 *
 */
@Data
@ApiModel("GfcodetranstypeVo对象")
public class GfcodetranstypeSaveRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：TRANS_TYPE,备注：转码类型，如：feetypecode收付原因代码转换 ttytype合约类型转换财务代码 */
	@ApiModelProperty("转码类型，如：feetypecode收付原因代码转换 ttytype合约类型转换财务代码")
	private String transType;
	/** 对应字段：TRANS_TYPE_CDESC,备注：转码类型中文描述 */
	@ApiModelProperty("转码类型中文描述")
	private String transTypeCdesc;
	/** 对应字段：TRANS_TYPE_TDESC,备注：转码类型繁体描述 */
	@ApiModelProperty("转码类型繁体描述")
	private String transTypeTdesc;
	/** 对应字段：TRANS_TYPE_EDESC,备注：转码类型英文描述 */
	@ApiModelProperty("转码类型英文描述")
	private String transTypeEdesc;
	/** 对应字段：VALID_IND,备注：有效状态 0 无效 1 有效 */
	@ApiModelProperty("有效状态 0 无效 1 有效")
	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
	private String validInd;
	/** 对应字段：REMARK,备注：备注 */
	@ApiModelProperty("备注")
	private String remark;
	/** 对应字段：FLAG,备注：预留标志 */
	@ApiModelProperty("预留标志")
	private String flag;
	/** 对应字段：VAILD_DATE,备注：生效时间 */
	@ApiModelProperty("生效时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date vaildDate;
	/** 对应字段：INVALID_DATE,备注：失效时间 */
	@ApiModelProperty("失效时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date invalidDate;
	/** 对应字段：CREATE_TIME,备注：创建时间 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date createTime;
	/** 对应字段：MODIFIED_TIME,备注：更新时间 */
	@ApiModelProperty("更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date modifiedTime;
}
