package ins.channel.codetransfer.vo;

import ins.platform.common.PageBaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * GfcodetranstypeVo对象.对应实体描述：转码类型配置表
 *
 */
@Data
@ApiModel("GfcodetranstypeVo对象")
public class GfcodetranstypeSearchRequestVo extends PageBaseDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：TRANS_TYPE,备注：转码类型，如：feetypecode收付原因代码转换 ttytype合约类型转换财务代码 */
	@ApiModelProperty("转码类型，如：feetypecode收付原因代码转换 ttytype合约类型转换财务代码")
	private String transType;
	/** 对应字段：TRANS_TYPE_CDESC,备注：转码类型中文描述 */
	@ApiModelProperty("转码类型中文描述")
	private String transTypeCdesc;
	/** 对应字段：VALID_IND,备注：有效状态 0 无效 1 有效 */
	@ApiModelProperty("有效状态 0 无效 1 有效")
	@Pattern(regexp = "[0|1]{0,1}",message = "有效标示不符合数据校验 0|1")
	private String validInd;

	@ApiModelProperty("查询创建日期 开始时间")
	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))",
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	private String startTime;

	@Pattern(regexp = "((^$)|([0-9]{4}[-][0-9]{2}[-][0-9]{2}))",
			message = "查询起始日期不符合规定格式 yyyy-MM-dd")
	@ApiModelProperty("查询创建日期 结束时间")
	private String endTime;

}
