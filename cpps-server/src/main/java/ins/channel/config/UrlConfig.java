package ins.channel.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
  * @description: 服务间调用接口配置类
  * @author: <PERSON><PERSON><PERSON><PERSON>
  * @date: 2021/1/26
 */
@Data
@Configuration
public class UrlConfig {
    @Value("${policy.channel.queryPolicyNoEmployer}")
    private String queryPolicyNoEmployer;
    @Value("${policy.channel.queryPolicyEmployer}")
    private String queryPolicyEmployer;
    @Value("${policy.channel.endorCommonInQua}")
    private String endorCommonInQua;
    //调用理赔接口,查询被保险人赔案信息URL
    @Value("${policy.channel.queryClaims4Insured}")
    private String queryClaims4Insured;
    //调用服务平台,获取保险凭证打印平台PDF的URL
    @Value("${policy.channel.callSP4PolicyVoucher}")
    private String callSP4PolicyVoucher;
    //调用服务平台,获取电子批单PDF的URL
    @Value("${policy.channel.callSP4EndorNo}")
    private String callSP4EndorNo;
    //调用服务平台，获取结算路径
    @Value("${policy.channel.settall}")
    private String settall;

    @Value("${sale.createProposal}")
    private String createProposalUrl;
}
