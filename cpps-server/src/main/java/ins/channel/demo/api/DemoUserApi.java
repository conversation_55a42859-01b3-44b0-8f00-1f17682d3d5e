package ins.channel.demo.api;

import ins.framework.common.ResultPage;
import ins.framework.web.ApiResponse;
import ins.channel.demo.service.UserService;
import ins.channel.demo.vo.UserInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by sino on 2019/9/3.
 */
@RestController
@RequestMapping("/demo/user")
@Api(tags = "Demo-User", description = "DEMO-用户服务")
public class DemoUserApi {

    @Autowired
    private UserService service;

    @ApiOperation("新增人员")
    @PostMapping("/addOne")
    public ApiResponse<Void> addOne(@RequestBody UserInfoVo vo) {
        service.addOne(vo);
        return ApiResponse.ok();
    }

    @ApiOperation("通过年龄列表查询")
    @GetMapping("/listByAge")
    public ApiResponse<List<UserInfoVo>> listByAge(@RequestParam("age") Integer age) {
        List<UserInfoVo> userInfoVos = service.listByAge(age);
        return ApiResponse.ok(userInfoVos);
    }

    @ApiOperation("分页查询")
    @PostMapping("/pageByCondition")
    public ApiResponse<ResultPage<UserInfoVo>> pageByCondition(@ModelAttribute UserInfoVo conditionVo) {
        return ApiResponse.ok(service.pageByCondition(conditionVo));
    }

    @ApiOperation("列表查询")
    @GetMapping("/listAll")
    public ApiResponse<List<UserInfoVo>> listAll() {
        List<UserInfoVo> userInfoVos = service.listAll();
        return ApiResponse.ok(userInfoVos);
    }

}
