package ins.channel.demo.api;

import java.util.Date;

import javax.servlet.http.HttpServletRequest;

import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import ins.framework.common.ResultPage;
import ins.framework.web.ApiResponse;
import ins.channel.demo.service.EchoService;
import ins.channel.demo.vo.EchoVo;
import io.swagger.annotations.ApiOperation;

/**
 * 示例用的EchoService实现
 *
 */
@RestController
@RequestMapping("/demo/echo")
@Api(tags = "Demo-Echo", description = "DEMO-回显服务")
public class EchoApi {
	@Autowired
	private EchoService echoService;

	@ApiOperation(value = "传入对象，返回对象")
	@PostMapping(value = "/echoObjectByObject")
	public ApiResponse<EchoVo> echoObjectByObject(@RequestBody EchoVo echo) {
		EchoVo vo = echoService.echoObjectByObject(echo);
		return ApiResponse.ok(vo);
	} 
	
	@ApiOperation(value = "传入String，返回String")
	@GetMapping(value = "/echoStringByString/{value}")
	public ApiResponse<String> echoStringByString(HttpServletRequest req, @PathVariable(value = "value") String value) {
		String str = echoService.echoStringByString(value);
		// 往Session中存入内容，在echoStringInSession可以获取
		req.getSession().setAttribute("echoString", str);
		// 演示TraceLog用
		for (int i = 0; i < 3; i++) {
			echoService.echoStringByString(value);
		}
		return ApiResponse.ok(str);
	}

	@ApiOperation(value = "传入String，返回对象")
	@GetMapping(value = "/echoObjectByString/{value}")
	public ApiResponse<EchoVo> echoObjectByString(@PathVariable(value = "value") String value) {
		EchoVo vo = echoService.echoObjectByString(value);
		return ApiResponse.ok(vo);
	}

	@ApiOperation(value = "传入对象，返回String")
	@PostMapping(value = "/echoStringByObject")
	public ApiResponse<String> echoStringByObject(@RequestBody EchoVo echo) {
		String str = echoService.echoStringByObject(echo);
		return ApiResponse.ok(str);
	}

	@ApiOperation(value = "传入String和Date，返回String")
	@GetMapping(value = "/echoStringByStringAndDate")
	public ApiResponse<String> echoStringByStringAndDate(@RequestParam(name = "value") String value,
			@RequestParam(name = "date") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date) {
		String str = echoService.echoStringByStringAndDate(value, date);
		return ApiResponse.ok(str);
	}

	@ApiOperation(value = "分页查询")
	@GetMapping(value = "/search")
	public ApiResponse<ResultPage<EchoVo>> search(@RequestParam(name = "_pageNo", defaultValue = "1") int pageNo,
			@RequestParam(name = "_pageSize", defaultValue = "10") int pageSize) {
		ResultPage<EchoVo> result = echoService.search(pageNo, pageSize);
		return ApiResponse.ok(result);
	}
}
