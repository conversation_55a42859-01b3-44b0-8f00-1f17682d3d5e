package ins.channel.demo.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.channel.demo.po.UserInfo;
import ins.framework.mybatis.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * 表user_info对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface UserInfoDao extends MybatisBaseDao<UserInfo, Long> {

    List<UserInfo> selectByAge(@Param("age") Integer age);

    List<UserInfo> selectAll(@Param("tableName") String tableName);

}
