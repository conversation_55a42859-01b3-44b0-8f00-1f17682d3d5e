package ins.channel.demo.po;

import java.io.Serializable;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表user_info的PO对象<br/>
 * 对应表名：user_info
 *
 */
@Data
@Table(name = "user_info")
public class UserInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：idnum,备注：主键ID */
	@Column(name = "idnum", description = "主键ID")
	private Long idnum;
	/** 对应字段：user_code,备注：代码 */
	@Column(name = "user_code", description = "代码")
	private String userCode;
	/** 对应字段：user_name,备注：名称 */
	@Column(name = "user_name", description = "名称")
	private String userName;
	/** 对应字段：parent_name,备注：父亲名称 */
	@Column(name = "parent_name", description = "父亲名称")
	private String parentName;
	/** 对应字段：age,备注：年龄 */
	@Column(name = "age", description = "年龄")
	private Integer age;
}
