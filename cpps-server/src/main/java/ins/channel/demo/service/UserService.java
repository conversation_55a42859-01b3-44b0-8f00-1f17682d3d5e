package ins.channel.demo.service;

import ins.framework.common.ResultPage;
import ins.framework.mybatis.MybatisApiUtils;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.framework.mybatis.util.Pages;
import ins.channel.demo.dao.UserInfoDao;
import ins.channel.demo.po.UserInfo;
import ins.channel.demo.vo.UserInfoVo;
import ins.platform.utils.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;

/**
 * Created by sino on 2019/9/3.
 */
@Service
public class UserService {

    @Autowired
    private UserInfoDao mapper;

    @Transactional
    public void addOne(UserInfoVo vo) {
        Assert.notNull(vo, "Object must have value");
        mapper.insertSelective(BeanCopyUtils.clone(vo, UserInfo.class));
    }

    @Transactional(readOnly = true)
    public List<UserInfoVo> listByAge(Integer age) {
        return BeanCopyUtils.cloneList(mapper.selectByAge(age), UserInfoVo.class);
    }

    @Transactional(readOnly = true)
    public ResultPage<UserInfoVo> pageByCondition(UserInfoVo vo) {
        PageParam pageParam = MybatisApiUtils.getPageParam();
        Page<UserInfo> userInfos = mapper.selectPage(pageParam, BeanCopyUtils.clone(vo, UserInfo.class));
        return Pages.convert(pageParam, userInfos, UserInfoVo.class);
    }

    @Transactional(readOnly = true)
    public List<UserInfoVo> listAll() {
        return BeanCopyUtils.cloneList(mapper.selectAll("user_info"), UserInfoVo.class);
    }

}
