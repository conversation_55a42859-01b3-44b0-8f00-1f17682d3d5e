package ins.channel.demo.vo;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * UserInfoVo对象.
 *
 */
@Data
@ApiModel("UserInfoVo对象")
public class UserInfoVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：idnum,备注：主键ID */
	@ApiModelProperty("主键ID")
	private Long idnum;
	/** 对应字段：user_code,备注：代码 */
	@ApiModelProperty("代码")
	private String userCode;
	/** 对应字段：user_name,备注：名称 */
	@ApiModelProperty("名称")
	private String userName;
	/** 对应字段：parent_name,备注：父亲名称 */
	@ApiModelProperty("父亲名称")
	private String parentName;
	/** 对应字段：age,备注：年龄 */
	@ApiModelProperty("年龄")
	private Integer age;
}
