package ins.channel.filter;


import ins.channel.utils.RSAUtil;
import ins.framework.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Slf4j
public class WrapperedRequest extends HttpServletRequestWrapper {


    private String requestBody;

    private ByteArrayInputStream byteArrayInputStream;

    private HttpServletRequest req;

    private byte[] privateKey;


    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request the {@link HttpServletRequest} to be wrapped.
     * @throws IllegalArgumentException if the request is null
     */
    public WrapperedRequest(String privateKey,HttpServletRequest request) throws Exception {
        super(request);
        this.req = request;
//        this.requestBody = requestBody;
        this.privateKey = Base64.getDecoder().decode(privateKey);

        ServletInputStream inputStream = request.getInputStream();

        ByteArrayOutputStream data = new ByteArrayOutputStream();

        byte[] b = new byte[1024];
        int len = 0;
        while ((len = inputStream.read(b)) != -1){
            data.write(b,0,len);
        }
        this.requestBody = new String(data.toByteArray(),StandardCharsets.UTF_8);
        log.info("请求报文:{}",this.requestBody);
        if(this.requestBody.length() > 0){
            try{
                byte[] bytes = RSAUtil.decryptByPrivateKey(Base64.getDecoder().decode(this.requestBody), this.privateKey);
                String body = new String(bytes, StandardCharsets.UTF_8);
                body = URLDecoder.decode(body,"utf-8");
                this.byteArrayInputStream = new ByteArrayInputStream(body.getBytes(StandardCharsets.UTF_8));
                log.info("请求报文-解密:{}", body);
//                this.requestBody = new String(bytes,StandardCharsets.UTF_8);
                if(bytes.length == 0){
                    throw new BusinessException("解密失败");
                }
            }catch (Exception e){
                throw new BusinessException("解密失败");
            }
        }else{
            this.byteArrayInputStream = new ByteArrayInputStream(new byte[]{});
        }


    }

    @Override
    public ServletRequest getRequest() {
        return req;
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new StringReader(requestBody));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {


        return new ServletInputStream() {
            ByteArrayInputStream i = byteArrayInputStream;
            @Override
            public boolean isFinished() {
                return true;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener) {

            }

            @Override
            public int read() throws IOException {
                return i.read();
            }

            @Override
            public int read(byte[] b) throws IOException {
                return i.read(b);
            }

            @Override
            public int read(byte[] b, int off, int len) throws IOException {
                return i.read(b, off, len);
            }
        };
    }
}
