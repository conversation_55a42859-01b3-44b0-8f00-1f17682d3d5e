package ins.channel.filter;

import ins.channel.utils.RSAUtil;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLEncoder;
import java.util.Base64;

@Slf4j
public class WrapperedResponse extends HttpServletResponseWrapper {

    private HttpServletResponse response;

    private ByteArrayOutputStream byteArrayOutputStream;

    private ServletOutputStream servletOutputStream;

    private PrintWriter writer;

    private byte[] key;

    /**
     * Constructs a response adaptor wrapping the given response.
     *
     * @param response the {@link HttpServletResponse} to be wrapped.
     * @throws IllegalArgumentException if the response is null
     */
    public WrapperedResponse(String key,HttpServletResponse response) {
        super(response);
        this.response = response;
        this.byteArrayOutputStream = new ByteArrayOutputStream();
        this.key = Base64.getDecoder().decode(key);
    }

    public void encode() throws Exception {
        log.info("响应数据大小:{}",byteArrayOutputStream.size());
        log.info("响应报文:{}", new String(byteArrayOutputStream.toByteArray(), "utf-8"));
        String r= URLEncoder.encode(new String(byteArrayOutputStream.toByteArray(), "utf-8"),"utf-8");
        byte[] bytes = RSAUtil.encryptByPublicKey(r.getBytes(), key);
        ServletOutputStream outputStream = this.response.getOutputStream();
        outputStream.write(Base64.getEncoder().encode(bytes));
        outputStream.flush();
    }

    @Override
    public ServletOutputStream getOutputStream() {
        log.info("获取OutputStream");
        return new WrapperedOutputStream(byteArrayOutputStream);
    }

    @Override
    public void flushBuffer() throws IOException {
        log.info("flushBuffer");
        if(this.servletOutputStream != null){
            this.servletOutputStream.flush();
        }
        if(this.writer != null){
            this.writer.flush();
        }
    }

    @Override
    public void reset() {
        log.info("reset");
        this.byteArrayOutputStream.reset();
    }

    private class WrapperedOutputStream extends ServletOutputStream{

        private ByteArrayOutputStream outputStream;

        public WrapperedOutputStream(ByteArrayOutputStream outputStream){
            this.outputStream = outputStream;
        }

        @Override
        public boolean isReady() {
            return false;
        }

        @Override
        public void setWriteListener(WriteListener writeListener) {

        }

        @Override
        public void write(int b) throws IOException {
            outputStream.write(b);
        }

        @Override
        public void write(byte[] b) throws IOException {
            outputStream.write(b);
        }

        @Override
        public void write(byte[] b, int off, int len) throws IOException {
            outputStream.write(b, off, len);
        }
    }
}
