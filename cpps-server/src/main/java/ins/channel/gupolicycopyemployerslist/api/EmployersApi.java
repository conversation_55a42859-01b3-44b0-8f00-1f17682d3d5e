package ins.channel.gupolicycopyemployerslist.api;

import ins.channel.gupolicycopyemployerslist.po.Gupolicycopyemployerslist;
import ins.channel.gupolicycopyemployerslist.service.GuPolicyCopyEmployersListService;
import ins.channel.gupolicycopyemployerslist.vo.GuPolicyCopyEmployersListVo;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/employers")
public class EmployersApi {

    @Autowired
    private GuPolicyCopyEmployersListService service;

    @RequestMapping("/queryByPage")
    @ResponseBody
    public ResponseVo<PageResult<Gupolicycopyemployerslist>> getData(@RequestBody GuPolicyCopyEmployersListVo vo){
        PageResult<Gupolicycopyemployerslist> data = service.getDataByEndorNo(vo);
        return ResponseVo.ok(data);

    }


}
