package ins.channel.gupolicycopyemployerslist.service;


import ins.channel.gupolicycopyemployerslist.dao.GupolicycopyemployerslistDao;
import ins.channel.gupolicycopyemployerslist.po.Gupolicycopyemployerslist;
import ins.channel.gupolicycopyemployerslist.vo.GuPolicyCopyEmployersListVo;
import ins.framework.exception.BusinessException;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.platform.common.PageResult;
import ins.platform.utils.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GuPolicyCopyEmployersListService {

    @Autowired
    private GupolicycopyemployerslistDao dao;

    public PageResult<Gupolicycopyemployerslist> getDataByEndorNo(GuPolicyCopyEmployersListVo vo){
        if(vo == null || vo.getEndorNo() == null){
            throw new BusinessException("批单号不能为空");
        }
        PageParam pageParam = PageHelper.getPageParam(vo);
        Gupolicycopyemployerslist gupolicycopyemployerslist = new Gupolicycopyemployerslist();
        gupolicycopyemployerslist.setEndorNo(vo.getEndorNo());
        Page<Gupolicycopyemployerslist> page = dao.selectByConditionForPage(pageParam, gupolicycopyemployerslist);
        return PageHelper.convert(pageParam, page, Gupolicycopyemployerslist.class);
    }




}
