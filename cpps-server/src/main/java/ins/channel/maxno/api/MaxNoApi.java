package ins.channel.maxno.api;


import ins.channel.gumaxno.vo.GumaxnoVo;
import ins.channel.maxno.service.MaxNoService;
import ins.channel.maxno.vo.MaxNoQueryVo;
import ins.framework.exception.BusinessException;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @apiNote 日志存储相关服务
 * @author: Zhou<PERSON>aoyu
 * @date: 2021-02-02
 */
@RestController
@RequestMapping("api/maxno")
@Api(tags = {"MaxNo"}, description = "日志存储相关服务")
public class MaxNoApi {

}
