package ins.channel.maxno.service;

import ins.channel.gumaxno.dao.GumaxnoDao;
import ins.channel.gumaxno.po.Gumaxno;
import ins.channel.gumaxno.vo.GumaxnoVo;
import ins.channel.maxno.vo.MaxNoQueryVo;
import ins.channel.support.constant.BusinessNoType;
import ins.channel.support.service.BusinessNoService;
import ins.platform.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * @apiNote: 最新版本号Service
 * @author: ZhouTaoyu
 * @date: 2021-02-02
 */
@Service
@Slf4j
@Transactional
public class MaxNoService {
    @Autowired
    private GumaxnoDao maxnoDao;
    @Autowired
    private BusinessNoService businessNoService;

    /**
     * @description: 根据保单号, 可修改状态,标识 查询并更新最新版本号数据(自增1)
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/2/2
     * @return: 查询失败: 返回"0"  查询成功: 返回最新版本号字符串
     **/
    @Transactional(rollbackFor = Exception.class)
    public String query(GumaxnoVo vo) throws Exception {
        String result = "0";
        vo.setStatus("0");
        Gumaxno gumaxno = null;
        try {
            gumaxno = maxnoDao.selectByCondition(vo);
        } catch (Exception e) {
            return "0";
        }
        if (gumaxno != null) {
            int temp = (Integer.parseInt(gumaxno.getVersionNo()));
            temp++;
            result = String.valueOf(temp);
            gumaxno.setVersionNo(result);
            gumaxno.setStatus("1");
            Map<Object, Object> map = new HashMap<>();
            map.put("maxno", gumaxno);
            map.put("oldStatus", "0");
            Integer i = maxnoDao.updateSelectiveByPolicyNoAndStatus(map);
            if (i != 1) {
                return "0";
            }
        } else {
            //查看是否为第一次批改
            vo.setStatus(null);
            Gumaxno maxno = maxnoDao.selectByCondition(vo);
            if (maxno == null) {
                Gumaxno clone = BeanCopyUtils.clone(vo, Gumaxno.class);
                String id = businessNoService.nextNo(BusinessNoType.ID);
                clone.setId(id);
                clone.setStatus("1");
                clone.setVersionNo("1");
                try {
                    maxnoDao.insertSelective(clone);
                } catch (Exception e) {
                    log.debug(e.getMessage());
                    return "0";
                }
                result = clone.getVersionNo();
            }
        }
        return result;
    }

    /**
     * @description: 获取最新版本号公共入口, 循环调用, 当未查询到版本号时, 等待100毫秒, 3次后仍未获取到, 则抛错
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/2/2
     * @return: 查询失败: 返回"0"  查询成功: 返回最新版本号字符串
     **/
    @Transactional(rollbackFor = Exception.class)
    public MaxNoQueryVo getVersionNo(GumaxnoVo vo) throws Exception {
        MaxNoQueryVo maxNoQueryVo = new MaxNoQueryVo();
        String result = null;
        String errorMsg = "获取批改序号失败!网络异常!请稍后重试!";
        try {
            for (int i = 0; i < 3; i++) {
                //获取锁
                result = this.query(vo);
                if ("0".equals(result)) {
                    System.out.println(Thread.currentThread().getName() + "sleepping!");
                    Thread.currentThread().sleep(100);
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            log.info(MessageFormat.format("获取批改序号失败!异常信息:{0}", e.getMessage()));
            maxNoQueryVo.setCode("500");
            maxNoQueryVo.setResult(errorMsg);
            return maxNoQueryVo;
        }
        if ("0".equals(result)) {
            maxNoQueryVo.setCode("500");
            maxNoQueryVo.setResult(errorMsg);
        } else {
            maxNoQueryVo.setCode("200");
            maxNoQueryVo.setResult(result);
        }
        return maxNoQueryVo;
    }

    /**
     * @description: 释放保单锁
     * @param:
     * @author: zhoutaoyu
     * @date: 2021/2/2
     * @return:
     **/
    @Transactional(rollbackFor = Exception.class)
    public void releaseKey(GumaxnoVo vo) {
        Gumaxno gumaxno = BeanCopyUtils.clone(vo, Gumaxno.class);
        gumaxno.setStatus("0");
        Map<Object, Object> map = new HashMap<>();
        map.put("maxno", gumaxno);
        map.put("oldStatus", "1");
        maxnoDao.updateSelectiveByPolicyNoAndStatus(map);
    }

    /**
     * 补零操作
     *
     * @param length
     * @param number
     * @return
     */
    private String zeroFill(int length, int number) {
        return String.format("%0".concat(String.valueOf(length)).concat("d"), number);
    }

}
