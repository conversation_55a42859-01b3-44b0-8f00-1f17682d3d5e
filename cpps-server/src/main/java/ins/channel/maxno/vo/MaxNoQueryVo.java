package ins.channel.maxno.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;

/**
 * @description: 最新版本号查询结果Vo对象
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2021/02/02
 */
@ApiModel(value = "最新版本号查询结果Vo对象")
@Data
@SuppressWarnings("serial")
public class MaxNoQueryVo implements Serializable {
    @ApiModelProperty(value = "状态代码 200-成功 500-异常")
    String code;
    @ApiModelProperty(value = "查询结果/异常信息")
    String result;

    private static final long serialVersionUID = 1L;
}
