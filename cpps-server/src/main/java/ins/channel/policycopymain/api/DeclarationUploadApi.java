package ins.channel.policycopymain.api;

import ins.channel.policycopymain.service.DeclarationInputService;
import ins.channel.policycopymain.vo.DeclarationQuoteReqVo;
import ins.framework.exception.BusinessException;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * @author: ZhouTaoyu
 * @date: 2021/03/01
 */
@RestController
@RequestMapping("/api/declarationUpload")
@Api(tags = "DeclarationUpload", description = "批量申报导入服务")
public class DeclarationUploadApi {

    @Autowired
    DeclarationInputService declarationInputService;

    /**
     * 根据前端传来的excel文件及对应的创新业务标识,解析excel并保存至数据库中
     *
     * @param file 上传的excel文件,需校验合法性及大小
     * @return
     */
    @ApiOperation(value = "批量申报excel文件上传解析")
    @PostMapping(value = "/resolveExcel")
    public ResponseVo<DeclarationQuoteReqVo> resolveExcel(MultipartFile file,DeclarationQuoteReqVo vo) throws BusinessException {
        return ResponseVo.ok(declarationInputService.resolveExcel(file,vo));
    }

    @ApiOperation(value = "批量投保excel文件上传解析")
    @PostMapping(value = "/resolveProposalExcel")
    public ResponseVo<DeclarationQuoteReqVo> resolveProposalExcel(MultipartFile file, DeclarationQuoteReqVo vo) throws BusinessException {
        return ResponseVo.ok(declarationInputService.resolveProposalExcel(file,vo));
    }
}
