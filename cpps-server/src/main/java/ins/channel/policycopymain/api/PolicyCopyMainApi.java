package ins.channel.policycopymain.api;


import ins.channel.gupolicycopymain.vo.DeclarationReqVo;
import ins.channel.gupolicycopymain.vo.DeclarationRespVo;
import ins.channel.gupolicyemployerslist.vo.EmployerslistQueryReqVo;
import ins.channel.gupolicyemployerslist.vo.EmployerslistQueryRespVo;
import ins.channel.gupolicymain.vo.PolicyMainReqVo;
import ins.channel.gupolicymain.vo.PolicyMainRespVo;
import ins.channel.policycopymain.service.PolicyCopyMainService;
import ins.channel.policycopymain.vo.DeclarationQuoteReqVo;
import ins.channel.policycopymain.vo.DeclarationQuoteRespVo;
import ins.channel.policymain.service.PolicyMainService;
import ins.framework.exception.BusinessException;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: ZhouTaoyu
 * @date: 2021/01/26
 */
@RestController
@RequestMapping("/api/policycopymain")
@Api(tags = "PolicyCopyMain", description = "申报服务")
public class PolicyCopyMainApi {
    @Autowired
    private PolicyCopyMainService policyCopyMainService;
    @Autowired
    PolicyMainService policyMainService;

    /**
     * @description: 根据批改报价保存轨迹表数据
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/2/10
     * @return:
     **/
    @ApiOperation(value = "根据批改报价保存轨迹表数据")
    @PostMapping("saveTrack")
    public ResponseVo<DeclarationQuoteRespVo> saveTrack(@RequestBody DeclarationQuoteRespVo vo) {
        return ResponseVo.ok(policyCopyMainService.saveTrack(vo,vo.getFlag()));
    }

    /**
     * @description: 根据传入的申报单号,调用服务平台批改承保风险接口, 进行申报批单生成/补生成批单
     * @param: vo  flag 0-批单生成 1-补生成批单
     * @author: zhoutaoyu
     * @date: 2021/2/22
     * @return:
     **/
    @ApiOperation(value = "申报批单生成/补生成批单")
    @PostMapping("declaration")
    public ResponseVo<DeclarationQuoteRespVo> declaration(@RequestBody DeclarationQuoteRespVo vo) throws Exception{
        return ResponseVo.ok(policyCopyMainService.batchDeclaration(vo));
    }

    /**
     * @description: 根据传入的申报单号,删除轨迹表数据
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/2/22
     * @return:
     **/
    @ApiOperation(value = "申报取消")
    @PostMapping("cancelDeclaration")
    public ResponseVo<DeclarationQuoteRespVo> cancelDeclaration(@RequestBody List<String> endorNoList) throws Exception{
        return ResponseVo.ok(policyCopyMainService.cancelDeclaration(endorNoList));
    }

    /**
     * 申报查询
     * @param vo 条件查询载体
     * @Return 查询结果
     * <AUTHOR>
     * @date 2021年02月07日 17:38:12
     */
    @ApiOperation(value = "申报查询-分页模糊查询申报信息")
    @PostMapping(value = "/pageQueryDeclarationByCondition")
    public ResponseVo<PageResult<DeclarationRespVo>> pageQueryDeclarationByCondition(@RequestBody DeclarationReqVo vo) {
        vo.setIsExcelBatchQueryFlag(0);
        PageResult<DeclarationRespVo> result = policyCopyMainService.pageQueryDeclarationByCondition(vo);
        return ResponseVo.ok(result);
    }


    /**
     * 申报查询-根据条件查询结果集导出
     * @param vo 条件查询载体
     * @return
     */
    @ApiOperation(value = "申报查询-根据条件查询结果集导出")
    @PostMapping(value = "/selectByAllDeclarationRespVo")
    public ResponseVo<List<DeclarationRespVo>>selectByAllDeclarationRespVo(@RequestBody DeclarationReqVo vo){
        List<DeclarationRespVo> declarationRespVoList = policyCopyMainService.selectByAllDeclarationRespVo(vo);
        return ResponseVo.ok(declarationRespVoList);
    }

    /**
     * @description: 申报页面-分页模糊查询保单信息
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/2/8
     * @return:
     **/
    @ApiOperation (value = "申报页面-分页模糊查询保单信息")
    @PostMapping (value = "/queryPolicyByPage")
    public ResponseVo<PageResult<PolicyMainRespVo>> queryPolicyByPage(@RequestBody PolicyMainReqVo vo) {
        PageResult<PolicyMainRespVo> result = policyMainService.queryPolicyByPage(vo);
        return ResponseVo.ok(result);
    }

    /**
     * @description: 逐笔申报减员-分页模糊查询雇员清单信息
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/2/22
     * @return:
     **/
    @ApiOperation (value = "逐笔申报减员-分页模糊查询雇员清单信息")
    @PostMapping (value = "/queryPolicyEmployersListByPage")
    public ResponseVo<PageResult<EmployerslistQueryRespVo>> queryPolicyEmployersListByPage(@RequestBody EmployerslistQueryReqVo vo) {
        PageResult<EmployerslistQueryRespVo> result = policyMainService.queryPolicyEmployersListByPage(vo);
        return ResponseVo.ok(result);
    }

    /**
     * @description: 根据保单号查询逐笔增员的保单信息
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/2/8
     * @return:
     **/
    @ApiOperation (value = "申报页面-查询逐笔增员的保单信息")
    @PostMapping (value = "/selectOneForDeclarationAdd")
    public ResponseVo<PolicyMainRespVo> selectOneForDeclarationAdd(@RequestBody PolicyMainReqVo vo) {
        PolicyMainRespVo result = policyMainService.selectOneForDeclarationAdd(vo);
        return ResponseVo.ok(result);
    }

    /**
     * @description: 根据前端传入的申报信息调用核心批改报价接口
     * @param: vo  flag - 批改方式: 0-逐笔申报 1-批量申报
     * @author: zhoutaoyu
     * @date: 2021/2/8
     * @return:
     **/
    @ApiOperation(value = "申报页面-批改报价")
    @PostMapping(value = "/declarationQuote")
    public ResponseVo<DeclarationQuoteRespVo> declarationQuote(@RequestBody DeclarationQuoteReqVo vo) {
        DeclarationQuoteRespVo respVo = policyCopyMainService.declarationQuote(vo, vo.getFlag());
        return ResponseVo.ok(respVo);
    }

    /**
     * @description: 申报页面-批量申报模板下载
     * @param: vo productcode-产品代码
     * @author: zhoutaoyu
     * @date: 2021/2/8
     * @return:
     **/
    @ApiOperation (value = "申报页面-批量申报模板下载")
    @PostMapping (value = "/downLoadTemplate")
    public ResponseVo<String> downLoadTemplate() {
        String template = policyCopyMainService.downLoadTemplate(PolicyCopyMainService.TEMPLATE_BATCH_IMPORT);
        return ResponseVo.ok(template);
    }

    /**
     * @description: 申报页面-批量申报模板下载
     * @param: vo productcode-产品代码
     * @author: zhoutaoyu
     * @date: 2021/2/8
     * @return:
     **/
    @ApiOperation (value = "申报页面-批量申报模板下载-团意险")
    @PostMapping (value = "/downLoadTemplateTy")
    public ResponseVo<String> downLoadTemplateTy() {
        String template = policyCopyMainService.downLoadTemplate(PolicyCopyMainService.TEMPLATE_BATCH_IMPORT_TY);
        return ResponseVo.ok(template);
    }

    /**
     * 申报批量查询模板下载
     * <AUTHOR>
     * @date 2021年02月07日 17:38:12
     */
    @ApiOperation(value = "申报查询-批量查询模板下载")
    @PostMapping(value = "/downloadBatchQueryDeclarationTemplate")
    public ResponseVo<String> downloadBatchQueryDeclarationTemplate() {
        String template = policyCopyMainService.downLoadTemplate(PolicyCopyMainService.TEMPLATE_BATCH_QUERY);
        return ResponseVo.ok(template);
    }

    /**
     * 申报excel导入批量查询功能
     * @Return
     * <AUTHOR>
     * @date 2021年03月09日 19:27:00
     */
    @ApiOperation(value = "申报查询-批量查询功能")
    @PostMapping(value = "/batchQueryDeclarationTemplate")
    public ResponseVo<PageResult<DeclarationRespVo>> batchQueryDeclarationTemplate(MultipartFile file,String pageNum, String pageSize) throws BusinessException {
        PageResult<DeclarationRespVo> result = policyCopyMainService.batchQueryDeclaration(file,pageNum,pageSize);
        return ResponseVo.ok(result);
    }

    /**
     * 申报excel下载  --修改
     *
     * @Return
     * <AUTHOR>
     * @date 2021年09月22日
     */
    @ApiOperation(value = "申报查询-批量查询功能")
    @PostMapping(value = "/uploadExcelBySearch")
    public ResponseVo<String> uploadExcelBySearch(@RequestBody DeclarationQuoteReqVo vo) throws BusinessException {
        String template = policyCopyMainService.uploadExcelBySearch(vo.getPolicyNo(),vo.getItemNo());
        return ResponseVo.ok(template);
    } 
    @ApiOperation (value = "投保页面-批量投保模板下载-团意险")
    @PostMapping (value = "/downLoadProposalTemplate")
    public ResponseVo<String> downLoadProposalTemplate() {
        String template = policyCopyMainService.downLoadTemplate(PolicyCopyMainService.TEMPLATE_BATCH_IMPORT_INSUREDLIST);
        return ResponseVo.ok(template);
    }
}
