package ins.channel.policycopymain.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import ins.channel.gupolicycopymain.vo.SelectSettlementVo;
import ins.channel.policycopymain.service.ShowSettlementService;
import ins.channel.policycopymain.vo.ShowVo;
import ins.channel.policycopymain.vo.ToolsVo;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/api/showsettlement")
@Api(tags = "ShowSettlement", description = "结算服务")
public class ShowSettlementApi {

    @Resource
    private ShowSettlementService showSettlementService;

    /**
     * 雇主-结算页面查询、根据条件查询、分页
     * @param selectSettlementVo
     * @return
     */
    @ApiOperation(value = "雇主-结算页面查询、根据条件查询、分页")
    @PostMapping(path = "/settlement")
    public ResponseVo<PageResult<SelectSettlementVo>> settlement(@RequestBody SelectSettlementVo selectSettlementVo){
        System.out.println(JSON.toJSONString(selectSettlementVo, SerializerFeature.WriteMapNullValue));
        PageResult<SelectSettlementVo> selectShow = showSettlementService.settlement(selectSettlementVo);
        return ResponseVo.ok(selectShow);
    }

    /**
     * 雇主-结算页面结算
     * @param list
     * @return
     */
    @ApiOperation(value = "雇主结算页面结算")
    @PostMapping(path = "/settlementUpdate")
    public ResponseVo<ShowVo> update(@RequestBody List<SelectSettlementVo> list){
        return showSettlementService.SettlementUpdate(list);
    }

    //接口返回
    @PostMapping (value = "/getSettall")
    public String ShowSettAll(@RequestBody ToolsVo toolsVo){
        String ShowSettlementinterfaceVo = showSettlementService.showSettlementInterface(toolsVo);
        return ShowSettlementinterfaceVo;
    }
}
