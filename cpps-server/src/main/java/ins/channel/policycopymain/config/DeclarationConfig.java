package ins.channel.policycopymain.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * @apiNote 银行流水导入配置类
 * @author: Zhou<PERSON>aoyu
 * @date: 2019-12-19
 */
@Data
@Configuration
@PropertySource(value = "classpath:declarationConfig.properties")
public class DeclarationConfig {
    @Value("${declaration.resource.url}")
    private String declarationResourceUrl;
    @Value("${declaration.HXXTTemplateName}")
    private String HXXTTemplateName;
    @Value("${declaration.HXXTTemplateNameTy}")
    private String HXXTTemplateNameTy;
    @Value("${declaration.UpdateTemplateName}")
    private String UpdateTemplateName;
    @Value("${declaration.UpdateTemplateNameTy}")
    private String UpdateTemplateNameTy;
    @Value("${declaration.batchQueryDeclarationTemplate}")
    private String batchQueryDeclarationTemplate;
    @Value("${declaration.BatchProposalTemplate}")
    private String batchProposalTemplate;
}
