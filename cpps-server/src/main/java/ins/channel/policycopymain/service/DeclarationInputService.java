package ins.channel.policycopymain.service;

import com.alibaba.fastjson.JSON;
import ins.channel.gztemplatesave.dao.GztemplatesaveDao;
import ins.channel.gztemplatesave.po.Gztemplatesave;
import ins.channel.policycopymain.vo.DeclarationQuoteReqVo;
import ins.channel.policycopymain.vo.ExcelTempProposalVo;
import ins.channel.policycopymain.vo.ExcelTempVo;
import ins.channel.policycopymain.vo.InsuredlistTempVo;
import ins.channel.policycopymain.vo.PolicyDynamicListVo;
import ins.channel.support.constant.BusinessNoType;
import ins.channel.support.service.BusinessNoService;
import ins.framework.exception.BusinessException;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.ExportExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 批量申报导入Service
 * @author: ZhouTaoyu
 * @date: 2021/03/01
 */
@Service
@Slf4j
@Transactional
public class DeclarationInputService {
    //雇主责任险批量申报模板初始读取行数索引值
    private static final int HXXT_INIT_ROWNUM = 3;
    @Autowired
    private PolicyCopyMainService policyCopyMainService;
    @Autowired
    private GztemplatesaveDao gztemplatesaveDao;
    @Autowired
    private BusinessNoService businessNoService;

    /**
     * 根据前端传来的excel文件及对应的创新业务标识,解析excel并保存至数据库中
     *
     * @param file 上传的excel文件,需校验合法性及大小
     * @return
     */
    public DeclarationQuoteReqVo resolveExcel(MultipartFile file, DeclarationQuoteReqVo vo) {
        //对上传的文件进行合法性校验
        if (!ExportExcelUtil.checkFileXlsx(file)) {
            throw new BusinessException("文件为空或非法的文件格式!");
        }
        //获取文件名
        String fileName = file.getOriginalFilename();
        Workbook workbook = null;
        try {
            //获取输入流
            InputStream is = file.getInputStream();
            //根据excel版本获取对应poi解析对象
            workbook = ExportExcelUtil.judegExcelEdition(fileName, is);
        } catch (Exception e) {
            throw new BusinessException("文件为空或非法的文件格式!");
        }

        String surverind = vo.getPolicyNo().substring(7, 11);
        String templateType = PolicyCopyMainService.TEMPLATE_BATCH_IMPORT;
        if ("1144".equals(surverind)) {
            templateType = PolicyCopyMainService.TEMPLATE_BATCH_IMPORT_TY;
        }

        //读取excel并保存至List中

//            //传入不同的Vo对象
        List<ExcelTempVo> excelTempVoList = ExportExcelUtil.readExcel(workbook, ExcelTempVo.class, HXXT_INIT_ROWNUM, templateType);
        convert2DynamicList(excelTempVoList, vo);
        String errorMsg = policyCopyMainService.checkDynamicList(vo, "1");
        //校验雇员信息若未通过,则抛出异常
//        Assert.isTrue(!StringUtils.hasText(errorMsg), errorMsg);
        if (StringUtils.hasText(errorMsg)) {
            vo.setCode("500");
            vo.setMsg(errorMsg);
        } else {
            vo.setCode("200");
            vo.setMsg("导入成功");
        }
        return vo;
    }

    /**
     * @description: 将excel导出对象转换为报价参数
     * @param: excelTempVoList
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/3/1
     * @return:
     **/
    private void convert2DynamicList(List<ExcelTempVo> excelTempVoList, DeclarationQuoteReqVo vo) {
        List<PolicyDynamicListVo> policyDynamicListVos = BeanCopyUtils.cloneList(excelTempVoList, PolicyDynamicListVo.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        String nowDate = sdf.format(new Date());
        List<PolicyDynamicListVo> result = policyDynamicListVos.stream().filter((e1) -> excelTempVoList.stream().allMatch((e2) -> {
            if (e1.getFieldAB().equals(e2.getFieldAB()) && e1.getFieldAF().equals(e2.getFieldAF())) {
                if ("I".equals(e1.getEditType())) {
//                    e1.setEntrydate(e2.getEntryOrResignationdate());
                    e1.setEntrydate(nowDate);
                } else if ("D".equals(e2.getEditType())) {
//                    e1.setResignationdate(e2.getEntryOrResignationdate());
                    e1.setResignationdate(nowDate);
                }
                e1.setMonthPay("0");
            }
            return true;
        })).collect(Collectors.toList());
        vo.setDynamicList(result);
    }
    public DeclarationQuoteReqVo resolveProposalExcel(MultipartFile file, DeclarationQuoteReqVo vo) {
        // 对上传的文件进行合法性校验
        if (!ExportExcelUtil.checkFileXlsx(file)) {
            throw new BusinessException("文件为空或非法的文件格式!");
        }
        Workbook workbook;
        try {
            //获取输入流
            InputStream inputStream = file.getInputStream();
            //根据excel版本获取对应 poi 解析对象
            workbook = ExportExcelUtil.judegExcelEdition(file.getOriginalFilename(), inputStream);
        } catch (Exception e) {
            throw new BusinessException("文件为空或非法的文件格式!");
        }
        String templateType = PolicyCopyMainService.TEMPLATE_BATCH_IMPORT_INSUREDLIST;
        // 读取excel并保存至List中 传入不同的Vo对象
        List<ExcelTempProposalVo> excelTempProposalVoList = ExportExcelUtil.readExcel(workbook, ExcelTempProposalVo.class, HXXT_INIT_ROWNUM, templateType);
        // 校验数据
        String errorMsg = policyCopyMainService.checkProposalDynamicList(excelTempProposalVoList);
        if (StringUtils.hasText(errorMsg)) {
            // todo 存日志
            vo.setCode("500");
            vo.setMsg(errorMsg);
            return vo;
        }
        List<InsuredlistTempVo> insuredlistTempVoList = BeanCopyUtils.cloneList(excelTempProposalVoList, InsuredlistTempVo.class);
        for (int i = 0; i < insuredlistTempVoList.size(); i++) {
            InsuredlistTempVo insuredlistTempVo = insuredlistTempVoList.get(i);
            // 处理数据 拆分带 "-" 的字段
            String certificateType = (insuredlistTempVo.getCerti_type().split("-"))[0];
            String relation = (insuredlistTempVo.getRelation().split("-"))[0];
            String ontheJobstatus = (insuredlistTempVo.getOntheJobstatus().split("-"))[0];
            String provinceCode = (insuredlistTempVo.getProvinceCode().split("-"))[0];
            String provinceName = (insuredlistTempVo.getProvinceCode().split("-"))[1];
            String cityCode = (insuredlistTempVo.getCityCode().split("-"))[0];
            String cityName = (insuredlistTempVo.getCityCode().split("-"))[1];
            String countyCode = (insuredlistTempVo.getCountyCode().split("-"))[0];
            String countyName = (insuredlistTempVo.getCountyCode().split("-"))[1];
            // 职业大类代码
            String occupationType = insuredlistTempVo.getOccupationCode().substring(0,4);
            // 覆盖
            insuredlistTempVo.setCerti_type(certificateType);
            insuredlistTempVo.setRelation(relation);
            insuredlistTempVo.setOntheJobstatus(ontheJobstatus);
            insuredlistTempVo.setProvinceCode(provinceCode);
            insuredlistTempVo.setProvinceName(provinceName);
            insuredlistTempVo.setCityCode(cityCode);
            insuredlistTempVo.setCityName(cityName);
            insuredlistTempVo.setCountyCode(countyCode);
            insuredlistTempVo.setCountyName(countyName);
            insuredlistTempVo.setOccupationType(occupationType);
            insuredlistTempVo.setType("1");
            insuredlistTempVo.setPostCode("-");
        }
        // 保存数据
        String uuid = businessNoService.nextNo(BusinessNoType.ID);
        Gztemplatesave gztemplatesave = new Gztemplatesave();
        gztemplatesave.setUuid(uuid);
        gztemplatesave.setTemplatedata(JSON.toJSONString(insuredlistTempVoList));
        gztemplatesave.setCreateDate(new Date());
        gztemplatesaveDao.insert(gztemplatesave);
        //组装成功报文
        // todo 存日志
        vo.setCode("200");
        vo.setMsg("导入成功,共计" + insuredlistTempVoList.size() + "条");
        vo.setSuccessCount(insuredlistTempVoList.size());
        vo.setUuid(uuid);
        return vo;
    }
}
