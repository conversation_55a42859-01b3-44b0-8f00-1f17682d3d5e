package ins.channel.policycopymain.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import ins.channel.base.utils.DateUtils;
import ins.channel.claim.service.CallClaimService;
import ins.channel.claim.vo.CallClaimReqVo;
import ins.channel.code.dao.GgcodeDao;
import ins.channel.code.po.Ggcode;
import ins.channel.config.UrlConfig;
import ins.channel.gumaxno.vo.GumaxnoVo;
import ins.channel.gupolicycoinsurance.dao.GuPolicyCoinsuranceDtoMapper;
import ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDto;
import ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDtoExample;
import ins.channel.gupolicycopyemployerslist.dao.GupolicycopyemployerslistDao;
import ins.channel.gupolicycopyemployerslist.po.Gupolicycopyemployerslist;
import ins.channel.gupolicycopyemployersplan.dao.GupolicycopyemployersplanDao;
import ins.channel.gupolicycopyemployersplan.po.Gupolicycopyemployersplan;
import ins.channel.gupolicycopyitemkind.dao.GupolicycopyitemkindDao;
import ins.channel.gupolicycopyitemkind.po.Gupolicycopyitemkind;
import ins.channel.gupolicycopymain.dao.GupolicycopymainDao;
import ins.channel.gupolicycopymain.po.Gupolicycopymain;
import ins.channel.gupolicycopymain.vo.DeclarationReqVo;
import ins.channel.gupolicycopymain.vo.DeclarationRespVo;
import ins.channel.gupolicyemployerslist.dao.GupolicyemployerslistDao;
import ins.channel.gupolicyemployerslist.po.Gupolicyemployerslist;
import ins.channel.gupolicyemployersplan.dao.GupolicyemployersplanDao;
import ins.channel.gupolicyemployersplan.po.Gupolicyemployersplan;
import ins.channel.gupolicyitemkind.dao.GupolicyitemkindDao;
import ins.channel.gupolicyitemkind.po.Gupolicyitemkind;
import ins.channel.gupolicylog.dao.GupolicylogDao;
import ins.channel.gupolicylog.po.Gupolicylog;
import ins.channel.gupolicymain.dao.GupolicymainDao;
import ins.channel.gupolicymain.po.Gupolicymain;
import ins.channel.gzendorrealated.dao.GzEndorRealatedDtoMapper;
import ins.channel.gzendorrealated.dto.domain.GzEndorRealatedDto;
import ins.channel.gzendorrealated.dto.domain.GzEndorRealatedDtoExample;
import ins.channel.maxno.service.MaxNoService;
import ins.channel.maxno.vo.MaxNoQueryVo;
import ins.channel.policycopymain.common.Base64;
import ins.channel.policycopymain.common.EnctyptUtil;
import ins.channel.policycopymain.config.DeclarationConfig;
import ins.channel.policycopymain.vo.DeclarationQuoteReqVo;
import ins.channel.policycopymain.vo.DeclarationQuoteRespVo;
import ins.channel.policycopymain.vo.ExcelTempProposalVo;
import ins.channel.policycopymain.vo.ExcelTempQueryVo;
import ins.channel.policycopymain.vo.KindMessageVo;
import ins.channel.policycopymain.vo.PolicyDynamicListVo;
import ins.channel.policycopymain.vo.PolicyplanVo;
import ins.channel.policycopymain.vo.mainmessage;
import ins.channel.policylog.service.PolicyLogService;
import ins.channel.support.constant.BusinessNoType;
import ins.channel.support.service.BusinessNoService;
import ins.channel.utils.ApplicantIDCardCheck;
import ins.channel.utils.IDCardCheckUtils;
import ins.framework.exception.BusinessException;
import ins.framework.exception.PermissionException;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.platform.common.PageResult;
import ins.platform.common.SysUser;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.ExportExcelUtil;
import ins.platform.utils.HttpClientUtils;
import ins.platform.utils.PageHelper;
import ins.platform.utils.SessionHelper;
import ins.platform.utils.UserPermitDataHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 保单主信息批改轨迹表Service层
 * @author: ZhouTaoyu
 * @date: 2021/1/26
 **/
@Service
@Slf4j
public class PolicyCopyMainService {
    // 批量申报模板文件地址(相对路径)
    private final static String FILEPATH = "/static/declarationTemplate/";
    //请求类型:批改报价
    private static final String DECLARATION_QUOTE = "50";
    //日志表存储标志
    public static final String ENDORCOMMONINQUA50 = "endorCommonInQua50";

    //请求类型:批改承保风险
    private static final String DECLARATION = "60";
    //日志表存储标志
    public static final String ENDORCOMMONINQUA60 = "endorCommonInQua60";

    //产品代码:雇主责任险
    private static final String HXXT = "HXXT";
    //导出文件模板类型
    public static final String TEMPLATE_BATCH_IMPORT = "batchImport";//批量申报模板下载
    public static final String TEMPLATE_BATCH_IMPORT_TY = "batchImportTy";//批量申报模板下载
    public static final String TEMPLATE_BATCH_QUERY = "batchQuery";//批量查询模板下载
    public static final String TEMPLATE_BATCH_IMPORT_INSUREDLIST = "batchImportInsuredlist";//批量上传被保人模板下载
    //雇主责任险申报批量查询模板初始读取行数索引值
    private static final int HXXT_INIT_ROWNUM = 2;

    @Autowired
    private GupolicymainDao policymainDao;
    @Autowired
    private GupolicycopymainDao policycopymainDao;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private UrlConfig urlConfig;
    @Autowired
    private GgcodeDao ggcodeDao;
    @Autowired
    private GupolicylogDao gupolicylogDao;
    @Autowired
    private GupolicyemployersplanDao gupolicyemployersplanDao;
    @Autowired
    private GupolicycopyemployersplanDao gupolicycopyemployersplanDao;
    @Autowired
    private GupolicyemployerslistDao gupolicyemployerslistDao;
    @Autowired
    private GupolicycopyemployerslistDao gupolicycopyemployerslistDao;
    @Autowired
    private GupolicyitemkindDao gupolicyitemkindDao;
    @Autowired
    private GupolicycopyitemkindDao gupolicycopyitemkindDao;
    @Autowired
    private PolicyLogService policyLogService;
    @Autowired
    private CallClaimService callClaimService;
    @Autowired
    private DeclarationConfig declarationConfig;
    @Autowired
    private BusinessNoService businessNoService;
    @Autowired
    private GuPolicyCoinsuranceDtoMapper guPolicyCoinsuranceDtoMapper;
    @Autowired
    private GzEndorRealatedDtoMapper gzEndorRealatedDtoMapper;
    @Value("${declaration.flag}")
    private String declarationFlag;

    /**
     * @description: 根据传入的申报号, 调用服务平台批改承保风险接口, 进行申报批单生成
     * @param: vo flag 0-批单生成 1-补生成批单
     * @author: zhoutaoyu
     * @date: 2021/2/22
     * @return:
     **/
    @Transactional
    public DeclarationQuoteRespVo declaration(DeclarationQuoteRespVo vo) throws Exception {
        //获取yml文件中配置的接口地址
        String endorCommonInQua = urlConfig.getEndorCommonInQua();
        //根据传入的申报单号获取报价接口返回的json参数
        Gupolicylog policyLogReq = gupolicylogDao.selectByPrimaryKey(vo.getEndorNo());
        Assert.notNull(policyLogReq, "该申报单号未进行批改报价,请确认!");
        Assert.isTrue(!"1".equals(policyLogReq.getResponsestatus()), "该申报单号未申报确认成功,请确认!");
        Assert.isTrue(!"2".equals(policyLogReq.getResponsestatus()), "该申报单号已生成批单,请确认!");
        Assert.isTrue(!"5".equals(policyLogReq.getResponsestatus()), "该申报单号未进行申报确认!");
        Assert.isTrue(!"6".equals(policyLogReq.getResponsestatus()), "该申报单号申报确认失败,请重新进行报价!");
        //批单生成
        if ("0".equals(vo.getFlag())) {
            Assert.isTrue(!"3".equals(policyLogReq.getResponsestatus()), "该申报单号生成批单数据失败,请执行补生成批单操作!");
        } else if ("1".equals(vo.getFlag())) { //补生成批单
            Assert.isTrue(!"0".equals(policyLogReq.getResponsestatus()), "该申报单号未生成批单,请执行申报批单生成操作!");
            Assert.isTrue(!"4".equals(policyLogReq.getResponsestatus()), "该申报单号未成功生成批单,请执行申报批单生成操作!");
        } else {
            throw new BusinessException("未定义的标识!请确认!");
        }
        DeclarationQuoteRespVo reqVo = null;
        String reqJson = null;
        String responseJson = null;
        DeclarationQuoteRespVo respVo = new DeclarationQuoteRespVo();
        Gupolicylog gupolicylog = new Gupolicylog();

        //定义存储日志对象
        SysUser loginUser = SessionHelper.getLoginUser();
        gupolicylog.setOperatorcode(loginUser == null ? "xxl-job" : loginUser.getUserCode());
        policyLogReq.setOperatorcode(loginUser == null ? "xxl-job" : loginUser.getUserCode());
        gupolicylog.setRemark(ENDORCOMMONINQUA60);
        //设置id
        gupolicylog.setId(businessNoService.nextNo(BusinessNoType.ID));
        //若为申报批单生成,则组织数据,请求服务平台接口
        if ("0".equals(vo.getFlag())) {
            Gupolicycopymain policycopymainCondition = new Gupolicycopymain();
            policycopymainCondition.setEndorNo(vo.getEndorNo());
            List<Gupolicycopymain> gupolicycopymainList = policycopymainDao.selectByCondition(policycopymainCondition);
            Assert.notEmpty(gupolicycopymainList, "该申报单号未进行申报确认,请确认!");
            String responsecontent = policyLogReq.getResponsecontent();
            JSONObject parse = JSON.parseObject(responsecontent);
            reqVo = JSONObject.toJavaObject(parse, DeclarationQuoteRespVo.class);
            //设置渠道id
            reqVo.setChannel_id(HXXT);
            //设置时间戳
            reqVo.setTimestamp(DateUtils.format(new Date(), DateUtils.PATTERN_DATE_TIME));
            //设置交易类型 （默认：60：批改）
            reqVo.setRequesttype(DECLARATION);
            //组织入参Json
            reqJson = JSON.toJSONString(reqVo);
            //定义返参对象
            respVo = new DeclarationQuoteRespVo();
            //调用服务平台接口查询待同步保单号清单
            try {
                responseJson = HttpClientUtils.postJson(endorCommonInQua, reqJson).getBodyAsString();
            } catch (Exception e) {
                log.info(MessageFormat.format("申报单号:{0},调用批改承保风险接口失败!原因:{1}", vo.getEndorNo(), e.getMessage()));
                //保存异常日志
                gupolicylog.setRequestcontent(reqJson);
                gupolicylog.setResponsecontent(MessageFormat.format("申报单号:{0},调用批改承保风险接口失败!原因:{1}", vo.getEndorNo(), e.getMessage()));
                gupolicylog.setResponsestatus("4");
                gupolicylog.setRequesetdate(new Date());
                gupolicylogDao.insertSelective(gupolicylog);
                //更新异常日志
                policyLogReq.setResponsestatus("4");
                gupolicylogDao.updateSelectiveByPrimaryKey(policyLogReq);
                respVo.setCode("500");
                respVo.setMsg("调用批改承保风险接口失败,网络异常,请稍后重试或联系管理员处理!");
                return respVo;
            }
            if (!StringUtils.hasText(responseJson)) {
                //保存异常日志
                gupolicylog.setRequestcontent(reqJson);
                gupolicylog.setResponsecontent(MessageFormat.format("申报单号:{0},调用批改承保风险接口失败!响应为空", vo.getEndorNo()));
                gupolicylog.setResponsestatus("4");
                gupolicylog.setRequesetdate(new Date());
                gupolicylogDao.insertSelective(gupolicylog);
                //更新异常日志
                policyLogReq.setResponsestatus("4");
                gupolicylogDao.updateSelectiveByPrimaryKey(policyLogReq);
                respVo.setCode("500");
                respVo.setMsg("调用批改承保风险接口失败!响应为空,请联系管理员处理!");
                return respVo;
            }
            JSONObject respJsonObj = JSONObject.parseObject(responseJson);
            String code = (String) respJsonObj.get("code");
            //若不为响应状态码不为"200"则查询失败
            if (!"200".equals(code)) {
                //获取异常信息
                String msg = (String) respJsonObj.get("msg");
                log.info(MessageFormat.format("申报单号:{0},调用批改承保风险接口失败!原因:{1}", vo.getEndorNo(), msg));
                //保存异常日志
                gupolicylog.setRequestcontent(reqJson);
                gupolicylog.setResponsecontent(MessageFormat.format("申报单号:{0},调用批改承保风险接口失败!原因:{1}", vo.getEndorNo(), msg));
                gupolicylog.setResponsestatus("4");
                gupolicylog.setRequesetdate(new Date());
                gupolicylogDao.insertSelective(gupolicylog);
                //更新异常日志
                policyLogReq.setResponsestatus("4");
                gupolicylogDao.updateSelectiveByPrimaryKey(policyLogReq);
                respVo.setCode("500");
                respVo.setMsg(MessageFormat.format("调用批改承保风险接口失败!原因:{1}", vo.getEndorNo(), msg));
                return respVo;
            }
            //成功
            parse = JSON.parseObject(responseJson);
            DeclarationQuoteRespVo resq = JSONObject.toJavaObject(parse, DeclarationQuoteRespVo.class);
            //获取核心批改序号
            reqVo.setEndorseqno(resq.getEndorseqno());
        } else {
            //若为补生成批单,则直接取日志表对应请求与响应数据回写轨迹表即可
            String requestcontent = policyLogReq.getRequestcontent();
            reqJson = requestcontent;
            JSONObject parse = JSON.parseObject(requestcontent);
            reqVo = JSONObject.toJavaObject(parse, DeclarationQuoteRespVo.class);
            responseJson = policyLogReq.getResponsecontent();
        }
        //回写金额与状态
        updateTrack(reqVo, responseJson, gupolicylog, policyLogReq);
        //更新原日志表数据状态
        policyLogReq.setRequestcontent(reqJson);
        policyLogReq.setResponsecontent(responseJson);
        policyLogReq.setResponsestatus("2");
        gupolicylogDao.updateSelectiveByPrimaryKey(policyLogReq);
        respVo.setCode("200");
        respVo.setMsg(MessageFormat.format("申报批单生成成功!申报单号:{0}", reqVo.getEndorNo()));
        return respVo;
    }

    public DeclarationQuoteRespVo batchDeclaration(DeclarationQuoteRespVo vo) throws Exception {
        List<GzEndorRealatedDto> gzEndorRealatedDtoList = getGzEndorRealatedDtoList(vo.getEndorNo());
        if (CollectionUtils.isEmpty(gzEndorRealatedDtoList)) {
            GzEndorRealatedDto gzEndorRealatedDtoTemp = new GzEndorRealatedDto();
            gzEndorRealatedDtoTemp.setPolicyno(vo.getPolicyNo());
            gzEndorRealatedDtoTemp.setEndorno(vo.getEndorNo());
            gzEndorRealatedDtoList.add(gzEndorRealatedDtoTemp);
        }
        List<String> endorNoList = gzEndorRealatedDtoList.stream().map(GzEndorRealatedDto::getEndorno).collect(Collectors.toList());
        //定义返参对象
        DeclarationQuoteRespVo declarationQuoteRespVo = new DeclarationQuoteRespVo();
        StringBuilder errorMsg = new StringBuilder();
        StringBuilder successMsg = new StringBuilder();
        StringBuilder message = new StringBuilder();
        int successCount = 0;
        int failCount = 0;
        for (String endorNo : endorNoList) {
            try {
                vo.setEndorNo(endorNo);
                DeclarationQuoteRespVo respVo = declaration(vo);
                if ("500".equals(respVo.getCode())) {
                    errorMsg.append(MessageFormat.format("申报单号:{0} 生成批单失败!原因: {1}\n", endorNo, respVo.getMsg()));
                    failCount++;
                } else if ("200".equals(respVo.getCode())) {
                    successMsg.append(respVo.getMsg()).append("\n");
                    successCount++;
                }
            } catch (Exception e) {
                errorMsg.append(MessageFormat.format("申报单号:{0} 生成批单失败!原因: {1}\n", endorNo, e.getMessage()));
                failCount++;
            }
        }

        if (successCount > 0){
            declarationQuoteRespVo.setCode("200");
            message.append(successMsg);
        }

        if (failCount > 0){
            declarationQuoteRespVo.setCode("500");
            message.append(errorMsg);
        }
        declarationQuoteRespVo.setMsg(String.valueOf(message));
        return declarationQuoteRespVo;
    }

    /**
     * @description: 根据传入的保单号与申报单号更新最新保单表与轨迹表的金额/申报状态
     * @param: reqUpdateVo
     * @author: zhoutaoyu
     * @date: 2021/2/23
     * @return:
     **/
    @Transactional(propagation = Propagation.NESTED)
    public void updateTrack(DeclarationQuoteRespVo reqUpdateVo, String responseJson, Gupolicylog gupolicylog, Gupolicylog policyLogReq) throws Exception {
        //保单号
        String policyNo = reqUpdateVo.getPolicyNo();
        //申报单号
        String endorNo = reqUpdateVo.getEndorNo();
        Exception exception = null;
        try {
            //处理保单主信息表
            //根据保单号与申报单号查询轨迹表数据
            Gupolicycopymain policycopymainCondition = new Gupolicycopymain();
            policycopymainCondition.setPolicyNo(policyNo);
            policycopymainCondition.setEndorNo(endorNo);
            List<Gupolicycopymain> gupolicycopymainList = policycopymainDao.selectByCondition(policycopymainCondition);
            Gupolicycopymain gupolicycopymain = gupolicycopymainList.get(0);
            //根据保单号查最新表数据
            Gupolicymain policymainCondition = new Gupolicymain();
            policymainCondition.setPolicyNo(policyNo);
            List<Gupolicymain> gupolicymainList = policymainDao.selectByCondition(policymainCondition);
            Gupolicymain gupolicymain = gupolicymainList.get(0);
            //更新主表的金额
            // 旧总保额
            BigDecimal oldsuminsured = gupolicymain.getSuminsured();
            //总保额变化量
            BigDecimal changeinsured = gupolicycopymain.getChangeinsured();
            gupolicymain.setSuminsured(oldsuminsured.add(changeinsured));
            gupolicymain.setSuminsuredcny(oldsuminsured.add(changeinsured));
            gupolicycopymain.setSuminsured(oldsuminsured.add(changeinsured));
            gupolicycopymain.setSuminsuredcny(oldsuminsured.add(changeinsured));
            // 旧总毛保费
            BigDecimal oldsumgrosspremium = gupolicymain.getSumgrosspremium();
            //总保费变化量
            BigDecimal changegrosspremium = gupolicycopymain.getChangegrosspremium();
            gupolicymain.setSumgrosspremium(oldsumgrosspremium.add(changegrosspremium));
            gupolicymain.setSumgrosspremiumcny(oldsumgrosspremium.add(changegrosspremium));
            gupolicycopymain.setSumgrosspremium(oldsumgrosspremium.add(changegrosspremium));
            gupolicycopymain.setSumgrosspremiumcny(oldsumgrosspremium.add(changegrosspremium));
            //旧总净保费
            BigDecimal oldsumnetpremium = gupolicymain.getSumnetpremium();
            //总净保费变化量
            BigDecimal changenetpremium = gupolicycopymain.getChangenetpremium();
            gupolicymain.setSumnetpremium(oldsumnetpremium.add(changenetpremium));
            gupolicycopymain.setSumnetpremium(oldsumnetpremium.add(changenetpremium));
            //旧总承保保费
            BigDecimal oldsumuwpremium = gupolicymain.getSumuwpremium();
            //总承保保费变化量
            BigDecimal changeuwpremium = gupolicycopymain.getChangeuwpremium();
            gupolicymain.setSumuwpremium(oldsumuwpremium.add(changeuwpremium));
            gupolicymain.setSumuwpremiumcny(oldsumuwpremium.add(changeuwpremium));
            gupolicycopymain.setSumuwpremium(oldsumuwpremium.add(changeuwpremium));
            gupolicycopymain.setSumuwpremiumcny(oldsumuwpremium.add(changeuwpremium));
            //批改次数 轨迹表批改序号
            BigDecimal oldendorseTimes = gupolicymain.getEndorseTimes();
            BigDecimal endorseTimes = oldendorseTimes == null ? BigDecimal.ZERO : oldendorseTimes.add(BigDecimal.ONE);
            gupolicymain.setEndorseTimes(endorseTimes);
            gupolicycopymain.setEndorseTimes(endorseTimes);
            //设置批改序号(核心)
            gupolicycopymain.setEndorseqno(reqUpdateVo.getEndorseqno());
            //设置核批通过标识
            gupolicycopymain.setUnderwriteind("1");
            //设置核批通过时间
            gupolicycopymain.setUnderWriteEndDate(new Date());
            //设置备注
            gupolicymain.setRemark(gupolicycopymain.getRemark());
            //设置修改时间
            gupolicymain.setUpdatesysdate(new Date());
            gupolicycopymain.setUpdatesysdate(new Date());

            //更新计划表金额
            //查询最新计划表数据
            Gupolicyemployersplan employersplanCondition = new Gupolicyemployersplan();
            employersplanCondition.setPolicyNo(policyNo);
            employersplanCondition.setTargetcalculate("1");
            List<Gupolicyemployersplan> gupolicyemployersplans = gupolicyemployersplanDao.selectByCondition(employersplanCondition);
            Gupolicyemployersplan gupolicyemployersplan = gupolicyemployersplans.get(0);
            //查询计划轨迹表数据
            Gupolicycopyemployersplan copyemployersplanCondition = new Gupolicycopyemployersplan();
            copyemployersplanCondition.setPolicyNo(policyNo);
            copyemployersplanCondition.setEndorNo(endorNo);
            copyemployersplanCondition.setTargetcalculate("1");
            Gupolicycopyemployersplan gupolicycopyemployersplan = gupolicycopyemployersplanDao.selectByCondition(copyemployersplanCondition);
            //旧投保雇员人数
            Integer oldemployees = Double.valueOf(gupolicyemployersplan.getEmployees()).intValue();
            //本次变更人数
            Integer employeesthischange = Double.valueOf(gupolicycopyemployersplan.getEmployeesthischange()).intValue();
            gupolicyemployersplan.setEmployeesthischange(String.valueOf(employeesthischange));
            //投保雇员人数
            gupolicycopyemployersplan.setEmployees(String.valueOf((oldemployees + employeesthischange)));
            gupolicyemployersplan.setEmployees(String.valueOf((oldemployees + employeesthischange)));
            //旧伤亡医疗合计费用
            BigDecimal oldpersonsumpremium = gupolicyemployersplan.getPersonsumpremium();
            //伤亡医疗合计费用变化量
            BigDecimal changepersonsumpremium = gupolicycopyemployersplan.getChangepersonsumpremium();
            gupolicyemployersplan.setPersonsumpremium(oldpersonsumpremium.add(changepersonsumpremium));
            gupolicycopyemployersplan.setPersonsumpremium(oldpersonsumpremium.add(changepersonsumpremium));
            //设置更新时间
            gupolicyemployersplan.setUpdatesysdate(new Date());
            gupolicycopyemployersplan.setUpdatesysdate(new Date());

            //更新险别表金额
            //查询最新险别表数据
            //查询险别轨迹表
            Gupolicycopyitemkind copyitemkindCondition = new Gupolicycopyitemkind();
            copyitemkindCondition.setPolicyNo(policyNo);
            copyitemkindCondition.setEndorNo(endorNo);
            List<Gupolicycopyitemkind> gupolicycopyitemkinds = gupolicycopyitemkindDao.selectByCondition(copyitemkindCondition);
            if ("1219".equals(gupolicycopyitemkinds.get(0).getRiskCode())) {
                Gupolicyitemkind itemkindCondition = new Gupolicyitemkind();
                itemkindCondition.setPolicyNo(policyNo);
                itemkindCondition.setCalculateind("1");
                List<Gupolicyitemkind> gupolicyitemkinds = gupolicyitemkindDao.selectByCondition(itemkindCondition);
                Gupolicyitemkind gupolicyitemkind = gupolicyitemkinds.get(0);

                Gupolicycopyitemkind gupolicycopyitemkind = gupolicycopyitemkinds.get(0);
                // 旧总保额
                BigDecimal oldkindsuminsured = gupolicyitemkind.getSuminsured();
                //险别的总保额变化量
                BigDecimal kindchangeinsured = gupolicycopyitemkind.getChangeinsured();
                gupolicyitemkind.setSuminsured(oldkindsuminsured.add(kindchangeinsured));
                gupolicyitemkind.setSuminsuredcny(oldkindsuminsured.add(kindchangeinsured));
                gupolicycopyitemkind.setSuminsured(oldkindsuminsured.add(kindchangeinsured));
                gupolicycopyitemkind.setSuminsuredcny(oldkindsuminsured.add(kindchangeinsured));
                //旧险别的毛保费
                BigDecimal oldgrosspremium = gupolicyitemkind.getGrosspremium();
                //险别的毛保费变化量
                BigDecimal kindchangegrosspremium = gupolicycopyitemkind.getChangegrosspremium();
                gupolicyitemkind.setGrosspremium(oldgrosspremium.add(kindchangegrosspremium));
                gupolicycopyitemkind.setGrosspremium(oldgrosspremium.add(kindchangegrosspremium));
                //旧险别净保费
                BigDecimal oldnetPremium = gupolicyitemkind.getNetPremium();
                //险别净保费变化量
                BigDecimal kindchangenetpremium = gupolicycopyitemkind.getChangenetpremium();
                gupolicyitemkind.setNetPremium(oldnetPremium.add(kindchangenetpremium));
                gupolicycopyitemkind.setNetPremium(oldnetPremium.add(kindchangenetpremium));
                //旧险别承保保费
                BigDecimal olduwpremium = gupolicyitemkind.getUwpremium();
                //险别承保保费变化量
                BigDecimal kindchangeuwpremium = gupolicycopyitemkind.getChangeuwpremium();
                gupolicyitemkind.setUwpremium(olduwpremium.add(kindchangeuwpremium));
                gupolicyitemkind.setUwpremiumcny(olduwpremium.add(kindchangeuwpremium));
                gupolicycopyitemkind.setUwpremium(olduwpremium.add(kindchangeuwpremium));
                gupolicycopyitemkind.setUwpremiumcny(olduwpremium.add(kindchangeuwpremium));
                //旧险别年保费
                BigDecimal oldyearpremium = gupolicyitemkind.getYearpremium();
                //旧险别年保费 + 险别的毛保费变化量
                gupolicyitemkind.setYearpremium(oldyearpremium.add(kindchangegrosspremium));
                gupolicycopyitemkind.setYearpremium(oldyearpremium.add(kindchangegrosspremium));
                //设置修改日期
                gupolicyitemkind.setUpdatesysdate(new Date());
                gupolicycopyitemkind.setUpdatesysdate(new Date());
                //更新险别表数据
                gupolicyitemkindDao.updateSelectiveByPrimaryKey(gupolicyitemkind);
                gupolicycopyitemkindDao.updateSelectiveByPrimaryKey(gupolicycopyitemkind);
            }else if ("1144".equals(gupolicycopyitemkinds.get(0).getRiskCode())){
                Gupolicyitemkind itemkindCondition = new Gupolicyitemkind();
                itemkindCondition.setPolicyNo(policyNo);
                List<Gupolicyitemkind> gupolicyitemkinds = gupolicyitemkindDao.selectByCondition(itemkindCondition);
                Gupolicyitemkind gupolicyitemkind = null;
                //查询险别轨迹表
                Gupolicycopyitemkind gupolicycopyitemkind = null;
                for (int i =0,isize = gupolicycopyitemkinds.size();i<isize;i++){
                    gupolicycopyitemkind = gupolicycopyitemkinds.get(i);
                    for (int j =0,jsize = gupolicyitemkinds.size();j<jsize;j++){
                        gupolicyitemkind = gupolicyitemkinds.get(j);
                        if (gupolicycopyitemkind.getRiskCode().equals(gupolicyitemkind.getRiskCode())){
                            // 旧总保额
                            BigDecimal oldkindsuminsured = gupolicyitemkind.getSuminsured();
                            //险别的总保额变化量
                            BigDecimal kindchangeinsured = gupolicycopyitemkind.getChangeinsured();
                            gupolicyitemkind.setSuminsured(oldkindsuminsured.add(kindchangeinsured));
                            gupolicyitemkind.setSuminsuredcny(oldkindsuminsured.add(kindchangeinsured));
                            gupolicycopyitemkind.setSuminsured(oldkindsuminsured.add(kindchangeinsured));
                            gupolicycopyitemkind.setSuminsuredcny(oldkindsuminsured.add(kindchangeinsured));
                            //旧险别的毛保费
                            BigDecimal oldgrosspremium = gupolicyitemkind.getGrosspremium();
                            //险别的毛保费变化量
                            BigDecimal kindchangegrosspremium = gupolicycopyitemkind.getChangegrosspremium();
                            gupolicyitemkind.setGrosspremium(oldgrosspremium.add(kindchangegrosspremium));
                            gupolicycopyitemkind.setGrosspremium(oldgrosspremium.add(kindchangegrosspremium));
                            //旧险别净保费
                            BigDecimal oldnetPremium = gupolicyitemkind.getNetPremium();
                            //险别净保费变化量
                            BigDecimal kindchangenetpremium = gupolicycopyitemkind.getChangenetpremium();
                            gupolicyitemkind.setNetPremium(oldnetPremium.add(kindchangenetpremium));
                            gupolicycopyitemkind.setNetPremium(oldnetPremium.add(kindchangenetpremium));
                            //旧险别承保保费
                            BigDecimal olduwpremium = gupolicyitemkind.getUwpremium();
                            //险别承保保费变化量
                            BigDecimal kindchangeuwpremium = gupolicycopyitemkind.getChangeuwpremium();
                            gupolicyitemkind.setUwpremium(olduwpremium.add(kindchangeuwpremium));
                            gupolicyitemkind.setUwpremiumcny(olduwpremium.add(kindchangeuwpremium));
                            gupolicycopyitemkind.setUwpremium(olduwpremium.add(kindchangeuwpremium));
                            gupolicycopyitemkind.setUwpremiumcny(olduwpremium.add(kindchangeuwpremium));

                            //设置修改日期
                            gupolicyitemkind.setUpdatesysdate(new Date());
                            gupolicycopyitemkind.setUpdatesysdate(new Date());
                            //更新险别表数据
                            gupolicyitemkindDao.updateSelectiveByPrimaryKey(gupolicyitemkind);
                            gupolicycopyitemkindDao.updateSelectiveByPrimaryKey(gupolicycopyitemkind);
                            break;
                        }
                    }
                }
            }
            //更新雇员清单表
            //根据保单号与标识查询最新雇员清单表
//            Gupolicyemployerslist employerslistCondition = new Gupolicyemployerslist();
//            employerslistCondition.setPolicyNo(policyNo);
//            employerslistCondition.setTargetflag("I");
            List<Gupolicyemployerslist> policyemployersAddlists = new ArrayList<>();
            List<Gupolicyemployerslist> policyemployersUpdatelists = new ArrayList<>();
            List<Gupolicyemployerslist> policyemployersUpdateElists = new ArrayList<>();
            //根据保单号/批单号与标识 查询雇员清单轨迹表新增数据
            Gupolicycopyemployerslist copyemployerslistCondition = new Gupolicycopyemployerslist();
            copyemployerslistCondition.setPolicyNo(policyNo);
            copyemployerslistCondition.setEndorNo(endorNo);
            copyemployerslistCondition.setTargetflag("I");
            List<Gupolicycopyemployerslist> copyemployersAddlists = gupolicycopyemployerslistDao.selectByCondition(copyemployerslistCondition);
            if (!CollectionUtils.isEmpty(copyemployersAddlists)) {
                for (Gupolicycopyemployerslist copyemployersAddlist : copyemployersAddlists) {
                    copyemployersAddlist.setUpdatesysdate(new Date());
                }
                //获取最新雇员清单表待插入数据
                policyemployersAddlists = BeanCopyUtils.cloneList(copyemployersAddlists, Gupolicyemployerslist.class);
                for (Gupolicyemployerslist policyemployersAddlist : policyemployersAddlists) {
                    policyemployersAddlist.setId(businessNoService.nextNo(BusinessNoType.ID));
                }
            }
            //根据保单号/批单号与标识 查询雇员清单轨迹表更新数据
            copyemployerslistCondition.setTargetflag("U");
            List<Gupolicycopyemployerslist> copyemployersUpdatelists = gupolicycopyemployerslistDao.selectByCondition(copyemployerslistCondition);
            if (!CollectionUtils.isEmpty(copyemployersUpdatelists)) {
                for (Gupolicycopyemployerslist copyemployersUpdatelist : copyemployersUpdatelists) {
                    copyemployersUpdatelist.setUpdatesysdate(new Date());
                    //获取最新雇员清单表待更新数据
                    Gupolicyemployerslist employerslistCondition = new Gupolicyemployerslist();
                    //根据姓名/雇员证件号/保单号查询对应计划表数据
                    employerslistCondition.setPolicyNo(copyemployersUpdatelist.getPolicyNo());
                    //雇员证件号
                    employerslistCondition.setEmpidentifynumber(copyemployersUpdatelist.getEmpidentifynumber());
                    //标的标识
                    employerslistCondition.setTargetflag("D");
                    List<Gupolicyemployerslist> policyemployerslists = gupolicyemployerslistDao.searchByPolicyNo(employerslistCondition);
                    Gupolicyemployerslist gupolicyemployerslist = policyemployerslists.get(0);
                    Gupolicycopyemployerslist clone = BeanCopyUtils.clone(copyemployersUpdatelist, Gupolicycopyemployerslist.class);
                    //不更新主键/入机时间
                    clone.setId(null);
                    clone.setInputDate(null);
                    //设置主表待更新标的标识为 D-删除
                    clone.setTargetflag("D");
                    BeanCopyUtils.copyNotNull(clone, gupolicyemployerslist);
                    policyemployersUpdatelists.add(gupolicyemployerslist);
                }
                for (Gupolicyemployerslist policyemployersUpdatelist : policyemployersUpdatelists) {
                    //不更新最新雇员清单表入机时间
                    policyemployersUpdatelist.setInputDate(null);
                    //设置最新雇员清单表标的标志为D-删除
                    policyemployersUpdatelist.setTargetflag("D");
                }
            }
            copyemployerslistCondition.setTargetflag("E");
            List<Gupolicycopyemployerslist> copyemployersEList = gupolicycopyemployerslistDao.selectByCondition(copyemployerslistCondition);
            if (!CollectionUtils.isEmpty(copyemployersEList)) {
                for (Gupolicycopyemployerslist copyemployersUpdatelist : copyemployersEList) {
                    copyemployersUpdatelist.setUpdatesysdate(new Date());
                    //获取最新雇员清单表待更新数据
                    Gupolicyemployerslist employerslistCondition = new Gupolicyemployerslist();
                    //根据姓名/雇员证件号/保单号查询对应计划表数据
                    employerslistCondition.setPolicyNo(copyemployersUpdatelist.getPolicyNo());
                    //雇员证件号
                    employerslistCondition.setEmpidentifynumber(copyemployersUpdatelist.getEmpidentifynumber());
                    //标的标识
                    employerslistCondition.setTargetflag("D");
                    List<Gupolicyemployerslist> policyemployerslists = gupolicyemployerslistDao.searchByPolicyNo(employerslistCondition);
                    Gupolicyemployerslist gupolicyemployerslist = policyemployerslists.get(0);
                    Gupolicycopyemployerslist clone = BeanCopyUtils.clone(copyemployersUpdatelist, Gupolicycopyemployerslist.class);
                    //设置主表待更新标的标识为 E-修改
                    clone.setId(null);
                    clone.setInputDate(null);
                    clone.setTargetflag("E");
                    BeanCopyUtils.copyNotNull(clone, gupolicyemployerslist);
                    policyemployersUpdateElists.add(gupolicyemployerslist);
                }
            }
            //合并待更新雇员清单表数据集合
            if (!CollectionUtils.isEmpty(copyemployersUpdatelists) || !CollectionUtils.isEmpty(copyemployersAddlists)) {
                copyemployersUpdatelists.addAll(copyemployersAddlists);
            }
            //更新主表数据
            policymainDao.updateSelectiveByPrimaryKey(gupolicymain);
            policycopymainDao.updateSelectiveByPrimaryKey(gupolicycopymain);
            //更新计划表数据
            gupolicyemployersplanDao.updateSelectiveByPrimaryKey(gupolicyemployersplan);
            gupolicycopyemployersplanDao.updateSelectiveByPrimaryKey(gupolicycopyemployersplan);

            //批量插入雇员清单表新增数据
            if (!CollectionUtils.isEmpty(policyemployersAddlists)) {
                gupolicyemployerslistDao.batchInsert(policyemployersAddlists);
            }
            //更新雇员清单表数据
            if (!CollectionUtils.isEmpty(copyemployersUpdatelists)) {
                for (Gupolicyemployerslist policyemployersUpdatelist : policyemployersUpdatelists) {
                    gupolicyemployerslistDao.updateSelectiveByPrimaryKey(policyemployersUpdatelist);
                }
                for (Gupolicycopyemployerslist copyemployersUpdatelist : copyemployersUpdatelists) {
                    gupolicycopyemployerslistDao.updateSelectiveByPrimaryKey(copyemployersUpdatelist);
                }
            }

            //更新雇员清单表数据
            if (!CollectionUtils.isEmpty(copyemployersEList)) {
                for (Gupolicyemployerslist policyemployersUpdatelist : policyemployersUpdateElists) {
                    gupolicyemployerslistDao.updateSelectiveByPrimaryKey(policyemployersUpdatelist);
                }
            }
        } catch (Exception e) {
            log.info(MessageFormat.format("回写轨迹表数据失败! 申报单号:{0} 异常原因: {1}", endorNo, e.getMessage()));
            exception = e;
            throw new BusinessException(MessageFormat.format("申报单号:{0}, 批改承保风险回写金额失败!请联系运维人员处理,异常原因:{1}", endorNo, e.getMessage()));
        } finally {
            if (exception != null) {
                //保存异常日志
                gupolicylog.setRequesetdate(new Date());
                policyLogReq.setRequestcontent(JSON.toJSONString(reqUpdateVo));
                gupolicylog.setResponsecontent(MessageFormat.format("回写轨迹表数据失败! 申报单号:{0} 异常原因: {1}", endorNo, exception.getMessage()));
                //生成核心批单成功但回写金额失败的数据
                gupolicylog.setResponsestatus("3");
                policyLogService.saveLog(gupolicylog);
                //更新原日志表数据状态
                policyLogReq.setRequesetdate(new Date());
                policyLogReq.setRequestcontent(JSON.toJSONString(reqUpdateVo));
                policyLogReq.setResponsecontent(responseJson);
                policyLogReq.setResponsestatus("3");
                policyLogService.updateLog(policyLogReq);
            }
        }
    }

    @Transactional
    public DeclarationQuoteRespVo saveTrack(DeclarationQuoteRespVo vo, String flag) {
        List<GzEndorRealatedDto> gzEndorRealatedDtosList = getGzEndorRealatedDtoList(vo.getEndorNo());
        if (CollectionUtils.isEmpty(gzEndorRealatedDtosList)) {
            GzEndorRealatedDto gzEndorRealatedDtoTemp = new GzEndorRealatedDto();
            gzEndorRealatedDtoTemp.setPolicyno(vo.getPolicyNo());
            gzEndorRealatedDtoTemp.setEndorno(vo.getEndorNo());
            gzEndorRealatedDtosList.add(gzEndorRealatedDtoTemp);
        }
        for (GzEndorRealatedDto gzEndorRealatedDto : gzEndorRealatedDtosList) {
            Gupolicylog policyLogReq = gupolicylogDao.selectByPrimaryKey(gzEndorRealatedDto.getEndorno());
            //vo信息预处理 !!!!
            vo.setPolicyNo(gzEndorRealatedDto.getPolicyno());
            vo.setEndorNo(gzEndorRealatedDto.getEndorno());

            Exception exception = null;
            //查询最新版本号表获取该批单最新批改序号
            GumaxnoVo gumaxnoVo = new GumaxnoVo();
            gumaxnoVo.setPolicyNo(vo.getPolicyNo());
            gumaxnoVo.setFlag("ENDORSE");
            //处理日志表中返参申报日期
            //根据传入的申报单号获取报价接口返回的json参数
            Assert.notNull(policyLogReq, "该申报单号未进行批改报价,请确认!");
            Assert.isTrue(!"0".equals(policyLogReq.getResponsestatus()), "该申报单号已申报确认,请勿重复操作!");
            Assert.isTrue(!"1".equals(policyLogReq.getResponsestatus()), "该申报单号批改报价失败,请重新进行报价!");
            Assert.isTrue(!"2".equals(policyLogReq.getResponsestatus()), "该申报单号已生成批单,请确认!");
            Assert.isTrue(!"3".equals(policyLogReq.getResponsestatus()), "该申报单号生成批单数据失败,请执行补生成批单操作!");
            Assert.isTrue(!"4".equals(policyLogReq.getResponsestatus()), "该申报单号申报生成批单失败,请确认!");
            Assert.isTrue(!"6".equals(policyLogReq.getResponsestatus()), "该申报单号申报确认失败,请重新进行报价!");
            String responsecontent = policyLogReq.getResponsecontent();
            JSONObject parse = JSON.parseObject(responsecontent);
            DeclarationQuoteRespVo respVo = JSONObject.toJavaObject(parse, DeclarationQuoteRespVo.class);
            //设置批单申报日期为当前时间
            respVo.setAcceptdate(DateUtils.format(new Date(), DateUtils.PATTERN_DATE_TIME));
            String respJson = JSON.toJSONString(respVo);
            //设置日志表操作人员代码
            policyLogReq.setOperatorcode(SessionHelper.getLoginUser().getUserCode());
            try {
                //校验雇员信息
                DeclarationQuoteReqVo checkVo = BeanCopyUtils.clone(vo, DeclarationQuoteReqVo.class);
                String errorMsg = checkDynamicList(checkVo, flag);
                //校验雇员信息若未通过,则抛出异常
                Assert.isTrue(!(errorMsg.length() > 0 && StringUtils.hasText(errorMsg)), errorMsg);
                List<PolicyDynamicListVo> dynamicList = vo.getDynamicList();
                PolicyDynamicListVo policyDynamicListVo = dynamicList.get(0);
                Date now = new Date();
                Gupolicymain policymainCondition = new Gupolicymain();
                policymainCondition.setPolicyNo(gzEndorRealatedDto.getPolicyno());
                List<Gupolicymain> gupolicymains = policymainDao.selectByCondition(policymainCondition);
                //根据保单号/有效状态 查询保单主信息轨迹表数据
                Gupolicycopymain gupolicycopymain = new Gupolicycopymain();
                BeanCopyUtils.copy(gupolicymains.get(0), gupolicycopymain);
                //组织存储Gupolicycopymain 表数据
                //设置id
                gupolicycopymain.setId(businessNoService.nextNo(BusinessNoType.ID));
                //设置申报日期
                gupolicycopymain.setAcceptdate(now);
                //设置生效日期
                gupolicycopymain.setValidDate(DateUtils.parseDate(vo.getValidDate()));
                //设置批改申请号
                gupolicycopymain.setEndorNo(gzEndorRealatedDto.getEndorno());
                //设置修改时间
                gupolicycopymain.setUpdatesysdate(now);
                //设置核保标识 0-未通过 1-核批完成
                gupolicycopymain.setUnderwriteind("0");
                //设置核保通过日期为空
                gupolicycopymain.setUnderWriteEndDate(null);
                //设置CHANGEINSURED 总保额变化量
                mainmessage mainmessage = vo.getMainmessage();
                gupolicycopymain.setChangeinsured(new BigDecimal(mainmessage.getChangeamount()));
                gupolicycopymain.setChangeinsuredcny(new BigDecimal(mainmessage.getChangeamount()));
                //设置CHANGEGROSSPREMIUM 总毛保费变化量
                gupolicycopymain.setChangegrosspremium(new BigDecimal(mainmessage.getChangepremium()));
                gupolicycopymain.setChangegrosspremiumcny(new BigDecimal(mainmessage.getChangepremium()));
                //设置  CHANGENETPREMIUM 总净保费变化量
                gupolicycopymain.setChangenetpremium(new BigDecimal(mainmessage.getChangepremium()));
                //设置 CHANGEUWPREMIUM 总承保保费变化量
                gupolicycopymain.setChangeuwpremium(new BigDecimal(mainmessage.getChangepremium()));
                gupolicycopymain.setChangeuwpremiumcny(new BigDecimal(mainmessage.getChangepremium()));
                //设置 备注
                gupolicycopymain.setRemark(policyDynamicListVo.getRemark());
                //设置 操作人PM代码与名称
                gupolicycopymain.setProjectmanagercode(SessionHelper.getLoginUser().getUserCode());
                gupolicycopymain.setProjectmanagername(SessionHelper.getLoginUser().getUserCname());
                //操作员信息
                gupolicycopymain.setLastModifyManagerCode(SessionHelper.getLoginUser().getUserCode());
                gupolicycopymain.setLastModifyManagerName(SessionHelper.getLoginUser().getUserCname());
                //结算状态 0-未结算 1-已生成结算单 2-已结算  默认为0
                gupolicycopymain.setSettleStatus("0");
                //设置入机与修改日期
                gupolicycopymain.setInputDate(now);
                gupolicycopymain.setUpdatesysdate(now);

                //根据传入的雇员变更信息集合处理雇员清单表数据
                //定义本次变更人数变量
                Integer employeesthischange = 0;

                for (PolicyDynamicListVo dynamicListVo : dynamicList) {
                    //组织 GUPOLICYCOPYEMPLOYERSLIST 表数据
                    Gupolicycopyemployerslist gupolicycopyemployerslist = new Gupolicycopyemployerslist();
                    Gupolicyemployerslist employerslistCondition = new Gupolicyemployerslist();
                    //根据姓名/雇员证件号/保单号查询对应计划表数据
                    employerslistCondition.setPolicyNo(gzEndorRealatedDto.getPolicyno());
                    //雇员证件号
                    employerslistCondition.setEmpidentifynumber(dynamicListVo.getFieldAF());
                    //标的标识
                    employerslistCondition.setTargetflag("D");
                    List<Gupolicyemployerslist> policyemployerslists = gupolicyemployerslistDao.searchByPolicyNo(employerslistCondition);
                    //新增
                    if ("I".equals(dynamicListVo.getEditType())) {
                        SysUser sysUser = SessionHelper.getLoginUser();
                        //获取页面新增的雇员信息
                        //计划编码
                        gupolicycopyemployerslist.setPlanid(dynamicListVo.getFieldAA());
                        //雇员姓名
                        gupolicycopyemployerslist.setEmpname(dynamicListVo.getFieldAB());
                        //雇员性别
                        gupolicycopyemployerslist.setEmpsex(dynamicListVo.getFieldAC());
                        //雇员生日
                        Date birthday = DateUtils.parse(dynamicListVo.getFieldAD(), DateUtils.PATTERN_DATE);
                        gupolicycopyemployerslist.setEmpbirthday(DateUtils.formatDate(birthday));
                        //雇员证件类型
                        gupolicycopyemployerslist.setEmpidentifytype(dynamicListVo.getFieldAE());
                        //雇员证件号
                        gupolicycopyemployerslist.setEmpidentifynumber(dynamicListVo.getFieldAF());
                        //职业代码
                        gupolicycopyemployerslist.setOccupationCode(dynamicListVo.getFieldAG());
                        //职业名称
                        gupolicycopyemployerslist.setOccupationname(dynamicListVo.getFieldAH());
                        //职业等级
                        gupolicycopyemployerslist.setOccupationlevel(dynamicListVo.getFieldAI());
                        //约定月薪
//                    gupolicycopyemployerslist.setMonthPay(new BigDecimal(dynamicListVo.getMonthPay()));
                        //项目
                        gupolicycopyemployerslist.setProject(dynamicListVo.getProject());
                        //标的序号
                        gupolicycopyemployerslist.setItemNo(new BigDecimal(vo.getPlanList().get(0).getPlan()));
                        //currency 从主表中获取
                        gupolicycopyemployerslist.setCurrency(gupolicycopymain.getCurrency());
                        //entrydate 入职日期
                        gupolicycopyemployerslist.setEntrydate(DateUtils.parse(dynamicListVo.getEntrydate(), DateUtils.PATTERN_DATE));
                        //resignationdate 离职日期 新增时不存
                        //effectivedate 生效日期  取批改生效日期
                        gupolicycopyemployerslist.setEffectivedate(DateUtils.parse(vo.getValidDate(), DateUtils.PATTERN_DATE));
                        //enddate 终保日期
                        gupolicycopyemployerslist.setEndDate(gupolicycopymain.getEndDate());
                        //dynamictargettype  默认存"5"
                        gupolicycopyemployerslist.setDynamictargettype("5");
                        //listbelongind  默认0
                        gupolicycopyemployerslist.setListbelongind("0");
                        //每人毛保费
                        gupolicycopyemployerslist.setEmppremium(new BigDecimal(dynamicListVo.getChangeperpremium()));
                        //每人毛保费变化量
                        gupolicycopyemployerslist.setChangeemppremium(new BigDecimal(dynamicListVo.getChangeperpremium()));
                        //批改标志
                        gupolicycopyemployerslist.setTargetflag(dynamicListVo.getEditType());
                        //雇员序号
                        gupolicycopyemployerslist.setListseqno(new BigDecimal(dynamicListVo.getListseqno()));
                        //详细地址
                        gupolicycopyemployerslist.setHomeaddress(dynamicListVo.getHomeaddress());
                        //电话
                        gupolicycopyemployerslist.setHometel(dynamicListVo.getHometel());
                        //设置批改申请号
                        gupolicycopyemployerslist.setEndorNo(gzEndorRealatedDto.getEndorno());
                        //设置保单号
                        gupolicycopyemployerslist.setPolicyNo(gzEndorRealatedDto.getPolicyno());
                        //设置部门为登陆用户部门
                        gupolicycopyemployerslist.setDepartment(sysUser.getDepartment());
                        //设置部门为登陆用户部门
                        dynamicListVo.setDepartment(sysUser.getDepartment());
                        gupolicycopyemployerslist.setProvincecode(dynamicListVo.getProvinceCode());
                        gupolicycopyemployerslist.setCitycode(dynamicListVo.getCityCode());
                        gupolicycopyemployerslist.setCountycode(dynamicListVo.getCityCode());
                        //设置入机日期与修改日期
                        gupolicycopyemployerslist.setInputDate(now);
                        gupolicycopyemployerslist.setUpdatesysdate(now);
                        //生成ID
                        gupolicycopyemployerslist.setId(businessNoService.nextNo(BusinessNoType.ID));
                        employeesthischange++;
                    } else if ("D".equals(dynamicListVo.getEditType())) {
                        //根据姓名、证件号码、保单号、批改标志 查询copy表记录
                        Gupolicycopyemployerslist copyemployerslistCondition = new Gupolicycopyemployerslist();
                        //雇员证件号
                        copyemployerslistCondition.setEmpidentifynumber(dynamicListVo.getFieldAF());
                        //保单号
                        copyemployerslistCondition.setPolicyNo(gzEndorRealatedDto.getPolicyno());
                        //批改标志
                        copyemployerslistCondition.setTargetflag("D");
                        List<Gupolicycopyemployerslist> oldPolicycopyemployerslists = gupolicycopyemployerslistDao.searchBypolicyNo(copyemployerslistCondition);
                        //TODO 临时修正 批减时取批文数据
                        Gupolicycopyemployerslist oldPolicycopyemployerslist = oldPolicycopyemployerslists.get(0);
                        for (Gupolicycopyemployerslist oldPolicycopyemployers : oldPolicycopyemployerslists) {
                            if (!"0".equals(oldPolicycopyemployers.getChangeemppremium())) {
                                oldPolicycopyemployerslist = oldPolicycopyemployers;
                                break;
                            }
                        }
                        //将轨迹表中的原数据批改标志置为 D-删除
                        oldPolicycopyemployerslist.setTargetflag("D");
                        oldPolicycopyemployerslist.setUpdatesysdate(now);
                        gupolicycopyemployerslistDao.updateSelectiveByPrimaryKey(oldPolicycopyemployerslist);
                        //插入新的批改轨迹数据
                        //批改标志置为U-更新
                        BeanCopyUtils.copy(policyemployerslists.get(0), gupolicycopyemployerslist);
                        gupolicycopyemployerslist.setTargetflag("U");
                        //离职日期
                        gupolicycopyemployerslist.setResignationdate(DateUtils.parse(dynamicListVo.getResignationdate(), DateUtils.PATTERN_DATE));
                        //终保日期  存批改生效日期
                        gupolicycopyemployerslist.setEndDate(DateUtils.parse(vo.getValidDate(), DateUtils.PATTERN_DATE));
                        //旧每人毛保费
                        BigDecimal oldemppremium = policyemployerslists.get(0).getEmppremium();
                        //每人毛保费变化量
                        BigDecimal changeperpremium = new BigDecimal(dynamicListVo.getChangeperpremium());
                        gupolicycopyemployerslist.setChangeemppremium(changeperpremium);
                        gupolicycopyemployerslist.setEmppremium(oldemppremium.add(changeperpremium));
                        //设置批改申请号
                        gupolicycopyemployerslist.setEndorNo(gzEndorRealatedDto.getEndorno());
                        //设置入机日期与修改日期
                        gupolicycopyemployerslist.setInputDate(now);
                        gupolicycopyemployerslist.setUpdatesysdate(now);
                        //生成ID
                        gupolicycopyemployerslist.setId(businessNoService.nextNo(BusinessNoType.ID));
                        employeesthischange--;
                    } else if ("U".equals(dynamicListVo.getEditType())) {
                        //根据姓名、证件号码、保单号、批改标志 查询copy表记录
                        Gupolicycopyemployerslist copyemployerslistCondition = new Gupolicycopyemployerslist();
                        //雇员证件号
                        copyemployerslistCondition.setEmpidentifynumber(dynamicListVo.getFieldAF());
                        //保单号
                        copyemployerslistCondition.setPolicyNo(gzEndorRealatedDto.getPolicyno());
                        //批改标志
                        copyemployerslistCondition.setTargetflag("D");
                        List<Gupolicycopyemployerslist> oldPolicycopyemployerslists = gupolicycopyemployerslistDao.searchBypolicyNo(copyemployerslistCondition);
                        Gupolicycopyemployerslist oldPolicycopyemployerslist = oldPolicycopyemployerslists.get(0);
                        //将轨迹表中的原数据批改标志置为 D-删除
                        //TODO 不为 D 改为 E ，导致 I 变为 E
                        oldPolicycopyemployerslist.setTargetflag("E");
                        oldPolicycopyemployerslist.setUpdatesysdate(now);
                        gupolicycopyemployerslistDao.updateSelectiveByPrimaryKey(oldPolicycopyemployerslist);
                        //插入新的批改轨迹数据
                        //批改标志置为U-更新
                        BeanCopyUtils.copy(oldPolicycopyemployerslist, gupolicycopyemployerslist);
                        //currency 从主表中获取
                        gupolicycopyemployerslist.setCurrency(gupolicycopymain.getCurrency());
                        //dynamictargettype  默认存"5"
                        gupolicycopyemployerslist.setDynamictargettype("5");
                        //listbelongind  默认0
                        gupolicycopyemployerslist.setListbelongind("0");
                        gupolicycopyemployerslist.setEmpname(dynamicListVo.getFieldAB());
                        gupolicycopyemployerslist.setEmpsex(dynamicListVo.getFieldAC());
                        gupolicycopyemployerslist.setEmpbirthday(dynamicListVo.getFieldAD());
                        gupolicycopyemployerslist.setEmpidentifytype(dynamicListVo.getFieldAE());
                        gupolicycopyemployerslist.setEmpidentifynumber(dynamicListVo.getFieldAF());
                        gupolicycopyemployerslist.setOccupationCode(dynamicListVo.getFieldAG());
                        gupolicycopyemployerslist.setOccupationname(dynamicListVo.getFieldAH());
                        gupolicycopyemployerslist.setOccupationlevel(dynamicListVo.getFieldAI());

                        //详细地址
                        gupolicycopyemployerslist.setHomeaddress(dynamicListVo.getHomeaddress());
                        //电话
                        gupolicycopyemployerslist.setHometel(dynamicListVo.getHometel());
                        gupolicycopyemployerslist.setProvincecode(dynamicListVo.getProvinceCode());
                        gupolicycopyemployerslist.setCitycode(dynamicListVo.getCityCode());
                        gupolicycopyemployerslist.setCountycode(dynamicListVo.getCityCode());
//                    gupolicycopyemployerslist.setMonthPay(new BigDecimal(dynamicListVo.getMonthPay()));
                        gupolicycopyemployerslist.setProject(dynamicListVo.getProject());
                        gupolicycopyemployerslist.setTargetflag("E");
                        gupolicycopyemployerslist.setUpdatesysdate(null);
                        //旧每人毛保费
                        BigDecimal oldemppremium = policyemployerslists.get(0).getEmppremium();
                        //每人毛保费变化量
                        BigDecimal changeperpremium = new BigDecimal(dynamicListVo.getChangeperpremium());
                        gupolicycopyemployerslist.setChangeemppremium(changeperpremium);
                        gupolicycopyemployerslist.setEmppremium(oldemppremium.add(changeperpremium));
                        //设置批改申请号
                        gupolicycopyemployerslist.setEndorNo(gzEndorRealatedDto.getEndorno());
                        //设置入机日期与修改日期
                        gupolicycopyemployerslist.setInputDate(now);
                        gupolicycopyemployerslist.setUpdatesysdate(now);
                        //生成ID
                        gupolicycopyemployerslist.setId(businessNoService.nextNo(BusinessNoType.ID));
                    }
                    gupolicycopyemployerslistDao.insertSelective(gupolicycopyemployerslist);
                }

                //组织 GUPOLICYCOPYEMPLOYERSPLAN 表数据
                //根据保单号与标的计算标识(0-不计算 1-计算)查询最新计划表对应数据
                Gupolicyemployersplan employersplanCondition = new Gupolicyemployersplan();
                employersplanCondition.setPolicyNo(gzEndorRealatedDto.getPolicyno());
                employersplanCondition.setTargetcalculate("1");
                employersplanCondition.setItemNo(new BigDecimal(vo.getPlanList().get(0).getPlan()));
                List<Gupolicyemployersplan> gupolicyemployersplans = gupolicyemployersplanDao.selectByCondition(employersplanCondition);
                Assert.notEmpty(gupolicyemployersplans, "未找到对应保单计划信息!");
                //获取伤亡责任限额不为空的最新计划数据
//            Gupolicyemployersplan gupolicyemployersplan = gupolicyemployersplans.stream().filter(plan -> plan.getPersoncasualtieslimit() != null).findFirst().orElse(null);
                Gupolicyemployersplan gupolicyemployersplan = gupolicyemployersplans.get(0);
                Assert.notNull(gupolicyemployersplan, "未找到对应保单计划(伤亡责任)信息!");
                //根据最新计划表数据生成轨迹表数据
                Gupolicycopyemployersplan gupolicycopyemployersplan = BeanCopyUtils.clone(gupolicyemployersplan, Gupolicycopyemployersplan.class);
                //设置id
                gupolicycopyemployersplan.setId(businessNoService.nextNo(BusinessNoType.ID));
                //设置批改申请号
                gupolicycopyemployersplan.setEndorNo(gzEndorRealatedDto.getEndorno());
                //设置计划编码  TODO  保持与policyplan表一致
//            gupolicycopyemployersplan.setPlanid(policyDynamicListVo.getFieldAA());
                //设置伤亡医疗合计费用变化量
                PolicyplanVo policyplanVo = vo.getPlanList().get(0);
                gupolicycopyemployersplan.setChangepersonsumpremium(new BigDecimal(policyplanVo.getPlanchangepremium()));
                //本次变更投保雇员人数
                gupolicycopyemployersplan.setEmployeesthischange(String.valueOf(employeesthischange));
                //旧投保雇员人数
                Integer oldemployees = Double.valueOf(gupolicyemployersplan.getEmployees()).intValue();
                //投保雇员人数
                gupolicycopyemployersplan.setEmployees(String.valueOf((oldemployees + employeesthischange)));
                //设置修改日期
                gupolicycopyemployersplan.setUpdatesysdate(now);
                //设置入机日期
                gupolicycopyemployersplan.setInputDate(now);

                //组织 GUPOLICYITEMKIND 表数据
                //获取报价接口返参中险别保费变化量不为空的参数
                List<KindMessageVo> kindmessageList = vo.getKindmessageList();
                KindMessageVo kindMessage = kindmessageList.stream().filter(kindMessageVo -> kindMessageVo.getKindchangepremium() != null).findFirst().orElse(null);
                Gupolicycopyitemkind gupolicycopyitemkind = new Gupolicycopyitemkind();
                Gupolicyitemkind itemkindCondition = new Gupolicyitemkind();
                itemkindCondition.setPolicyNo(gzEndorRealatedDto.getPolicyno());
                itemkindCondition.setCalculateind("1");
                List<Gupolicyitemkind> gupolicyitemkinds = gupolicyitemkindDao.selectByCondition(itemkindCondition);
                if (kindMessage != null && "1219".equals(gupolicyitemkinds.get(0).getRiskCode())) {
                    Assert.notEmpty(gupolicyitemkinds, "未找到对应保单险别信息!");
                    //插入保单险别轨迹表数据
                    BeanCopyUtils.copy(gupolicyitemkinds.get(0), gupolicycopyitemkind);
                    //设置批改申请号
                    gupolicycopyitemkind.setEndorNo(gzEndorRealatedDto.getEndorno());
                    //设置计划编码
                    gupolicycopyitemkind.setPlanid(policyDynamicListVo.getFieldAA());
                    //设置险别的毛保费变化量
                    gupolicycopyitemkind.setChangegrosspremium(new BigDecimal(kindMessage.getKindchangepremium()));
                    //设置险别净保费变化量
                    gupolicycopyitemkind.setChangenetpremium(new BigDecimal(kindMessage.getKindchangepremium()));
                    //设置险别承保保费变化量
                    gupolicycopyitemkind.setChangeuwpremium(new BigDecimal(kindMessage.getKindchangepremium()));
                    gupolicycopyitemkind.setChangeuwpremiumcny(new BigDecimal(kindMessage.getKindchangepremium()));
                    //设置险别的总保额变化量
                    gupolicycopyitemkind.setChangeinsured(new BigDecimal(kindMessage.getKindchangeamount()));
                    gupolicycopyitemkind.setChangeinsuredcny(new BigDecimal(kindMessage.getKindchangeamount()));
                    //设置修改日期
                    gupolicycopyitemkind.setUpdatesysdate(now);
                    //设置入机日期
                    gupolicycopyitemkind.setInputDate(now);
                    //设置id
                    gupolicycopyitemkind.setId(businessNoService.nextNo(BusinessNoType.ID));
                    gupolicycopyitemkindDao.insertSelective(gupolicycopyitemkind);
                }else if(kindMessage != null && "1144".equals(gupolicyitemkinds.get(0).getRiskCode())){
                    itemkindCondition = new Gupolicyitemkind();
                    itemkindCondition.setPolicyNo(gzEndorRealatedDto.getPolicyno());
                    gupolicyitemkinds = gupolicyitemkindDao.selectByCondition(itemkindCondition);
                    Assert.notEmpty(gupolicyitemkinds, "未找到对应保单险别信息!");
                    //插入保单险别轨迹表数据
                    for (int i=0,isize=kindmessageList.size();i<isize;i++){
                        kindMessage = kindmessageList.get(i);
                        for (int j=0,jsize = gupolicyitemkinds.size();j<jsize;j++){
                            BeanCopyUtils.copy(gupolicyitemkinds.get(j), gupolicycopyitemkind);
                            if (kindMessage.getKindcode().equals(gupolicycopyitemkind.getKindCode())){
                                //设置批改申请号
                                gupolicycopyitemkind.setEndorNo(gzEndorRealatedDto.getEndorno());
                                //设置计划编码
                                gupolicycopyitemkind.setPlanid(policyDynamicListVo.getFieldAA());
                                //设置险别的毛保费变化量
                                gupolicycopyitemkind.setChangegrosspremium(new BigDecimal(kindMessage.getKindchangepremium()));
                                //设置险别净保费变化量
                                gupolicycopyitemkind.setChangenetpremium(new BigDecimal(kindMessage.getKindchangepremium()));
                                //设置险别承保保费变化量
                                gupolicycopyitemkind.setChangeuwpremium(new BigDecimal(kindMessage.getKindchangepremium()));
                                gupolicycopyitemkind.setChangeuwpremiumcny(new BigDecimal(kindMessage.getKindchangepremium()));
                                //设置险别的总保额变化量
                                gupolicycopyitemkind.setChangeinsured(new BigDecimal(kindMessage.getKindchangeamount()));
                                gupolicycopyitemkind.setChangeinsuredcny(new BigDecimal(kindMessage.getKindchangeamount()));
                                //设置修改日期
                                gupolicycopyitemkind.setUpdatesysdate(now);
                                //设置入机日期
                                gupolicycopyitemkind.setInputDate(now);
                                //设置id
                                gupolicycopyitemkind.setId(businessNoService.nextNo(BusinessNoType.ID));
                                gupolicycopyitemkindDao.insertSelective(gupolicycopyitemkind);
                                break;
                            }
                        }
                    }
                }

                MaxNoQueryVo maxNoQueryVo = maxNoService.getVersionNo(gumaxnoVo);
                String endorserialno = null;
                if ("200".equals(maxNoQueryVo.getCode())) {
                    endorserialno = maxNoQueryVo.getResult();
                } else {
                    log.error(MessageFormat.format("获取批改序号失败! :{0}", maxNoQueryVo.getResult()));
                    throw new BusinessException("获取批改序号失败!网络连接异常!请稍后再试");
                }
                //批改序号
                gupolicycopymain.setEndorserialno(new BigDecimal(endorserialno));
                policycopymainDao.insertSelective(gupolicycopymain);
                gupolicycopyemployersplanDao.insertSelective(gupolicycopyemployersplan);

                //更新成功日志
                policyLogReq.setResponsecontent(respJson);
                policyLogReq.setResponsestatus("0");
                gupolicylogDao.updateSelectiveByPrimaryKey(policyLogReq);
                //释放锁
                maxNoService.releaseKey(gumaxnoVo);
            } catch (Exception e) {
                log.info(MessageFormat.format("申报确认失败!异常原因: {0}", e.getMessage()));
                exception = e;
                throw new BusinessException(MessageFormat.format("申报确认失败!异常原因: {0}", e.getMessage()));
            } finally {
                if (exception != null) {
                    //更新异常日志
                    policyLogReq.setResponsecontent(MessageFormat.format("申报确认失败!异常原因: {0}", exception.getMessage()));
                    policyLogReq.setOperatorcode(SessionHelper.getLoginUser().getUserCode());
                    policyLogReq.setRequesetdate(new Date());
                    policyLogReq.setResponsestatus("6");
                    policyLogService.updateLog(policyLogReq);
                }
            }
        }
        //只返回最后一次主保单的信息
        DeclarationQuoteRespVo resp = new DeclarationQuoteRespVo();
        resp.setEndorNo(vo.getEndorNo());
        resp.setPolicyNo(vo.getPolicyNo());
        //申报确认成功后, 直接生成批单
        if ("1".equals(declarationFlag)) {
            //flag 0-批单生成 1-补生成批单
            resp.setFlag("0");
            try {
                DeclarationQuoteRespVo declarationQuoteRespVo = batchDeclaration(resp);
                resp.setMsg(declarationQuoteRespVo.getMsg());
                log.info("msg================================="+declarationQuoteRespVo.getMsg() );
            } catch (Exception e) {
                e.printStackTrace();
                throw new BusinessException(MessageFormat.format("生成批单失败!异常原因: {0}", e.getMessage()));
            }
        }
        return resp;
    }


    //获取同一批次的批单号
    private List<GzEndorRealatedDto> getGzEndorRealatedDtoList(String endorNo) {
        List<GzEndorRealatedDto> gzEndorRealatedDtoList = new ArrayList<>();
        GzEndorRealatedDtoExample gzEndorRealatedDtoExample = new GzEndorRealatedDtoExample();
        gzEndorRealatedDtoExample.createCriteria().andEndornoEqualTo(endorNo);
        List<GzEndorRealatedDto> gzEndorRealatedDtos = gzEndorRealatedDtoMapper.selectByExample(gzEndorRealatedDtoExample);
        if (CollectionUtils.isEmpty(gzEndorRealatedDtos)) {
            return gzEndorRealatedDtoList;
        }
        String uuid = gzEndorRealatedDtos.get(0).getUuid();
        GzEndorRealatedDtoExample gzEndorRealatedDtoExample2 = new GzEndorRealatedDtoExample();
        gzEndorRealatedDtoExample2.createCriteria().andUuidEqualTo(uuid);
        gzEndorRealatedDtoList = gzEndorRealatedDtoMapper.selectByExample(gzEndorRealatedDtoExample2);
        return gzEndorRealatedDtoList;
    }

    /**
     * 申报查询-分页模糊查询申报信息
     *
     * @param vo 查询条件载体
     * @return
     * @Return
     * <AUTHOR>
     * @date 2021年02月05日 10:43:27
     */
    public PageResult<DeclarationRespVo> pageQueryDeclarationByCondition(DeclarationReqVo vo) {
        //获取业务员代码并设置查询条件
        SysUser loginUser = SessionHelper.getLoginUser();
        String userInd = loginUser.getUserInd();
        if ("3".equals(userInd)) {//判断是否为业务员TODO 设置用户职级常量.
            vo.setProjectManagerCode(loginUser.getUserCode());
        }
        if (null != loginUser.getDepartment()){
            vo.setDepartment(loginUser.getDepartment());
        }
        String errorMsg = DateUtils.checkDateStartEnd(vo.getEffectiveDateStart(), vo.getEffectiveDateEnd(), "生效日期");
        if (!errorMsg.isEmpty()) {
            throw new BusinessException(errorMsg);
        }
        errorMsg = DateUtils.checkDateStartEnd(vo.getEndDateStart(), vo.getEndDateEnd(), "到期日期");
        if (!errorMsg.isEmpty()) {
            throw new BusinessException(errorMsg);
        }
        errorMsg = DateUtils.checkDateStartEnd(vo.getAcceptDateStart(), vo.getAcceptDateEnd(), "申报日期");
        if (!errorMsg.isEmpty()) {
            throw new BusinessException(errorMsg);
        }
        //排除SQL注入   待提取共用
        if (vo.getOrderColumn() != null) {
            vo.setOrderColumn(vo.getOrderColumn().replaceAll("'", "")
                    .replaceAll("&#39;", "")
                    .replaceAll("--", "")
                    .replaceAll("&", "")
                    .replaceAll("/*", "")
                    .replaceAll(";", "")
                    .replaceAll("%", ""));
        }
        if (vo.getOrderType() != null &&
                !"asc".equals(vo.getOrderType().toLowerCase(Locale.ROOT)) &&
                !"desc".equals(vo.getOrderType().toLowerCase(Locale.ROOT))) {
            //只能为asc或者desc
            throw new BusinessException("升序降序字段只能为asc或者desc!");
        }

//         数据权限校验：关联机构
        Set<String> permitComs = UserPermitDataHelper.getUserPermitCom();
        if (StringUtils.hasText(vo.getCompanyCode())) { // 如果查询参数中指定了机构，直接做权限校验
            if (!permitComs.contains(vo.getCompanyCode())) {
                throw new PermissionException("当前登录用户没有该机构的操作权限,请确认!");
            }
        } else {
            vo.setCompanyCodes(permitComs);
        }
        PageParam pageParam = PageHelper.getPageParam(vo);
        //TODO 查询有待处理字段
        Page<DeclarationRespVo> results = policycopymainDao.pageQueryDeclarationByCondition(pageParam, vo);
        for (DeclarationRespVo declarationRespVo : results) {
            if(declarationRespVo.getLastModifyManagerName() == null ) {
                declarationRespVo.setLastModifyManagerName("原始保单");
            }

            declarationRespVo.setProjectManagerName(declarationRespVo.getLastModifyManagerName());

            if(declarationRespVo.getChangeemppremium() == null ) {
                declarationRespVo.setChangeemppremium(declarationRespVo.getEmpPremium());
            }
            if("0".equals(declarationRespVo.getChangeemppremium().toString())
                    && "E".equals(declarationRespVo.getTargetFlag())){
                declarationRespVo.setEmpPremium(declarationRespVo.getChangeemppremium());
            }
        }
        return PageHelper.convert(pageParam, results, DeclarationRespVo.class);
    }

    /**
     * 申报查询-根据条件查询结果集导出
     *
     * @param vo 查询条件载体
     * @return
     * @Return
     * <AUTHOR>
     * @date 2021年03月30日
     */
    public List<DeclarationRespVo> selectByAllDeclarationRespVo(DeclarationReqVo vo) {
        //获取业务员代码并设置查询条件
        SysUser loginUser = SessionHelper.getLoginUser();
        String userInd = loginUser.getUserInd();
        if ("3".equals(userInd)) {//判断是否为业务员TODO 设置用户职级常量.
            vo.setProjectManagerCode(loginUser.getUserCode());
        }
        String errorMsg = DateUtils.checkDateStartEnd(vo.getEffectiveDateStart(), vo.getEffectiveDateEnd(), "生效日期");
        if (!errorMsg.isEmpty()) {
            throw new BusinessException(errorMsg);
        }
        errorMsg = DateUtils.checkDateStartEnd(vo.getEndDateStart(), vo.getEndDateEnd(), "到期日期");
        if (!errorMsg.isEmpty()) {
            throw new BusinessException(errorMsg);
        }
        errorMsg = DateUtils.checkDateStartEnd(vo.getAcceptDateStart(), vo.getAcceptDateEnd(), "申报日期");
        if (!errorMsg.isEmpty()) {
            throw new BusinessException(errorMsg);
        }
        //排除SQL注入   待提取共用
        if (vo.getOrderColumn() != null) {
            vo.setOrderColumn(vo.getOrderColumn().replaceAll("'", "")
                    .replaceAll("&#39;", "")
                    .replaceAll("--", "")
                    .replaceAll("&", "")
                    .replaceAll("/*", "")
                    .replaceAll(";", "")
                    .replaceAll("%", ""));
        }

        //数据权限校验：关联机构
        Set<String> permitComs = UserPermitDataHelper.getUserPermitCom();
        if (StringUtils.hasText(vo.getCompanyCode())) { // 如果查询参数中指定了机构，直接做权限校验
            if (!permitComs.contains(vo.getCompanyCode())) {
                throw new PermissionException("当前登录用户没有该机构的操作权限,请确认!");
            }
        } else {
            vo.setCompanyCodes(permitComs);
        }
        List<DeclarationRespVo> declarationRespVoList = policycopymainDao.selectByAllDeclarationRespVo(vo);
        for (DeclarationRespVo declarationRespVo : declarationRespVoList) {
            if(declarationRespVo.getLastModifyManagerName() == null ) {
                declarationRespVo.setLastModifyManagerName("原始保单");
            }

            declarationRespVo.setProjectManagerName(declarationRespVo.getLastModifyManagerName());

            if(declarationRespVo.getChangeemppremium() == null ) {
                declarationRespVo.setChangeemppremium(declarationRespVo.getEmpPremium());
            }

            if("0".equals(declarationRespVo.getChangeemppremium().toString())
                    && "更新".equals(declarationRespVo.getTargetFlag())){
                declarationRespVo.setEmpPremium(declarationRespVo.getChangeemppremium());
            }
        }
        return declarationRespVoList;
    }


    /**
     * @description: 根据前端传入的申报信息调用核心批改报价接口
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/2/9
     * @return:
     **/
    @Transactional
    public DeclarationQuoteRespVo declarationQuote(DeclarationQuoteReqVo vo, String flag) {
        DeclarationQuoteRespVo respVo = null;
        String uuid = businessNoService.nextNo(BusinessNoType.ID);
        List<String> relatedCoinsPolicynoList  = getRelatedCoinsPolicynoList(vo.getPolicyNo());
        relatedCoinsPolicynoList.add(relatedCoinsPolicynoList.size(), vo.getPolicyNo());
        if (!CollectionUtils.isEmpty(relatedCoinsPolicynoList)) {
            for (String policyno : relatedCoinsPolicynoList) {
                //赋值
                vo.setPolicyNo(policyno);
                List<PolicyDynamicListVo> dynamicList = vo.getDynamicList();
                //校验雇员信息
                String errorMsg = checkDynamicList(vo, flag);
                //校验雇员信息若未通过,则抛出异常
                Assert.isTrue(!(errorMsg.length() > 0 && StringUtils.hasText(errorMsg)), errorMsg);
                Calendar calendar = Calendar.getInstance();
                // 批改生效日期 (若该保单起保日期大于等于当前日期,则为起保日期+1天,否则为申报日期+1天)
                Gupolicymain policymainConditon = new Gupolicymain();
                policymainConditon.setPolicyNo(vo.getPolicyNo());
                List<Gupolicymain> gupolicymainList = policymainDao.selectByCondition(policymainConditon);
                //根据传入保单号查询对应最新保单数据,获取起保日期
                Gupolicymain gupolicymain = gupolicymainList.get(0);
                Date startDate = gupolicymain.getStartDate();
                //判断起保日期是否大于等于当前日期
                Date temp = startDate.after(new Date()) ? startDate : new Date();
                calendar.setTime(temp);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.add(Calendar.DATE, 1);
                Date validDate = calendar.getTime();
                //根据保单号与计算标识查询最新保单计划表
                Gupolicyemployersplan policyemployersplanCondition = new Gupolicyemployersplan();
                policyemployersplanCondition.setPolicyNo(vo.getPolicyNo());
                policyemployersplanCondition.setTargetcalculate("1");
                policyemployersplanCondition.setItemNo(new BigDecimal(vo.getItemNo()));
                List<Gupolicyemployersplan> gupolicyemployersplans = gupolicyemployersplanDao.selectByCondition(policyemployersplanCondition);
                Assert.notEmpty(gupolicyemployersplans, "批改报价失败!未找到对应保单计划表信息");
                Gupolicyemployersplan gupolicyemployersplan = gupolicyemployersplans.get(0);
                //获取标的序号
                BigDecimal itemNo = gupolicyemployersplan.getItemNo();
                //定义本次申报变更人数
                Integer changepercount = 0;
                //定义本次申报增员人数
                Integer addpercount = 0;
                //定义本次申报减员人数
                Integer subpercount = 0;
                for (PolicyDynamicListVo policyDynamicListVo : dynamicList) {
                    //TODO 保险计划待处理
                    //设置计划编码
                    policyDynamicListVo.setFieldAA(String.valueOf(itemNo.intValue()));
                    if (!"1144".equals(gupolicymainList.get(0).getProductcode())) {
                        //逐笔时处理职业代码
                        if ("0".equals(flag)) {
                            String fieldAG = policyDynamicListVo.getFieldAG();
                            Ggcode ggcode = ggcodeDao.queryByCode(fieldAG);
                            //获取职业名称
                            String codeCname = ggcode.getCodeCname();
                            //获取职业等级
                            String occupationlevel = ggcode.getRemark();
                            policyDynamicListVo.setFieldAH(codeCname);
                            policyDynamicListVo.setFieldAI(occupationlevel);
                        }
                    }
                    if ("D".equals(policyDynamicListVo.getEditType())) {
                        //设置离职日期
                        calendar.setTime(DateUtils.parse(policyDynamicListVo.getResignationdate(), DateUtils.PATTERN_DATE));
                        calendar.set(Calendar.HOUR_OF_DAY, 23);
                        calendar.set(Calendar.MINUTE, 59);
                        calendar.set(Calendar.SECOND, 59);
                        Date resignationdate = calendar.getTime();
                        policyDynamicListVo.setResignationdate(DateUtils.format(resignationdate, DateUtils.PATTERN_DATE_TIME));
                        //总人数-1
                        changepercount--;
                        //减员人数+1
                        subpercount++;
                    } else if ("I".equals(policyDynamicListVo.getEditType())) {
                        //总人数+1
                        changepercount++;
                        //增员人数+1
                        addpercount++;
                    }
                }
                //组织入参
                //设置交易类型（默认：50：批改报价）
                vo.setRequesttype(DECLARATION_QUOTE);
                //设置时间戳
                String timestamp = DateUtils.format(new Date(), DateUtils.PATTERN_DATE_TIME);
                vo.setTimestamp(timestamp);
                //设置批改生效日期 (申报日期+1天)
                vo.setValidDate(DateUtils.format(validDate, DateUtils.PATTERN_DATE_TIME));
                //设置雇主入机时间
                vo.setUpdateSysDate(DateUtils.format(gupolicymain.getUpdatesysdate(), DateUtils.PATTERN_DATE_TIME));
                //设置变更人数(取申报信息总条数)
                vo.setChangepercount(String.valueOf(changepercount));
                //设置增员人数
                vo.setAddpercount(String.valueOf(addpercount));
                //设置减员人数
                vo.setSubpercount(String.valueOf(subpercount));
                //设置渠道id
                vo.setChannel_id(HXXT);
                //调用服务平台批改报价接口
                respVo = endorCommonInQua(vo,gupolicymain,uuid);
            }
        }
        return respVo;
    }


    /**
     * @description: 校验传入的待申报雇员信息, 返回错误信息
     * @param: vo
     * @param: flag 0-逐笔 1-批量
     * @author: zhoutaoyu
     * @date: 2021/2/26
     * @return:
     **/
    public String checkDynamicList(DeclarationQuoteReqVo vo, String flag) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        //定义雇员异常信息
        StringBuilder errorMsg = new StringBuilder();
        //校验保单号是否有效
        Gupolicymain policymainCondition = new Gupolicymain();
        policymainCondition.setPolicyNo(vo.getPolicyNo());
        List<Gupolicymain> gupolicymains = policymainDao.selectByCondition(policymainCondition);
        if (CollectionUtils.isEmpty(gupolicymains)) {
            errorMsg.append(MessageFormat.format("{0} 为无效的保单号!,请核实!\n", vo.getPolicyNo()));
            return errorMsg.toString();
        }
        Gupolicycopymain copyMain = new Gupolicycopymain();
        copyMain.setPolicyNo(vo.getPolicyNo());
        copyMain.setUnderwriteind("0");
        List<Gupolicycopymain> copyMainList = policycopymainDao.selectByCondition(copyMain);
        if (null != copyMainList && 0 < copyMainList.size()){
            errorMsg.append(MessageFormat.format("{0} 有未生成的批单!申报单号为 {1} , 请生成批单后再进行批改!\n", vo.getPolicyNo(),copyMainList.get(0).getEndorNo()));
            return errorMsg.toString();
        }
        //校验申报人员信息是否为空
        List<PolicyDynamicListVo> dynamicList = vo.getDynamicList();
        if (CollectionUtils.isEmpty(dynamicList)) {
            errorMsg.append(MessageFormat.format("保单号 {0} 申报信息不能为空!请核实!\n", vo.getPolicyNo()));
            return errorMsg.toString();
        }
        //校验excel中是否存在重复的证件号码
        if ("1".equals(flag)) {
            List<String> result = new ArrayList<>();
            List<String> fieldAFList = dynamicList.stream().map(PolicyDynamicListVo::getFieldAF).collect(Collectors.toList());
            Map<String, Long> repeatMap = fieldAFList.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
            repeatMap.forEach((repeatIdentifyNumber,count)->{
                if(count>1) {
                    result.add(repeatIdentifyNumber);
                }
            });
            Assert.isTrue(CollectionUtils.isEmpty(result),MessageFormat.format("存在重复的证件号码: {0} ,请核实!",result) );
        }

        Integer index = 0;
        for (PolicyDynamicListVo dynamicListVo : dynamicList) {
            StringBuilder excelErrorMsg = new StringBuilder();
            index++;
            //批量申报excel校验
            if ("1".equals(flag)) {
                //雇员姓名
                if (!StringUtils.hasText(dynamicListVo.getFieldAB())) {
                    excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员姓名不能为空!请核实!\n", index));
                }
                //雇员性别
                if (!StringUtils.hasText(dynamicListVo.getFieldAC())) {
                    excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员性别不能为空!请核实!\n", index));
                }
                //雇员生日
                if (!StringUtils.hasText(dynamicListVo.getFieldAD())) {
                    excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员生日不能为空!请核实!\n", index));
                } else {
                    try {
                        DateUtils.parseDate(dynamicListVo.getFieldAD());
                    } catch (Exception e) {
                        excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员生日格式不正确!请核实!\n", index));
                    }
                }
                //雇员证件类型
                if (!StringUtils.hasText(dynamicListVo.getFieldAE())) {
                    excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员证件类型不能为空!请核实!\n", index));
                }
                //雇员证件号
                if (!StringUtils.hasText(dynamicListVo.getFieldAF())) {
                    excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员证件号不能为空!请核实!\n", index));
                }
                //职业代码
                if (!StringUtils.hasText(dynamicListVo.getFieldAG())) {
                    excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员职业代码不能为空!请核实!\n", index));
                }
                //职业名称
                if (!StringUtils.hasText(dynamicListVo.getFieldAH())) {
                    excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员职业名称不能为空!请核实!\n", index));
                }
                //职业等级
                if (!StringUtils.hasText(dynamicListVo.getFieldAI())) {
                    excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员职业等级不能为空!请核实!\n", index));
                }
                if ("1144".equals(gupolicymains.get(0).getProductcode())){
                    //详细地址
                    if (!StringUtils.hasText(dynamicListVo.getHomeaddress())) {
                        excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员详细地址不能为空!请核实!\n", index));
                    }
                    //电话
//                    if (!StringUtils.hasText(dynamicListVo.getHometel())) {
//                        excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员电话不能为空!请核实!\n", index));
//                    }
                }
                //校验职业代码/名称等级
                if (!"1144".equals(gupolicymains.get(0).getProductcode())) {
                    if (StringUtils.hasText(dynamicListVo.getFieldAG())) {
                        String fieldAG = dynamicListVo.getFieldAG();
                        Ggcode ggcode = ggcodeDao.queryByCode(fieldAG);
                        if (ggcode == null) {
                            excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员职业代码无效或未配置!请核实!\n", index));
                        }
                        // 批增时校验,批减和修改不去校验
                        if ("I".equals(dynamicListVo.getEditType()) && ggcode.getCodeType() != null && !"JobCodeNew2022".equals(ggcode.getCodeType())) {
                            excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员职业代码已失效,请下载最新的模板!请核实!\n", index));
                        }
                        //获取职业名称
                        String codeCname = ggcode.getCodeCname();
                        //获取职业等级
                        String occupationlevel = ggcode.getRemark();
                        if (!codeCname.equals(dynamicListVo.getFieldAH())) {
                            excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员职业名称无效或未配置!请核实!\n", index));
                        }
                        if (!occupationlevel.equals(dynamicListVo.getFieldAI())) {
                            excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员职业等级无效或未配置!请核实!\n", index));
                        }
                    }
                }
                //约定月薪
                if (!StringUtils.hasText(dynamicListVo.getMonthPay())) {
                    excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员约定月薪不能为空!请核实!\n", index));
                }
                //批改标志
                if (!StringUtils.hasText(dynamicListVo.getEditType())) {
                    excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员批改标志不能为空!请核实!\n", index));
                }
                //入/离职日期
                if ("I".equals(dynamicListVo.getEditType()) && !StringUtils.hasText(dynamicListVo.getEntrydate())) {
                    excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员入职日期不能为空!请核实!\n", index));
                } else if ("D".equals(dynamicListVo.getEditType()) && !StringUtils.hasText(dynamicListVo.getResignationdate())) {
                    excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员离职日期不能为空!请核实!\n", index));
                }else if(StringUtils.hasText(dynamicListVo.getEntrydate())){
                    try {
                        DateUtils.parseDate(dynamicListVo.getEntrydate());
                    } catch (Exception e) {
                        excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:申报人员入/离职日期格式不正确!请核实!\n", index));
                    }
                }
                //若excel表格空值校验未通过则直接返回
                if (excelErrorMsg.length() > 0 || StringUtils.hasText(excelErrorMsg.toString())) {
                    errorMsg.append(excelErrorMsg);
                    continue;
                }
            }

            String checkErrorMsg = ApplicantIDCardCheck.AppCertiNo(dynamicListVo.getFieldAE(), dynamicListVo.getFieldAF(), "1", dynamicListVo.getFieldAB());
            if (!"ok".equals(checkErrorMsg)) {
                errorMsg.append(checkErrorMsg).append("1".equals(flag) ? "\n" : "");
                continue;
            }
            if ("01".equals(dynamicListVo.getFieldAE())) {//证件类型为身份证校验
                //投保人证件号校验-证件号码中的出生日期和出生日期一致
                String certiBirthDate = IDCardCheckUtils.getBirthByIdCard(dynamicListVo.getFieldAF());
                String formatBirthDateStr = certiBirthDate.substring(0, 4) + "-" + certiBirthDate.substring(4, 6) + "-" + certiBirthDate.substring(6, 8);
                String birthDateStr = dynamicListVo.getFieldAD();
                Date birthDate = DateUtils.parseDate(birthDateStr);
                Date formatBirthDate = DateUtils.parseDate(formatBirthDateStr);
                if (formatBirthDate.compareTo(birthDate) != 0) {
                    errorMsg.append(MessageFormat.format("申报人员{0},证件号码:{1},证件号码中的出生日期和录入的出生日期不一致!,请核实!", dynamicListVo.getFieldAB(), dynamicListVo.getFieldAF())).append("1".equals(flag) ? "\n" : "");
                }
                //性别
                String sex = IDCardCheckUtils.getOPGenderByIdCard(dynamicListVo.getFieldAF());
                if (!sex.equals(dynamicListVo.getFieldAC())) {
                    errorMsg.append(MessageFormat.format("申报人员{0},证件号码:{1}的性别有误!,请核实!", dynamicListVo.getFieldAB(), dynamicListVo.getFieldAF())).append("1".equals(flag) ? "\n" : "");
                }
            }

            //校验职业代码
            if ("0".equals(flag)) {
                if (!"1144".equals(gupolicymains.get(0).getProductcode())) {
                    String fieldAG = dynamicListVo.getFieldAG();
                    Ggcode ggcode = ggcodeDao.queryByCode(fieldAG);
                    if (ggcode == null) {
                        errorMsg.append(MessageFormat.format("申报人员{0},证件号码:{1}职业代码无效或未配置!请核实!", dynamicListVo.getFieldAB(), dynamicListVo.getFieldAF()));
                    }
                    if ("I".equals(dynamicListVo.getEditType()) && ggcode.getCodeType() != null && !"JobCodeNew2022".equals(ggcode.getCodeType())) {
                        errorMsg.append(MessageFormat.format("申报人员{0},证件号码:{1}职业代码已失效!请核实!", dynamicListVo.getFieldAB(), dynamicListVo.getFieldAF()));
                    }
                }
            }

            //根据姓名/雇员证件号/保单号/标识查询雇员清单表数据
            Gupolicyemployerslist employerslistCondition = new Gupolicyemployerslist();
            //保单号
            employerslistCondition.setPolicyNo(vo.getPolicyNo());
            //姓名
            employerslistCondition.setEmpname(dynamicListVo.getFieldAB());
            //雇员证件号
            employerslistCondition.setEmpidentifynumber(dynamicListVo.getFieldAF());
            //标的标识
            employerslistCondition.setTargetflag("I");
            //保险计划
            if ("1".equals(flag) && "D".equals(dynamicListVo.getEditType())) {
                if (StringUtils.hasText(vo.getItemNo())) {
                    employerslistCondition.setItemNo(new BigDecimal(vo.getItemNo()));
                }
            }
            List<Gupolicyemployerslist> policyemployerslists = gupolicyemployerslistDao.selectByCondition(employerslistCondition);
            //校验批改标识是否正确
            if (!"I".equals(dynamicListVo.getEditType()) && !"D".equals(dynamicListVo.getEditType())&& !"U".equals(dynamicListVo.getEditType())) {
                errorMsg.append(MessageFormat.format("申报人员{0},证件号码:{1}批改标志不正确!,请核实!", dynamicListVo.getFieldAB(), dynamicListVo.getFieldAF())).append("1".equals(flag) ? "\n" : "");
            }
            //增员校验
            if ("I".equals(dynamicListVo.getEditType())) {
                Gupolicycopyemployerslist copyemployerslistCondition = new Gupolicycopyemployerslist();
                //姓名
                copyemployerslistCondition.setEmpname(dynamicListVo.getFieldAB());
                //雇员证件号
                copyemployerslistCondition.setEmpidentifynumber(dynamicListVo.getFieldAF());
                //保单号
                copyemployerslistCondition.setPolicyNo(vo.getPolicyNo());
                //批改标志
                copyemployerslistCondition.setTargetflag("I");
                //查询该人员是否已经申报增员,不允许重复增员
                List<Gupolicycopyemployerslist> repeatList = gupolicycopyemployerslistDao.selectByCondition(copyemployerslistCondition);
                if (!CollectionUtils.isEmpty(policyemployerslists)) {
                    errorMsg.append(MessageFormat.format("申报人员{0},证件号码:{1}已经存在,不允许重复投保,请核实!", dynamicListVo.getFieldAB(), dynamicListVo.getFieldAF())).append("1".equals(flag) ? "\n" : "");
                } else if (!CollectionUtils.isEmpty(repeatList)) {
                    errorMsg.append(MessageFormat.format("申报人员{0},证件号码:{1}已申报增员,不允许重复申报,请核实!", dynamicListVo.getFieldAB(), dynamicListVo.getFieldAF())).append("1".equals(flag) ? "\n" : "");
                }
            } else if ("D".equals(dynamicListVo.getEditType())) { //减员校验
                if (CollectionUtils.isEmpty(policyemployerslists)) {
                    Gupolicycopyemployerslist copyemployerslistConditionForI = new Gupolicycopyemployerslist();
                    //姓名
                    copyemployerslistConditionForI.setEmpname(dynamicListVo.getFieldAB());
                    //雇员证件号
                    copyemployerslistConditionForI.setEmpidentifynumber(dynamicListVo.getFieldAF());
                    //保单号
                    copyemployerslistConditionForI.setPolicyNo(vo.getPolicyNo());
                    //批改标志
                    copyemployerslistConditionForI.setTargetflag("I");
                    if (CollectionUtils.isEmpty(gupolicycopyemployerslistDao.selectByCondition(copyemployerslistConditionForI))) {
                        Gupolicycopyemployerslist copyemployerslistCondition = new Gupolicycopyemployerslist();
                        //姓名
                        copyemployerslistCondition.setEmpname(dynamicListVo.getFieldAB());
                        //雇员证件号
                        copyemployerslistCondition.setEmpidentifynumber(dynamicListVo.getFieldAF());
                        //保单号
                        copyemployerslistCondition.setPolicyNo(vo.getPolicyNo());
                        //批改标志
                        copyemployerslistCondition.setTargetflag("D");
                        //查询该人员是否已经申报增员,不允许重复增员
                        List<Gupolicycopyemployerslist> repeatList = gupolicycopyemployerslistDao.searchBypolicyNo(copyemployerslistCondition);
                        if (CollectionUtils.isEmpty(repeatList)) {
                            errorMsg.append(String.format("申报人员%s,证件号:%s,在保单号%s下未匹配到记录，请核实！", dynamicListVo.getFieldAB(), dynamicListVo.getFieldAF(), vo.getPolicyNo())).append("1".equals(flag) ? "\n" : "");
                        }
                    } else {
                        if ("1".equals(flag)) {
                            errorMsg.append(String.format("申报人员%s,证件号:%s,在保单号%s中非已选择计划%s中的人员，不允许进行批减操作", dynamicListVo.getFieldAB(), dynamicListVo.getFieldAF(), vo.getPolicyNo(), vo.getItemNo())).append("1".equals(flag) ? "\n" : "");
                        } else {
                            errorMsg.append(String.format("申报人员%s,证件号:%s,在保单号%s中已申报还未审核通过，不允许进行减员操作", dynamicListVo.getFieldAB(), dynamicListVo.getFieldAF(), vo.getPolicyNo())).append("1".equals(flag) ? "\n" : "");
                        }
                    }
                }

                //
                //查询该人员是否存在理赔记录
                //组织雇员信息,调用理赔接口查询
                CallClaimReqVo callClaimReqVo = new CallClaimReqVo();
                //保单号
                callClaimReqVo.setPolicyNo(vo.getPolicyNo());
                // 被保险人/投保人名称
                callClaimReqVo.setInsuredName(dynamicListVo.getFieldAB());
                // 证件类型
                callClaimReqVo.setIdentifyType(dynamicListVo.getFieldAE());
                // 证件号码
                callClaimReqVo.setIdentifyNumber(dynamicListVo.getFieldAF());
                // 来源渠道1移动端2渠道
                callClaimReqVo.setSourceFlag("2");
                // 1-车，0-非车
                callClaimReqVo.setCarFlag("0");
                // try {
                //     List<CallClaimRespVo> callClaimRespVos = this.callClaimService.queryClaims4Insured(callClaimReqVo);
                //     if (!CollectionUtils.isEmpty(callClaimRespVos)) {
                //         errorMsg.append(MessageFormat.format("申报人员{0},证件号码:{1}已存在理赔记录，不允许减员操作，请与相关人员核实！", dynamicListVo.getFieldAB(),dynamicListVo.getFieldAF())).append("1".equals(flag) ? "\n" : "");
                //     }
                // } catch (Exception e) {
                //     errorMsg.append(MessageFormat.format("申报人员{0},证件号码:{1}查询理赔记录请求异常,请联系管理员！异常原因:{2}", dynamicListVo.getFieldAB(),dynamicListVo.getFieldAF(), e.getMessage())).append("1".equals(flag) ? "\n" : "");
                // }
            }else if ("U".equals(dynamicListVo.getEditType())) {
                Gupolicycopyemployerslist copyemployerslistCondition = new Gupolicycopyemployerslist();
                //雇员证件号
                copyemployerslistCondition.setEmpidentifynumber(dynamicListVo.getFieldAF());
                //保单号
                copyemployerslistCondition.setPolicyNo(vo.getPolicyNo());
                //批改标志
                copyemployerslistCondition.setTargetflag("D");
                //查询该人员是否已经申报增员,不允许重复增员
                List<Gupolicycopyemployerslist> repeatList = gupolicycopyemployerslistDao.searchBypolicyNo(copyemployerslistCondition);
                if (CollectionUtils.isEmpty(repeatList)) {
                    errorMsg.append(MessageFormat.format("申报人员{0},证件号码:{1}不存在,不允许修改,请核实!", dynamicListVo.getFieldAB(), dynamicListVo.getFieldAF())).append("1".equals(flag) ? "\n" : "");
                }
            }
        }
        return errorMsg.toString();
    }

    /**
     * @description:
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/2/9
     * @return:
     **/
    @Transactional
    public DeclarationQuoteRespVo endorCommonInQua(DeclarationQuoteReqVo vo, Gupolicymain gupolicymain, String uuid) {
        //获取yml文件中配置的接口地址
        String endorCommonInQua = urlConfig.getEndorCommonInQua();
        String reqJson = JSON.toJSONString(vo);
        String responseJson;
        DeclarationQuoteRespVo respVo = new DeclarationQuoteRespVo();
        //生成批改申请号
//        String endorNo = DocumentFactory.getSnowflakeNo(new EndorNoTask());
        String insurancecompanycode = gupolicymain.getInsurancecompanycode();
        String companyCodeStr = insurancecompanycode.substring(insurancecompanycode.length() - 6);
        String endorNo = businessNoService.nextNo(BusinessNoType.ENDOR_NO, companyCodeStr, gupolicymain.getProductcode());
        //定义存储日志对象
        Gupolicylog gupolicylog = new Gupolicylog();
        gupolicylog.setRequesetdate(new Date());
        gupolicylog.setOperatorcode(SessionHelper.getLoginUser().getUserCode());
        gupolicylog.setRemark(ENDORCOMMONINQUA50);
        //设置批改申请号为id
        gupolicylog.setId(endorNo);
        //调用服务平台接口查询待同步保单号清单
        try {
            responseJson = HttpClientUtils.postJson(endorCommonInQua, reqJson).getBodyAsString();
        } catch (Exception e) {
            log.info(MessageFormat.format("保单号:{0},批改报价失败,原因:{1}", vo.getPolicyNo(), e.getMessage()));
            //保存异常日志
            gupolicylog.setRequestcontent(reqJson);
            gupolicylog.setResponsecontent(MessageFormat.format("保单号:{0},批改报价失败,原因:{1}", vo.getPolicyNo(), e.getMessage()));
            gupolicylog.setResponsestatus("1");
            gupolicylogDao.insertSelective(gupolicylog);
            respVo.setCode("500");
            respVo.setMsg("批改报价失败,网络异常!请稍后重试或联系管理员处理");
            return respVo;
        }
        if (!StringUtils.hasText(responseJson)) {
            log.info(MessageFormat.format("保单号:{0},批改报价失败,原因{1}", vo.getPolicyNo(), "响应为空"));
            gupolicylog.setRequestcontent(reqJson);
            gupolicylog.setResponsecontent(MessageFormat.format("保单号:{0},批改报价失败,原因:{1}", vo.getPolicyNo(), "响应为空"));
            gupolicylog.setResponsestatus("1");
            gupolicylogDao.insertSelective(gupolicylog);
            respVo.setCode("500");
            respVo.setMsg("批改报价失败,原因:响应为空!");
        } else {
            JSONObject respJsonObj = JSONObject.parseObject(responseJson);
            String code = (String) respJsonObj.get("code");
            //若不为响应状态码不为"200"则查询失败
            if (!"200".equals(code)) {
                //获取异常信息
                String msg = (String) respJsonObj.get("msg");
                log.info(MessageFormat.format("保单号:{0},批改报价失败,原因:{1}", vo.getPolicyNo(), msg));
                //保存异常日志
                gupolicylog.setRequestcontent(reqJson);
                gupolicylog.setResponsecontent(MessageFormat.format("保单号:{0},批改报价失败,原因:{1}", vo.getPolicyNo(), msg));
                gupolicylog.setResponsestatus("1");
                gupolicylogDao.insertSelective(gupolicylog);
                respVo.setCode("500");
                respVo.setMsg(MessageFormat.format("批改报价失败,原因:{0}", msg));
                return respVo;
            }
            //设置返回参数中申报单号
            JSONObject parse = JSON.parseObject(responseJson);
            respVo = JSONObject.toJavaObject(parse, DeclarationQuoteRespVo.class);
            respVo.setEndorNo(endorNo);
            //设置增员人数
            respVo.setAddpercount(String.valueOf(vo.getAddpercount()));
            //设置减员人数
            respVo.setSubpercount(String.valueOf(vo.getSubpercount()));
            //设置flag
            respVo.setFlag(vo.getFlag());
            //保存成功日志
            gupolicylog.setRequestcontent(reqJson);
            gupolicylog.setResponsecontent(JSON.toJSONString(respVo));
            gupolicylog.setResponsestatus("5");
            gupolicylogDao.insertSelective(gupolicylog);
            //存储批单关联关系
            GzEndorRealatedDto gzEndorRealatedDto = new GzEndorRealatedDto();
            gzEndorRealatedDto.setEndorno(endorNo);
            gzEndorRealatedDto.setUuid(uuid);
            gzEndorRealatedDto.setPolicyno(vo.getPolicyNo());
            gzEndorRealatedDtoMapper.insert(gzEndorRealatedDto);
        }
        return respVo;
    }

    private List<String> getRelatedCoinsPolicynoList(String coinsPolicyno) {
        log.info(String.format("---------尝试获取 %s 从联保单-------------",coinsPolicyno));
        List<String> relatedCoinsPolicynoList = new ArrayList<>();
        GuPolicyCoinsuranceDtoExample guPolicyCoinsuranceDtoExample = new GuPolicyCoinsuranceDtoExample();
        //2-联保 2-从方
        guPolicyCoinsuranceDtoExample.createCriteria().andPolicynoEqualTo(coinsPolicyno).andRelatedcoinstypeEqualTo("2").andPrincipalindEqualTo("2");
        List<GuPolicyCoinsuranceDto>  guPolicyCoinsuranceDtoList = guPolicyCoinsuranceDtoMapper.selectByExample(guPolicyCoinsuranceDtoExample);
        if (!CollectionUtils.isEmpty(guPolicyCoinsuranceDtoList)) {
//            for (GuPolicyCoinsuranceDto guPolicyCoinsuranceDto : guPolicyCoinsuranceDtoList) {
//                relatedCoinsPolicynoList.add(guPolicyCoinsuranceDto.getRelatedcoinspolicyno());
//            }
			relatedCoinsPolicynoList = guPolicyCoinsuranceDtoList.stream()
					.map(GuPolicyCoinsuranceDto::getRelatedcoinspolicyno)
					.collect(Collectors.toList());
            log.info(String.format("---------已获取 %s 从联保单 %s -------------", coinsPolicyno,relatedCoinsPolicynoList));
        }
        return relatedCoinsPolicynoList;
    }


    /**
     * @description:批量申报模板下载
     * @author: zhoutaoyu
     * @date: 2021/3/1
     * @return:
     **/
    @Transactional
    public String downLoadTemplate(String templateType) {
        String template = "";
        String filePath = null;
        String resourceUrl = declarationConfig.getDeclarationResourceUrl();
        String templateName = "";
        if ((TEMPLATE_BATCH_IMPORT.equals(templateType))) {
            templateName = declarationConfig.getHXXTTemplateName();
        } else if ((TEMPLATE_BATCH_QUERY.equals(templateType))) {
            templateName = declarationConfig.getBatchQueryDeclarationTemplate();
        } else if ((TEMPLATE_BATCH_IMPORT_TY.equals(templateType))) {
            templateName = declarationConfig.getHXXTTemplateNameTy();
        } else if (TEMPLATE_BATCH_IMPORT_INSUREDLIST.equals(templateType)) {
            templateName = declarationConfig.getBatchProposalTemplate();
        }
        try {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(resourceUrl)) {
                if (!org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(resourceUrl.substring(resourceUrl.length() - 1), "/", "\\")) {
                    resourceUrl = resourceUrl.concat(File.separator);
                }
            } else {
                resourceUrl = FILEPATH;
            }
            filePath = URLDecoder.decode(resourceUrl, "UTF-8").concat(templateName);
            template = Base64.encode(EnctyptUtil.getPath(filePath));
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException("网络连接异常,请稍后重试!");
        }
        return template;
    }

    /**
     * @description: 根据传入的申报单号, 删除轨迹表数据
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/3/4
     * @return:
     **/
    @Transactional(rollbackFor = Exception.class)
    public DeclarationQuoteRespVo cancelDeclaration(List<String> endorNoList) throws Exception {
        StringBuilder errorMsg = new StringBuilder();
        DeclarationQuoteRespVo respVo = new DeclarationQuoteRespVo();
        for (String endorno : endorNoList) {
            List<GzEndorRealatedDto> gzEndorRealatedDtoList = getGzEndorRealatedDtoList(endorno);
            if (CollectionUtils.isEmpty(gzEndorRealatedDtoList)) {
                GzEndorRealatedDto gzEndorRealatedDtoTemp = new GzEndorRealatedDto();
                gzEndorRealatedDtoTemp.setEndorno(endorno);
                gzEndorRealatedDtoList.add(gzEndorRealatedDtoTemp);
            }
            for (GzEndorRealatedDto gzEndorRealatedDto : gzEndorRealatedDtoList) {
                String endorNo = gzEndorRealatedDto.getEndorno();
                StringBuilder sb = new StringBuilder();
                //根据申报单号查询日志表
                Gupolicylog policyLogReq = gupolicylogDao.selectByPrimaryKey(endorNo);
                if (policyLogReq == null) {
                    sb.append(MessageFormat.format("申报单号:{0}未进行批改报价,请确认!\n", endorNo));
                } else if ("2".equals(policyLogReq.getResponsestatus())) {
                    sb.append(MessageFormat.format("申报单号:{0}申报生成批单成功,不允许取消,请确认!\n", endorNo));
                } else if ("3".equals(policyLogReq.getResponsestatus())) {
                    sb.append(MessageFormat.format("申报单号:{0}需补生成批单,不允许取消,请确认!\n", endorNo));
                }
                if (sb.length() > 0 || StringUtils.hasText(sb.toString())) {
                    errorMsg.append(sb);
                    continue;
                }

                //未进行申报确认或申报确认失败的数据不需处理轨迹表数据
                try {
                    if (!"1".equals(policyLogReq.getResponsestatus()) && !"5".equals(policyLogReq.getResponsestatus()) && !"6".equals(policyLogReq.getResponsestatus())) {
                        //删除保单主信息轨迹表数据
                        policycopymainDao.deleteByEndorNo(endorNo);
                        //删除保单雇员计划轨迹表数据
                        gupolicycopyemployersplanDao.deleteByEndorNo(endorNo);
                        //删除保单险别轨迹表数据
                        gupolicycopyitemkindDao.deleteByEndorNo(endorNo);
                        //删除保单雇员清单轨迹表数据前先根据申报号修改信息
                        gupolicycopyemployerslistDao.updateEndorNo(endorNo);
                        //删除保单雇员清单轨迹表数据
                        gupolicycopyemployerslistDao.deleteByEndorNo(endorNo);
                    }
                    //删除日志表数据
                    gupolicylogDao.deleteByPrimaryKey(policyLogReq.getId());
                } catch (Exception e) {
                    log.info(MessageFormat.format("申报单号:{0}申报取消异常!请联系管理员处理!原因:{0}", endorNo, e.getMessage()));
                    throw new BusinessException(MessageFormat.format("申报取消异常!请联系管理员处理原因:{0}", endorNo, e.getMessage()));
                }
            }
        }
        //若校验信息有值,则返回失败信息与提示
        if (errorMsg.length() > 0 && StringUtils.hasText(errorMsg)) {
            respVo.setCode("500");
            respVo.setMsg(errorMsg.toString());
        } else {
            respVo.setCode("200");
            respVo.setMsg("申报取消成功!");
        }
        return respVo;
    }


    /**
     * 申报页面批量查询功能
     *
     * <AUTHOR>
     * @date 2021年03月10日 18:16:46
     */
    public PageResult<DeclarationRespVo> batchQueryDeclaration(MultipartFile file, String pageNum, String pageSize) {
        //对上传的文件进行合法性校验
        if (!ExportExcelUtil.checkFileXlsx(file)) {
            throw new BusinessException("文件为空或非法的文件格式!");
        }
        //获取文件名
        String fileName = file.getOriginalFilename();
        Workbook workbook = null;
        try {
            //获取输入流
            InputStream is = file.getInputStream();
            //根据excel版本获取对应poi解析对象
            workbook = ExportExcelUtil.judegExcelEdition(fileName, is);
        } catch (Exception e) {
            throw new BusinessException("文件为空或非法的文件格式!");
        }
        //读取excel并保存至List中
        List<ExcelTempQueryVo> excelTempQueryVoList = ExportExcelUtil.readExcel(workbook,
                ExcelTempQueryVo.class, HXXT_INIT_ROWNUM, PolicyCopyMainService.TEMPLATE_BATCH_QUERY);
        List<DeclarationReqVo> list = new ArrayList<>();
        DeclarationReqVo declarationReqVo;
        ExcelTempQueryVo excelTempQueryVo;
        Iterator<ExcelTempQueryVo> iterator = excelTempQueryVoList.listIterator();
        String uuid = businessNoService.nextNo(BusinessNoType.ID);//获取序列
        while (iterator.hasNext()) {
            excelTempQueryVo = iterator.next();
            declarationReqVo = new DeclarationReqVo();
            declarationReqVo.setUuid(uuid);
            declarationReqVo.setEmpName(excelTempQueryVo.getEmpName());
            declarationReqVo.setEmpIdentifyNumber(excelTempQueryVo.getEmpIdentifyNumber());
            list.add(declarationReqVo);
        }
        //存储临时数据至临时表
        policycopymainDao.insertBatchDeclarationBatchQueryTemp(list);
        //调用分页查询,返回分页查询数据
        DeclarationReqVo vo = new DeclarationReqVo();
        vo.setIsExcelBatchQueryFlag(1);
        vo.setUuid(uuid);
        //处理分页信息
        boolean isNum = IDCardCheckUtils.isNum(pageNum);
        pageNum = isNum ? pageNum : "1";
        isNum = IDCardCheckUtils.isNum(pageSize);
        pageSize = isNum ? pageSize : "10";
        vo.setPageNum(Integer.parseInt(pageNum));
        vo.setPageSize(Integer.parseInt(pageSize));
        //只支持查询单一保单号下的数据
        vo.setPolicyNo(excelTempQueryVoList.get(0).getPolicyNo());
        return pageQueryDeclarationByCondition(vo);
    }

    /**
     * @description:批量申报模板下载--修改
     * @author: huanghaiyang
     * @date: 2021/9/22
     * @return:
     **/
    @Transactional
    public String uploadExcelBySearch(String policyNo,String itemNo) {
        String template = "";
        String filePath = null;
        String resourceUrl = declarationConfig.getDeclarationResourceUrl();
        String templateName = "";
        templateName = declarationConfig.getUpdateTemplateName();
        Gupolicyitemkind gupolicyitemkind = new Gupolicyitemkind();
        gupolicyitemkind.setPolicyNo(policyNo);
        List<Gupolicyitemkind> itemkindList =  gupolicyitemkindDao.selectByCondition(gupolicyitemkind);
        if ("1144".equals(itemkindList.get(0).getRiskCode())){
            templateName = declarationConfig.getUpdateTemplateNameTy();
        }
        try {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(resourceUrl)) {
                if (!org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(resourceUrl.substring(resourceUrl.length() - 1), "/", "\\")) {
                    resourceUrl = resourceUrl.concat(File.separator);
                }
            } else {
                resourceUrl = FILEPATH;
            }
            filePath = URLDecoder.decode(resourceUrl, "UTF-8").concat(templateName);
            InputStream excelFileInputStream = EnctyptUtil.class.getResourceAsStream(filePath);
            XSSFWorkbook workbook = new XSSFWorkbook(excelFileInputStream);
            ByteArrayOutputStream bot = new ByteArrayOutputStream();
            XSSFSheet sheet = workbook.getSheetAt(0);

            Gupolicyemployerslist employerslistCondition = new Gupolicyemployerslist();
            employerslistCondition.setPolicyNo(policyNo);
            employerslistCondition.setTargetflag("D");
            employerslistCondition.setItemNo(new BigDecimal(itemNo));
            SysUser sysUser = SessionHelper.getLoginUser();
            if(null != sysUser.getDepartment()){
                employerslistCondition.setDepartment(sysUser.getDepartment());
            }
            List<Gupolicyemployerslist> list = gupolicyemployerslistDao.searchByPolicyNo(employerslistCondition);
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            if (null != list && 0 < list.size()) {
                XSSFRow newRow = null;
                XSSFCell newNameCell = null;
                for (int i = 0, iSize = list.size(); i < iSize; i++) {
                    employerslistCondition = list.get(i);
                    if (null != employerslistCondition) {
                        newRow = sheet.createRow(i + 3);
                        newNameCell = newRow.createCell(0, Cell.CELL_TYPE_STRING);
                        newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
                        newNameCell.setCellValue("U");
                        newNameCell = newRow.createCell(1, Cell.CELL_TYPE_STRING);
                        newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
                        newNameCell.setCellValue(employerslistCondition.getListseqno().toString());
                        newNameCell = newRow.createCell(2, Cell.CELL_TYPE_STRING);
                        newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
                        newNameCell.setCellValue(employerslistCondition.getEmpname());
                        newNameCell = newRow.createCell(3, Cell.CELL_TYPE_STRING);
                        newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
                        newNameCell.setCellValue(employerslistCondition.getEmpsex());
                        newNameCell = newRow.createCell(4, Cell.CELL_TYPE_STRING);
                        newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
                        //newNameCell.setCellFormula("IF(F"+(i+4)+"=\"01\",IF(G"+(i+4)+"=\"\",\"\",TEXT(TEXT(MID(G"+(i+4)+",7,LEN(G"+(i+4)+")/2-1),\"#-00-00\"),\"e-m-d\")),\"\")");
                        newNameCell.setCellValue(employerslistCondition.getEmpbirthday());
                        newNameCell = newRow.createCell(5, Cell.CELL_TYPE_STRING);
                        newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
                        newNameCell.setCellValue(employerslistCondition.getEmpidentifytype());
                        newNameCell = newRow.createCell(6, Cell.CELL_TYPE_STRING);
                        newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
                        newNameCell.setCellValue(employerslistCondition.getEmpidentifynumber());
                        newNameCell = newRow.createCell(7, Cell.CELL_TYPE_STRING);
                        newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
                        newNameCell.setCellFormula("IF(I"+(i+4)+"=\"\",\"请选择职业名称\",VLOOKUP(I"+(i+4)+",IF({1,0},职业代码映射!B:B,职业代码映射!A:A),2,0))");
                        newNameCell.setCellValue(employerslistCondition.getOccupationCode());
                        newNameCell = newRow.createCell(8, Cell.CELL_TYPE_STRING);
                        newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
                        newNameCell.setCellValue(employerslistCondition.getOccupationname());
                        newNameCell = newRow.createCell(9, Cell.CELL_TYPE_STRING);
                        newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
                        newNameCell.setCellFormula("IF(I"+(i+4)+"=\"\",\"请选择职业名称\",VLOOKUP(I"+(i+4)+",职业代码映射!B:C,2,0))");
                        newNameCell.setCellValue(employerslistCondition.getOccupationlevel());
                        newNameCell = newRow.createCell(10, Cell.CELL_TYPE_STRING);
                        newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
                        newNameCell.setCellValue(employerslistCondition.getEntrydate() != null ? format.format(employerslistCondition.getEntrydate()) : "");
//                        newNameCell = newRow.createCell(11, Cell.CELL_TYPE_STRING);
//                        newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
//                        newNameCell.setCellValue(employerslistCondition.getPlanid());
                        newNameCell = newRow.createCell(11, Cell.CELL_TYPE_STRING);
                        newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
                        newNameCell.setCellValue(employerslistCondition.getProject());
                        if ("1144".equals(itemkindList.get(0).getRiskCode())){
                            newNameCell = newRow.createCell(12, Cell.CELL_TYPE_STRING);
                            newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
                            newNameCell.setCellValue(employerslistCondition.getHomeaddress());
//                            newNameCell = newRow.createCell(13, Cell.CELL_TYPE_STRING);
//                            newNameCell.setCellType(HSSFCell.CELL_TYPE_STRING);
//                            newNameCell.setCellValue(employerslistCondition.getHometel());
                        }
                    }
                }

                workbook.write(bot);
                excelFileInputStream.close();
            }
            template = Base64.encode(bot.toByteArray());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException("网络连接异常,请稍后重试!");
        }

        return template;
    }
    public String checkProposalDynamicList(List<ExcelTempProposalVo> dynamicList) {
        //定义被保人异常信息
        StringBuilder errorMsg = new StringBuilder();
        //校验被保人信息是否为空
        if (CollectionUtils.isEmpty(dynamicList)) {
            errorMsg.append("被保人信息不能为空!请核实!\n");
            return errorMsg.toString();
        }
        //校验 excel中是否存在重复的证件号码
        List<String> result = new ArrayList<>();
        List<String> fieldAFList = dynamicList.stream().map(ExcelTempProposalVo::getCerti_no).collect(Collectors.toList());
        Map<String, Long> repeatMap = fieldAFList.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        repeatMap.forEach((repeatIdentifyNumber,count)->{
            if(count>1) {
                result.add(repeatIdentifyNumber);
            }
        });
        Assert.isTrue(CollectionUtils.isEmpty(result),MessageFormat.format("存在重复的证件号码: {0} ,请核实!",result) );

        Integer index = 0;
        for (ExcelTempProposalVo dynamicListVo : dynamicList) {
            StringBuilder excelErrorMsg = new StringBuilder();
            index++;
            // 姓名
            if (!StringUtils.hasText(dynamicListVo.getName())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:被保人姓名不能为空!请核实!\n", index));
            }
            // 性别
            if (!StringUtils.hasText(dynamicListVo.getSex())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:被保人性别不能为空!请核实!\n", index));
            }
            // 生日
            if (!StringUtils.hasText(dynamicListVo.getBirthdate())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:被保人生日不能为空!请核实!\n", index));
            } else {
                try {
                    DateUtils.parseDate(dynamicListVo.getBirthdate());
                } catch (Exception e) {
                    excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:被保人生日格式不正确!请核实!\n", index));
                }
            }
            // 证件类型
            if (!StringUtils.hasText(dynamicListVo.getCerti_type())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:被保人证件类型不能为空!请核实!\n", index));
            }
            // 证件号
            if (!StringUtils.hasText(dynamicListVo.getCerti_no())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:被保人证件号不能为空!请核实!\n", index));
            }
            // 与投保人关系
            if (!StringUtils.hasText(dynamicListVo.getRelation())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:与投保人关系不能为空!请核实!\n", index));
            }
            // 邮箱
            if (!StringUtils.hasText(dynamicListVo.getEmail())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:被保人邮箱不能为空!请核实!\n", index));
            }
            // 电话
            if (!StringUtils.hasText(dynamicListVo.getPhone())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:被保人电话不能为空!请核实!\n", index));
            }
            // 详细地址
            if (!StringUtils.hasText(dynamicListVo.getContactAdr())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:被保人详细地址不能为空!请核实!\n", index));
            }
            // 在职状态
            if (!StringUtils.hasText(dynamicListVo.getOntheJobstatus())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:被保人在职状态不能为空!请核实!\n", index));
            }
            
            // 职业代码
            if (!StringUtils.hasText(dynamicListVo.getOccupationCode())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:职业代码不能为空!请核实!\n", index));
            }
            
            // 职业名称
            if (!StringUtils.hasText(dynamicListVo.getOccupationName())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:职业名称不能为空!请核实!\n", index));
            }
            
            // 等级
            if (!StringUtils.hasText(dynamicListVo.getOccupationLevel())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:职业等级不能为空!请核实!\n", index));
            }
            
            // 省级代码
            if (!StringUtils.hasText(dynamicListVo.getProvinceCode())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:省级代码不能为空!请核实!\n", index));
            }
            
            // 市级代码
            if (!StringUtils.hasText(dynamicListVo.getCityCode())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:市级代码不能为空!请核实!\n", index));
            }
            
            // 县区代码
            if (!StringUtils.hasText(dynamicListVo.getCountyCode())) {
                excelErrorMsg.append(MessageFormat.format("表格数据第{0}条:县区代码不能为空!请核实!\n", index));
            }
            
            // 若 excel 表格空值校验未通过则直接返回
            if (excelErrorMsg.length() > 0 || StringUtils.hasText(excelErrorMsg.toString())) {
                errorMsg.append(excelErrorMsg);
                continue;
            }

            String checkErrorMsg = ApplicantIDCardCheck.AppCertiNo(dynamicListVo.getCerti_type(), dynamicListVo.getCerti_no(), "1", dynamicListVo.getName());
            if (!"ok".equals(checkErrorMsg)) {
                errorMsg.append(checkErrorMsg).append("\n");
                continue;
            }
            if ("01".equals(dynamicListVo.getCerti_type())) {//证件类型为身份证校验
                //投保人证件号校验-证件号码中的出生日期和出生日期一致
                String certiBirthDate = IDCardCheckUtils.getBirthByIdCard(dynamicListVo.getCerti_no());
                String formatBirthDateStr = certiBirthDate.substring(0, 4) + "-" + certiBirthDate.substring(4, 6) + "-" + certiBirthDate.substring(6, 8);
                String birthDateStr = dynamicListVo.getBirthdate();
                Date birthDate = DateUtils.parseDate(birthDateStr);
                Date formatBirthDate = DateUtils.parseDate(formatBirthDateStr);
                if (formatBirthDate.compareTo(birthDate) != 0) {
                    errorMsg.append(MessageFormat.format("被保人{0},证件号码:{1},证件号码中的出生日期和录入的出生日期不一致!,请核实!", dynamicListVo.getName(), dynamicListVo.getCerti_no())).append("\n");
                }
                //性别
                String sex = IDCardCheckUtils.getOPGenderByIdCard(dynamicListVo.getCerti_no());
                if (!sex.equals(dynamicListVo.getSex())) {
                    errorMsg.append(MessageFormat.format("被保人{0},证件号码:{1}的性别有误!,请核实!", dynamicListVo.getName(), dynamicListVo.getCerti_no())).append("\n");
                }
            }
        }
        return errorMsg.toString();
    }
}

