package ins.channel.policycopymain.service;

import com.alibaba.fastjson.JSONObject;
import ins.channel.base.utils.DateUtils;
import ins.channel.config.UrlConfig;
import ins.channel.gupolicycopymain.dao.GupolicycopymainDao;
import ins.channel.gupolicycopymain.po.Gupolicycopymain;
import ins.channel.gupolicycopymain.vo.SelectSettlementVo;
import ins.channel.gupolicymain.dao.GupolicymainDao;
import ins.channel.gupolicymain.po.Gupolicymain;
import ins.channel.policycopymain.vo.ShowVo;
import ins.channel.policycopymain.vo.SettlementInterfaceVo;
import ins.channel.policycopymain.vo.ToolsVo;
import ins.channel.support.constant.BusinessNoType;
import ins.channel.support.service.BusinessNoService;
import ins.framework.exception.BusinessException;
import ins.framework.exception.PermissionException;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import ins.platform.common.SysUser;
import ins.platform.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 结算页面service
 */
@Service
@Slf4j
@Transactional
public class ShowSettlementService {

    @Resource
    private GupolicycopymainDao gupolicycopymainDao;
    @Resource
    private BusinessNoService businessNoService;
    @Autowired
    private UrlConfig urlConfig;
    @Resource
    private GupolicymainDao gupolicymainDao;
    /**
     * 结算页面查询
     * @param selectSettlementVo
     * @return
     */
    public PageResult<SelectSettlementVo> settlement(SelectSettlementVo selectSettlementVo){
        //时间验证
        String errorMsg = DateUtils.checkDateStartEnd(selectSettlementVo.getStartdate(), selectSettlementVo.getEnddate(), "到期日期");
        if (!errorMsg.isEmpty()) {
            throw new BusinessException(errorMsg);
        }
        errorMsg = DateUtils.checkDateStartEnd(selectSettlementVo.getValiddate(),selectSettlementVo.getValiddateEnd(), "生效日期");
        if (!errorMsg.isEmpty()) {
            throw new BusinessException(errorMsg);
        }
        errorMsg = DateUtils.checkDateStartEnd(selectSettlementVo.getAcceptdate(),selectSettlementVo.getAcceptdateEnd(), "申报日期");
        if (!errorMsg.isEmpty()) {
            throw new BusinessException(errorMsg);
        }
        SysUser sysUser = SessionHelper.getLoginUser();
        if (null != sysUser.getDepartment()){
            selectSettlementVo.setDepartment(sysUser.getDepartment());
        }
        /** 数据权限校验关联机构*/
        Set<String> permitComs = UserPermitDataHelper.getUserPermitCom();
        if (StringUtils.hasText(selectSettlementVo.getCompanyCode())) { // 如果查询参数中指定了机构，直接做权限校验
            if (!permitComs.contains(selectSettlementVo.getCompanyCode())) {
                throw new PermissionException("当前登录用户没有该机构的操作权限,请确认!");
            }
        } else {
            selectSettlementVo.setCompanyCodes(permitComs);
        }
        PageParam pageParam = PageHelper.getPageParam(selectSettlementVo);
        //TODO 查询有待处理字段
        Page<SelectSettlementVo> selectSettlementVoPage = gupolicycopymainDao.shouByAll(pageParam,selectSettlementVo);
        return PageHelper.convert(pageParam, selectSettlementVoPage, SelectSettlementVo.class);
    }

    /**
     * 结算页面结算 根据申报单号修改
     * @param list
     * @return
     */
    public ResponseVo<ShowVo> SettlementUpdate(List<SelectSettlementVo> list){
        BigDecimal settlefee = BigDecimal.ZERO;
        //生成结算单号
        String settleno = businessNoService.nextNo(BusinessNoType.SETTLE_NO);
        for (int i = 0; i<list.size();i++){
            SelectSettlementVo selectSettlementVo = gupolicycopymainDao.selectGupolicycopymain(list.get(i).getEndorno());
            if(selectSettlementVo!= null){
                settlefee = settlefee.add(selectSettlementVo.getChangegrosspremium());
                selectSettlementVo.setEndorno(list.get(i).getEndorno());
                selectSettlementVo.setSettleno(settleno);
                gupolicycopymainDao.SettlementUpdate(selectSettlementVo);
            }
        }
        ShowVo showVo = new ShowVo();
        showVo.setSettlefee(settlefee);
        showVo.setSettleno(settleno);
        return ResponseVo.ok(showVo);
    }
    /**
     * 接口返回
     * 同步申报单结算结果
     * @return
     */

    public String  showSettlementInterface(ToolsVo toolsVo) {
        //等于3是手工结算单同步否则就是定时任务
        Gupolicycopymain gupolicycopymain = new Gupolicycopymain();
        List<Gupolicycopymain> gupolicycopymainList = new ArrayList<>();
        System.out.println(toolsVo.getFlag());
        if("3".equals(toolsVo.getFlag())){
            //根据传入的申报单号查询Gupolicycopymain表数据   判断他结算状态是否为1 若不唯一直接报错提示前端：该申报单尚未完成生成批单操作，请确认！
            SelectSettlementVo selectSettlementVo = new SelectSettlementVo();
            selectSettlementVo.setEndorno(toolsVo.getEndorNo());
            /** 数据权限校验关联机构*/
            Set<String> permitComs = UserPermitDataHelper.getUserPermitCom();
            selectSettlementVo.setCompanyCodes(permitComs);
            gupolicycopymainList = gupolicycopymainDao.selectByStell(selectSettlementVo);
            Assert.notEmpty(gupolicycopymainList, "该申报单号未进行申报确认,请确认!");
            if(!"1".equals(gupolicycopymainList.get(0).getSettleStatus())){
                throw new BusinessException("该申报单尚未生成结算单，请先生成结算单！");
            }
            gupolicycopymain = gupolicycopymainList.get(0);
        }else{
            //数据库查询信息
            gupolicycopymain = toolsVo.getGupolicycopymainvo();
            if(gupolicycopymainList == null&& gupolicycopymainList.size()==0){
                throw new BusinessException("结算单已全部同步完成！");
            }
        }
        String returnJson="接收成功！";
            String gupolicycopymainListjson ;
            try{
                //组装JSON数据
                gupolicycopymainListjson = JSONObject.toJSON(gupolicycopymain).toString();
            }catch (Exception e){
                //向前端抛出异常原因
                log.info(String.format("%s申报单号调用结算同步查询接口失败！,异常原因：%s",toolsVo.getEndorNo(),e.getMessage()));
                throw new BusinessException(String.format("%s申报单号调用结算同步查询接口失败！异常原因：%s",toolsVo.getEndorNo(),e.getMessage()));
            }
            //调用服务平台接口地址
            String callSPUrl = urlConfig.getSettall();
            //请求调用服务平台
            HttpResponse httpResponse = HttpClientUtils.postJson(callSPUrl,gupolicycopymainListjson);
            if(httpResponse!=null){
                String spJson = httpResponse.getBodyAsString();
                log.info(String.format("请求服务平台接口成功！,报文:%s", spJson));
                if(spJson!=null&&!spJson.isEmpty()){
                    SettlementInterfaceVo settlementInterfaceVo = JSONObject.parseObject(spJson,SettlementInterfaceVo.class);
                    //判断是否为空
                    if(settlementInterfaceVo!=null && settlementInterfaceVo.getDelinquentFee() != null){
                        //接口返回金额
                        BigDecimal DelinquentFee = new BigDecimal(settlementInterfaceVo.getDelinquentFee());
                        //数据库查询金额
                        BigDecimal SettlefeeOne = gupolicycopymain.getSettlefee();
                        //结算总金额
                        BigDecimal gupolicymainSettlefee = null;
                        //变化量金额
                        BigDecimal gupolicycopymainSettlefee = null;
                        //状态
                        String status = null;
                        Gupolicymain gupolicymain = new Gupolicymain();
                        gupolicymain.setPolicyNo(gupolicycopymain.getPolicyNo());
                        List<Gupolicymain> gupolicymainList = gupolicymainDao.selectByCondition(gupolicymain);
                        gupolicymain = gupolicymainList.get(0);
                        if(DelinquentFee.compareTo(BigDecimal.ZERO)==0 && SettlefeeOne.compareTo(BigDecimal.ZERO)==0){
                            //总毛包费减去接口返回金额
                            gupolicycopymainSettlefee = gupolicycopymain.getChangegrosspremium().subtract(DelinquentFee);
                            gupolicymainSettlefee = gupolicycopymain.getChangegrosspremium();
                            log.info(String.format("请求服务平台接口成功！返回金额为0，修改状态为2,金额:%s",  gupolicymainSettlefee));
                            status = "2";
                        }else if(DelinquentFee.compareTo(BigDecimal.ZERO)!=0 && SettlefeeOne.compareTo(BigDecimal.ZERO)==0){
                            //总毛包费减去接口返回金额
                            gupolicycopymainSettlefee = gupolicycopymain.getChangegrosspremium().subtract(DelinquentFee);
                            gupolicymainSettlefee = gupolicycopymain.getChangegrosspremium().subtract(DelinquentFee);
                            status = "1";
                            log.info(String.format("请求服务平台接口成功！返回金额不为0，修改状态为1,金额:%s",  gupolicymainSettlefee));
                        }else if(DelinquentFee.compareTo(BigDecimal.ZERO)!=0 && SettlefeeOne.compareTo(BigDecimal.ZERO)!=0){
                            //总毛包费减去接口金额再减去原有金额
                            gupolicycopymainSettlefee = gupolicycopymain.getChangegrosspremium().subtract(DelinquentFee);
                            gupolicymainSettlefee = gupolicycopymain.getChangegrosspremium().subtract(DelinquentFee).subtract(SettlefeeOne).add(gupolicymain.getSettlefee());
                            status = "1";
                            log.info(String.format("请求服务平台接口成功！返回金额不为0，原有不为0，修改状态为1,金额:%s",  gupolicymainSettlefee));
                        }else if(DelinquentFee.compareTo(BigDecimal.ZERO)==0 && SettlefeeOne.compareTo(BigDecimal.ZERO)!=0){
                            //总毛包费减去接口金额再减去原有金额
                            gupolicycopymainSettlefee = gupolicycopymain.getChangegrosspremium().subtract(DelinquentFee);
                            gupolicymainSettlefee = gupolicycopymain.getChangegrosspremium().subtract(DelinquentFee).subtract(SettlefeeOne).add(gupolicymain.getSettlefee());
                            status = "2";
                            log.info(String.format("请求服务平台接口成功！返回金额为0，但原有不为0，修改状态为2,金额:%s",  gupolicymainSettlefee));
                        }
                        if(gupolicymainSettlefee !=null && status!=null){
                            Gupolicycopymain gupolicycopymain1 = new Gupolicycopymain();
                            //根据保单号、批单序号、批单号修改
                            gupolicycopymain1.setPolicyNo(settlementInterfaceVo.getPolicyNo());
                            gupolicycopymain1.setEndorNo(settlementInterfaceVo.getEndorNo());
                            gupolicycopymain1.setEndorseqno(settlementInterfaceVo.getEndorSeqNo());
                            //修改结算总金额
                            gupolicycopymain1.setSettlefee(gupolicycopymainSettlefee);
                            //修改状态
                            gupolicycopymain1.setSettleStatus(status);
                            //表GupolicycopymainUpd方法提交
                            gupolicycopymainDao.GupolicycopymainUpd(gupolicycopymain1);
                            log.info(String.format("修改-金额、状态，修改表GupolicycopymainUpd成功！,报文:%s",  settlementInterfaceVo));
                            //最新保单住信息表修改
                            Gupolicymain gupolicymain1 = new Gupolicymain();
                            //根据保单号修改
                            gupolicymain1.setPolicyNo(settlementInterfaceVo.getPolicyNo());
                            //修改结算变动的金额
                            gupolicymain1.setSettlefee(gupolicymainSettlefee);
                            //表gupolicymain方法提交执行修改
                            gupolicymainDao.UpdateSettlefee(gupolicymain1);
                            log.info(String.format("修改-金额 修改表gupolicymain成功！,报文:%s", settlementInterfaceVo));
                        }
                    }else {
                        log.info(String.format("修改失败！数据为空！,报文:%s", settlementInterfaceVo));
                        throw new BusinessException("结算同步失败！，返回值为空或返回金额为空");
                    }
                }else {
                    log.info(String.format("修改失败！服务平台接收数据为空！"));
                    throw new BusinessException("结算同步失败！,服务平台接收数据为为空！");
                }
            }else {
                log.info(String.format("修改失败！服务平台调用失败！"));
                throw new BusinessException("结算同步失败！,服务平台调用失败");
            }
        return returnJson;
    }


}
