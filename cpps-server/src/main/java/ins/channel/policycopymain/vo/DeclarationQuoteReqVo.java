package ins.channel.policycopymain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 批改报价入参
 * <AUTHOR>
 * @date 2021/02/08
 */
@Data
@ApiModel("DeclarationQuoteReqVo")
public class DeclarationQuoteReqVo implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty("渠道代码 ")
	private String channel_id;
	@ApiModelProperty("保单号码")
	private String policyNo;
	@ApiModelProperty("交易类型（默认：50：批改报价）")
	private String requesttype;
	@ApiModelProperty("时间戳（毫秒数yyyy-MM-dd HH:mm:ss）")
	private String timestamp;
	@ApiModelProperty("变更人数")
	private String changepercount;
	@ApiModelProperty("批改生效日期（yyyy-MM-dd）")
	private String validDate;
	@ApiModelProperty("变更人员清单集合")
	private List<PolicyDynamicListVo> dynamicList;
	@ApiModelProperty( "每人伤亡责任限额")
	private BigDecimal personcasualtieslimit;
	@ApiModelProperty("计划编码")
	private String planId;
	@ApiModelProperty("批改方式: 0-逐笔申报 1-批量申报")
	private String flag;
	@ApiModelProperty("本次增员人数")
	private String addpercount;
	@ApiModelProperty("本次减员人数")
	private String subpercount;
	@ApiModelProperty("状态码 200-成功 500-失败")
	private String code;
	@ApiModelProperty("提示信息")
	private String msg;
	@ApiModelProperty("最后更新时间")
	private String updateSysDate;

	@ApiModelProperty("保险计划")
	private String itemNo;
	@ApiModelProperty("上传模板唯一id")
	private String uuid;
	@ApiModelProperty("模板成功导入的条数")
	private int successCount;
}
