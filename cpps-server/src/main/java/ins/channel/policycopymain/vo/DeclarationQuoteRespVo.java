package ins.channel.policycopymain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 批改报价返参
 * <AUTHOR>
 * @date 2021/02/09
 */
@Data
@ApiModel("DeclarationQuoteRespVo")
public class DeclarationQuoteRespVo implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty("渠道代码 ")
	private String channel_id;
	@ApiModelProperty("批改类型：08")
	private String endorType;
	@ApiModelProperty("交易类型（默认：60：批改）")
	private String requesttype;
	@ApiModelProperty("响应代码，200 成功，500失败 ")
	private String code;
	@ApiModelProperty("提示信息")
	private String msg;
	@ApiModelProperty("保单号码")
	private String policyNo;
	@ApiModelProperty("批单申请号")
	private String endorNo;
	@ApiModelProperty("批单序号(核心)")
	private String endorseqno;
	@ApiModelProperty("保单信息")
	private mainmessage mainmessage;
	@ApiModelProperty("方案信息")
	private List<PolicyplanVo> planList;
	@ApiModelProperty("险别信息")
	private List<KindMessageVo> kindmessageList;
	@ApiModelProperty("时间戳（毫秒数yyyy-MM-dd HH:mm:ss）")
	private String timestamp;
	@ApiModelProperty("变更人数")
	private String changepercount;
	@ApiModelProperty("批改生效日期（yyyy-MM-dd）")
	private String validDate;
	@ApiModelProperty("变更人员清单集合")
	private List<PolicyDynamicListVo> dynamicList;
	@ApiModelProperty( "每人伤亡责任限额")
	private BigDecimal personcasualtieslimit;
	/** 对应字段：ACCEPTDATE,备注：承保确认时间/批单申报日期 */
	@ApiModelProperty("承保确认时间/批单申报日期")
	private String acceptdate;
	@ApiModelProperty("本次申报后的总人数")
	private String totalnum;
	@ApiModelProperty("页面操作标识: 0-批单生成 1-补生成批单")
	private String flag;
	@ApiModelProperty("本次增员人数")
	private String addpercount;
	@ApiModelProperty("本次减员人数")
	private String subpercount;
}
