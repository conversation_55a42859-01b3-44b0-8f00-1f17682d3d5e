package ins.channel.policycopymain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 批量申报excel导入Vo对象
 * <AUTHOR>
 * @date 2021/02/08
 */
@Data
@ApiModel("ExcelTempVo")
public class ExcelTempVo {

    @ApiModelProperty("批改标志：（I：新增；U-修改; D:删除）")
    private String editType;
    @ApiModelProperty( "序号")
    private String listseqno;
    @ApiModelProperty("雇员姓名")
    private String fieldAB;
    @ApiModelProperty("性别 0-男 1-女")
    private String fieldAC;
    @ApiModelProperty("出生日期（yyyy-MM-dd）")
    private String fieldAD;
    @ApiModelProperty("证件类型")
    private String fieldAE;
    @ApiModelProperty("证件号码")
    private String fieldAF;
    @ApiModelProperty("职业代码")
    private String fieldAG;
    @ApiModelProperty("职业名称")
    private String fieldAH;
    @ApiModelProperty("等级")
    private String fieldAI;
    @ApiModelProperty( "入职日期（yyyy-MM-dd）")
    private String entryOrResignationdate;
//    @ApiModelProperty("计划编码")
//    private String planId;
    @ApiModelProperty( "项目")  //2021-10-18  改为 "项目"
    private String project;
    @ApiModelProperty("详细地址")
    private String homeaddress;
//    @ApiModelProperty("电话")
//    private String hometel;
    @ApiModelProperty("省代码")
    private String provinceCode;
    @ApiModelProperty("市代码")
    private String cityCode;
    @ApiModelProperty("区代码")
    private String countyCode;

}