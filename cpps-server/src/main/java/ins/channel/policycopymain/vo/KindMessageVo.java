package ins.channel.policycopymain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 批改报价返参-险别信息Vo对象
 * <AUTHOR>
 * @date 2021/02/09
 */
@Data
@ApiModel("KindMessageVo对象")
public class KindMessageVo {
    @ApiModelProperty("险别代码")
    private String kindcode;
    @ApiModelProperty("险别名称")
    private String kindname;
    @ApiModelProperty("险别保额变化量（保留两位小数）")
    private String kindchangeamount;
    @ApiModelProperty("险别保费变化量（保留两位小数）")
    private String kindchangepremium;
    @ApiModelProperty("保险计划")
    private String itemNo;
}
