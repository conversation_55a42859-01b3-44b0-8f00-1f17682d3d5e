package ins.channel.policycopymain.vo;

import ins.framework.mybatis.annotations.Column;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 变更人员清单Vo
 * <AUTHOR>
 * @date 2021/02/08
 */
@Data
@ApiModel("PolicyDynamicListVo")
public class PolicyDynamicListVo {

    @ApiModelProperty("方案代码")
    private String fieldAA;
    @ApiModelProperty("雇员姓名")
    private String fieldAB;
    @ApiModelProperty("性别 1-男 2-女")
    private String fieldAC;
    @ApiModelProperty("出生日期（yyyy-MM-dd）")
    private String fieldAD;
    @ApiModelProperty("证件类型")
    private String fieldAE;
    @ApiModelProperty("证件号码")
    private String fieldAF;
    @ApiModelProperty("职业代码")
    private String fieldAG;
    @ApiModelProperty("职业名称")
    private String fieldAH;
    @ApiModelProperty("等级")
    private String fieldAI;
    @ApiModelProperty("批改标志：（I：新增；D:删除）")
    private String editType;
    @ApiModelProperty("每人保费变化量（保留两位小数）")
    private String changeperpremium;
    @ApiModelProperty( "约定月薪")
    private String monthPay;
    @ApiModelProperty( "项目")
    private String project;
    @ApiModelProperty( "入职日期（yyyy-MM-dd）")
    private String entrydate;
    @ApiModelProperty( "离职日期 (yyyy-MM-dd）")
    private String resignationdate;
    @ApiModelProperty( "生效日期 （yyyy-MM-dd）)")
    private String effectivedate;
    @ApiModelProperty( "备注")
    private String remark;
    @ApiModelProperty( "雇员序号")
    private String listseqno;
    @ApiModelProperty("详细地址")
    private String homeaddress;
    @ApiModelProperty("电话")
    private String hometel;
    @ApiModelProperty("省代码")
    private String provinceCode;
    @ApiModelProperty("市代码")
    private String cityCode;
    @ApiModelProperty("区代码")
    private String countyCode;
    @ApiModelProperty("所属部门")
    private String department;
}