package ins.channel.policycopymain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 批改报价返参-方案信息Vo对象
 * <AUTHOR>
 * @date 2021/02/09
 */
@Data
@ApiModel("PolicyplanVo对象")
public class PolicyplanVo {
    @ApiModelProperty("方案")
    private String plan;
    @ApiModelProperty("方案保费变化量（保留两位小数）")
    private String planchangepremium;

}
