package ins.channel.policycopymain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("SettlementInterfaceVo")
public class SettlementInterfaceVo implements Serializable{

        private static final long serialVersionUID = 1L;

            @ApiModelProperty ("批单序号号")
            private String endorSeqNo;
            @ApiModelProperty ("批改申请号")
            private String endorNo;
            @ApiModelProperty ("保单号")
            private String policyNo;
            @ApiModelProperty ("金额")
            private String  delinquentFee;


    }
