package ins.channel.policycopymain.vo;

import ins.channel.gupolicycopymain.po.Gupolicycopymain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ToolsVo {

    @ApiModelProperty("页面操作标识: 0-批单生成 1-补生成批单 3-结算单同步")
    private String flag;
    @ApiModelProperty("批单申请号")
    private String endorNo;
    private Gupolicycopymain gupolicycopymainvo;
}
