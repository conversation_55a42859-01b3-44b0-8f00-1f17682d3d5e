package ins.channel.policycopymain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 批改报价返参-保单信息Vo对象
 * <AUTHOR>
 * @date 2021/02/09
 */
@Data
@ApiModel("mainmessageVo对象")
public class mainmessage {
    @ApiModelProperty("保额变化量（保留两位小数）")
    private String changeamount;
    @ApiModelProperty("保费变化量（保留两位小数）")
    private String changepremium;
}
