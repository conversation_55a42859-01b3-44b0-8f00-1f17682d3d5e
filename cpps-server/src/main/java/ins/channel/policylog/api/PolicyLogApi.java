package ins.channel.policylog.api;

import ins.channel.baseCode.service.BaseCodeService;
import ins.channel.policymain.service.PolicyMainService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @apiNote: 日志存储相关服务
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2021-01-27
 */
@RestController
@RequestMapping("api/policyLog")
@Api(tags = {"PolicyLog"}, description = "日志存储相关服务")
public class PolicyLogApi {
    @Autowired
    private BaseCodeService baseCodeService;
    @Autowired
    private PolicyMainService policyMainService;

//    @PostMapping("queryCodeList")
//    @ApiOperation(value = "查询所有基础码表集合")
//    public ResponseVo<List<BaseCodeVo>> queryCodeList() {
//        List<BaseCodeVo> baseCodeVoList = baseCodeService.queryCodeList();
//        return ResponseVo.ok(baseCodeVoList);
//    }


}
