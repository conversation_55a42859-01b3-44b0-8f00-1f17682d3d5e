package ins.channel.policylog.service;

import ins.channel.gupolicylog.dao.GupolicylogDao;
import ins.channel.gupolicylog.po.Gupolicylog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @apiNote: 日志表管理服务Service
 * @author: Zhou<PERSON><PERSON><PERSON>
 * @date: 2021-01-27
 */
@Service
public class PolicyLogService {

    @Autowired
    private GupolicylogDao gupolicylogDao;
    /**
     * @description: 用于抛出异常后保存日志
     * @param: gupolicylog
     * @author: zhoutaoyu
     * @date: 2021/2/24
     * @return: 
     **/
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveLog(Gupolicylog gupolicylog) {
        gupolicylogDao.insertSelective(gupolicylog);
    }

    /**
     * @description: 用于抛出异常后更新日志
     * @param: gupolicylog
     * @author: zhout<PERSON><PERSON>
     * @date: 2021/2/24
     * @return:
     **/
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateLog(Gupolicylog gupolicylog) {
        gupolicylogDao.updateSelectiveByPrimaryKey(gupolicylog);
    }
}
