package ins.channel.policymain.api;

import com.alibaba.fastjson.JSONObject;
import ins.channel.gupolicycopymain.vo.DeclarationReqVo;
import ins.channel.gupolicymain.vo.PolicyMainReqVo;
import ins.channel.gupolicymain.vo.PolicyMainRespVo;
import ins.channel.policymain.service.PolicyMainService;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: ZhouTaoyu
 * @date: 2021/01/26
 */
@RestController
@RequestMapping("/api/policymain")
@Api(tags = "PolicyMain", description = "最新保单主信息服务")
public class PolicyMainApi {

    @Autowired
    PolicyMainService policyMainService;
    
    /**
     *
     * 保单条件分页查询
     * @param vo 查询条件载体
     * @Return 数据结果集
     * <AUTHOR>
     * @date 2021年02月07日 17:37:37
     */
    @ApiOperation (value = "分页模糊查询保单信息")
    @PostMapping (value = "/pageQueryPolicyByCondition")
    public ResponseVo<PageResult<PolicyMainRespVo>> pageQueryPolicyByCondition(@RequestBody PolicyMainReqVo vo) {
        System.out.println(JSONObject.toJSON(vo));
        PageResult<PolicyMainRespVo> result = policyMainService.pageQueryPolicyByCondition(vo);
        return ResponseVo.ok(result);
    }

    /**
     * 申报查询页面若查询结果集为1则将保单号传给前端
     * @return
     */
    @ApiOperation(value = "申报擦查询的保单号")
    @PostMapping(value = "/searchPolicyNo")
    public ResponseVo<String> searchPolicyNo(@RequestBody DeclarationReqVo vo){
        String policyNumber = policyMainService.searchPolicyNo(vo);
        return ResponseVo.ok(policyNumber);
    }

}
