package ins.channel.policymain.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import ins.channel.code.dao.GgcodeDao;
import ins.channel.code.po.Ggcode;
import ins.channel.code.po.GgcodeSearch;
import ins.channel.config.UrlConfig;
import ins.channel.gsclientmain.dao.GsclientmainDao;
import ins.channel.gsclientmain.po.Gsclientmain;
import ins.channel.gupolicycoinsurance.dao.GuPolicyCoinsuranceDtoMapper;
import ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDto;
import ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDtoExample;
import ins.channel.gupolicycopyemployerslist.dao.GupolicycopyemployerslistDao;
import ins.channel.gupolicycopyemployerslist.po.Gupolicycopyemployerslist;
import ins.channel.gupolicycopyemployersplan.dao.GupolicycopyemployersplanDao;
import ins.channel.gupolicycopyemployersplan.po.Gupolicycopyemployersplan;
import ins.channel.gupolicycopyholder.dao.GupolicycopyholderDao;
import ins.channel.gupolicycopyholder.po.Gupolicycopyholder;
import ins.channel.gupolicycopyinsured.dao.GupolicycopyinsuredDao;
import ins.channel.gupolicycopyinsured.po.Gupolicycopyinsured;
import ins.channel.gupolicycopyitemaccilist.dao.GupolicycopyitemaccilistDao;
import ins.channel.gupolicycopyitemaccilist.po.Gupolicycopyitemaccilist;
import ins.channel.gupolicycopyitemkind.dao.GupolicycopyitemkindDao;
import ins.channel.gupolicycopyitemkind.po.Gupolicycopyitemkind;
import ins.channel.gupolicycopymain.dao.GupolicycopymainDao;
import ins.channel.gupolicycopymain.po.Gupolicycopymain;
import ins.channel.gupolicycopymain.vo.DeclarationReqVo;
import ins.channel.gupolicycopymain.vo.DeclarationRespVo;
import ins.channel.gupolicycopymainbasic.dao.GupolicycopymainbasicDao;
import ins.channel.gupolicycopymainbasic.po.Gupolicycopymainbasic;
import ins.channel.gupolicyemployerslist.dao.GupolicyemployerslistDao;
import ins.channel.gupolicyemployerslist.po.Gupolicyemployerslist;
import ins.channel.gupolicyemployerslist.vo.EmployerslistQueryReqVo;
import ins.channel.gupolicyemployerslist.vo.EmployerslistQueryRespVo;
import ins.channel.gupolicyemployersplan.dao.GupolicyemployersplanDao;
import ins.channel.gupolicyemployersplan.po.Gupolicyemployersplan;
import ins.channel.gupolicyholder.dao.GupolicyholderDao;
import ins.channel.gupolicyholder.po.Gupolicyholder;
import ins.channel.gupolicyinsured.dao.GupolicyinsuredDao;
import ins.channel.gupolicyinsured.po.Gupolicyinsured;
import ins.channel.gupolicyitemaccilist.dao.GupolicyitemaccilistDao;
import ins.channel.gupolicyitemaccilist.po.Gupolicyitemaccilist;
import ins.channel.gupolicyitemkind.dao.GupolicyitemkindDao;
import ins.channel.gupolicyitemkind.po.Gupolicyitemkind;
import ins.channel.gupolicylog.dao.GupolicylogDao;
import ins.channel.gupolicylog.po.Gupolicylog;
import ins.channel.gupolicymain.dao.GupolicymainDao;
import ins.channel.gupolicymain.po.Gupolicymain;
import ins.channel.gupolicymain.vo.PolicyMainReqVo;
import ins.channel.gupolicymain.vo.PolicyMainRespVo;
import ins.channel.gupolicymainbasic.dao.GupolicymainbasicDao;
import ins.channel.gupolicymainbasic.po.Gupolicymainbasic;
import ins.channel.policymain.vo.GsClientMainDto;
import ins.channel.policymain.vo.GuPolicyDynamicListDto;
import ins.channel.policymain.vo.GuPolicyItemAcciDto;
import ins.channel.policymain.vo.GuPolicyItemAcciListDto;
import ins.channel.policymain.vo.GuPolicyItemKindDto;
import ins.channel.policymain.vo.GuPolicyMainDto;
import ins.channel.policymain.vo.GuPolicyRelatedPartyDto;
import ins.channel.policymain.vo.GuPolicyRiskDto;
import ins.channel.policymain.vo.GuPolicyRiskRelatedPartyDto;
import ins.channel.policymain.vo.PolicyDtoResponse;
import ins.channel.policymain.vo.PolicySyncQueryRespVo;
import ins.channel.policymain.vo.PolicySyncRequestVo;
import ins.channel.support.constant.BusinessNoType;
import ins.channel.support.service.BusinessNoService;
import ins.framework.exception.BusinessException;
import ins.framework.exception.PermissionException;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.platform.common.PageResult;
import ins.platform.common.SysUser;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.DateUtils;
import ins.platform.utils.HttpClientUtils;
import ins.platform.utils.PageHelper;
import ins.platform.utils.SessionHelper;
import ins.platform.utils.UserPermitDataHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: 最新保单主信息Service层
 * @author: ZhouTaoyu
 * @date: 2021/1/26
 **/
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class PolicyMainService {
    @Autowired
    private UrlConfig urlConfig;
    @Autowired
    private GupolicylogDao gupolicylogDao;
    @Autowired
    private GupolicymainDao gupolicymainDao;
    @Autowired
    private GupolicymainbasicDao gupolicymainbasicDao;
    @Autowired
    private GupolicyemployersplanDao gupolicyemployersplanDao;
    @Autowired
    private GupolicyemployerslistDao gupolicyemployerslistDao;
    @Autowired
    private GupolicyitemkindDao gupolicyitemkindDao;
    @Autowired
    private GupolicyholderDao gupolicyholderDao;
    @Autowired
    private GupolicyinsuredDao gupolicyinsuredDao;
    @Autowired
    private GupolicycopymainDao gupolicycopymainDao;
    @Autowired
    private GupolicycopymainbasicDao gupolicycopymainbasicDao;
    @Autowired
    private GupolicycopyemployersplanDao gupolicycopyemployersplanDao;
    @Autowired
    private GupolicycopyemployerslistDao gupolicycopyemployerslistDao;
    @Autowired
    private GupolicycopyitemkindDao gupolicycopyitemkindDao;
    @Autowired
    private GupolicycopyholderDao gupolicycopyholderDao;
    @Autowired
    private GupolicycopyinsuredDao gupolicycopyinsuredDao;
    @Autowired
    private BusinessNoService businessNoService;
    @Autowired
    private GsclientmainDao gsclientmainDao;

    @Autowired
    private GupolicyitemaccilistDao gupolicyitemaccilistDao;


    @Autowired
    private GupolicycopyitemaccilistDao gupolicycopyitemaccilistDao;


    @Autowired
    private GuPolicyCoinsuranceDtoMapper guPolicyCoinsuranceDtoMapper;

    @Autowired
    private GupolicymainDao policymainDao;

    @Autowired
    private GgcodeDao ggcodeDao;

    private static final int length = 500;

    /**
     * @description: 根据传入的创新业务标识与保单号集合, 同步核心最新保单信息
     * @param: vo
     * @param: isAutoTask  是否为定时任务运行 0-否 1-是
     * @author: zhoutaoyu
     * @date: 2021/1/26
     * @return:
     **/
    public PolicySyncQueryRespVo syncPolicy(PolicySyncRequestVo requestVo, int isAutoTask) {
        PolicySyncQueryRespVo respVo = new PolicySyncQueryRespVo();
        //定时任务或批量同步时,需要先根据传入的创新业务标识,查询核心待同步保单清单
        if (isAutoTask == 1 || "1".equals(requestVo.getFlag())) {
            //处理入参日期
            List<Date> issuedatelist = requestVo.getIssuedatelist();
            Assert.isTrue(!CollectionUtils.isEmpty(issuedatelist), "出单日期区间不能为空");
            Assert.isTrue(issuedatelist.size() == 2, "出单日期区间长度有误!");
            Assert.notNull(issuedatelist.get(0), "出单日期起期不能为空!");
            Assert.notNull(issuedatelist.get(1), "出单日期止期不能为空!");
            Assert.isTrue(issuedatelist.get(0).compareTo(issuedatelist.get(1)) <= 0, "出单日期起期需小于等于止期!");
            Assert.isTrue(issuedatelist.get(0).before(issuedatelist.get(1)), "出单日期起期需小于止期!");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.DATE, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date now = calendar.getTime();
            Assert.isTrue(issuedatelist.get(1).before(now), "止期最晚不得超过当天");
            //设置出单日期起期
            calendar.setTime(issuedatelist.get(0));
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            String issuestartdate = DateUtils.format(calendar.getTime(), DateUtils.PATTERN_DATE_TIME);
            requestVo.setIssuestartdate(issuestartdate);
            //设置出单日期止期
            calendar.setTime(issuedatelist.get(1));
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            String issueenddate = DateUtils.format(calendar.getTime(), DateUtils.PATTERN_DATE_TIME);
            requestVo.setIssueenddate(issueenddate);
            PolicySyncRequestVo clone = BeanCopyUtils.clone(requestVo, PolicySyncRequestVo.class);
            respVo = this.queryPolicyNoEmployer(clone);
            if (!"200".equals(respVo.getStatus())) {
                return respVo;
            }
//            System.out.println("保单信息集合(去重前)条数: " + respVo.getPolicyNolist().size());

            requestVo = removeDuplicationByPolicyNo(respVo, requestVo);
            if (CollectionUtils.isEmpty(requestVo.getPolicylist())) {
                respVo.setStatus("200");
                respVo.setMessage("所选区间内所有保单已存在,无需同步!");
                return respVo;
            }
//            System.out.println("保单信息集合(去重后)条数: " + vo.getPolicylist().size());
        } else { //逐笔入参处理
            String policyno = requestVo.getPolicyno();
            Assert.hasText(policyno, "保单号码不能为空!");
            //查询数据库是否已存在该保单,若存在则直接返回前端提示信息
//            Integer i = gupolicymainDao.selectCountByPolicyNo(policyno);
//            if (i != 0) {
//                respVo.setStatus("200");
//                respVo.setMessage("该保单已存在,无需同步!");
//                return respVo;
//            }
            List<String> list = new ArrayList<>();
            list.add(policyno);
            requestVo.setPolicylist(list);
        }
        //根据传入的保单号集合, 按批次同步核心最新保单信息
        respVo = this.queryPolicyEmployerByBatch(requestVo, 5);
        List<PolicyDtoResponse> policylist = respVo.getPolicylist();
        //同步成功,调用解析接口
        if (!"500".equals(respVo.getStatus())) {
            resolvePolicyInfo(policylist, requestVo.getSurverind());
        }
        respVo.setPolicylist(null);
        return respVo;
    }

    /**
     * @param respVo
     * @param vo
     * @description: 查询最新保单主表, 将已存在的保单信息移除
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/2/4
     * @return:
     */
    private PolicySyncRequestVo removeDuplicationByPolicyNo(PolicySyncQueryRespVo respVo, PolicySyncRequestVo vo) {
        List<String> policyNoList = gupolicymainDao.selectAllPolicyNo();
        List<String> policylist = respVo.getPolicyNolist();
        policylist = policylist.stream()
                .filter(policyNo -> !policyNoList.contains(policyNo))
                .collect(Collectors.toList());
        vo.setPolicylist(policylist);
        return vo;
    }

    /**
     * @description: 解析查询核心库得到的保单信息, 同步生成对应最新保单信息表数据
     * @param: policylist 待解析保单信息集合
     * @author: zhoutaoyu
     * @date: 2021/2/3
     * @return:
     **/
    public void resolvePolicyInfo(List<PolicyDtoResponse> policylist, String riskCode) {
        List<Gupolicymain> newPolicyMainList = new ArrayList<>();
        List<Gupolicymainbasic> newPolicyMainBasicList = new ArrayList<>();
        List<Gupolicyemployersplan> newPolicyemployersplanList = new ArrayList<>();
        List<Gupolicyemployersplan> newPolicyemployersplanAllList = new ArrayList<>();
        List<Gupolicyemployerslist> newPolicyemployerslistAll = new ArrayList<>();
        List<Gupolicyitemkind> newPolicyitemkindAllList = new ArrayList<>();
        List<Gupolicyholder> newPolicyholderList = new ArrayList<>();
        List<Gupolicyinsured> newPolicyinsuredAllList = new ArrayList<>();
        List<Gsclientmain> gsClientMainList = new ArrayList<>();
        List<Gupolicyemployerslist> newPolicyEmployerslists = new ArrayList<>();
        List<GuPolicyCoinsuranceDto> newPolicyCoinsuranceDtoList = new ArrayList<>();
        List<String> policyNoList = new ArrayList<>();
        for (PolicyDtoResponse policyDtoResponse : policylist) {
            //处理gupolicymain 表数据
            Gupolicymain newPolicyMain = createPolicyMain(policyDtoResponse);
            newPolicyMainList.add(newPolicyMain);
            policyNoList.add(newPolicyMain.getPolicyNo());
            //处理gupolicymainbasic 表数据
            Gupolicymainbasic newPolicymainbasic = createPolicyMainBasic(policyDtoResponse);
            newPolicyMainBasicList.add(newPolicymainbasic);
            //处理gupolicyemployersplan 表数据
            if (riskCode.equals("1219")) {
                if("1".equals(policyDtoResponse.getNewFlag1219())){
                    //新单
                    newPolicyemployersplanList = createPolicyListByItemAcciList1219(policyDtoResponse);
                }else{
                    newPolicyemployersplanList = createPolicyEmployersplan(policyDtoResponse);
                }
            } else if (riskCode.equals("1144")) {
                newPolicyemployersplanList = createPolicyListByItemAcciList1144(policyDtoResponse);
            }
            if (!CollectionUtils.isEmpty(newPolicyemployersplanList)) {
                newPolicyemployersplanAllList.addAll(newPolicyemployersplanList);
            }
            //处理gupolicyemployerslist 表数据
            if (riskCode.equals("1219")) {
                newPolicyEmployerslists = createPolicyEmployerslist(policyDtoResponse, newPolicyemployersplanList);
            }else if (riskCode.equals("1144")){
                newPolicyEmployerslists= createPolicyEmployerslist1144(policyDtoResponse);
            }
            if (!CollectionUtils.isEmpty(newPolicyEmployerslists)) {
                newPolicyemployerslistAll.addAll(newPolicyEmployerslists);
            }

            //处理gupolicyitemkind 表数据
            List<Gupolicyitemkind> newPolicyitemkindList = createPolicyitemkind(policyDtoResponse);
            if (!CollectionUtils.isEmpty(newPolicyitemkindList)) {
                newPolicyitemkindAllList.addAll(newPolicyitemkindList);
            }

            //处理gupolicyholder 表数据
            Gupolicyholder newPolicyholder = createPolicyholder(policyDtoResponse);
            newPolicyholderList.add(newPolicyholder);

            //处理gupolicyinsured 表数据
            List<Gupolicyinsured> newPolicyinsuredList = createPolicyinsured(policyDtoResponse);
            if (!CollectionUtils.isEmpty(newPolicyinsuredList)) {
                newPolicyinsuredAllList.addAll(newPolicyinsuredList);
            }

            gsClientMainList = createGsClientMain(policyDtoResponse);
            //
            List<GuPolicyCoinsuranceDto> guPolicyCoinsuranceDtoList = policyDtoResponse.getGuPolicyCoinsuranceDtoList();
            for (GuPolicyCoinsuranceDto guPolicyCoinsuranceDto : guPolicyCoinsuranceDtoList) {
                newPolicyCoinsuranceDtoList.add(guPolicyCoinsuranceDto);
            }
        }
        //批量插入最新保单联共保信息表
        createBatchList(newPolicyCoinsuranceDtoList, GuPolicyCoinsuranceDto.class, length, policyNoList);
        //批量插入最新保单主信息表
        createBatchList(newPolicyMainList, Gupolicymain.class, length, policyNoList);
        //批量插入最新保单基础信息表
        createBatchList(newPolicyMainBasicList, Gupolicymainbasic.class, length, policyNoList);
        //批量插入最新保单雇主计划表
        createBatchList(newPolicyemployersplanAllList, Gupolicyemployersplan.class, length, policyNoList);
        //批量插入最新保单雇员清单表
        createBatchList(newPolicyemployerslistAll, Gupolicyemployerslist.class, length, policyNoList);
        //批量插入最新保单计划险别表
        createBatchList(newPolicyitemkindAllList, Gupolicyitemkind.class, length, policyNoList);
        //批量插入最新保单投保人信息表
        createBatchList(newPolicyholderList, Gupolicyholder.class, length, policyNoList);
        //批量插入最新保单被保险人信息表
        createBatchList(newPolicyinsuredAllList, Gupolicyinsured.class, length, policyNoList);

        //处理轨迹表数据
        //处理gupolicycopymain 表数据
        List<Gupolicycopymain> newPolicyCopyMainList = createPolicyCopyMain(newPolicyMainList);
        //批量插入保单主信息轨迹表
        createBatchList(newPolicyCopyMainList, Gupolicycopymain.class, length, policyNoList);

        //处理gupolicycopymainbasic 表数据
        List<Gupolicycopymainbasic> newPolicyCopyMainBasicList = BeanCopyUtils.cloneList(newPolicyMainBasicList, Gupolicycopymainbasic.class);
        //批量插入基础信息轨迹表
        createBatchList(newPolicyCopyMainBasicList, Gupolicycopymainbasic.class, length, policyNoList);

        //处理gupolicycopyemployersplan 表数据
        List<Gupolicycopyemployersplan> newPolicyCopyemployersplanList = createPolicycopyemployersplan(newPolicyemployersplanAllList);
        //BeanCopyUtils.cloneList(newPolicyemployersplanAllList, Gupolicycopyemployersplan.class);
        //批量插入保单雇主计划轨迹表
        createBatchList(newPolicyCopyemployersplanList, Gupolicycopyemployersplan.class, length, policyNoList);

        //处理gupolicycopyemployerslist表数据
        List<Gupolicycopyemployerslist> newPolicyCopyemployerslistAll = createPolicycopyemployerslist(newPolicyemployerslistAll);
        //BeanCopyUtils.cloneList(newPolicyemployerslistAll, Gupolicycopyemployerslist.class);
        //批量插入保单雇员清单轨迹表
        createBatchList(newPolicyCopyemployerslistAll, Gupolicycopyemployerslist.class, length, policyNoList);

        //处理gupolicycopyitemkind表数据
        List<Gupolicycopyitemkind> newPolicyCopyitemkindAllList = BeanCopyUtils.cloneList(newPolicyitemkindAllList, Gupolicycopyitemkind.class);
        //批量插入保单计划险别轨迹表
        createBatchList(newPolicyCopyitemkindAllList, Gupolicycopyitemkind.class, length, policyNoList);

        //处理Gupolicycopyholder表数据
        List<Gupolicycopyholder> newPolicyCopyholderList = BeanCopyUtils.cloneList(newPolicyholderList, Gupolicycopyholder.class);
        //批量插入保单投保人信息轨迹表
        createBatchList(newPolicyCopyholderList, Gupolicycopyholder.class, length, policyNoList);

        //处理Gupolicycopyinsured表数据
        List<Gupolicycopyinsured> newPolicyCopyinsuredAllList = BeanCopyUtils.cloneList(newPolicyinsuredAllList, Gupolicycopyinsured.class);
        //批量插入保单被保险人信息轨迹表
        createBatchList(newPolicyCopyinsuredAllList, Gupolicycopyinsured.class, length, policyNoList);
        //批量插入客户信息
        createBatchList(gsClientMainList, Gsclientmain.class, length, policyNoList);

        //批量插入被保险人信息表 ————1144
//        List<Gupolicycopyitemaccilist> newPolicyCopyItemAcciList = createPolicyCopyItemAcciList(newGupolicyitemaccilistList);
//        createBatchList(newPolicyCopyItemAcciList, Gupolicycopyitemaccilist.class, length, policyNoList);

    }


    /**
     * @description: 处理gupolicycopymain 表数据
     * @param: newPolicyMainList
     * @author: zhoutaoyu
     * @date: 2021/3/4
     * @return:
     **/
    private List<Gupolicycopymain> createPolicyCopyMain(List<Gupolicymain> newPolicyMainList) {
        List<Gupolicycopymain> gupolicycopymainList = BeanCopyUtils.cloneList(newPolicyMainList, Gupolicycopymain.class);
        for (Gupolicycopymain gupolicycopymain : gupolicycopymainList) {
            //批单序号（核心）,默认为 000
            gupolicycopymain.setEndorseqno("000");
            //申报单号默认为 保单号
            gupolicycopymain.setEndorNo(gupolicycopymain.getPolicyNo());
            //批单编号,默认为 0
            gupolicycopymain.setEndorserialno(BigDecimal.ZERO);
            //结算状态 0-未结算 1-已生成结算单 2-已结算  默认为0
            gupolicycopymain.setSettleStatus("0");
        }
        return gupolicycopymainList;
    }

    /**
     * @description: gupolicycopyitemaccilist 表数据
     * @param: newPolicyMainList
     * @author: zhoutaoyu
     * @date: 2021/3/4
     * @return:
     **/
    private List<Gupolicycopyitemaccilist> createPolicyCopyItemAcciList(List<Gupolicyitemaccilist> gupolicycopyitemaccilistList) {
        List<Gupolicycopyitemaccilist> gupolicycopyitemaccilists = BeanCopyUtils.cloneList(gupolicycopyitemaccilistList, Gupolicycopyitemaccilist.class);
        for (Gupolicycopyitemaccilist gupolicycopyitemaccilist : gupolicycopyitemaccilists) {
            //批单序号（核心）,默认为 000
            gupolicycopyitemaccilist.setEndorseqno("000");
        }
        return gupolicycopyitemaccilists;
    }


    /**
     * @description: 处理Gupolicycopyemployersplan 表数据
     * @param: newPolicyemployersplanAllList
     * @author: zhoutaoyu
     * @date: 2021/3/4
     * @return:
     **/
    private List<Gupolicycopyemployersplan> createPolicycopyemployersplan(List<Gupolicyemployersplan> newPolicyemployersplanAllList) {
        List<Gupolicycopyemployersplan> gupolicycopyemployersplanList = BeanCopyUtils.cloneList(newPolicyemployersplanAllList, Gupolicycopyemployersplan.class);
        for (Gupolicycopyemployersplan gupolicycopyemployersplan : gupolicycopyemployersplanList) {

            //申报单号默认为 保单号
            gupolicycopyemployersplan.setEndorNo(gupolicycopyemployersplan.getPolicyNo());

        }
        return gupolicycopyemployersplanList;
    }

    /**
     * @description: 处理gupolicycopyemployerslist 表数据
     * @param: newPolicyemployerslistAll
     * @author: zhoutaoyu
     * @date: 2021/3/4
     * @return:
     **/
    private List<Gupolicycopyemployerslist> createPolicycopyemployerslist(List<Gupolicyemployerslist> newPolicyemployerslistAll) {
        List<Gupolicycopyemployerslist> gupolicycopyemployerslistList = BeanCopyUtils.cloneList(newPolicyemployerslistAll, Gupolicycopyemployerslist.class);
        for (Gupolicycopyemployerslist gupolicycopyemployerslist : gupolicycopyemployerslistList) {

            //申报单号默认为 保单号
            gupolicycopyemployerslist.setEndorNo(gupolicycopyemployerslist.getPolicyNo());

        }
        return gupolicycopyemployerslistList;
    }


    /**
     * @description: 解析保单被保人信息表数据
     * @param: policyDtoResponse
     * @author: zhoutaoyu
     * @date: 2021/2/7
     * @return:
     **/
    private List<Gupolicyinsured> createPolicyinsured(PolicyDtoResponse policyDtoResponse) {
        //获取核心保单计划险别表数据集合
        List<GuPolicyRiskRelatedPartyDto> guPolicyRiskRelatedPartyDtoList = policyDtoResponse.getGuPolicyRiskRelatedPartyDtoList();
        if (CollectionUtils.isEmpty(guPolicyRiskRelatedPartyDtoList)) {
            return null;
        }
        List<Gupolicyinsured> gupolicyinsuredList = new ArrayList<>();
        for (GuPolicyRiskRelatedPartyDto guPolicyRiskRelatedPartyDto : guPolicyRiskRelatedPartyDtoList) {
            Map<String, String> config = new HashMap<>();
            // 自定义配置
            config.put("policyno", "policyNo");
            config.put("riskcode", "riskCode");
            config.put("serialno", "serialNo");
            config.put("insuredtype", "insuredType");
            config.put("insuredcode", "insuredCode");
            config.put("insuredname", "insuredName");
            config.put("insuredaddress", "insuredAddress");
            config.put("insuredflag", "insuredFlag");
            config.put("identifytype", "identifyType");
            config.put("identifynumber", "identifyNumber");
            config.put("relateserialno", "relateSerialNo");
//            config.put("inputdate", "inputDate");
            log.info("拷贝前：{}", JSON.toJSONString(guPolicyRiskRelatedPartyDto, SerializerFeature.WriteMapNullValue));
            Gupolicyinsured gupolicyinsured = BeanCopyUtils.copy(Gupolicyinsured.class, guPolicyRiskRelatedPartyDto, config);
            //设置入机日期与修改日期
            Date now = new Date();
            gupolicyinsured.setInputDate(now);
            gupolicyinsured.setUpdatesysdate(now);
            //生成ID
            String id = businessNoService.nextNo(BusinessNoType.ID);
            gupolicyinsured.setId(id);
            gupolicyinsuredList.add(gupolicyinsured);
            log.info("拷贝后：{}", gupolicyinsured);
        }
        return gupolicyinsuredList;
    }

    /**
     * @description: 解析保单投保人信息表数据
     * @param: policyDtoResponse
     * @author: zhoutaoyu
     * @date: 2021/2/7
     * @return:
     **/
    private Gupolicyholder createPolicyholder(PolicyDtoResponse policyDtoResponse) {
        //获取核心保单计划险别表数据集合
        List<GuPolicyRelatedPartyDto> guPolicyRelatedPartyDtoList = policyDtoResponse.getGuPolicyRelatedPartyDtoList();
        if (CollectionUtils.isEmpty(guPolicyRelatedPartyDtoList)) {
            return null;
        }
        //获取主表productcode   产品代码
        String productcode = policyDtoResponse.getGuPolicyMainDto().getProductcode();
        //获取主表companycode     保险公司归属机构
        String insurancecompanycode = policyDtoResponse.getGuPolicyMainDto().getCompanycode();
        GuPolicyRelatedPartyDto guPolicyRelatedPartyDto = guPolicyRelatedPartyDtoList.get(0);
        Map<String, String> config = new HashMap<>();
        // 自定义配置
        config.put("policyno", "policyNo");
        config.put("insuredtype", "insuredType");
        config.put("insuredcode", "insuredCode");
        config.put("insuredname", "insuredName");
        config.put("insuredaddress", "insuredAddress");
        config.put("identifytype", "identifyType");
        config.put("identifynumber", "identifyNumber");
        config.put("birthdate", "birthDate");
        config.put("postcode", "postCode");
        config.put("homephone", "homePhone");
        config.put("bankname", "bankName");
//            config.put("occupationcode", "occupationCode");
//            config.put("inputdate", "inputDate");
        log.info("拷贝前：{}", JSON.toJSONString(guPolicyRelatedPartyDto, SerializerFeature.WriteMapNullValue));
        Gupolicyholder gupolicyholder = BeanCopyUtils.copy(Gupolicyholder.class, guPolicyRelatedPartyDto, config);
        //productcode   产品代码 取主表该字段值
        gupolicyholder.setProductcode(productcode);
        //insurancecompanycode 保险公司归属机构 取主表companycode
        gupolicyholder.setInsurancecompanycode(insurancecompanycode);
        //设置入机日期与修改日期
        Date now = new Date();
        gupolicyholder.setInputDate(now);
        gupolicyholder.setUpdatesysdate(now);
        //生成ID
        String id = businessNoService.nextNo(BusinessNoType.ID);
        gupolicyholder.setId(id);
        log.info("拷贝后：{}", gupolicyholder);
        return gupolicyholder;
    }

    /**
     * @description: 解析保单雇员清单表数据
     * @param: policyDtoResponse
     * @author: zhoutaoyu
     * @date: 2021/2/5
     * @return:
     **/
    private List<Gupolicyitemkind> createPolicyitemkind(PolicyDtoResponse policyDtoResponse) {
        //获取核心保单计划险别表数据集合
        List<GuPolicyItemKindDto> gupolicyItemkindDtoList = policyDtoResponse.getGupolicyItemkindDtoList();
        if (CollectionUtils.isEmpty(gupolicyItemkindDtoList)) {
            return null;
        }
        List<Gupolicyitemkind> gupolicyitemkindList = new ArrayList<>();
        for (GuPolicyItemKindDto guPolicyItemKindDto : gupolicyItemkindDtoList) {
            Map<String, String> config = new HashMap<>();
            // 自定义配置
            config.put("policyno", "policyNo");
            config.put("riskcode", "riskCode");
            config.put("itemkindno", "itemKindNo");
            config.put("itemno", "itemNo");
            config.put("itemcode", "itemCode");
            config.put("kindcode", "kindCode");
            config.put("kindname", "kindName");
            config.put("modecode", "modeCode");
            config.put("modename", "modeName");
            config.put("startdate", "startDate");
            config.put("enddate", "endDate");
            config.put("sumvalue", "sumValue");
            config.put("rateperiod", "ratePeriod");
            config.put("shortrateflag", "shortrateFlag");
            config.put("shortrate", "shortRate");
            config.put("netpremium", "netPremium");
            config.put("deductiblerate", "deductibleRate");
            config.put("liabcode", "liabCode");
            config.put("unitpremium", "unitPremium");
//            config.put("inputdate", "inputDate");
//            log.info("拷贝前：{}", JSON.toJSONString(guPolicyItemKindDto, SerializerFeature.WriteMapNullValue));
            Gupolicyitemkind gupolicyitemkind = BeanCopyUtils.copy(Gupolicyitemkind.class, guPolicyItemKindDto, config);
            //设置险别总保额本位币金额变化量
            gupolicyitemkind.setSuminsuredcny(gupolicyitemkind.getSuminsured());
            //设置入机日期与修改日期
            Date now = new Date();
            gupolicyitemkind.setInputDate(now);
            gupolicyitemkind.setUpdatesysdate(now);
            //生成ID
            String id = businessNoService.nextNo(BusinessNoType.ID);
            gupolicyitemkind.setId(id);
            gupolicyitemkindList.add(gupolicyitemkind);
//            log.info("拷贝后：{}", gupolicyitemkind);
        }
        return gupolicyitemkindList;
    }

    /**
     * @description: 解析保单雇员清单表数据
     * @param: policyDtoResponse
     * @author: zhoutaoyu
     * @date: 2021/2/5
     * @return:
     **/
    private List<Gupolicyemployerslist> createPolicyEmployerslist(PolicyDtoResponse policyDtoResponse, List<Gupolicyemployersplan> newPolicyemployersplanList) {
        //获取核心动态标的表中 dynamictargettype='5' 的数据集合  (雇员清单)
        List<GuPolicyDynamicListDto> guPolicyDynamicListDtoList = policyDtoResponse.getGuPolicyDynamicListDtoList2();
        if (CollectionUtils.isEmpty(guPolicyDynamicListDtoList)) {
            return null;
        }
        //获取核心保单主表币别
        String currency = policyDtoResponse.getGuPolicyMainDto().getCurrency();
        //获取核心保单险种表起/终保日期
        GuPolicyRiskDto guPolicyRiskDto = policyDtoResponse.getGuPolicyRiskDtoList().get(0);
        Date startdate = guPolicyRiskDto.getStartdate();
        Date enddate = guPolicyRiskDto.getEnddate();
        List<Gupolicyemployerslist> gupolicyemployerslists = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        for (GuPolicyDynamicListDto guPolicyDynamicListDto : guPolicyDynamicListDtoList) {
            Map<String, String> config = new HashMap<>();
            // 自定义配置
            config.put("policyno", "policyNo");
            config.put("itemno", "itemNo");
            config.put("flag", "targetflag");
//            config.put("occupationcode", "occupationCode");
//            config.put("enddate", "endDate");
//            config.put("inputdate", "inputDate");
//            log.info("拷贝前：{}", JSON.toJSONString(guPolicyDynamicListDto, SerializerFeature.WriteMapNullValue));
            String itemNo = "";
            BigDecimal itemNoBg;
            if("1".equals(policyDtoResponse.getNewFlag1219())){
                itemNo = guPolicyDynamicListDto.getItemno()+"";
                itemNoBg = new BigDecimal(guPolicyDynamicListDto.getItemno()+"");
            }else {
                itemNo = guPolicyDynamicListDto.getFieldaa();
                itemNoBg = new BigDecimal(guPolicyDynamicListDto.getFieldaa());
            }
            Gupolicyemployerslist gupolicyemployerslist = BeanCopyUtils.copy(Gupolicyemployerslist.class, guPolicyDynamicListDto, config);
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldaa())) { //计划编码
                gupolicyemployerslist.setPlanid(itemNo);
                gupolicyemployerslist.setItemNo(itemNoBg);
            }
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldab())) { //雇员姓名
                gupolicyemployerslist.setEmpname(guPolicyDynamicListDto.getFieldab());
            }
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldac())) { //雇员性别
                gupolicyemployerslist.setEmpsex(guPolicyDynamicListDto.getFieldac());
            }
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldad())) { //雇员生日
                gupolicyemployerslist.setEmpbirthday(guPolicyDynamicListDto.getFieldad());
            }
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldae())) { //雇员证件类型
                gupolicyemployerslist.setEmpidentifytype(guPolicyDynamicListDto.getFieldae());
            }
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldaf())) { //雇员证件号
                gupolicyemployerslist.setEmpidentifynumber(guPolicyDynamicListDto.getFieldaf());
            }
            // 由于核心雇员清单模板调整，兼容新版格式 —— fieldag 职业/工种 fiedlah 职业等级 fiedai null
            if (StringUtils.isEmpty(guPolicyDynamicListDto.getFieldai())) {
                if (StringUtils.isEmpty(guPolicyDynamicListDto.getFieldag()) || StringUtils.isEmpty(guPolicyDynamicListDto.getFieldah())) {
                    throw new BusinessException("职业/工种或职业等级数据缺失!");
                }
                // 不再兼容复制旧保单出的新单 fieldag 职业代码 fiedlah 职业名称 fiedai null
                //根据职业/工种和等级查询 ggcode
                GgcodeSearch ggcodeSearch = new GgcodeSearch();
                ggcodeSearch.setCodeCname(guPolicyDynamicListDto.getFieldag());
                ggcodeSearch.setRemark(guPolicyDynamicListDto.getFieldah());
                ggcodeSearch.setValidInd("1");
                ggcodeSearch.setCodeType("JobCodeNew2022"); //旧编码
                Page<Ggcode> results = ggcodeDao.searchPage(new PageParam(), ggcodeSearch);
                // 新的匹配不到用旧的匹配
                if (CollectionUtils.isEmpty(results)) {
                    ggcodeSearch.setCodeType("JobCode1219");//新编码
                    results = ggcodeDao.searchPage(new PageParam(), ggcodeSearch);
                    if (CollectionUtils.isEmpty(results)){
                        throw new BusinessException("未匹配到相应的code");
                    }
                }
                Ggcode ggcode = results.get(0); //存在多个,不唯一
                gupolicyemployerslist.setOccupationCode(ggcode.getCodeCode());
                gupolicyemployerslist.setOccupationname(ggcode.getCodeCname());
                gupolicyemployerslist.setOccupationlevel(ggcode.getRemark());
            } else {
                // 旧清单模板
                if (StringUtils.hasText(guPolicyDynamicListDto.getFieldag())) { //职业代码
                    gupolicyemployerslist.setOccupationCode(guPolicyDynamicListDto.getFieldag());
                }
                if (StringUtils.hasText(guPolicyDynamicListDto.getFieldah())) { //职业名称
                    gupolicyemployerslist.setOccupationname(guPolicyDynamicListDto.getFieldah());
                }
                if (StringUtils.hasText(guPolicyDynamicListDto.getFieldai())) { //职业等级
                    gupolicyemployerslist.setOccupationlevel(guPolicyDynamicListDto.getFieldai());
                }
            }
            //处理每人毛保费
            //获取法律合计费用
            BigDecimal legalsumpremium = newPolicyemployersplanList.stream()
                    .filter(p -> p.getLegalsumpremium() != null
                            && p.getListseqno().compareTo(itemNoBg) == 0)
                    .map(Gupolicyemployersplan::getLegalsumpremium)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //获取伤亡医疗合计费用
            BigDecimal personsumpremium = newPolicyemployersplanList.stream()
                    .filter(p -> p.getPersonsumpremium() != null
                            && p.getListseqno().compareTo(itemNoBg) == 0)
                    .map(Gupolicyemployersplan::getPersonsumpremium)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //获取投保人数
            String employees = newPolicyemployersplanList.stream()
                    .filter(p -> p.getEmployees() != null
                            && p.getListseqno().compareTo(itemNoBg) == 0)
                    .map(Gupolicyemployersplan::getEmployees)
                    .findFirst().orElse("0.00");
            //设置每人毛保费 = (法律合计费用+伤亡医疗合计费用) / 投保人数
            BigDecimal emppremium = (legalsumpremium.add(personsumpremium)).divide(new BigDecimal(employees), RoundingMode.HALF_UP);
            gupolicyemployerslist.setEmppremium(emppremium);
            //currency 从主表中获取
            gupolicyemployerslist.setCurrency(currency);
            //entrydate 入职日期  保单时存起保日期
            gupolicyemployerslist.setEntrydate(startdate);
            //resignationdate 离职日期  保单时存终保日期
//            gupolicyemployerslist.setResignationdate(enddate);
            //effectivedate 生效日期
            gupolicyemployerslist.setEffectivedate(startdate);
            //enddate 终保日期
            gupolicyemployerslist.setEndDate(enddate);
            //dynamictargettype  默认存"5"
            gupolicyemployerslist.setDynamictargettype(guPolicyDynamicListDto.getDynamiclisttype());
            //listbelongind  默认0
            gupolicyemployerslist.setListbelongind("0");
            //targetflag 标的标识 U-更新 I-插入 D-删除  默认为 I
            gupolicyemployerslist.setTargetflag("I");
            //monthPay 约定月薪  默认为0
            gupolicyemployerslist.setMonthPay(BigDecimal.ZERO);
            //设置入机日期与修改日期
            Date now = new Date();
            gupolicyemployerslist.setInputDate(now);
            gupolicyemployerslist.setUpdatesysdate(now);
            //生成ID
            String id = businessNoService.nextNo(BusinessNoType.ID);
            gupolicyemployerslist.setId(id);
            //处理非原始保单的雇员信息
            try {
                if (StringUtils.hasText(guPolicyDynamicListDto.getValiddate())) { //生效日期
                    gupolicyemployerslist.setEffectivedate(dateFormat.parse(guPolicyDynamicListDto.getValiddate()));
                }
                if (StringUtils.hasText(guPolicyDynamicListDto.getEndordate())) { //入职日期
                    gupolicyemployerslist.setEntrydate(dateFormat.parse(guPolicyDynamicListDto.getEndordate()));
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }

            if (StringUtils.hasText(guPolicyDynamicListDto.getGrosspremium())) {
                gupolicyemployerslist.setEmppremium(new BigDecimal(guPolicyDynamicListDto.getGrosspremium()).setScale(6, BigDecimal.ROUND_HALF_UP));;
            }

            gupolicyemployerslists.add(gupolicyemployerslist);
        }
        return gupolicyemployerslists;
    }

    private List<Gupolicyemployerslist> createPolicyEmployerslist1144(PolicyDtoResponse policyDtoResponse) {
        List<GuPolicyItemAcciListDto> itemAcciList = policyDtoResponse.getGuPolicyItemAcciListDtoList();
        List<GuPolicyItemAcciDto> acciDtoList = policyDtoResponse.getGuPolicyItemAcciDtoList();
        if (CollectionUtils.isEmpty(itemAcciList) || CollectionUtils.isEmpty(acciDtoList)) {
            return null;
        }
        List<Gupolicyemployerslist> employersList = new ArrayList<>();
        Gupolicyemployerslist employers = null;
        String occupationCode = acciDtoList.get(0).getOccupationcode(); //职业代码
        String occupationName = acciDtoList.get(0).getOccupation(); //职业名称
        String occupationLevel = acciDtoList.get(0).getOccupationlevel(); //职业等级
        Date startDate = policyDtoResponse.getGuPolicyRiskDtoList().get(0).getStartdate();//起保日期
        Date endDate = policyDtoResponse.getGuPolicyRiskDtoList().get(0).getEnddate();//终保日期
        BigDecimal unitinsured = new BigDecimal(policyDtoResponse.getGuPolicyItemAcciDtoList().get(0).getUnitinsured());
        BigDecimal emppremium = new BigDecimal(policyDtoResponse.getGuPolicyItemAcciDtoList().get(0).getUnitpremium());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date now = new Date();
        for (GuPolicyItemAcciListDto dto : itemAcciList){
            employers = new Gupolicyemployerslist();
            employers.setPolicyNo(dto.getPolicyno());
            employers.setPlanid(dto.getProjectcode());
            employers.setItemNo(new BigDecimal(dto.getItemno()));
            employers.setDynamictargettype("5");
            employers.setListseqno(new BigDecimal(dto.getClientno()));
            employers.setTargetflag("I");
            employers.setEmpname(dto.getClientcname());
            employers.setEmpsex(dto.getSex());
            employers.setEmpbirthday(format.format(dto.getBirthday()));
            employers.setEmpidentifytype(dto.getIdentifytypea());
            employers.setEmpidentifynumber(dto.getIdentifynoa());
            employers.setOccupationCode(occupationCode);
            employers.setOccupationname(occupationName);
            employers.setOccupationlevel(occupationLevel);
            employers.setEffectivedate(startDate);
            employers.setEndDate(endDate);
            employers.setCurrency(policyDtoResponse.getGuPolicyMainDto().getCurrency());
            employers.setEmppremium(emppremium);
            employers.setEmpinsured(unitinsured);
            employers.setListbelongind("0");
            employers.setEntrydate(startDate);
            employers.setHomeaddress(dto.getHomeaddress());
            employers.setHometel(dto.getHometel());
            employers.setDepartment(dto.getDepartment());
            employers.setProvincecode(dto.getProvincecode());
            employers.setCitycode(dto.getCitycode());
            employers.setCountycode(dto.getCountycode());
            //处理非原始保单的人员信息
            try {
                if (StringUtils.hasText(dto.getEndordate())) {
                    employers.setEntrydate(format.parse(dto.getEndordate()));
                }
                if (StringUtils.hasText(dto.getValiddate())) {
                    employers.setEffectivedate(format.parse(dto.getValiddate()));
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }

            if (dto.getGrosspremium() != null) {
                employers.setEmppremium(BigDecimal.valueOf(dto.getGrosspremium()));
            }
            //生成ID
            String id = businessNoService.nextNo(BusinessNoType.ID);
            employers.setId(id);

            employers.setInputDate(now);
            employers.setUpdatesysdate(now);
            employersList.add(employers);
        }
        return employersList;
    }

    /**
     * @description: 解析保单雇主计划表数据
     * @param: policyDtoResponse
     * @author: zhoutaoyu
     * @date: 2021/2/5
     * @return:
     **/
    private List<Gupolicyemployersplan> createPolicyEmployersplan(PolicyDtoResponse policyDtoResponse) {
        //获取核心动态标的表中 dynamictargettype='10' 的数据集合  (标的物清单)
        List<GuPolicyDynamicListDto> guPolicyDynamicListDtoList = policyDtoResponse.getGuPolicyDynamicListDtoList1();
        if (CollectionUtils.isEmpty(guPolicyDynamicListDtoList)) {
            return null;
        }
        String currency = policyDtoResponse.getGuPolicyMainDto().getCurrency();
        List<Gupolicyemployersplan> gupolicyemployersplanList = new ArrayList<>();
        for (GuPolicyDynamicListDto guPolicyDynamicListDto : guPolicyDynamicListDtoList) {
            Map<String, String> config = new HashMap<>();
            // 自定义配置
            config.put("policyno", "policyNo");
            config.put("riskcode", "riskCode");
            config.put("itemno", "itemNo");
            config.put("flag", "targetflag");
//            log.info("拷贝前：{}", JSON.toJSONString(guPolicyDynamicListDto, SerializerFeature.WriteMapNullValue));
            Gupolicyemployersplan gupolicyemployersplan = BeanCopyUtils.copy(Gupolicyemployersplan.class, guPolicyDynamicListDto, config);
            if (StringUtils.hasText(guPolicyDynamicListDto.getListseqno().toString())) {
                gupolicyemployersplan.setItemNo(new BigDecimal(guPolicyDynamicListDto.getListseqno().toString()));
            }
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldae())) { //投保人数
                gupolicyemployersplan.setEmployees(guPolicyDynamicListDto.getFieldae());
                //同步时本次变更人数与投保人数相同
                gupolicyemployersplan.setEmployeesthischange(guPolicyDynamicListDto.getFieldae());
            }
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldah())) { //法律费用责任限额
                gupolicyemployersplan.setLegalfeeslimit(new BigDecimal(guPolicyDynamicListDto.getFieldah().replace(",", "")));
            }
            //fieldai", "legalfeesrate");
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldai())) { //法律费用责任费率‰
                gupolicyemployersplan.setLegalfeesrate(new BigDecimal(guPolicyDynamicListDto.getFieldai().replace(",", "")));
            }
            //fieldan", "legalsumpremium");
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldan())) { //法律合计费用
                gupolicyemployersplan.setLegalsumpremium(new BigDecimal(guPolicyDynamicListDto.getFieldan().replace(",", "")));
            }
            //fieldaf", "personcasualtieslimit");
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldaf())) { //每人伤亡责任限额
                gupolicyemployersplan.setPersoncasualtieslimit(new BigDecimal(guPolicyDynamicListDto.getFieldaf().replace(",", "")));
            }
            //fieldag", "personcasualtiesrate");
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldag())) { //每人伤亡责任费率‰
                gupolicyemployersplan.setPersoncasualtiesrate(new BigDecimal(guPolicyDynamicListDto.getFieldag().replace(",", "")));
            }
            //fieldaj", "personmedicallimit");
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldaj())) { //每人医疗费用责任限额
                gupolicyemployersplan.setPersonmedicallimit(new BigDecimal(guPolicyDynamicListDto.getFieldaj().replace(",", "")));
            }
            //fieldak", "personmedicalrate");
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldak())) { //每人医疗费用责任费率‰
                gupolicyemployersplan.setPersonmedicalrate(new BigDecimal(guPolicyDynamicListDto.getFieldak().replace(",", "")));
            }
            //fieldal", "personsumpremium");
            if (StringUtils.hasText(guPolicyDynamicListDto.getFieldal())) { //伤亡医疗合计费用
                gupolicyemployersplan.setPersonsumpremium(new BigDecimal(guPolicyDynamicListDto.getFieldal().replace(",", "")));
            }
            //planid   计划编码  存 itemNo (标的序号)   TODO 暂时不处理
//            gupolicyemployersplan.setPlanid(CommonUtil.doubleToString(guPolicyDynamicListDto.getItemno()));
            //schemecode   方案代码 TODO 暂时不处理,后续需要添加方案代码校验
//            gupolicyemployersplan.setSchemecode("1");
            //currency 从主表中获取
            gupolicyemployersplan.setCurrency(currency);
            //dynamictargettype  默认存"10"
            gupolicyemployersplan.setDynamictargettype("10");
            // targetcalculate  伤亡和医疗为1，法律为0  根据 fieldaf -  personcasualtieslimit -每人伤亡责任限额 是否有值进行区分
            BigDecimal personcasualtieslimit = gupolicyemployersplan.getPersoncasualtieslimit();
            String targetcalculate = personcasualtieslimit == null || personcasualtieslimit.compareTo(BigDecimal.ZERO) == 0 ? "0" : "1";
            gupolicyemployersplan.setTargetcalculate(targetcalculate);
            //employeesthischange 保单为0，批单存储变更人数
            gupolicyemployersplan.setEmployeesthischange("0");
            //listbelongind  默认0
            gupolicyemployersplan.setListbelongind("0");
            //设置入机日期与修改日期
            Date now = new Date();
            gupolicyemployersplan.setInputDate(now);
            gupolicyemployersplan.setUpdatesysdate(now);
            //生成ID
            String id = businessNoService.nextNo(BusinessNoType.ID);
            gupolicyemployersplan.setId(id);
            gupolicyemployersplanList.add(gupolicyemployersplan);
        }
//            log.info("拷贝后：{}", gupolicyemployersplan);
        return gupolicyemployersplanList;
    }

    /**
     * 1219 新单存储逻辑  方案存在itemacci表中
     * 解析itemacci表数据 封装为splan表
     * @param policyDtoResponse
     * @return
     */
    private List<Gupolicyemployersplan> createPolicyListByItemAcciList1219(PolicyDtoResponse policyDtoResponse) {
        //获取核心保单计划险别表数据集合
        List<GuPolicyItemKindDto> gupolicyItemkindDtoList = policyDtoResponse.getGupolicyItemkindDtoList();
        List<GuPolicyItemAcciDto> acciDtos = policyDtoResponse.getGuPolicyItemAcciDtoList();
        if (CollectionUtils.isEmpty(acciDtos)) {
            return null;
        }
        List<Gupolicyemployersplan> gupolicyemployersplans = new ArrayList<>();
        Gupolicyemployersplan splan = null;
        double unitinsured = 0D;
        for (GuPolicyItemAcciDto dto : acciDtos) {
            splan = new Gupolicyemployersplan();
            splan.setPolicyNo(dto.getPolicyno());
            splan.setSubpolicyno(dto.getPolicyno());
            splan.setPlancode(dto.getPlancode());
            splan.setRiskCode(dto.getRiskcode());
            splan.setItemNo(new BigDecimal(dto.getItemno()));
            splan.setCurrency(dto.getCurrency());
            splan.setDynamictargettype("10");
            splan.setListseqno(new BigDecimal(dto.getProjectcode()));
            splan.setTargetcalculate("1");
            splan.setEmployees(dto.getQuantity().intValue()+"");
            splan.setEmployeesthischange("0");
            //每人伤亡责任限额, 影响页面显示计划. 单独根据1219036险别处理
            if (!CollectionUtils.isEmpty(gupolicyItemkindDtoList)) {
                for (GuPolicyItemKindDto guPolicyItemKindDto : gupolicyItemkindDtoList) {
                    if(guPolicyItemKindDto.getItemno()!=null&&guPolicyItemKindDto.getItemno().equals(dto.getItemno())&&"1219036".equals(guPolicyItemKindDto.getKindcode())){
                        unitinsured = guPolicyItemKindDto.getUnitinsured();
                        break;
                    }
                }
            }
            splan.setPersoncasualtieslimit(new BigDecimal(unitinsured+""));
            splan.setPersonsumpremium(new BigDecimal(dto.getGrosspremium()));
            splan.setListbelongind("0");
            //生成ID
            String id = businessNoService.nextNo(BusinessNoType.ID);
            splan.setId(id);
            //设置入机时间
            Date now = new Date();
            splan.setInputDate(now);
            splan.setUpdatesysdate(now);
            gupolicyemployersplans.add(splan);
        }
        return gupolicyemployersplans;
    }
    
    /**
     * 1144 险种保单同步
     * 解析itemacci表数据 封装为splan表
     *
     * @param policyDtoResponse
     * @return
     */
    private List<Gupolicyemployersplan> createPolicyListByItemAcciList1144(PolicyDtoResponse policyDtoResponse) {
        List<GuPolicyItemAcciDto> acciDtos = policyDtoResponse.getGuPolicyItemAcciDtoList();
        if (CollectionUtils.isEmpty(acciDtos)) {
            return null;
        }
        List<Gupolicyemployersplan> gupolicyemployersplans = new ArrayList<>();
        Gupolicyemployersplan splan = null;
        for (GuPolicyItemAcciDto dto : acciDtos) {
            splan = new Gupolicyemployersplan();
            splan.setPolicyNo(dto.getPolicyno());
            splan.setSubpolicyno(dto.getPolicyno());
            splan.setPlancode(dto.getPlancode());
            splan.setRiskCode(dto.getRiskcode());
            splan.setItemNo(new BigDecimal(dto.getItemno()));
            splan.setCurrency(dto.getCurrency());
            splan.setDynamictargettype("10");
            splan.setListseqno(new BigDecimal(dto.getProjectcode()));
            splan.setTargetcalculate("1");
            splan.setEmployees(dto.getQuantity().intValue()+"");
            splan.setEmployeesthischange("0");
            splan.setPersoncasualtieslimit(new BigDecimal(dto.getUnitinsured()));
            splan.setPersonsumpremium(new BigDecimal(dto.getGrosspremium()));
            splan.setListbelongind("0");
            //生成ID
            String id = businessNoService.nextNo(BusinessNoType.ID);
            splan.setId(id);
            //设置入机时间
            Date now = new Date();
            splan.setInputDate(now);
            splan.setUpdatesysdate(now);
            gupolicyemployersplans.add(splan);
        }
        return gupolicyemployersplans;
    }

    /**
     * @description: 解析保单基础信息表数据
     * @param: policyDtoResponse
     * @author: zhoutaoyu
     * @date: 2021/2/3
     * @return:
     **/
    private Gupolicymainbasic createPolicyMainBasic(PolicyDtoResponse policyDtoResponse) {
        //获取核心保单主表对象
        GuPolicyMainDto guPolicyMainDto = policyDtoResponse.getGuPolicyMainDto();
        Map<String, String> config = new HashMap<>();
        // 自定义配置
        config.put("policyno", "policyNo");
        config.put("agreementno", "agreementNo");
        config.put("businesssource", "businessSource");
        config.put("businesstype", "businessType");
        config.put("issuedate", "issueDate");
        config.put("operatedate", "operateDate");
        config.put("arguesolution", "argueSolution");
        config.put("isFarming", "isfarming");
        config.put("underwritecode", "underWriteCode");
        config.put("underwritename", "underWriteName");
//        log.info("拷贝前：{}", JSON.toJSONString(guPolicyMainDto, SerializerFeature.WriteMapNullValue));
        Gupolicymainbasic gupolicymainbasic = BeanCopyUtils.copy(Gupolicymainbasic.class, guPolicyMainDto, config);

//       处理 insurancecompanycode	保险公司归属机构
        gupolicymainbasic.setInsurancecompanycode(guPolicyMainDto.getCompanycode());
//        处理 renewedtime	续保次数
        gupolicymainbasic.setRenewedtime(BigDecimal.ZERO);
//        处理 uploadind  导入标志（riskdynamic） 默认存 1-导入
        gupolicymainbasic.setUploadind("1");
//        处理 nominativeind  记名标志（riskdynamic）默认存 1-记名
        gupolicymainbasic.setNominativeind("1");
//        处理 teammanagercode	客户经理代码
        gupolicymainbasic.setTeammanagercode(null);
//        处理 teammanagername	客户经理名称
        gupolicymainbasic.setTeammanagername(null);
        GuPolicyRiskDto guPolicyRiskDto = policyDtoResponse.getGuPolicyRiskDtoList().get(0);
//        处理 riskapplytype	险种适用范围
        gupolicymainbasic.setRiskapplytype(guPolicyRiskDto.getRiskapplytype());
//        处理 productedition  条款代码
        gupolicymainbasic.setProductedition(guPolicyRiskDto.getProductedition());
//        处理 producteditionname	条款名称
        gupolicymainbasic.setProducteditionname(guPolicyRiskDto.getProducteditionname());
//        处理 compensationtype	责任险保单基础
        gupolicymainbasic.setCompensationtype(guPolicyRiskDto.getCompensationtype());
//        处理 discoverstartdate	追溯起始日期
        gupolicymainbasic.setDiscoverstartdate(guPolicyRiskDto.getDiscoverstartdate());
//        处理 discoverenddate	发现终止日期
        gupolicymainbasic.setDiscoverenddate(guPolicyRiskDto.getDiscoverenddate());
//        处理 judicalcode	司法管辖代码
        gupolicymainbasic.setJudicalCode(guPolicyRiskDto.getJudicalcode());
//        处理 geographicalarea	承保地区代码
        gupolicymainbasic.setGeographicalarea(guPolicyRiskDto.getGeographicalarea());
//        处理 geographicalareadesc 承保地区名称
        gupolicymainbasic.setGeographicalareadesc(guPolicyRiskDto.getGeographicalareadesc());
        //设置入机日期与修改日期
        Date now = new Date();
        gupolicymainbasic.setInputDate(now);
        gupolicymainbasic.setUpdatesysdate(now);
        //生成ID
        String id = businessNoService.nextNo(BusinessNoType.ID);
        gupolicymainbasic.setId(id);
        return gupolicymainbasic;
    }

    /**
     * @description: 解析保单主信息表数据
     * @param: policyDtoResponse
     * @author: zhoutaoyu
     * @date: 2021/2/3
     * @return:
     **/
    private Gupolicymain createPolicyMain(PolicyDtoResponse policyDtoResponse) {
        //获取核心保单主表信息
        GuPolicyMainDto guPolicyMainDto = policyDtoResponse.getGuPolicyMainDto();
        Gupolicymain tiaojian = new Gupolicymain();
        Gupolicymain searchVo = null;
        tiaojian.setPolicyNo(guPolicyMainDto.getPolicyno());
        List<Gupolicymain> searchVoList = gupolicymainDao.selectByCondition(tiaojian);
        if (null != searchVoList && 0 < searchVoList.size()) {
            searchVo = searchVoList.get(0);
        }
        Map<String, String> config = new HashMap<>();
        // 自定义配置
        config.put("proposalno", "proposalNo");
        config.put("policyno", "policyNo");
        config.put("applicode", "appliCode");
        config.put("appliname", "appliName");
        config.put("insuredcode", "insuredCode");
        config.put("insuredname", "insuredName");
        config.put("uwyear", "uwYear");
        config.put("underwriteenddate", "underWriteEndDate");
        config.put("endorsetimes", "endorseTimes");
        config.put("registtimes", "registTimes");
//        log.info("拷贝前：guPolicyMainDto:{}", JSON.toJSONString(guPolicyMainDto, SerializerFeature.WriteMapNullValue));
        Gupolicymain gupolicymain = BeanCopyUtils.copy(Gupolicymain.class, guPolicyMainDto, config);
//       处理 insurancecompanycode	保险公司归属机构
        gupolicymain.setInsurancecompanycode(guPolicyMainDto.getCompanycode());
        //       处理 companycode	关联机构代码
        if (null != searchVo && null != searchVo.getCompanycode()) {
            gupolicymain.setCompanycode(searchVo.getCompanycode());
        } else {
            gupolicymain.setCompanycode(null);
        }
        //        处理 companyname	关联机构名称
        if (null != searchVo && null != searchVo.getCompanyname()) {
            gupolicymain.setCompanyname(searchVo.getCompanyname());
        } else {
            gupolicymain.setCompanyname(null);
        }
        //        处理 projectmanagercode	PM代码
        if (null != searchVo && null != searchVo.getProjectmanagercode()) {
            gupolicymain.setProjectmanagercode(searchVo.getProjectmanagercode());
        } else {
            gupolicymain.setProjectmanagercode(null);
        }
//        处理 projectmanagername 	PM名称
        if (null != searchVo && null != searchVo.getProjectmanagername()) {
            gupolicymain.setProjectmanagername(searchVo.getProjectmanagername());
        } else {
            gupolicymain.setProjectmanagername(null);
        }


//        处理 notaxagentrate	手续费比例（不含税）
        gupolicymain.setNotaxagentrate(null);
//        处理 fsh  FSH%值
        gupolicymain.setFsh(null);
//        处理 xsf	  XSF%调整系数值
        gupolicymain.setXsf(null);
//        处理 xsfind	XSF等级
        gupolicymain.setXsfind(null);
//        处理 renewedtime	续保次数
        gupolicymain.setRenewedtime(BigDecimal.ZERO);
        //获取核心险种信息
        GuPolicyRiskDto guPolicyRiskDto = policyDtoResponse.getGuPolicyRiskDtoList().get(0);
//        处理 startdate	起保日期
        gupolicymain.setStartDate(guPolicyRiskDto.getStartdate());
//        处理 enddate	终保日期
        gupolicymain.setEndDate(guPolicyRiskDto.getEnddate());
        //处理审核通过状态  默认为1-审核通过
        gupolicymain.setUnderwriteind("1");
        //处理已结算金额  默认为0
        gupolicymain.setSettlefee(BigDecimal.ZERO);
        //设置入机日期与修改日期
        Date now = new Date();
        gupolicymain.setInputDate(now);
        gupolicymain.setUpdatesysdate(now);
        //生成ID
        String id = businessNoService.nextNo(BusinessNoType.ID);
        gupolicymain.setId(id);
//        log.info("拷贝后：gupolicymain:{}", gupolicymain);
        return gupolicymain;
    }

    private List<Gsclientmain> createGsClientMain(PolicyDtoResponse policyDtoResponse) {
        List<GsClientMainDto> gsClientMainDtoList = policyDtoResponse.getGsClientMainDtoList();
        List<Gsclientmain> gsclientmainList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(gsClientMainDtoList)) {
            for (GsClientMainDto gsClientMainDto : gsClientMainDtoList) {
                //已经存在的客户信息不再同步
                Gsclientmain gsclientmainOld = gsclientmainDao.selectByPrimaryKey(gsClientMainDto.getClientcode());
                if (gsclientmainOld != null) {
                    continue;
                }
                Map<String, String> config = new HashMap<>();
                config.put("cancelreason", "cancelReason");
                config.put("canceldate", "cancelDate");
                config.put("postcode", "postCode");
                config.put("postaddress", "postAddress");
                config.put("bankcode", "bankCode");
                config.put("accountno", "accountNo");
                config.put("createtime", "createTime");
                config.put("updatercode", "updaterCode");
                config.put("bankname", "bankName");
                Gsclientmain gsclientmain = BeanCopyUtils.copy(Gsclientmain.class, gsClientMainDto, config);
                gsclientmainList.add(gsclientmain);
            }
        }
        return gsclientmainList;
    }

    /**
     * 分批次批量插入各表
     *
     * @param list   待分组List
     * @param length 每组List的长度
     */
    @Transactional
    public <T, A> void createBatchList(List<T> list, Class<A> paramClass, Integer length, List<String> policyNoList) {
        //若为空则不继续执行
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(policyNoList)) {
            return;
        }
        int size = list.size();
        int count = (size + length - 1) / length;
        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * length, ((i + 1) * length > size ? size : length * (i + 1)));
            if (paramClass.equals(Gupolicymain.class)) {
                List<Gupolicymain> gupolicymainList = (List<Gupolicymain>) subList;
                for (String policyNo : policyNoList) {
                    gupolicymainDao.deleteByPolicyNo(policyNo);
                }
                int i1 = gupolicymainDao.batchInsert(gupolicymainList);
                System.out.println("Gupolicymain插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gupolicymainbasic.class)) {
                List<Gupolicymainbasic> gupolicymainbasicList = (List<Gupolicymainbasic>) subList;
                for (String policyNo : policyNoList) {
                    gupolicymainbasicDao.deleteByPolicyNo(policyNo);
                }
                int i1 = gupolicymainbasicDao.batchInsert(gupolicymainbasicList);
                System.out.println("Gupolicymainbasic插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gupolicyemployersplan.class)) {
                List<Gupolicyemployersplan> gupolicyemployersplanList = (List<Gupolicyemployersplan>) subList;
                for (String policyNo : policyNoList) {
                    gupolicyemployersplanDao.deleteByPolicyNo(policyNo);
                }
                int i1 = gupolicyemployersplanDao.batchInsert(gupolicyemployersplanList);
                System.out.println("Gupolicyemployersplan插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gupolicyemployerslist.class)) {
                List<Gupolicyemployerslist> gupolicyemployerslists = (List<Gupolicyemployerslist>) subList;
                if (i ==0) {
                    for (String policyNo : policyNoList) {
                        gupolicyemployerslistDao.deleteByPolicyNo(policyNo);
                    }
                }
                int i1 = gupolicyemployerslistDao.batchInsert(gupolicyemployerslists);
                System.out.println("Gupolicyemployerslist插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gupolicyitemkind.class)) {
                List<Gupolicyitemkind> gupolicyitemkindList = (List<Gupolicyitemkind>) subList;
                for (String policyNo : policyNoList) {
                    gupolicyitemkindDao.deleteByPolicyNo(policyNo);
                }
                int i1 = gupolicyitemkindDao.batchInsert(gupolicyitemkindList);
                System.out.println("Gupolicyitemkind插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gupolicyholder.class)) {
                List<Gupolicyholder> gupolicyholderList = (List<Gupolicyholder>) subList;
                for (String policyNo : policyNoList) {
                    gupolicyholderDao.deleteByPolicyNo(policyNo);
                }
                int i1 = gupolicyholderDao.batchInsert(gupolicyholderList);
                System.out.println("Gupolicyholder插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gupolicyinsured.class)) {
                List<Gupolicyinsured> gupolicyholderList = (List<Gupolicyinsured>) subList;
                for (String policyNo : policyNoList) {
                    gupolicyinsuredDao.deleteByPolicyNo(policyNo);
                }
                int i1 = gupolicyinsuredDao.batchInsert(gupolicyholderList);
                System.out.println("Gupolicyinsured插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gupolicycopymain.class)) {
                List<Gupolicycopymain> gupolicycopymainList = (List<Gupolicycopymain>) subList;
                for (String policyNo : policyNoList) {
                    gupolicycopymainDao.deleteByPolicyNo(policyNo);
                }
                int i1 = gupolicycopymainDao.batchInsert(gupolicycopymainList);
                System.out.println("Gupolicycopymain插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gupolicycopymainbasic.class)) {
                List<Gupolicycopymainbasic> gupolicycopymainbasicList = (List<Gupolicycopymainbasic>) subList;
                for (String policyNo : policyNoList) {
                    gupolicycopymainbasicDao.deleteByPolicyNo(policyNo);
                }
                int i1 = gupolicycopymainbasicDao.batchInsert(gupolicycopymainbasicList);
                System.out.println("Gupolicycopymainbasic插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gupolicycopyemployersplan.class)) {
                List<Gupolicycopyemployersplan> gupolicycopyemployersplanList = (List<Gupolicycopyemployersplan>) subList;
                for (String policyNo : policyNoList) {
                    gupolicycopyemployersplanDao.deleteByPolicyNo(policyNo);
                }
                int i1 = gupolicycopyemployersplanDao.batchInsert(gupolicycopyemployersplanList);
                System.out.println("Gupolicycopyemployersplan插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gupolicycopyemployerslist.class)) {
                List<Gupolicycopyemployerslist> gupolicycopyemployerslists = (List<Gupolicycopyemployerslist>) subList;
                if (i == 0) {
                    for (String policyNo : policyNoList) {
                        gupolicycopyemployerslistDao.deleteByPolicyNo(policyNo);
                    }
                }
                int i1 = gupolicycopyemployerslistDao.batchInsert(gupolicycopyemployerslists);
                System.out.println("Gupolicycopyemployerslist插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gupolicycopyitemkind.class)) {
                List<Gupolicycopyitemkind> gupolicycopyitemkindList = (List<Gupolicycopyitemkind>) subList;
                for (String policyNo : policyNoList) {
                    gupolicycopyitemkindDao.deleteByPolicyNo(policyNo);
                }
                int i1 = gupolicycopyitemkindDao.batchInsert(gupolicycopyitemkindList);
                System.out.println("Gupolicycopyitemkind插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gupolicycopyholder.class)) {
                List<Gupolicycopyholder> gupolicycopyholderList = (List<Gupolicycopyholder>) subList;
                for (String policyNo : policyNoList) {
                    gupolicycopyholderDao.deleteByPolicyNo(policyNo);
                }
                int i1 = gupolicycopyholderDao.batchInsert(gupolicycopyholderList);
                System.out.println("Gupolicycopyholder插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gupolicycopyinsured.class)) {
                List<Gupolicycopyinsured> gupolicycopyinsuredList = (List<Gupolicycopyinsured>) subList;
                for (String policyNo : policyNoList) {
                    gupolicycopyinsuredDao.deleteByPolicyNo(policyNo);
                }
                int i1 = gupolicycopyinsuredDao.batchInsert(gupolicycopyinsuredList);
                System.out.println("Gupolicycopyinsured插入条数: " + i1 + "条");
            } else if (paramClass.equals(Gsclientmain.class)) {
                List<Gsclientmain> gsclientmainList = (List<Gsclientmain>) subList;
                int i1 = gsclientmainDao.batchInsert(gsclientmainList);
                System.out.println("Gsclientmain插入条数: " + i1 + "条");
            } else if (paramClass.equals(GuPolicyCoinsuranceDto.class)) {
                for (String policyNo : policyNoList) {
                    GuPolicyCoinsuranceDtoExample guPolicyCoinsuranceDtoExample = new GuPolicyCoinsuranceDtoExample();
                    guPolicyCoinsuranceDtoExample.createCriteria().andPolicynoEqualTo(policyNo);
                    guPolicyCoinsuranceDtoMapper.deleteByExample(guPolicyCoinsuranceDtoExample);
                }
                List<GuPolicyCoinsuranceDto> guPolicyCoinsuranceDtoList = (List<GuPolicyCoinsuranceDto>) subList;
                for (GuPolicyCoinsuranceDto guPolicyCoinsuranceDto : guPolicyCoinsuranceDtoList) {
                    guPolicyCoinsuranceDtoMapper.insertSelective(guPolicyCoinsuranceDto);
                }

            }

        }

    }

    /**
     * @param vo
     * @description: 查询核心待同步保单信息集合
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/1/26
     * @return:
     */
    public PolicySyncQueryRespVo queryPolicyNoEmployer(PolicySyncRequestVo vo) {
        String responseJson = null;
        PolicySyncQueryRespVo respVo = new PolicySyncQueryRespVo();
        //获取yml文件中配置的接口地址
        String queryPolicyNoEmployerUrl = urlConfig.getQueryPolicyNoEmployer();
        String reqJson = JSON.toJSONString(vo);
        String errorMsg = "查询待同步保单号失败,网络异常!";
        //定义存储日志对象
        Gupolicylog gupolicylog = new Gupolicylog();
        gupolicylog.setRequesetdate(new Date());
        gupolicylog.setOperatorcode(SessionHelper.getLoginUser().getUserCode());
        gupolicylog.setRemark("queryPolicyNoEmployer");
        //调用服务平台接口查询待同步保单号清单
        try {
            responseJson = HttpClientUtils.postJson(queryPolicyNoEmployerUrl, reqJson).getBodyAsString();
        } catch (Exception e) {
            log.info(MessageFormat.format("{0}{1}", errorMsg, e.getMessage()));
            //保存异常日志
            gupolicylog.setRequestcontent(reqJson);
            gupolicylog.setResponsecontent(e.getMessage());
            gupolicylog.setResponsestatus("1");
            //设置id
            String id = businessNoService.nextNo(BusinessNoType.ID);
            gupolicylog.setId(id);
            gupolicylogDao.insertSelective(gupolicylog);
            respVo.setStatus("500");
            respVo.setMessage(errorMsg);
            return respVo;
        }
        if (!StringUtils.hasText(responseJson)) {
            errorMsg = "查询待同步保单号:响应为空!";
            log.info(errorMsg);
            gupolicylog.setRequestcontent(reqJson);
            gupolicylog.setResponsecontent(errorMsg);
            gupolicylog.setResponsestatus("1");
            //设置id
            String id = businessNoService.nextNo(BusinessNoType.ID);
            gupolicylog.setId(id);
            gupolicylogDao.insertSelective(gupolicylog);
            respVo.setStatus("500");
            respVo.setMessage(errorMsg);
            return respVo;
        } else {
            JSONObject respJsonObj = JSONObject.parseObject(responseJson);
            String code = (String) respJsonObj.get("code");
            //若不为响应状态码不为"200"则查询失败
            if (!"200".equals(code)) {
                //获取异常信息
                String msg = (String) respJsonObj.get("message");
                log.info(MessageFormat.format("{0}{1}", errorMsg, msg));
                //保存异常日志
                gupolicylog.setRequestcontent(reqJson);
                gupolicylog.setResponsecontent(msg);
                gupolicylog.setResponsestatus("1");
                //设置id
                String id = businessNoService.nextNo(BusinessNoType.ID);
                gupolicylog.setId(id);
                gupolicylogDao.insertSelective(gupolicylog);
                respVo.setStatus("500");
                respVo.setMessage(MessageFormat.format("{0}{1}", errorMsg, msg));
                return respVo;
            }
            //保存成功日志
            gupolicylog.setRequestcontent(reqJson);
            gupolicylog.setResponsecontent(responseJson);
            gupolicylog.setResponsestatus("0");
            //设置id
            String id = businessNoService.nextNo(BusinessNoType.ID);
            gupolicylog.setId(id);
            gupolicylogDao.insertSelective(gupolicylog);
            List<String> result = (List<String>) respJsonObj.get("policylist");
            String msg = (String) respJsonObj.get("message");
            respVo.setStatus("200");
            respVo.setMessage(msg);
            respVo.setPolicyNolist(result);
        }
        return respVo;
    }

    /**
     * @description: 根据传入的保单号集合, 按批次同步核心最新保单信息
     * @param: vo
     * @param: length 每批次请求保单号集合长度
     * @author: zhoutaoyu
     * @date: 2021/1/26
     * @return:
     **/
    public PolicySyncQueryRespVo queryPolicyEmployerByBatch(PolicySyncRequestVo vo, Integer length) {
        List<PolicyDtoResponse> result = new ArrayList<>();
        List<String> errorList = new ArrayList<>();
        List<String> missing = new ArrayList<>();
        List<String> success = new ArrayList<>();
        PolicySyncQueryRespVo respVo = new PolicySyncQueryRespVo();
        String errorMsg = null;
        //  todo 历史提交处理
        // 校验险种选项与保单号是否一致
        // if(!(vo.getSurverind().equals(vo.getPolicyno().substring(7, 11)))){
        //     respVo.setMessage(MessageFormat.format("保单同步失败！请确认险种选项 {0} 与保单号 {1} 是否一致！", vo.getSurverind(), vo.getPolicyno()));
        //     respVo.setPolicylist(result);
        //     return respVo;
        // }
        //定义存储日志对象
        Gupolicylog gupolicylog = new Gupolicylog();
        gupolicylog.setRequesetdate(new Date());
        gupolicylog.setOperatorcode(SessionHelper.getLoginUser().getUserCode());
        gupolicylog.setRemark("queryPolicyEmployer");
        List<String> policyList = vo.getPolicylist();
        //获取List长度
        int size = policyList.size();
        //获取循环次数
        int count = (size + length - 1) / length;
        for (int i = 0; i < count; i++) {
            //获取每批次请求的保单List
            List<String> subList = policyList.subList(i * length, ((i + 1) * length > size ? size : length * (i + 1)));
            PolicySyncRequestVo batchPolicySyncReqVo = BeanCopyUtils.clone(vo, PolicySyncRequestVo.class);
            batchPolicySyncReqVo.setPolicylist(subList);
            String responseJson = null;
            //获取yml文件中配置的接口地址
            String queryPolicyNoEmployerUrl = urlConfig.getQueryPolicyEmployer();
            String reqJson = JSON.toJSONString(batchPolicySyncReqVo);
            //调用服务平台接口查询待同步保单号清单
            try {
                responseJson = HttpClientUtils.postJson(queryPolicyNoEmployerUrl, reqJson).getBodyAsString();
            } catch (Exception e) {
                // 未同步的保单集合,返回前端
                errorList.addAll(subList);
                gupolicylog.setRequestcontent(reqJson);
                gupolicylog.setResponsecontent(MessageFormat.format("{0}{1}", errorMsg, e.getMessage()));
                gupolicylog.setResponsestatus("1");
                //设置id
                String id = businessNoService.nextNo(BusinessNoType.ID);
                gupolicylog.setId(id);
                gupolicylogDao.insertSelective(gupolicylog);
                log.info(MessageFormat.format("{0}{1}", errorMsg, e.getMessage()));
            }
            if (!StringUtils.hasText(responseJson)) {
                errorMsg = "保单同步失败:响应为空!";
                // 未同步的保单集合,返回前端
                errorList.addAll(subList);
                gupolicylog.setRequestcontent(reqJson);
                gupolicylog.setResponsecontent(errorMsg);
                gupolicylog.setResponsestatus("1");
                //设置id
                String id = businessNoService.nextNo(BusinessNoType.ID);
                gupolicylog.setId(id);
                gupolicylogDao.insertSelective(gupolicylog);
                log.info(MessageFormat.format("{0}{1}", errorMsg, subList));
            } else {
                JSONObject respJsonObj = JSONObject.parseObject(responseJson);
                String code = (String) respJsonObj.get("code");
                //若不为响应状态码不为"200"则查询失败
                if (!"200".equals(code)) {
                    // 未同步的保单集合,返回前端
                    errorList.addAll(subList);
                    errorMsg = (String) respJsonObj.get("message");
                    gupolicylog.setRequestcontent(reqJson);
                    gupolicylog.setResponsecontent(errorMsg);
                    gupolicylog.setResponsestatus("1");
                    //设置id
                    String id = businessNoService.nextNo(BusinessNoType.ID);
                    gupolicylog.setId(id);
                    gupolicylogDao.insertSelective(gupolicylog);
                    log.info(MessageFormat.format("保单同步失败！原因：{0}", errorMsg));
                } else {
                    //响应成功
                    //查询成功的保单集合
                    JSONArray policyDtoResponseList = (JSONArray) respJsonObj.get("policyDtoResponseList");
                    List<PolicyDtoResponse> successList = policyDtoResponseList.toJavaList(PolicyDtoResponse.class);
                    //未查询到的保单集合
                    JSONArray missingArray = (JSONArray) respJsonObj.get("missing");
                    List<String> missingList = missingArray.toJavaList(String.class);
                    //成功保单集合
                    JSONArray successArray = (JSONArray) respJsonObj.get("success");
                    success = successArray.toJavaList(String.class);
                    if (!CollectionUtils.isEmpty(missingList)) {
                        missing.addAll(missingList);
                        errorMsg = MessageFormat.format("未查询到 {0} 保单，请确认填写是否有误！", missingList.get(0));
                    }
                    if (!CollectionUtils.isEmpty(successList)) {
                        result.addAll(successList);
                    }
                    gupolicylog.setRequestcontent(reqJson);
                    gupolicylog.setResponsecontent(responseJson);
                    gupolicylog.setResponsestatus("0");
                    //设置id
                    String id = businessNoService.nextNo(BusinessNoType.ID);
                    gupolicylog.setId(id);
                    gupolicylogDao.insertSelective(gupolicylog);
                }
            }

        }
        int failCount = (CollectionUtils.isEmpty(errorList) ? 0 : errorList.size()) + (CollectionUtils.isEmpty(missing) ? 0 : missing.size());
        String message = "保单同步完成!";
        if (CollectionUtils.isEmpty(result)) {  //若成功条数为0
            respVo.setStatus("500");
            respVo.setMessage(MessageFormat.format(
                    "保单同步失败! {0} 同步成功条数:{1} 条,同步失败条数{2} 条,请联系管理员!",errorMsg, "0", failCount));
        } else {
            respVo.setStatus("200");
            respVo.setPolicylist(result);
            respVo.setPolicyNolist(success);
            respVo.setMessage(MessageFormat.format(
                    "{0} {1}{2}",
                    failCount == 0 ? message : message.concat("存在同步失败的保单,请联系管理员!"),
                    result.size() == 0 ? "" : "同步成功条数:".concat(String.valueOf(result.size())).concat("条"),
                    failCount == 0 ? "" : "同步失败条数:".concat(String.valueOf(failCount)).concat("条"))
            );
        }
        return respVo;
    }

    /**
     * 保单条件分页查询
     *
     * @param vo 查询条件载体
     * @Return 分页数据集合
     * <AUTHOR>
     * @date 2021年02月07日 17:36:54
     */
    public PageResult<PolicyMainRespVo> pageQueryPolicyByCondition(PolicyMainReqVo vo) {
        String errorMsg = ins.channel.base.utils.DateUtils.checkDateStartEnd(vo.getEffectiveDateStart(), vo.getEffectiveDateEnd(), "生效日期");
        if (!errorMsg.isEmpty()) {
            throw new BusinessException(errorMsg);
        }
        PageParam pageParam = PageHelper.getPageParam(vo);
        Page<PolicyMainRespVo> results = gupolicymainDao.pageQueryPolicyByCondition(pageParam, vo);
        return PageHelper.convert(pageParam, results, PolicyMainRespVo.class);
    }

    /**
     * @description: 申报页面-分页模糊查询保单信息
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/2/8
     * @return:
     **/
    public PageResult<PolicyMainRespVo> queryPolicyByPage(PolicyMainReqVo vo) {
        SysUser loginUser = SessionHelper.getLoginUser();
        String userInd = loginUser.getUserInd();
        if ("3".equals(userInd)) {//判断是否为业务员TODO 设置用户职级常量.
            vo.setProjectManagerCode(loginUser.getUserCode());
        }
        if (null != loginUser.getDepartment()){
            vo.setDepartment(loginUser.getDepartment());
        }
        // 数据权限校验：关联机构
        Set<String> permitComs = UserPermitDataHelper.getUserPermitCom();
        if (StringUtils.hasText(vo.getCompanyCode())) { // 如果查询参数中指定了机构，直接做权限校验
            if (!permitComs.contains(vo.getCompanyCode())) {
                throw new PermissionException("当前登录用户没有该机构的操作权限,请确认!");
            }
        } else {
            vo.setCompanyCodes(permitComs);
        }
        PageParam pageParam = PageHelper.getPageParam(vo);
        //TODO 查询条件中有待处理字段  
        Page<PolicyMainRespVo> result = gupolicymainDao.queryPolicyByPage(pageParam, vo);
        return PageHelper.convert(pageParam, result, PolicyMainRespVo.class);
    }

    public PolicyMainRespVo selectOneForDeclarationAdd(PolicyMainReqVo vo) {
        Assert.hasText(vo.getPolicyNo(), "保单号不能为空!");
        //根据保单号查询出该保单基本信息
        List<PolicyMainRespVo> policyMainRespVoList = gupolicymainDao.selectForDeclarationAdd(vo);
        Assert.notEmpty(policyMainRespVoList, "未查询到该保单信息!");
        PolicyMainRespVo policyMainRespVo = policyMainRespVoList.get(0);
        //取第一条,根据该保单号取雇员清单表中该保单下所有职业名称集合,返回 , 号 分割后的字符串结果
        String occupationNames = gupolicyemployerslistDao.selectAllOccupationNamesByPolicyNo(policyMainRespVo.getPolicyNo());
        //设置职业名称集合
        policyMainRespVo.setOccupationName(occupationNames);
        return policyMainRespVo;
    }

    /**
     * @description:逐笔申报减员-分页模糊查询雇员清单信息
     * @param: vo
     * @author: zhoutaoyu
     * @date: 2021/2/22
     * @return:
     **/
    public PageResult<EmployerslistQueryRespVo> queryPolicyEmployersListByPage(EmployerslistQueryReqVo vo) {
        Assert.hasText(vo.getPolicyNo(), "保单号不能为空!");
        PageParam pageParam = PageHelper.getPageParam(vo);
        SysUser sysUser = SessionHelper.getLoginUser();
        if (null != sysUser.getDepartment()){
            vo.setDepartment(sysUser.getDepartment());
        }
        Page<EmployerslistQueryRespVo> result = gupolicyemployerslistDao.queryPolicyEmployersListByPage(pageParam, vo);
        return PageHelper.convert(pageParam, result, EmployerslistQueryRespVo.class);
    }

    /**
     * 申报查询若查询结果集为1条数据则将保单号传给前端
     *
     * @param vo
     * @return
     */
    public String searchPolicyNo(DeclarationReqVo vo) {
        Set<String> permitComs = UserPermitDataHelper.getUserPermitCom();
        if (StringUtils.hasText(vo.getCompanyCode())) { // 如果查询参数中指定了机构，直接做权限校验
            if (!permitComs.contains(vo.getCompanyCode())) {
                throw new PermissionException("当前登录用户没有该机构的操作权限,请确认!");
            }
        } else {
            vo.setCompanyCodes(permitComs);
        }
        ;
        List<DeclarationRespVo> declarationReqVoList = gupolicymainDao.searchPolicyNo(vo);
        String policynumber = null;
        if (declarationReqVoList.size() == 1) {
            DeclarationRespVo declarationRespVo = declarationReqVoList.get(0);
            policynumber = declarationRespVo.getPolicyNo();
        }
        return policynumber;
    }

    public PolicySyncQueryRespVo queryPolicyStatus(PolicySyncRequestVo vo) {
        String message = null;
        String status = null;
        String responseJson = null;
        JSONObject respJsonObj = null;
        PolicySyncQueryRespVo respVo = new PolicySyncQueryRespVo();
        String queryPolicyNoEmployerUrl = urlConfig.getQueryPolicyEmployer();
        String reqJson = JSON.toJSONString(vo);
        try {
            responseJson = HttpClientUtils.postJson(queryPolicyNoEmployerUrl, reqJson).getBodyAsString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!StringUtils.hasText(responseJson)) {
            message = "查询保单失败：响应为空！";
            status = "500";
        } else {
            respJsonObj = JSONObject.parseObject(responseJson);
            status = (String) respJsonObj.get("code");
            message = (String) respJsonObj.get("message");
        }
        respVo.setStatus(status);
        respVo.setMessage(message);
        return respVo;
    }
}

