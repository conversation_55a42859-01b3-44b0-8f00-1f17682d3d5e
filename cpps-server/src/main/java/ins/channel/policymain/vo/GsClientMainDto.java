package ins.channel.policymain.vo;

import java.util.Date;

public class GsClientMainDto {
    private String clientcode;

    private String clientcname;

    private String clientename;

    private String clienttype;

    private Date joindate;

    private String blacklistind;

    private String formalind;

    private String vipind;

    private String cancelind;

    private String cancelreason;

    private Date canceldate;

    private String relatedclient;

    private String remark;

    private String flag;

    private String connection;

    private String postcode;

    private String postaddress;

    private String qualityind;

    private String clientstatus;

    private String visitind;

    private String bankcode;

    private String accountno;

    private String moneylaunderingind;

    private String directclientind;

    private String scandocind;

    private String originclientcode;

    private String relateclientcode;

    private String creatorcode;

    private Date createtime;

    private String updatercode;

    private Date updatetime;

    private String itemprovincecode;

    private String itemcitycode;

    private String itemprovincecname;

    private String itemcitycname;

    private String starlevel;

    private String bankname;

    private Date updatesysdate;

    private String email;

    private String industrymaincode;

    private String industrykindcode;

    private String tallagetype;

    private String tallageno;

    private String corporateaddress;

    private String openingbank;

    private String tallagebankcode;

    private String corporatetel;

    private String sendaddress;

    private String tallageremarks;

    private String invoicetype;

    private String registercode;

    //以下为临时属性，不与表对应
    /**
     * 证件类型
     */
    private String identifyType;
    /**
     * 证件号码
     */
    private String identifyNumber;
    /**
     * 省份代码
     */
    private String provinceCode;
    /**
     * 省份名称
     */
    private String provinceCName;
    /**
     * 城市代码
     */
    private String cityCode;
    /**
     * 城市名称
     */
    private String cityCName;
    /**
     * 客户地址
     */
    private String showClientAddress;
    /**
     * 业务员
     */
    private String handlerCode;
    /**
     * 性别
     */
    private String sex;
    /**
     *  出生日期
     */
    private String birthDate;
    /**
     * 归属机构代码
     */
    private String companyCode;
    /**
     * 手机号码
     */
    private String mobile;


    public String getClientcode() {
        return clientcode;
    }

    public String getIdentifyType() {
        return identifyType;
    }

    public void setIdentifyType(String identifyType) {
        this.identifyType = identifyType;
    }

    public String getIdentifyNumber() {
        return identifyNumber;
    }

    public void setIdentifyNumber(String identifyNumber) {
        this.identifyNumber = identifyNumber;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceCName() {
        return provinceCName;
    }

    public void setProvinceCName(String provinceCName) {
        this.provinceCName = provinceCName;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityCName() {
        return cityCName;
    }

    public void setCityCName(String cityCName) {
        this.cityCName = cityCName;
    }

    public String getShowClientAddress() {
        return showClientAddress;
    }

    public void setShowClientAddress(String showClientAddress) {
        this.showClientAddress = showClientAddress;
    }

    public String getHandlerCode() {
        return handlerCode;
    }

    public void setHandlerCode(String handlerCode) {
        this.handlerCode = handlerCode;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setClientcode(String clientcode) {
        this.clientcode = clientcode == null ? null : clientcode.trim();
    }

    public String getClientcname() {
        return clientcname;
    }

    public void setClientcname(String clientcname) {
        this.clientcname = clientcname == null ? null : clientcname.trim();
    }

    public String getClientename() {
        return clientename;
    }

    public void setClientename(String clientename) {
        this.clientename = clientename == null ? null : clientename.trim();
    }

    public String getClienttype() {
        return clienttype;
    }

    public void setClienttype(String clienttype) {
        this.clienttype = clienttype == null ? null : clienttype.trim();
    }

    public Date getJoindate() {
        return joindate;
    }

    public void setJoindate(Date joindate) {
        this.joindate = joindate;
    }

    public String getBlacklistind() {
        return blacklistind;
    }

    public void setBlacklistind(String blacklistind) {
        this.blacklistind = blacklistind == null ? null : blacklistind.trim();
    }

    public String getFormalind() {
        return formalind;
    }

    public void setFormalind(String formalind) {
        this.formalind = formalind == null ? null : formalind.trim();
    }

    public String getVipind() {
        return vipind;
    }

    public void setVipind(String vipind) {
        this.vipind = vipind == null ? null : vipind.trim();
    }

    public String getCancelind() {
        return cancelind;
    }

    public void setCancelind(String cancelind) {
        this.cancelind = cancelind == null ? null : cancelind.trim();
    }

    public String getCancelreason() {
        return cancelreason;
    }

    public void setCancelreason(String cancelreason) {
        this.cancelreason = cancelreason == null ? null : cancelreason.trim();
    }

    public Date getCanceldate() {
        return canceldate;
    }

    public void setCanceldate(Date canceldate) {
        this.canceldate = canceldate;
    }

    public String getRelatedclient() {
        return relatedclient;
    }

    public void setRelatedclient(String relatedclient) {
        this.relatedclient = relatedclient == null ? null : relatedclient.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag == null ? null : flag.trim();
    }

    public String getConnection() {
        return connection;
    }

    public void setConnection(String connection) {
        this.connection = connection == null ? null : connection.trim();
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode == null ? null : postcode.trim();
    }

    public String getPostaddress() {
        return postaddress;
    }

    public void setPostaddress(String postaddress) {
        this.postaddress = postaddress == null ? null : postaddress.trim();
    }

    public String getQualityind() {
        return qualityind;
    }

    public void setQualityind(String qualityind) {
        this.qualityind = qualityind == null ? null : qualityind.trim();
    }

    public String getClientstatus() {
        return clientstatus;
    }

    public void setClientstatus(String clientstatus) {
        this.clientstatus = clientstatus == null ? null : clientstatus.trim();
    }

    public String getVisitind() {
        return visitind;
    }

    public void setVisitind(String visitind) {
        this.visitind = visitind == null ? null : visitind.trim();
    }

    public String getBankcode() {
        return bankcode;
    }

    public void setBankcode(String bankcode) {
        this.bankcode = bankcode == null ? null : bankcode.trim();
    }

    public String getAccountno() {
        return accountno;
    }

    public void setAccountno(String accountno) {
        this.accountno = accountno == null ? null : accountno.trim();
    }

    public String getMoneylaunderingind() {
        return moneylaunderingind;
    }

    public void setMoneylaunderingind(String moneylaunderingind) {
        this.moneylaunderingind = moneylaunderingind == null ? null : moneylaunderingind.trim();
    }

    public String getDirectclientind() {
        return directclientind;
    }

    public void setDirectclientind(String directclientind) {
        this.directclientind = directclientind == null ? null : directclientind.trim();
    }

    public String getScandocind() {
        return scandocind;
    }

    public void setScandocind(String scandocind) {
        this.scandocind = scandocind == null ? null : scandocind.trim();
    }

    public String getOriginclientcode() {
        return originclientcode;
    }

    public void setOriginclientcode(String originclientcode) {
        this.originclientcode = originclientcode == null ? null : originclientcode.trim();
    }

    public String getRelateclientcode() {
        return relateclientcode;
    }

    public void setRelateclientcode(String relateclientcode) {
        this.relateclientcode = relateclientcode == null ? null : relateclientcode.trim();
    }

    public String getCreatorcode() {
        return creatorcode;
    }

    public void setCreatorcode(String creatorcode) {
        this.creatorcode = creatorcode == null ? null : creatorcode.trim();
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getUpdatercode() {
        return updatercode;
    }

    public void setUpdatercode(String updatercode) {
        this.updatercode = updatercode == null ? null : updatercode.trim();
    }

    public Date getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    public String getItemprovincecode() {
        return itemprovincecode;
    }

    public void setItemprovincecode(String itemprovincecode) {
        this.itemprovincecode = itemprovincecode == null ? null : itemprovincecode.trim();
    }

    public String getItemcitycode() {
        return itemcitycode;
    }

    public void setItemcitycode(String itemcitycode) {
        this.itemcitycode = itemcitycode == null ? null : itemcitycode.trim();
    }

    public String getItemprovincecname() {
        return itemprovincecname;
    }

    public void setItemprovincecname(String itemprovincecname) {
        this.itemprovincecname = itemprovincecname == null ? null : itemprovincecname.trim();
    }

    public String getItemcitycname() {
        return itemcitycname;
    }

    public void setItemcitycname(String itemcitycname) {
        this.itemcitycname = itemcitycname == null ? null : itemcitycname.trim();
    }

    public String getStarlevel() {
        return starlevel;
    }

    public void setStarlevel(String starlevel) {
        this.starlevel = starlevel == null ? null : starlevel.trim();
    }

    public String getBankname() {
        return bankname;
    }

    public void setBankname(String bankname) {
        this.bankname = bankname;
    }

    public Date getUpdatesysdate() {
        return updatesysdate;
    }

    public void setUpdatesysdate(Date updatesysdate) {
        this.updatesysdate = updatesysdate;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getIndustrymaincode() {
        return industrymaincode;
    }

    public void setIndustrymaincode(String industrymaincode) {
        this.industrymaincode = industrymaincode;
    }

    public String getIndustrykindcode() {
        return industrykindcode;
    }

    public void setIndustrykindcode(String industrykindcode) {
        this.industrykindcode = industrykindcode;
    }

    public String getTallagetype() {
        return tallagetype;
    }

    public void setTallagetype(String tallagetype) {
        this.tallagetype = tallagetype;
    }

    public String getTallageno() {
        return tallageno;
    }

    public void setTallageno(String tallageno) {
        this.tallageno = tallageno;
    }

    public String getCorporateaddress() {
        return corporateaddress;
    }

    public void setCorporateaddress(String corporateaddress) {
        this.corporateaddress = corporateaddress;
    }

    public String getOpeningbank() {
        return openingbank;
    }

    public void setOpeningbank(String openingbank) {
        this.openingbank = openingbank;
    }

    public String getTallagebankcode() {
        return tallagebankcode;
    }

    public void setTallagebankcode(String tallagebankcode) {
        this.tallagebankcode = tallagebankcode;
    }

    public String getCorporatetel() {
        return corporatetel;
    }

    public void setCorporatetel(String corporatetel) {
        this.corporatetel = corporatetel;
    }

    public String getSendaddress() {
        return sendaddress;
    }

    public void setSendaddress(String sendaddress) {
        this.sendaddress = sendaddress;
    }

    public String getTallageremarks() {
        return tallageremarks;
    }

    public void setTallageremarks(String tallageremarks) {
        this.tallageremarks = tallageremarks;
    }

    public String getInvoicetype() {
        return invoicetype;
    }

    public void setInvoicetype(String invoicetype) {
        this.invoicetype = invoicetype;
    }

    public String getRegistercode() {
        return registercode;
    }

    public void setRegistercode(String registercode) {
        this.registercode = registercode;
    }
}