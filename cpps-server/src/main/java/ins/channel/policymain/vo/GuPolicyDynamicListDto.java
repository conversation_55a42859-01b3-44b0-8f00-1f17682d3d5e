package ins.channel.policymain.vo;

public class GuPolicyDynamicListDto {
    private String policyno;

    private String dynamiclisttype;

    private String plancode;

    private String riskcode;

    private Double itemno;

    private Double listseqno;

    private String flag;

    private String itemdetailno;

    private String subpolicyno;

    private String listbelongind;

    private String fieldaa;

    private String fieldab;

    private String fieldac;

    private String fieldad;

    private String fieldae;

    private String fieldaf;

    private String fieldag;

    private String fieldah;

    private String fieldai;

    private String fieldaj;

    private String fieldak;

    private String fieldal;

    private String fieldam;

    private String fieldan;

    private String fieldao;

    private String fieldap;

    private String fieldaq;

    private String fieldar;

    private String fieldas;

    private String fieldat;

    private String fieldau;

    private String fieldav;

    private String fieldaw;

    private String fieldax;

    private String fielday;

    private String fieldaz;
    
    //入职日期
    private String endordate;
    //生效日期
    private String validdate;
    //批单号
    private String endorno;

    //应收保费
    private String grosspremium;
    //保费
    private String premium;

    public String getGrosspremium() {
        return grosspremium;
    }

    public void setGrosspremium(String grosspremium) {
        this.grosspremium = grosspremium;
    }

    public String getPremium() {
        return premium;
    }

    public void setPremium(String premium) {
        this.premium = premium;
    }

    public String getEndordate() {
        return endordate;
    }

    public void setEndordate(String endordate) {
        this.endordate = endordate;
    }

    public String getValiddate() {
        return validdate;
    }

    public void setValiddate(String validdate) {
        this.validdate = validdate;
    }

    public String getEndorno() {
        return endorno;
    }

    public void setEndorno(String endorno) {
        this.endorno = endorno;
    }

    public String getPolicyno() {
        return policyno;
    }

    public void setPolicyno(String policyno) {
        this.policyno = policyno == null ? null : policyno.trim();
    }

    public String getDynamiclisttype() {
        return dynamiclisttype;
    }

    public void setDynamiclisttype(String dynamiclisttype) {
        this.dynamiclisttype = dynamiclisttype == null ? null : dynamiclisttype.trim();
    }

    public String getPlancode() {
        return plancode;
    }

    public void setPlancode(String plancode) {
        this.plancode = plancode == null ? null : plancode.trim();
    }

    public String getRiskcode() {
        return riskcode;
    }

    public void setRiskcode(String riskcode) {
        this.riskcode = riskcode == null ? null : riskcode.trim();
    }

    public Double getItemno() {
        return itemno;
    }

    public void setItemno(Double itemno) {
        this.itemno = itemno;
    }

    public Double getListseqno() {
        return listseqno;
    }

    public void setListseqno(Double listseqno) {
        this.listseqno = listseqno;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag == null ? null : flag.trim();
    }

    public String getItemdetailno() {
        return itemdetailno;
    }

    public void setItemdetailno(String itemdetailno) {
        this.itemdetailno = itemdetailno == null ? null : itemdetailno.trim();
    }

    public String getSubpolicyno() {
        return subpolicyno;
    }

    public void setSubpolicyno(String subpolicyno) {
        this.subpolicyno = subpolicyno == null ? null : subpolicyno.trim();
    }

    public String getListbelongind() {
        return listbelongind;
    }

    public void setListbelongind(String listbelongind) {
        this.listbelongind = listbelongind == null ? null : listbelongind.trim();
    }

    public String getFieldaa() {
        return fieldaa;
    }

    public void setFieldaa(String fieldaa) {
        this.fieldaa = fieldaa == null ? null : fieldaa.trim();
    }

    public String getFieldab() {
        return fieldab;
    }

    public void setFieldab(String fieldab) {
        this.fieldab = fieldab == null ? null : fieldab.trim();
    }

    public String getFieldac() {
        return fieldac;
    }

    public void setFieldac(String fieldac) {
        this.fieldac = fieldac == null ? null : fieldac.trim();
    }

    public String getFieldad() {
        return fieldad;
    }

    public void setFieldad(String fieldad) {
        this.fieldad = fieldad == null ? null : fieldad.trim();
    }

    public String getFieldae() {
        return fieldae;
    }

    public void setFieldae(String fieldae) {
        this.fieldae = fieldae == null ? null : fieldae.trim();
    }

    public String getFieldaf() {
        return fieldaf;
    }

    public void setFieldaf(String fieldaf) {
        this.fieldaf = fieldaf == null ? null : fieldaf.trim();
    }

    public String getFieldag() {
        return fieldag;
    }

    public void setFieldag(String fieldag) {
        this.fieldag = fieldag == null ? null : fieldag.trim();
    }

    public String getFieldah() {
        return fieldah;
    }

    public void setFieldah(String fieldah) {
        this.fieldah = fieldah == null ? null : fieldah.trim();
    }

    public String getFieldai() {
        return fieldai;
    }

    public void setFieldai(String fieldai) {
        this.fieldai = fieldai == null ? null : fieldai.trim();
    }

    public String getFieldaj() {
        return fieldaj;
    }

    public void setFieldaj(String fieldaj) {
        this.fieldaj = fieldaj == null ? null : fieldaj.trim();
    }

    public String getFieldak() {
        return fieldak;
    }

    public void setFieldak(String fieldak) {
        this.fieldak = fieldak == null ? null : fieldak.trim();
    }

    public String getFieldal() {
        return fieldal;
    }

    public void setFieldal(String fieldal) {
        this.fieldal = fieldal == null ? null : fieldal.trim();
    }

    public String getFieldam() {
        return fieldam;
    }

    public void setFieldam(String fieldam) {
        this.fieldam = fieldam == null ? null : fieldam.trim();
    }

    public String getFieldan() {
        return fieldan;
    }

    public void setFieldan(String fieldan) {
        this.fieldan = fieldan == null ? null : fieldan.trim();
    }

    public String getFieldao() {
        return fieldao;
    }

    public void setFieldao(String fieldao) {
        this.fieldao = fieldao == null ? null : fieldao.trim();
    }

    public String getFieldap() {
        return fieldap;
    }

    public void setFieldap(String fieldap) {
        this.fieldap = fieldap == null ? null : fieldap.trim();
    }

    public String getFieldaq() {
        return fieldaq;
    }

    public void setFieldaq(String fieldaq) {
        this.fieldaq = fieldaq == null ? null : fieldaq.trim();
    }

    public String getFieldar() {
        return fieldar;
    }

    public void setFieldar(String fieldar) {
        this.fieldar = fieldar == null ? null : fieldar.trim();
    }

    public String getFieldas() {
        return fieldas;
    }

    public void setFieldas(String fieldas) {
        this.fieldas = fieldas == null ? null : fieldas.trim();
    }

    public String getFieldat() {
        return fieldat;
    }

    public void setFieldat(String fieldat) {
        this.fieldat = fieldat == null ? null : fieldat.trim();
    }

    public String getFieldau() {
        return fieldau;
    }

    public void setFieldau(String fieldau) {
        this.fieldau = fieldau == null ? null : fieldau.trim();
    }

    public String getFieldav() {
        return fieldav;
    }

    public void setFieldav(String fieldav) {
        this.fieldav = fieldav == null ? null : fieldav.trim();
    }

    public String getFieldaw() {
        return fieldaw;
    }

    public void setFieldaw(String fieldaw) {
        this.fieldaw = fieldaw == null ? null : fieldaw.trim();
    }

    public String getFieldax() {
        return fieldax;
    }

    public void setFieldax(String fieldax) {
        this.fieldax = fieldax == null ? null : fieldax.trim();
    }

    public String getFielday() {
        return fielday;
    }

    public void setFielday(String fielday) {
        this.fielday = fielday == null ? null : fielday.trim();
    }

    public String getFieldaz() {
        return fieldaz;
    }

    public void setFieldaz(String fieldaz) {
        this.fieldaz = fieldaz == null ? null : fieldaz.trim();
    }
}
