package ins.channel.policymain.vo;

public class GuPolicyItemAcciDto {
    private String policyno;

    private String plancode;

    private String riskcode;

    private Double itemno;

    private String currency;

    private String itemcode;

    private String itemname;

    private String projectcode;

    private String projectname;

    private String occupation;

    private String jobtitle;

    private Double quantity;

    private Double groupdiscount;

    private String rationtype;

    private Double uwcount;

    private String flag;

    private String journeyback;

    private String contracttype;

    private Double contractvalue;

    private String journeyend;

    private Double aop;

    private String dependantind;

    private String sortind;

    private Double unitcount;

    private String nominativeind;

    private String traveltimestype;

    private String uploadind;

    private Double maininsuredcount;

    private Double contractarea;

    private String calculatetype;

    private Double subinsuredcount;

    private String uploadstatusind;

    private String abroadind;

    private String travelmode;

    private String journeystart;

    private String creditlevel;

    private Double aoka;

    private String occupationtype;

    private String occupationtypename;

    private String occupationlevel;

    private String occupationcode;

    private String remark;

    private String contractaddress;

    private String contracttitle;

    private Double unitinsured;
    
    private Double unitpremium;
    
    private Double grosspremium;
    
    private String insurancemethod;//投保方式

    private String vaccinetype;//疫苗类型
    
    private String flightno;
    
    private String startclaimtime;
    
    public String getFlightno() {
		return flightno;
	}

	public void setFlightno(String flightno) {
		this.flightno = flightno;
	}

	public String getStartclaimtime() {
		return startclaimtime;
	}

	public void setStartclaimtime(String startclaimtime) {
		this.startclaimtime = startclaimtime;
	}
	public String getInsurancemethod() {
		return insurancemethod;
	}

	public void setInsurancemethod(String insurancemethod) {
		this.insurancemethod = insurancemethod;
	}

	public String getVaccinetype() {
		return vaccinetype;
	}

	public void setVaccinetype(String vaccinetype) {
		this.vaccinetype = vaccinetype;
	}

	public Double getUnitinsured() {
		return unitinsured;
	}

	public void setUnitinsured(Double unitinsured) {
		this.unitinsured = unitinsured;
	}

	public Double getUnitpremium() {
		return unitpremium;
	}

	public void setUnitpremium(Double unitpremium) {
		this.unitpremium = unitpremium;
	}

	public Double getGrosspremium() {
		return grosspremium;
	}

	public void setGrosspremium(Double grosspremium) {
		this.grosspremium = grosspremium;
	}

	public String getPolicyno() {
        return policyno;
    }

    public void setPolicyno(String policyno) {
        this.policyno = policyno == null ? null : policyno.trim();
    }

    public String getPlancode() {
        return plancode;
    }

    public void setPlancode(String plancode) {
        this.plancode = plancode == null ? null : plancode.trim();
    }

    public String getRiskcode() {
        return riskcode;
    }

    public void setRiskcode(String riskcode) {
        this.riskcode = riskcode == null ? null : riskcode.trim();
    }

    public Double getItemno() {
        return itemno;
    }

    public void setItemno(Double itemno) {
        this.itemno = itemno;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode == null ? null : itemcode.trim();
    }

    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname == null ? null : itemname.trim();
    }

    public String getProjectcode() {
        return projectcode;
    }

    public void setProjectcode(String projectcode) {
        this.projectcode = projectcode == null ? null : projectcode.trim();
    }

    public String getProjectname() {
        return projectname;
    }

    public void setProjectname(String projectname) {
        this.projectname = projectname == null ? null : projectname.trim();
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation == null ? null : occupation.trim();
    }

    public String getJobtitle() {
        return jobtitle;
    }

    public void setJobtitle(String jobtitle) {
        this.jobtitle = jobtitle == null ? null : jobtitle.trim();
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public Double getGroupdiscount() {
        return groupdiscount;
    }

    public void setGroupdiscount(Double groupdiscount) {
        this.groupdiscount = groupdiscount;
    }

    public String getRationtype() {
        return rationtype;
    }

    public void setRationtype(String rationtype) {
        this.rationtype = rationtype == null ? null : rationtype.trim();
    }

    public Double getUwcount() {
        return uwcount;
    }

    public void setUwcount(Double uwcount) {
        this.uwcount = uwcount;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag == null ? null : flag.trim();
    }

    public String getJourneyback() {
        return journeyback;
    }

    public void setJourneyback(String journeyback) {
        this.journeyback = journeyback == null ? null : journeyback.trim();
    }

    public String getContracttype() {
        return contracttype;
    }

    public void setContracttype(String contracttype) {
        this.contracttype = contracttype == null ? null : contracttype.trim();
    }

    public Double getContractvalue() {
        return contractvalue;
    }

    public void setContractvalue(Double contractvalue) {
        this.contractvalue = contractvalue;
    }

    public String getJourneyend() {
        return journeyend;
    }

    public void setJourneyend(String journeyend) {
        this.journeyend = journeyend == null ? null : journeyend.trim();
    }

    public Double getAop() {
        return aop;
    }

    public void setAop(Double aop) {
        this.aop = aop;
    }

    public String getDependantind() {
        return dependantind;
    }

    public void setDependantind(String dependantind) {
        this.dependantind = dependantind == null ? null : dependantind.trim();
    }

    public String getSortind() {
        return sortind;
    }

    public void setSortind(String sortind) {
        this.sortind = sortind == null ? null : sortind.trim();
    }

    public Double getUnitcount() {
        return unitcount;
    }

    public void setUnitcount(Double unitcount) {
        this.unitcount = unitcount;
    }

    public String getNominativeind() {
        return nominativeind;
    }

    public void setNominativeind(String nominativeind) {
        this.nominativeind = nominativeind == null ? null : nominativeind.trim();
    }

    public String getTraveltimestype() {
        return traveltimestype;
    }

    public void setTraveltimestype(String traveltimestype) {
        this.traveltimestype = traveltimestype == null ? null : traveltimestype.trim();
    }

    public String getUploadind() {
        return uploadind;
    }

    public void setUploadind(String uploadind) {
        this.uploadind = uploadind == null ? null : uploadind.trim();
    }

    public Double getMaininsuredcount() {
        return maininsuredcount;
    }

    public void setMaininsuredcount(Double maininsuredcount) {
        this.maininsuredcount = maininsuredcount;
    }

    public Double getContractarea() {
        return contractarea;
    }

    public void setContractarea(Double contractarea) {
        this.contractarea = contractarea;
    }

    public String getCalculatetype() {
        return calculatetype;
    }

    public void setCalculatetype(String calculatetype) {
        this.calculatetype = calculatetype == null ? null : calculatetype.trim();
    }

    public Double getSubinsuredcount() {
        return subinsuredcount;
    }

    public void setSubinsuredcount(Double subinsuredcount) {
        this.subinsuredcount = subinsuredcount;
    }

    public String getUploadstatusind() {
        return uploadstatusind;
    }

    public void setUploadstatusind(String uploadstatusind) {
        this.uploadstatusind = uploadstatusind == null ? null : uploadstatusind.trim();
    }

    public String getAbroadind() {
        return abroadind;
    }

    public void setAbroadind(String abroadind) {
        this.abroadind = abroadind == null ? null : abroadind.trim();
    }

    public String getTravelmode() {
        return travelmode;
    }

    public void setTravelmode(String travelmode) {
        this.travelmode = travelmode == null ? null : travelmode.trim();
    }

    public String getJourneystart() {
        return journeystart;
    }

    public void setJourneystart(String journeystart) {
        this.journeystart = journeystart == null ? null : journeystart.trim();
    }

    public String getCreditlevel() {
        return creditlevel;
    }

    public void setCreditlevel(String creditlevel) {
        this.creditlevel = creditlevel == null ? null : creditlevel.trim();
    }

    public Double getAoka() {
        return aoka;
    }

    public void setAoka(Double aoka) {
        this.aoka = aoka;
    }

    public String getOccupationtype() {
        return occupationtype;
    }

    public void setOccupationtype(String occupationtype) {
        this.occupationtype = occupationtype == null ? null : occupationtype.trim();
    }

    public String getOccupationtypename() {
        return occupationtypename;
    }

    public void setOccupationtypename(String occupationtypename) {
        this.occupationtypename = occupationtypename == null ? null : occupationtypename.trim();
    }

    public String getOccupationlevel() {
        return occupationlevel;
    }

    public void setOccupationlevel(String occupationlevel) {
        this.occupationlevel = occupationlevel == null ? null : occupationlevel.trim();
    }

    public String getOccupationcode() {
        return occupationcode;
    }

    public void setOccupationcode(String occupationcode) {
        this.occupationcode = occupationcode == null ? null : occupationcode.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getContractaddress() {
        return contractaddress;
    }

    public void setContractaddress(String contractaddress) {
        this.contractaddress = contractaddress == null ? null : contractaddress.trim();
    }

    public String getContracttitle() {
        return contracttitle;
    }

    public void setContracttitle(String contracttitle) {
        this.contracttitle = contracttitle == null ? null : contracttitle.trim();
    }
}