package ins.channel.policymain.vo;

import java.util.Date;

public class GuPolicyItemAcciListDto {
    private String policyno;

    private String plancode;

    private String riskcode;

    private Double itemno;

    private Double clientno;

    private String itemcode;

    private String itemname;

    private Double itemdetailno;

    private String itemdetailcode;

    private String itemdetailname;

    private String clientcode;

    private String memberno;

    private String clientcname;

    private String clientename;

    private String sex;

    private Date birthday;

    private Double age;

    private String identifytypea;

    private String identifynoa;

    private String identifytypeb;

    private String identifynob;

    private Date enrollmentdate;

    private Date startdate;

    private Date enddate;

    private String bankname;

    private String bankaccountno;

    private String creditno;

    private Date creditexpiry;

    private String occupationtype;

    private String occupation;

    private String occupationcode;

    private String jobtitle;

    private String homeaddress;

    private String hometel;

    private String businessnature;

    private String jobunitcode;

    private String jobunitname;

    private String officetel;

    private String employername;

    private String employeeind;

    private String maternityind;

    private String autopayind;

    private String relationcode;

    private String relationship;

    private String projectcode;

    private Double uwcount;

    private Double suminsured;

    private Double basepremium;

    private Double netpremium;

    private Double grosspremium;

    private Double annualpremium;

    private Double proratapremium;

    private String exprirefund;

    private String preexistind;

    private String activeind;

    private Date commencedate;

    private String email;

    private String district;

    private String ipaservice;

    private String countrycode;

    private String registaddress;

    private String memberrefrence;

    private Double discount;

    private String flag;

    private String journeystart;

    private String journeyend;

    private String journeyback;

    private String applirelation;

    private String linkername;

    private String linkerphone;

    private Double pledged;

    private String claimpayway;

    private String clienttype;

    private String occupationtypename;

    private String occupationlevel;

    private String maininsuredind;

    private String surname;

    private String moniker;

    private String firstname;

    private String lastname;

    private String provincecode;

    private String citycode;

    private String countycode;

    private Double displayno;

    private String specialclause;

    private String remark;

    private String innerremark;
    
    private String relationWithHolder;
    
    private String vaccination;//接种疫苗

    private Date inoculabilitytime;//接种时间
    
    /**
     * 渠道需要添加节点
     */
    private String benefitName;
    
    private String benefitIdentifyNo;

    private String itemaddress;
    
    private Date driverlicenseexpirationdate;
    
    private String driverlicenseno;
    
    private String drivingModel;
    
    private String useNatureCode;
    
    private String driverType;
    
    private String platcustomno;
   
    private String onthejobstatus;//在职状态（01：在职；02：退保；09：其他）1144

    private String department;

    //批单信息

    //入职日期
    private String endordate;
    //生效日期
    private String validdate;
    //批单号
    private String endorno;

    public String getEndordate() {
        return endordate;
    }

    public void setEndordate(String endordate) {
        this.endordate = endordate;
    }

    public String getValiddate() {
        return validdate;
    }

    public void setValiddate(String validdate) {
        this.validdate = validdate;
    }

    public String getEndorno() {
        return endorno;
    }

    public void setEndorno(String endorno) {
        this.endorno = endorno;
    }

    public String getPlatcustomno() {
		return platcustomno;
	}

	public void setPlatcustomno(String platcustomno) {
		this.platcustomno = platcustomno;
	}

	public String getVaccination() {
		return vaccination;
	}

	public void setVaccination(String vaccination) {
		this.vaccination = vaccination;
	}

	

	public Date getInoculabilitytime() {
		return inoculabilitytime;
	}

	public void setInoculabilitytime(Date inoculabilitytime) {
		this.inoculabilitytime = inoculabilitytime;
	}

	public String getDriverType() {
		return driverType;
	}

	public void setDriverType(String driverType) {
		this.driverType = driverType;
	}

	public String getDriverlicenseno() {
		return driverlicenseno;
	}

	public void setDriverlicenseno(String driverlicenseno) {
		this.driverlicenseno = driverlicenseno;
	}

	public String getDrivingModel() {
		return drivingModel;
	}

	public void setDrivingModel(String drivingModel) {
		this.drivingModel = drivingModel;
	}

	public String getUseNatureCode() {
		return useNatureCode;
	}

	public void setUseNatureCode(String useNatureCode) {
		this.useNatureCode = useNatureCode;
	}

	public Date getDriverlicenseexpirationdate() {
		return driverlicenseexpirationdate;
	}

	public void setDriverlicenseexpirationdate(Date driverlicenseexpirationdate) {
		this.driverlicenseexpirationdate = driverlicenseexpirationdate;
	}
    
    public String getItemaddress() {
		return itemaddress;
	}

	public void setItemaddress(String itemaddress) {
		this.itemaddress = itemaddress;
	}

    public String getBenefitName() {
		return benefitName;
	}

	public void setBenefitName(String benefitName) {
		this.benefitName = benefitName;
	}

	public String getBenefitIdentifyNo() {
		return benefitIdentifyNo;
	}

	public void setBenefitIdentifyNo(String benefitIdentifyNo) {
		this.benefitIdentifyNo = benefitIdentifyNo;
	}

	public String getPolicyno() {
        return policyno;
    }

    public void setPolicyno(String policyno) {
        this.policyno = policyno == null ? null : policyno.trim();
    }

    public String getPlancode() {
        return plancode;
    }

    public void setPlancode(String plancode) {
        this.plancode = plancode == null ? null : plancode.trim();
    }

    public String getRiskcode() {
        return riskcode;
    }

    public void setRiskcode(String riskcode) {
        this.riskcode = riskcode == null ? null : riskcode.trim();
    }

    public Double getItemno() {
        return itemno;
    }

    public void setItemno(Double itemno) {
        this.itemno = itemno;
    }

    public Double getClientno() {
        return clientno;
    }

    public void setClientno(Double clientno) {
        this.clientno = clientno;
    }

    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode == null ? null : itemcode.trim();
    }

    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname == null ? null : itemname.trim();
    }

    public Double getItemdetailno() {
        return itemdetailno;
    }

    public void setItemdetailno(Double itemdetailno) {
        this.itemdetailno = itemdetailno;
    }

    public String getItemdetailcode() {
        return itemdetailcode;
    }

    public void setItemdetailcode(String itemdetailcode) {
        this.itemdetailcode = itemdetailcode == null ? null : itemdetailcode.trim();
    }

    public String getItemdetailname() {
        return itemdetailname;
    }

    public void setItemdetailname(String itemdetailname) {
        this.itemdetailname = itemdetailname == null ? null : itemdetailname.trim();
    }

    public String getClientcode() {
        return clientcode;
    }

    public void setClientcode(String clientcode) {
        this.clientcode = clientcode == null ? null : clientcode.trim();
    }

    public String getMemberno() {
        return memberno;
    }

    public void setMemberno(String memberno) {
        this.memberno = memberno == null ? null : memberno.trim();
    }

    public String getClientcname() {
        return clientcname;
    }

    public void setClientcname(String clientcname) {
        this.clientcname = clientcname == null ? null : clientcname.trim();
    }

    public String getClientename() {
        return clientename;
    }

    public void setClientename(String clientename) {
        this.clientename = clientename == null ? null : clientename.trim();
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex == null ? null : sex.trim();
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Double getAge() {
        return age;
    }

    public void setAge(Double age) {
        this.age = age;
    }

    public String getIdentifytypea() {
        return identifytypea;
    }

    public void setIdentifytypea(String identifytypea) {
        this.identifytypea = identifytypea == null ? null : identifytypea.trim();
    }

    public String getIdentifynoa() {
        return identifynoa;
    }

    public void setIdentifynoa(String identifynoa) {
        this.identifynoa = identifynoa == null ? null : identifynoa.trim();
    }

    public String getIdentifytypeb() {
        return identifytypeb;
    }

    public void setIdentifytypeb(String identifytypeb) {
        this.identifytypeb = identifytypeb == null ? null : identifytypeb.trim();
    }

    public String getIdentifynob() {
        return identifynob;
    }

    public void setIdentifynob(String identifynob) {
        this.identifynob = identifynob == null ? null : identifynob.trim();
    }

    public Date getEnrollmentdate() {
        return enrollmentdate;
    }

    public void setEnrollmentdate(Date enrollmentdate) {
        this.enrollmentdate = enrollmentdate;
    }

    public Date getStartdate() {
        return startdate;
    }

    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }

    public Date getEnddate() {
        return enddate;
    }

    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }

    public String getBankname() {
        return bankname;
    }

    public void setBankname(String bankname) {
        this.bankname = bankname == null ? null : bankname.trim();
    }

    public String getBankaccountno() {
        return bankaccountno;
    }

    public void setBankaccountno(String bankaccountno) {
        this.bankaccountno = bankaccountno == null ? null : bankaccountno.trim();
    }

    public String getCreditno() {
        return creditno;
    }

    public void setCreditno(String creditno) {
        this.creditno = creditno == null ? null : creditno.trim();
    }

    public Date getCreditexpiry() {
        return creditexpiry;
    }

    public void setCreditexpiry(Date creditexpiry) {
        this.creditexpiry = creditexpiry;
    }

    public String getOccupationtype() {
        return occupationtype;
    }

    public void setOccupationtype(String occupationtype) {
        this.occupationtype = occupationtype == null ? null : occupationtype.trim();
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation == null ? null : occupation.trim();
    }

    public String getOccupationcode() {
        return occupationcode;
    }

    public void setOccupationcode(String occupationcode) {
        this.occupationcode = occupationcode == null ? null : occupationcode.trim();
    }

    public String getJobtitle() {
        return jobtitle;
    }

    public void setJobtitle(String jobtitle) {
        this.jobtitle = jobtitle == null ? null : jobtitle.trim();
    }

    public String getHomeaddress() {
        return homeaddress;
    }

    public void setHomeaddress(String homeaddress) {
        this.homeaddress = homeaddress == null ? null : homeaddress.trim();
    }

    public String getHometel() {
        return hometel;
    }

    public void setHometel(String hometel) {
        this.hometel = hometel == null ? null : hometel.trim();
    }

    public String getBusinessnature() {
        return businessnature;
    }

    public void setBusinessnature(String businessnature) {
        this.businessnature = businessnature == null ? null : businessnature.trim();
    }

    public String getJobunitcode() {
        return jobunitcode;
    }

    public void setJobunitcode(String jobunitcode) {
        this.jobunitcode = jobunitcode == null ? null : jobunitcode.trim();
    }

    public String getJobunitname() {
        return jobunitname;
    }

    public void setJobunitname(String jobunitname) {
        this.jobunitname = jobunitname == null ? null : jobunitname.trim();
    }

    public String getOfficetel() {
        return officetel;
    }

    public void setOfficetel(String officetel) {
        this.officetel = officetel == null ? null : officetel.trim();
    }

    public String getEmployername() {
        return employername;
    }

    public void setEmployername(String employername) {
        this.employername = employername == null ? null : employername.trim();
    }

    public String getEmployeeind() {
        return employeeind;
    }

    public void setEmployeeind(String employeeind) {
        this.employeeind = employeeind == null ? null : employeeind.trim();
    }

    public String getMaternityind() {
        return maternityind;
    }

    public void setMaternityind(String maternityind) {
        this.maternityind = maternityind == null ? null : maternityind.trim();
    }

    public String getAutopayind() {
        return autopayind;
    }

    public void setAutopayind(String autopayind) {
        this.autopayind = autopayind == null ? null : autopayind.trim();
    }

    public String getRelationcode() {
        return relationcode;
    }

    public void setRelationcode(String relationcode) {
        this.relationcode = relationcode == null ? null : relationcode.trim();
    }

    public String getRelationship() {
        return relationship;
    }

    public void setRelationship(String relationship) {
        this.relationship = relationship == null ? null : relationship.trim();
    }

    public String getProjectcode() {
        return projectcode;
    }

    public void setProjectcode(String projectcode) {
        this.projectcode = projectcode == null ? null : projectcode.trim();
    }

    public Double getUwcount() {
        return uwcount;
    }

    public void setUwcount(Double uwcount) {
        this.uwcount = uwcount;
    }

    public Double getSuminsured() {
        return suminsured;
    }

    public void setSuminsured(Double suminsured) {
        this.suminsured = suminsured;
    }

    public Double getBasepremium() {
        return basepremium;
    }

    public void setBasepremium(Double basepremium) {
        this.basepremium = basepremium;
    }

    public Double getNetpremium() {
        return netpremium;
    }

    public void setNetpremium(Double netpremium) {
        this.netpremium = netpremium;
    }

    public Double getGrosspremium() {
        return grosspremium;
    }

    public void setGrosspremium(Double grosspremium) {
        this.grosspremium = grosspremium;
    }

    public Double getAnnualpremium() {
        return annualpremium;
    }

    public void setAnnualpremium(Double annualpremium) {
        this.annualpremium = annualpremium;
    }

    public Double getProratapremium() {
        return proratapremium;
    }

    public void setProratapremium(Double proratapremium) {
        this.proratapremium = proratapremium;
    }

    public String getExprirefund() {
        return exprirefund;
    }

    public void setExprirefund(String exprirefund) {
        this.exprirefund = exprirefund == null ? null : exprirefund.trim();
    }

    public String getPreexistind() {
        return preexistind;
    }

    public void setPreexistind(String preexistind) {
        this.preexistind = preexistind == null ? null : preexistind.trim();
    }

    public String getActiveind() {
        return activeind;
    }

    public void setActiveind(String activeind) {
        this.activeind = activeind == null ? null : activeind.trim();
    }

    public Date getCommencedate() {
        return commencedate;
    }

    public void setCommencedate(Date commencedate) {
        this.commencedate = commencedate;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district == null ? null : district.trim();
    }

    public String getIpaservice() {
        return ipaservice;
    }

    public void setIpaservice(String ipaservice) {
        this.ipaservice = ipaservice == null ? null : ipaservice.trim();
    }

    public String getCountrycode() {
        return countrycode;
    }

    public void setCountrycode(String countrycode) {
        this.countrycode = countrycode == null ? null : countrycode.trim();
    }

    public String getRegistaddress() {
        return registaddress;
    }

    public void setRegistaddress(String registaddress) {
        this.registaddress = registaddress == null ? null : registaddress.trim();
    }

    public String getMemberrefrence() {
        return memberrefrence;
    }

    public void setMemberrefrence(String memberrefrence) {
        this.memberrefrence = memberrefrence == null ? null : memberrefrence.trim();
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag == null ? null : flag.trim();
    }

    public String getJourneystart() {
        return journeystart;
    }

    public void setJourneystart(String journeystart) {
        this.journeystart = journeystart == null ? null : journeystart.trim();
    }

    public String getJourneyend() {
        return journeyend;
    }

    public void setJourneyend(String journeyend) {
        this.journeyend = journeyend == null ? null : journeyend.trim();
    }

    public String getJourneyback() {
        return journeyback;
    }

    public void setJourneyback(String journeyback) {
        this.journeyback = journeyback == null ? null : journeyback.trim();
    }

    public String getApplirelation() {
        return applirelation;
    }

    public void setApplirelation(String applirelation) {
        this.applirelation = applirelation == null ? null : applirelation.trim();
    }

    public String getLinkername() {
        return linkername;
    }

    public void setLinkername(String linkername) {
        this.linkername = linkername == null ? null : linkername.trim();
    }

    public String getLinkerphone() {
        return linkerphone;
    }

    public void setLinkerphone(String linkerphone) {
        this.linkerphone = linkerphone == null ? null : linkerphone.trim();
    }

    public Double getPledged() {
        return pledged;
    }

    public void setPledged(Double pledged) {
        this.pledged = pledged;
    }

    public String getClaimpayway() {
        return claimpayway;
    }

    public void setClaimpayway(String claimpayway) {
        this.claimpayway = claimpayway == null ? null : claimpayway.trim();
    }

    public String getClienttype() {
        return clienttype;
    }

    public void setClienttype(String clienttype) {
        this.clienttype = clienttype == null ? null : clienttype.trim();
    }

    public String getOccupationtypename() {
        return occupationtypename;
    }

    public void setOccupationtypename(String occupationtypename) {
        this.occupationtypename = occupationtypename == null ? null : occupationtypename.trim();
    }

    public String getOccupationlevel() {
        return occupationlevel;
    }

    public void setOccupationlevel(String occupationlevel) {
        this.occupationlevel = occupationlevel == null ? null : occupationlevel.trim();
    }

    public String getMaininsuredind() {
        return maininsuredind;
    }

    public void setMaininsuredind(String maininsuredind) {
        this.maininsuredind = maininsuredind == null ? null : maininsuredind.trim();
    }

    public String getSurname() {
        return surname;
    }

    public void setSurname(String surname) {
        this.surname = surname == null ? null : surname.trim();
    }

    public String getMoniker() {
        return moniker;
    }

    public void setMoniker(String moniker) {
        this.moniker = moniker == null ? null : moniker.trim();
    }

    public String getFirstname() {
        return firstname;
    }

    public void setFirstname(String firstname) {
        this.firstname = firstname == null ? null : firstname.trim();
    }

    public String getLastname() {
        return lastname;
    }

    public void setLastname(String lastname) {
        this.lastname = lastname == null ? null : lastname.trim();
    }

    public String getProvincecode() {
        return provincecode;
    }

    public void setProvincecode(String provincecode) {
        this.provincecode = provincecode == null ? null : provincecode.trim();
    }

    public String getCitycode() {
        return citycode;
    }

    public void setCitycode(String citycode) {
        this.citycode = citycode == null ? null : citycode.trim();
    }

    public String getCountycode() {
        return countycode;
    }

    public void setCountycode(String countycode) {
        this.countycode = countycode == null ? null : countycode.trim();
    }

    public Double getDisplayno() {
        return displayno;
    }

    public void setDisplayno(Double displayno) {
        this.displayno = displayno;
    }

    public String getSpecialclause() {
        return specialclause;
    }

    public void setSpecialclause(String specialclause) {
        this.specialclause = specialclause == null ? null : specialclause.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getInnerremark() {
        return innerremark;
    }

    public void setInnerremark(String innerremark) {
        this.innerremark = innerremark == null ? null : innerremark.trim();
    }

	public String getRelationWithHolder() {
		return relationWithHolder;
	}

	public void setRelationWithHolder(String relationWithHolder) {
		this.relationWithHolder = relationWithHolder;
	}

	public String getOnthejobstatus() {
		return onthejobstatus;
	}

	public void setOnthejobstatus(String onthejobstatus) {
		this.onthejobstatus = onthejobstatus;
	}

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }
}
