package ins.channel.policymain.vo;

import java.util.Date;
import java.util.List;

public class GuPolicyItemKindDto {
    private String policyno;

    private String riskcode;

    private String plancode;

    private Double itemkindno;

    private Double itemno;

    private String itemcode;

    private Double itemdetailno;

    private String itemdetailcode;

    private String projectcode;

    private String liabcode;

    private String kindcode;

    private String kindname;

    private String modecode;

    private String modename;

    private Date startdate;

    private Date enddate;

    private String calculateind;

    private String currency;

    private Double unitvalue;

    private Double unitinsured;

    private Double unitpremium;

    private Double quantity;

    private String unit;

    private Double sumvalue;

    private Double suminsured;

    private Double rateperiod;

    private Double rate;

    private String shortrateflag;

    private Double shortrate;

    private Double shortratenumerator;

    private Double shortratedenominator;

    private Double basepremium;

    private Double benchmarkpremium;

    private Double loading;

    private Double grosspremium;

    private Double discount;

    private Double adjustrate;

    private Double netpremium;

    private Double deductiblerate;

    private Double deductible;

    private String premiumformula;

    private Double yearpremium;

    private String surrenderind;

    private Double firstlossrate;

    private String kindind;

    private String flag;

    private Double uwcount;

    private Double uwpremium;

    private Double originuwpremium;

    private Double origingrosspremium;

    private String relatedind;

    private Double endoradjustrate;

    private String pubinsuredind;

    private String specialind;

    private String subpolicyno;

    private String mustinsureind;

    private String premiumtype;

    private String valuetype;

    private Double franchise;

    private String respecialind;

    private String itemdetaillist;
    //意健险等待期
    private String accidentwaitdays;
    //非意健险等待期
    private String noaccidentwaitdays;

    private Double noTaxPremium;
    /**
     * 保险期间系数
     */
    private Double insuranceperratio;
    
    private Double uwpremiumcny;//总签单保费本位币金额
    
    private String claimcountlimit;


	public Double getInsuranceperratio() {
		return insuranceperratio;
	}

	public void setInsuranceperratio(Double insuranceperratio) {
		this.insuranceperratio = insuranceperratio;
	}

	public Double getNoTaxPremium() {
		return noTaxPremium;
	}

	public void setNoTaxPremium(Double noTaxPremium) {
		this.noTaxPremium = noTaxPremium;
	}

	public String getAccidentwaitdays() {
		return accidentwaitdays;
	}

	public void setAccidentwaitdays(String accidentwaitdays) {
		this.accidentwaitdays = accidentwaitdays;
	}

	public String getNoaccidentwaitdays() {
		return noaccidentwaitdays;
	}

	public void setNoaccidentwaitdays(String noaccidentwaitdays) {
		this.noaccidentwaitdays = noaccidentwaitdays;
	}

	private String remark;

    //电子保单查询新增字段
    private String itemDetailName;

    
    public String getPolicyno() {
        return policyno;
    }

    public void setPolicyno(String policyno) {
        this.policyno = policyno == null ? null : policyno.trim();
    }

    public String getRiskcode() {
        return riskcode;
    }

    public void setRiskcode(String riskcode) {
        this.riskcode = riskcode == null ? null : riskcode.trim();
    }

    public String getPlancode() {
        return plancode;
    }

    public void setPlancode(String plancode) {
        this.plancode = plancode == null ? null : plancode.trim();
    }

    public Double getItemkindno() {
        return itemkindno;
    }

    public void setItemkindno(Double itemkindno) {
        this.itemkindno = itemkindno;
    }

    public Double getItemno() {
        return itemno;
    }

    public void setItemno(Double itemno) {
        this.itemno = itemno;
    }

    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode == null ? null : itemcode.trim();
    }

    public Double getItemdetailno() {
        return itemdetailno;
    }

    public void setItemdetailno(Double itemdetailno) {
        this.itemdetailno = itemdetailno;
    }

    public String getItemdetailcode() {
        return itemdetailcode;
    }

    public void setItemdetailcode(String itemdetailcode) {
        this.itemdetailcode = itemdetailcode == null ? null : itemdetailcode.trim();
    }

    public String getProjectcode() {
        return projectcode;
    }

    public void setProjectcode(String projectcode) {
        this.projectcode = projectcode == null ? null : projectcode.trim();
    }

    public String getLiabcode() {
        return liabcode;
    }

    public void setLiabcode(String liabcode) {
        this.liabcode = liabcode == null ? null : liabcode.trim();
    }

    public String getKindcode() {
        return kindcode;
    }

    public void setKindcode(String kindcode) {
        this.kindcode = kindcode == null ? null : kindcode.trim();
    }

    public String getKindname() {
        return kindname;
    }

    public void setKindname(String kindname) {
        this.kindname = kindname == null ? null : kindname.trim();
    }

    public String getModecode() {
        return modecode;
    }

    public void setModecode(String modecode) {
        this.modecode = modecode == null ? null : modecode.trim();
    }

    public String getModename() {
        return modename;
    }

    public void setModename(String modename) {
        this.modename = modename == null ? null : modename.trim();
    }

    public Date getStartdate() {
        return startdate;
    }

    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }

    public Date getEnddate() {
        return enddate;
    }

    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }

    public String getCalculateind() {
        return calculateind;
    }

    public void setCalculateind(String calculateind) {
        this.calculateind = calculateind == null ? null : calculateind.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public Double getUnitvalue() {
        return unitvalue;
    }

    public void setUnitvalue(Double unitvalue) {
        this.unitvalue = unitvalue;
    }

    public Double getUnitinsured() {
        return unitinsured;
    }

    public void setUnitinsured(Double unitinsured) {
        this.unitinsured = unitinsured;
    }

    public Double getUnitpremium() {
        return unitpremium;
    }

    public void setUnitpremium(Double unitpremium) {
        this.unitpremium = unitpremium;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public Double getSumvalue() {
        return sumvalue;
    }

    public void setSumvalue(Double sumvalue) {
        this.sumvalue = sumvalue;
    }

    public Double getSuminsured() {
        return suminsured;
    }

    public void setSuminsured(Double suminsured) {
        this.suminsured = suminsured;
    }

    public Double getRateperiod() {
        return rateperiod;
    }

    public void setRateperiod(Double rateperiod) {
        this.rateperiod = rateperiod;
    }

    public Double getRate() {
        return rate;
    }

    public void setRate(Double rate) {
        this.rate = rate;
    }

    public String getShortrateflag() {
        return shortrateflag;
    }

    public void setShortrateflag(String shortrateflag) {
        this.shortrateflag = shortrateflag == null ? null : shortrateflag.trim();
    }

    public Double getShortrate() {
        return shortrate;
    }

    public void setShortrate(Double shortrate) {
        this.shortrate = shortrate;
    }

    public Double getShortratenumerator() {
        return shortratenumerator;
    }

    public void setShortratenumerator(Double shortratenumerator) {
        this.shortratenumerator = shortratenumerator;
    }

    public Double getShortratedenominator() {
        return shortratedenominator;
    }

    public void setShortratedenominator(Double shortratedenominator) {
        this.shortratedenominator = shortratedenominator;
    }

    public Double getBasepremium() {
        return basepremium;
    }

    public void setBasepremium(Double basepremium) {
        this.basepremium = basepremium;
    }

    public Double getBenchmarkpremium() {
        return benchmarkpremium;
    }

    public void setBenchmarkpremium(Double benchmarkpremium) {
        this.benchmarkpremium = benchmarkpremium;
    }

    public Double getLoading() {
        return loading;
    }

    public void setLoading(Double loading) {
        this.loading = loading;
    }

    public Double getGrosspremium() {
        return grosspremium;
    }

    public void setGrosspremium(Double grosspremium) {
        this.grosspremium = grosspremium;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Double getAdjustrate() {
        return adjustrate;
    }

    public void setAdjustrate(Double adjustrate) {
        this.adjustrate = adjustrate;
    }

    public Double getNetpremium() {
        return netpremium;
    }

    public void setNetpremium(Double netpremium) {
        this.netpremium = netpremium;
    }

    public Double getDeductiblerate() {
        return deductiblerate;
    }

    public void setDeductiblerate(Double deductiblerate) {
        this.deductiblerate = deductiblerate;
    }

    public Double getDeductible() {
        return deductible;
    }

    public void setDeductible(Double deductible) {
        this.deductible = deductible;
    }

    public String getPremiumformula() {
        return premiumformula;
    }

    public void setPremiumformula(String premiumformula) {
        this.premiumformula = premiumformula == null ? null : premiumformula.trim();
    }

    public Double getYearpremium() {
        return yearpremium;
    }

    public void setYearpremium(Double yearpremium) {
        this.yearpremium = yearpremium;
    }

    public String getSurrenderind() {
        return surrenderind;
    }

    public void setSurrenderind(String surrenderind) {
        this.surrenderind = surrenderind == null ? null : surrenderind.trim();
    }

    public Double getFirstlossrate() {
        return firstlossrate;
    }

    public void setFirstlossrate(Double firstlossrate) {
        this.firstlossrate = firstlossrate;
    }

    public String getKindind() {
        return kindind;
    }

    public void setKindind(String kindind) {
        this.kindind = kindind == null ? null : kindind.trim();
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag == null ? null : flag.trim();
    }

    public Double getUwcount() {
        return uwcount;
    }

    public void setUwcount(Double uwcount) {
        this.uwcount = uwcount;
    }

    public Double getUwpremium() {
        return uwpremium;
    }

    public void setUwpremium(Double uwpremium) {
        this.uwpremium = uwpremium;
    }

    public Double getOriginuwpremium() {
        return originuwpremium;
    }

    public void setOriginuwpremium(Double originuwpremium) {
        this.originuwpremium = originuwpremium;
    }

    public Double getOrigingrosspremium() {
        return origingrosspremium;
    }

    public void setOrigingrosspremium(Double origingrosspremium) {
        this.origingrosspremium = origingrosspremium;
    }

    public String getRelatedind() {
        return relatedind;
    }

    public void setRelatedind(String relatedind) {
        this.relatedind = relatedind == null ? null : relatedind.trim();
    }

    public Double getEndoradjustrate() {
        return endoradjustrate;
    }

    public void setEndoradjustrate(Double endoradjustrate) {
        this.endoradjustrate = endoradjustrate;
    }

    public String getPubinsuredind() {
        return pubinsuredind;
    }

    public void setPubinsuredind(String pubinsuredind) {
        this.pubinsuredind = pubinsuredind == null ? null : pubinsuredind.trim();
    }

    public String getSpecialind() {
        return specialind;
    }

    public void setSpecialind(String specialind) {
        this.specialind = specialind == null ? null : specialind.trim();
    }

    public String getSubpolicyno() {
        return subpolicyno;
    }

    public void setSubpolicyno(String subpolicyno) {
        this.subpolicyno = subpolicyno == null ? null : subpolicyno.trim();
    }

    public String getMustinsureind() {
        return mustinsureind;
    }

    public void setMustinsureind(String mustinsureind) {
        this.mustinsureind = mustinsureind == null ? null : mustinsureind.trim();
    }

    public String getPremiumtype() {
        return premiumtype;
    }

    public void setPremiumtype(String premiumtype) {
        this.premiumtype = premiumtype == null ? null : premiumtype.trim();
    }

    public String getValuetype() {
        return valuetype;
    }

    public void setValuetype(String valuetype) {
        this.valuetype = valuetype == null ? null : valuetype.trim();
    }

    public Double getFranchise() {
        return franchise;
    }

    public void setFranchise(Double franchise) {
        this.franchise = franchise;
    }

    public String getRespecialind() {
        return respecialind;
    }

    public void setRespecialind(String respecialind) {
        this.respecialind = respecialind == null ? null : respecialind.trim();
    }

    public String getItemdetaillist() {
        return itemdetaillist;
    }

    public void setItemdetaillist(String itemdetaillist) {
        this.itemdetaillist = itemdetaillist == null ? null : itemdetaillist.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

	public String getItemDetailName() {
		return itemDetailName;
	}

	public void setItemDetailName(String itemDetailName) {
		this.itemDetailName = itemDetailName;
	}

	public Double getUwpremiumcny() {
		return uwpremiumcny;
	}

	public void setUwpremiumcny(Double uwpremiumcny) {
		this.uwpremiumcny = uwpremiumcny;
	}

	public String getClaimcountlimit() {
		return claimcountlimit;
	}

	public void setClaimcountlimit(String claimcountlimit) {
		this.claimcountlimit = claimcountlimit;
	}
    
}