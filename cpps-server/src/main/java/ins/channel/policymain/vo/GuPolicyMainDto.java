package ins.channel.policymain.vo;

import java.util.Date;

public class GuPolicyMainDto {
    private String policyno;

    private String proposalno;

    private String printno;

    private String businesschannel;

    private String businesssource;

    private String businesstype;

    private String policysort;

    private String language;

    private String groupind;

    private String intermediarycode;

    private String agreementno;

    private String solutioncode;

    private long creditperiod;

    private String indicationno;

    private Double indicationseqno;

    private String quotationno;

    private Double quotationseqno;

    private String holdcoverno;

    private Double holdcoverseqno;

    private String covernoteno;

    private Double covernoteseqno;

    private String agentreferenceno;

    private String renewalno;

    private String replacedpolicyno;

    private String promotioncode;

    private String applicode;

    private String appliname;

    private String appliaddress;

    private String appliaddrtype;

    private String insuredcode;

    private String insuredname;

    private String insuredaddrtype;

    private Date operatedate;

    private Date receiveddate;

    private Date issuedate;

    private String currency;

    private Double suminsured;

    private Double sumgrosspremium;

    private Double sumnetpremium;

    private String arguesolution;

    private String mainport;

    private String subport;

    private String othpolicyno;

    private String theircompanycode;

    private String theircompanyname;

    private String theirreference;

    private String arbitoryname;

    private Long installmentno;

    private Long endorsetimes;

    private Long registtimes;

    private Long claimstimes;

    private Long printtimes;

    private String issuecompany;

    private String issueplace;

    private String companycode;

    private String salesmancode;

    private String approvercode;

    private String operatorcode;

    private Date inputdate;

    private String uwyear;

    private String underwritecode;

    private String underwritename;

    private Date underwriteenddate;

    private String underwriteind;

    private String autorenewind;

    private String multiriskind;

    private String lastmodifiercode;

    private Date lastmodifydate;

    private Double frontingripercent;

    private Double frontingricommissionpercent;

    private Double frontingriadminfeepercent;

    private Double frontingritaxpercent;

    private String renewind;

    private String renewedind;

    private String surrenderind;

    private String endind;

    private Double yearpremium;

    private String cancelind;

    private String lostind;

    private String debtorind;

    private String statind;

    private String sourceid;

    private String migratedclientno;

    private String migratedaccountnum;

    private String migratedpolicyno;

    private String validind;

    private String flag;

    private Double sumuwpremium;

    private String visatype;

    private String imageind;

    private String proposalpolicyind;

    private String hengsangind;

    private String domesticind;

    private String historyind;

    private String replaceproposerind;

    private String handlestatus;

    private Date approveenddate;

    private String dealercode;

    private String codind;

    private Date acceptdate;

    private String specialacceptance;

    private String relatedriskcode;

    private String agrvalidind;

    private String outerusercode;

    private String outercompanycode;

    private String serviceprojectcode;

    private String channeldetailcode;

    private String productcode;

    private String problemind;

    private Date canceltime;

    private String channelflag;

    private String agricultureflag;

    private String historyflag;

    private String calculatetype;

    private String businessind;

    private String salescommissionercode;

    private String cmbprintivcind;

    private String businessmode;

    private String applyno;

    private String oldcarprintflag;

    private String channeltip;

    private String cooperatesitecode;

    private Double applyserialno;

    private String cominsureind;

    private String teammanager;

    private String pioneercode;

    private String compolicyinfo;

    private String prefeeflag;

    private String poaserialno;

    private String surveyind;

    private String priind;

    private Date printdate;

    private String outersubcompanycode;

    private String departmentcode;

    private String basketind;

    private String flowid;

    private String intersalesmanregisterno;

    private String intersalesmancode;

    private String salesmanregisterno;

    private String imageattachind;

    private String productclass;

    private String moneysuspiciousind;

    private String isautouw;

    private String issendsms;

    private String issendemail;

    private String outerdeptcompanycode;

    private String integrateind;

    private String cooperatetype;

    private String cooperatechannel;

    private String sellername;

    private String sellerregisterno;

    private String accommodationflag;

    private String isadjustvalue;

    private String accommodationadjustvalue;

    private Double adjustdiscount;

    private String specialacceptancetype;

    private String insuredaddress;

    private String remark;

    private String inwardreference;

    private String contactremark;
    
    private Integer waitdays;
    
    //
    private String strSumInsured = "";
    
    private String strSumUWPremium = "";
    
    private String salesteamcode;
    
    private String policystyle;

    private String salesteamname;
    
    private String isdifferentplace;
    
    private String salesmanname = "";
    
    private String presentInd;//赠险标志1:赠险；空：非赠险
    
    private String prepayfeeflag;
    
    private Double fsh;
    private Double xsf;
	private Double tis;
	private String shareHolderFlag;
	private String isFarming;
	private String xsfInd;
	private Double adjustRate;
	
	private String itemacciind;//存QDPT
	private Double sumuwpremiumcny;

	public String getXsfInd() {
		return xsfInd;
	}

	public void setXsfInd(String xsfInd) {
		this.xsfInd = xsfInd;
	}

	public Double getAdjustRate() {
		return adjustRate;
	}

	public void setAdjustRate(Double adjustRate) {
		this.adjustRate = adjustRate;
	}

	public String getShareHolderFlag() {
		return shareHolderFlag;
	}

	public void setShareHolderFlag(String shareHolderFlag) {
		this.shareHolderFlag = shareHolderFlag;
	}

	public String getIsFarming() {
		return isFarming;
	}

	public void setIsFarming(String isFarming) {
		this.isFarming = isFarming;
	}

	public Double getTis() {
		return tis;
	}

	public void setTis(Double tis) {
		this.tis = tis;
	}
	public Double getXsf() {
		return xsf;
	}

	public void setXsf(Double xsf) {
		this.xsf = xsf;
	}

	public Double getFsh() {
		return fsh;
	}

	public void setFsh(Double fsh) {
		this.fsh = fsh;
	}

	public String getPrepayfeeflag() {
		return prepayfeeflag;
	}

	public void setPrepayfeeflag(String prepayfeeflag) {
		this.prepayfeeflag = prepayfeeflag;
	}

	public String getSalesmanname() {
		return salesmanname;
	}

	public void setSalesmanname(String salesmanname) {
		this.salesmanname = salesmanname;
	}

	public String getIsdifferentplace() {
		return isdifferentplace;
	}

	public void setIsdifferentplace(String isdifferentplace) {
		this.isdifferentplace = isdifferentplace;
	}

	public String getSalesteamname() {
		return salesteamname;
	}

	public void setSalesteamname(String salesteamname) {
		this.salesteamname = salesteamname;
	}

	public String getPolicystyle() {
		return policystyle;
	}

	public void setPolicystyle(String policystyle) {
		this.policystyle = policystyle;
	}

	public String getSalesteamcode() {
		return salesteamcode;
	}

	public void setSalesteamcode(String salesteamcode) {
		this.salesteamcode = salesteamcode;
	}

	public String getStrSumInsured() {
		return strSumInsured;
	}

	public void setStrSumInsured(String strSumInsured) {
		this.strSumInsured = strSumInsured;
	}

	public String getStrSumUWPremium() {
		return strSumUWPremium;
	}

	public void setStrSumUWPremium(String strSumUWPremium) {
		this.strSumUWPremium = strSumUWPremium;
	}

	public String getPolicyno() {
        return policyno;
    }

    public void setPolicyno(String policyno) {
        this.policyno = policyno == null ? null : policyno.trim();
    }

    public String getProposalno() {
        return proposalno;
    }

    public void setProposalno(String proposalno) {
        this.proposalno = proposalno == null ? null : proposalno.trim();
    }

    public String getPrintno() {
        return printno;
    }

    public void setPrintno(String printno) {
        this.printno = printno == null ? null : printno.trim();
    }

    public String getBusinesschannel() {
        return businesschannel;
    }

    public void setBusinesschannel(String businesschannel) {
        this.businesschannel = businesschannel == null ? null : businesschannel.trim();
    }

    public String getBusinesssource() {
        return businesssource;
    }

    public void setBusinesssource(String businesssource) {
        this.businesssource = businesssource == null ? null : businesssource.trim();
    }

    public String getBusinesstype() {
        return businesstype;
    }

    public void setBusinesstype(String businesstype) {
        this.businesstype = businesstype == null ? null : businesstype.trim();
    }

    public String getPolicysort() {
        return policysort;
    }

    public void setPolicysort(String policysort) {
        this.policysort = policysort == null ? null : policysort.trim();
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language == null ? null : language.trim();
    }

    public String getGroupind() {
        return groupind;
    }

    public void setGroupind(String groupind) {
        this.groupind = groupind == null ? null : groupind.trim();
    }

    public String getIntermediarycode() {
        return intermediarycode;
    }

    public void setIntermediarycode(String intermediarycode) {
        this.intermediarycode = intermediarycode == null ? null : intermediarycode.trim();
    }

    public String getAgreementno() {
        return agreementno;
    }

    public void setAgreementno(String agreementno) {
        this.agreementno = agreementno == null ? null : agreementno.trim();
    }

    public String getSolutioncode() {
        return solutioncode;
    }

    public void setSolutioncode(String solutioncode) {
        this.solutioncode = solutioncode == null ? null : solutioncode.trim();
    }

    public long getCreditperiod() {
        return creditperiod;
    }

    public void setCreditperiod(long creditperiod) {
        this.creditperiod = creditperiod;
    }

    public String getIndicationno() {
        return indicationno;
    }

    public void setIndicationno(String indicationno) {
        this.indicationno = indicationno == null ? null : indicationno.trim();
    }

    public Double getIndicationseqno() {
        return indicationseqno;
    }

    public void setIndicationseqno(Double indicationseqno) {
        this.indicationseqno = indicationseqno;
    }

    public String getQuotationno() {
        return quotationno;
    }

    public void setQuotationno(String quotationno) {
        this.quotationno = quotationno == null ? null : quotationno.trim();
    }

    public Double getQuotationseqno() {
        return quotationseqno;
    }

    public void setQuotationseqno(Double quotationseqno) {
        this.quotationseqno = quotationseqno;
    }

    public String getHoldcoverno() {
        return holdcoverno;
    }

    public void setHoldcoverno(String holdcoverno) {
        this.holdcoverno = holdcoverno == null ? null : holdcoverno.trim();
    }

    public Double getHoldcoverseqno() {
        return holdcoverseqno;
    }

    public void setHoldcoverseqno(Double holdcoverseqno) {
        this.holdcoverseqno = holdcoverseqno;
    }

    public String getCovernoteno() {
        return covernoteno;
    }

    public void setCovernoteno(String covernoteno) {
        this.covernoteno = covernoteno == null ? null : covernoteno.trim();
    }

    public Double getCovernoteseqno() {
        return covernoteseqno;
    }

    public void setCovernoteseqno(Double covernoteseqno) {
        this.covernoteseqno = covernoteseqno;
    }

    public String getAgentreferenceno() {
        return agentreferenceno;
    }

    public void setAgentreferenceno(String agentreferenceno) {
        this.agentreferenceno = agentreferenceno == null ? null : agentreferenceno.trim();
    }

    public String getRenewalno() {
        return renewalno;
    }

    public void setRenewalno(String renewalno) {
        this.renewalno = renewalno == null ? null : renewalno.trim();
    }

    public String getReplacedpolicyno() {
        return replacedpolicyno;
    }

    public void setReplacedpolicyno(String replacedpolicyno) {
        this.replacedpolicyno = replacedpolicyno == null ? null : replacedpolicyno.trim();
    }

    public String getPromotioncode() {
        return promotioncode;
    }

    public void setPromotioncode(String promotioncode) {
        this.promotioncode = promotioncode == null ? null : promotioncode.trim();
    }

    public String getApplicode() {
        return applicode;
    }

    public void setApplicode(String applicode) {
        this.applicode = applicode == null ? null : applicode.trim();
    }

    public String getAppliname() {
        return appliname;
    }

    public void setAppliname(String appliname) {
        this.appliname = appliname == null ? null : appliname.trim();
    }

    public String getAppliaddress() {
        return appliaddress;
    }

    public void setAppliaddress(String appliaddress) {
        this.appliaddress = appliaddress == null ? null : appliaddress.trim();
    }

    public String getAppliaddrtype() {
        return appliaddrtype;
    }

    public void setAppliaddrtype(String appliaddrtype) {
        this.appliaddrtype = appliaddrtype == null ? null : appliaddrtype.trim();
    }

    public String getInsuredcode() {
        return insuredcode;
    }

    public void setInsuredcode(String insuredcode) {
        this.insuredcode = insuredcode == null ? null : insuredcode.trim();
    }

    public String getInsuredname() {
        return insuredname;
    }

    public void setInsuredname(String insuredname) {
        this.insuredname = insuredname == null ? null : insuredname.trim();
    }

    public String getInsuredaddrtype() {
        return insuredaddrtype;
    }

    public void setInsuredaddrtype(String insuredaddrtype) {
        this.insuredaddrtype = insuredaddrtype == null ? null : insuredaddrtype.trim();
    }

    public Date getOperatedate() {
        return operatedate;
    }

    public void setOperatedate(Date operatedate) {
        this.operatedate = operatedate;
    }

    public Date getReceiveddate() {
        return receiveddate;
    }

    public void setReceiveddate(Date receiveddate) {
        this.receiveddate = receiveddate;
    }

    public Date getIssuedate() {
        return issuedate;
    }

    public void setIssuedate(Date issuedate) {
        this.issuedate = issuedate;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public Double getSuminsured() {
        return suminsured;
    }

    public void setSuminsured(Double suminsured) {
        this.suminsured = suminsured;
    }

    public Double getSumgrosspremium() {
        return sumgrosspremium;
    }

    public void setSumgrosspremium(Double sumgrosspremium) {
        this.sumgrosspremium = sumgrosspremium;
    }

    public Double getSumnetpremium() {
        return sumnetpremium;
    }

    public void setSumnetpremium(Double sumnetpremium) {
        this.sumnetpremium = sumnetpremium;
    }

    public String getArguesolution() {
        return arguesolution;
    }

    public void setArguesolution(String arguesolution) {
        this.arguesolution = arguesolution == null ? null : arguesolution.trim();
    }

    public String getMainport() {
        return mainport;
    }

    public void setMainport(String mainport) {
        this.mainport = mainport == null ? null : mainport.trim();
    }

    public String getSubport() {
        return subport;
    }

    public void setSubport(String subport) {
        this.subport = subport == null ? null : subport.trim();
    }

    public String getOthpolicyno() {
        return othpolicyno;
    }

    public void setOthpolicyno(String othpolicyno) {
        this.othpolicyno = othpolicyno == null ? null : othpolicyno.trim();
    }

    public String getTheircompanycode() {
        return theircompanycode;
    }

    public void setTheircompanycode(String theircompanycode) {
        this.theircompanycode = theircompanycode == null ? null : theircompanycode.trim();
    }

    public String getTheircompanyname() {
        return theircompanyname;
    }

    public void setTheircompanyname(String theircompanyname) {
        this.theircompanyname = theircompanyname == null ? null : theircompanyname.trim();
    }

    public String getTheirreference() {
        return theirreference;
    }

    public void setTheirreference(String theirreference) {
        this.theirreference = theirreference == null ? null : theirreference.trim();
    }

    public String getArbitoryname() {
        return arbitoryname;
    }

    public void setArbitoryname(String arbitoryname) {
        this.arbitoryname = arbitoryname == null ? null : arbitoryname.trim();
    }

    public Long getInstallmentno() {
        return installmentno;
    }

    public void setInstallmentno(Long installmentno) {
        this.installmentno = installmentno;
    }

    public Long getEndorsetimes() {
        return endorsetimes;
    }

    public void setEndorsetimes(Long endorsetimes) {
        this.endorsetimes = endorsetimes;
    }

    public Long getRegisttimes() {
        return registtimes;
    }

    public void setRegisttimes(Long registtimes) {
        this.registtimes = registtimes;
    }

    public Long getClaimstimes() {
        return claimstimes;
    }

    public void setClaimstimes(Long claimstimes) {
        this.claimstimes = claimstimes;
    }

    public Long getPrinttimes() {
        return printtimes;
    }

    public void setPrinttimes(Long printtimes) {
        this.printtimes = printtimes;
    }

    public String getIssuecompany() {
        return issuecompany;
    }

    public void setIssuecompany(String issuecompany) {
        this.issuecompany = issuecompany == null ? null : issuecompany.trim();
    }

    public String getIssueplace() {
        return issueplace;
    }

    public void setIssueplace(String issueplace) {
        this.issueplace = issueplace == null ? null : issueplace.trim();
    }

    public String getCompanycode() {
        return companycode;
    }

    public void setCompanycode(String companycode) {
        this.companycode = companycode == null ? null : companycode.trim();
    }

    public String getSalesmancode() {
        return salesmancode;
    }

    public void setSalesmancode(String salesmancode) {
        this.salesmancode = salesmancode == null ? null : salesmancode.trim();
    }

    public String getApprovercode() {
        return approvercode;
    }

    public void setApprovercode(String approvercode) {
        this.approvercode = approvercode == null ? null : approvercode.trim();
    }

    public String getOperatorcode() {
        return operatorcode;
    }

    public void setOperatorcode(String operatorcode) {
        this.operatorcode = operatorcode == null ? null : operatorcode.trim();
    }

    public Date getInputdate() {
        return inputdate;
    }

    public void setInputdate(Date inputdate) {
        this.inputdate = inputdate;
    }

    public String getUwyear() {
        return uwyear;
    }

    public void setUwyear(String uwyear) {
        this.uwyear = uwyear == null ? null : uwyear.trim();
    }

    public String getUnderwritecode() {
        return underwritecode;
    }

    public void setUnderwritecode(String underwritecode) {
        this.underwritecode = underwritecode == null ? null : underwritecode.trim();
    }

    public String getUnderwritename() {
        return underwritename;
    }

    public void setUnderwritename(String underwritename) {
        this.underwritename = underwritename == null ? null : underwritename.trim();
    }

    public Date getUnderwriteenddate() {
        return underwriteenddate;
    }

    public void setUnderwriteenddate(Date underwriteenddate) {
        this.underwriteenddate = underwriteenddate;
    }

    public String getUnderwriteind() {
        return underwriteind;
    }

    public void setUnderwriteind(String underwriteind) {
        this.underwriteind = underwriteind == null ? null : underwriteind.trim();
    }

    public String getAutorenewind() {
        return autorenewind;
    }

    public void setAutorenewind(String autorenewind) {
        this.autorenewind = autorenewind == null ? null : autorenewind.trim();
    }

    public String getMultiriskind() {
        return multiriskind;
    }

    public void setMultiriskind(String multiriskind) {
        this.multiriskind = multiriskind == null ? null : multiriskind.trim();
    }

    public String getLastmodifiercode() {
        return lastmodifiercode;
    }

    public void setLastmodifiercode(String lastmodifiercode) {
        this.lastmodifiercode = lastmodifiercode == null ? null : lastmodifiercode.trim();
    }

    public Date getLastmodifydate() {
        return lastmodifydate;
    }

    public void setLastmodifydate(Date lastmodifydate) {
        this.lastmodifydate = lastmodifydate;
    }

    public Double getFrontingripercent() {
        return frontingripercent;
    }

    public void setFrontingripercent(Double frontingripercent) {
        this.frontingripercent = frontingripercent;
    }

    public Double getFrontingricommissionpercent() {
        return frontingricommissionpercent;
    }

    public void setFrontingricommissionpercent(Double frontingricommissionpercent) {
        this.frontingricommissionpercent = frontingricommissionpercent;
    }

    public Double getFrontingriadminfeepercent() {
        return frontingriadminfeepercent;
    }

    public void setFrontingriadminfeepercent(Double frontingriadminfeepercent) {
        this.frontingriadminfeepercent = frontingriadminfeepercent;
    }

    public Double getFrontingritaxpercent() {
        return frontingritaxpercent;
    }

    public void setFrontingritaxpercent(Double frontingritaxpercent) {
        this.frontingritaxpercent = frontingritaxpercent;
    }

    public String getRenewind() {
        return renewind;
    }

    public void setRenewind(String renewind) {
        this.renewind = renewind == null ? null : renewind.trim();
    }

    public String getRenewedind() {
        return renewedind;
    }

    public void setRenewedind(String renewedind) {
        this.renewedind = renewedind == null ? null : renewedind.trim();
    }

    public String getSurrenderind() {
        return surrenderind;
    }

    public void setSurrenderind(String surrenderind) {
        this.surrenderind = surrenderind == null ? null : surrenderind.trim();
    }

    public String getEndind() {
        return endind;
    }

    public void setEndind(String endind) {
        this.endind = endind == null ? null : endind.trim();
    }

    public Double getYearpremium() {
        return yearpremium;
    }

    public void setYearpremium(Double yearpremium) {
        this.yearpremium = yearpremium;
    }

    public String getCancelind() {
        return cancelind;
    }

    public void setCancelind(String cancelind) {
        this.cancelind = cancelind == null ? null : cancelind.trim();
    }

    public String getLostind() {
        return lostind;
    }

    public void setLostind(String lostind) {
        this.lostind = lostind == null ? null : lostind.trim();
    }

    public String getDebtorind() {
        return debtorind;
    }

    public void setDebtorind(String debtorind) {
        this.debtorind = debtorind == null ? null : debtorind.trim();
    }

    public String getStatind() {
        return statind;
    }

    public void setStatind(String statind) {
        this.statind = statind == null ? null : statind.trim();
    }

    public String getSourceid() {
        return sourceid;
    }

    public void setSourceid(String sourceid) {
        this.sourceid = sourceid == null ? null : sourceid.trim();
    }

    public String getMigratedclientno() {
        return migratedclientno;
    }

    public void setMigratedclientno(String migratedclientno) {
        this.migratedclientno = migratedclientno == null ? null : migratedclientno.trim();
    }

    public String getMigratedaccountnum() {
        return migratedaccountnum;
    }

    public void setMigratedaccountnum(String migratedaccountnum) {
        this.migratedaccountnum = migratedaccountnum == null ? null : migratedaccountnum.trim();
    }

    public String getMigratedpolicyno() {
        return migratedpolicyno;
    }

    public void setMigratedpolicyno(String migratedpolicyno) {
        this.migratedpolicyno = migratedpolicyno == null ? null : migratedpolicyno.trim();
    }

    public String getValidind() {
        return validind;
    }

    public void setValidind(String validind) {
        this.validind = validind == null ? null : validind.trim();
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag == null ? null : flag.trim();
    }

    public Double getSumuwpremium() {
        return sumuwpremium;
    }

    public void setSumuwpremium(Double sumuwpremium) {
        this.sumuwpremium = sumuwpremium;
    }

    public String getVisatype() {
        return visatype;
    }

    public void setVisatype(String visatype) {
        this.visatype = visatype == null ? null : visatype.trim();
    }

    public String getImageind() {
        return imageind;
    }

    public void setImageind(String imageind) {
        this.imageind = imageind == null ? null : imageind.trim();
    }

    public String getProposalpolicyind() {
        return proposalpolicyind;
    }

    public void setProposalpolicyind(String proposalpolicyind) {
        this.proposalpolicyind = proposalpolicyind == null ? null : proposalpolicyind.trim();
    }

    public String getHengsangind() {
        return hengsangind;
    }

    public void setHengsangind(String hengsangind) {
        this.hengsangind = hengsangind == null ? null : hengsangind.trim();
    }

    public String getDomesticind() {
        return domesticind;
    }

    public void setDomesticind(String domesticind) {
        this.domesticind = domesticind == null ? null : domesticind.trim();
    }

    public String getHistoryind() {
        return historyind;
    }

    public void setHistoryind(String historyind) {
        this.historyind = historyind == null ? null : historyind.trim();
    }

    public String getReplaceproposerind() {
        return replaceproposerind;
    }

    public void setReplaceproposerind(String replaceproposerind) {
        this.replaceproposerind = replaceproposerind == null ? null : replaceproposerind.trim();
    }

    public String getHandlestatus() {
        return handlestatus;
    }

    public void setHandlestatus(String handlestatus) {
        this.handlestatus = handlestatus == null ? null : handlestatus.trim();
    }

    public Date getApproveenddate() {
        return approveenddate;
    }

    public void setApproveenddate(Date approveenddate) {
        this.approveenddate = approveenddate;
    }

    public String getDealercode() {
        return dealercode;
    }

    public void setDealercode(String dealercode) {
        this.dealercode = dealercode == null ? null : dealercode.trim();
    }

    public String getCodind() {
        return codind;
    }

    public void setCodind(String codind) {
        this.codind = codind == null ? null : codind.trim();
    }

    public Date getAcceptdate() {
        return acceptdate;
    }

    public void setAcceptdate(Date acceptdate) {
        this.acceptdate = acceptdate;
    }

    public String getSpecialacceptance() {
        return specialacceptance;
    }

    public void setSpecialacceptance(String specialacceptance) {
        this.specialacceptance = specialacceptance == null ? null : specialacceptance.trim();
    }

    public String getRelatedriskcode() {
        return relatedriskcode;
    }

    public void setRelatedriskcode(String relatedriskcode) {
        this.relatedriskcode = relatedriskcode == null ? null : relatedriskcode.trim();
    }

    public String getAgrvalidind() {
        return agrvalidind;
    }

    public void setAgrvalidind(String agrvalidind) {
        this.agrvalidind = agrvalidind == null ? null : agrvalidind.trim();
    }

    public String getOuterusercode() {
        return outerusercode;
    }

    public void setOuterusercode(String outerusercode) {
        this.outerusercode = outerusercode == null ? null : outerusercode.trim();
    }

    public String getOutercompanycode() {
        return outercompanycode;
    }

    public void setOutercompanycode(String outercompanycode) {
        this.outercompanycode = outercompanycode == null ? null : outercompanycode.trim();
    }

    public String getServiceprojectcode() {
        return serviceprojectcode;
    }

    public void setServiceprojectcode(String serviceprojectcode) {
        this.serviceprojectcode = serviceprojectcode == null ? null : serviceprojectcode.trim();
    }

    public String getChanneldetailcode() {
        return channeldetailcode;
    }

    public void setChanneldetailcode(String channeldetailcode) {
        this.channeldetailcode = channeldetailcode == null ? null : channeldetailcode.trim();
    }

    public String getProductcode() {
        return productcode;
    }

    public void setProductcode(String productcode) {
        this.productcode = productcode == null ? null : productcode.trim();
    }

    public String getProblemind() {
        return problemind;
    }

    public void setProblemind(String problemind) {
        this.problemind = problemind == null ? null : problemind.trim();
    }

    public Date getCanceltime() {
        return canceltime;
    }

    public void setCanceltime(Date canceltime) {
        this.canceltime = canceltime;
    }

    public String getChannelflag() {
        return channelflag;
    }

    public void setChannelflag(String channelflag) {
        this.channelflag = channelflag == null ? null : channelflag.trim();
    }

    public String getAgricultureflag() {
        return agricultureflag;
    }

    public void setAgricultureflag(String agricultureflag) {
        this.agricultureflag = agricultureflag == null ? null : agricultureflag.trim();
    }

    public String getHistoryflag() {
        return historyflag;
    }

    public void setHistoryflag(String historyflag) {
        this.historyflag = historyflag == null ? null : historyflag.trim();
    }

    public String getCalculatetype() {
        return calculatetype;
    }

    public void setCalculatetype(String calculatetype) {
        this.calculatetype = calculatetype == null ? null : calculatetype.trim();
    }

    public String getBusinessind() {
        return businessind;
    }

    public void setBusinessind(String businessind) {
        this.businessind = businessind == null ? null : businessind.trim();
    }

    public String getSalescommissionercode() {
        return salescommissionercode;
    }

    public void setSalescommissionercode(String salescommissionercode) {
        this.salescommissionercode = salescommissionercode == null ? null : salescommissionercode.trim();
    }

    public String getCmbprintivcind() {
        return cmbprintivcind;
    }

    public void setCmbprintivcind(String cmbprintivcind) {
        this.cmbprintivcind = cmbprintivcind == null ? null : cmbprintivcind.trim();
    }

    public String getBusinessmode() {
        return businessmode;
    }

    public void setBusinessmode(String businessmode) {
        this.businessmode = businessmode == null ? null : businessmode.trim();
    }

    public String getApplyno() {
        return applyno;
    }

    public void setApplyno(String applyno) {
        this.applyno = applyno == null ? null : applyno.trim();
    }

    public String getOldcarprintflag() {
        return oldcarprintflag;
    }

    public void setOldcarprintflag(String oldcarprintflag) {
        this.oldcarprintflag = oldcarprintflag == null ? null : oldcarprintflag.trim();
    }

    public String getChanneltip() {
        return channeltip;
    }

    public void setChanneltip(String channeltip) {
        this.channeltip = channeltip == null ? null : channeltip.trim();
    }

    public String getCooperatesitecode() {
        return cooperatesitecode;
    }

    public void setCooperatesitecode(String cooperatesitecode) {
        this.cooperatesitecode = cooperatesitecode == null ? null : cooperatesitecode.trim();
    }

    public Double getApplyserialno() {
        return applyserialno;
    }

    public void setApplyserialno(Double applyserialno) {
        this.applyserialno = applyserialno;
    }

    public String getCominsureind() {
        return cominsureind;
    }

    public void setCominsureind(String cominsureind) {
        this.cominsureind = cominsureind == null ? null : cominsureind.trim();
    }

    public String getTeammanager() {
        return teammanager;
    }

    public void setTeammanager(String teammanager) {
        this.teammanager = teammanager == null ? null : teammanager.trim();
    }

    public String getPioneercode() {
        return pioneercode;
    }

    public void setPioneercode(String pioneercode) {
        this.pioneercode = pioneercode == null ? null : pioneercode.trim();
    }

    public String getCompolicyinfo() {
        return compolicyinfo;
    }

    public void setCompolicyinfo(String compolicyinfo) {
        this.compolicyinfo = compolicyinfo == null ? null : compolicyinfo.trim();
    }

    public String getPrefeeflag() {
        return prefeeflag;
    }

    public void setPrefeeflag(String prefeeflag) {
        this.prefeeflag = prefeeflag == null ? null : prefeeflag.trim();
    }

    public String getPoaserialno() {
        return poaserialno;
    }

    public void setPoaserialno(String poaserialno) {
        this.poaserialno = poaserialno == null ? null : poaserialno.trim();
    }

    public String getSurveyind() {
        return surveyind;
    }

    public void setSurveyind(String surveyind) {
        this.surveyind = surveyind == null ? null : surveyind.trim();
    }

    public String getPriind() {
        return priind;
    }

    public void setPriind(String priind) {
        this.priind = priind == null ? null : priind.trim();
    }

    public Date getPrintdate() {
        return printdate;
    }

    public void setPrintdate(Date printdate) {
        this.printdate = printdate;
    }

    public String getOutersubcompanycode() {
        return outersubcompanycode;
    }

    public void setOutersubcompanycode(String outersubcompanycode) {
        this.outersubcompanycode = outersubcompanycode == null ? null : outersubcompanycode.trim();
    }

    public String getDepartmentcode() {
        return departmentcode;
    }

    public void setDepartmentcode(String departmentcode) {
        this.departmentcode = departmentcode == null ? null : departmentcode.trim();
    }

    public String getBasketind() {
        return basketind;
    }

    public void setBasketind(String basketind) {
        this.basketind = basketind == null ? null : basketind.trim();
    }

    public String getFlowid() {
        return flowid;
    }

    public void setFlowid(String flowid) {
        this.flowid = flowid == null ? null : flowid.trim();
    }

    public String getIntersalesmanregisterno() {
        return intersalesmanregisterno;
    }

    public void setIntersalesmanregisterno(String intersalesmanregisterno) {
        this.intersalesmanregisterno = intersalesmanregisterno == null ? null : intersalesmanregisterno.trim();
    }

    public String getIntersalesmancode() {
        return intersalesmancode;
    }

    public void setIntersalesmancode(String intersalesmancode) {
        this.intersalesmancode = intersalesmancode == null ? null : intersalesmancode.trim();
    }

    public String getSalesmanregisterno() {
        return salesmanregisterno;
    }

    public void setSalesmanregisterno(String salesmanregisterno) {
        this.salesmanregisterno = salesmanregisterno == null ? null : salesmanregisterno.trim();
    }

    public String getImageattachind() {
        return imageattachind;
    }

    public void setImageattachind(String imageattachind) {
        this.imageattachind = imageattachind == null ? null : imageattachind.trim();
    }

    public String getProductclass() {
        return productclass;
    }

    public void setProductclass(String productclass) {
        this.productclass = productclass == null ? null : productclass.trim();
    }

    public String getMoneysuspiciousind() {
        return moneysuspiciousind;
    }

    public void setMoneysuspiciousind(String moneysuspiciousind) {
        this.moneysuspiciousind = moneysuspiciousind == null ? null : moneysuspiciousind.trim();
    }

    public String getIsautouw() {
        return isautouw;
    }

    public void setIsautouw(String isautouw) {
        this.isautouw = isautouw == null ? null : isautouw.trim();
    }

    public String getIssendsms() {
        return issendsms;
    }

    public void setIssendsms(String issendsms) {
        this.issendsms = issendsms == null ? null : issendsms.trim();
    }

    public String getIssendemail() {
        return issendemail;
    }

    public void setIssendemail(String issendemail) {
        this.issendemail = issendemail == null ? null : issendemail.trim();
    }

    public String getOuterdeptcompanycode() {
        return outerdeptcompanycode;
    }

    public void setOuterdeptcompanycode(String outerdeptcompanycode) {
        this.outerdeptcompanycode = outerdeptcompanycode == null ? null : outerdeptcompanycode.trim();
    }

    public String getIntegrateind() {
        return integrateind;
    }

    public void setIntegrateind(String integrateind) {
        this.integrateind = integrateind == null ? null : integrateind.trim();
    }

    public String getCooperatetype() {
        return cooperatetype;
    }

    public void setCooperatetype(String cooperatetype) {
        this.cooperatetype = cooperatetype == null ? null : cooperatetype.trim();
    }

    public String getCooperatechannel() {
        return cooperatechannel;
    }

    public void setCooperatechannel(String cooperatechannel) {
        this.cooperatechannel = cooperatechannel == null ? null : cooperatechannel.trim();
    }

    public String getSellername() {
        return sellername;
    }

    public void setSellername(String sellername) {
        this.sellername = sellername == null ? null : sellername.trim();
    }

    public String getSellerregisterno() {
        return sellerregisterno;
    }

    public void setSellerregisterno(String sellerregisterno) {
        this.sellerregisterno = sellerregisterno == null ? null : sellerregisterno.trim();
    }

    public String getAccommodationflag() {
        return accommodationflag;
    }

    public void setAccommodationflag(String accommodationflag) {
        this.accommodationflag = accommodationflag == null ? null : accommodationflag.trim();
    }

    public String getIsadjustvalue() {
        return isadjustvalue;
    }

    public void setIsadjustvalue(String isadjustvalue) {
        this.isadjustvalue = isadjustvalue == null ? null : isadjustvalue.trim();
    }

    public String getAccommodationadjustvalue() {
        return accommodationadjustvalue;
    }

    public void setAccommodationadjustvalue(String accommodationadjustvalue) {
        this.accommodationadjustvalue = accommodationadjustvalue == null ? null : accommodationadjustvalue.trim();
    }

    public Double getAdjustdiscount() {
        return adjustdiscount;
    }

    public void setAdjustdiscount(Double adjustdiscount) {
        this.adjustdiscount = adjustdiscount;
    }

    public String getSpecialacceptancetype() {
        return specialacceptancetype;
    }

    public void setSpecialacceptancetype(String specialacceptancetype) {
        this.specialacceptancetype = specialacceptancetype == null ? null : specialacceptancetype.trim();
    }

    public String getInsuredaddress() {
        return insuredaddress;
    }

    public void setInsuredaddress(String insuredaddress) {
        this.insuredaddress = insuredaddress == null ? null : insuredaddress.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getInwardreference() {
        return inwardreference;
    }

    public void setInwardreference(String inwardreference) {
        this.inwardreference = inwardreference == null ? null : inwardreference.trim();
    }

    public String getContactremark() {
        return contactremark;
    }

    public void setContactremark(String contactremark) {
        this.contactremark = contactremark == null ? null : contactremark.trim();
    }
    
    public Integer getWaitdays() {
        return waitdays;
    }

    public void setWaitdays(Integer waitdays) {
        this.waitdays = waitdays;
    }

	public String getPresentInd() {
		return presentInd;
	}

	public void setPresentInd(String presentInd) {
		this.presentInd = presentInd;
	}

	public String getItemacciind() {
		return itemacciind;
	}

	public void setItemacciind(String itemacciind) {
		this.itemacciind = itemacciind;
	}

	public Double getSumuwpremiumcny() {
		return sumuwpremiumcny;
	}

	public void setSumuwpremiumcny(Double sumuwpremiumcny) {
		this.sumuwpremiumcny = sumuwpremiumcny;
	}
}