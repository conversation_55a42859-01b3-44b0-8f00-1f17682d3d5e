package ins.channel.policymain.vo;

import java.util.Date;

public class GuPolicyRelatedPartyDto {
    private String policyno;

    private Double serialno;

    private String insuredtype;

    private String insuredcode;

    private String insuredname;

    private String insuredaddress;

    private String insuredbusinesssource;

    private String insuredflag;

    private String insuredrole;

    private String identifytype;

    private String identifynumber;

    private String insuredrelation;

    private Double relateserialno;

    private String insuredind;

    private String remark;

    private String flag;

    private String sex;

    private Date birthdate;

    private String postcode;

    private String officephone;

    private String mobilephone;

    private String homephone;

    private String contactname;

    private String contactphone;

    private String marriagestatus;

    private String educationbackground;

    private String email;

    private String relationwithholder;

    private String contactsex;

    private String contacttype;

    private String contactdepartment;

    private String contactposition;

    private String contactofficenumber;

    private String contactmobile;

    private String surname;

    private String moniker;

    private String firstname;

    private String lastname;

    private String itemprovincecode;

    private String itemcitycode;

    private String itemprovincecname;

    private String itemcitycname;

    private String moneylaunderingind;

    private String starlevel;

    private String vipind;

    private String countycode;

    private String socialsecurityno;

    private String companynature;

    private Double numberofunits;

    private Date updatesysdate;

    private String bankname;

    private String banknumber;
    /**
     * 渠道电子保单需要驼峰格式字段
     */
    private String InsuredBusinessAddress;
    
    private String insuredName;
    
    private String identifyType;
    
    private String identifyNumber;
    
    private String endorSeqNo;
        
    private String contactName;
    
    private String contactMobile;
    
    private String moneyLaunderingInd;
    
    private String vipInd;
    
    private String copyEmailSend;
    
    private String policyNo;
    
    private String serialNo;
    
    private String insuredType;
    
    private String insuredCode;
    
    private String insuredAddress;
    
    private String insuredBusinessSource;
    
    private String insuredFlag;
    
    private String officePhone;
    
    private String marriageStatus;
    
    private String educationBackground;
    
    private String contactSex;
    
    private String contactType;
    
    private String mobilePhone;
    
    private String postCode;
    
    /**
     * 电子保单使用的区县名称
     */
    private String districtName;
    
    private String otherinsuredphone;
    
    private String industrymaincode;//行业大类
    
    private String industrykindcode;//行业小类
    
    public String getOtherinsuredphone() {
		return otherinsuredphone;
	}

	public void setOtherinsuredphone(String otherinsuredphone) {
		this.otherinsuredphone = otherinsuredphone;
	}

	public String getMobilePhone() {
		return mobilePhone;
	}

	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	public String getPolicyNo() {
		return policyNo;
	}

	public void setPolicyNo(String policyNo) {
		this.policyNo = policyNo;
	}

	public String getSerialNo() {
		return serialNo;
	}

	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}

	public String getInsuredType() {
		return insuredType;
	}

	public void setInsuredType(String insuredType) {
		this.insuredType = insuredType;
	}

	public String getInsuredCode() {
		return insuredCode;
	}

	public void setInsuredCode(String insuredCode) {
		this.insuredCode = insuredCode;
	}

	public String getInsuredAddress() {
		return insuredAddress;
	}

	public void setInsuredAddress(String insuredAddress) {
		this.insuredAddress = insuredAddress;
	}

	public String getInsuredBusinessSource() {
		return insuredBusinessSource;
	}

	public void setInsuredBusinessSource(String insuredBusinessSource) {
		this.insuredBusinessSource = insuredBusinessSource;
	}

	public String getInsuredFlag() {
		return insuredFlag;
	}

	public void setInsuredFlag(String insuredFlag) {
		this.insuredFlag = insuredFlag;
	}

	public String getOfficePhone() {
		return officePhone;
	}

	public void setOfficePhone(String officePhone) {
		this.officePhone = officePhone;
	}

	public String getMarriageStatus() {
		return marriageStatus;
	}

	public void setMarriageStatus(String marriageStatus) {
		this.marriageStatus = marriageStatus;
	}

	public String getEducationBackground() {
		return educationBackground;
	}

	public void setEducationBackground(String educationBackground) {
		this.educationBackground = educationBackground;
	}

	public String getContactSex() {
		return contactSex;
	}

	public void setContactSex(String contactSex) {
		this.contactSex = contactSex;
	}

	public String getContactType() {
		return contactType;
	}

	public void setContactType(String contactType) {
		this.contactType = contactType;
	}

	public String getCopyEmailSend() {
		return copyEmailSend;
	}

	public void setCopyEmailSend(String copyEmailSend) {
		this.copyEmailSend = copyEmailSend;
	}

	public String getEndorSeqNo() {
		return endorSeqNo;
	}

	public void setEndorSeqNo(String endorSeqNo) {
		this.endorSeqNo = endorSeqNo;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContactMobile() {
		return contactMobile;
	}

	public void setContactMobile(String contactMobile) {
		this.contactMobile = contactMobile;
	}

	public String getMoneyLaunderingInd() {
		return moneyLaunderingInd;
	}

	public void setMoneyLaunderingInd(String moneyLaunderingInd) {
		this.moneyLaunderingInd = moneyLaunderingInd;
	}

	public String getVipInd() {
		return vipInd;
	}

	public void setVipInd(String vipInd) {
		this.vipInd = vipInd;
	}

	public String getInsuredName() {
		return insuredName;
	}

	public void setInsuredName(String insuredName) {
		this.insuredName = insuredName;
	}

	public String getIdentifyType() {
		return identifyType;
	}

	public void setIdentifyType(String identifyType) {
		this.identifyType = identifyType;
	}

	public String getIdentifyNumber() {
		return identifyNumber;
	}

	public void setIdentifyNumber(String identifyNumber) {
		this.identifyNumber = identifyNumber;
	}

	public String getPolicyno() {
        return policyno;
    }

    public void setPolicyno(String policyno) {
        this.policyno = policyno == null ? null : policyno.trim();
    }

    public Double getSerialno() {
        return serialno;
    }

    public void setSerialno(Double serialno) {
        this.serialno = serialno;
    }

    public String getInsuredtype() {
        return insuredtype;
    }

    public void setInsuredtype(String insuredtype) {
        this.insuredtype = insuredtype == null ? null : insuredtype.trim();
    }

    public String getInsuredcode() {
        return insuredcode;
    }

    public void setInsuredcode(String insuredcode) {
        this.insuredcode = insuredcode == null ? null : insuredcode.trim();
    }

    public String getInsuredname() {
        return insuredname;
    }

    public void setInsuredname(String insuredname) {
        this.insuredname = insuredname == null ? null : insuredname.trim();
    }

    public String getInsuredaddress() {
        return insuredaddress;
    }

    public void setInsuredaddress(String insuredaddress) {
        this.insuredaddress = insuredaddress == null ? null : insuredaddress.trim();
    }

    public String getInsuredbusinesssource() {
        return insuredbusinesssource;
    }

    public void setInsuredbusinesssource(String insuredbusinesssource) {
        this.insuredbusinesssource = insuredbusinesssource == null ? null : insuredbusinesssource.trim();
    }

    public String getInsuredflag() {
        return insuredflag;
    }

    public void setInsuredflag(String insuredflag) {
        this.insuredflag = insuredflag == null ? null : insuredflag.trim();
    }

    public String getInsuredrole() {
        return insuredrole;
    }

    public void setInsuredrole(String insuredrole) {
        this.insuredrole = insuredrole == null ? null : insuredrole.trim();
    }

    public String getIdentifytype() {
        return identifytype;
    }

    public void setIdentifytype(String identifytype) {
        this.identifytype = identifytype == null ? null : identifytype.trim();
    }

    public String getIdentifynumber() {
        return identifynumber;
    }

    public void setIdentifynumber(String identifynumber) {
        this.identifynumber = identifynumber == null ? null : identifynumber.trim();
    }

    public String getInsuredrelation() {
        return insuredrelation;
    }

    public void setInsuredrelation(String insuredrelation) {
        this.insuredrelation = insuredrelation == null ? null : insuredrelation.trim();
    }

    public Double getRelateserialno() {
        return relateserialno;
    }

    public void setRelateserialno(Double relateserialno) {
        this.relateserialno = relateserialno;
    }

    public String getInsuredind() {
        return insuredind;
    }

    public void setInsuredind(String insuredind) {
        this.insuredind = insuredind == null ? null : insuredind.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag == null ? null : flag.trim();
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex == null ? null : sex.trim();
    }

    public Date getBirthdate() {
        return birthdate;
    }

    public void setBirthdate(Date birthdate) {
        this.birthdate = birthdate;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode == null ? null : postcode.trim();
    }

    public String getOfficephone() {
        return officephone;
    }

    public void setOfficephone(String officephone) {
        this.officephone = officephone == null ? null : officephone.trim();
    }

    public String getMobilephone() {
        return mobilephone;
    }

    public void setMobilephone(String mobilephone) {
        this.mobilephone = mobilephone == null ? null : mobilephone.trim();
    }

    public String getHomephone() {
        return homephone;
    }

    public void setHomephone(String homephone) {
        this.homephone = homephone == null ? null : homephone.trim();
    }

    public String getContactname() {
        return contactname;
    }

    public void setContactname(String contactname) {
        this.contactname = contactname == null ? null : contactname.trim();
    }

    public String getContactphone() {
        return contactphone;
    }

    public void setContactphone(String contactphone) {
        this.contactphone = contactphone == null ? null : contactphone.trim();
    }

    public String getMarriagestatus() {
        return marriagestatus;
    }

    public void setMarriagestatus(String marriagestatus) {
        this.marriagestatus = marriagestatus == null ? null : marriagestatus.trim();
    }

    public String getEducationbackground() {
        return educationbackground;
    }

    public void setEducationbackground(String educationbackground) {
        this.educationbackground = educationbackground == null ? null : educationbackground.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getRelationwithholder() {
        return relationwithholder;
    }

    public void setRelationwithholder(String relationwithholder) {
        this.relationwithholder = relationwithholder == null ? null : relationwithholder.trim();
    }

    public String getContactsex() {
        return contactsex;
    }

    public void setContactsex(String contactsex) {
        this.contactsex = contactsex == null ? null : contactsex.trim();
    }

    public String getContacttype() {
        return contacttype;
    }

    public void setContacttype(String contacttype) {
        this.contacttype = contacttype == null ? null : contacttype.trim();
    }

    public String getContactdepartment() {
        return contactdepartment;
    }

    public void setContactdepartment(String contactdepartment) {
        this.contactdepartment = contactdepartment == null ? null : contactdepartment.trim();
    }

    public String getContactposition() {
        return contactposition;
    }

    public void setContactposition(String contactposition) {
        this.contactposition = contactposition == null ? null : contactposition.trim();
    }

    public String getContactofficenumber() {
        return contactofficenumber;
    }

    public void setContactofficenumber(String contactofficenumber) {
        this.contactofficenumber = contactofficenumber == null ? null : contactofficenumber.trim();
    }

    public String getContactmobile() {
        return contactmobile;
    }

    public void setContactmobile(String contactmobile) {
        this.contactmobile = contactmobile == null ? null : contactmobile.trim();
    }

    public String getSurname() {
        return surname;
    }

    public void setSurname(String surname) {
        this.surname = surname == null ? null : surname.trim();
    }

    public String getMoniker() {
        return moniker;
    }

    public void setMoniker(String moniker) {
        this.moniker = moniker == null ? null : moniker.trim();
    }

    public String getFirstname() {
        return firstname;
    }

    public void setFirstname(String firstname) {
        this.firstname = firstname == null ? null : firstname.trim();
    }

    public String getLastname() {
        return lastname;
    }

    public void setLastname(String lastname) {
        this.lastname = lastname == null ? null : lastname.trim();
    }

    public String getItemprovincecode() {
        return itemprovincecode;
    }

    public void setItemprovincecode(String itemprovincecode) {
        this.itemprovincecode = itemprovincecode == null ? null : itemprovincecode.trim();
    }

    public String getItemcitycode() {
        return itemcitycode;
    }

    public void setItemcitycode(String itemcitycode) {
        this.itemcitycode = itemcitycode == null ? null : itemcitycode.trim();
    }

    public String getItemprovincecname() {
        return itemprovincecname;
    }

    public void setItemprovincecname(String itemprovincecname) {
        this.itemprovincecname = itemprovincecname == null ? null : itemprovincecname.trim();
    }

    public String getItemcitycname() {
        return itemcitycname;
    }

    public void setItemcitycname(String itemcitycname) {
        this.itemcitycname = itemcitycname == null ? null : itemcitycname.trim();
    }

    public String getMoneylaunderingind() {
        return moneylaunderingind;
    }

    public void setMoneylaunderingind(String moneylaunderingind) {
        this.moneylaunderingind = moneylaunderingind == null ? null : moneylaunderingind.trim();
    }

    public String getStarlevel() {
        return starlevel;
    }

    public void setStarlevel(String starlevel) {
        this.starlevel = starlevel == null ? null : starlevel.trim();
    }

    public String getVipind() {
        return vipind;
    }

    public void setVipind(String vipind) {
        this.vipind = vipind == null ? null : vipind.trim();
    }

    public String getCountycode() {
        return countycode;
    }

    public void setCountycode(String countycode) {
        this.countycode = countycode == null ? null : countycode.trim();
    }

    public String getSocialsecurityno() {
        return socialsecurityno;
    }

    public void setSocialsecurityno(String socialsecurityno) {
        this.socialsecurityno = socialsecurityno == null ? null : socialsecurityno.trim();
    }

    public String getCompanynature() {
        return companynature;
    }

    public void setCompanynature(String companynature) {
        this.companynature = companynature == null ? null : companynature.trim();
    }

    public Double getNumberofunits() {
        return numberofunits;
    }

    public void setNumberofunits(Double numberofunits) {
        this.numberofunits = numberofunits;
    }

    public Date getUpdatesysdate() {
        return updatesysdate;
    }

    public void setUpdatesysdate(Date updatesysdate) {
        this.updatesysdate = updatesysdate;
    }

    public String getBankname() {
        return bankname;
    }

    public void setBankname(String bankname) {
        this.bankname = bankname == null ? null : bankname.trim();
    }

    public String getBanknumber() {
        return banknumber;
    }

    public void setBanknumber(String banknumber) {
        this.banknumber = banknumber == null ? null : banknumber.trim();
    }

	public String getDistrictName() {
		return districtName;
	}

	public void setDistrictName(String districtName) {
		this.districtName = districtName;
	}

	public String getIndustrymaincode() {
		return industrymaincode;
	}

	public void setIndustrymaincode(String industrymaincode) {
		this.industrymaincode = industrymaincode;
	}

	public String getIndustrykindcode() {
		return industrykindcode;
	}

	public void setIndustrykindcode(String industrykindcode) {
		this.industrykindcode = industrykindcode;
	}

	public String getInsuredBusinessAddress() {
		return InsuredBusinessAddress;
	}

	public void setInsuredBusinessAddress(String insuredBusinessAddress) {
		InsuredBusinessAddress = insuredBusinessAddress;
	}

	public String getPostCode() {
		return postCode;
	}

	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}
    
}