package ins.channel.policymain.vo;

import java.util.Date;

public class GuPolicyRiskDto {
    private String policyno;

    private String plancode;

    private String riskcode;

    private Date startdate;

    private Date enddate;

    private String starttimezone;

    private String endtimezone;

    private Date commencedate;

    private Date retroactivestartdate;

    private Date retroactiveenddate;

    private String currency;

    private Double sumvalue;

    private Double suminsured;

    private Double sumgrosspremium;

    private Double commission;

    private Double discount;

    private Double sumnetpremium;

    private Double loading;

    private String judicalcode;

    private String judicalscope;

    private String uwyear;

    private String geographicalarea;

    private String declarationind;

    private String declarationinterval;

    private String lowestpremiumind;

    private String lowestpremcurrency;

    private Double lowestpremium;

    private String commissionrateind;

    private String validind;

    private String flag;

    private String adjustmentind;

    private Double sumuwpremium;

    private String geographicalareadesc;

    private Date discoverstartdate;

    private Date discoverenddate;

    private String compensationtype;

    private String subpolicyno;

    private String policyvisatype;

    private String policyvisaserialno;

    private String endorvisatype;

    private String endorvisaserialno;

    private Double uwyeardays;

    private String combineind;

    private Double backdays;

    private Date delayfinalenddate;

    private Date delayenddate;

    private Double bonusrate;

    private Double referbonusrate;

    private String geographicalareadetail;

    private Date delaystartdate;

    private Double delaytime;

    private String businessclass;

    private String coinsind;

    private String specialreinsind;

    private String updatecommissionind;

    private String surrenderind;

    private String declarationexplain;

    private String riskapplytype;

    private String productedition;

    private String endorcalculatemode;

    private String effectflag;

    private String outerremark;

    private String remark;
    
    private Double noTaxPremium;
    
    private String flightnumber;
    
    private String onewayorreturn;
    
    private Date fightdayreturn;
    
    private Date fightday;
    
    private String singleorregular;
    
    private String channelfundflag;//1为走资金,空不走资金
    
    private String proposalway;

    
    public String getSingleorregular() {
		return singleorregular;
	}

	public void setSingleorregular(String singleorregular) {
		this.singleorregular = singleorregular;
	}

	/**渠道非车更新字段--条款名称**/
    private String producteditionname;
    
    	
    public String getProducteditionname() {
		return producteditionname;
	}

	public void setProducteditionname(String producteditionname) {
		this.producteditionname = producteditionname;
	}

	public Date getFightday() {
		return fightday;
	}

	public void setFightday(Date fightday) {
		this.fightday = fightday;
	}

	public String getFlightnumber() {
		return flightnumber;
	}

	public void setFlightnumber(String flightnumber) {
		this.flightnumber = flightnumber;
	}

	public String getOnewayorreturn() {
		return onewayorreturn;
	}

	public void setOnewayorreturn(String onewayorreturn) {
		this.onewayorreturn = onewayorreturn;
	}

	public Date getFightdayreturn() {
		return fightdayreturn;
	}

	public void setFightdayreturn(Date fightdayreturn) {
		this.fightdayreturn = fightdayreturn;
	}
    
    
    public Double getNoTaxPremium() {
		return noTaxPremium;
	}

	public void setNoTaxPremium(Double noTaxPremium) {
		this.noTaxPremium = noTaxPremium;
	}

	//电子保单查询添加属性
    private Long insuredDays;
    
    //费率因子
    private Long calculator;

    public String getPolicyno() {
        return policyno;
    }

    public void setPolicyno(String policyno) {
        this.policyno = policyno == null ? null : policyno.trim();
    }

    public String getPlancode() {
        return plancode;
    }

    public void setPlancode(String plancode) {
        this.plancode = plancode == null ? null : plancode.trim();
    }

    public String getRiskcode() {
        return riskcode;
    }

    public void setRiskcode(String riskcode) {
        this.riskcode = riskcode == null ? null : riskcode.trim();
    }

    public Date getStartdate() {
        return startdate;
    }

    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }

    public Date getEnddate() {
        return enddate;
    }

    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }

    public String getStarttimezone() {
        return starttimezone;
    }

    public void setStarttimezone(String starttimezone) {
        this.starttimezone = starttimezone == null ? null : starttimezone.trim();
    }

    public String getEndtimezone() {
        return endtimezone;
    }

    public void setEndtimezone(String endtimezone) {
        this.endtimezone = endtimezone == null ? null : endtimezone.trim();
    }

    public Date getCommencedate() {
        return commencedate;
    }

    public void setCommencedate(Date commencedate) {
        this.commencedate = commencedate;
    }

    public Date getRetroactivestartdate() {
        return retroactivestartdate;
    }

    public void setRetroactivestartdate(Date retroactivestartdate) {
        this.retroactivestartdate = retroactivestartdate;
    }

    public Date getRetroactiveenddate() {
        return retroactiveenddate;
    }

    public void setRetroactiveenddate(Date retroactiveenddate) {
        this.retroactiveenddate = retroactiveenddate;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public Double getSumvalue() {
        return sumvalue;
    }

    public void setSumvalue(Double sumvalue) {
        this.sumvalue = sumvalue;
    }

    public Double getSuminsured() {
        return suminsured;
    }

    public void setSuminsured(Double suminsured) {
        this.suminsured = suminsured;
    }

    public Double getSumgrosspremium() {
        return sumgrosspremium;
    }

    public void setSumgrosspremium(Double sumgrosspremium) {
        this.sumgrosspremium = sumgrosspremium;
    }

    public Double getCommission() {
        return commission;
    }

    public void setCommission(Double commission) {
        this.commission = commission;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Double getSumnetpremium() {
        return sumnetpremium;
    }

    public void setSumnetpremium(Double sumnetpremium) {
        this.sumnetpremium = sumnetpremium;
    }

    public Double getLoading() {
        return loading;
    }

    public void setLoading(Double loading) {
        this.loading = loading;
    }

    public String getJudicalcode() {
        return judicalcode;
    }

    public void setJudicalcode(String judicalcode) {
        this.judicalcode = judicalcode == null ? null : judicalcode.trim();
    }

    public String getJudicalscope() {
        return judicalscope;
    }

    public void setJudicalscope(String judicalscope) {
        this.judicalscope = judicalscope == null ? null : judicalscope.trim();
    }

    public String getUwyear() {
        return uwyear;
    }

    public void setUwyear(String uwyear) {
        this.uwyear = uwyear == null ? null : uwyear.trim();
    }

    public String getGeographicalarea() {
        return geographicalarea;
    }

    public void setGeographicalarea(String geographicalarea) {
        this.geographicalarea = geographicalarea == null ? null : geographicalarea.trim();
    }

    public String getDeclarationind() {
        return declarationind;
    }

    public void setDeclarationind(String declarationind) {
        this.declarationind = declarationind == null ? null : declarationind.trim();
    }

    public String getDeclarationinterval() {
        return declarationinterval;
    }

    public void setDeclarationinterval(String declarationinterval) {
        this.declarationinterval = declarationinterval == null ? null : declarationinterval.trim();
    }

    public String getLowestpremiumind() {
        return lowestpremiumind;
    }

    public void setLowestpremiumind(String lowestpremiumind) {
        this.lowestpremiumind = lowestpremiumind == null ? null : lowestpremiumind.trim();
    }

    public String getLowestpremcurrency() {
        return lowestpremcurrency;
    }

    public void setLowestpremcurrency(String lowestpremcurrency) {
        this.lowestpremcurrency = lowestpremcurrency == null ? null : lowestpremcurrency.trim();
    }

    public Double getLowestpremium() {
        return lowestpremium;
    }

    public void setLowestpremium(Double lowestpremium) {
        this.lowestpremium = lowestpremium;
    }

    public String getCommissionrateind() {
        return commissionrateind;
    }

    public void setCommissionrateind(String commissionrateind) {
        this.commissionrateind = commissionrateind == null ? null : commissionrateind.trim();
    }

    public String getValidind() {
        return validind;
    }

    public void setValidind(String validind) {
        this.validind = validind == null ? null : validind.trim();
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag == null ? null : flag.trim();
    }

    public String getAdjustmentind() {
        return adjustmentind;
    }

    public void setAdjustmentind(String adjustmentind) {
        this.adjustmentind = adjustmentind == null ? null : adjustmentind.trim();
    }

    public Double getSumuwpremium() {
        return sumuwpremium;
    }

    public void setSumuwpremium(Double sumuwpremium) {
        this.sumuwpremium = sumuwpremium;
    }

    public String getGeographicalareadesc() {
        return geographicalareadesc;
    }

    public void setGeographicalareadesc(String geographicalareadesc) {
        this.geographicalareadesc = geographicalareadesc == null ? null : geographicalareadesc.trim();
    }

    public Date getDiscoverstartdate() {
        return discoverstartdate;
    }

    public void setDiscoverstartdate(Date discoverstartdate) {
        this.discoverstartdate = discoverstartdate;
    }

    public Date getDiscoverenddate() {
        return discoverenddate;
    }

    public void setDiscoverenddate(Date discoverenddate) {
        this.discoverenddate = discoverenddate;
    }

    public String getCompensationtype() {
        return compensationtype;
    }

    public void setCompensationtype(String compensationtype) {
        this.compensationtype = compensationtype == null ? null : compensationtype.trim();
    }

    public String getSubpolicyno() {
        return subpolicyno;
    }

    public void setSubpolicyno(String subpolicyno) {
        this.subpolicyno = subpolicyno == null ? null : subpolicyno.trim();
    }

    public String getPolicyvisatype() {
        return policyvisatype;
    }

    public void setPolicyvisatype(String policyvisatype) {
        this.policyvisatype = policyvisatype == null ? null : policyvisatype.trim();
    }

    public String getPolicyvisaserialno() {
        return policyvisaserialno;
    }

    public void setPolicyvisaserialno(String policyvisaserialno) {
        this.policyvisaserialno = policyvisaserialno == null ? null : policyvisaserialno.trim();
    }

    public String getEndorvisatype() {
        return endorvisatype;
    }

    public void setEndorvisatype(String endorvisatype) {
        this.endorvisatype = endorvisatype == null ? null : endorvisatype.trim();
    }

    public String getEndorvisaserialno() {
        return endorvisaserialno;
    }

    public void setEndorvisaserialno(String endorvisaserialno) {
        this.endorvisaserialno = endorvisaserialno == null ? null : endorvisaserialno.trim();
    }

    public Double getUwyeardays() {
        return uwyeardays;
    }

    public void setUwyeardays(Double uwyeardays) {
        this.uwyeardays = uwyeardays;
    }

    public String getCombineind() {
        return combineind;
    }

    public void setCombineind(String combineind) {
        this.combineind = combineind == null ? null : combineind.trim();
    }

    public Double getBackdays() {
        return backdays;
    }

    public void setBackdays(Double backdays) {
        this.backdays = backdays;
    }

    public Date getDelayfinalenddate() {
        return delayfinalenddate;
    }

    public void setDelayfinalenddate(Date delayfinalenddate) {
        this.delayfinalenddate = delayfinalenddate;
    }

    public Date getDelayenddate() {
        return delayenddate;
    }

    public void setDelayenddate(Date delayenddate) {
        this.delayenddate = delayenddate;
    }

    public Double getBonusrate() {
        return bonusrate;
    }

    public void setBonusrate(Double bonusrate) {
        this.bonusrate = bonusrate;
    }

    public Double getReferbonusrate() {
        return referbonusrate;
    }

    public void setReferbonusrate(Double referbonusrate) {
        this.referbonusrate = referbonusrate;
    }

    public String getGeographicalareadetail() {
        return geographicalareadetail;
    }

    public void setGeographicalareadetail(String geographicalareadetail) {
        this.geographicalareadetail = geographicalareadetail == null ? null : geographicalareadetail.trim();
    }

    public Date getDelaystartdate() {
        return delaystartdate;
    }

    public void setDelaystartdate(Date delaystartdate) {
        this.delaystartdate = delaystartdate;
    }

    public Double getDelaytime() {
        return delaytime;
    }

    public void setDelaytime(Double delaytime) {
        this.delaytime = delaytime;
    }

    public String getBusinessclass() {
        return businessclass;
    }

    public void setBusinessclass(String businessclass) {
        this.businessclass = businessclass == null ? null : businessclass.trim();
    }

    public String getCoinsind() {
        return coinsind;
    }

    public void setCoinsind(String coinsind) {
        this.coinsind = coinsind == null ? null : coinsind.trim();
    }

    public String getSpecialreinsind() {
        return specialreinsind;
    }

    public void setSpecialreinsind(String specialreinsind) {
        this.specialreinsind = specialreinsind == null ? null : specialreinsind.trim();
    }

    public String getUpdatecommissionind() {
        return updatecommissionind;
    }

    public void setUpdatecommissionind(String updatecommissionind) {
        this.updatecommissionind = updatecommissionind == null ? null : updatecommissionind.trim();
    }

    public String getSurrenderind() {
        return surrenderind;
    }

    public void setSurrenderind(String surrenderind) {
        this.surrenderind = surrenderind == null ? null : surrenderind.trim();
    }

    public String getDeclarationexplain() {
        return declarationexplain;
    }

    public void setDeclarationexplain(String declarationexplain) {
        this.declarationexplain = declarationexplain == null ? null : declarationexplain.trim();
    }

    public String getRiskapplytype() {
        return riskapplytype;
    }

    public void setRiskapplytype(String riskapplytype) {
        this.riskapplytype = riskapplytype == null ? null : riskapplytype.trim();
    }

    public String getProductedition() {
        return productedition;
    }

    public void setProductedition(String productedition) {
        this.productedition = productedition == null ? null : productedition.trim();
    }

    public String getEndorcalculatemode() {
        return endorcalculatemode;
    }

    public void setEndorcalculatemode(String endorcalculatemode) {
        this.endorcalculatemode = endorcalculatemode == null ? null : endorcalculatemode.trim();
    }

    public String getEffectflag() {
        return effectflag;
    }

    public void setEffectflag(String effectflag) {
        this.effectflag = effectflag == null ? null : effectflag.trim();
    }

    public String getOuterremark() {
        return outerremark;
    }

    public void setOuterremark(String outerremark) {
        this.outerremark = outerremark == null ? null : outerremark.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

	public Long getInsuredDays() {
		return insuredDays;
	}

	public void setInsuredDays(Long insuredDays) {
		this.insuredDays = insuredDays;
	}

	public Long getCalculator() {
		return calculator;
	}

	public void setCalculator(Long calculator) {
		this.calculator = calculator;
	}

	public String getChannelfundflag() {
		return channelfundflag;
	}

	public void setChannelfundflag(String channelfundflag) {
		this.channelfundflag = channelfundflag;
	}

	public String getProposalway() {
		return proposalway;
	}

	public void setProposalway(String proposalway) {
		this.proposalway = proposalway;
	}

}