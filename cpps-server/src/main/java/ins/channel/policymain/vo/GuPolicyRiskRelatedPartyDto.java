package ins.channel.policymain.vo;

import java.util.Date;

public class GuPolicyRiskRelatedPartyDto {
    private String policyno;

    private String plancode;

    private String riskcode;

    private Double serialno;

    private String subpolicyno;

    private String insuredtype;

    private String insuredcode;

    private String insuredname;

    private String insuredaddress;

    private String insuredbusinesssource;

    private String insuredflag;

    private String insuredrole;

    private String identifytype;

    private String identifynumber;

    private String insuredrelation;

    private Double relateserialno;

    private String insuredind;

    private String remark;

    private String flag;

    private String insuredsex;

    private Date insuredbirthdate;

    private String insuredpostcode;

    private String insuredofficephone;

    private String insuredmobilephone;

    private String insuredhomephone;

    private String contactname;

    private String contactphone;

    private String marriagestatus;

    private String educationbackground;

    private String relationwithholder;

    private String contactsex;

    private String contacttype;

    private String contactdepartment;

    private String contactposition;

    private String contactofficenumber;

    private String contactmobile;

    private String relatedtype;

    private String itemprovincecode;

    private String itemcitycode;

    private String itemprovincecname;

    private String itemcitycname;

    private String moneylaunderingind;

    private String starlevel;

    private String vipind;

    private Date updatesysdate;

    private String machinecode;

    private String idcollectind;

    private String countycode;
    
    /**
     * 渠道电子保单所需驼峰格式
     */
    private String insuredBirthDateString;
    
    private String insuredOfficePhone;
    
    private String insuredName;
    
    private String identifyType;
    
    private String identifyNumber;
    
    private String insuredPostCode;
    
    private String insuredAddress;
    
    private String insuredBirthDate;
    
    private String relationWithHolder;
    
    private String insuredSex;
    
    private String insuredCode;
    
    private String insuredFlag;
    
    private String insuredType;
    
    private String contactType;
    
    private String insuredMobilePhone;
    
    private String industrymaincode;
    
    private String industrykindcode;
    
    private String filmwithholder;
    
    private String contactMobile;
    
    private String Vaccination;//接种疫苗
      
    private String VaccinationDate;//接种时间
    
    public String getContactMobile() {
		return contactMobile;
	}

	public void setContactMobile(String contactMobile) {
		this.contactMobile = contactMobile;
	}

	public String getIndustrymaincode() {
		return industrymaincode;
	}

	public void setIndustrymaincode(String industrymaincode) {
		this.industrymaincode = industrymaincode;
	}

	public String getIndustrykindcode() {
		return industrykindcode;
	}

	public void setIndustrykindcode(String industrykindcode) {
		this.industrykindcode = industrykindcode;
	}

	public String getFilmwithholder() {
		return filmwithholder;
	}

	public void setFilmwithholder(String filmwithholder) {
		this.filmwithholder = filmwithholder;
	}

	public String getInsuredCode() {
		return insuredCode;
	}

	public void setInsuredCode(String insuredCode) {
		this.insuredCode = insuredCode;
	}

	public String getInsuredFlag() {
		return insuredFlag;
	}

	public void setInsuredFlag(String insuredFlag) {
		this.insuredFlag = insuredFlag;
	}

	public String getInsuredType() {
		return insuredType;
	}

	public void setInsuredType(String insuredType) {
		this.insuredType = insuredType;
	}

	public String getContactType() {
		return contactType;
	}

	public void setContactType(String contactType) {
		this.contactType = contactType;
	}

	public String getInsuredMobilePhone() {
		return insuredMobilePhone;
	}

	public void setInsuredMobilePhone(String insuredMobilePhone) {
		this.insuredMobilePhone = insuredMobilePhone;
	}

	public String getInsuredBirthDate() {
		return insuredBirthDate;
	}

	public void setInsuredBirthDate(String insuredBirthDate) {
		this.insuredBirthDate = insuredBirthDate;
	}

	public String getRelationWithHolder() {
		return relationWithHolder;
	}

	public void setRelationWithHolder(String relationWithHolder) {
		this.relationWithHolder = relationWithHolder;
	}

	public String getInsuredSex() {
		return insuredSex;
	}

	public void setInsuredSex(String insuredSex) {
		this.insuredSex = insuredSex;
	}

	public String getInsuredAddress() {
		return insuredAddress;
	}

	public void setInsuredAddress(String insuredAddress) {
		this.insuredAddress = insuredAddress;
	}

	public String getInsuredPostCode() {
		return insuredPostCode;
	}

	public void setInsuredPostCode(String insuredPostCode) {
		this.insuredPostCode = insuredPostCode;
	}

	public String getInsuredName() {
		return insuredName;
	}
    
    private String email;


	public void setInsuredName(String insuredName) {
		this.insuredName = insuredName;
	}

	public String getIdentifyType() {
		return identifyType;
	}

	public void setIdentifyType(String identifyType) {
		this.identifyType = identifyType;
	}

	public String getIdentifyNumber() {
		return identifyNumber;
	}

	public void setIdentifyNumber(String identifyNumber) {
		this.identifyNumber = identifyNumber;
	}
	
    public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getPolicyno() {
        return policyno;
    }

    public void setPolicyno(String policyno) {
        this.policyno = policyno == null ? null : policyno.trim();
    }

    public String getPlancode() {
        return plancode;
    }

    public void setPlancode(String plancode) {
        this.plancode = plancode == null ? null : plancode.trim();
    }

    public String getRiskcode() {
        return riskcode;
    }

    public void setRiskcode(String riskcode) {
        this.riskcode = riskcode == null ? null : riskcode.trim();
    }

    public Double getSerialno() {
        return serialno;
    }

    public void setSerialno(Double serialno) {
        this.serialno = serialno;
    }

    public String getSubpolicyno() {
        return subpolicyno;
    }

    public void setSubpolicyno(String subpolicyno) {
        this.subpolicyno = subpolicyno == null ? null : subpolicyno.trim();
    }

    public String getInsuredtype() {
        return insuredtype;
    }

    public void setInsuredtype(String insuredtype) {
        this.insuredtype = insuredtype == null ? null : insuredtype.trim();
    }

    public String getInsuredcode() {
        return insuredcode;
    }

    public void setInsuredcode(String insuredcode) {
        this.insuredcode = insuredcode == null ? null : insuredcode.trim();
    }

    public String getInsuredname() {
        return insuredname;
    }

    public void setInsuredname(String insuredname) {
        this.insuredname = insuredname == null ? null : insuredname.trim();
    }

    public String getInsuredaddress() {
        return insuredaddress;
    }

    public void setInsuredaddress(String insuredaddress) {
        this.insuredaddress = insuredaddress == null ? null : insuredaddress.trim();
    }

    public String getInsuredbusinesssource() {
        return insuredbusinesssource;
    }

    public void setInsuredbusinesssource(String insuredbusinesssource) {
        this.insuredbusinesssource = insuredbusinesssource == null ? null : insuredbusinesssource.trim();
    }

    public String getInsuredflag() {
        return insuredflag;
    }

    public void setInsuredflag(String insuredflag) {
        this.insuredflag = insuredflag == null ? null : insuredflag.trim();
    }

    public String getInsuredrole() {
        return insuredrole;
    }

    public void setInsuredrole(String insuredrole) {
        this.insuredrole = insuredrole == null ? null : insuredrole.trim();
    }

    public String getIdentifytype() {
        return identifytype;
    }

    public void setIdentifytype(String identifytype) {
        this.identifytype = identifytype == null ? null : identifytype.trim();
    }

    public String getIdentifynumber() {
        return identifynumber;
    }

    public void setIdentifynumber(String identifynumber) {
        this.identifynumber = identifynumber == null ? null : identifynumber.trim();
    }

    public String getInsuredrelation() {
        return insuredrelation;
    }

    public void setInsuredrelation(String insuredrelation) {
        this.insuredrelation = insuredrelation == null ? null : insuredrelation.trim();
    }

    public Double getRelateserialno() {
        return relateserialno;
    }

    public void setRelateserialno(Double relateserialno) {
        this.relateserialno = relateserialno;
    }

    public String getInsuredind() {
        return insuredind;
    }

    public void setInsuredind(String insuredind) {
        this.insuredind = insuredind == null ? null : insuredind.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag == null ? null : flag.trim();
    }

    public String getInsuredsex() {
        return insuredsex;
    }

    public void setInsuredsex(String insuredsex) {
        this.insuredsex = insuredsex == null ? null : insuredsex.trim();
    }

    public Date getInsuredbirthdate() {
        return insuredbirthdate;
    }

    public void setInsuredbirthdate(Date insuredbirthdate) {
        this.insuredbirthdate = insuredbirthdate;
    }

    public String getInsuredpostcode() {
        return insuredpostcode;
    }

    public void setInsuredpostcode(String insuredpostcode) {
        this.insuredpostcode = insuredpostcode == null ? null : insuredpostcode.trim();
    }

    public String getInsuredofficephone() {
        return insuredofficephone;
    }

    public void setInsuredofficephone(String insuredofficephone) {
        this.insuredofficephone = insuredofficephone == null ? null : insuredofficephone.trim();
    }

    public String getInsuredmobilephone() {
        return insuredmobilephone;
    }

    public void setInsuredmobilephone(String insuredmobilephone) {
        this.insuredmobilephone = insuredmobilephone == null ? null : insuredmobilephone.trim();
    }

    public String getInsuredhomephone() {
        return insuredhomephone;
    }

    public void setInsuredhomephone(String insuredhomephone) {
        this.insuredhomephone = insuredhomephone == null ? null : insuredhomephone.trim();
    }

    public String getContactname() {
        return contactname;
    }

    public void setContactname(String contactname) {
        this.contactname = contactname == null ? null : contactname.trim();
    }

    public String getContactphone() {
        return contactphone;
    }

    public void setContactphone(String contactphone) {
        this.contactphone = contactphone == null ? null : contactphone.trim();
    }

    public String getMarriagestatus() {
        return marriagestatus;
    }

    public void setMarriagestatus(String marriagestatus) {
        this.marriagestatus = marriagestatus == null ? null : marriagestatus.trim();
    }

    public String getEducationbackground() {
        return educationbackground;
    }

    public void setEducationbackground(String educationbackground) {
        this.educationbackground = educationbackground == null ? null : educationbackground.trim();
    }

    public String getRelationwithholder() {
        return relationwithholder;
    }

    public void setRelationwithholder(String relationwithholder) {
        this.relationwithholder = relationwithholder == null ? null : relationwithholder.trim();
    }

    public String getContactsex() {
        return contactsex;
    }

    public void setContactsex(String contactsex) {
        this.contactsex = contactsex == null ? null : contactsex.trim();
    }

    public String getContacttype() {
        return contacttype;
    }

    public void setContacttype(String contacttype) {
        this.contacttype = contacttype == null ? null : contacttype.trim();
    }

    public String getContactdepartment() {
        return contactdepartment;
    }

    public void setContactdepartment(String contactdepartment) {
        this.contactdepartment = contactdepartment == null ? null : contactdepartment.trim();
    }

    public String getContactposition() {
        return contactposition;
    }

    public void setContactposition(String contactposition) {
        this.contactposition = contactposition == null ? null : contactposition.trim();
    }

    public String getContactofficenumber() {
        return contactofficenumber;
    }

    public void setContactofficenumber(String contactofficenumber) {
        this.contactofficenumber = contactofficenumber == null ? null : contactofficenumber.trim();
    }

    public String getContactmobile() {
        return contactmobile;
    }

    public void setContactmobile(String contactmobile) {
        this.contactmobile = contactmobile == null ? null : contactmobile.trim();
    }

    public String getRelatedtype() {
        return relatedtype;
    }

    public void setRelatedtype(String relatedtype) {
        this.relatedtype = relatedtype == null ? null : relatedtype.trim();
    }

    public String getItemprovincecode() {
        return itemprovincecode;
    }

    public void setItemprovincecode(String itemprovincecode) {
        this.itemprovincecode = itemprovincecode == null ? null : itemprovincecode.trim();
    }

    public String getItemcitycode() {
        return itemcitycode;
    }

    public void setItemcitycode(String itemcitycode) {
        this.itemcitycode = itemcitycode == null ? null : itemcitycode.trim();
    }

    public String getItemprovincecname() {
        return itemprovincecname;
    }

    public void setItemprovincecname(String itemprovincecname) {
        this.itemprovincecname = itemprovincecname == null ? null : itemprovincecname.trim();
    }

    public String getItemcitycname() {
        return itemcitycname;
    }

    public void setItemcitycname(String itemcitycname) {
        this.itemcitycname = itemcitycname == null ? null : itemcitycname.trim();
    }

    public String getMoneylaunderingind() {
        return moneylaunderingind;
    }

    public void setMoneylaunderingind(String moneylaunderingind) {
        this.moneylaunderingind = moneylaunderingind == null ? null : moneylaunderingind.trim();
    }

    public String getStarlevel() {
        return starlevel;
    }

    public void setStarlevel(String starlevel) {
        this.starlevel = starlevel == null ? null : starlevel.trim();
    }

    public String getVipind() {
        return vipind;
    }

    public void setVipind(String vipind) {
        this.vipind = vipind == null ? null : vipind.trim();
    }

    public Date getUpdatesysdate() {
        return updatesysdate;
    }

    public void setUpdatesysdate(Date updatesysdate) {
        this.updatesysdate = updatesysdate;
    }

    public String getMachinecode() {
        return machinecode;
    }

    public void setMachinecode(String machinecode) {
        this.machinecode = machinecode == null ? null : machinecode.trim();
    }

    public String getIdcollectind() {
        return idcollectind;
    }

    public void setIdcollectind(String idcollectind) {
        this.idcollectind = idcollectind == null ? null : idcollectind.trim();
    }

    public String getCountycode() {
        return countycode;
    }

    public void setCountycode(String countycode) {
        this.countycode = countycode == null ? null : countycode.trim();
    }

	public String getVaccination() {
		return Vaccination;
	}

	public void setVaccination(String vaccination) {
		Vaccination = vaccination;
	}

	public String getVaccinationDate() {
		return VaccinationDate;
	}

	public void setVaccinationDate(String vaccinationDate) {
		VaccinationDate = vaccinationDate;
	}

	public String getInsuredOfficePhone() {
		return insuredOfficePhone;
	}

	public void setInsuredOfficePhone(String insuredOfficePhone) {
		this.insuredOfficePhone = insuredOfficePhone;
	}

	public String getInsuredBirthDateString() {
		return insuredBirthDateString;
	}

	public void setInsuredBirthDateString(String insuredBirthDateString) {
		this.insuredBirthDateString = insuredBirthDateString;
	}

	
}