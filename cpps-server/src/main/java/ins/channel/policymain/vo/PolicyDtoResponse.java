package ins.channel.policymain.vo;


import ins.channel.gupolicycoinsurance.dto.domain.GuPolicyCoinsuranceDto;

import java.util.List;

public class PolicyDtoResponse {
	
	private GuPolicyMainDto guPolicyMainDto;
	private List<GuPolicyRiskDto> guPolicyRiskDtoList;
	private List<GuPolicyRelatedPartyDto> guPolicyRelatedPartyDtoList;
	private List<GuPolicyRiskRelatedPartyDto> guPolicyRiskRelatedPartyDtoList;
	private List<GuPolicyDynamicListDto> guPolicyDynamicListDtoList1;
	private List<GuPolicyDynamicListDto> guPolicyDynamicListDtoList2;
	private List<GuPolicyItemKindDto> gupolicyItemkindDtoList;
	private List<GsClientMainDto> gsClientMainDtoList;
	private List<GuPolicyItemAcciListDto> guPolicyItemAcciListDtoList;
	private List<GuPolicyItemAcciDto> guPolicyItemAcciDtoList;
	private List<GuPolicyCoinsuranceDto> guPolicyCoinsuranceDtoList;
	private String newFlag1219;//1219 需求 RA-2022Z000218关于《雇主责任保险、安全生产责任保险修改核心系统标的信息面和增加规则引擎》 改造后表示新单 旧单 1新单

	public List<GuPolicyCoinsuranceDto> getGuPolicyCoinsuranceDtoList() {
		return guPolicyCoinsuranceDtoList;
	}

	public void setGuPolicyCoinsuranceDtoList(List<GuPolicyCoinsuranceDto> guPolicyCoinsuranceDtoList) {
		this.guPolicyCoinsuranceDtoList = guPolicyCoinsuranceDtoList;
	}

	public GuPolicyMainDto getGuPolicyMainDto() {
		return guPolicyMainDto;
	}
	public void setGuPolicyMainDto(GuPolicyMainDto guPolicyMainDto) {
		this.guPolicyMainDto = guPolicyMainDto;
	}
	public List<GuPolicyRiskDto> getGuPolicyRiskDtoList() {
		return guPolicyRiskDtoList;
	}
	public void setGuPolicyRiskDtoList(List<GuPolicyRiskDto> guPolicyRiskDtoList) {
		this.guPolicyRiskDtoList = guPolicyRiskDtoList;
	}
	public List<GuPolicyRelatedPartyDto> getGuPolicyRelatedPartyDtoList() {
		return guPolicyRelatedPartyDtoList;
	}
	public void setGuPolicyRelatedPartyDtoList(List<GuPolicyRelatedPartyDto> guPolicyRelatedPartyDtoList) {
		this.guPolicyRelatedPartyDtoList = guPolicyRelatedPartyDtoList;
	}
	public List<GuPolicyRiskRelatedPartyDto> getGuPolicyRiskRelatedPartyDtoList() {
		return guPolicyRiskRelatedPartyDtoList;
	}
	public void setGuPolicyRiskRelatedPartyDtoList(List<GuPolicyRiskRelatedPartyDto> guPolicyRiskRelatedPartyDtoList) {
		this.guPolicyRiskRelatedPartyDtoList = guPolicyRiskRelatedPartyDtoList;
	}
	public List<GuPolicyDynamicListDto> getGuPolicyDynamicListDtoList1() {
		return guPolicyDynamicListDtoList1;
	}
	public void setGuPolicyDynamicListDtoList1(List<GuPolicyDynamicListDto> guPolicyDynamicListDtoList1) {
		this.guPolicyDynamicListDtoList1 = guPolicyDynamicListDtoList1;
	}
	public List<GuPolicyDynamicListDto> getGuPolicyDynamicListDtoList2() {
		return guPolicyDynamicListDtoList2;
	}
	public void setGuPolicyDynamicListDtoList2(List<GuPolicyDynamicListDto> guPolicyDynamicListDtoList2) {
		this.guPolicyDynamicListDtoList2 = guPolicyDynamicListDtoList2;
	}
	public List<GuPolicyItemKindDto> getGupolicyItemkindDtoList() {
		return gupolicyItemkindDtoList;
	}
	public void setGupolicyItemkindDtoList(List<GuPolicyItemKindDto> gupolicyItemkindDtoList) {
		this.gupolicyItemkindDtoList = gupolicyItemkindDtoList;
	}

	public List<GsClientMainDto> getGsClientMainDtoList() {
		return gsClientMainDtoList;
	}

	public void setGsClientMainDtoList(List<GsClientMainDto> gsClientMainDtoList) {
		this.gsClientMainDtoList = gsClientMainDtoList;
	}

	public List<GuPolicyItemAcciListDto> getGuPolicyItemAcciListDtoList() {
		return guPolicyItemAcciListDtoList;
	}

	public void setGuPolicyItemAcciListDtoList(List<GuPolicyItemAcciListDto> guPolicyItemAcciListDtoList) {
		this.guPolicyItemAcciListDtoList = guPolicyItemAcciListDtoList;
	}

	public List<GuPolicyItemAcciDto> getGuPolicyItemAcciDtoList() {
		return guPolicyItemAcciDtoList;
	}

	public void setGuPolicyItemAcciDtoList(List<GuPolicyItemAcciDto> guPolicyItemAcciDtoList) {
		this.guPolicyItemAcciDtoList = guPolicyItemAcciDtoList;
	}

	public String getNewFlag1219() {
		return newFlag1219;
	}

	public void setNewFlag1219(String newFlag1219) {
		this.newFlag1219 = newFlag1219;
	}
}
