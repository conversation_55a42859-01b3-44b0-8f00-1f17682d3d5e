package ins.channel.policymain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("最新保单同步查询保单号清单结果对象")
public class PolicySyncQueryRespVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("状态码 200-成功 500-异常")
    private String status;
    @ApiModelProperty("提示信息")
    private String message;
    @ApiModelProperty("保单信息查询结果集合")
    private List<PolicyDtoResponse> policylist;

    @ApiModelProperty("保单号码集合")
    private List<String> policyNolist;
}
