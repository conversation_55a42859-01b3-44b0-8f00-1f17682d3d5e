package ins.channel.policymain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("最新保单同步查询入参对象")
public class PolicySyncRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty("创新业务标识")
	private String surverind;
	@ApiModelProperty("保单号集合")
	private List<String> policylist;
	@ApiModelProperty("保单号")
	private String policyno;
	@ApiModelProperty("保单同步方式 0-逐笔 1-批量")
	private String flag;
	//出单日期起期
	private String issuestartdate;
	//出单日期止期
	private String issueenddate;
	@ApiModelProperty("出单日期查询区间（yyyy-MM-dd）")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private List<Date> issuedatelist;

	@ApiModelProperty("保单查询 0-申报入口校验")
	private String queryFlag;

}
