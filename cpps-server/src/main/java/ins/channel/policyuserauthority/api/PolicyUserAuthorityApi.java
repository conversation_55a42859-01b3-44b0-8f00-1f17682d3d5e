package ins.channel.policyuserauthority.api;

import ins.channel.policyuserauthority.service.GuPolicyUserAuthorityService;
import ins.channel.gupolicyuserauthority.vo.PolicyuserauthorityReqVo;
import ins.channel.gupolicyuserauthority.vo.PolicyuserauthorityRespVo;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Created by sino on 2019/9/6.
 */
@RestController
@RequestMapping("/api/PolicyUserAuthority")
@Api(tags = "PolicyUserAuthority", description = "保单授权管理服务")
public class PolicyUserAuthorityApi {

    @Autowired
    private GuPolicyUserAuthorityService service;



    @ApiOperation(value = "分页模糊查询保单授权信息")
    @PostMapping(value = "/pageByCondition")
    public ResponseVo<PageResult<PolicyuserauthorityRespVo>> pageByCondition(@RequestBody  PolicyuserauthorityReqVo vo) {
        PageResult<PolicyuserauthorityRespVo> result = service.pageByCondition(vo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "根据保单号查询待修改的保单授权信息")
    @PostMapping(value = "/selectOne")
    public ResponseVo<PolicyuserauthorityRespVo> selectOne(@RequestBody PolicyuserauthorityReqVo vo) {
        return ResponseVo.ok(service.queryOne(vo));
    }

    @ApiOperation(value = "保单授权")
    @PostMapping(value = "/save")
    public ResponseVo<String> save(@RequestBody PolicyuserauthorityReqVo vo) {
        return ResponseVo.ok(service.create(vo));
    }

    @ApiOperation(value = "修改保单授权信息")
    @PostMapping(value = "/modify")
    public ResponseVo<String> modify(@RequestBody PolicyuserauthorityReqVo vo) {
        return ResponseVo.ok(service.modify(vo));
    }

    @ApiOperation(value = "删除指定保单授权信息")
    @PostMapping(value = "/delete")
    public ResponseVo<String> delete(@RequestBody PolicyuserauthorityReqVo vo) {
        return ResponseVo.ok(service.delete(vo));
    }
}
