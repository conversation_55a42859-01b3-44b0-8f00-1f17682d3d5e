package ins.channel.policyuserauthority.service;

import com.sinosoft.power.po.G<PERSON>er;
import ins.channel.company.dao.GgcompanyDao;
import ins.channel.company.po.Ggcompany;
import ins.channel.gupolicycopymain.dao.GupolicycopymainDao;
import ins.channel.gupolicycopymain.po.Gupolicycopymain;
import ins.channel.gupolicymain.dao.GupolicymainDao;
import ins.channel.gupolicymain.po.Gupolicymain;
import ins.channel.gupolicymainbasic.dao.GupolicymainbasicDao;
import ins.channel.gupolicymainbasic.po.Gupolicymainbasic;
import ins.channel.gupolicyuserauthority.dao.GupolicyuserauthorityDao;
import ins.channel.gupolicyuserauthority.po.Gupolicyuserauthority;
import ins.channel.gupolicyuserauthority.vo.PolicyuserauthorityReqVo;
import ins.channel.gupolicyuserauthority.vo.PolicyuserauthorityRespVo;
import ins.channel.support.constant.BusinessNoType;
import ins.channel.support.service.BusinessNoService;
import ins.channel.user.service.GguserService;
import ins.channel.user.vo.GguserVo;
import ins.framework.exception.BusinessException;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.platform.common.PageResult;
import ins.platform.common.SysUser;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.PageHelper;
import ins.platform.utils.SessionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by sino on 2019/9/5.
 */
@Service
@Slf4j
@Transactional
public class GuPolicyUserAuthorityService {

    @Autowired
    private GupolicyuserauthorityDao gupolicyuserauthorityDao;

    @Autowired
    private GupolicymainDao gupolicymainDao;
    @Autowired
    private GupolicymainbasicDao gupolicymainbasicDao;
    @Autowired
    private GgcompanyDao ggcompanyDao;
    @Autowired
    private BusinessNoService businessNoService;
    @Autowired
    private GupolicycopymainDao gupolicycopymainDao;

    @Autowired
    private GguserService userService;

    /**
     * 分页模糊查询保单授权信息
     *
     * @param policyuserauthorityReqVo
     * @return
     */
    @Transactional(readOnly = true)
    public PageResult<PolicyuserauthorityRespVo> pageByCondition(PolicyuserauthorityReqVo policyuserauthorityReqVo) {
        //校验参数
        List<Date> startDateList = policyuserauthorityReqVo.getStartDateList();
        if (!CollectionUtils.isEmpty(startDateList)) {
            Assert.isTrue(startDateList.size() == 2, "起保日期查询区间长度有误!");
            Assert.notNull(startDateList.get(0), "起保日期起期不能为空!");
            Assert.notNull(startDateList.get(1), "起保日期止期不能为空!");
            Assert.isTrue(startDateList.get(0).compareTo(startDateList.get(1)) <= 0, "起保日期起期需小于等于止期!");
            policyuserauthorityReqVo.setStartDatestart(startDateList.get(0));
            policyuserauthorityReqVo.setStartDateend(startDateList.get(1));
        }
        List<Date> endDateList = policyuserauthorityReqVo.getEndDateList();
        if (!CollectionUtils.isEmpty(endDateList)) {
            Assert.isTrue(endDateList.size() == 2, "终保日期查询区间长度有误!");
            Assert.notNull(endDateList.get(0), "终保日期起期不能为空!");
            Assert.notNull(endDateList.get(1), "终保日期止期不能为空!");
            Assert.isTrue(endDateList.get(0).compareTo(endDateList.get(1)) <= 0, "终保日期起期需小于等于止期!");
            policyuserauthorityReqVo.setEndDatestart(endDateList.get(0));
            policyuserauthorityReqVo.setEndDateend(endDateList.get(1));
        }
        PageParam pageParam = PageHelper.getPageParam(policyuserauthorityReqVo);
        Page<PolicyuserauthorityRespVo> results = gupolicyuserauthorityDao.pageByCondition(pageParam, policyuserauthorityReqVo);
        return PageHelper.convert(pageParam, results, PolicyuserauthorityRespVo.class);
    }

    @Transactional(readOnly = true)
    public PolicyuserauthorityRespVo queryOne(PolicyuserauthorityReqVo vo) {
        Assert.hasText(vo.getPolicyNo(), "保单号码不能为空");
        List<Gupolicyuserauthority> gupolicyuserauthorityList = gupolicyuserauthorityDao.selectByCondition(vo);
        Assert.notEmpty(gupolicyuserauthorityList, "无效的保单号");
        return BeanCopyUtils.clone(gupolicyuserauthorityList.get(0), PolicyuserauthorityRespVo.class);
    }

    /**
     * 保单授权
     *
     * @param vo
     * @return
     */
    @Transactional
    public String create(PolicyuserauthorityReqVo vo) {
        //校验入参
        Set<String> policynoList = vo.getPolicynoList();
        Assert.notNull(policynoList, "请至少选择一条保单数据");
        Assert.hasText(vo.getCompanyCode(), "关联机构代码不能为空");
        Assert.notNull(vo.getUserCode(), "关联机构用户代码不能为空");
        Assert.notNull(vo.getUserCname(), "关联机构用户名称不能为空");
        //根据关联机构代码查询名称
        Ggcompany companyCondition = new Ggcompany();
        companyCondition.setCompanyCode(vo.getCompanyCode());
        List<Ggcompany> ggcompanyList = ggcompanyDao.selectByCondition(companyCondition);
        if (CollectionUtils.isEmpty(ggcompanyList)) {
            throw new BusinessException("存在无效的关联机构代码: " + vo.getCompanyCode());
        }
        Ggcompany ggcompany = ggcompanyList.get(0);
        //判断是否有该保单授权信息
        PolicyuserauthorityReqVo voCondition = new PolicyuserauthorityReqVo();
        voCondition.setPolicynoList(policynoList);
        List<Gupolicyuserauthority> gupolicyuserauthorityList = gupolicyuserauthorityDao.selectByCondition(voCondition);
        if (!CollectionUtils.isEmpty(gupolicyuserauthorityList)) {
            List<String> errorList = gupolicyuserauthorityList.stream().map(Gupolicyuserauthority::getPolicyNo).collect(Collectors.toList());
            throw new BusinessException("存在已授权的保单! 保单号: " + errorList);
        }

        for (String policyNo : policynoList) {
            //根据保单号获取保单主表信息
            Gupolicymain condition = new Gupolicymain();
            condition.setPolicyNo(policyNo);
            List<Gupolicymain> gupolicymainList = gupolicymainDao.selectByCondition(condition);
            if (CollectionUtils.isEmpty(gupolicymainList)) {
                throw new BusinessException("存在无效的保单号: " + policyNo);
            }
            Gupolicymain gupolicymain = gupolicymainList.get(0);
            Gupolicymain policymainUpdateCondition = new Gupolicymain();
            //设置保单主表关联机构代码
            policymainUpdateCondition.setCompanycode(vo.getCompanyCode());
            //设置保单主表关联机构名称
            policymainUpdateCondition.setCompanyname(ggcompany.getCompanyCname());
            //设置保单授权人员
            policymainUpdateCondition.setProjectmanagercode(vo.getUserCode());
            policymainUpdateCondition.setProjectmanagername(vo.getUserCname());
            policymainUpdateCondition.setUpdatesysdate(new Date());
            //设置保单主表主键
            policymainUpdateCondition.setId(gupolicymain.getId());

            //根据保单号码获取保单轨迹表信息
            Gupolicycopymain gupolicycopymain = new Gupolicycopymain();
            gupolicycopymain.setPolicyNo(policyNo);
            List<Gupolicycopymain> gupolicycopymainList = gupolicycopymainDao.selectByCondition(gupolicycopymain);
            if (CollectionUtils.isEmpty(gupolicycopymainList)) {
                throw new BusinessException("保单轨迹表存在无效的保单号: " + policyNo);
            }
            gupolicycopymain.setPolicyNo(policyNo);
            gupolicycopymain.setCompanycode(vo.getCompanyCode());
            gupolicycopymain.setCompanyname(ggcompany.getCompanyCname());
            gupolicycopymain.setProjectmanagercode(vo.getUserCode());
            gupolicycopymain.setProjectmanagername(vo.getUserCname());
            gupolicycopymain.setUpdatesysdate(new Date());

            //添加授权表信息
            Gupolicyuserauthority policyuserauthorityClone = BeanCopyUtils.clone(vo, Gupolicyuserauthority.class);
            //根据保单号获取保单基础信息表信息
            Gupolicymainbasic basicCondition = new Gupolicymainbasic();
            basicCondition.setPolicyNo(policyNo);
            List<Gupolicymainbasic> gupolicymainbasicList = gupolicymainbasicDao.selectByCondition(basicCondition);
            if (CollectionUtils.isEmpty(gupolicymainbasicList)) {
                throw new BusinessException("存在无效的保单号: " + policyNo);
            }
            Gupolicymainbasic gupolicymainbasic = gupolicymainbasicList.get(0);
            //设置授权表渠道代码
            policyuserauthorityClone.setChannelcode(gupolicymainbasic.getBusinesschannel());
            //设置授权表保险公司归属机构
            policyuserauthorityClone.setInsurancecompanycode(gupolicymainbasic.getInsurancecompanycode());
            //设置授权表创新业务标识
            policyuserauthorityClone.setSurveyind(gupolicymain.getSurveyind());
            //设置授权表修改人代码
            policyuserauthorityClone.setUserCode(SessionHelper.getLoginUser().getUserCode());
            //设置授权表险种
            policyuserauthorityClone.setRiskCode(gupolicymain.getProductcode());
            //设置授权表保单号
            policyuserauthorityClone.setPolicyNo(gupolicymain.getPolicyNo());
            //设置授权表入机日期
            policyuserauthorityClone.setInputDate(new Date());
            //设置id
            policyuserauthorityClone.setId(businessNoService.nextNo(BusinessNoType.ID));
            try {
                gupolicyuserauthorityDao.insertSelective(BeanCopyUtils.clone(policyuserauthorityClone, Gupolicyuserauthority.class));
                gupolicymainDao.updateSelectiveByPrimaryKey(policymainUpdateCondition);
                //信息轨迹表更新关联机构代码和关联机构名称
                gupolicycopymainDao.updateCompanyCodeAndCompanyName(gupolicycopymain);
            } catch (BusinessException e) {
                log.info(MessageFormat.format("保单授权异常:{0}", e.getMessage()));
                throw new BusinessException("授权失败!网络连接异常,请稍后再试或联系运维人员处理");
            }
        }
        return "授权成功!";
    }

    @Transactional
    public String modify(PolicyuserauthorityReqVo vo) {
        GguserVo modifyUser = userService.queryOne(vo.getUserCode());
        //校验入参
        String policyNo = vo.getPolicyNo();
        Assert.hasText(policyNo, "请至少选择一条保单数据");
        Assert.hasText(vo.getCompanyCode(), "关联机构代码不能为空");
        //根据关联机构代码查询名称
        Ggcompany companyCondition = new Ggcompany();
        companyCondition.setCompanyCode(vo.getCompanyCode());
        List<Ggcompany> ggcompanyList = ggcompanyDao.selectByCondition(companyCondition);
        if (CollectionUtils.isEmpty(ggcompanyList)) {
            throw new BusinessException("无效的关联机构代码: " + vo.getCompanyCode());
        }
        Ggcompany ggcompany = ggcompanyList.get(0);
        //判断是否有该保单授权信息

        PolicyuserauthorityReqVo voCondition = new PolicyuserauthorityReqVo();
        voCondition.setPolicyNo(vo.getPolicyNo());
        List<Gupolicyuserauthority> gupolicyuserauthorityList = gupolicyuserauthorityDao.selectByCondition(voCondition);
        if (CollectionUtils.isEmpty(gupolicyuserauthorityList)) {
            throw new BusinessException("无效的保单号: " + vo.getPolicyNo());
        }
        for (Gupolicyuserauthority gupolicyuserauthority : gupolicyuserauthorityList) {
            //更新授权表信息
            gupolicyuserauthority.setCompanyCode(vo.getCompanyCode());
            gupolicyuserauthority.setUpdatesysdate(new Date());
            gupolicyuserauthority.setUserCode(SessionHelper.getLoginUser().getUserCode());
            gupolicyuserauthority.setRemark(vo.getRemark());

            //更新保单主表信息
            //根据保单号获取保单主表信息
            Gupolicymain condition = new Gupolicymain();
            condition.setPolicyNo(policyNo);
            List<Gupolicymain> gupolicymainList = gupolicymainDao.selectByCondition(condition);
            if (CollectionUtils.isEmpty(gupolicymainList)) {
                throw new BusinessException("无效的保单号: " + policyNo);
            }
            Gupolicycopymain gupolicycopymain = new Gupolicycopymain();
            gupolicycopymain.setPolicyNo(policyNo);
            List<Gupolicycopymain> gupolicycopymainList = gupolicycopymainDao.selectByCondition(gupolicycopymain);
            if (CollectionUtils.isEmpty(gupolicycopymainList)) {
                throw new BusinessException("无效的保单号: " + policyNo);
            }
            Gupolicymain gupolicymain = gupolicymainList.get(0);
            //设置保单主表关联机构代码
            Gupolicymain policymainUpdateCondition = new Gupolicymain();
            //设置保单主表关联机构代码
            policymainUpdateCondition.setCompanycode(vo.getCompanyCode());
            //设置保单主表关联机构名称
            policymainUpdateCondition.setCompanyname(ggcompany.getCompanyCname());
            policymainUpdateCondition.setProjectmanagercode(modifyUser.getUserCode());
            policymainUpdateCondition.setProjectmanagername(modifyUser.getUserCname());
            policymainUpdateCondition.setUpdatesysdate(new Date());
            policymainUpdateCondition.setId(gupolicymain.getId());

            gupolicycopymain = new Gupolicycopymain();
            gupolicycopymain.setPolicyNo(policyNo);
            gupolicycopymain.setCompanycode(vo.getCompanyCode());
            gupolicycopymain.setCompanyname(ggcompany.getCompanyCname());
            gupolicycopymain.setProjectmanagercode(modifyUser.getUserCode());
            gupolicycopymain.setProjectmanagername(modifyUser.getUserCname());
            gupolicycopymain.setUpdatesysdate(new Date());
            try {
                gupolicyuserauthorityDao.updateSelectiveByPrimaryKey(gupolicyuserauthority);
                gupolicymainDao.updateSelectiveByPrimaryKey(policymainUpdateCondition);
                gupolicycopymainDao.updateCompanyCodeAndCompanyName(gupolicycopymain);
            } catch (Exception e) {
                log.info(MessageFormat.format("保单授权修改异常:{0}", e.getMessage()));
                throw new BusinessException("修改失败!网络连接异常,请稍后再试或联系运维人员处理");
            }
        }
        return "修改成功!";
    }

    @Transactional
    public String delete(PolicyuserauthorityReqVo vo) {
        Assert.hasText(vo.getPolicyNo(), "请至少选择一条保单数据");
        List<Gupolicyuserauthority> gupolicyuserauthorityList = gupolicyuserauthorityDao.selectByCondition(vo);
        if (CollectionUtils.isEmpty(gupolicyuserauthorityList)) {
            List<String> errorList = gupolicyuserauthorityList.stream().map(Gupolicyuserauthority::getPolicyNo).collect(Collectors.toList());
            throw new BusinessException("无效的保单号: " + vo.getPolicyNo());
        }
        //删除授权表对应信息
        Gupolicyuserauthority gupolicyuserauthority = gupolicyuserauthorityList.get(0);
        //将保单主表关联机构代码/名称置空
        //根据保单号获取保单主表信息
        Gupolicymain condition = new Gupolicymain();
        condition.setPolicyNo(vo.getPolicyNo());
        List<Gupolicymain> gupolicymainList = gupolicymainDao.selectByCondition(condition);
        if (CollectionUtils.isEmpty(gupolicymainList)) {
            throw new BusinessException("无效的保单号: " + vo.getPolicyNo());
        }
        Gupolicycopymain gupolicycopymain = new Gupolicycopymain();
        gupolicycopymain.setPolicyNo(vo.getPolicyNo());
        List<Gupolicycopymain> gupolicycopymainList = gupolicycopymainDao.selectByCondition(gupolicycopymain);
        if (CollectionUtils.isEmpty(gupolicycopymainList)) {
            throw new BusinessException("无效的保单号: " + vo.getPolicyNo());
        }
        Gupolicymain gupolicymain = gupolicymainList.get(0);
        //设置保单主表关联机构代码
        gupolicymain.setCompanycode(null);
        gupolicymain.setCompanyname(null);
        gupolicymain.setProjectmanagername(null);
        gupolicymain.setProjectmanagercode(null);
        gupolicymain.setUpdatesysdate(new Date());
        gupolicymain.setId(gupolicymain.getId());

        gupolicycopymain = new Gupolicycopymain();
        gupolicycopymain.setPolicyNo(vo.getPolicyNo());
        gupolicycopymain.setCompanycode(null);
        gupolicycopymain.setCompanyname(null);
        gupolicycopymain.setProjectmanagername(null);
        gupolicycopymain.setProjectmanagercode(null);
        gupolicycopymain.setUpdatesysdate(new Date());


        try {
            gupolicyuserauthorityDao.deleteByPrimaryKey(gupolicyuserauthority.getId());
            gupolicymainDao.invalidMainCompany(gupolicymain);
            gupolicycopymainDao.updateNullValues(gupolicycopymain);
        } catch (Exception e) {
            log.info(MessageFormat.format("保单授权信息删除异常:{0}", e.getMessage()));
            throw new BusinessException("删除失败!网络连接异常,请稍后再试或联系运维人员处理");
        }
        return "删除成功!";
    }

}
