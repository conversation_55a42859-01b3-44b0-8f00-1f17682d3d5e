package ins.channel.power.api;

import ins.channel.power.service.SaaGradeService;
import ins.channel.power.vo.GradeQueryPageVo;
import ins.channel.power.vo.GradeQueryVo;
import ins.channel.power.vo.SaagradeInfoVo;
import ins.channel.power.vo.SaagradeVo;
import ins.channel.user.vo.GguserVo;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import ins.platform.common.SysUser;
import ins.platform.utils.JwtAuthHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/grade")
@Api(tags = "Grade", description = "角色管理服务")
public class GradeApi {

    @Autowired
    private JwtAuthHelper authHelper;

    @Autowired
    private SaaGradeService service;

    @ApiOperation(value = "添加角色")
    @PostMapping("/addGrade")
    public ResponseVo<Void> addGrade(@RequestBody SaagradeVo saagradeVo) {
        //modify By Zhoutaoyu 角色级别默认为总公司('1'),前端不展示 2019/10/04
        service.addGrade(saagradeVo);
        return ResponseVo.ok();
    }


    @ApiOperation(value = "查询角色列表")
    @PostMapping("/queryGrade")
    public ResponseVo<List<SaagradeVo>> queryGrade(@RequestBody GradeQueryVo vo,@RequestHeader("Authorization") String token) {
        SysUser sysUser = authHelper.getSysUser(token);
        String userInd = sysUser.getUserInd();
        if("1".equals(userInd))userInd = null;
        return ResponseVo.ok(service.query(vo.getId(), vo.getGradecname(),vo.getValidstatus(),userInd));
    }

    @ApiOperation(value = "角色分页模糊查询")
    @PostMapping("/queryGradePage")
    public ResponseVo<PageResult<SaagradeVo>> queryGradePage(@RequestBody GradeQueryPageVo vo,@RequestHeader("Authorization") String token) {
        SysUser sysUser = authHelper.getSysUser(token);
        if(!"1".equals(sysUser.getUserInd()))vo.setUserInd(sysUser.getUserInd());
        return ResponseVo.ok(service.queryPage(vo));
    }

    /*@ApiOperation(value = "查询登录角色的本级及下级  传角色id就行了")
    @PostMapping("/querySubGrade")
    public ResponseVo<List<SaagradeVo>> querySubGrade(@RequestBody GradeQueryVo vo) {
        return ResponseVo.ok(service.querySubGrade(vo.getId()));
    }*/


    //Add By zhoutaoyu 新增修改角色名称及有效状态Api 2019/10/04
    @ApiOperation(value = "修改角色名称及有效状态")
    @PostMapping("/modifyGrade")
    public ResponseVo<Void> modifyGrade(@RequestBody GradeQueryVo vo) {
        service.modifyGrade(vo);
        return ResponseVo.ok();
    }

    @ApiOperation(value = "查询选择角色功能详细信息")
    @PostMapping("/queryGradeInfo")
    public ResponseVo<SaagradeInfoVo> queryGradeInfo(@RequestBody GradeQueryVo vo) {
        return ResponseVo.ok(service.queryGradeInfo(vo.getId(), vo.getGradecname(), vo.getUserInd()));
    }

    @ApiOperation(value = "更新选择角色功能")
    @PostMapping("/updateGradeInfo")
    public ResponseVo<Void> updateGradeInfo(@RequestBody SaagradeInfoVo vo) {
        service.updateGradeInfo(vo);
        return ResponseVo.ok();
    }
}
