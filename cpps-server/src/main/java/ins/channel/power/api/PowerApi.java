package ins.channel.power.api;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import ins.channel.power.po.Saauserpaymentcompany;
import ins.channel.power.service.SaaGradeService;
import ins.channel.power.vo.*;
import ins.channel.user.po.Gguser;
import ins.platform.utils.JwtAuthHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import com.sinosoft.power.api.SaaPowerApi;
import com.sinosoft.power.vo.PowerVo;

import ins.framework.exception.BusinessException;
import ins.channel.company.vo.GgcompanyVo;
import ins.channel.power.service.PowerService;
import ins.platform.common.ResponseVo;
import ins.platform.common.SysUser;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.SessionHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@RestController
@RequestMapping("/api/power")
@Api(tags = "Power", description = "权限管理服务")
public class PowerApi {

    @Autowired
    private PowerService powerService;

    @Value("${administrator.usercode:000000}")
    private String adminCode;

    /**
     * 校验用户权限
     *
     * @param userCode
     * @return
     */
   /* @ApiOperation(value = "校验用户权限", notes = "校验用户权限")
    @RequestMapping(value = "/checkPowerByUserCodeAndTaskCode", method = RequestMethod.GET)
    public ResponseVo<Boolean> checkPowerByUserCodeAndTaskCode(String userCode, String taskCode) {
        //TODO SaaUserGradeTask  不用了需要修改
        return ResponseVo.ok(SaaPowerApi.getInstance().getPowerService().checkPower(userCode, taskCode));
    }*/

    /*@ApiOperation(value = "查询用户的角色 传userCode ")
    @PostMapping("/initUserGrade")
    public ResponseVo<UserPowerVo> initUserGrade(@RequestBody UserPowerVo UserPowerVo) {
        PowerVo vo = new PowerVo();
        BeanCopyUtils.copy(UserPowerVo, vo);
        SaaPowerApi.getInstance().getSaaDeployService().initUserGrade(vo);
        BeanCopyUtils.copy(vo, UserPowerVo);
        //Add By Zhoutaoyu Reason:设置当前登录用户代码 Time:2019/09/25
        UserPowerVo.setCurrentUserCode(SessionHelper.getLoginUser().getUserCode());
        //TODO 默认角色ID待处理
        UserPowerVo.setDefaultGradeId(1001L);
        return ResponseVo.ok(UserPowerVo);
    }*/


    @Autowired
    private JwtAuthHelper authHelper;

    //Add By Zhoutaoyu Time:2019/09/24
    @ApiOperation(value = "查询选择用户角色详细信息")
    @PostMapping("/queryUserInfo")
    public ResponseVo<GguserInfoVo> queryUserInfo(@RequestBody UserQueryVo vo,@RequestHeader("Authorization") String token) {
        SysUser sysUser = authHelper.getSysUser(token);
        return ResponseVo.ok(powerService.queryUserInfo(vo.getUserCode(), vo.getUserCname(), sysUser.getUserInd()));
    }

    /*@ApiOperation(value = "查询用户的默认角色")
    @PostMapping("/queryDefaultGradeByUser")
    public ResponseVo<Long> queryDefaultGradeByUser(@ApiParam("用户代码") @RequestParam("userCode") String userCode) {
        Long defaultGradeid = userService.queryOne(userCode).getDefaultGradeid();
        return ResponseVo.ok(defaultGradeid);
    }*/

    /*
    //Modify By Zhoutaoyu Reason: 返回Void  Time:2019/09/25
    //@ApiOperation(value = "保存用户的角色 userCode currentUserCode defaultGradeId saaUserGradeVoList里的gradeid 返回原名字")
    @ApiOperation(value = "保存用户的角色")
    @PostMapping("/saveUserGrade")
    //public ResponseVo<Set<String>> saveUserGrade(@RequestBody UserPowerVo userPowerVo) {
    public ResponseVo<Void> saveUserGrade(@RequestBody UserPowerVo userPowerVo) {
        PowerVo vo = new PowerVo();
        BeanCopyUtils.copy(userPowerVo, vo);
        if(CollectionUtils.isNotEmpty(userPowerVo.getSaaUserGradeVoList())){
            if(userPowerVo.getSaaUserGradeVoList().size()>1){
                if(userPowerVo.getDefaultGradeId() ==null){
                    throw new BusinessException("默认角色不能为空");
                }
            }
        }
        Set<String> oldNameList =SaaPowerApi.getInstance().getSaaDeployService().saveUserGrade(vo, userPowerVo.getCurrentUserCode(), userPowerVo.getDefaultGradeId());
//        return ResponseVo.ok(oldNameList);
        return ResponseVo.ok();
    }*/

    //Add By Zhoutaoyu Time:2019/09/23
    @ApiOperation(value = "更新选择用户角色")
    @PostMapping("/updateUserGradeInfo")
    public ResponseVo<Void> updateUserGradeInfo(@RequestBody GguserInfoVo vo,@RequestHeader("Authorization") String token) {
        SysUser sysUser = authHelper.getSysUser(token);
        if(!adminCode.contains(sysUser.getUserCode())){
            return ResponseVo.ok();
        }
        powerService.updateUserGradeInfo(vo);
        return ResponseVo.ok();
    }

   /* @ApiOperation(value = "查询用户角色的操作机构代码")
    @PostMapping("/querySelectComCodeByUser")
    public ResponseVo<Set<String>> querySelectComCodeByUser(@ApiParam("用户代码") @RequestParam("userCode") String userCode,
                                                            @ApiParam("角色代码") @RequestParam("gradeId")Long gradeId) {
        return ResponseVo.ok(SaaPowerApi.getInstance().getPowerService().querySelectComCodeByUser(userCode, gradeId));
    }*/

    /*@ApiOperation(value = "查询用户角色的操作机构信息")
    @PostMapping("/querySelectComInfoByUser")
    public ResponseVo<List<GgcompanyVo>> querySelectComInfoByUser(@ApiParam("用户代码") @RequestParam("userCode") String userCode,
                                                                  @ApiParam("角色代码") @RequestParam("gradeId")Long gradeId) {
        List<GgcompanyVo> companyVos = powerService.queryUserGradeOrgInfo(userCode, gradeId);
        return ResponseVo.ok(companyVos);
    }*/

    /*@ApiOperation(value = "查询用户角色的默认操作机构代码")
    @PostMapping("/queryDefaultComCodeByUser")
    public ResponseVo<String> queryDefaultComCodeByUser(@ApiParam("用户代码") @RequestParam("userCode") String userCode) {
        String defaultComCode = userService.queryOne(userCode).getDefaultCom();
        return ResponseVo.ok( defaultComCode);
    }*/

    /*@ApiOperation(value = "保存用户角色的操作机构")
    @PostMapping("/saveUserGradeCom")
    public ResponseVo<Set<String>> saveUserGradeCom(@RequestBody SaaComDataSaveVo saaComDataSaveVo) {
        SaaComDataVo vo = new SaaComDataVo();
        BeanCopyUtils.copy(saaComDataSaveVo, vo);
        return ResponseVo.ok(SaaPowerApi.getInstance().getSaaDeployService().saveUserComData(vo, saaComDataSaveVo.getCurrentUserCode(),saaComDataSaveVo.getDefaultComCode()));
    }*/
//Modify By Zhoutaoyu Reason: 在更新操作中完成增加和删除  Time:2019/09/25
    @ApiOperation(value = "更新选择用户的操作机构")
    @PostMapping("/updateUserGradeMakeCom")
    public ResponseVo<Void> updateUserGradeMakeCom(@RequestBody SaauserpermitdataInfoVo vo) {
        powerService.updateUserGradeMakeCom(vo);
        return ResponseVo.ok();
    }

    //Add By Zhoutaoyu Time:2019/09/24
    @ApiOperation(value = "查询选择用户操作机构详细信息")
    @PostMapping("/queryUserMakeComInfo")
    public ResponseVo<SaauserpermitdataInfoVo> queryUserMakeComInfo(@RequestBody UserQueryVo vo) {
        return ResponseVo.ok(powerService.queryuserpermitdataInfo(vo.getUserCode(), vo.getUserCname()));
    }

    @ApiOperation(value = "查询用户角色的收付机构信息")
    @PostMapping("/queryPaymentCompanyByUser")
    public ResponseVo<List<Map<String, String>>> queryPaymentComCodeByUser(@ApiParam("用户代码") @RequestParam("userCode") String userCode) {
        List<SaauserpaymentcompanyVo> paymentCompanyList = powerService.queryPaymentCompanyByUserCode(userCode);
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        for (SaauserpaymentcompanyVo saauserpaymentcompanyVo : paymentCompanyList) {
        	Map<String, String> paymentcompanyInfoMap = new HashMap<String, String>();
        	paymentcompanyInfoMap.put("paymentComcode", saauserpaymentcompanyVo.getPaymentComcode());
        	paymentcompanyInfoMap.put("paymentCompanyname", saauserpaymentcompanyVo.getPaymentCompanyname());
        	result.add(paymentcompanyInfoMap);
		}
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "查询用户角色的收付机构信息(根据登录用户信息查询)")
    @PostMapping("/queryPaymentCompanyByLoginUser")
    public ResponseVo<List<Map<String, String>>> queryPaymentCompanyByLoginUser() {
        SysUser loginUser = SessionHelper.getLoginUser();
        List<SaauserpaymentcompanyVo> paymentCompanyList = powerService.queryPaymentCompanyByUserCode(loginUser.getUserCode());
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        for (SaauserpaymentcompanyVo saauserpaymentcompanyVo : paymentCompanyList) {
        	Map<String, String> paymentcompanyInfoMap = new HashMap<String, String>();
        	paymentcompanyInfoMap.put("paymentComcode", saauserpaymentcompanyVo.getPaymentComcode());
        	paymentcompanyInfoMap.put("paymentCompanyname", saauserpaymentcompanyVo.getPaymentCompanyname());
        	result.add(paymentcompanyInfoMap);
		}
        return ResponseVo.ok(result);
    }

    //Add By Zhoutaoyu Time:2019/09/29
    @ApiOperation(value = "查询选择用户收付机构详细信息")
    @PostMapping("/queryUserPaymentComInfo")
    public ResponseVo<SaauserpaymentcompanyInfoVo> queryUserPaymentComInfo(@RequestBody UserQueryVo vo) {
        return ResponseVo.ok(powerService.queryuserpaymentcompanyInfo(vo.getUserCode(), vo.getUserCname()));
    }

    @ApiOperation(value = "更新选择用户的收付机构")
    @PostMapping("/updateUserPaymentCom")
    public ResponseVo<Void> updateUserPaymentCom(@RequestBody SaauserpaymentcompanyInfoVo vo) {
        powerService.updateUserPaymentCom(vo);
        return ResponseVo.ok();
    }

    @ApiOperation(value = "查询用户角色的操作机构信息(根据登录用户信息查询)")
    @PostMapping("/queryGgCompanyByLoginUser")
    public ResponseVo<List<Map<String, String>>> queryGgCompanyByLoginUser() {
        SysUser loginUser = SessionHelper.getLoginUser();
        List<UserpermitdataVo> ggCompanyList = powerService.queryGgCompanyByLoginUser(loginUser.getUserCode());
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        for (UserpermitdataVo userpermitdataVo : ggCompanyList) {
            Map<String, String> ggcompanyInfoMap = new HashMap<String, String>();
            ggcompanyInfoMap.put("comCode", userpermitdataVo.getComCode());
            ggcompanyInfoMap.put("companyName", userpermitdataVo.getCompanyCname());
            result.add(ggcompanyInfoMap);
        }
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "根据机构查询用户信息")
    @PostMapping("/searchUserInfoListByCompanyCoude")
    public ResponseVo<List<Map<String, String>>> searchUserInfoListByCompanyCoude(@ApiParam("机构编码") @RequestParam("companyCode") String companyCode) {
        List<Gguser> ggCompanyList = powerService.searchUserInfoListByCompanyCoude(companyCode);
        List<Map<String, String>> result = new ArrayList<Map<String, String>>();
        Map<String, String> ggcompanyInfoMap = new HashMap<String, String>();
        ggcompanyInfoMap.put("userCode", "0");
        ggcompanyInfoMap.put("userCname", "全部");
        result.add(ggcompanyInfoMap);
        for (Gguser gguser : ggCompanyList) {
            ggcompanyInfoMap = new HashMap<String, String>();
            ggcompanyInfoMap.put("userCode", gguser.getUserCode());
            ggcompanyInfoMap.put("userCname", gguser.getUserCname());
            result.add(ggcompanyInfoMap);
        }
        return ResponseVo.ok(result);
    }

}
