package ins.channel.power.api;


import ins.channel.power.service.SaaTaskService;
import ins.channel.power.vo.SaataskVo;
import ins.channel.power.vo.TaskAddedVo;
import ins.channel.power.vo.TaskQueryVo;
import ins.platform.common.ResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * @apiNote 功能管理Api
 * <AUTHOR>
 * @date 2019-10-04
 */
@RestController
@RequestMapping("/api/task")
@Api(tags = "Task", description = "功能管理服务")
public class TaskApi {

    @Autowired
    private SaaTaskService service;

    @ApiOperation(value = "添加功能")
    @PostMapping("/addTask")
    public ResponseVo<Void> addTask(@RequestBody TaskAddedVo vo) {
        service.addTask(vo);
        return ResponseVo.ok();
    }

    @ApiOperation(value = "查询功能列表")
    @PostMapping("/queryTask")
    public ResponseVo<List<SaataskVo>> queryTask(@RequestBody TaskQueryVo vo) {
        return ResponseVo.ok(service.query(vo.getTaskcode(), vo.getTaskcname(),vo.getParentcode(),vo.getValidflag()));
    }

    @ApiOperation(value = "查询所有功能代码(供下拉列表使用)")
    @PostMapping("/queryAllTaskCode")
    public ResponseVo<List<String>> queryAllTaskCode() {
        return ResponseVo.ok(service.queryAllTaskCode());
    }

    @ApiOperation(value = "根据父级代码查询父级功能名称")
    @PostMapping("/queryParentNameByParentCode")
    public ResponseVo<String> queryParentNameByParentCode(@ApiParam("父级代码") @RequestParam("parentcode") String parentcode) {
        return ResponseVo.ok(service.queryParentNameByParentCode(parentcode));
    }

    @ApiOperation(value = "查询待修改功能")
    @PostMapping("/queryModifyTask")
    public ResponseVo<TaskQueryVo> queryModifyTask(@ApiParam("主键ID") @RequestParam("id") Long id) {
        return ResponseVo.ok(service.queryModifyTask(id));
    }

    @ApiOperation(value = "修改功能名称及有效状态")
    @PostMapping("/modifyTask")
    public ResponseVo<Void> modifyTask(@RequestBody TaskQueryVo vo) {
        service.modifyTask(vo);
        return ResponseVo.ok();
    }
}
