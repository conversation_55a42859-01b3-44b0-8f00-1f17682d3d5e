package ins.channel.power.service;

import com.sinosoft.power.util.PowerConstant;
import ins.channel.company.dao.GgcompanyDao;
import ins.channel.company.po.Ggcompany;
import ins.framework.exception.BusinessException;
import ins.channel.paycomcodedefine.dao.GppaycomcodedefineDao;
import ins.channel.paycomcodedefine.po.Gppaycomcodedefine;
import ins.channel.power.dao.*;
import ins.channel.power.po.*;
import ins.channel.power.vo.*;
import ins.channel.user.dao.GguserDao;
import ins.channel.user.po.Gguser;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.SessionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * Created by xf on 2019/6/3.
 */
@Service
@Slf4j
public class PowerService {

    @Autowired
    private SaauserpaymentcompanyDao userpaymentcompanyDao;

    @Autowired
    private SaausergradeDao saausergradeDao;

    @Autowired
    private SaaGradeDao saaGradeDao;

    @Autowired
    private GguserDao gguserDao;

    @Autowired
    private GgcompanyDao ggcompanyDao;

    @Autowired
    private SaauserpermitdataDao saauserpermitdataDao;

    @Autowired
    private GppaycomcodedefineDao gppaycomcodedefineDao;

    /*
     * 查询用户角色的操作机构信息
     * @param userCode
     * @return
     */
   /* @Transactional(readOnly = true)
    public List<GgcompanyVo> queryUserGradeOrgInfo(String userCode, Long gradeId) {
        List<Ggcompany> companyList = userpermitdataDao.queryUserGradeOrgInfo(userCode,gradeId);
        return BeanCopyUtils.cloneList(companyList, GgcompanyVo.class);
    }*/

    /**
     * 根据用户信息查询收付机构
     * @param userCode   用户编号
     * @param defaultInd 是否查询有效收付机构
     * @param validInd   是否查询有效机构
     * @return
     */
    public List<SaauserpaymentcompanyVo> queryPaymentCompanyByUserCode(String userCode, boolean defaultInd,
                                                                       boolean validInd) {
        Saauserpaymentcompany saauserpaymentcompany = new Saauserpaymentcompany();
        saauserpaymentcompany.setUserCode(userCode);
        saauserpaymentcompany.setValidInd(validInd ? "1" : "0");
        saauserpaymentcompany.setDefaultInd(defaultInd ? "1" : "0");
        List<Saauserpaymentcompany> companyList = userpaymentcompanyDao.queryByUserCode(saauserpaymentcompany);
        return BeanCopyUtils.cloneList(companyList, SaauserpaymentcompanyVo.class);
    }

    /**
     * 根据用户信息查询收付机构
     * @param userCode   用户编号
     * @param validInd   是否查询有效机构
     * @return
     */
    public List<SaauserpaymentcompanyVo> queryAllPaymentCompanyByUserCode(String userCode, boolean validInd) {
        Saauserpaymentcompany saauserpaymentcompany = new Saauserpaymentcompany();
        saauserpaymentcompany.setUserCode(userCode);
        saauserpaymentcompany.setValidInd(validInd ? "1" : "0");
        List<Saauserpaymentcompany> companyList = userpaymentcompanyDao.queryByUserCode(saauserpaymentcompany);
        return BeanCopyUtils.cloneList(companyList, SaauserpaymentcompanyVo.class);
    }

    /**
     * 根据用户信息查询收付机构
     * @param userCode   用户编号
     * @param defaultInd 是否查询有效收付机构
     * @return
     */
    public List<SaauserpaymentcompanyVo> queryPaymentCompanyByUserCode(String userCode, boolean defaultInd) {
        Saauserpaymentcompany saauserpaymentcompany = new Saauserpaymentcompany();
        saauserpaymentcompany.setUserCode(userCode);
        return queryPaymentCompanyByUserCode(userCode, defaultInd, true);
    }

    /**
     * 根据用户信息查询收付机构
     * @param userCode   用户编号
     * @param defaultInd 是否查询有效收付机构
     * @return
     */
    public List<String> queryDefaultPaymentCompanyByUserCode(String userCode, boolean defaultInd) {
        Saauserpaymentcompany saauserpaymentcompany = new Saauserpaymentcompany();
        saauserpaymentcompany.setUserCode(userCode);
        List<SaauserpaymentcompanyVo> paymentCompanyList = queryPaymentCompanyByUserCode(userCode, defaultInd, true);
        List<String> paymentComList = new ArrayList<String>();
        for (SaauserpaymentcompanyVo saauserpaymentcompanyVo : paymentCompanyList) {
            paymentComList.add(saauserpaymentcompanyVo.getPaymentComcode());
        }
        return paymentComList;
    }

    /**
     * 根据用户信息查询收付机构
     * @param userCode 用户编号
     * @return
     */
    public List<SaauserpaymentcompanyVo> queryPaymentCompanyByUserCode(String userCode) {
        Saauserpaymentcompany saauserpaymentcompany = new Saauserpaymentcompany();
        saauserpaymentcompany.setUserCode(userCode);
        return queryAllPaymentCompanyByUserCode(userCode, true);
    }

    /**
     * 查询选择用户角色详细信息
     * @param userCode
     * @param userCname
     * @return
     */
    @Transactional(readOnly = true)
    public GguserInfoVo queryUserInfo(String userCode, String userCname,String userInd) {
        Assert.notNull(userCode, "参数不能为空");
        Assert.hasText(userCname, "参数不能为空");
        GguserInfoVo gguserInfoVo = new GguserInfoVo();
        gguserInfoVo.setUserCode(userCode);
        gguserInfoVo.setUserCname(userCname);
        if("1".equals(userInd)) userInd = null;
        gguserInfoVo.setUsergradelist(saaGradeDao.queryUserInfo(userCode,userInd));
        return gguserInfoVo;
    }

    /**
     * 查询选择用户操作机构详细信息
     * @param userCode
     * @param userCname
     * @return
     */
    @Transactional(readOnly = true)
    public SaauserpermitdataInfoVo queryuserpermitdataInfo(String userCode, String userCname) {
        Assert.notNull(userCode, "参数不能为空");
        Assert.hasText(userCname, "参数不能为空");
        SaauserpermitdataInfoVo saauserpermitdataInfoVo = new SaauserpermitdataInfoVo();
        saauserpermitdataInfoVo.setUserCode(userCode);
        saauserpermitdataInfoVo.setUserCname(userCname);
        saauserpermitdataInfoVo.setUserpermitdatalist(ggcompanyDao.queryuserpermitdataInfo(userCode));
        return saauserpermitdataInfoVo;
    }

    /**
     * 更新选择用户角色
     * @param vo
     */
    @Transactional
    public void updateUserGradeInfo(GguserInfoVo vo) {
        Assert.notNull(vo.getUserCode(), "usercode不能为空");
        Gguser gguser = BeanCopyUtils.clone(vo, Gguser.class);
        Gguser checkGguser = gguserDao.selectByPrimaryKey(gguser.getUserCode());
        if (checkGguser == null) {
            throw new BusinessException("用户不存在");
        }

        //页面获取的角色ID集合
        Set<Long> pageGradeIdSet = new HashSet<>(vo.getGradeid());

        //数据库保存的角色ID集合
        List<Long> gradeIdList = saausergradeDao.queryGradeIdListByUserCode(vo.getUserCode());
        Set<Long> loadGradeIdSet = new HashSet<>(gradeIdList);

        //页面增加的角色集合
        Set<Long> addSet = new HashSet<>(pageGradeIdSet);
        addSet.removeAll(loadGradeIdSet);
        if (!CollectionUtils.isEmpty(addSet)) {
            List<SaaGrade> addSaaGradeList = saaGradeDao.querySaaGradeListBySet(addSet);
            for (SaaGrade saaGrade : addSaaGradeList) {
                Saausergrade saausergrade = saausergradeDao.queryUserGradeByUserCodeAndGradeId(vo.getUserCode(), saaGrade.getId());
                if (saausergrade == null) {
                    saausergrade = new Saausergrade();
                    saausergrade.setUserCode(vo.getUserCode());
                    saausergrade.setGradeid(saaGrade.getId());
                    saausergrade.setCreatorcode(SessionHelper.getLoginUser().getUserCode());
                    saausergrade.setUpdaterCode(SessionHelper.getLoginUser().getUserCode());
                    saausergrade.setValidStatus("1");
                    Long id = 1L;
                    Long maxId = saausergradeDao.selectMaxId();
                    if (maxId != null) {
                        id=maxId+1L;
                    }
                    saausergrade.setId(id);
                    saausergradeDao.insertSelective(saausergrade);

                } else {
                    Map<String, Object> map = new HashMap<>();
                    map.put("userCode", vo.getUserCode());
                    map.put("gradeId", saaGrade.getId());
                    saausergradeDao.validSingleGradeTask(map);
                }
            }
        }

        //页面减少的权限集合
        Set<Long> plusSet = new HashSet<>(loadGradeIdSet);
        plusSet.removeAll(pageGradeIdSet);
        if (!CollectionUtils.isEmpty(plusSet)) {
            //将用户角色置为无效
            Map<String, Object> map = new HashMap<>();
            map.put("userCode", vo.getUserCode());
            map.put("gradeIdSet", plusSet);
            saausergradeDao.invalidGradeTask(map);
        }
    }

    /**
     * 更新选择用户的操作机构
     * @param vo
     */
    @Transactional
    public void updateUserGradeMakeCom(SaauserpermitdataInfoVo vo) {
        Assert.notNull(vo.getUserCode(), "usercode不能为空");
        Gguser gguser = BeanCopyUtils.clone(vo, Gguser.class);
        Gguser checkGguser = gguserDao.selectByPrimaryKey(gguser.getUserCode());
        if (checkGguser == null) {
            throw new BusinessException("用户不存在");
        }

        //页面获取的操作机构机构代码集合
        Set<String> pageComCodeSet = new HashSet<>(vo.getComCodeList());

        //数据库保存的机构代码集合
        List<String> comCodeList = saauserpermitdataDao.queryComcodeListByUserCode(vo.getUserCode());
        Set<String> loadComcodeSet = new HashSet<>(comCodeList);

        //页面增加的操作机构集合
        Set<String> addSet = new HashSet<>(pageComCodeSet);
        addSet.removeAll(loadComcodeSet);
        if (!CollectionUtils.isEmpty(addSet)) {
            List<Ggcompany> addGgcompanyList = ggcompanyDao.queryGgcompanyListByCodeSet(addSet);
            for (Ggcompany ggcompany : addGgcompanyList) {
                Saauserpermitdata saauserpermitdata = saauserpermitdataDao.queryUserPermitdataByUserCodeAndComCode(vo.getUserCode(), ggcompany.getCompanyCode());
                if (saauserpermitdata == null) {
                    saauserpermitdata = new Saauserpermitdata();
                    saauserpermitdata.setUserCode(vo.getUserCode());
                    //TODO 用户归属机构默认存01
                    saauserpermitdata.setComCode("01");
                    saauserpermitdata.setDatavalue1(ggcompany.getCompanyCode());
                    saauserpermitdata.setDatavalue2(ggcompany.getCompanyCode());
                    saauserpermitdata.setCreatorcode(SessionHelper.getLoginUser().getUserCode());
                    saauserpermitdata.setUpdateCode(SessionHelper.getLoginUser().getUserCode());
                    saauserpermitdata.setValidflag("1");
                    saauserpermitdata.setDataoper("like");
                    saauserpermitdata.setSystemCode(PowerConstant.SYSTEMCODE);
                    saauserpermitdata.setCreateTime(new Date());
                    saauserpermitdata.setUpdatetime(new Date());
                    saauserpermitdata.setFactorCode(PowerConstant.FACTOR_COM);
                    Long id = 1L;
                    Long maxId = saauserpermitdataDao.selectMaxId();
                    if (maxId != null) {
                        id = maxId +1L;
                    }
                    saauserpermitdata.setId(id);
                    saauserpermitdataDao.insertSelective(saauserpermitdata);

                } else {
                    Map<String, Object> map = new HashMap<>();
                    map.put("userCode", vo.getUserCode());
                    map.put("comCode", ggcompany.getCompanyCode());
                    saauserpermitdataDao.validSingleUserpermitdata(map);
                }
            }
        }

        //页面减少的操作机构集合
        Set<String> plusSet = new HashSet<>(loadComcodeSet);
        plusSet.removeAll(pageComCodeSet);
        if (!CollectionUtils.isEmpty(plusSet)) {
            //将操作机构置为无效
            Map<String, Object> map = new HashMap<>();
            map.put("userCode", vo.getUserCode());
            map.put("comCodeSet", plusSet);
            saauserpermitdataDao.invalidvalidSingleUserpermitdata(map);
        }
    }

    /**
     * 查询选择用户收付机构详细信息
     * @param userCode
     * @param userCname
     * @return
     */
    @Transactional(readOnly = true)
    public SaauserpaymentcompanyInfoVo queryuserpaymentcompanyInfo(String userCode, String userCname) {
        Assert.notNull(userCode, "参数不能为空");
        Assert.hasText(userCname, "参数不能为空");
        SaauserpaymentcompanyInfoVo saauserpaymentcompanyInfoVo = new SaauserpaymentcompanyInfoVo();
        saauserpaymentcompanyInfoVo.setUserCode(userCode);
        saauserpaymentcompanyInfoVo.setUserCname(userCname);
        saauserpaymentcompanyInfoVo.setUserpaymentcompanyList(gppaycomcodedefineDao.queryuserpaymentcompanyInfo(userCode));
        return saauserpaymentcompanyInfoVo;
    }

    /**
     * 更新选择用户的收付机构
     * @param vo
     */
    @Transactional
    public void updateUserPaymentCom(SaauserpaymentcompanyInfoVo vo) {
        Assert.notNull(vo.getUserCode(), "usercode不能为空");
        Gguser gguser = BeanCopyUtils.clone(vo, Gguser.class);
        Gguser checkGguser = gguserDao.selectByPrimaryKey(gguser.getUserCode());
        if (checkGguser == null) {
            throw new BusinessException("用户不存在");
        }

        //页面获取的待更新收付机构集合
        List<UserpaymentcompanyVo> updatePaymentComList = vo.getUpdatePaymentComList();
        //页面传来的默认收付机构
        UserpaymentcompanyVo pageDefaultPaymentCom = new UserpaymentcompanyVo();
        for (UserpaymentcompanyVo vo1 : updatePaymentComList) {
            if ("1".equals(vo1.getDefaultInd())) {
                BeanCopyUtils.copy(vo1, pageDefaultPaymentCom);
            }
        }

        //待更新的机构代码集合
        Set<String> pagePaymentComcodeSet = new HashSet<>();
        for (UserpaymentcompanyVo vo1 : updatePaymentComList) {
            pagePaymentComcodeSet.add(vo1.getPaymentComcode());
        }

        //数据库保存的有效机构代码集合
        List<String> paymentComcodeList = userpaymentcompanyDao.queryPaymentComcodeListByUserCode(vo.getUserCode());
        Set<String> loadPaymentComcodeSet = new HashSet<>(paymentComcodeList);

        //页面增加的机构代码集合
        Set<String> addSet = new HashSet<>(pagePaymentComcodeSet);
        addSet.removeAll(loadPaymentComcodeSet);
        //初始化默认、有效标志字段值
        String defaultInd = "0";
        String validInd = "0";
        if (!CollectionUtils.isEmpty(addSet)) {
            //根据机构代码集合查询GPPAYCOMCODEDEFINE表
            List<Gppaycomcodedefine> gppaymentComList = gppaycomcodedefineDao.queryPaymentComListByCodeSet(addSet);
            for (Gppaycomcodedefine gppaycomcodedefine : gppaymentComList) {
                Saauserpaymentcompany saauserpaymentcompany = userpaymentcompanyDao.queryByUserCodeAndPaymentComcode(vo.getUserCode(), gppaycomcodedefine.getPaymentComcode());
                if (saauserpaymentcompany == null) { //如果记录不存在,需要在中间表中新增
                    saauserpaymentcompany = new Saauserpaymentcompany();
                    saauserpaymentcompany.setUserCode(vo.getUserCode());
                    saauserpaymentcompany.setUserCname(vo.getUserCname());
                    //TODO 用户归属机构默认存01
                    saauserpaymentcompany.setComCode("01");
                    saauserpaymentcompany.setPaymentComcode(gppaycomcodedefine.getPaymentComcode());
                    saauserpaymentcompany.setPaymentCompanyname(gppaycomcodedefine.getOfcenterCname());
                    saauserpaymentcompany.setCreateCode(SessionHelper.getLoginUser().getUserCode());
                    saauserpaymentcompany.setCreateTime(new Date());
                    saauserpaymentcompany.setModifiedTime(new Date());
                    saauserpaymentcompany.setValidInd("1");
                    //如果页面选中的同时设为默认,需要去数据库中修改原来的默认机构
                    if (saauserpaymentcompany.getPaymentComcode().equals(pageDefaultPaymentCom.getPaymentComcode())) {
                        //修改数据库中原默认机构
                        userpaymentcompanyDao.updateOldDefaultCom(vo.getUserCode());
                        saauserpaymentcompany.setDefaultInd("1");
                    } else {
                        saauserpaymentcompany.setDefaultInd("0");
                    }
                    Long id = 1L;
                    Long maxId = userpaymentcompanyDao.selectMaxId();
                    if (maxId != null) {
                        id = maxId + 1L;
                    }
                    saauserpaymentcompany.setId(id.toString());
                    userpaymentcompanyDao.insertSelective(saauserpaymentcompany);
                } else { //如果记录已存在中间表中,需要更新状态
                    validInd = "1";
                    //如果页面选中的时候设为默认,需要去数据库中修改原来的默认机构
                    if (saauserpaymentcompany.getPaymentComcode().equals(pageDefaultPaymentCom.getPaymentComcode())) {
                        //修改数据库中原默认机构
                        userpaymentcompanyDao.updateOldDefaultCom(vo.getUserCode());
                        defaultInd = "1";
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put("userCode", vo.getUserCode());
                    map.put("paymentComcode", gppaycomcodedefine.getPaymentComcode());
                    map.put("defaultInd", defaultInd);
                    map.put("validInd", validInd);
                    userpaymentcompanyDao.updatePaymentCom(map);
                }
            }
        }
        //页面减少的机构代码集合
        Set<String> plusSet = new HashSet<>(loadPaymentComcodeSet);
        plusSet.removeAll(pagePaymentComcodeSet);
        if (!CollectionUtils.isEmpty(plusSet)) {
            List<Saauserpaymentcompany> plusUserpaymentComList = userpaymentcompanyDao.queryuserpaymentComListBySet(plusSet,vo.getUserCode());
            for (Saauserpaymentcompany plusUserpaymentCom : plusUserpaymentComList) {
                //修改数据库中原默认机构
                userpaymentcompanyDao.updateOldDefaultCom(vo.getUserCode());
                //将要减少的机构全部设置为无效、非默认
                Map<String, Object> map = new HashMap<>();
                defaultInd = "0";
                validInd = "0";
                map.put("userCode", vo.getUserCode());
                map.put("paymentComcode", plusUserpaymentCom.getPaymentComcode());
                map.put("defaultInd", defaultInd);
                map.put("validInd", validInd);
                userpaymentcompanyDao.updatePaymentCom(map);
            }
        }

        //如果只修改了默认机构,需要将原数据库中的默认置为无效,然后设置页面传来的默认机构
        userpaymentcompanyDao.updateOldDefaultCom(vo.getUserCode());
        //再更新页面传来的默认机构
        defaultInd = "1";
        validInd = "1";
        Map<String, Object> map = new HashMap<>();
        map.put("userCode", vo.getUserCode());
        map.put("paymentComcode", pageDefaultPaymentCom.getPaymentComcode());
        map.put("defaultInd", defaultInd);
        map.put("validInd", validInd);
        userpaymentcompanyDao.updatePaymentCom(map);

    }

    /**
     * 查询用户角色的操作机构信息(根据登录用户信息查询)
     * @param userCode
     * @return
     */
    public List<UserpermitdataVo> queryGgCompanyByLoginUser(String userCode) {
        return saauserpermitdataDao.queryGgCompanyByLoginUser(userCode);
    }

    /**
     * 根据机构编码查询操作员
     * @param companyCode
     * @return
     */
    public List<Gguser> searchUserInfoListByCompanyCoude(String companyCode) {
        return gguserDao.searchUserListByCompanyCode(companyCode);
    }


}

