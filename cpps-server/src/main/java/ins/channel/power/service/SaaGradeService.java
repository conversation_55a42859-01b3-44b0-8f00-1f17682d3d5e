package ins.channel.power.service;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import ins.channel.power.vo.GradeQueryPageVo;
import ins.channel.power.vo.GradeQueryVo;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.platform.common.PageResult;
import ins.platform.utils.PageHelper;
import ins.platform.utils.SessionHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import ins.framework.exception.BusinessException;
import ins.channel.power.dao.SaaGradeDao;
import ins.channel.power.dao.SaaTaskDao;
import ins.channel.power.dao.SaaUserGradeTaskDao;
import ins.channel.power.dao.SaagradetaskDao;
import ins.channel.power.dao.SaausergradeDao;
import ins.channel.power.po.SaaGrade;
import ins.channel.power.po.SaaTask;
import ins.channel.power.po.Saagradetask;
import ins.channel.power.po.Saausergrade;
import ins.channel.power.vo.SaagradeInfoVo;
import ins.channel.power.vo.SaagradeVo;
import ins.platform.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by xf on 2019/6/3.
 */
@Service
@Slf4j
public class SaaGradeService {

    @Autowired
    private SaaGradeDao saaGradeDao;

    @Autowired
    private SaaTaskDao saaTaskDao;

    @Autowired
    private SaagradetaskDao saaGradeTaskDao;

    @Autowired
    private SaaUserGradeTaskDao saaUserGradeTaskDao;

    @Autowired
    private SaausergradeDao saaUserGradeDao;

    /**
     * 查询角色列表
     *
     * @param id
     * @param gradeCName
     * @return
     */
    @Transactional(readOnly = true)
    public List<SaagradeVo> query(Long id, String gradeCName, String validStatus, String userInd) {
        //Modify By ZhouTaoyu  Reason:不使用Example对象   Time:2019/09/20
        SaaGrade saaGrade = new SaaGrade();
        //Modify By ZhouTaoyu  Reason:查询所有角色   Time:2019/10/05
//        saaGrade.setValidstatus("1");
        if (id == null && StringUtils.isBlank(gradeCName) && StringUtils.isBlank(validStatus)) {
            List<SaaGrade> list = saaGradeDao.selectAll();
            return BeanCopyUtils.cloneList(list, SaagradeVo.class);
        }
        if (id != null) {
            saaGrade.setId(id);
        }
        if (StringUtils.isNotBlank(gradeCName)) {
            saaGrade.setGradecname(gradeCName);
        }
        //modify By Zhoutaoyu 新增前端查询条件(validStatus) 2019/10/04
        if (StringUtils.isNotBlank(validStatus)) {
            saaGrade.setValidstatus(validStatus);
        }
        saaGrade.setUserInd(userInd);
        List<SaaGrade> list = saaGradeDao.selectByCondition(saaGrade);
//        List<SaagradeVo> saagradeVoList = BeanCopyUtils.cloneList(list, SaagradeVo.class);
        return BeanCopyUtils.cloneList(list, SaagradeVo.class);
    }

    /**
     * 分页模糊查询角色列表
     *
     * @param vo
     * @return
     */
    @Transactional(readOnly = true)
    public PageResult queryPage(GradeQueryPageVo vo) {
        PageParam pageParam = PageHelper.getPageParam(vo);
        SaaGrade saaGrade = BeanCopyUtils.clone(vo, SaaGrade.class);
        Page<SaaGrade> results = saaGradeDao.searchPage(pageParam, saaGrade);
        return PageHelper.convert(pageParam, results, SaagradeVo.class);

    }


    /**
     * 查询登录角色的本级及下级  传角色id就行了
     * @param id
     * @return
     */
    /*@Transactional(readOnly = true)
    public  List<SaagradeVo> querySubGrade(Long id) {
        Assert.notNull(id, "角色id不能为空");
        String gradeLevel = id.toString().substring(0, 1);
        List<SaaGrade> list = saaGradeDao.querySubGrade(gradeLevel);
        return BeanCopyUtils.cloneList(list, SaagradeVo.class);
    }*/

    /**
     * 查询选择角色功能详细信息
     *
     * @param id
     * @param gradecname
     * @return
     */
    @Transactional(readOnly = true)
    public SaagradeInfoVo queryGradeInfo(Long id, String gradecname, String userInd) {
        Assert.notNull(id, "参数不能为空");
        Assert.hasText(gradecname, "参数不能为空");
        SaagradeInfoVo saagradeInfoVo = new SaagradeInfoVo();
        saagradeInfoVo.setId(id);
        saagradeInfoVo.setGradecname(gradecname);
        saagradeInfoVo.setUserInd(userInd);
        saagradeInfoVo.setGradetasklist(saaGradeDao.queryGradeInfo(id));
        //Add By Zhoutaoyu Reason:返回userCode给前端页面
        saagradeInfoVo.setUsercode(SessionHelper.getLoginUser().getUserCode());
        return saagradeInfoVo;
    }

    /**
     * 添加角色
     *
     * @param gradeCName
     */
    @Transactional
    public void addGrade(SaagradeVo saagradeVo) {
        Assert.hasText(saagradeVo.getGradecname(), "参数不能为空");
        //判断是否有该角色
        /*Example example = new Example(SaaGrade.class);
        example.and().andEqualTo("gradecname", gradeCName);
        example.and().andEqualTo("validstatus", "1");
        SaaGrade haveGrade = saaGradeDao.selectOneByExample(example);
        if (haveGrade != null) {
            throw new BusinessException("角色已存在");
        }*/

        //Modify By ZhouTaoyu  Reason:不使用Example对象   Time:2019/09/20
        SaaGrade saaGrade = new SaaGrade();
        saaGrade.setGradecname(saagradeVo.getGradecname());
        saaGrade.setValidstatus("1");
        List<SaaGrade> saaGradeList = saaGradeDao.selectByCondition(saaGrade);
        if (!CollectionUtils.isEmpty(saaGradeList)) {
            throw new BusinessException("角色已存在");
        }
        saaGrade.setValidstatus(null);
        List<SaaGrade> oldGradeList = saaGradeDao.selectByCondition(saaGrade);
        //如果不存在则新增角色
        if (CollectionUtils.isEmpty(oldGradeList)) {
            //modify By Zhoutaoyu 角色级别默认为总公司('1'),前端不展示 2019/10/04
//            Long id = Long.parseLong(gradeLevel + "001");
            //Long maxId = saaGradeDao.selectMaxId(gradeLevel);
            Long id = 1001L;
            Long maxId = saaGradeDao.selectMaxId();
            if (maxId != null) {
                id = maxId + 1L;
            }
            saaGrade.setId(id);
            saaGrade.setValidstatus("1");
            saaGrade.setCreatorcode(SessionHelper.getLoginUser().getUserCode());
            saaGrade.setUpdatercode(SessionHelper.getLoginUser().getUserCode());
            saaGrade.setUserInd(saagradeVo.getUserInd());
            saaGradeDao.insertSelective(saaGrade);
        } else {  // 否则修改该角色validStatus字段值='1'
            SaaGrade oldSaaGrade = oldGradeList.get(0);
            oldSaaGrade.setValidstatus("1");
            oldSaaGrade.setOperatetimeforhis(new Date());
            oldSaaGrade.setUserInd(saagradeVo.getUserInd());
            saaGradeDao.updateSelectiveByPrimaryKey(oldSaaGrade);
        }

        //记录日志
        /*TSaagradetrace tSaagradetrace = new TSaagradetrace();
        tSaagradetrace.setGradeid(id);
        tSaagradetrace.setGradecname(gradeCName);
        tSaagradetrace.setOperateType(SaaGradeTraceConstants.INSERT);
        tSaagradetrace.setUserCode(userCode);
        tSaagradetraceDao.insertSelective(tSaagradetrace);*/
    }

    /**
     * 修改角色名称及有效状态
     *
     * @param vo
     */
    @Transactional
    public void modifyGrade(GradeQueryVo vo) {
        Assert.notNull(vo.getId(), "参数不能为空");
        //Modify By ZhouTaoyu  Reason:若未将角色分配给用户,直接查询SaaUserGrade中间表会报错,修改为查询SaaGrade表 Time:2019/09/20
        SaaGrade saaGrade = new SaaGrade();
        saaGrade.setId(vo.getId());
        List<SaaGrade> saaGradeList = saaGradeDao.selectByCondition(saaGrade);
        if (CollectionUtils.isEmpty(saaGradeList)) {
            throw new BusinessException("角色不存在");
        }

        //判断修改后角色名称是否已存在
        saaGrade.setGradecname(vo.getGradecname());
        if (saaGradeDao.selectNotDiff(saaGrade)!=0) {
            throw new BusinessException("角色名称已存在");
        }

        saaGrade.setId(vo.getId());
        saaGrade.setGradecname(vo.getGradecname());
        saaGrade.setUpdatercode(SessionHelper.getLoginUser().getUserCode());
        saaGrade.setValidstatus(vo.getValidstatus());
        saaGrade.setUserInd(vo.getUserInd());
        saaGradeDao.updateSelectiveByPrimaryKey(saaGrade);

        if ("0".equals(vo.getValidstatus())) {
            //Add By ZhouTaoyu  Reason:需要将SaaUserGrade记录也一并删去.(实际为修改validStatus字段值='0') Time:2019/09/20
            Saausergrade saaUserGrade = new Saausergrade();
            saaUserGrade.setGradeid(vo.getId());
            saaUserGrade.setUpdaterCode(SessionHelper.getLoginUser().getUserCode());
            saaUserGrade.setValidStatus(vo.getValidstatus());
            saaUserGrade.setOperatetimeforhis(new Date());
            saaUserGradeDao.updateSelectiveByGradeId(saaUserGrade);

            //Add By ZhouTaoyu  Reason:需要将SaaGradeTask记录也一并删去.(实际为修改validStatus字段值='0') Time:2019/10/06
            Saagradetask saagradetask = new Saagradetask();
            saagradetask.setGradeid(vo.getId());
            saagradetask.setUpdaterCode(SessionHelper.getLoginUser().getUserCode());
            saagradetask.setValidStatus(vo.getValidstatus());
            saagradetask.setOperatetimeforhis(new Date());
            saaGradeTaskDao.updateSelectiveByGradeId(saagradetask);
        }

        //记录日志
        /*TSaagradetrace tSaagradetrace = new TSaagradetrace();
        tSaagradetrace.setGradeid(id);
        tSaagradetrace.setOperateType(SaaGradeTraceConstants.DELETE);
        tSaagradetrace.setUserCode(userCode);
        tSaagradetraceDao.insertSelective(tSaagradetrace);*/
    }

    /**
     * 更新选择角色功能
     *
     * @param vo
     */
    @Transactional
    public void updateGradeInfo(SaagradeInfoVo vo) {
        Assert.notNull(vo.getId(), "id不能为空");

        //Modify By ZhouTaoyu  Reason:不使用Example对象   Time:2019/09/20
        //判断是否有该角色
        SaaGrade saaGrade = BeanCopyUtils.clone(vo, SaaGrade.class);
        SaaGrade checkGrade = saaGradeDao.selectByPrimaryKey(saaGrade.getId());
        if (checkGrade == null) {
            throw new BusinessException("角色不存在");
        }
        //Modify By Zhoutaoyu  不在更新角色功能配置中修改角色  2019/09/30
        /*saaGrade.setGradecname(vo.getGradecname());
        saaGrade.setUpdatercode(SessionHelper.getLoginUser().getUserCode());
        saaGrade.setOperatetimeforhis(new Date());
        saaGradeDao.updateSelectiveByPrimaryKey(saaGrade);*/

        //页面获取的task集合
        Set<Long> taskIdSet = new HashSet<>();
        for (Long tmp : vo.getTaskid()) {
            taskIdSet.add(tmp);
        }

        //页面获取角色taskCode集合
        Set<String> pageTaskCodeSet = saaTaskDao.queryTaskCodeSetByTaskIdSet(taskIdSet);
        //数据库保存角色taskCode集合
        List<String> gradeTaskCodeList = saaGradeTaskDao.querySaaTaskCodeListByGradeId(vo.getId());
        Set<String> loadTaskCodeSet = new HashSet<>(gradeTaskCodeList);

        //页面增加的权限集合
        Set<String> addSet = new HashSet<>(pageTaskCodeSet);
        addSet.removeAll(loadTaskCodeSet);
        if (!CollectionUtils.isEmpty(addSet)) {
            List<SaaTask> addSaaTaskList = saaTaskDao.querySaaTaskListByTaskCodeSet(addSet);
            for (SaaTask saaTask : addSaaTaskList) {
                Saagradetask saaGradeTask = saaGradeTaskDao.queryAllGradeTaskByGradeIdAndTaskId(vo.getId(), saaTask.getId());
                if (saaGradeTask == null) {
                    saaGradeTask = new Saagradetask();
                    saaGradeTask.setGradeid(vo.getId());
                    saaGradeTask.setTaskid(saaTask.getId().toString());
                    saaGradeTask.setCreatorcode(SessionHelper.getLoginUser().getUserCode());
                    saaGradeTask.setUpdaterCode(SessionHelper.getLoginUser().getUserCode());
                    saaGradeTask.setValidStatus("1");
                    // Modify By ZhouTaoyu      Reason:无序列,手动实现主键自增  time:2019/09/18
                    Long id = 1L;
                    Long maxId = saaGradeTaskDao.selectMaxId();
                    if (maxId != null) {
                        id = maxId + 1L;
                    }
                    saaGradeTask.setId(id);
                    saaGradeTask.setInserttimeforhis(new Date());
                    saaGradeTask.setOperatetimeforhis(new Date());
                    saaGradeTaskDao.insertSelective(saaGradeTask);
                } else {
                    // Modify By ZhouTaoyu      Reason:批量参数映射问题,需要用Map接收  time:2019/09/21
                    Map<String, Long> map = new HashMap<String, Long>();
                    map.put("gradeId", vo.getId());
                    map.put("taskId", saaTask.getId());
                    //saaGradeTaskDao.validSingleGradeTask(vo.getId(), saaTask.getId());
                    saaGradeTaskDao.validSingleGradeTask(map);
                }
            }
        }

        //页面减少的权限集合
        Set<String> plusSet = new HashSet<>(loadTaskCodeSet);
        plusSet.removeAll(pageTaskCodeSet);
        if (!CollectionUtils.isEmpty(plusSet)) {
            //将角色权限置位无效
            List<SaaTask> plusSaaTaskList = saaTaskDao.querySaaTaskListByTaskCodeSet(plusSet);
            Set<Long> plusTaskIdSet = new HashSet<>();
            for (SaaTask saaTask : plusSaaTaskList) {
                plusTaskIdSet.add(saaTask.getId());
            }
//            saaGradeTaskDao.invalidGradeTask(vo.getId(), plusTaskIdSet);
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("gradeId", vo.getId());
            map.put("taskIdSet", plusTaskIdSet);
            saaGradeTaskDao.invalidGradeTask(map);
        }

        //记录日志
        /*StringJoiner stringJoiner = new StringJoiner(",");
        for (Long long1 : taskIdSet) {
            stringJoiner.add(long1.toString());
        }
        TSaagradetrace tSaagradetrace = new TSaagradetrace();
        tSaagradetrace.setGradeid(vo.getId());
        tSaagradetrace.setOperateType(SaaGradeTraceConstants.UPDATE);
        tSaagradetrace.setGradecname(vo.getGradecname());
        tSaagradetrace.setTaskcode(stringJoiner.toString());
        tSaagradetrace.setUserCode(vo.getUserCode());
        tSaagradetraceDao.insertSelective(tSaagradetrace);*/
    }

}
