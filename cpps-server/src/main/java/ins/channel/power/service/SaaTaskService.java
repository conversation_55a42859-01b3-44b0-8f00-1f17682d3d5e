package ins.channel.power.service;

import ins.framework.exception.BusinessException;
import ins.channel.power.dao.*;
import ins.channel.power.po.SaaTask;
import ins.channel.power.po.Saagradetask;
import ins.channel.power.vo.TaskAddedVo;
import ins.channel.power.vo.TaskQueryVo;
import ins.channel.power.vo.SaataskVo;
import ins.channel.user.service.GguserService;
import ins.channel.user.vo.GguserVo;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.SessionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @apiNote 功能管理Service
 * @date 2019-10-04
 */
@Service
@Slf4j
public class SaaTaskService {

    @Autowired
    private SaaTaskDao saaTaskDao;
    @Autowired
    private SaagradetaskDao saagradetaskDao;
    @Autowired
    private GguserService gguserService;
    /**
     * 查询功能
     * @param taskCode
     * @param taskCName
     * @return
     */
    @Transactional(readOnly = true)
    public List<SaataskVo> query(String taskCode, String taskCName, String parentCode, String validflag) {
        SaaTask saaTask = new SaaTask();
        if (StringUtils.isBlank(taskCode) && StringUtils.isBlank(taskCName)  && StringUtils.isBlank(validflag)) {
            List<SaaTask> list = saaTaskDao.selectAll();
            return BeanCopyUtils.cloneList(list, SaataskVo.class);
        }
        if (StringUtils.isNotBlank(taskCode)) {
            saaTask.setTaskcode(taskCode);
        }
        if (StringUtils.isNotBlank(taskCName)) {
            saaTask.setTaskcname(taskCName);
        }
        if (StringUtils.isNotBlank(parentCode)) {
            saaTask.setParentcode(parentCode);
        }
        if (StringUtils.isNotBlank(validflag)) {
            saaTask.setValidflag(validflag);
        }
        List<SaaTask> list = saaTaskDao.selectByCondition(saaTask);
        return BeanCopyUtils.cloneList(list, SaataskVo.class);
    }

    /**
     *查询所有功能代码(供下拉列表使用)
     * @return
     */
    public List<String> queryAllTaskCode() {
        return saaTaskDao.selectAllTaskCode();
    }

    /**
     * 根据父级代码查询父级功能名称
     * @param parentcode
     * @return
     */
    public String queryParentNameByParentCode(String parentcode) {
        String parentName = saaTaskDao.selectParentNameByCode(parentcode);
        if (StringUtils.isBlank(parentName)) {
            throw new BusinessException("功能不存在!");
        }
        return parentName;
    }


    /**
     * 添加功能
     * @param vo
     */
    @Transactional
    public void addTask(TaskAddedVo vo) {
        Assert.notNull(vo.getTaskcode(), "功能代码不能为空!");
        Assert.notNull(vo.getTaskcname(), "功能名称不能为空!");
        Assert.notNull(vo.getParentcode(), "父级代码不能为空!");
        //判断是否有该功能
        SaaTask saaTask = new SaaTask();
        saaTask.setTaskcode(vo.getTaskcode());
        List<SaaTask> taskCodeList = saaTaskDao.selectByCondition(saaTask);
        if (!CollectionUtils.isEmpty(taskCodeList)) {
            throw new BusinessException("功能代码已存在!");
        }
        saaTask.setTaskcode(null);
        saaTask.setTaskcname(vo.getTaskcname());
        List<SaaTask> taskNameList = saaTaskDao.selectByCondition(saaTask);
        if (!CollectionUtils.isEmpty(taskNameList)) {
            throw new BusinessException("功能名称已存在!");
        }
        saaTask.setTaskcode(null);
        saaTask.setTaskcname(null);
        saaTask.setTaskNum(vo.getTaskNum());
        List<SaaTask> taskNumList = saaTaskDao.selectByCondition(saaTask);
        if (!CollectionUtils.isEmpty(taskNumList)) {
            throw new BusinessException("功能序号已存在!");
        }
        //添加功能
        SaaTask clone = BeanCopyUtils.clone(vo, SaaTask.class);
        Long id = 1L;
        Long maxId = saaTaskDao.selectMaxId();
        if (maxId != null) {
            id = maxId + 1L;
        }
        clone.setId(id);
        clone.setValidflag("1");
        clone.setCreatorcode(SessionHelper.getLoginUser().getUserCode());
        //TODO 系统暂时固定为"payment",url待设置
        clone.setSystem("payment");
        clone.setCreatetime(new Date());
        clone.setInserttimeforhis(new Date());
        clone.setOperatetimeforhis(new Date());
        saaTaskDao.insertSelective(clone);

        //记录日志
        /*TSaatasktrace tSaatasktrace = new TSaatasktrace();
        tSaatasktrace.setTaskid(id);
        tSaatasktrace.setTaskcname(taskCName);
        tSaatasktrace.setOperateType(SaaTaskTraceConstants.INSERT);
        tSaatasktrace.setUserCode(userCode);
        tSaatasktraceDao.insertSelective(tSaatasktrace);*/
    }

    /**
     * 查询待修改功能
     * @param id
     * @return
     */
    public TaskQueryVo queryModifyTask(Long id) {
        SaaTask saaTask = saaTaskDao.selectByPrimaryKey(id);
        TaskQueryVo clone = BeanCopyUtils.clone(saaTask, TaskQueryVo.class);
        GguserVo gguserVo = gguserService.queryOne(clone.getCreatorcode());
        clone.setUserCname(gguserVo.getUserCname());
        clone.setParentname(queryParentNameByParentCode(clone.getParentcode()));
        return clone;
    }

    /**
     * 修改功能名称及有效状态
     * @param vo
     */
    @Transactional
    public void modifyTask(TaskQueryVo vo) {
        Assert.notNull(vo.getTaskcode(), "功能代码不能为空!");
        Assert.notNull(vo.getTaskcname(), "功能名称不能为空!");
        Assert.notNull(vo.getParentcode(), "父级代码不能为空!");
        Assert.notNull(vo.getValidflag(), "有效状态不能为空!");
        SaaTask saaTask = new SaaTask();
        saaTask.setId(Long.parseLong(vo.getId()));
        List<SaaTask> saaTaskList = saaTaskDao.selectByCondition(saaTask);
        if (CollectionUtils.isEmpty(saaTaskList)) {
            throw new BusinessException("功能不存在!");
        }
        //判断修改后功能代码是否已存在
        saaTask.setTaskcode(vo.getTaskcode());
        if (saaTaskDao.selectNotDiff(saaTask)!=0) {
            throw new BusinessException("功能代码已存在!");
        }
        //判断修改后菜单顺序是否已存在
        saaTask.setTaskcode(null);
        saaTask.setTaskNum(vo.getTaskNum());
        if (saaTaskDao.selectNotDiff(saaTask)!=0) {
            throw new BusinessException("功能序号已存在!");
        }

        saaTask.setTaskcode(vo.getTaskcode());
        saaTask.setTaskcname(vo.getTaskcname());
        saaTask.setTaskename(vo.getTaskename());
        saaTask.setValidflag(vo.getValidflag());
        saaTask.setParentcode(vo.getParentcode());
        saaTaskDao.updateSelectiveByPrimaryKey(saaTask);

        if ("0".equals(vo.getValidflag())) {
        //如果功能修改为无效,将角色功能表中对应的记录置为无效
        Saagradetask saagradetask = new Saagradetask();
        saagradetask.setTaskid(vo.getId());
        saagradetask.setUpdaterCode(SessionHelper.getLoginUser().getUserCode());
        saagradetask.setValidStatus("0");
        saagradetaskDao.invalidGradeTaskByTaskId(saagradetask);
        }

        //记录日志
        /*TSaatasktrace tSaatasktrace = new TSaatasktrace();
        tSaatasktrace.setTaskid(id);
        tSaatasktrace.setOperateType(SaaTaskTraceConstants.DELETE);
        tSaatasktrace.setUserCode(userCode);
        tSaatasktraceDao.insertSelective(tSaatasktrace);*/
    }
}
