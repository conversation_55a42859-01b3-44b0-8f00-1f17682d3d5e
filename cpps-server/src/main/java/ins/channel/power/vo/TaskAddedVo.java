package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value = "新增功能信息")
@Data
@SuppressWarnings("serial")
public class TaskAddedVo implements Serializable {
    /**
     * 功能代码
     */
    @ApiModelProperty(value = "功能代码")
    private String taskcode;

    /**
     * 功能中文名称
     */
    @ApiModelProperty(value = "功能中文名称")
    private String taskcname;

    /**
     *功能英文名称
     */
    @ApiModelProperty(value = "功能英文名称")
    private String taskename;

    /**
     *父级代码
     */
    @ApiModelProperty(value = "父级代码")
    private String parentcode;
    /**
     *路径
     */
    @ApiModelProperty(value = "路径")
    private String url;

    /**
     *系统
     */
    @ApiModelProperty(value = "系统")
    private String system;

    /**
     * 对应字段：TASK_NUM,备注：菜单顺序
     */
    @ApiModelProperty(value = "功能序号")
    private BigDecimal taskNum;

    private static final long serialVersionUID = 1L;
}

