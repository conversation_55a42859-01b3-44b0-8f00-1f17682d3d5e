package ins.channel.power.vo;

import com.sinosoft.power.vo.SaaUserGradeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "用户角色保存")
@Data
@SuppressWarnings("serial")
public class UserGradeSaveVo implements Serializable {
    /**
     * 用户代码
     */
    @ApiModelProperty(value = "用户代码")
    private String usercode;

    /**
     * 角色集合
     */
    @ApiModelProperty(value = "角色集合  gradeId必传")
    private List<SaaUserGradeVo> saaUserGradeVoList;

    /**
     * 用户代码
     */
    @ApiModelProperty(value = "当前用户代码")
    private String currentUserCode;


    private static final long serialVersionUID = 1L;
}

