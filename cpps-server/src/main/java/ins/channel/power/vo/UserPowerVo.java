package ins.channel.power.vo;

import com.sinosoft.power.vo.SaaGradeVo;
import com.sinosoft.power.vo.SaaUserGradeVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserPowerVo implements Serializable{
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "用户代码")
	private String userCode;

	@ApiModelProperty(value = "角色代码")
	private Long gradeId;

	@ApiModelProperty(value = "邮箱")
	private String email;

	@ApiModelProperty(value = "用户角色集合")
	private List<SaaUserGradeVo> saaUserGradeVoList;

	@ApiModelProperty(value = "角色集合")
	private List<SaaGradeVo> saaGradeVoList;

	@ApiModelProperty(value = "当前用户代码")
	private String currentUserCode;

	@ApiModelProperty(value = "默认角色代码")
	private Long defaultGradeId;
	
}
