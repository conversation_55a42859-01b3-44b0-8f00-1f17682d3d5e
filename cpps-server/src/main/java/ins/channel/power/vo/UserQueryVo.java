package ins.channel.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "用户查询信息")
@Data
@SuppressWarnings("serial")
public class UserQueryVo implements Serializable {
    /**
     * 用户代码
     */
    @ApiModelProperty(value = "用户代码")
    private String userCode;

    /**
     * 岗位中文名称
     */
    @ApiModelProperty(value = "角色中文名称")
    private String userCname;



    private static final long serialVersionUID = 1L;
}

