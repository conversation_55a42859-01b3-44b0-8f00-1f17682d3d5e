package ins.channel.print.api;

import ins.channel.print.service.EndorPrintService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 电子批单打印接口
 * <AUTHOR>
 * @date 2021年03月03日 10:37:22
 */
@RestController
@RequestMapping("/api/endorPrint")
@Api(tags = "EndorPrint")
public class EndorPrintApi {
    @Autowired
    private EndorPrintService endorPrintService;

    /**
     * 根据传输json字符串调用打印接口,获取电子批单pdf
     * <AUTHOR>
     * @date 2021年03月02日 20:44:37
     */
    @ApiOperation(value = "组装数据调用服务平台批单打印接口")
    @PostMapping (value = "/getEndorPrintPdf")
    public String getEndorPrintPdf(@RequestBody String endorNoJson) {
        String resultUrl = endorPrintService.getEndorPrintPdf(endorNoJson);
        return resultUrl;
    }



    public static void main(String[] args) {
        
        
    }
    



}
