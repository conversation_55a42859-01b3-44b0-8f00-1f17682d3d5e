package ins.channel.print.api;

import ins.channel.print.service.PolicyVoucherService;
import ins.channel.print.vo.PolicyVoucherVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 申报查询界面,打印保险凭证
 * <AUTHOR>
 * @date 2021年02月20日 11:42:49
 */
@RestController
@RequestMapping("/api/policyVoucher")
@Api(tags = "PolicyVoucher")
public class PolicyVoucherApi {
    @Autowired
    private PolicyVoucherService policyVoucherService;

    /**
     * 根据传输json字符串调用打印接口,获取保险凭证pdf
     * <AUTHOR>
     * @date 2021年02月20日 11:45:04
     */
    @ApiOperation(value = "组装数据调用打印接口")
    @PostMapping (value = "/getPolicyVoucherPdf")
    public String getPolicyVoucherPdf(@RequestBody PolicyVoucherVo vo) {
        String resultUrl = policyVoucherService.getPolicyVoucherPdf(vo);
        return resultUrl;
    }
    
    public static void main(String[] args) {
    }



}
