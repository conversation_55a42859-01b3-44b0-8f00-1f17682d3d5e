package ins.channel.print.service;

import com.alibaba.fastjson.JSONObject;
import ins.channel.config.UrlConfig;
import ins.channel.print.vo.EndorPrintVo;
import ins.framework.utils.Uuids;
import ins.platform.utils.HttpClientUtils;
import ins.platform.utils.HttpResponse;

import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 电子批单打印服务层
 * <AUTHOR>
 * @date 2021年03月03日 09:09:00
 */
@Service
@Slf4j
@Transactional
public class EndorPrintService {
    @Autowired
    private UrlConfig urlConfig;

    /**
     * 调用服务平台获取电子批单下载地址
     * <AUTHOR>
     * @date 2021年03月03日 09:09:00
     */
    public String getEndorPrintPdf(String endorNo) {
        String businessNo = Uuids.shortUuid();
        //组装JSON数据
        String sendJson = bindingJson(endorNo);
        //将XML放入json进行传输
        //调用服务平台打印接口地址
        String callSPUrl = urlConfig.getCallSP4EndorNo();
        String reUrl = "服务请求失败,未获得PDF文件下载地址";
        log.info(String.format("%s-调用服务平台获取电子批单接口,推送报文:%s",businessNo,sendJson));
        //请求调用服务平台
        HttpResponse httpResponse = HttpClientUtils.postJson(callSPUrl,sendJson);
        String returnJson="{\"code\":%d,\"url\":\"%s\"}";
        String pdfUrl = "";
        if(httpResponse!=null){
            String spJson = httpResponse.getBodyAsString();
            if(spJson!=null&&!spJson.isEmpty()){
                EndorPrintVo endorPrintVo = JSONObject.parseObject(spJson,EndorPrintVo.class);
                if(endorPrintVo!=null){
                    if("0000".equals(endorPrintVo.getBody().getInsResults().get(0).getErrorCode())){
                        pdfUrl = endorPrintVo.getBody().getInsResults().get(0).getPolicyDownUrl();
                        returnJson = String.format(returnJson,0,pdfUrl);
                        log.info(String.format("%s-调用服务平台获取电子批单接口返回URL成功,报文:%s",businessNo,spJson));
                    }else{
                        log.error(String.format("%s-调用服务平台获取电子批单接口返回URL失败,报文:%s",businessNo,spJson));
                        returnJson = String.format(returnJson,1,endorPrintVo.getBody().getInsResults().get(0).getErrorMsg());
                    }
                }else{
                    log.error(String.format("%s-调用服务平台获取电子批单接口返回URL失败,报文:%s",businessNo,spJson));
                    returnJson = String.format(returnJson,1,"调用服务平台返回报文异常,JSON解析失败");
                }
            }else{
                log.error(String.format("%s-调用服务平台获取电子批单接口返回URL失败,报文:%s",businessNo,spJson));
                returnJson = String.format(returnJson,1,"调用服务平台返回报文为空");
            }
        }
        return returnJson;
    }
    
    /**
     * 组装提供服务平台生成电子批单的json报文
     * @Return 完整json报文
     * <AUTHOR>
     * @date 2021年03月03日 09:10:23
     */
    private static String bindingJson(String endorNoJson){
        JSONObject jsonObject = JSONObject.parseObject(endorNoJson);
        EndorPrintVo endorPrintVo = new EndorPrintVo();
        EndorPrintVo.Head head = new EndorPrintVo.Head();
        head.setRequestType("90");//请求类型定值
        endorPrintVo.setHead(head);
        EndorPrintVo.Body body = new EndorPrintVo.Body();
        //目前只实现了单批单号查询
        EndorPrintVo.InsResult insResult = new EndorPrintVo.InsResult();
        insResult.setEndorNo(jsonObject.get("endorNo").toString());//批单号码
        List<EndorPrintVo.InsResult> list = new ArrayList<>();
        list.add(insResult);
        body.setInsResults(list);
        endorPrintVo.setBody(body);
        return JSONObject.toJSON(endorPrintVo).toString();
    }







    public static void main(String[] args) {
        System.out.println(bindingJson("123412341234"));
    }

}

