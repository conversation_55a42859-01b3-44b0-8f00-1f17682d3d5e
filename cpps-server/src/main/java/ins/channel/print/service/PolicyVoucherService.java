package ins.channel.print.service;

import com.alibaba.fastjson.JSONObject;
import ins.channel.config.UrlConfig;
import ins.channel.gupolicymain.dao.GupolicymainDao;
import ins.channel.gupolicymain.vo.PolicyVoucherInfoVo;
import ins.channel.print.vo.PolicyVoucherVo;
import ins.channel.utils.LocalDateUtils;
import ins.channel.xmlbean.policyvoucher.INSUREQRET;
import ins.channel.xmlbean.policyvoucher.PolicyVoucherFactory;
import ins.framework.utils.Uuids;
import ins.platform.utils.HttpClientUtils;
import ins.platform.utils.HttpResponse;
import java.io.StringWriter;
import java.time.LocalDate;
import java.time.LocalDateTime;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.namespace.QName;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 申报查询界面,打印保险凭证服务层
 * <AUTHOR>
 * @date 2021年02月20日 11:42:49
 */
@Service
@Slf4j
@Transactional
public class PolicyVoucherService {
    @Autowired
    private UrlConfig urlConfig;
    @Autowired
    private GupolicymainDao policymainDao;

    /**
     * 查询保单信息,组装调用打印平台接口
     * @param vo 前端页面封装信息
     * @Return 打印平台返回的PDF文件URL
     * <AUTHOR>
     * @date 2021年02月20日 15:18:50
     */
    public String getPolicyVoucherPdf(PolicyVoucherVo vo) {
        PolicyVoucherInfoVo policyVoucherInfoVo = policymainDao.queryPolicyVoucherInfo(vo.getPolicyNo());
        if(policyVoucherInfoVo==null){
            String errorMsg = String.format("数据异常：保单号【%s】不存在，请重试或联系管理员！",vo.getPolicyNo());
            log.error(errorMsg);
            return errorMsg;
        }
        String businessNo = Uuids.shortUuid();
        //组装XML数据
        String sendXml = bindingXml(policyVoucherInfoVo,vo,businessNo);
        //将XML放入json进行传输
        JSONObject sendJO = new JSONObject();
        sendJO.put("businessNo",businessNo);
        sendJO.put("policyNo",policyVoucherInfoVo.getPolicyNo());
        sendJO.put("xmlData",sendXml);
        String sendJsonStr = sendJO.toJSONString();
        //调用服务平台打印接口地址
        String callSPUrl = urlConfig.getCallSP4PolicyVoucher();
        String reUrl = "服务请求失败,未获得PDF文件下载地址";
        log.info(String.format("%s-调用服务平台打印接口,推送报文:%s",businessNo,sendJsonStr));
        //请求调用服务平台
        HttpResponse httpResponse = HttpClientUtils.postJson(callSPUrl,sendJsonStr);
        String jsonStr="{\"code\":1,\"url\":\"调用服务平台HTTP服务错误\"}";
        if(httpResponse!=null){
            jsonStr = httpResponse.getBodyAsString();
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            Object reCode = jsonObject.get("code");
            if(reCode!=null&&"0".equals(reCode.toString())){//成功
                log.info(String.format("%s-调用服务平台打印接口返回URL成功,报文:%s",businessNo,jsonStr));
            }else{
                log.error(String.format("%s-调用服务平台打印接口返回URL失败,报文:%s",businessNo,jsonStr));
            }
        }
        return jsonStr;
    }
    
    /**
     * 组装提供打印平台生成保险凭证的XML报文
     * @param pvInfoVo 根据保单号查询的数据库数据
     * @param pvVo 前端传输的集合数据
     * @param businessNo 凭证号码
     * @Return 完整XML报文
     * <AUTHOR>
     * @date 2021年02月24日 15:13:39
     */
    private String bindingXml(PolicyVoucherInfoVo pvInfoVo,PolicyVoucherVo pvVo,String businessNo){
        PolicyVoucherFactory policyVoucherFactory = new PolicyVoucherFactory();
        INSUREQRET insureqret = policyVoucherFactory.createINSUREQRET();
        insureqret.setMAIN(policyVoucherFactory.createINSUREQRETMAIN());
        insureqret.getMAIN().setREQUESTTYPE("E01");//请求类型 P01纸质单证 E01 电子单证
        insureqret.getMAIN().setDOCUMENTSERIALNO(Uuids.longUuid());
        insureqret.getMAIN().setBUSINESSCODE("51");//业务类型
        //长度不能大于30,否则报错
        insureqret.getMAIN().setBUSINESSNO(businessNo);//凭证号码
        insureqret.getMAIN().setBUSINESSNOSEQ(businessNo);//凭证号码
        insureqret.getMAIN().setBUSINESSSOURCE(businessNo);
        insureqret.getMAIN().setPOLICYTYPE("PZ01");//保险凭证打印模板名称
        insureqret.getMAIN().setPRINTDATE(LocalDate.now().toString());
        insureqret.getMAIN().setEmailFlag("0");//发送邮件标识 0不发送1发送
        insureqret.getMAIN().setSMSFlag("0");//发送短信标识 0不发送1发送
        insureqret.getMAIN().setAccessory("0");
        insureqret.getMAIN().setHOLDERINFO(policyVoucherFactory.createINSUREQRETMAINHOLDERINFO());
        insureqret.getMAIN().getHOLDERINFO().setHolderEmail("");
        insureqret.getMAIN().getHOLDERINFO().setHolderMobile("");
        insureqret.getMAIN().setPRINTTYPE("Q");
        insureqret.getMAIN().setBusinessType("0001");
        insureqret.getMAIN().setSQLSAVE("FCDZBD.properties");//归档配置 定值
        insureqret.getMAIN().setImportFlag("2");
        insureqret.setMainDto(policyVoucherFactory.createINSUREQRETMainDto());
        insureqret.getMainDto().setCompanyCode(pvInfoVo.getInsurancecompanycode());//公司代码
        String ymd = LocalDateUtils.getCHYMD(LocalDateTime.now());
        insureqret.getMainDto().setStartDate(ymd);//当前签单时间
        insureqret.setPOLICYRISK(policyVoucherFactory.createINSUREQRETPOLICYRISK());
        insureqret.getPOLICYRISK().setRiskCode(pvInfoVo.getProductcode());//险种名称
        insureqret.setBaseInfo(policyVoucherFactory.createINSUREQRETBaseInfo());
        insureqret.getBaseInfo().setSIGNBRANCH(pvVo.getSignBranch());//承保保险公司名称
        insureqret.setPARAGRAPHINFO(policyVoucherFactory.createINSUREQRETPARAGRAPHINFO());
        insureqret.getPARAGRAPHINFO().setPOLICYNO(pvInfoVo.getPolicyNo());//保单号码
        insureqret.getPARAGRAPHINFO().setINSUREDCOMPANY(pvInfoVo.getInsuredCompany());//被保险公司
        insureqret.getPARAGRAPHINFO().setKINDNAME(pvInfoVo.getKindName());//险种名称
        insureqret.getPARAGRAPHINFO().setINSUREDNAME(pvInfoVo.getAppliName());//被保险人名称
        insureqret.getPARAGRAPHINFO().setINSUREDADDRESS(pvInfoVo.getInsuredAddress());//被保险人地址
        //获取年月日
        ymd = LocalDateUtils.getCHYMD(pvInfoVo.getStartDate(),LocalDateUtils.LOCAL_DATE_TIME_S);
        insureqret.getPARAGRAPHINFO().setSTARTDATE(ymd);//开始时间（年月日）
        insureqret.getPARAGRAPHINFO().setSTARTDATEHOUR("零");//开始时间（时）
        ymd = LocalDateUtils.getCHYMD(pvInfoVo.getEndDate(),LocalDateUtils.LOCAL_DATE_TIME_S);
        insureqret.getPARAGRAPHINFO().setENDDATE(ymd);//结束时间（年月日）
        insureqret.getPARAGRAPHINFO().setENDDATEHOUR("二十四");//结束时间（时）
        insureqret.setINSURERLIST(policyVoucherFactory.createINSUREQRETINSURERLIST());
        INSUREQRET.INSURERLIST.INSURER insurer;
        int seqNo = 1;
        for (PolicyVoucherVo.Insurer insurer1:pvVo.getInsurerList()){
            insurer = policyVoucherFactory.createINSUREQRETINSURERLISTINSURER();
            insurer.setSERIALNUMBER(seqNo+++"");//序号
            insurer.setINSUREDNAME(insurer1.getEmpName());//被保险人雇员姓名
            insurer.setIDENTIFYNUMBER(insurer1.getEmpIdentifyNumber());//证件号码
            insurer.setINSURANCEPLAN(insurer1.getPersoncasualtieslimit());//保险计划
            insurer.setSTARTDATE(insurer1.getEffectiveDate());//生效日期
            insurer.setENDDATE(insurer1.getEndDate());//到期日期
            insurer.setDECLARATIONDATE(insurer1.getAcceptDate());//申报日期
            insurer.setPMNAME(insurer1.getProject());//项目  2021-10-18改
            insureqret.getINSURERLIST().getINSURER().add(insurer);
        }
        String requestXML = "";
        try {
            JAXBContext jc = JAXBContext.newInstance(insureqret.getClass());
            Marshaller marshaller = jc.createMarshaller();
            JAXBElement je2 = new JAXBElement(new QName(insureqret.getClass()
                    .getSimpleName()), insureqret.getClass(),insureqret);
            StringWriter writer = new StringWriter();
            marshaller.marshal(je2, writer);
            requestXML = writer.toString().replaceAll("&lt;", "<")
                    .replaceAll("&gt;", ">").replaceAll("&amp;", "&");
        } catch (JAXBException e) {
            e.printStackTrace();
        }
        return requestXML;
    }

    public static void main(String[] args) {
    }

}

