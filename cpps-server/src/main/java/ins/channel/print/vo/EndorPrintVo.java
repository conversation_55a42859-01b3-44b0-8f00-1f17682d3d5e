package ins.channel.print.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 电子批单打印VO
 * <AUTHOR>
 * @date 2021年03月02日 20:46:09
 */
@Data
@ApiModel ("EndorPrintVo")
public class EndorPrintVo implements Serializable {
    private static final long serialVersionUID = 1L;
    
    
    @ApiModelProperty ("请求头")
    private Head head;
    @ApiModelProperty ("请求体")
    private Body body;

    @Data
    @ApiModel ("Head")
    public static class Head {
        @ApiModelProperty ("请求类型")
        private String requestType;
        @ApiModelProperty ("返回状态码")
        private String errorCode;
        @ApiModelProperty ("返回信息")
        private String errorMsg;
    }
    
    @Data
    @ApiModel ("Body")
    public static class Body {
        @ApiModelProperty ("数据体")
        private List<InsResult> insResults;
    }
    
    @Data
    @ApiModel ("InsResults")
    public static class InsResult {
        @ApiModelProperty ("批单号")
        private String endorNo;
        @ApiModelProperty ("返回状态码")
        private String errorCode;
        @ApiModelProperty ("返回信息")
        private String errorMsg;
        @ApiModelProperty ("电子单证下载地址")
        private String policyDownUrl;
        @ApiModelProperty ("保单号")
        private String policyNo;
    }

}
