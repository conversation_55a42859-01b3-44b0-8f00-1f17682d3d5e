package ins.channel.print.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 保险凭证打印VO
 * <AUTHOR>
 * @date 2021年03月02日 20:45:50
 */
@Data
@ApiModel ("PolicyVoucherVo")
public class PolicyVoucherVo implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty ("承保保险公司名称")
    private String signBranch="黄河财产保险股份有限公司";
    @ApiModelProperty ("保单号码")
    private String policyNo;

    @ApiModelProperty ("雇员信息集合")
    private List<Insurer> insurerList;
    
    @Data
    @ApiModel ("Insurer")
    public static class Insurer {
        @ApiModelProperty ("被保险人雇员姓名")
        private String empName;
        @ApiModelProperty ("证件号码")
        private String empIdentifyNumber;
        @ApiModelProperty ("保险计划")
        private String personcasualtieslimit;
        @ApiModelProperty ("生效日期")
        private String effectiveDate;
        @ApiModelProperty ("到期日期")
        private String endDate;
        @ApiModelProperty ("申报日期")
        private String acceptDate;
        @ApiModelProperty ("项目-关联机构")
        private String companyName;
        @ApiModelProperty ("项目")
        private String project;
    }

}
