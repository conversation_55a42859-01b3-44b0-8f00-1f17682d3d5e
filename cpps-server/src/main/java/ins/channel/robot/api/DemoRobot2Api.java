package ins.channel.robot.api;

import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import ins.framework.common.ResultPage;
import ins.framework.web.ApiResponse;
import ins.platform.common.CrudApi;
import ins.platform.common.CrudService;
import ins.channel.robot.service.DemoRobot2Service;
import ins.channel.robot.vo.DemoRobotMain2Vo;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/robot/demo2")
@Api(tags = "Demo-Robot2", description = "DEMO-服务示例2")
public class DemoRobot2Api implements CrudApi<DemoRobotMain2Vo, Long> {
	@Autowired
	private DemoRobot2Service demoRobot2Service;

	@Override
	public CrudService<DemoRobotMain2Vo, Long> getCrudService() {
		return demoRobot2Service;
	}

	@ApiOperation(value = "根据生产厂家查找对象（包含可能的子对象）")
	@PostMapping(value = "/_selectByManufactureName")
	public ApiResponse<ResultPage<DemoRobotMain2Vo>> selectByManufactureName(
			@RequestParam(value = "manufactureName") String manufactureName) {
		ResultPage<DemoRobotMain2Vo> result = demoRobot2Service.selectByManufactureName(manufactureName);
		return ApiResponse.ok(result);
	}
}
