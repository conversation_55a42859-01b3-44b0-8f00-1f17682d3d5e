package ins.channel.robot.api;

import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import ins.framework.common.ResultPage;
import ins.framework.web.ApiResponse;
import ins.platform.common.CrudApi;
import ins.platform.common.CrudService;
import ins.channel.robot.service.DemoRobotService;
import ins.channel.robot.vo.DemoRobotMainVo;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/robot/demo")
@Api(tags = "Demo-Robot", description = "DEMO-服务示例")
public class DemoRobotApi implements CrudApi<DemoRobotMainVo, Long> {
	@Autowired
	private DemoRobotService demoRobotService;

	@Override
	public CrudService<DemoRobotMainVo, Long> getCrudService() {
		return demoRobotService;
	}

	@ApiOperation(value = "根据生产厂家查找对象（包含可能的子对象）")
	@PostMapping(value = "/_selectByManufactureName")
	public ApiResponse<ResultPage<DemoRobotMainVo>> selectByManufactureName(
			@RequestParam(value = "manufactureName") String manufactureName) {
		ResultPage<DemoRobotMainVo> result = demoRobotService.selectByManufactureName(manufactureName);
		return ApiResponse.ok(result);
	}
}
