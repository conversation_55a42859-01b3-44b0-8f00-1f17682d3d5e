package ins.channel.robot.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.channel.robot.po.DemoRobotJob2;
import ins.channel.robot.po.DemoRobotJob2Key;

/**
 *
 * 表demo_robot_job2对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface DemoRobotJob2Dao extends MybatisBaseDao<DemoRobotJob2, DemoRobotJob2Key> {

	List<DemoRobotJob2> selectByRobotId(Long robotId);

	int deleteByRobotIds(List<Long> robotIds);

	Page<DemoRobotJob2> selectPage2(DemoRobotJob2 entity);
}
