package ins.channel.robot.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.robot.po.DemoRobotJob;

/**
 *
 * 表demo_robot_job对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface DemoRobotJobDao extends MybatisBaseDao<DemoRobotJob, Long> {

	Page<DemoRobotJob> selectByRobotId(PageParam pageParam, Long robotId);

	void deleteByRobotIds(List<Long> robotIds);
}
