package ins.channel.robot.dao;

import org.apache.ibatis.annotations.Mapper;

import ins.framework.mybatis.MybatisBaseDao;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.robot.po.DemoRobotMain2;

/**
 *
 * 表demo_robot_main2对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface DemoRobotMain2Dao extends MybatisBaseDao<DemoRobotMain2, Long> {

	Page<DemoRobotMain2> selectByManufactureName(PageParam pageParam, String manufactureName);
}
