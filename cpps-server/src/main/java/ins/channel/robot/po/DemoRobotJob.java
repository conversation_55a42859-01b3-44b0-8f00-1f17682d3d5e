package ins.channel.robot.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表demo_robot_job的PO对象<br/>
 * 对应表名：demo_robot_job,备注：演示机器人作业表
 *
 */
@Data
@Table(name = "demo_robot_job")
public class DemoRobotJob implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：id,备注：ID */
	@Column(name = "id", description = "ID")
	private Long id;
	/** 对应字段：robot_id,备注：机器人ID */
	@Column(name = "robot_id", description = "机器人ID")
	private Long robotId;
	/** 对应字段：start_time,备注：起始时间 */
	@Column(name = "start_time", description = "起始时间")
	private Date startTime;
	/** 对应字段：end_time,备注：结束时间 */
	@Column(name = "end_time", description = "结束时间")
	private Date endTime;
	/** 对应字段：walk_count,备注：行走次数 */
	@Column(name = "walk_count", description = "行走次数")
	private Long walkCount;
	/** 对应字段：consume_energy,备注：消耗能量 */
	@Column(name = "consume_energy", description = "消耗能量")
	private BigDecimal consumeEnergy;
	/** 对应字段：job_content,备注：作业内容 */
	@Column(name = "job_content", description = "作业内容")
	private String jobContent;
	/** 对应字段：job_image,备注：作业录像 */
	@Column(name = "job_image", description = "作业录像")
	private byte[] jobImage;
	/** 对应字段：com_code,备注：归属机构 */
	@Column(name = "com_code", description = "归属机构")
	private String comCode;
	/** 对应字段：version,备注：版本号（内部使用） */
	@Column(name = "version", description = "版本号（内部使用）")
	private Integer version;
	/** 对应字段：Insert_Time_For_His,备注：插入时间 */
	@Column(name = "Insert_Time_For_His", description = "插入时间")
	private Date insertTimeForHis;
	/** 对应字段：Operate_Time_For_His,备注：更新时间 */
	@Column(name = "Operate_Time_For_His", description = "更新时间")
	private Date operateTimeForHis;
}
