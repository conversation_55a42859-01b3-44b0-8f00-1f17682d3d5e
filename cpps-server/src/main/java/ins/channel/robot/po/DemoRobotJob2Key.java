package ins.channel.robot.po;

import java.io.Serializable;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表demo_robot_job2的复合主键PO主键对象<br/>
 * 对应表名：demo_robot_job2,备注：演示机器人作业表
 *
 */
@Data
@Table(name = "demo_robot_job2")
public class DemoRobotJob2Key implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：robot_id,备注：机器人ID */
	@Column(name = "robot_id", description = "机器人ID")
	private Long robotId;
	/** 对应字段：serial_no,备注：序号 */
	@Column(name = "serial_no", description = "序号")
	private Integer serialNo;
}
