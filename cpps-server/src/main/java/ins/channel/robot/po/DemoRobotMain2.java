package ins.channel.robot.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import ins.framework.mybatis.annotations.Table;
import ins.framework.mybatis.annotations.Column;
import lombok.Data;

/**
 *
 * 通过ins-framework-mybatis-generator工具自动生成，请勿手工修改。表demo_robot_main2的PO对象<br/>
 * 对应表名：demo_robot_main2,备注：演示机器人主表2
 *
 */
@Data
@Table(name = "demo_robot_main2")
public class DemoRobotMain2 implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：id,备注：ID */
	@Column(name = "id", description = "ID")
	private Long id;
	/** 对应字段：robot_sn,备注：机器人编号 */
	@Column(name = "robot_sn", description = "机器人编号")
	private String robotSn;
	/** 对应字段：robot_height,备注：机器人高度 */
	@Column(name = "robot_height", description = "机器人高度")
	private BigDecimal robotHeight;
	/** 对应字段：nickname,备注：昵称 */
	@Column(name = "nickname", description = "昵称")
	private String nickname;
	/** 对应字段：recharge_count,备注：充电循环 */
	@Column(name = "recharge_count", description = "充电循环")
	private Integer rechargeCount;
	/** 对应字段：manufacture_name,备注：生产厂家 */
	@Column(name = "manufacture_name", description = "生产厂家")
	private String manufactureName;
	/** 对应字段：manufacture_date,备注：生产日期 */
	@Column(name = "manufacture_date", description = "生产日期")
	private Date manufactureDate;
	/** 对应字段：com_code,备注：归属机构 */
	@Column(name = "com_code", description = "归属机构")
	private String comCode;
	/** 对应字段：version,备注：版本号（内部使用） */
	@Column(name = "version", description = "版本号（内部使用）")
	private Integer version;
	/** 对应字段：Insert_Time_For_His,备注：插入时间 */
	@Column(name = "Insert_Time_For_His", description = "插入时间")
	private Date insertTimeForHis;
	/** 对应字段：Operate_Time_For_His,备注：更新时间 */
	@Column(name = "Operate_Time_For_His", description = "更新时间")
	private Date operateTimeForHis;
}
