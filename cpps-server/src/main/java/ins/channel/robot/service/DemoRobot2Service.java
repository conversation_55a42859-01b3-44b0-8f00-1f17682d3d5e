package ins.channel.robot.service;

import java.util.List;

import ins.platform.common.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import ins.framework.common.ResultPage;
import ins.platform.common.CrudService;
import ins.channel.robot.vo.DemoRobotJob2Vo;
import ins.channel.robot.vo.DemoRobotMain2Vo;

/**
 * 用于演示数据库操作的Robot服务实现
 * 
 * <AUTHOR>
 *
 */
@Service
@Transactional
public class DemoRobot2Service implements CrudService<DemoRobotMain2Vo, Long> {
	@Autowired
	private ins.channel.robot.service.DemoRobotMain2Service demoRobotMain2Service;
	@Autowired
	private ins.channel.robot.service.DemoRobotJob2Service demoRobotJob2Service;

	/**
	 * 插入一条记录
	 * 
	 * @param vo 传入VO
	 * @return 返回主对象主键
	 */
	@Override
	public Long create(DemoRobotMain2Vo vo) {
		Long id = demoRobotMain2Service.create(vo);
		int index = 0;
		for (DemoRobotJob2Vo jobVo : vo.getJobList()) {
			jobVo.setRobotId(vo.getId());
			jobVo.setSerialNo(index++);
			demoRobotJob2Service.create(jobVo);
		}

		return id;
	}

	/**
	 * 根据生产厂家查询
	 * 
	 * @param manufactureName 生产厂家
	 * @return 返回结果对象
	 */

	@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
	public ResultPage<DemoRobotMain2Vo> selectByManufactureName(String manufactureName) {
		return demoRobotMain2Service.selectByManufactureName(manufactureName);
	}

	/**
	 * 修改记录信息
	 * 
	 * @param vo 传入VO
	 * 
	 * @return 影响的主对象的记录数
	 */
	@Override
	public int update(DemoRobotMain2Vo vo) {
		int count = demoRobotMain2Service.update(vo);
		int index = 0;
		for (DemoRobotJob2Vo jobVo : vo.getJobList()) {
			jobVo.setRobotId(vo.getId());
			jobVo.setSerialNo(index++);
			demoRobotJob2Service.update(jobVo);
		}

		return count;
	}

	/**
	 * 获取记录信息
	 * 
	 * @param id 传入id
	 * @return 返回结果对象
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
	public DemoRobotMain2Vo selectByPrimaryKey(Long id) {
		DemoRobotMain2Vo vo = demoRobotMain2Service.selectByPrimaryKey(id);
		if (vo != null) {
			vo.setJobList(demoRobotJob2Service.selectByRobotId(id));
		}
		return vo;
	}

	/**
	 * 删除多条记录
	 * 
	 * @param ids 传入ids
	 * @return 影响的主对象的记录数
	 */
	@Override
	public int delete(List<Long> ids) {
		demoRobotJob2Service.deleteByRobotIds(ids);
		return demoRobotMain2Service.delete(ids);
	}

	/**
	 * 前端页面查询
	 * 
	 * @param demoRobotMainVo 传入VO
	 * @return 返回结果对象
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS, isolation = Isolation.READ_COMMITTED, readOnly = true)
	public PageResult<DemoRobotMain2Vo> search(DemoRobotMain2Vo demoRobotMainVo) {
		return demoRobotMain2Service.search(demoRobotMainVo);
	}
}
