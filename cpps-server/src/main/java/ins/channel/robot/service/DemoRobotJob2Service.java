package ins.channel.robot.service;

import java.util.ArrayList;
import java.util.List;

import ins.platform.common.PageResult;
import ins.platform.utils.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import ins.framework.common.ResultPage;
import ins.framework.mybatis.MybatisApiUtils;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.framework.mybatis.util.Pages;
import ins.framework.utils.Beans;
import ins.platform.common.CrudService;
import ins.channel.robot.dao.DemoRobotJob2Dao;
import ins.channel.robot.po.DemoRobotJob2;
import ins.channel.robot.po.DemoRobotJob2Key;
import ins.channel.robot.vo.DemoRobotJob2KeyVo;
import ins.channel.robot.vo.DemoRobotJob2Vo;

/**
 * 用于演示数据库操作的Robot服务实现
 * 
 * <AUTHOR>
 *
 */
@Service
@Transactional
public class DemoRobotJob2Service implements CrudService<DemoRobotJob2Vo, DemoRobotJob2KeyVo> {
	@Autowired
	private DemoRobotJob2Dao demoRobotJob2Dao;

	/**
	 * 插入一条记录
	 * 
	 * @param vo 传入VO
	 * @return 返回主对象主键
	 */
	@Override
	public DemoRobotJob2KeyVo create(DemoRobotJob2Vo vo) {
		// 1.检查传入参数
		Assert.notNull(vo, "Object must have value"); 
		Assert.notNull(vo.getRobotId(), "robotId must have value");
		Assert.notNull(vo.getSerialNo(), "serialNo must have value");
		// 2.构建PO对象
		DemoRobotJob2 po = Beans.copyDepth().from(vo).to(DemoRobotJob2.class);
		 
		// 3.设置PO本身的属性

		// 4.实际调用Dao进行操作
		demoRobotJob2Dao.insertSelective(po);

		DemoRobotJob2KeyVo result = new DemoRobotJob2KeyVo();
		Beans.copy().from(po).to(result);
		return result;
	}

	/**
	 * 修改记录信息
	 * 
	 * @param vo 传入VO
	 * 
	 * @return 影响的主对象的记录数
	 */
	@Override
	public int update(DemoRobotJob2Vo vo) {
		// 1.检查传入参数
		Assert.notNull(vo, "Object must have value"); 
		Assert.notNull(vo.getRobotId(), "robotId must have value");
		Assert.notNull(vo.getSerialNo(), "serialNo must have value");
		// 2.构建PO对象
		DemoRobotJob2 po = new DemoRobotJob2();
		// 3.VO复制值到PO
		Beans.copy().from(vo).to(po);
		// 4.设置PO本身的属性

		// 5.实际调用Dao进行操作
		return demoRobotJob2Dao.updateSelectiveByPrimaryKey(po);
	}

	/**
	 * 获取记录信息
	 * 
	 * @param id 传入id
	 * @return 返回结果对象
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
	public DemoRobotJob2Vo selectByPrimaryKey(DemoRobotJob2KeyVo id) {
		DemoRobotJob2Key pk = new DemoRobotJob2Key();
		Beans.copy().from(id).to(pk);

		DemoRobotJob2 po = demoRobotJob2Dao.selectByPrimaryKey(pk);
		if (po == null) {
			return null;
		} 
		return Beans.copyDepth().from(po).to(DemoRobotJob2Vo.class);
	}

	/**
	 * 删除多条记录
	 * 
	 * @param ids 传入ids
	 * @return 影响的主对象的记录数
	 */
	@Override
	public int delete(List<DemoRobotJob2KeyVo> ids) {

		List<DemoRobotJob2Key> pks = new ArrayList<>();
		for (DemoRobotJob2KeyVo id : ids) {

			DemoRobotJob2Key pk = new DemoRobotJob2Key();
			Beans.copy().from(id).to(pk);
			pks.add(pk);
		}

		return demoRobotJob2Dao.deleteBatchByPrimaryKeys(pks);
	}

	/**
	 * 前端页面查询
	 * 
	 * @param demoRobotJobVo 传入VO
	 * @return 返回结果对象
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS, isolation = Isolation.READ_COMMITTED, readOnly = true)
	public PageResult<DemoRobotJob2Vo> search(DemoRobotJob2Vo demoRobotJobVo) {
		DemoRobotJob2 po = new DemoRobotJob2();
		Beans.copy().from(demoRobotJobVo).to(po);
		PageParam pageParam = MybatisApiUtils.getPageParam();
		Page<DemoRobotJob2> poList = demoRobotJob2Dao.selectPage(pageParam, po);
		return PageHelper.convert(pageParam, poList, DemoRobotJob2Vo.class);
	}

	/**
	 * 根据robotId查询job列表
	 * 
	 * @param robotId robotId
	 * @return 返回job列表
	 */
	@Transactional(propagation = Propagation.SUPPORTS, isolation = Isolation.READ_COMMITTED, readOnly = true)
	public List<DemoRobotJob2Vo> selectByRobotId(Long robotId) {
		List<DemoRobotJob2> poList = demoRobotJob2Dao.selectByRobotId(robotId);
		List<DemoRobotJob2Vo> result = new ArrayList<>(poList.size());
		for (DemoRobotJob2 po : poList) {
			DemoRobotJob2Vo vo = new DemoRobotJob2Vo();
			Beans.copy().from(po).to(po);
			result.add(vo);
		}
		return result;
	}

	/**
	 * 根据robotIds删除多条记录
	 * 
	 * @param robotIds 传入robotIds
	 * @return 影响的主对象的记录数
	 */
	public int deleteByRobotIds(List<Long> robotIds) {
		return demoRobotJob2Dao.deleteByRobotIds(robotIds);
	}
}
