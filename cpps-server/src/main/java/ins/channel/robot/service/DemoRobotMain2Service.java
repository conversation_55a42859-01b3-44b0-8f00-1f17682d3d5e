package ins.channel.robot.service;

import java.util.List;

import ins.platform.common.PageResult;
import ins.platform.utils.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import ins.framework.common.ResultPage;
import ins.framework.mybatis.MybatisApiUtils;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.framework.mybatis.util.Pages;
import ins.framework.utils.Beans;
import ins.platform.common.CrudService;
import ins.channel.robot.dao.DemoRobotMain2Dao;
import ins.channel.robot.po.DemoRobotMain2;
import ins.channel.robot.vo.DemoRobotMain2Vo;

/**
 * 用于演示数据库操作的Robot服务实现
 * 
 * <AUTHOR>
 *
 */
@Service
@Transactional
public class DemoRobotMain2Service implements CrudService<DemoRobotMain2Vo, Long> {
	@Autowired
	private DemoRobotMain2Dao demoRobotMain2Dao;

	/**
	 * 插入一条记录
	 * 
	 * @param vo 传入VO
	 * @return 返回主对象主键
	 */
	@Override
	public Long create(DemoRobotMain2Vo vo) {
		// 1.检查传入参数
		Assert.notNull(vo, "Object must have value");
		// 2.构建PO对象
		DemoRobotMain2 po = new DemoRobotMain2();
		// 3.VO复制值到PO
		Beans.copy().from(vo).to(po);
		// 4.设置PO本身的属性

		// 5.实际调用Dao进行操作
		demoRobotMain2Dao.insertSelective(po);

		DemoRobotMain2Vo result = new DemoRobotMain2Vo();
		Beans.copy().from(po).to(result);
		return po.getId();
	}

	/**
	 * 根据生产厂家查询
	 * 
	 * @param manufactureName 生产厂家
	 * @return 返回结果对象
	 */

	@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
	public ResultPage<DemoRobotMain2Vo> selectByManufactureName(String manufactureName) {
		PageParam pageParam = MybatisApiUtils.getPageParam();
		Page<DemoRobotMain2> poList = demoRobotMain2Dao.selectByManufactureName(pageParam, manufactureName);
		return Pages.convert(pageParam, poList, DemoRobotMain2Vo.class);
	}

	/**
	 * 修改记录信息
	 * 
	 * @param vo 传入VO
	 * 
	 * @return 影响的主对象的记录数
	 */
	@Override
	public int update(DemoRobotMain2Vo vo) {
		DemoRobotMain2 po = new DemoRobotMain2();
		Beans.copy().from(vo).to(po);
		return demoRobotMain2Dao.updateSelectiveByPrimaryKey(po);
	}

	/**
	 * 获取记录信息
	 * 
	 * @param id 传入id
	 * @return 返回结果对象
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
	public DemoRobotMain2Vo selectByPrimaryKey(Long id) {
		DemoRobotMain2 po = demoRobotMain2Dao.selectByPrimaryKey(id);
		if (po == null) {
			return null;
		}
		DemoRobotMain2Vo vo = new DemoRobotMain2Vo();
		Beans.copy().from(po).to(vo);

		return vo;
	}

	/**
	 * 删除多条记录
	 * 
	 * @param ids 传入ids
	 * @return 影响的主对象的记录数
	 */
	@Override
	public int delete(List<Long> ids) {
		return demoRobotMain2Dao.deleteBatchByPrimaryKeys(ids);
	}

	/**
	 * 前端页面查询
	 * 
	 * @param demoRobotMainVo 传入VO
	 * @return 返回结果对象
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS, isolation = Isolation.READ_COMMITTED, readOnly = true)
	public PageResult<DemoRobotMain2Vo> search(DemoRobotMain2Vo demoRobotMainVo) {
		DemoRobotMain2 po = new DemoRobotMain2();
		Beans.copy().from(demoRobotMainVo).to(po);
		PageParam pageParam = MybatisApiUtils.getPageParam();
		Page<DemoRobotMain2> poList = demoRobotMain2Dao.selectPage(pageParam, po);
		return PageHelper.convert(pageParam, poList, DemoRobotMain2Vo.class);
	}

	/**
	 * 获取Main记录信息
	 * 
	 * @param id 传入id
	 * @return 返回结果对象
	 */

	@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
	public DemoRobotMain2Vo selectMainByPrimaryKey(Long id) {
		DemoRobotMain2 po = demoRobotMain2Dao.selectByPrimaryKey(id);
		if (po == null) {
			return null;
		}
		DemoRobotMain2Vo vo = new DemoRobotMain2Vo();
		Beans.copy().from(po).to(vo);
		return vo;
	}

}
