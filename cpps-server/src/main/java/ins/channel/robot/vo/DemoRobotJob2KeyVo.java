package ins.channel.robot.vo;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * DemoRobotJob2KeyVo对象.对应实体描述：演示机器人作业表
 *
 */
@Data
@ApiModel(" DemoRobotJob2KeyVo对象")
public class DemoRobotJob2KeyVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：robot_id,备注：机器人ID */
	@ApiModelProperty("机器人ID")
	private Long robotId;
	/** 对应字段：serial_no,备注：序号 */
	@ApiModelProperty("序号")
	private Integer serialNo;
}
