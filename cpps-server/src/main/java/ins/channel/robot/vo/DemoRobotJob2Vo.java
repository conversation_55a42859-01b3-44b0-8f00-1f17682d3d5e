package ins.channel.robot.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * DemoRobotJob2Vo对象.对应实体描述：演示机器人作业表
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("DemoRobotJob2Vo对象")
public class DemoRobotJob2Vo extends DemoRobotJob2KeyVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：start_time,备注：起始时间 */
	@ApiModelProperty("起始时间")
	private Date startTime;
	/** 对应字段：end_time,备注：结束时间 */
	@ApiModelProperty("结束时间")
	private Date endTime;
	/** 对应字段：walk_count,备注：行走次数 */
	@ApiModelProperty("行走次数")
	private Long walkCount;
	/** 对应字段：consume_energy,备注：消耗能量 */
	@ApiModelProperty("消耗能量")
	private BigDecimal consumeEnergy;
	/** 对应字段：job_content,备注：作业内容 */
	@ApiModelProperty("作业内容")
	private String jobContent;
	/** 对应字段：job_image,备注：作业录像 */
	@ApiModelProperty("作业录像")
	private byte[] jobImage;
	/** 对应字段：com_code,备注：归属机构 */
	@ApiModelProperty("归属机构")
	private String comCode;
	/** 对应字段：version,备注：版本号（内部使用） */
	@ApiModelProperty("版本号（内部使用）")
	private Integer version;
	/** 对应字段：Insert_Time_For_His,备注：插入时间 */
	@ApiModelProperty("插入时间")
	private Date insertTimeForHis;
	/** 对应字段：Operate_Time_For_His,备注：更新时间 */
	@ApiModelProperty("更新时间")
	private Date operateTimeForHis;
}
