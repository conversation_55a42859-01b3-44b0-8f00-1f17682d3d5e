package ins.channel.robot.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * DemoRobotJobVo对象.
 *
 */
@Data
@ApiModel("DemoRobotJobVo对象")
public class DemoRobotJobVo implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id */
	private Long id;
	/** 对应字段：robot_id */
	private Long robotId;
	/** 对应字段：start_time */
	@ApiModelProperty()
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date startTime;
	/** 对应字段：end_time */
	@ApiModelProperty()
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date endTime;
	/** 对应字段：walk_count */
	@ApiModelProperty()
	private Long walkCount;
	/** 对应字段：consume_energy */
	@ApiModelProperty()
	private BigDecimal consumeEnergy;
	/** 对应字段：job_content */
	@ApiModelProperty()
	private String jobContent;
	/** 对应字段：job_image */
	@ApiModelProperty()
	private byte[] jobImage;
	/** 对应字段：com_code */
	@ApiModelProperty()
	private String comCode;
	/** 对应字段：version */
	@ApiModelProperty()
	private Integer version;
	/** 对应字段：Insert_Time_For_His */
	@ApiModelProperty()
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date insertTimeForHis;
	/** 对应字段：Operate_Time_For_His */
	@ApiModelProperty()
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date operateTimeForHis;
}
