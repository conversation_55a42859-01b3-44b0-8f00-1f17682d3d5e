package ins.channel.robot.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * DemoRobotMain2Vo对象.对应实体描述：演示机器人主表2
 *
 */
@Data
@ApiModel("DemoRobotMain2Vo对象")
public class DemoRobotMain2Vo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：id,备注：ID */
	@ApiModelProperty("ID")
	private Long id;
	/** 对应字段：robot_sn,备注：机器人编号 */
	@ApiModelProperty("机器人编号")
	private String robotSn;
	/** 对应字段：robot_height,备注：机器人高度 */
	@ApiModelProperty("机器人高度")
	private BigDecimal robotHeight;
	/** 对应字段：nickname,备注：昵称 */
	@ApiModelProperty("昵称")
	private String nickname;
	/** 对应字段：recharge_count,备注：充电循环 */
	@ApiModelProperty("充电循环")
	private Integer rechargeCount;
	/** 对应字段：manufacture_name,备注：生产厂家 */
	@ApiModelProperty("生产厂家")
	private String manufactureName;
	/** 对应字段：manufacture_date,备注：生产日期 */
	@ApiModelProperty("生产日期")
	private Date manufactureDate;
	/** 对应字段：com_code,备注：归属机构 */
	@ApiModelProperty("归属机构")
	private String comCode;
	/** 对应字段：version,备注：版本号（内部使用） */
	@ApiModelProperty("版本号（内部使用）")
	private Integer version;
	/** 对应字段：Insert_Time_For_His,备注：插入时间 */
	@ApiModelProperty("插入时间")
	private Date insertTimeForHis;
	/** 对应字段：Operate_Time_For_His,备注：更新时间 */
	@ApiModelProperty("更新时间")
	private Date operateTimeForHis;
	
	private List<DemoRobotJob2Vo> jobList = new ArrayList<>();	
}
