package ins.channel.robot.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * DemoRobotMainVo对象.
 *
 */
@Data
@ApiModel("DemoRobotMainVo对象")
public class DemoRobotMainVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 对应字段：id */
	@ApiModelProperty()
	private Long id;
	/** 对应字段：robot_sn */
	@ApiModelProperty()
	private String robotSn;
	/** 对应字段：robot_height */
	@ApiModelProperty()
	private BigDecimal robotHeight;
	/** 对应字段：nickname */
	@ApiModelProperty()
	private String nickname;
	/** 对应字段：recharge_count */
	@ApiModelProperty()
	private Integer rechargeCount;
	/** 对应字段：manufacture_name */
	@ApiModelProperty()
	private String manufactureName;
	/** 对应字段：manufacture_date */
	@ApiModelProperty()
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date manufactureDate;
	/** 对应字段：com_code */
	@ApiModelProperty()
	private String comCode;
	/** 对应字段：version */
	@ApiModelProperty()
	private Integer version;
	/** 对应字段：Insert_Time_For_His */
	@ApiModelProperty()
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date insertTimeForHis;
	/** 对应字段：Operate_Time_For_His */
	@ApiModelProperty()
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date operateTimeForHis;

	private List<DemoRobotJobVo> jobList = new ArrayList<>();
}
