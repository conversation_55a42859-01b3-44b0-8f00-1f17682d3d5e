package ins.channel.sale.api;

import ins.channel.sale.entity.EsProcessorResult;
import ins.channel.sale.service.SaleService;
import ins.channel.sale.vo.IssueReqVo;
import ins.channel.sale.vo.IssueRespVo;
import ins.channel.sale.vo.ProposalReq;
import ins.framework.exception.BusinessException;
import ins.platform.common.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/sale")
public class SaleApi {
    @Autowired
    public SaleService saleService;
    
    /** 投保*/
    @PostMapping(value = "/createProposal")
    public EsProcessorResult createProposal(@RequestBody ProposalReq vo) throws BusinessException {
        return saleService.createProposal(vo);
    }
    
    @PostMapping(value = "/issue")
    public ResponseVo<IssueRespVo> issue(@RequestBody IssueReqVo issueReqVo) throws BusinessException {
        return ResponseVo.ok(saleService.issue(issueReqVo));
    }

}
