package ins.channel.sale.entity;

public class EsProcessorResult {

	// 是否成功
	private boolean success = true;

	// 提示信息
	private String msg = "操作成功";

	// 其他信息
	private Object obj = null;

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public Object getObj() {
		return obj;
	}

	public void setObj(Object obj) {
		this.obj = obj;
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}
	
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("EsProcessorResult [success=");
		builder.append(success);
		builder.append(", msg=");
		builder.append(msg);
		builder.append(", obj=");
		builder.append(obj);
		builder.append("]");
		return builder.toString();
	}

}
