package ins.channel.sale.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import ins.channel.base.service.GgcompanyService;
import ins.channel.base.vo.GgcompanySelectResponseVo;
import ins.channel.config.UrlConfig;
import ins.channel.gupolicyuserauthority.vo.PolicyuserauthorityReqVo;
import ins.channel.gztemplatesave.dao.GztemplatesaveDao;
import ins.channel.gztemplatesave.po.Gztemplatesave;
import ins.channel.policycopymain.vo.InsuredlistTempVo;
import ins.channel.policymain.service.PolicyMainService;
import ins.channel.policymain.vo.PolicyDtoResponse;
import ins.channel.policymain.vo.PolicySyncQueryRespVo;
import ins.channel.policymain.vo.PolicySyncRequestVo;
import ins.channel.policyuserauthority.api.PolicyUserAuthorityApi;
import ins.channel.power.service.PowerService;
import ins.channel.power.vo.UserpermitdataVo;
import ins.channel.sale.entity.EsProcessorResult;
import ins.channel.sale.vo.IssueReqVo;
import ins.channel.sale.vo.IssueRespVo;
import ins.channel.sale.vo.ProposaInsuredlist;
import ins.channel.sale.vo.ProposalReq;
import ins.channel.sale.vo.ProposalResp;
import ins.channel.sale.vo.Proposalnsurancelist;
import ins.platform.common.ResponseVo;
import ins.platform.common.SysUser;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.HttpClientUtils;
import ins.platform.utils.SessionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ins.channel.sale.vo.Policylist;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class SaleService {
    @Autowired
    private UrlConfig urlConfig;
    @Autowired
    private PolicyMainService policyMainService;
    @Autowired
    private GztemplatesaveDao gztemplatesaveDao;

    @Autowired
    private GgcompanyService ggcompanyService;
    @Autowired
    private PowerService powerService;

    @Autowired
    private PolicyUserAuthorityApi policyUserAuthorityApi;

    public IssueRespVo issue(IssueReqVo issueReqVo){
        IssueRespVo issueRespVo = new IssueRespVo();
        // todo 考虑投保失败的问题
        //基本校验
        if (StringUtils.isEmpty(issueReqVo.getUuid())){
            log.info("被保人模板 uuid 不能为空!");
            issueRespVo.setCode("500");
            issueRespVo.setMessage("被保人模板 uuid 不能为空!");
            return issueRespVo;
        }
        
        // 根据模板 uuid 获取客户上传的模板数据
        Gztemplatesave gztemplatesave = gztemplatesaveDao.selectByPrimaryKey(issueReqVo.getUuid());
        if (StringUtils.isEmpty(gztemplatesave.getTemplatedata())){
            log.info("模板数据不能为空");
            issueRespVo.setCode("500");
            issueRespVo.setMessage("模板数据不能为空!");
            return issueRespVo;
        }
        JSONArray jsonArray = JSONArray.parseArray(gztemplatesave.getTemplatedata());
        List<InsuredlistTempVo> insuredlistTempVo = JSONObject.parseArray(jsonArray.toJSONString(), InsuredlistTempVo.class);
        
        // 组装投保请求报文
        List<ProposalReq> proposalReqList = assembleProposalReq(issueReqVo,insuredlistTempVo);
        
        // 批量投保
        List<String> salePolicynoList = new ArrayList<>();
        for (ProposalReq proposalReq : proposalReqList) {
            EsProcessorResult result = createProposal(proposalReq);
            if (result.isSuccess() && result.getObj() != null) {
                ProposalResp proposalResp = (ProposalResp) result.getObj();
                proposalResp.getPolicylist().stream()
                        .map(Policylist::getPolicyno)
                        .filter(Objects::nonNull)
                        .forEach(salePolicynoList::add);
            }
        }
        log.info("traceId + salePolicynoList:{}",salePolicynoList);
        if (CollectionUtils.isEmpty(salePolicynoList)){
            log.info("未生成有效保单!");
            issueRespVo.setCode("500");
            issueRespVo.setMessage("未生成有效保单!");
            return issueRespVo;
        }

        // 同步投保生成的保单
        String riskcode = issueReqVo.getInsurance_list().get(0).getRiskcode();
        List<String> syncPolicynoList = autoSyncPolicyBySale(salePolicynoList,riskcode);
        log.info("traceId + syncPolicynoList:{}",syncPolicynoList);
        if (CollectionUtils.isEmpty(syncPolicynoList)) {
            log.info("保单同步异常!");
            issueRespVo.setCode("500");
            issueRespVo.setMessage("保单同步异常!");
            return issueRespVo;
        }
        
        // 授权
        autoAuthorization(syncPolicynoList);
 
        return issueRespVo;
    }
    /** 调用应用平台投保 */
    public EsProcessorResult createProposal(ProposalReq proposalReq){
        EsProcessorResult result = new EsProcessorResult();
        //获取 yml文件中配置的接口地址
        String createProposalUrl = urlConfig.getCreateProposalUrl();
        log.info("=====投保请求渠道路径 {}",createProposalUrl);
        log.info("=====投保请求渠道报文 {}",JSON.toJSONString(proposalReq));
        String responseJson;
        try {
            responseJson = HttpClientUtils.postJson(createProposalUrl, JSON.toJSONString(proposalReq)).getBodyAsString();
        } catch (Exception e) {
            log.info(e.getMessage());
            //todo 保存异常日志
            result.setSuccess(false);
            result.setMsg(e.getMessage());
            return result;
        }

        if (StringUtils.isEmpty(responseJson)) {
            log.info("渠道返回报文为空");
            //todo 保存异常日志
            result.setSuccess(false);
            result.setMsg("渠道返回报文为空");
            return result;
        }
        log.info("=====投保渠道返回报文 {}",responseJson);
        ProposalResp proposalResp = JSONObject.parseObject(responseJson, ProposalResp.class);
        // 若响应状态码不为 "0000"则查询失败
        if (!"0000".equals(proposalResp.getResposecode())) {
            log.info("=====渠道返回错误提示信息 {}",proposalResp.getResposemsg());
            //todo 保存异常日志
            result.setSuccess(false);
            result.setMsg(proposalResp.getResposemsg());
            return result;
        }
        //todo 保存成功日志
        result.setObj(proposalResp);
        return result;
    }
    /** 同步投保生成的保单 */
    public List<String> autoSyncPolicyBySale(List<String> salePolicynoList, String riskCode){
        PolicySyncRequestVo requestVo = new PolicySyncRequestVo();
        requestVo.setPolicylist(salePolicynoList);
        requestVo.setSurverind(riskCode); 
        //根据传入的保单号集合, 按批次同步核心最新保单信息
        PolicySyncQueryRespVo respVo = policyMainService.queryPolicyEmployerByBatch(requestVo, 5);
        List<PolicyDtoResponse> policyDtoResponseList = respVo.getPolicylist();
        //同步成功,调用解析接口
        if (!"500".equals(respVo.getStatus())) {
            policyMainService.resolvePolicyInfo(policyDtoResponseList, requestVo.getSurverind());
        }
        return respVo.getPolicyNolist();
    }
    /** 自动授权予出单用户 */
    public void autoAuthorization(List<String> syncPolicynoList){
        log.info("开始授权 traceId + syncPolicynoList:{}",syncPolicynoList);
        // 获取授权机构下拉选项
        List<GgcompanySelectResponseVo> result = ggcompanyService.companyForSelectAll();
        // 获取用户所属机构
        SysUser loginUser = SessionHelper.getLoginUser();
        List<UserpermitdataVo> ggCompanyList = powerService.queryGgCompanyByLoginUser(loginUser.getUserCode());
        List<String> comCodeList = ggCompanyList.stream().
                map(UserpermitdataVo::getComCode)
                .sorted(Comparator.reverseOrder())
                .collect(Collectors.toList());
        log.info("======开始匹配用户所属机构======");
        String companyCode = null;
        for (GgcompanySelectResponseVo ggcompanySelectResponseVo : result) {
            if (companyCode != null) {
                break;
            }
            for (String comCode : comCodeList) {
                if (ggcompanySelectResponseVo.getCompanyCode().equals(comCode)) {
                    companyCode = ggcompanySelectResponseVo.getCompanyCode();
                    log.info("已匹配用户所属机构 companyCode{}",companyCode);
                    break;
                }
            }
        }
        if (companyCode == null) {
            log.info("未能匹配用户所属机构,无法自动授权!");
            return;
        }
        // 组装报文
        PolicyuserauthorityReqVo policyuserauthorityReqVo = new PolicyuserauthorityReqVo();
        policyuserauthorityReqVo.setUserCode(loginUser.getUserCode());
        policyuserauthorityReqVo.setUserCname(loginUser.getUserCname());
        policyuserauthorityReqVo.setCompanyCode(companyCode);
        policyuserauthorityReqVo.setPolicynoList(new HashSet<>(syncPolicynoList));
        // 请求授权接口
        ResponseVo<String> save = policyUserAuthorityApi.save(policyuserauthorityReqVo);
    }
    
    /** 组装投保请求报文 */
    public List<ProposalReq> assembleProposalReq(IssueReqVo issueReqVo,List<InsuredlistTempVo> excelTempProposalVoList) {
        List<ProposalReq> proposalReqList = new ArrayList<>();
        for (InsuredlistTempVo excelTempProposalVo : excelTempProposalVoList) {
            ProposalReq proposalReq = BeanCopyUtils.clone(issueReqVo, ProposalReq.class);
            // copy 模板数据至被保人节点
            List<ProposaInsuredlist> proposaInsuredlists = Arrays.asList(BeanCopyUtils.clone(excelTempProposalVo, ProposaInsuredlist.class));
            proposalReq.setInsuredlist(proposaInsuredlists);
            // 更新标的信息
            List<Proposalnsurancelist> insuranceList = proposalReq.getInsurance_list();
            for (Proposalnsurancelist insurance : insuranceList) {
                insurance.setOccupationType(excelTempProposalVo.getOccupationType());
                insurance.setOccupationCode(excelTempProposalVo.getOccupationCode());
                insurance.setOccupationLevel(excelTempProposalVo.getOccupationLevel());
            }
            proposalReqList.add(proposalReq);
        }
        return proposalReqList;
    }
}
