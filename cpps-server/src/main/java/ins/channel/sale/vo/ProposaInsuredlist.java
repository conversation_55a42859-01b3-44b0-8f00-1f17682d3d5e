package ins.channel.sale.vo;

import lombok.Data;

/** 被保人节点 */
@Data
public class ProposaInsuredlist {
    /** 姓名 */
    private String name;
    /** 性别 1-男 2-女 */
    private String sex;
    /** 出生日期（yyyy-MM-dd） */
    private String birthdate;
    /** 证件类型 */
    private String certi_type;
    /** 证件号码 */
    private String certi_no;
    /** 邮编 */
    private String postCode;
    /** 与投保人关系（0-本人，1-配偶，2-子女，12-亲属，13-父母，14-其他） */
    private String relation;
    /** 邮箱 */
    private String email;
    /** 电话 */
    private String phone;
    /** 联系地址 */
    private String contactAdr;
    /** 在职状态 01:在职；02：退休；09：其他*/
    private String ontheJobstatus;
    /** 职业/工种  todo 需要配置平台支持  */ 
    private String occupation;
    /** 省份代码 */
    private String provinceCode;
    /** 省份名称 */
    private String provinceName;
    /** 城市代码 */
    private String cityCode;
    /** 城市名称 */
    private String cityName;
    /** 区县代码 */
    private String countyCode;
    /** 区县名称 */
    private String countyName;
    /** 投保人类型 1-个人 2-企业 */
    private String type;
    /** 户口类型（1：农村户口，2：城镇居民户口） */
    private String residenceType;
}
