package ins.channel.sale.vo;

import lombok.Data;

/** 投保人节点 */
@Data
public class ProposalApplicantlist {
    /** 姓名 */
    private String name;
    
    /** 性别（1：男；2：女；3：其他） */
    private String sex;
    
    /** 证件类型 （01居民身份证,02护照,03军人证,04驾驶证,05港澳台同胞证,06港澳台居民身份证,07中国护照,08外国人永久居留身份证,99其他）
     企业证件类型（1-组织机构代码证，2-税务登记证，3-营业执照，4-社会统一信用代码，99-其他）*/
    private String certi_type;
    
    /** 证件号 */
    private String certi_no;
    
    /** 国籍 */
    private String countrycode;
    
    /** 邮政编码 */
    private String postCode;
    
    /** 投保人类型（1：个人；2：企业） */
    private String type;
    
    /** 邮箱 */
    private String email;
    
    /** 电话 */
    private String phone;
    
    /** 联系地址 */
    private String contactAdr;
    
    /** 出生日期 */
    private String birthdate;
    
    /** 省份代码 */
    private String provinceCode;
    
    /** 省份名称 */
    private String provinceName;
    
    /** 城市代码 */
    private String cityCode;
    
    /** 城市名称 */
    private String cityName;
    
    /** 区县代码 */
    private String countyCode;
    
    /** 区县名称 */
    private String countyName;
    
    /** 联系人名称，被保人类型为2：企业时必传 */
    private String contactName;
    
    /** 单位电话，被保人类型为2：企业时必传 */
    private String officephonenumber;
    
    /** 单位性质（详见码表单位性质）投保人类型为2：企业时必传 */
    private String companynature;
    
    /** 单位总人数，投保人类型为 2：企业时必传 */
    private String numberofunits;
}
