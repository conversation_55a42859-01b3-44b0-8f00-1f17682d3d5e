package ins.channel.sale.vo;

import lombok.Data;

import java.util.List;

/** 投保渠道请求 */
@Data
public class ProposalReq {
	/** 渠道编码 */
	private String channel_id;
	/** 交易流水号 */
	private String request_no;
	/** 时间戳 */
	private String timestamp;
	/** 交易类型 */
	private String requesttype;
	/** 产品代码 */
	private String product_id;
	/** 保额 */
	private String amount;
	/** 保费 */
	private String premium;
	/** 投保日期 */
	private String insure_date;
	/** 起保日期 */
	private String start_date;
	/** 终保日期 */
	private String end_date;

	private List<ProposalApplicantlist> applicantlist;
	private List<ProposaInsuredlist> insuredlist;
	private ProposalExtendparams extend_params;
	private List<Proposalnsurancelist> insurance_list;
}
