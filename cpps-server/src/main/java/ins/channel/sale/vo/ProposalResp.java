package ins.channel.sale.vo;

import lombok.Data;
import java.util.List;
/** 投保渠道返回 */
@Data
public class ProposalResp {
    /** 保额 */
    private String amount;
    /** 起保日期 */
    private String start_date;
    /** 终保日期 */
    private String end_date;
    /** 保费 */
    private String premium;
    /** 响应码（成功：0000;失败：1000） */
    private String resposecode;
    /** 响应信息描述 */
    private String resposemsg;
    /** 投保单清单 */
    private List<Proposalnolist> proposalnolist;
    /** 保单清单 */
    private List<Policylist> policylist;
}
