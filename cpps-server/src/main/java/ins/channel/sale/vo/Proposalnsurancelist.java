package ins.channel.sale.vo;

import lombok.Data;
/** 标的信息 */
@Data
public class Proposalnsurancelist {
    /** 职业大类代码 */
    private String occupationType;
    /** 职业大类对应职业工种代码 */
    private String occupationCode;
    /** 职业等级 */
    private String occupationLevel;
    /** 险种代码 */
    private String riskcode;
    /** 投保总人数 */
    private String fieldAF;
    /** 有无被保险人残疾、患有精神疾病或重大疾病 fieldAK 是-1 否-0 默认-是*/
    private String fieldAK;
    /** 未满期标志 满期-0 未满期-1 默认-满期 */
    private String fieldAZ;
}
