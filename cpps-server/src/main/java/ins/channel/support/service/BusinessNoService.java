package ins.channel.support.service;

import ins.channel.support.constant.BusinessNoType;
import ins.channel.support.dao.BusinessNoDao;
import ins.channel.utils.DocumentFactory;
import ins.channel.utils.IDTask;
import ins.framework.exception.BusinessException;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 序列号生成
 * Created by sino on 2019/9/7.
 */
@Service
@Transactional
public class BusinessNoService {

    FastDateFormat fdf = FastDateFormat.getInstance("yyyyMMdd");
    FastDateFormat fdy = FastDateFormat.getInstance("yyyy");


    private static final Map<String, String> MAPPING_BUSTYPE_SEQNAME = new HashMap<>();

    static {
        MAPPING_BUSTYPE_SEQNAME.put(BusinessNoType.ID, "SEQ_DOCUMENTNO");
        MAPPING_BUSTYPE_SEQNAME.put(BusinessNoType.SETTLE_NO, "SEQ_SETTLENO");
        MAPPING_BUSTYPE_SEQNAME.put(BusinessNoType.ENDOR_NO, "SEQ_ENDORNO");
    }

    @Autowired
    private BusinessNoDao dao;

    public String nextNo(String businessType) {
        StringBuffer noBuffer = new StringBuffer();
        int initLength=0;
        if (BusinessNoType.ID.equals(businessType)) {
            IDTask idTask = new IDTask();
            String snowflakeNo = DocumentFactory.getSnowflakeNo(idTask);
            noBuffer.append(snowflakeNo);
            initLength=11;
        } else if (BusinessNoType.SETTLE_NO.equals(businessType)) {
            noBuffer.append("ST").append(fdf.format(new Date()));
            initLength = 8;
        } else {
            throw new BusinessException("获取序列号失败!未知的业务类型!");
        }
        String nextSeqNo = Integer.toString(dao.nextSeqNo(MAPPING_BUSTYPE_SEQNAME.get(businessType)));
        int fillCount =  initLength - nextSeqNo.length();
        if (fillCount > 0) {
            for (int i = 0; i < fillCount; i++) {
                noBuffer.append("0");
            }
        }
        noBuffer.append(nextSeqNo);
        return noBuffer.toString();
    }

    public String nextNo(String businessType,String insuranceCompanyCode,String productCode) {
        StringBuffer noBuffer = new StringBuffer();
        int initLength=0;
         if (BusinessNoType.ENDOR_NO.equals(businessType)) {
            noBuffer.append("2").append(insuranceCompanyCode).append(productCode).append(fdy.format(new Date())).append("Q");
            initLength = 10;
        } else {
            throw new BusinessException("获取申报单号失败!业务类型错误!");
        }
        String nextSeqNo = Integer.toString(dao.nextSeqNo(MAPPING_BUSTYPE_SEQNAME.get(businessType)));
        int fillCount =  initLength - nextSeqNo.length();
        if (fillCount > 0) {
            for (int i = 0; i < fillCount; i++) {
                noBuffer.append("0");
            }
        }
        noBuffer.append(nextSeqNo);
        return noBuffer.toString();
    }
}
