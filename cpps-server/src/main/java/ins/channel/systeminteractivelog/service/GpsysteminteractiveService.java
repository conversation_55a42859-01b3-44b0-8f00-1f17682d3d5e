package ins.channel.systeminteractivelog.service;

import ins.channel.systeminteractivelog.dao.GpsysteminteractivelogDao;
import ins.channel.systeminteractivelog.po.Gpsysteminteractivelog;
import ins.channel.systeminteractivelog.vo.GpsysteminteractivelogVo;
import ins.platform.utils.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.UUID;
@Service
@Slf4j
@Transactional
public class GpsysteminteractiveService {

    @Autowired
    private GpsysteminteractivelogDao gpsysteminteractivelogDao;

    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public String insertGpsysteminteractiveLog(GpsysteminteractivelogVo gpsysteminteractivelogVo){
        Gpsysteminteractivelog gpsysteminteractivelog= BeanCopyUtils.clone(gpsysteminteractivelogVo, Gpsysteminteractivelog.class);
        gpsysteminteractivelog.setRequestmessage(this.truncateString(gpsysteminteractivelog.getRequestmessage(), 3000));
        gpsysteminteractivelog.setResponsemessage(this.truncateString(gpsysteminteractivelog.getResponsemessage(), 3000));

        String requestId = UUID.randomUUID().toString().substring(0, 30);
        gpsysteminteractivelog.setRequestid(requestId);
        try {
            gpsysteminteractivelogDao.insertSelective(gpsysteminteractivelog);
        } catch (Exception e) {
            if (log.isInfoEnabled()) {
                log.info("请求和返回日志存储异常：", e);
            }
        }
        return requestId;
    }

    /**
     * 插入日志
     * @param gpsysteminteractivelogVo
     * @return
     */
    public String insertSysteminteractiveLog(GpsysteminteractivelogVo gpsysteminteractivelogVo){
        Gpsysteminteractivelog gpsysteminteractivelog= BeanCopyUtils.clone(gpsysteminteractivelogVo, Gpsysteminteractivelog.class);
        String requestId = UUID.randomUUID().toString().substring(0, 30);
        gpsysteminteractivelog.setRequestid(requestId);
        try {
            gpsysteminteractivelogDao.insertSelective(gpsysteminteractivelog);
        } catch (Exception e) {
            if (log.isInfoEnabled()) {
                log.info("请求和返回日志存储异常：", e);
            }
        }
        return requestId;
    }
    private String truncateString(String message, int iLength) {
        if(message == null ){
           return "";
        }
        byte[] jsonStrBytes = message.getBytes();
        if (jsonStrBytes.length > iLength) {
            jsonStrBytes = Arrays.copyOfRange(jsonStrBytes, 0, iLength);
        }

        return new String(jsonStrBytes);
    }

}
