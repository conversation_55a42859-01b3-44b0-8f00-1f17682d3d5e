package ins.channel.timedtask.service;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import ins.channel.gupolicycopymain.dao.GupolicycopymainDao;
import ins.channel.gupolicycopymain.po.Gupolicycopymain;
import ins.channel.gupolicycopymain.vo.SelectSettlementVo;
import ins.channel.gupolicylog.dao.GupolicylogDao;
import ins.channel.gupolicylog.po.Gupolicylog;
import ins.channel.policycopymain.service.PolicyCopyMainService;
import ins.channel.policycopymain.service.ShowSettlementService;
import ins.channel.policycopymain.vo.DeclarationQuoteRespVo;
import ins.channel.policycopymain.vo.ToolsVo;
import ins.framework.exception.BusinessException;
import ins.framework.exception.PermissionException;
import ins.platform.utils.UserPermitDataHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TimedtaskService {

    @Resource
    private GupolicylogDao gupolicylogDao;
    @Autowired
    private PolicyCopyMainService policyCopyMainService;
    @Autowired
    private ShowSettlementService showSettlementService;
    @Resource
    private GupolicycopymainDao gupolicycopymainDao;

    /**
     * 任务示例（Bean模式）
     */
    @XxlJob("demoJobHandler")
    public Boolean demoJobHandler() throws Exception {
        try {
            XxlJobHelper.log("XXL-JOB, Hello World.");
            for (int i = 0; i < 5; i++) {
                XxlJobHelper.log("beat at:" + i);
                TimeUnit.SECONDS.sleep(2);
                if (i == 2) {
                    throw new BusinessException("测试异常!");
                }
            }
        } catch (Exception e) {
            return XxlJobHelper.handleFail("失败原因: " + e.getMessage());
        }
        return XxlJobHelper.handleSuccess("执行成功!");
        // default success
    }

    /**
     * 任务示例（Bean模式）
     */
    @XxlJob("batchDeclarationJobHandler")
    public Boolean batchDeclarationJobHandler() throws Exception {
        XxlJobHelper.log("====批量申报生成批单定时任务开始=====");
        //查询日志表状态为"0-待申报生成批单"的数据
        Gupolicylog policylogCondition = new Gupolicylog();
        policylogCondition.setResponsestatus("0");
        policylogCondition.setRemark(PolicyCopyMainService.ENDORCOMMONINQUA50);
        List<Gupolicylog> gupolicylogList = gupolicylogDao.selectByCondition(policylogCondition);

        if (CollectionUtils.isEmpty(gupolicylogList)) {
            return XxlJobHelper.handleSuccess("日志表记录数据已全部处理完成!");
        }
        List<String> endorNoList = gupolicylogList.stream().map(Gupolicylog::getId).collect(Collectors.toList());
        DeclarationQuoteRespVo vo = new DeclarationQuoteRespVo();
//        flag 0-批单生成 1-补生成批单
        vo.setFlag("0");
        StringBuilder errorMsg = new StringBuilder();
        StringBuilder successMsg = new StringBuilder();
        int successCount = 0;
        int failCount = 0;
        for (String endorNo : endorNoList) {
            try {
                vo.setEndorNo(endorNo);
                DeclarationQuoteRespVo respVo = policyCopyMainService.declaration(vo);
                if ("500".equals(respVo.getCode())) {
                    errorMsg.append(MessageFormat.format("申报单号:{0} 生成批单定时任务失败!原因: {1}", endorNo, respVo.getMsg())).append("/r/n");
                    failCount++;
                } else if ("200".equals(respVo.getCode())) {
                    successMsg.append(endorNo).append(",");
                    successCount++;
                }
            } catch (Exception e) {
                errorMsg.append(MessageFormat.format("申报单号:{0} 生成批单定时任务失败!原因: {1}", endorNo, e.getMessage())).append("/r/n");
                failCount++;
            }
        }
        if (failCount == endorNoList.size()) {
            log.info(MessageFormat.format("====批量申报生成批单定时任务结束,本次共处理{0}条数据!全部失败!申报单号清单:{1} ; 失败原因:{2}=====",gupolicylogList.size(),endorNoList,errorMsg));
            XxlJobHelper.log(MessageFormat.format("====批量申报生成批单定时任务结束,本次共处理{0}条数据!全部失败!申报单号清单:{1}; 失败原因:{2}=====",gupolicylogList.size(),endorNoList,errorMsg));
            return XxlJobHelper.handleFail("批量申报生成批单定时任务失败!");
        }
        log.info(MessageFormat.format("====批量申报生成批单定时任务结束,本次共处理{0}条数据!成功{1}条,申报成功单号清单:{2}; 申报失败信息:{3}=====",gupolicylogList.size(),successCount,successMsg,errorMsg));
        XxlJobHelper.log(MessageFormat.format("====批量申报生成批单定时任务结束,本次共处理{0}条数据!成功{1}条,申报成功单号清单:{2},申报失败信息: {3}=====",gupolicylogList.size(),successCount,successMsg,errorMsg));
        return XxlJobHelper.handleSuccess("批量申报生成批单定时任务成功!");
    }


    /**
     * 任务示例（Bean模式）
     * Settlement timing task
     */
    @XxlJob("settlementTimingTask")
    public boolean settlementTimingTask(){
        XxlJobHelper.log("====同步申报单结算结果定时任务开始=====");
        StringBuilder errorMsg = new StringBuilder();
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureRecord = new StringBuilder();
        int successCount = 0;
        int failCount = 0;
        ToolsVo toolsVo = new ToolsVo();
        toolsVo.setFlag("-1");
        List<Gupolicycopymain> gupolicycopymainList = gupolicycopymainDao.ShowEndorseqnoPolicynoEndorno();
        for (int i = 0; i<gupolicycopymainList.size();i++){
            toolsVo.setGupolicycopymainvo(gupolicycopymainList.get(i));
            try {
                 showSettlementService.showSettlementInterface(toolsVo);
                successMsg.append(gupolicycopymainList.get(i).getEndorNo()).append(",");
                successCount++;
            }catch (Exception e){
                failCount++;
                failureRecord.append(gupolicycopymainList.get(i).getEndorNo()).append(",");
                errorMsg.append(MessageFormat.format("====同步申报单结算结果任务失败：异常原因：{0}",e.getMessage()));
                log.info(MessageFormat.format("========同步申报单结算结果任务失败：异常原因：{0}",errorMsg));
            }
        }
        if (failCount == gupolicycopymainList.size()) {
            log.info(MessageFormat.format("====同步申报单结算结果定时任务结束,本次共处理{0}条数据!全部失败!失败申报单号:{1} ; 失败原因:{2}=====",gupolicycopymainList.size(),failureRecord,errorMsg));
            XxlJobHelper.log(MessageFormat.format("====同步申报单结算结果定时任务结束,本次共处理{0}条数据!全部失败!失败申报单号:{1}; 失败原因:{2}=====",gupolicycopymainList.size(),failureRecord,errorMsg));
            return XxlJobHelper.handleFail("同步申报单结算结果定时任务失败!");
        }
        log.info(MessageFormat.format("====同步申报单结算结果定时任务结束,本次共处理{0}条数据!成功{1}条,结算成功单号:{2}; 结算失败原因:{3}=====",gupolicycopymainList.size(),successCount,successMsg,errorMsg));
        XxlJobHelper.log(MessageFormat.format("====同步申报单结算结果定时任务结束,本次共处理{0}条数据!成功{1}条,结算成功单号:{2}; 结算失败原因:{3}=====",gupolicycopymainList.size(),successCount,successMsg,errorMsg));
        return XxlJobHelper.handleSuccess("同步申报单结算结果定时任务成功!");
    }

}
