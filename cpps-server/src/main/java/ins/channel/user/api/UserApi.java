package ins.channel.user.api;

import com.sinosoft.power.api.SaaPowerApi;
import ins.channel.user.service.GguserService;
import ins.channel.user.vo.GguserSerachRequestVo;
import ins.channel.user.vo.GguserVo;
import ins.channel.user.vo.UserModifyRequestVo;
import ins.channel.user.vo.UserAddRequestVo;
import ins.platform.common.PageResult;
import ins.platform.common.ResponseVo;
import ins.platform.common.SysUser;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.JwtAuthHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by sino on 2019/9/6.
 */
@RestController
@RequestMapping("/api/user")
@Api(tags = "User", description = "用户管理服务")
public class UserApi {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private GguserService service;

    @Value("${administrator.usercode:000000}")
    private String adminCode;

    /*@ApiOperation(value = "查询用户")
    @PostMapping("/query")
    public ResponseVo<ResultPage<UserQueryVo>> queryUser(@ModelAttribute UserConditionVo vo) {
        return ResponseVo.ok(service.query(vo));
    }*/

    @Autowired
    private JwtAuthHelper authHelper;

    @ApiOperation(value = "分页模糊查询用户")
    @PostMapping(value = "/pageByCondition")
    public ResponseVo<PageResult<UserModifyRequestVo>> pageByCondition(@Valid @ModelAttribute GguserSerachRequestVo vo,@RequestHeader("Authorization") String token) {
        SysUser sysUser = authHelper.getSysUser(token);
        //TODO 有待商榷  是否需要去数据库再查一遍
        GguserVo gguserVo = service.queryOne(sysUser.getUserCode());
        // 1-管理员
        if(!"1".equals(gguserVo.getUserInd()))vo.setUserInd(gguserVo.getUserInd());
        PageResult<UserModifyRequestVo> result = service.pageByCondition(vo);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "根据用户代码查询待修改的用户")
    @PostMapping(value = "/selectOne")
    public ResponseVo<UserModifyRequestVo> selectOne(@RequestBody GguserSerachRequestVo vo,@RequestHeader("Authorization") String token) {
        SysUser sysUser = authHelper.getSysUser(token);
        if(vo.getUserCode() == null || "".equals(vo.getUserCode()))vo.setUserCode(sysUser.getUserCode());
        GguserVo gguserVo = service.queryOne(vo.getUserCode());
        UserModifyRequestVo result = BeanCopyUtils.clone(gguserVo, UserModifyRequestVo.class);
        //处理密码有效时间区间
        List<Date> dates = new ArrayList<Date>();
        dates.add(gguserVo.getPasswordSetDate());
        dates.add(gguserVo.getPasswordExpireDate());
        result.setPasswordDateList(dates);
        return ResponseVo.ok(result);
    }

    @ApiOperation(value = "新增用户")
    @PostMapping(value = "/save")
    public ResponseVo<String> save(@Valid @RequestBody UserAddRequestVo vo,@RequestHeader("Authorization") String token) {
        SysUser sysUser = authHelper.getSysUser(token);
        if(!adminCode.contains(sysUser.getUserCode())){
            logger.info("未配置管理员账号,不能新增用户! 已配置的管理员账号: {}",adminCode);
            return ResponseVo.ok();
        }
        return ResponseVo.ok(service.create(vo));
    }

    @ApiOperation(value = "修改用户")
    @PostMapping(value = "/modify")
    public ResponseVo<Void> modify(@RequestBody UserModifyRequestVo vo) {
        service.modify(vo);
        return ResponseVo.ok();
    }

    @ApiOperation(value = "修改登录用户密码")
    @PostMapping(value = "/modifyPassword")
    public ResponseVo<String> modifyPassword(@RequestBody UserAddRequestVo vo) {
        return ResponseVo.ok(service.modifyPassword(vo));
    }

    @ApiOperation(value = "查询用户所属公司")
    @PostMapping(value = "/queryUserOuterCode")
    public ResponseVo<UserModifyRequestVo> queryUserOuterCode(@RequestBody GguserSerachRequestVo vo) {
        GguserVo gguserVo = service.queryOne(vo.getUserCode());
        UserModifyRequestVo result = BeanCopyUtils.clone(gguserVo, UserModifyRequestVo.class);
        //处理密码有效时间区间
        List<Date> dates = new ArrayList<Date>();
        dates.add(gguserVo.getPasswordSetDate());
        dates.add(gguserVo.getPasswordExpireDate());
        result.setPasswordDateList(dates);
        return ResponseVo.ok(result);
    }
}
