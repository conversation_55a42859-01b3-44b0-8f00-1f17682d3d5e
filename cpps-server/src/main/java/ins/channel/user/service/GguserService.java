package ins.channel.user.service;

import ins.channel.gsclientmain.dao.GsclientmainDao;
import ins.channel.gsclientmain.po.Gsclientmain;
import ins.channel.user.vo.UserModifyRequestVo;
import ins.channel.user.vo.UserAddRequestVo;
import ins.framework.exception.BusinessException;
import ins.framework.mybatis.MybatisApiUtils;
import ins.framework.mybatis.Page;
import ins.framework.mybatis.PageParam;
import ins.channel.power.dao.SaausergradeDao;
import ins.channel.power.dao.SaauserpermitdataDao;
import ins.channel.user.dao.GguserDao;
import ins.channel.user.po.Gguser;
import ins.channel.user.vo.GguserSerachRequestVo;
import ins.channel.user.vo.GguserVo;
import ins.platform.common.PageResult;
import ins.platform.utils.BeanCopyUtils;
import ins.platform.utils.PageHelper;
import ins.platform.utils.PasswordUtils;
import ins.platform.utils.SessionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.*;

/**
 * Created by sino on 2019/9/5.
 */
@Service
@Slf4j
@Transactional
public class GguserService {

    @Autowired
    private GguserDao gguserDao;
    @Autowired
    private SaausergradeDao saaUserGradeDao;
    @Autowired
    private SaauserpermitdataDao saauserpermitdataDao;
    @Autowired
    private GsclientmainDao gsclientmainDao;

    @Transactional(readOnly = true)
    public GguserVo queryOne(String userCode) {
        Assert.hasText(userCode, "用户代码不能为空");
        return BeanCopyUtils.clone(gguserDao.selectByPrimaryKey(userCode), GguserVo.class);
    }

    @Transactional
    public String create(UserAddRequestVo vo) {
        //校验入参
        Assert.hasText(vo.getUserCode(), "用户账号不能为空");
        Assert.hasText(vo.getUserCname(), "用户名称不能为空");
        Assert.hasText(vo.getPassword(), "密码不能为空");
        Assert.hasText(vo.getMobile(), "手机号码不能为空");
        Assert.hasText(vo.getEmail(), "邮箱不能为空");
        List<Date> passwordDateList = vo.getPasswordDateList();
        Assert.isTrue(!CollectionUtils.isEmpty(passwordDateList), "密码有效区间不能为空");
        Assert.isTrue(passwordDateList.size() == 2, "时间区间长度有误!");
        Assert.notNull(passwordDateList.get(0), "密码有效时间起期不能为空!");
        Assert.notNull(passwordDateList.get(1), "密码有效时间止期不能为空!");
        Assert.isTrue(passwordDateList.get(0).before(passwordDateList.get(1)), "密码有效时间起期需小于止期!");
        Assert.hasText(vo.getValidInd(), "有效标志不能为空");
        Assert.hasText(vo.getUserInd(), "用户职级不能为空");
        Assert.hasText(vo.getOuterCode(), "所属公司不能为空");
        //根据所属公司代码获取所属公司名称
        String teamManager = gsclientmainDao.translate(vo.getOuterCode());
        Assert.hasText(teamManager, "所属公司代码无效或未配置");
        int i = 0;
        vo.setPasswordSetDate(passwordDateList.get(0));
        vo.setPasswordExpireDate(passwordDateList.get(1));
        //判断是否有该用户
        Gguser gguser = new Gguser();
        gguser.setUserCode(vo.getUserCode());
        List<Gguser> userCodeList = gguserDao.selectByCondition(gguser);
        if (!CollectionUtils.isEmpty(userCodeList)) {
            throw new BusinessException("账号已存在!");
        }
        gguser.setUserCode(null);
        gguser.setUserCname(vo.getUserCname());
        List<Gguser> userNameList = gguserDao.selectByCondition(gguser);
        if (!CollectionUtils.isEmpty(userNameList)) {
            throw new BusinessException("名称已存在!");
        }
        String voPassword = vo.getPassword();
        //添加用户
        Gguser clone = BeanCopyUtils.clone(vo, Gguser.class);
        //MD5加密
        clone.setPassword(PasswordUtils.encodePassword(voPassword));
        clone.setCreatorCode(SessionHelper.getLoginUser().getUserCode());
        clone.setCreateTime(new Date());
        //TODO 归属机构代码 默认存01
        clone.setCompanyCode("01");
        //TODO 出单机构代码 默认存01
        clone.setIssueCompany("01");
        clone.setTeamManager(teamManager);
        try {
            i = gguserDao.insertSelective(BeanCopyUtils.clone(clone, Gguser.class));
        } catch (Exception e) {
            log.info(MessageFormat.format("新增用户异常:{0}", e.getMessage()));
        }
        return i == 1 ? "保存成功!" : "保存失败!网络连接异常,请稍后再试或联系运维人员处理";
    }

    public void modify(UserModifyRequestVo vo) {
        //校验入参
        Assert.hasText(vo.getUserCode(), "用户账号不能为空");
        Assert.hasText(vo.getUserCname(), "用户名称不能为空");
        Assert.hasText(vo.getMobile(), "手机号码不能为空");
        Assert.hasText(vo.getEmail(), "邮箱不能为空");
        List<Date> passwordDateList = vo.getPasswordDateList();
        Assert.isTrue(!CollectionUtils.isEmpty(passwordDateList), "密码有效区间不能为空");
        Assert.isTrue(passwordDateList.size() == 2, "时间区间长度有误!");
        Assert.notNull(passwordDateList.get(0), "密码有效时间起期不能为空!");
        Assert.notNull(passwordDateList.get(1), "密码有效时间止期不能为空!");
        Assert.isTrue(passwordDateList.get(0).before(passwordDateList.get(1)), "密码有效时间起期需小于止期!");
        Assert.hasText(vo.getValidInd(), "有效标志不能为空");
        Assert.hasText(vo.getUserInd(), "用户职级不能为空");
        Assert.hasText(vo.getOuterCode(), "所属公司不能为空");
        //根据所属公司代码获取所属公司名称
        String teamManager = gsclientmainDao.translate(vo.getOuterCode());
        Assert.hasText(teamManager, "投保公司代码无效或未配置");
        int i = 0;
        vo.setPasswordSetDate(passwordDateList.get(0));
        vo.setPasswordExpireDate(passwordDateList.get(1));
        //判断是否有该用户
        Gguser gguser = new Gguser();
        gguser.setUserCode(vo.getUserCode());
        List<Gguser> userCodeList = gguserDao.selectByCondition(gguser);
        if (CollectionUtils.isEmpty(userCodeList)) {
            throw new BusinessException("用户不存在!");
        }
        //更新用户
        gguser.setUserCode(vo.getUserCode());
        //MD5加密
//        String voPassword = vo.getPassword();
//        gguser.setPassword(PasswordUtils.encodePassword(voPassword));
        gguser.setUpdaterCode(SessionHelper.getLoginUser().getUserCode());
        gguser.setUpdateTime(new Date());
        gguser.setUserCname(vo.getUserCname());
        gguser.setSeal(vo.getSeal());
        gguser.setPasswordSetDate(vo.getPasswordSetDate());
        gguser.setPasswordExpireDate(vo.getPasswordExpireDate());
        gguser.setOuterCode(vo.getOuterCode());
        gguser.setMobile(vo.getMobile());
        gguser.setPhone(vo.getPhone());
        gguser.setEmail(vo.getEmail());
        gguser.setAddress(vo.getAddress());
        gguser.setValidInd(vo.getValidInd());
        gguser.setRemark(vo.getRemark());
        gguser.setUserInd(vo.getUserInd());
        gguser.setTeamManager(teamManager);
        gguserDao.updateSelectiveByPrimaryKey(gguser);

        if ("0".equals(gguser.getValidInd())) {
            //如果用户修改为无效
            // 将用户对应的角色记录置为无效
            Map<String, Object> map = new HashMap<>();
            String userCode = gguser.getUserCode();
            map.put("userCode", userCode);
            saaUserGradeDao.invalidGradeByUserCode(map);
            // 将用户关联机构表中对应的记录置为无效
            saauserpermitdataDao.invalidDataByUserCode(map);
        }
    }

    public String modifyPassword(UserAddRequestVo vo) {
        int i = 0;
        //校验入参
        Assert.hasText(vo.getPassword(), "原密码不能为空");
        Assert.hasText(vo.getNewpassword(), "新密码不能为空");
        String userCode = SessionHelper.getLoginUser().getUserCode();
        //根据登录用户查询数据库,校验原密码是否一致
        Gguser gguser = gguserDao.selectByPrimaryKey(userCode);
        if (!gguser.getPassword().equals(PasswordUtils.encodePassword(vo.getPassword()))) {
            throw new BusinessException("原密码错误!请确认后再尝试!");
        }

        Gguser user = new Gguser();
        user.setUserCode(userCode);
        user.setPassword(PasswordUtils.encodePassword(vo.getNewpassword()));
        user.setUpdaterCode(userCode);
        user.setUpdateTime(new Date());

        try {
            i = gguserDao.updateSelectiveByPrimaryKey(user);
        } catch (Exception e) {
            log.info(MessageFormat.format("{0}用户修改密码异常:{0}", gguser.getUserCode(), e.getMessage()));
        }
        return i == 1 ? "保存成功!" : "修改密码失败!网络连接异常,请稍后再试或联系管理员处理";
    }

    public int delete(List<String> ids) {
        Assert.notNull(ids, "对象不能为null");
        Assert.notEmpty(ids, "列表不能为空");
        int delCount = 0;
        Gguser user = null;
        for (String userCode : ids) {
            // 逻辑删除用户
            user = new Gguser();
            user.setUserCode(userCode);
            user.setValidInd("0");
            gguserDao.updateSelectiveByPrimaryKey(user);
            Map<String, Object> map = new HashMap<>();
            map.put("userCode", userCode);
            // 删除用户对应的角色数据
            saaUserGradeDao.invalidGradeByUserCode(map);
            // 删除用户对应的权限数据
            saauserpermitdataDao.invalidDataByUserCode(map);
            delCount++;
        }
        return delCount;
    }

    /**
     * 分页模糊查询用户
     *
     * @param gguserSerachRequestVo
     * @return
     */
    public PageResult<UserModifyRequestVo> pageByCondition(GguserSerachRequestVo gguserSerachRequestVo) {
        PageParam pageParam = MybatisApiUtils.getPageParam();
        Gguser gguser = BeanCopyUtils.clone(gguserSerachRequestVo, Gguser.class);
        Page<Gguser> ggusers = gguserDao.pageByCondition(pageParam, gguser);
        return PageHelper.convert(pageParam, ggusers, UserModifyRequestVo.class);
    }

}
