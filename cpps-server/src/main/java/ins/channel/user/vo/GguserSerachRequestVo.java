package ins.channel.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "用户模糊查询表")
@Data
public class GguserSerachRequestVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 对应字段：USER_CODE */
    @ApiModelProperty("用户账号")
    private String userCode;
    /** 对应字段：USER_CNAME */
    @ApiModelProperty("用户名称")
    private String userCname;
    /** 对应字段：VALID_IND */
    @ApiModelProperty("有效标志 0-无效 1-有效")
    private String validInd;
    /** 对应字段：USER_IND */
    @ApiModelProperty("职级")
    private String userInd;

}
