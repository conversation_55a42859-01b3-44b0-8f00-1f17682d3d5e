package ins.channel.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "权限配置用户查询条件")
@Data
@SuppressWarnings("serial")
public class UserConditionVo implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "用户代码")
	private String userCode;

	@ApiModelProperty(value = "用户姓名")
	private String userName;

	@ApiModelProperty(value = "机构集合")
	private List<String> companyCodeList;

}
