package ins.channel.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * UserModifyRequestVo 对象.对应实体描述：用户查询/修改入参Vo对象
 *
 */
@Data
@ApiModel("UserModifyRequestVo 对象")
public class UserModifyRequestVo implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("用户账号")
	private String userCode;

	@ApiModelProperty("用户名称")
	private String userCname;

	@ApiModelProperty("印鉴")
	private String seal;

	@ApiModelProperty("密码设置时间")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date passwordSetDate;

	@ApiModelProperty("密码过期日期")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date passwordExpireDate;

	@ApiModelProperty("密码有效日期集合")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private List<Date> passwordDateList;


	@ApiModelProperty("所属公司名称")
	private String teamManager;

	@ApiModelProperty("手机号码")
	private String mobile;

	@ApiModelProperty("固定电话")
	private String phone;

	@ApiModelProperty("邮箱")
	private String email;

	@ApiModelProperty("通信地址")
	private String address;

	@ApiModelProperty("有效标志   1-有效；0-无效")
	private String validInd;

	@ApiModelProperty("备注")
	private String remark;

	/** 对应字段：USER_IND */
	@ApiModelProperty("职级 1:管理员 2:业务管理岗 3:业务员 codeType ='UserRank'")
	private String userInd;

	/** 对应字段：OUTER_CODE */
	@ApiModelProperty("所属公司代码")
	private String outerCode;

	/** 对应字段：department */
	@ApiModelProperty("所属部门")
	private String department;
}
