package ins.channel.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "权限配置用户查询结果")
@Data
@SuppressWarnings("serial")
public class UserQueryVo implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "用户代码")
	private String userCode;

	@ApiModelProperty(value = "用户名称")
	private String userName;

	@ApiModelProperty(value = "机构代码")
	private String companyCode;

	@ApiModelProperty(value = "是否有效 0无效 1有效")
	private String validStatus;;

}
