package ins.channel.utils;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description: 申报人员身份证号校验
 * @author: ZhouTaoyu
 * @date: 2021/03/01
 */
@Slf4j
public class ApplicantIDCardCheck {
    /**
     * @description: 申报人员身份证号校验公共方法
     * @param: certiType :证件类型
     * @param: certiNo : 证件号码
     * @param: type : 申报人员类型 1-个人  2-企业
     * @param: name: 申报人员名称
     * @author: zhoutaoyu
     * @date: 2021/3/1
     * @return:
     **/
    public static String AppCertiNo(String certiType, String certiNo, String type, String name)  {
        String resposemsg = "ok";
        if ("1".equals(type)) {
            if (certiNo.length() < 4) {
                resposemsg = "申报人[" + name + "]证件号码必须大于四位！";
            }
            if ("01".equals(certiType)) {//身份证号
                if (certiNo.length() != 18 && certiNo.length() != 15) {
                    resposemsg = "申报人[" + name + "]证件号位数不对!应满足15位或18位!";
                }
            }
            // 申报人是个人证件类型
            switch (certiType) {
                case "01":// 居民身份证：校验长度为15位或18位，18位可带大写X或者小写x
                    if (!IDCardCheckUtils.validateCard(certiNo)) {
                        resposemsg = "申报人[" + name + "]身份证号码有误！";
                    }
                    break;
                case "02":// 护照：校验证件号7-20个字符(一个汉字代表两个字符)
                    if (!IDCardCheckUtils.verifyInsLength(certiNo, 7, 20)) {
                        resposemsg = "申报人[" + name + "]护照号码有误！";
                    }
                    break;
                case "03":// 军人证：校验证件号为3-5个汉字+6-12个数字
                    if (!IDCardCheckUtils.verifyArmyId(certiNo)) {
                        resposemsg = "申报人[" + name + "]军人证号码不正确!";
                    }
                    break;
                case "04":// 驾驶证：校验证件号7-20个字符(一个汉字代表两个字符)
                    if (!IDCardCheckUtils.verifyInsLength(certiNo, 7, 20)) {
                        resposemsg = "申报人[" + name + "]驾驶证号码有误！";
                    }
                    break;
                case "05":// 回乡证或港澳台同胞证：校验证件号8-20个字符(一个汉字代表两个字符)
                    if (!IDCardCheckUtils.verifyInsLength(certiNo, 8, 20)) {
                        resposemsg = "申报人[" + name + "]回乡证或港澳台同胞证号码有误！";
                    }
                    break;
                default:
                    break;
            }
        } else {
            if (certiNo.length() < 4) {
                resposemsg = "申报人[" + name + "]证件号码必须大于四位！";
            }
            // 申报人是企业证件类型
            switch (certiType) {
                case "1":// 组织机构代码证：格式为“8位数字”+“-”+“1位数字”，例如00000000-0；
                    if (!IDCardCheckUtils.verifyOrganization(certiNo)) {
                        resposemsg = "申报人[" + name + "]组织机构代码证有误！";
                    }
                    break;
                case "2":// 税务登记证：校验证件号7-20个字符；
                    if (!IDCardCheckUtils.verifyInsLength(certiNo, 7, 20)) {
                        resposemsg = "申报人[" + name + "]税务登记证有误！";
                    }
                    break;
                case "3":// 营业执照：校验证件号7-20个字符；
                    if (!IDCardCheckUtils.verifyInsLength(certiNo, 7, 20)) {
                        resposemsg = "申报人[" + name + "]营业执照不正确!";
                    }
                    break;
                default:
                    break;
            }
        }
        return resposemsg;
    }

    /**
     * 校验证件
     *
     * @param insuredName
     * @return
     * @throws Exception
     */
    public static String CheckApplicantName(String insuredName, String message) throws Exception {
        String resposemsg = "";
        Pattern p = Pattern.compile("[\\u4e00-\\u9fa5]");
        Matcher m = p.matcher(insuredName);
        if (m.find()) {//含有汉字
            if (insuredName.contains(" ") || insuredName.contains(" ") || insuredName.contains(" ")) {
                resposemsg = message + "[" + insuredName + "]录入有误，中文名称中不能有空格，请修改！";
            }
            if (Pattern.matches(".*\\d+.*", insuredName) || Pattern.matches(".*[A-Za-z]+.*", insuredName)) {
                resposemsg = message + "[" + insuredName + "]录入有误，中文名称中只能录入中文和字符，不能录入数字和字母！";
            }
            if (Pattern.matches("[\\u4e00-\\u9fa5]", insuredName) && insuredName.length() < 2) {//为汉字，并且长度小于2时校验
                resposemsg = message + "[" + insuredName + "]录入有误，中文名称大于等于2个汉字长度！";
            }
        }
        return resposemsg;
    }

    public static void main(String[] args) {
        String name = "/";
        boolean matches = Pattern.matches(".*\\d+.*", name) || Pattern.matches(".*[A-Za-z]+.*", name);
        System.out.println("姓名:"+name+"==== 是否通过校验:"+matches);
    }
}
