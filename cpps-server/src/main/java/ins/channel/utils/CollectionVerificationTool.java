package ins.channel.utils;

import ins.framework.exception.BusinessException;
import ins.channel.utils.annotation.Consistency;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 集合校验工具类
 * <AUTHOR>
 * @date 2019-11-16
 */
public class CollectionVerificationTool {
    /**
     * 校验集合数据对象部分属性值的一致性
     * 需在Field上面加Consistency注解
     * @param list
     * @return true-一致，false-不一致
     */
    public static boolean consistencyCheck(List<? extends Object> list){
        if(null!=list&&list.size()>0){
            try {
                Map<String,Object> map=new HashMap<>();
                List<String> fields=new ArrayList();
                Class<?> clazz=list.get(0).getClass();
                for(int i=0;i<list.size();i++){
                    Object obj = list.get(i);
                    if(i==0){
                        Field[] declaredFields = clazz.getDeclaredFields();
                        for(Field field : declaredFields){
                            if(null!=field.getAnnotation(Consistency.class)){
                                field.setAccessible(true);
                                fields.add(field.getName());
                                map.put(field.getName(),field.get(obj));
                            }
                        }
                        if(fields.size()==0)return false;
                    }else{
                        for(Iterator<String> iterator=fields.iterator();iterator.hasNext();){
                            String fieldName=iterator.next();
                            Field field = clazz.getDeclaredField(fieldName);
                            field.setAccessible(true);
                            if(null==map.get(fieldName)){
                                if(null!=field.get(obj)){
                                    return false;
                                }
                            }else{
                                if(!map.get(fieldName).equals(field.get(obj))){
                                    return false;
                                }
                            }
                        }
                    }
                }
                return true;
            } catch (NoSuchFieldException | IllegalAccessException e) {
                e.printStackTrace();
                throw new BusinessException("集合数据一致性校验异常");
            }
        }
        return false;
    }
}
