package ins.channel.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
@ComponentScan("ins.channel.utils")
@Slf4j
public class CommonUtil<T> {


    /**
     * 将list拆成指定长度的list
     * @param targe
     * @param size
     * @return
     */
    public  List<List<T>>  createList(List<T> targe, int size) {
        List<List<T>> listArr = new ArrayList<>();
        log.info("拆分对象长度："+targe.size()+"/拆分成的长度"+size);
        if(targe.size()<=size){
            listArr.add(targe);
            return listArr;
        }
        //获取被拆分的数组个数
        int arrSize = targe.size()%size==0?targe.size()/size:targe.size()/size+1;
        for(int i=0;i<arrSize;i++) {
            List<T>  sub = new ArrayList<>();
            //把指定索引数据放入到list中
            for(int j=i*size;j<=size*(i+1)-1;j++) {
                if(j<=targe.size()-1) {
                    //得到拆分后的集合
                    sub.add(targe.get(j));
                }
            }
            listArr.add(sub);
        }
        log.info("拆分对象结束!共拆分成"+arrSize+"个对象");
        return listArr;
    }
}
