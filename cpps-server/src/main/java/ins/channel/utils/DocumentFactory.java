package ins.channel.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * 雪花号码的生成工厂类
 * 
 * <AUTHOR>
 * @date: 2021/01/25
 * @version 1.0
 */
public class DocumentFactory{

	private static volatile ExecutorService threadPool = null;

	// 线程池维护线程的最大数量
	private static final int SIZE_MAX_POOL = 20;

	private DocumentFactory() {
	}

	/**
	 * 获得指定长度的线程池
	 *
	 * @param count
	 * @return
	 */
	public static ExecutorService getThreadPool(int count) {
		if (threadPool == null) {
			synchronized (ExecutorService.class) {
				if (threadPool == null) {
					count = count > 20 ? SIZE_MAX_POOL : count;
					threadPool = Executors.newFixedThreadPool(count);
				}
			}
		}
		return threadPool;
	}

	/**
	 * 获得默认长度的线程池
	 *
	 * @return
	 */
	public static ExecutorService getThreadPool() {
		return getThreadPool(10);
	}

	/**
	 * 获得一个雪花号码
	 * 
	 * @param callable
	 * @return
	 */
	public static String getSnowflakeNo(Callable<String> callable) {
		threadPool = getThreadPool(5);
		try {
			if (!threadPool.isShutdown()) {
				Future<String> future = threadPool.submit(callable);
				return future.get();
			}
		} catch (InterruptedException | ExecutionException e) {
			e.printStackTrace();
		} finally {
			// threadPool.shutdown();
		}
		return null;
	}

	/**
	 * 获得多个雪花号码
	 * 
	 * @param callableList
	 * @return
	 */
	public static List<String> getBatchSnowflakeNo(List<Callable<String>> callableList) {
		threadPool = getThreadPool(5);
		try {
			List<String> orderCodeList = new ArrayList<String>();
			for (int i = 0; i < callableList.size(); i++) {
				if (!threadPool.isShutdown()) {
					Future<String> future = threadPool.submit(callableList.get(i));
					orderCodeList.add(future.get());
				}
			}
			return orderCodeList;
		} catch (InterruptedException | ExecutionException e) {
			e.printStackTrace();
		} finally {
			// threadPool.shutdown();
		}
		return null;
	}

}
