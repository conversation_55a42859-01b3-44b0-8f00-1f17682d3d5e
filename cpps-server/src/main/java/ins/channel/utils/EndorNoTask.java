//package ins.channel.utils;
//
//
//import java.lang.management.ManagementFactory;
//import java.util.concurrent.Callable;
//
///**
// * 获取批改申请号任务类(弃用)
// *
// * <AUTHOR>
// * @date 2020.01.25
// * @version 1.0
// */
//public class EndorNoTask implements Callable<String> {
//		private static final Integer pre = 10000000;
//
//	@Override
//	public synchronized String call() throws Exception {
//		//获取JVM进程PID
//		String pid = ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
//		//补足位数
//		String prePid = String.format("%08d",Integer.parseInt(pid)+pre);
//		String id = IdsGen.BUSSINESS.getIdGenStr();
//		return String.format("%s%s", prePid,id);
//	}
//
//}
