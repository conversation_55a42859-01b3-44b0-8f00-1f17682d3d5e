package ins.channel.utils;

/**
 * 雪花号码生成器
 *
 * <AUTHOR>
 * @date: 2021/01/25
 * @version 1.0
 */
public enum IdsGen {

	/**
	 * 基础公共
	 */
	BASIC(0),

	/**
	 * 业务服务
	 */
	BUSSINESS(1),

	/**
	 * 其它
	 */
	OTHER(255);

	private SnowflakeIdGenerator snowflakeIdGenerator;

	IdsGen(final int service) {
		snowflakeIdGenerator = new SnowflakeIdGenerator(service);

	}

	public long getIdGen() {
		return snowflakeIdGenerator.nextId();
	}

	public String getIdGenStr() {
		return String.valueOf(snowflakeIdGenerator.nextId());
	}
}
