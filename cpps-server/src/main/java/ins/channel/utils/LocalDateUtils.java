package ins.channel.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * JDK1.8时间格式化工具类 
 * <AUTHOR>
 * @date 2021年02月20日 18:53:12
 */
public class LocalDateUtils {

    private static final String PATTERN_DATE = "yyyy-MM-dd";
    private static final String PATTERN_DATE_COMPACT = "yyyyMMdd";
    private static final String PATTERN_DATE_TIME = "yyyy-MM-dd HH:mm:ss";
    private static final String PATTERN_DATE_TIME_COMPACT = "yyyyMMddHHmmss";
    //兼容性时间格式
    public static final String LOCAL_DATE_TIME_S = "yyyy-M-d H:m:s.S";

    /**
     * 格式化时间为LocalDateTime
     * @param dateStr 时间字符串 格式yyyy-M-d H:m:s.S
     * @Return
     * <AUTHOR>
     * @date 2021年02月20日 18:51:34
     */
    public static LocalDateTime parseLocalDateTime(String dateStr,String templateStr){
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(templateStr);
        LocalDateTime ldt = LocalDateTime.parse(dateStr,dtf);
        return ldt;
    }

    /**
     * 根据localDateTime组装成yyyy年M月D日格式
     * @param localDateTime 时间
     * @Return yyyy年M月D日格式字符串
     * <AUTHOR>
     * @date 2021年02月20日 18:50:43
     */
    public static String getCHYMD(LocalDateTime localDateTime){
        return localDateTime.getYear()+"年"+localDateTime.getMonthValue()+"月"+localDateTime.getDayOfMonth()+"日";
    }

    public static String getCHYMD(String dateStr,String templateStr){
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(templateStr);
        LocalDateTime ldt = LocalDateTime.parse(dateStr,dtf);
        return getCHYMD(parseLocalDateTime(dateStr,templateStr));
    }

}
