package ins.channel.utils;


import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class RSAUtil {

    public static void main(String[] args) throws NoSuchAlgorithmException {

        Provider[] providers = Security.getProviders();
        for (Provider provider : providers) {
            System.out.println(provider.getName());
        }


        //实例化密钥生成器
//        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
//        //初始化密钥生成器
//        keyPairGenerator.initialize(1024);
//        //生成密钥对
//        KeyPair keyPair = keyPairGenerator.generateKeyPair();
//        //甲方公钥
//        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
//        //甲方私钥
//        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
//
//        System.out.println("公钥");
//        System.out.println(new String(Base64.getEncoder().encode(publicKey.getEncoded())));
//
//        System.out.println("私钥");
//        System.out.println(new String(Base64.getEncoder().encode(privateKey.getEncoded())));

        try {
//            byte[] bytes = encryptByPublicKey("{password: '1234!@#$', validateCode: '1', usercode: '000000'}".getBytes(StandardCharsets.UTF_8), publicKey.getEncoded());
//
//            System.out.println(Base64.getEncoder().encodeToString(bytes));
//
//            byte[] bytes1 = decryptByPrivateKey(bytes, privateKey.getEncoded());
//
//            System.out.println(new String(bytes1,"utf-8"));

            String data = "{\"test\":\"111111\"}";

            String prk = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCT7SS4goWpdX2bWEHz2hfJXjnVxfk61R6ZaVD8zewIRCjQLKJ47wYdJtz8xg/Iik7AsOttrlvUHX5ARCQgl9n13W4keLaU7A4A1n6zpfniRdVrnKQ1CxDBVRUZ1Kubgghg3gXkvG6fjVP7ygdKvkwXBpyOVa0IK7FfBQu3ahJOhwIDAQAB";

            byte[] decryptByPrivateKey = encryptByPublicKey(data.getBytes(), Base64.getDecoder().decode(prk));
            System.out.println(Base64.getEncoder().encodeToString(decryptByPrivateKey));

        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    public static int MAX_ENCRYPT_LENGTH = 117;
    public static int MAX_DECRYPT_LENGTH = 128;

    /**
     * 私钥加密
     *
     * @param data 待加密数据
     * @param key       密钥
     * @return byte[] 加密数据
     */
    public static byte[] encryptByPrivateKey(byte[] data, byte[] key) throws Exception {

        //取得私钥
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(key);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        //生成私钥
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8KeySpec);
        //数据加密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        return a(cipher,data,MAX_ENCRYPT_LENGTH);
    }

    /**
     * 公钥加密
     *
     * @param data 待加密数据
     * @param key       密钥
     * @return byte[] 加密数据
     */
    public static byte[] encryptByPublicKey(byte[] data, byte[] key) throws Exception {

        //实例化密钥工厂
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        //初始化公钥
        //密钥材料转换
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(key);
        //产生公钥
        PublicKey pubKey = keyFactory.generatePublic(x509KeySpec);

        //数据加密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);

        return a(cipher,data,MAX_ENCRYPT_LENGTH);
    }

    /**
     * 私钥解密
     *
     * @param data 待解密数据
     * @param key  密钥
     * @return byte[] 解密数据
     */
    public static byte[] decryptByPrivateKey(byte[] data, byte[] key) throws Exception {
        //取得私钥
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(key);

        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        //生成私钥
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8KeySpec);
        //数据解密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, privateKey);

        return a(cipher,data,MAX_DECRYPT_LENGTH);
    }

    /**
     * 公钥解密
     *
     * @param data 待解密数据
     * @param key  密钥
     * @return byte[] 解密数据
     */
    public static byte[] decryptByPublicKey(byte[] data, byte[] key) throws Exception {

        //实例化密钥工厂
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        //初始化公钥
        //密钥材料转换
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(key);
        //产生公钥
        PublicKey pubKey = keyFactory.generatePublic(x509KeySpec);
        //数据解密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, pubKey);
        return a(cipher,data,MAX_DECRYPT_LENGTH);
    }

    public static byte[] a(Cipher cipher, byte[] data, int length) throws IllegalBlockSizeException, BadPaddingException, IOException {
        ByteArrayOutputStream bo = new ByteArrayOutputStream();
        int dataLength = data.length;
        int offset = 0;
        while (dataLength - offset > length){
            bo.write(cipher.doFinal(data,offset,length));
            offset += length;
        }
        if(dataLength > offset){
            bo.write(cipher.doFinal(data,offset,dataLength - offset));
        }
        return bo.toByteArray();
    }

}
