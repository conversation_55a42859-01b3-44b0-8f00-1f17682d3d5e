package ins.channel.utils;

import java.net.InetAddress;
import java.net.InterfaceAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.text.MessageFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

/**
 * 雪花核心工具
 * <AUTHOR>
 * @date: 2021/01/25
 * @version 1.0
 */
public class SnowflakeIdGenerator {

	private static final long EPOCH;

	/**
	 * 业务线标识id所占的位数
	 **/
	private final long serviceIdBits = 8L;
	/**
	 * 业务线标识支持的最大数据标识id(这个移位算法可以很快的计算出几位二进制数所能表示的最大十进制数)
	 */
	private final long maxServiceId = -1L ^ (-1L << serviceIdBits);
	private final long serviceId;

	/**
	 * 机器id所占的位数
	 **/
	private final static long workerIdBits = 10L;
	/**
	 * 支持的最大机器id
	 */
	private final long maxWorkerId = -1L ^ (-1L << workerIdBits);
	private static long workerId;


	/**
	 * 序列在id中占的位数
	 **/
	private final long sequenceBits = 7L;
	/**
	 * sequence掩码，确保sequnce不会超出上限
	 * 最大的序列号，4096
	 * -1 的补码（二进制全1）右移12位, 然后取反
	 * 生成序列的掩码，这里为4095 (0b111111111111=0xfff=4095)
	 */
	private final long sequenceMask = -1L ^ (-1L << sequenceBits);

	/**
	 * 最大容忍时间, 单位秒, 即如果时钟只是回拨了该变量指定的时间, 那么等待相应的时间即可;
	 */
	private static final long MAX_BACKWARD_MS = 3000;

	/**
	 * 每台workerId服务器有3个备份workerId, 备份workerId数量越多, 可靠性越高, 但是可部署的sequence ID服务越少
	 */
	private final static long backupCount = 3L;
	
	/**
	 * 实际的最大workerId的值<br/>
	 * workerId原则上上限为1024, 但是需要为每台sequence服务预留BACKUP_AMOUNT个workerId,
	 */
	private final static long WORKER_ID_MAX_VALUE = (1L << workerIdBits) / (backupCount + 1);

	static {
		Calendar calendar = Calendar.getInstance();
		calendar.set(2021, Calendar.MARCH, 15);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		// EPOCH是服务器第一次上线时间点, 设置后不允许修改
		EPOCH = calendar.getTimeInMillis();
	}

	/**
	 * 开始时间戳
	 **/
	private final long twepoch = 1569919000000L;
	/**
	 * 最后一次的时间戳
	 **/
	private volatile long lastTimestamp = -1L;
	/**
	 * 毫秒内序列
	 **/
	private volatile long sequence = 0L;
	/**
	 * 随机生成器
	 **/
	private static volatile Random random = new Random();

	/**
	 * 机器id左移位数
	 **/
	private final long workerIdShift = sequenceBits;
	/**
	 * 业务线id左移位数
	 **/
	private final long serviceIdShift = workerIdBits + sequenceBits;
	/**
	 * 时间戳左移位数
	 **/
	private final long timestampLeftShift = serviceIdBits + workerIdBits + sequenceBits;

	/**
	 * 保留workerId和lastTime, 以及备用workerId和其对应的lastTime
	 */
	private static Map<Long, Long> workerIdLastTimeMap = new ConcurrentHashMap<>();

	static {
		for (int i = 0; i <= backupCount; i++) {
			workerIdLastTimeMap.put(workerId + (i * WORKER_ID_MAX_VALUE), 0L);
		}
		System.out.println("workerIdLastTimeMap:" + workerIdLastTimeMap);
	}

	public SnowflakeIdGenerator(long serviceId) {
		if ((serviceId > maxServiceId) || (serviceId < 0)) {
			throw new IllegalArgumentException(
					String.format("service Id can't be greater than %d or less than 0", maxServiceId));
		}
		workerId = getWorkerId();
		if ((workerId > maxWorkerId) || (workerId < 0)) {
			throw new IllegalArgumentException(
					String.format("worker Id can't be greater than %d or less than 0", maxWorkerId));
		}
		this.serviceId = serviceId;
	}

	public synchronized long nextId() {
		long timestamp = System.currentTimeMillis();
		if (timestamp < lastTimestamp) {
			// 如果时钟回拨在可接受范围内, 等待即可
			long offset = lastTimestamp - timestamp;
			if (offset <= MAX_BACKWARD_MS) {
				LockSupport.parkNanos(TimeUnit.MILLISECONDS.toMicros(offset));
				timestamp = System.currentTimeMillis();
				if (timestamp < lastTimestamp) {
					throw new RuntimeException("Clock moved backwards.  Refusing to generate id for "
							+ (lastTimestamp - timestamp) + " milliseconds.");
				}
			} else {
				tryGenerateKeyOnBackup(timestamp);
			}
		}
		// 如果是同一时间生成的，则进行毫秒内序列
		if (lastTimestamp == timestamp) {
			sequence = (sequence + 1) & sequenceMask;
			if (sequence == 0) {
				timestamp = tilNextMillis(lastTimestamp);
			}
		} else {
			// 跨毫秒时，序列号总是归0，会导致序列号为0的ID比较多，导致生成的ID取模后不均匀，所以采用10以内的随机数
			sequence = random.nextInt(10) & sequenceMask;
		}
		// 上次生成ID的时间截（设置最后时间戳）
		lastTimestamp = timestamp;

		  // 更新map中保存的workerId对应的lastTime
        workerIdLastTimeMap.put(workerId, lastTimestamp);

		// 移位并通过或运算拼到一起组成64位的ID
		return ((timestamp - twepoch) << timestampLeftShift) // 时间戳
				| (serviceId << serviceIdShift) // 业务线
				| (workerId << workerIdShift) // 机器
				| sequence; // 序号
	}

	/**
	 * 尝试在workerId的备份workerId上生成
	 * 
	 * @param currentMillis
	 *            当前时间
	 */
	private long tryGenerateKeyOnBackup(long currentMillis) {

		// 遍历所有workerId(包括备用workerId, 查看哪些workerId可用)
		for (Map.Entry<Long, Long> entry : workerIdLastTimeMap.entrySet()) {
			workerId = entry.getKey();
			// 取得备用workerId的lastTime
			Long tempLastTime = entry.getValue();
			lastTimestamp = tempLastTime == null ? 0L : tempLastTime;

			// 如果找到了合适的workerId
			if (lastTimestamp <= currentMillis) {
				return lastTimestamp;
			}
		}

		// 如果所有workerId以及备用workerId都处于时钟回拨, 那么抛出异常
		throw new IllegalStateException("Clock is moving backwards, current time is " + currentMillis
				+ " milliseconds, workerId map = " + workerIdLastTimeMap);
	}

	/**
	 * 等待下一个毫秒的到来, 保证返回的毫秒数在参数lastTimestamp之后 不停获得时间，直到大于最后时间
	 */
	private long tilNextMillis(final long lastTimestamp) {
		long timestamp = System.currentTimeMillis();
		while (timestamp <= lastTimestamp) {
			timestamp = System.currentTimeMillis();
		}
		return timestamp;
	}

	/**
	 * 根据机器的MAC地址获取工作进程Id，也可以使用机器IP获取工作进程Id，取最后两个段，一共10个bit
	 * 极端情况下，MAC地址后两个段一样，产品的工作进程Id会一样；再极端情况下，并发不大时，刚好跨毫秒，又刚好随机出来的sequence一样的话，产品的Id会重复
	 *
	 * @return
	 * @throws Exception
	 */
	protected long getWorkerId() {
		try {
			java.util.Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces();
			while (en.hasMoreElements()) {
				NetworkInterface iface = en.nextElement();
				List<InterfaceAddress> addrs = iface.getInterfaceAddresses();
				for (InterfaceAddress addr : addrs) {
					InetAddress ip = addr.getAddress();
					NetworkInterface network = NetworkInterface.getByInetAddress(ip);
					if (network == null) {
						continue;
					}
					byte[] mac = network.getHardwareAddress();
					if (mac == null) {
						continue;
					}
					long id = ((0x000000FF & (long) mac[mac.length - 1])
							| (0x0000FF00 & (((long) mac[mac.length - 2]) << 8))) >> 11;
					if (id > maxWorkerId) {
						return new Random(maxWorkerId).nextInt();
					}
					return id;
				}
			}
			return new Random(maxWorkerId).nextInt();
		} catch (SocketException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 获取序号
	 *
	 * @param id
	 * @return
	 */
	public static Long getSequence(Long id) {
		String str = Long.toBinaryString(id);
		int size = str.length();
		String sequenceBinary = str.substring(size - 7, size);
		return Long.parseLong(sequenceBinary, 2);
	}

	/**
	 * 获取机器
	 *
	 * @param id
	 * @return
	 */
	public static Long getWorker(Long id) {
		String str = Long.toBinaryString(id);
		int size = str.length();
		String sequenceBinary = str.substring(size - 7 - 10, size - 7);
		return Long.parseLong(sequenceBinary, 2);
	}

	/**
	 * 获取业务线
	 *
	 * @param id
	 * @return
	 */
	public static Long getService(Long id) {
		String str = Long.toBinaryString(id);
		int size = str.length();
		String sequenceBinary = str.substring(size - 7 - 10 - 8, size - 7 - 10);
		return Long.parseLong(sequenceBinary, 2);
	}

}