
package ins.channel.xmlbean.policyvoucher;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="MAIN">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="REQUEST_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="DOCUMENT_SERIALNO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="BUSINESS_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="BUSINESS_NO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="BUSINESS_NO_SEQ" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="BUSINESS_SOURCE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="POLICY_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="PRINT_DATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="EmailFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="SMSFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="accessory" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="HOLDER_INFO">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="HolderEmail" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="HolderMobile" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="PRINT_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="business_Type" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="SQL_SAVE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="ImportFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="MainDto">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="companyCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="startDate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="POLICYRISK">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="riskCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="BaseInfo">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="HEALTHDESC" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="SERVICE_TEL" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="WEBSIT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="Quantity" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="C_OPR_NME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="COMPANY_ADDR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="COMPANY_ZIP" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="JURIS_LIMIT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="PLY_PRINT_TM" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="SERIAL_NO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="N_PRM_ONE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="N_AMT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="INSURANCE_PERIOD" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="RISKCNAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="SURRDISCPROP03" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="PLY_CONFIRM_TM" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="INSURER" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="SIGN_BRANCH" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="COMPANY_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="AppType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="RISKCNAMETITLE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="PARAGRAPHINFO">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="INSUREDCOMPANY" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="KINDNAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="POLICYNO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="INSUREDNAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="INSUREDADDRESS" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="STARTDATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="STARTDATEHOUR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="ENDDATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="ENDDATEHOUR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="INSURERLIST">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="INSURER" maxOccurs="unbounded" minOccurs="0">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="SERIALNUMBER" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="INSUREDNAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="IDENTIFYNUMBER" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="INSURANCEPLAN" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="STARTDATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="ENDDATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="DECLARATIONDATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="PMNAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "main",
    "mainDto",
    "policyrisk",
    "baseInfo",
    "paragraphinfo",
    "insurerlist"
})
@XmlRootElement(name = "INSUREQRET")
public class INSUREQRET {

    @XmlElement(name = "MAIN", required = true)
    protected INSUREQRET.MAIN main;
    @XmlElement(name = "MainDto", required = true)
    protected INSUREQRET.MainDto mainDto;
    @XmlElement(name = "POLICYRISK", required = true)
    protected INSUREQRET.POLICYRISK policyrisk;
    @XmlElement(name = "BaseInfo", required = true)
    protected INSUREQRET.BaseInfo baseInfo;
    @XmlElement(name = "PARAGRAPHINFO", required = true)
    protected INSUREQRET.PARAGRAPHINFO paragraphinfo;
    @XmlElement(name = "INSURERLIST", required = true)
    protected INSUREQRET.INSURERLIST insurerlist;

    /**
     * 获取main属性的值。
     * 
     * @return
     *     possible object is
     *     {@link INSUREQRET.MAIN }
     *     
     */
    public INSUREQRET.MAIN getMAIN() {
        return main;
    }

    /**
     * 设置main属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link INSUREQRET.MAIN }
     *     
     */
    public void setMAIN(INSUREQRET.MAIN value) {
        this.main = value;
    }

    /**
     * 获取mainDto属性的值。
     * 
     * @return
     *     possible object is
     *     {@link INSUREQRET.MainDto }
     *     
     */
    public INSUREQRET.MainDto getMainDto() {
        return mainDto;
    }

    /**
     * 设置mainDto属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link INSUREQRET.MainDto }
     *     
     */
    public void setMainDto(INSUREQRET.MainDto value) {
        this.mainDto = value;
    }

    /**
     * 获取policyrisk属性的值。
     * 
     * @return
     *     possible object is
     *     {@link INSUREQRET.POLICYRISK }
     *     
     */
    public INSUREQRET.POLICYRISK getPOLICYRISK() {
        return policyrisk;
    }

    /**
     * 设置policyrisk属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link INSUREQRET.POLICYRISK }
     *     
     */
    public void setPOLICYRISK(INSUREQRET.POLICYRISK value) {
        this.policyrisk = value;
    }

    /**
     * 获取baseInfo属性的值。
     * 
     * @return
     *     possible object is
     *     {@link INSUREQRET.BaseInfo }
     *     
     */
    public INSUREQRET.BaseInfo getBaseInfo() {
        return baseInfo;
    }

    /**
     * 设置baseInfo属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link INSUREQRET.BaseInfo }
     *     
     */
    public void setBaseInfo(INSUREQRET.BaseInfo value) {
        this.baseInfo = value;
    }

    /**
     * 获取paragraphinfo属性的值。
     * 
     * @return
     *     possible object is
     *     {@link INSUREQRET.PARAGRAPHINFO }
     *     
     */
    public INSUREQRET.PARAGRAPHINFO getPARAGRAPHINFO() {
        return paragraphinfo;
    }

    /**
     * 设置paragraphinfo属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link INSUREQRET.PARAGRAPHINFO }
     *     
     */
    public void setPARAGRAPHINFO(INSUREQRET.PARAGRAPHINFO value) {
        this.paragraphinfo = value;
    }

    /**
     * 获取insurerlist属性的值。
     * 
     * @return
     *     possible object is
     *     {@link INSUREQRET.INSURERLIST }
     *     
     */
    public INSUREQRET.INSURERLIST getINSURERLIST() {
        return insurerlist;
    }

    /**
     * 设置insurerlist属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link INSUREQRET.INSURERLIST }
     *     
     */
    public void setINSURERLIST(INSUREQRET.INSURERLIST value) {
        this.insurerlist = value;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="HEALTHDESC" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="SERVICE_TEL" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="WEBSIT" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="Quantity" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="C_OPR_NME" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="COMPANY_ADDR" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="COMPANY_ZIP" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="JURIS_LIMIT" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="PLY_PRINT_TM" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="SERIAL_NO" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="N_PRM_ONE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="N_AMT" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="INSURANCE_PERIOD" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="RISKCNAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="SURRDISCPROP03" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="PLY_CONFIRM_TM" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="INSURER" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="SIGN_BRANCH" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="COMPANY_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="AppType" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="RISKCNAMETITLE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "healthdesc",
        "servicetel",
        "websit",
        "quantity",
        "coprnme",
        "companyaddr",
        "companyzip",
        "jurislimit",
        "plyprinttm",
        "serialno",
        "nprmone",
        "namt",
        "insuranceperiod",
        "riskcname",
        "surrdiscprop03",
        "plyconfirmtm",
        "insurer",
        "signbranch",
        "companyname",
        "appType",
        "riskcnametitle"
    })
    public static class BaseInfo {

        @XmlElement(name = "HEALTHDESC", required = true)
        protected String healthdesc;
        @XmlElement(name = "SERVICE_TEL", required = true)
        protected String servicetel;
        @XmlElement(name = "WEBSIT", required = true)
        protected String websit;
        @XmlElement(name = "Quantity", required = true)
        protected String quantity;
        @XmlElement(name = "C_OPR_NME", required = true)
        protected String coprnme;
        @XmlElement(name = "COMPANY_ADDR", required = true)
        protected String companyaddr;
        @XmlElement(name = "COMPANY_ZIP", required = true)
        protected String companyzip;
        @XmlElement(name = "JURIS_LIMIT", required = true)
        protected String jurislimit;
        @XmlElement(name = "PLY_PRINT_TM", required = true)
        protected String plyprinttm;
        @XmlElement(name = "SERIAL_NO", required = true)
        protected String serialno;
        @XmlElement(name = "N_PRM_ONE", required = true)
        protected String nprmone;
        @XmlElement(name = "N_AMT", required = true)
        protected String namt;
        @XmlElement(name = "INSURANCE_PERIOD", required = true)
        protected String insuranceperiod;
        @XmlElement(name = "RISKCNAME", required = true)
        protected String riskcname;
        @XmlElement(name = "SURRDISCPROP03", required = true)
        protected String surrdiscprop03;
        @XmlElement(name = "PLY_CONFIRM_TM", required = true)
        protected String plyconfirmtm;
        @XmlElement(name = "INSURER", required = true)
        protected String insurer;
        @XmlElement(name = "SIGN_BRANCH", required = true)
        protected String signbranch;
        @XmlElement(name = "COMPANY_NAME", required = true)
        protected String companyname;
        @XmlElement(name = "AppType", required = true)
        protected String appType;
        @XmlElement(name = "RISKCNAMETITLE", required = true)
        protected String riskcnametitle;

        /**
         * 获取healthdesc属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getHEALTHDESC() {
            return healthdesc;
        }

        /**
         * 设置healthdesc属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setHEALTHDESC(String value) {
            this.healthdesc = value;
        }

        /**
         * 获取servicetel属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSERVICETEL() {
            return servicetel;
        }

        /**
         * 设置servicetel属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSERVICETEL(String value) {
            this.servicetel = value;
        }

        /**
         * 获取websit属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getWEBSIT() {
            return websit;
        }

        /**
         * 设置websit属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setWEBSIT(String value) {
            this.websit = value;
        }

        /**
         * 获取quantity属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getQuantity() {
            return quantity;
        }

        /**
         * 设置quantity属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setQuantity(String value) {
            this.quantity = value;
        }

        /**
         * 获取coprnme属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCOPRNME() {
            return coprnme;
        }

        /**
         * 设置coprnme属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCOPRNME(String value) {
            this.coprnme = value;
        }

        /**
         * 获取companyaddr属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCOMPANYADDR() {
            return companyaddr;
        }

        /**
         * 设置companyaddr属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCOMPANYADDR(String value) {
            this.companyaddr = value;
        }

        /**
         * 获取companyzip属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCOMPANYZIP() {
            return companyzip;
        }

        /**
         * 设置companyzip属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCOMPANYZIP(String value) {
            this.companyzip = value;
        }

        /**
         * 获取jurislimit属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getJURISLIMIT() {
            return jurislimit;
        }

        /**
         * 设置jurislimit属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setJURISLIMIT(String value) {
            this.jurislimit = value;
        }

        /**
         * 获取plyprinttm属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getPLYPRINTTM() {
            return plyprinttm;
        }

        /**
         * 设置plyprinttm属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setPLYPRINTTM(String value) {
            this.plyprinttm = value;
        }

        /**
         * 获取serialno属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSERIALNO() {
            return serialno;
        }

        /**
         * 设置serialno属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSERIALNO(String value) {
            this.serialno = value;
        }

        /**
         * 获取nprmone属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getNPRMONE() {
            return nprmone;
        }

        /**
         * 设置nprmone属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setNPRMONE(String value) {
            this.nprmone = value;
        }

        /**
         * 获取namt属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getNAMT() {
            return namt;
        }

        /**
         * 设置namt属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setNAMT(String value) {
            this.namt = value;
        }

        /**
         * 获取insuranceperiod属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getINSURANCEPERIOD() {
            return insuranceperiod;
        }

        /**
         * 设置insuranceperiod属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setINSURANCEPERIOD(String value) {
            this.insuranceperiod = value;
        }

        /**
         * 获取riskcname属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getRISKCNAME() {
            return riskcname;
        }

        /**
         * 设置riskcname属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setRISKCNAME(String value) {
            this.riskcname = value;
        }

        /**
         * 获取surrdiscprop03属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSURRDISCPROP03() {
            return surrdiscprop03;
        }

        /**
         * 设置surrdiscprop03属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSURRDISCPROP03(String value) {
            this.surrdiscprop03 = value;
        }

        /**
         * 获取plyconfirmtm属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getPLYCONFIRMTM() {
            return plyconfirmtm;
        }

        /**
         * 设置plyconfirmtm属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setPLYCONFIRMTM(String value) {
            this.plyconfirmtm = value;
        }

        /**
         * 获取insurer属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getINSURER() {
            return insurer;
        }

        /**
         * 设置insurer属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setINSURER(String value) {
            this.insurer = value;
        }

        /**
         * 获取signbranch属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSIGNBRANCH() {
            return signbranch;
        }

        /**
         * 设置signbranch属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSIGNBRANCH(String value) {
            this.signbranch = value;
        }

        /**
         * 获取companyname属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCOMPANYNAME() {
            return companyname;
        }

        /**
         * 设置companyname属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCOMPANYNAME(String value) {
            this.companyname = value;
        }

        /**
         * 获取appType属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getAppType() {
            return appType;
        }

        /**
         * 设置appType属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setAppType(String value) {
            this.appType = value;
        }

        /**
         * 获取riskcnametitle属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getRISKCNAMETITLE() {
            return riskcnametitle;
        }

        /**
         * 设置riskcnametitle属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setRISKCNAMETITLE(String value) {
            this.riskcnametitle = value;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="INSURER" maxOccurs="unbounded" minOccurs="0">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="SERIALNUMBER" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="INSUREDNAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="IDENTIFYNUMBER" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="INSURANCEPLAN" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="STARTDATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="ENDDATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="DECLARATIONDATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="PMNAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "insurer"
    })
    public static class INSURERLIST {

        @XmlElement(name = "INSURER")
        protected List<INSUREQRET.INSURERLIST.INSURER> insurer;

        /**
         * Gets the value of the insurer property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the insurer property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getINSURER().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link INSUREQRET.INSURERLIST.INSURER }
         * 
         * 
         */
        public List<INSUREQRET.INSURERLIST.INSURER> getINSURER() {
            if (insurer == null) {
                insurer = new ArrayList<INSUREQRET.INSURERLIST.INSURER>();
            }
            return this.insurer;
        }


        /**
         * <p>anonymous complex type的 Java 类。
         * 
         * <p>以下模式片段指定包含在此类中的预期内容。
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="SERIALNUMBER" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="INSUREDNAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="IDENTIFYNUMBER" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="INSURANCEPLAN" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="STARTDATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="ENDDATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="DECLARATIONDATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="PMNAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "serialnumber",
            "insuredname",
            "identifynumber",
            "insuranceplan",
            "startdate",
            "enddate",
            "declarationdate",
            "pmname"
        })
        public static class INSURER {

            @XmlElement(name = "SERIALNUMBER", required = true)
            protected String serialnumber;
            @XmlElement(name = "INSUREDNAME", required = true)
            protected String insuredname;
            @XmlElement(name = "IDENTIFYNUMBER", required = true)
            protected String identifynumber;
            @XmlElement(name = "INSURANCEPLAN", required = true)
            protected String insuranceplan;
            @XmlElement(name = "STARTDATE", required = true)
            protected String startdate;
            @XmlElement(name = "ENDDATE", required = true)
            protected String enddate;
            @XmlElement(name = "DECLARATIONDATE", required = true)
            protected String declarationdate;
            @XmlElement(name = "PMNAME", required = true)
            protected String pmname;

            /**
             * 获取serialnumber属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getSERIALNUMBER() {
                return serialnumber;
            }

            /**
             * 设置serialnumber属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setSERIALNUMBER(String value) {
                this.serialnumber = value;
            }

            /**
             * 获取insuredname属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getINSUREDNAME() {
                return insuredname;
            }

            /**
             * 设置insuredname属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setINSUREDNAME(String value) {
                this.insuredname = value;
            }

            /**
             * 获取identifynumber属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getIDENTIFYNUMBER() {
                return identifynumber;
            }

            /**
             * 设置identifynumber属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setIDENTIFYNUMBER(String value) {
                this.identifynumber = value;
            }

            /**
             * 获取insuranceplan属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getINSURANCEPLAN() {
                return insuranceplan;
            }

            /**
             * 设置insuranceplan属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setINSURANCEPLAN(String value) {
                this.insuranceplan = value;
            }

            /**
             * 获取startdate属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getSTARTDATE() {
                return startdate;
            }

            /**
             * 设置startdate属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setSTARTDATE(String value) {
                this.startdate = value;
            }

            /**
             * 获取enddate属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getENDDATE() {
                return enddate;
            }

            /**
             * 设置enddate属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setENDDATE(String value) {
                this.enddate = value;
            }

            /**
             * 获取declarationdate属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getDECLARATIONDATE() {
                return declarationdate;
            }

            /**
             * 设置declarationdate属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setDECLARATIONDATE(String value) {
                this.declarationdate = value;
            }

            /**
             * 获取pmname属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getPMNAME() {
                return pmname;
            }

            /**
             * 设置pmname属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setPMNAME(String value) {
                this.pmname = value;
            }

        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="REQUEST_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="DOCUMENT_SERIALNO" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="BUSINESS_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="BUSINESS_NO" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="BUSINESS_NO_SEQ" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="BUSINESS_SOURCE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="POLICY_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="PRINT_DATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="EmailFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="SMSFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="accessory" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="HOLDER_INFO">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="HolderEmail" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="HolderMobile" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="PRINT_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="business_Type" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="SQL_SAVE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="ImportFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "requesttype",
        "documentserialno",
        "businesscode",
        "businessno",
        "businessnoseq",
        "businesssource",
        "policytype",
        "printdate",
        "emailFlag",
        "smsFlag",
        "accessory",
        "holderinfo",
        "printtype",
        "businessType",
        "sqlsave",
        "importFlag"
    })
    public static class MAIN {

        @XmlElement(name = "REQUEST_TYPE", required = true)
        protected String requesttype;
        @XmlElement(name = "DOCUMENT_SERIALNO", required = true)
        protected String documentserialno;
        @XmlElement(name = "BUSINESS_CODE", required = true)
        protected String businesscode;
        @XmlElement(name = "BUSINESS_NO", required = true)
        protected String businessno;
        @XmlElement(name = "BUSINESS_NO_SEQ", required = true)
        protected String businessnoseq;
        @XmlElement(name = "BUSINESS_SOURCE", required = true)
        protected String businesssource;
        @XmlElement(name = "POLICY_TYPE", required = true)
        protected String policytype;
        @XmlElement(name = "PRINT_DATE", required = true)
        protected String printdate;
        @XmlElement(name = "EmailFlag", required = true)
        protected String emailFlag;
        @XmlElement(name = "SMSFlag", required = true)
        protected String smsFlag;
        @XmlElement(required = true)
        protected String accessory;
        @XmlElement(name = "HOLDER_INFO", required = true)
        protected INSUREQRET.MAIN.HOLDERINFO holderinfo;
        @XmlElement(name = "PRINT_TYPE", required = true)
        protected String printtype;
        @XmlElement(name = "business_Type", required = true)
        protected String businessType;
        @XmlElement(name = "SQL_SAVE", required = true)
        protected String sqlsave;
        @XmlElement(name = "ImportFlag", required = true)
        protected String importFlag;

        /**
         * 获取requesttype属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getREQUESTTYPE() {
            return requesttype;
        }

        /**
         * 设置requesttype属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setREQUESTTYPE(String value) {
            this.requesttype = value;
        }

        /**
         * 获取documentserialno属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getDOCUMENTSERIALNO() {
            return documentserialno;
        }

        /**
         * 设置documentserialno属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setDOCUMENTSERIALNO(String value) {
            this.documentserialno = value;
        }

        /**
         * 获取businesscode属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getBUSINESSCODE() {
            return businesscode;
        }

        /**
         * 设置businesscode属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setBUSINESSCODE(String value) {
            this.businesscode = value;
        }

        /**
         * 获取businessno属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getBUSINESSNO() {
            return businessno;
        }

        /**
         * 设置businessno属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setBUSINESSNO(String value) {
            this.businessno = value;
        }

        /**
         * 获取businessnoseq属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getBUSINESSNOSEQ() {
            return businessnoseq;
        }

        /**
         * 设置businessnoseq属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setBUSINESSNOSEQ(String value) {
            this.businessnoseq = value;
        }

        /**
         * 获取businesssource属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getBUSINESSSOURCE() {
            return businesssource;
        }

        /**
         * 设置businesssource属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setBUSINESSSOURCE(String value) {
            this.businesssource = value;
        }

        /**
         * 获取policytype属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getPOLICYTYPE() {
            return policytype;
        }

        /**
         * 设置policytype属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setPOLICYTYPE(String value) {
            this.policytype = value;
        }

        /**
         * 获取printdate属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getPRINTDATE() {
            return printdate;
        }

        /**
         * 设置printdate属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setPRINTDATE(String value) {
            this.printdate = value;
        }

        /**
         * 获取emailFlag属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getEmailFlag() {
            return emailFlag;
        }

        /**
         * 设置emailFlag属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setEmailFlag(String value) {
            this.emailFlag = value;
        }

        /**
         * 获取smsFlag属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSMSFlag() {
            return smsFlag;
        }

        /**
         * 设置smsFlag属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSMSFlag(String value) {
            this.smsFlag = value;
        }

        /**
         * 获取accessory属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getAccessory() {
            return accessory;
        }

        /**
         * 设置accessory属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setAccessory(String value) {
            this.accessory = value;
        }

        /**
         * 获取holderinfo属性的值。
         * 
         * @return
         *     possible object is
         *     {@link INSUREQRET.MAIN.HOLDERINFO }
         *     
         */
        public INSUREQRET.MAIN.HOLDERINFO getHOLDERINFO() {
            return holderinfo;
        }

        /**
         * 设置holderinfo属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link INSUREQRET.MAIN.HOLDERINFO }
         *     
         */
        public void setHOLDERINFO(INSUREQRET.MAIN.HOLDERINFO value) {
            this.holderinfo = value;
        }

        /**
         * 获取printtype属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getPRINTTYPE() {
            return printtype;
        }

        /**
         * 设置printtype属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setPRINTTYPE(String value) {
            this.printtype = value;
        }

        /**
         * 获取businessType属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getBusinessType() {
            return businessType;
        }

        /**
         * 设置businessType属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setBusinessType(String value) {
            this.businessType = value;
        }

        /**
         * 获取sqlsave属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSQLSAVE() {
            return sqlsave;
        }

        /**
         * 设置sqlsave属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSQLSAVE(String value) {
            this.sqlsave = value;
        }

        /**
         * 获取importFlag属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getImportFlag() {
            return importFlag;
        }

        /**
         * 设置importFlag属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setImportFlag(String value) {
            this.importFlag = value;
        }


        /**
         * <p>anonymous complex type的 Java 类。
         * 
         * <p>以下模式片段指定包含在此类中的预期内容。
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="HolderEmail" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="HolderMobile" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "holderEmail",
            "holderMobile"
        })
        public static class HOLDERINFO {

            @XmlElement(name = "HolderEmail", required = true)
            protected String holderEmail;
            @XmlElement(name = "HolderMobile", required = true)
            protected String holderMobile;

            /**
             * 获取holderEmail属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getHolderEmail() {
                return holderEmail;
            }

            /**
             * 设置holderEmail属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setHolderEmail(String value) {
                this.holderEmail = value;
            }

            /**
             * 获取holderMobile属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getHolderMobile() {
                return holderMobile;
            }

            /**
             * 设置holderMobile属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setHolderMobile(String value) {
                this.holderMobile = value;
            }

        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="companyCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="startDate" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "companyCode",
        "startDate"
    })
    public static class MainDto {

        @XmlElement(required = true)
        protected String companyCode;
        @XmlElement(required = true)
        protected String startDate;

        /**
         * 获取companyCode属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCompanyCode() {
            return companyCode;
        }

        /**
         * 设置companyCode属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCompanyCode(String value) {
            this.companyCode = value;
        }

        /**
         * 获取startDate属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getStartDate() {
            return startDate;
        }

        /**
         * 设置startDate属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setStartDate(String value) {
            this.startDate = value;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="INSUREDCOMPANY" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="KINDNAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="POLICYNO" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="INSUREDNAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="INSUREDADDRESS" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="STARTDATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="STARTDATEHOUR" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="ENDDATE" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="ENDDATEHOUR" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "insuredcompany",
        "kindname",
        "policyno",
        "insuredname",
        "insuredaddress",
        "startdate",
        "startdatehour",
        "enddate",
        "enddatehour"
    })
    public static class PARAGRAPHINFO {

        @XmlElement(name = "INSUREDCOMPANY", required = true)
        protected String insuredcompany;
        @XmlElement(name = "KINDNAME", required = true)
        protected String kindname;
        @XmlElement(name = "POLICYNO", required = true)
        protected String policyno;
        @XmlElement(name = "INSUREDNAME", required = true)
        protected String insuredname;
        @XmlElement(name = "INSUREDADDRESS", required = true)
        protected String insuredaddress;
        @XmlElement(name = "STARTDATE", required = true)
        protected String startdate;
        @XmlElement(name = "STARTDATEHOUR", required = true)
        protected String startdatehour;
        @XmlElement(name = "ENDDATE", required = true)
        protected String enddate;
        @XmlElement(name = "ENDDATEHOUR", required = true)
        protected String enddatehour;

        /**
         * 获取insuredcompany属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getINSUREDCOMPANY() {
            return insuredcompany;
        }

        /**
         * 设置insuredcompany属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setINSUREDCOMPANY(String value) {
            this.insuredcompany = value;
        }

        /**
         * 获取kindname属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getKINDNAME() {
            return kindname;
        }

        /**
         * 设置kindname属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setKINDNAME(String value) {
            this.kindname = value;
        }

        /**
         * 获取policyno属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getPOLICYNO() {
            return policyno;
        }

        /**
         * 设置policyno属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setPOLICYNO(String value) {
            this.policyno = value;
        }

        /**
         * 获取insuredname属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getINSUREDNAME() {
            return insuredname;
        }

        /**
         * 设置insuredname属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setINSUREDNAME(String value) {
            this.insuredname = value;
        }

        /**
         * 获取insuredaddress属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getINSUREDADDRESS() {
            return insuredaddress;
        }

        /**
         * 设置insuredaddress属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setINSUREDADDRESS(String value) {
            this.insuredaddress = value;
        }

        /**
         * 获取startdate属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSTARTDATE() {
            return startdate;
        }

        /**
         * 设置startdate属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSTARTDATE(String value) {
            this.startdate = value;
        }

        /**
         * 获取startdatehour属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSTARTDATEHOUR() {
            return startdatehour;
        }

        /**
         * 设置startdatehour属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSTARTDATEHOUR(String value) {
            this.startdatehour = value;
        }

        /**
         * 获取enddate属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getENDDATE() {
            return enddate;
        }

        /**
         * 设置enddate属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setENDDATE(String value) {
            this.enddate = value;
        }

        /**
         * 获取enddatehour属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getENDDATEHOUR() {
            return enddatehour;
        }

        /**
         * 设置enddatehour属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setENDDATEHOUR(String value) {
            this.enddatehour = value;
        }

    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="riskCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "riskCode"
    })
    public static class POLICYRISK {

        @XmlElement(required = true)
        protected String riskCode;

        /**
         * 获取riskCode属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getRiskCode() {
            return riskCode;
        }

        /**
         * 设置riskCode属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setRiskCode(String value) {
            this.riskCode = value;
        }

    }

}
