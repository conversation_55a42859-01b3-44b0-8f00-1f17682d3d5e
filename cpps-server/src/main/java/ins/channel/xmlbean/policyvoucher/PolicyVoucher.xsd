<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="INSUREQRET">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="MAIN">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element type="xs:string" name="REQUEST_TYPE"/>
                            <xs:element type="xs:string" name="DOCUMENT_SERIALNO"/>
                            <xs:element type="xs:string" name="BUSINESS_CODE"/>
                            <xs:element type="xs:string" name="BUSINESS_NO"/>
                            <xs:element type="xs:string" name="BUSINESS_NO_SEQ"/>
                            <xs:element type="xs:string" name="BUSINESS_SOURCE"/>
                            <xs:element type="xs:string" name="POLICY_TYPE"/>
                            <xs:element type="xs:string" name="PRINT_DATE"/>
                            <xs:element type="xs:string" name="EmailFlag"/>
                            <xs:element type="xs:string" name="SMSFlag"/>
                            <xs:element type="xs:string" name="accessory"/>
                            <xs:element name="HOLDER_INFO">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element type="xs:string" name="HolderEmail"/>
                                        <xs:element type="xs:string" name="HolderMobile"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element type="xs:string" name="PRINT_TYPE"/>
                            <xs:element type="xs:string" name="business_Type"/>
                            <xs:element type="xs:string" name="SQL_SAVE"/>
                            <xs:element type="xs:string" name="ImportFlag"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="MainDto">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element type="xs:string" name="companyCode"/>
                            <xs:element type="xs:string" name="startDate"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="POLICYRISK">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element type="xs:string" name="riskCode"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="BaseInfo">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element type="xs:string" name="HEALTHDESC"/>
                            <xs:element type="xs:string" name="SERVICE_TEL"/>
                            <xs:element type="xs:string" name="WEBSIT"/>
                            <xs:element type="xs:string" name="Quantity"/>
                            <xs:element type="xs:string" name="C_OPR_NME"/>
                            <xs:element type="xs:string" name="COMPANY_ADDR"/>
                            <xs:element type="xs:string" name="COMPANY_ZIP"/>
                            <xs:element type="xs:string" name="JURIS_LIMIT"/>
                            <xs:element type="xs:string" name="PLY_PRINT_TM"/>
                            <xs:element type="xs:string" name="SERIAL_NO"/>
                            <xs:element type="xs:string" name="N_PRM_ONE"/>
                            <xs:element type="xs:string" name="N_AMT"/>
                            <xs:element type="xs:string" name="INSURANCE_PERIOD"/>
                            <xs:element type="xs:string" name="RISKCNAME"/>
                            <xs:element type="xs:string" name="SURRDISCPROP03"/>
                            <xs:element type="xs:string" name="PLY_CONFIRM_TM"/>
                            <xs:element type="xs:string" name="INSURER"/>
                            <xs:element type="xs:string" name="SIGN_BRANCH"/>
                            <xs:element type="xs:string" name="COMPANY_NAME"/>
                            <xs:element type="xs:string" name="AppType"/>
                            <xs:element type="xs:string" name="RISKCNAMETITLE"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="PARAGRAPHINFO">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element type="xs:string" name="INSUREDCOMPANY"/>
                            <xs:element type="xs:string" name="KINDNAME"/>
                            <xs:element type="xs:string" name="POLICYNO"/>
                            <xs:element type="xs:string" name="INSUREDNAME"/>
                            <xs:element type="xs:string" name="INSUREDADDRESS"/>
                            <xs:element type="xs:string" name="STARTDATE"/>
                            <xs:element type="xs:string" name="STARTDATEHOUR"/>
                            <xs:element type="xs:string" name="ENDDATE"/>
                            <xs:element type="xs:string" name="ENDDATEHOUR"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="INSURERLIST">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="INSURER" maxOccurs="unbounded" minOccurs="0">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element type="xs:string" name="SERIALNUMBER"/>
                                        <xs:element type="xs:string" name="INSUREDNAME"/>
                                        <xs:element type="xs:string" name="IDENTIFYNUMBER"/>
                                        <xs:element type="xs:string" name="INSURANCEPLAN"/>
                                        <xs:element type="xs:string" name="STARTDATE"/>
                                        <xs:element type="xs:string" name="ENDDATE"/>
                                        <xs:element type="xs:string" name="DECLARATIONDATE"/>
                                        <xs:element type="xs:string" name="PMNAME"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>