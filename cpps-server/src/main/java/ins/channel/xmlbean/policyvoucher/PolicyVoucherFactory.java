
package ins.channel.xmlbean.policyvoucher;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the ins.channel.xmlbean.policyvoucher package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class PolicyVoucherFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: ins.channel.xmlbean.policyvoucher
     * 
     */
    public PolicyVoucherFactory() {
    }

    /**
     * Create an instance of {@link INSUREQRET }
     * 
     */
    public INSUREQRET createINSUREQRET() {
        return new INSUREQRET();
    }

    /**
     * Create an instance of {@link INSUREQRET.INSURERLIST }
     * 
     */
    public INSUREQRET.INSURERLIST createINSUREQRETINSURERLIST() {
        return new INSUREQRET.INSURERLIST();
    }

    /**
     * Create an instance of {@link INSUREQRET.MAIN }
     * 
     */
    public INSUREQRET.MAIN createINSUREQRETMAIN() {
        return new INSUREQRET.MAIN();
    }

    /**
     * Create an instance of {@link INSUREQRET.MainDto }
     * 
     */
    public INSUREQRET.MainDto createINSUREQRETMainDto() {
        return new INSUREQRET.MainDto();
    }

    /**
     * Create an instance of {@link INSUREQRET.POLICYRISK }
     * 
     */
    public INSUREQRET.POLICYRISK createINSUREQRETPOLICYRISK() {
        return new INSUREQRET.POLICYRISK();
    }

    /**
     * Create an instance of {@link INSUREQRET.BaseInfo }
     * 
     */
    public INSUREQRET.BaseInfo createINSUREQRETBaseInfo() {
        return new INSUREQRET.BaseInfo();
    }

    /**
     * Create an instance of {@link INSUREQRET.PARAGRAPHINFO }
     * 
     */
    public INSUREQRET.PARAGRAPHINFO createINSUREQRETPARAGRAPHINFO() {
        return new INSUREQRET.PARAGRAPHINFO();
    }

    /**
     * Create an instance of {@link INSUREQRET.INSURERLIST.INSURER }
     * 
     */
    public INSUREQRET.INSURERLIST.INSURER createINSUREQRETINSURERLISTINSURER() {
        return new INSUREQRET.INSURERLIST.INSURER();
    }

    /**
     * Create an instance of {@link INSUREQRET.MAIN.HOLDERINFO }
     * 
     */
    public INSUREQRET.MAIN.HOLDERINFO createINSUREQRETMAINHOLDERINFO() {
        return new INSUREQRET.MAIN.HOLDERINFO();
    }

}
