package ins.platform.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by sino on 2019/9/4.
 */
@Data
public class SysUser {
    private static final long serialVersionUID = 1L;
    private String userCode;
    private String userCname;
    /**
     * 归属机构
     */
    private String companyCode;
    /**
     * 归属机构名
     */
    private String comCName;
    /**
     * 登陆时间
     */
    private Date loginTime;
    //Modify By zhoutaoyu 登录时返回所属公司名称、代码以及用户职级,供前端分页查询时展示使用 20210222
    @ApiModelProperty("所属公司名称")
    private String teamManager;
    @ApiModelProperty("所属公司代码")
    private String outerCode;
    @ApiModelProperty("职级 1:管理员 2:业务管理岗 3:业务员 codeType ='UserRank'")
    private String userInd;

    @ApiModelProperty("所属部门")
    private String department;
}
