package ins.platform.filter;

import com.sinosoft.power.api.SaaPowerApi;
import com.sinosoft.power.service.FilterPowerService;
import com.sinosoft.power.service.PowerService;
import com.sinosoft.power.vo.RequestKeyWordVo;
import ins.channel.filter.WrapperedRequest;
import ins.channel.filter.WrapperedResponse;
import ins.platform.common.SysUser;
import ins.platform.utils.JwtAuthHelper;
import ins.platform.utils.SessionHelper;
import ins.platform.utils.SpringContextUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.Map;
import java.util.regex.Pattern;

@Slf4j
public class SessionFilter implements Filter {

    private String excludedUrl;
    private String[] excludedUrlArray;

    @Value("${rsa.privateKey}")
    private String privateKey;
    @Value("${rsa.publicKey}")
    private String publicKey;
    
    @Value("${rsa.request.excluded}")
    private String[] rsaExcludedUrl;

    @Override
    public void destroy() {
    }

    @SneakyThrows
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletResponse res = (HttpServletResponse) response;
        HttpServletRequest req = (HttpServletRequest) request;

        log.info("uri -> {}",req.getRequestURI());
        
        if(Arrays.stream(rsaExcludedUrl).filter(s -> s.equals(req.getRequestURI())).count() == 0){
            request = new WrapperedRequest(privateKey, req);
            response = new WrapperedResponse(publicKey, res);
        }

        boolean isExcludedPage = false;
        for (String page : excludedUrlArray) {// 判断是否在过滤url之外
            if (((HttpServletRequest) request).getServletPath().indexOf(page) > -1) {
                isExcludedPage = true;
                break;
            }
        }
        if (isExcludedPage) {
            filterChain.doFilter(request, response);
            if(response instanceof WrapperedResponse){
                ((WrapperedResponse)response).encode();
            }
        } else {
            JwtAuthHelper authHelper = SpringContextUtils.getBean(JwtAuthHelper.class);
            String authHeader = null;
            if (!StringUtils.isEmpty(req.getHeader(authHelper.header))) {
                authHeader = req.getHeader(authHelper.header);
            } else {
                authHeader = req.getParameter(authHelper.header);
            }
            if (authHeader == null || !authHeader.startsWith(authHelper.tokenHead)) {
                returnLogin(res, req, "-3", "未登录！");
                return;
            }
            SysUser user;
            try {
                user = authHelper.getSysUser(authHeader);
            } catch (Exception ex) {
                returnLogin(res, req, "-3", "登录已失效，请重新登录！");
                return;
            }
            if (checkPower(request, user, response)) {
                try {
                    SessionHelper.setThreadLocalUserToken(authHelper.tokenAdapt(authHeader), user);
                    filterChain.doFilter(request, response);
                    if(response instanceof WrapperedResponse){
                        ((WrapperedResponse)response).encode();
                    }
                } finally {
                    SessionHelper.removeThreadLocalUserToken();
                }
            } else {
                returnLogin(res, req, "-2", "您无此权限,请联系管理员");
                return;
            }
        }
    }

    @Override
    public void init(FilterConfig config) throws ServletException {
        excludedUrl = config.getInitParameter("excludedUrl");
        if (StringUtils.isNotEmpty(excludedUrl)) {
            excludedUrlArray = excludedUrl.split(",");
        }
        return;
    }


    private void rewriteHeader(ServletRequest request, ServletResponse response) {
        HttpServletResponse res = (HttpServletResponse) response;
        HttpServletRequest req = (HttpServletRequest) request;
        String agent = req.getHeader("User-Agent").toLowerCase();
        String accept = req.getHeader("accept").toLowerCase();
        if ((agent.indexOf("msie") > 0 || agent.indexOf("gecko") > 0) && accept.contains("text/html")) {//如果是IE浏览器
            res.setContentType("text/html;charset=utf-8");
        }
    }

    private boolean checkPower(ServletRequest request, SysUser user, ServletResponse response) throws IOException {
        FilterPowerService filterPowerService = SaaPowerApi.getInstance().getFilterPowerService();
        PowerService powerService = SaaPowerApi.getInstance().getPowerService();

        //功能权限
        if (!checkUserTaskPower((HttpServletRequest) request, user, filterPowerService, powerService)) {
            return false;
        }

        //数据权限
        if (!checkUserDataPower((HttpServletRequest) request, user, filterPowerService, powerService)) {
            return false;
        }
        return true;
    }

    //功能权限校验
    private boolean checkUserTaskPower(HttpServletRequest request, SysUser sysUser, FilterPowerService powerService, PowerService lpPowerService) {
        // todo 待完善
        //有报案号的业务操作需要数据权限，无报案号的操作除报案新增外都是系统操作，只需要判断功能代码
        Map<String, RequestKeyWordVo> requestMap = powerService.gainRegistNoMapper();
        String url = request.getRequestURI().trim().replace(request.getContextPath(), "");
        if (requestMap.containsKey(url)) {
            RequestKeyWordVo requestKeyWordVo = requestMap.get(url);
            String registNo = request.getParameter(requestKeyWordVo.getRegistNoMapper());
            //判断是否要进行数据权限的校验
            if (!requestKeyWordVo.getDataFlag()) {
                return true;
            }
            if (null != registNo && !"".equals(registNo)) {
                String taskId = request.getParameter(requestKeyWordVo.getTaskNoMapper());
                if (StringUtils.isEmpty(taskId)) {
                    return true;
                }
                String classCode;
                String comCode;
                classCode = (String) request.getAttribute("classCode");
                comCode = (String) request.getAttribute("claimCode");
                String taskCode = (String) request.getAttribute("taskCodeTemp");
                return lpPowerService.validateDataPower(sysUser.getUserCode(), taskCode, comCode, classCode);

            }
        }
        return true;

    }

    //数据权限校验
    private boolean checkUserDataPower(HttpServletRequest request, SysUser sysUser, FilterPowerService powerService, PowerService lpPowerService) {
        try {
            // todo 待完善
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 返回错误代码
     */
    private void returnLogin(HttpServletResponse res, HttpServletRequest req, String status, String statusText) {
        res.setContentType("application/json;charset=utf-8");
        res.setHeader("praga", "no-cache");
        res.setHeader("cache-control", "no-cache");
        res.setHeader("Access-Control-Allow-Credentials", "true");
        // 指定允许其他域名访问
        String origin = req.getHeader("Origin");
        String originHeader = "*";
        if (StringUtils.isNotBlank(origin) && Pattern.matches("[\\.0-9A-Za-z:/]+", origin)) {
            originHeader = origin;
        }
        //LOGGER.info("filterLog:"+originHeader);
        //LOGGER.info("filterLog:"+req.getRemoteAddr());
        if (StringUtils.isNotBlank(originHeader) && Pattern.matches("[\\.0-9A-Za-z:/]+", originHeader)) {
            res.setHeader("Access-Control-Allow-Origin", originHeader);
        } else {
            res.setHeader("Access-Control-Allow-Origin", "*");
        }
        if (Pattern.matches("[\\.0-9A-Za-z:/]+", originHeader)) {
            res.setHeader("Access-Control-Allow-Origin", originHeader);
        }
        //响应类型
        res.setHeader("Access-Control-Allow-Methods", "GET,POST,OPTIONS");
        // 响应头设置
        res.setHeader("Access-Control-Allow-Headers", "Content-Type, Access-Control-Allow-Headers, Authorization, Access-Toke");
        PrintWriter writer = null;
        try {
            writer = res.getWriter();
            writer.write("{\"status\":\"" + status + "\",\"statusText\":\"" + statusText + "\"}");
        } catch (IOException e) {
            log.error("响应信息设置失败：{}", e);
        } finally {
            if (writer != null) {
                writer.close();
            }
        }

    }

}
