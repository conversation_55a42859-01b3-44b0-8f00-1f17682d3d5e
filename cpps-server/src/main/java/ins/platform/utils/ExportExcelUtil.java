package ins.platform.utils;

import ins.channel.policycopymain.service.PolicyCopyMainService;
import ins.framework.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.format.CellFormat;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 导出Excel
 *
 * @param <T>
 * <AUTHOR>
 */
public class ExportExcelUtil<T> {

    // 2007 版本以上 最大支持1048576行
    public final static String EXCEl_FILE_2007 = "2007";
    // 2003 版本 最大支持65536 行
    public final static String EXCEL_FILE_2003 = "2003";

    public ExportExcelUtil(String exportPath) {
        this.exportPath = exportPath;
    }

    private String exportPath;

    public String getExportPath() {
        return exportPath;
    }

    public void setExportPath(String exportPath) {
        this.exportPath = exportPath;
    }

    /**
     * <p>
     * 导出带有头部标题行的Excel <br>
     * 时间格式默认：yyyy-MM-dd hh:mm:ss <br>
     * </p>
     *
     * @param title   表格标题
     * @param headers 头部标题集合
     * @param dataset 数据集合
     * @param lines   字段名称
     * @param out     输出流
     * @param version 2003 或者 2007，不传时默认生成2003版本
     */
    public void exportExcel(String title, String[] headers, String[] lines, Collection<T> dataset, OutputStream out, String version) throws IOException {
        if (StringUtils.isBlank(version) || EXCEL_FILE_2003.equals(version.trim())) {
            exportExcel2003(title, headers, lines, dataset, out, "yyyy-MM-dd HH:mm:ss");
        } else {
            exportExcel2007(title, headers, lines, dataset, out, "yyyy-MM-dd HH:mm:ss");
        }
    }

    /**
     * <p>
     * 通用Excel导出方法,利用反射机制遍历对象的所有字段，将数据写入Excel文件中 <br>
     * 此版本生成2007以上版本的文件 (文件后缀：xlsx)
     * </p>
     *
     * @param title   表格标题名
     * @param headers 表格头部标题集合
     * @param dataset 需要显示的数据集合,集合中一定要放置符合JavaBean风格的类的对象。此方法支持的
     *                JavaBean属性的数据类型有基本数据类型及String,Date
     * @param out     与输出设备关联的流对象，可以将EXCEL文档导出到本地文件或者网络中
     * @param pattern 如果有时间数据，设定输出格式。默认为"yyyy-MM-dd hh:mm:ss"
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public void exportExcel2007(String title, String[] headers, String[] lines, Collection<T> dataset, OutputStream out, String pattern) {
        // 声明一个工作薄
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 生成一个表格
        XSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth(20);
        // 生成一个样式
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置这些样式
        style.setFillForegroundColor(new XSSFColor(java.awt.Color.gray));
        style.setFillPattern(XSSFCellStyle.SOLID_FOREGROUND);
        style.setBorderBottom(XSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(XSSFCellStyle.BORDER_THIN);
        style.setBorderRight(XSSFCellStyle.BORDER_THIN);
        style.setBorderTop(XSSFCellStyle.BORDER_THIN);
        style.setAlignment(XSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        XSSFFont font = workbook.createFont();
        font.setBoldweight(XSSFFont.BOLDWEIGHT_BOLD);
        font.setFontName("宋体");
        font.setColor(new XSSFColor(java.awt.Color.BLACK));
        font.setFontHeightInPoints((short) 11);
        // 把字体应用到当前的样式
        style.setFont(font);
        // 生成并设置另一个样式
        XSSFCellStyle style2 = workbook.createCellStyle();
        style2.setFillForegroundColor(new XSSFColor(java.awt.Color.WHITE));
        style2.setFillPattern(XSSFCellStyle.SOLID_FOREGROUND);
        style2.setBorderBottom(XSSFCellStyle.BORDER_THIN);
        style2.setBorderLeft(XSSFCellStyle.BORDER_THIN);
        style2.setBorderRight(XSSFCellStyle.BORDER_THIN);
        style2.setBorderTop(XSSFCellStyle.BORDER_THIN);
        style2.setAlignment(XSSFCellStyle.ALIGN_CENTER);
        style2.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
        // 生成另一个字体
        XSSFFont font2 = workbook.createFont();
        font2.setBoldweight(XSSFFont.BOLDWEIGHT_NORMAL);
        // 把字体应用到当前的样式
        style2.setFont(font2);

        // 产生表格标题行
        XSSFRow row = sheet.createRow(0);
        XSSFCell cellHeader;
        for (int i = 0; i < headers.length; i++) {
            cellHeader = row.createCell(i);
            cellHeader.setCellStyle(style);
            cellHeader.setCellValue(new XSSFRichTextString(headers[i]));
        }

        // 遍历集合数据，产生数据行
        Iterator<T> it = dataset.iterator();
        int index = 0;
        T t;
        Field[] fields;
        Field field;
        XSSFRichTextString richString;
        Pattern p = Pattern.compile("^//d+(//.//d+)?$");
        Matcher matcher;
        String fieldName;
        String getMethodName;
        XSSFCell cell;
        Class tCls;
        Method getMethod;
        Object value;
        String textValue;
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        while (it.hasNext()) {
            index++;
            row = sheet.createRow(index);
            t = (T) it.next();
            // 利用反射，根据JavaBean属性的先后顺序，动态调用getXxx()方法得到属性值
            fields = t.getClass().getDeclaredFields();
            for (int i = 0; i < lines.length; i++) {
                cell = row.createCell(i);
                cell.setCellStyle(style2);
                fieldName = lines[i];
                getMethodName = "get" + fieldName.substring(0, 1).toUpperCase()
                        + fieldName.substring(1);
                try {
                    tCls = t.getClass();
                    getMethod = tCls.getMethod(getMethodName, new Class[]{});
                    value = getMethod.invoke(t, new Object[]{});
                    // 判断值的类型后进行强制类型转换
                    textValue = null;
                    if (value instanceof Integer) {
                        cell.setCellValue((Integer) value);
                    } else if (value instanceof Float) {
                        textValue = String.valueOf((Float) value);
                        cell.setCellValue(textValue);
                    } else if (value instanceof Double) {
                        textValue = String.valueOf((Double) value);
                        cell.setCellValue(textValue);
                    } else if (value instanceof Long) {
                        cell.setCellValue((Long) value);
                    }
                    if (value instanceof Boolean) {
                        textValue = "是";
                        if (!(Boolean) value) {
                            textValue = "否";
                        }
                    } else if (value instanceof Date) {
                        textValue = sdf.format((Date) value);
                    } else {
                        // 其它数据类型都当作字符串简单处理
                        if (value != null) {
                            textValue = value.toString();
                        }
                    }
                    if (textValue != null) {
                        matcher = p.matcher(textValue);
                        if (matcher.matches()) {
                            // 是数字当作double处理
                            cell.setCellValue(Double.parseDouble(textValue));
                        } else {
                            richString = new XSSFRichTextString(textValue);
                            cell.setCellValue(richString);
                        }
                    }
                } catch (SecurityException e) {
                    e.printStackTrace();
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                } finally {
                    // 清理资源
                }
            }
        }
        try {
            workbook.write(out);
            // workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * <p>
     * 通用Excel导出方法,利用反射机制遍历对象的所有字段，将数据写入Excel文件中 <br>
     * 此方法生成2003版本的excel,文件名后缀：xls <br>
     * </p>
     *
     * @param title   表格标题名
     * @param headers 表格头部标题集合
     * @param dataset 需要显示的数据集合,集合中一定要放置符合JavaBean风格的类的对象。此方法支持的
     *                JavaBean属性的数据类型有基本数据类型及String,Date
     * @param out     与输出设备关联的流对象，可以将EXCEL文档导出到本地文件或者网络中
     * @param pattern 如果有时间数据，设定输出格式。默认为"yyyy-MM-dd hh:mm:ss"
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public void exportExcel2003(String title, String[] headers, String[] lines, Collection<T> dataset, OutputStream out, String pattern) throws IOException {
        // 声明一个工作薄
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 生成一个表格
        HSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth(20);
        // 生成一个样式
        HSSFCellStyle style = workbook.createCellStyle();
        // 设置这些样式
        style.setFillForegroundColor(HSSFColor.GREY_50_PERCENT.index);
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        HSSFFont font = workbook.createFont();
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        font.setFontName("宋体");
        font.setColor(HSSFColor.WHITE.index);
        font.setFontHeightInPoints((short) 11);
        // 把字体应用到当前的样式
        style.setFont(font);
        // 生成并设置另一个样式
        HSSFCellStyle style2 = workbook.createCellStyle();
        style2.setFillForegroundColor(HSSFColor.WHITE.index);
        style2.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style2.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style2.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style2.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style2.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style2.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        // 生成另一个字体
        HSSFFont font2 = workbook.createFont();
        font2.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
        // 把字体应用到当前的样式
        style2.setFont(font2);

        // 产生表格标题行
        HSSFRow row = sheet.createRow(0);
        HSSFCell cellHeader;
        for (int i = 0; i < headers.length; i++) {
            cellHeader = row.createCell(i);
            cellHeader.setCellStyle(style);
            cellHeader.setCellValue(new HSSFRichTextString(headers[i]));
        }

        // 遍历集合数据，产生数据行
        Iterator<T> it = dataset.iterator();
        int index = 0;
        T t;
        Field[] fields;
        Field field;
        HSSFRichTextString richString;
        Pattern p = Pattern.compile("^//d+(//.//d+)?$");
        Matcher matcher;
        String fieldName;
        String getMethodName;
        HSSFCell cell;
        Class tCls;
        Method getMethod;
        Object value;
        String textValue;
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        while (it.hasNext()) {
            index++;
            row = sheet.createRow(index);
            t = (T) it.next();
            // 利用反射，根据JavaBean属性的先后顺序，动态调用getXxx()方法得到属性值
            fields = t.getClass().getDeclaredFields();
            for (int i = 0; i < lines.length; i++) {
                cell = row.createCell(i);
                cell.setCellStyle(style2);
//                field = fields[i];
                fieldName = lines[i];
                getMethodName = "get" + fieldName.substring(0, 1).toUpperCase()
                        + fieldName.substring(1);
                try {
                    tCls = t.getClass();
                    getMethod = tCls.getMethod(getMethodName, new Class[]{});
                    value = getMethod.invoke(t, new Object[]{});
                    // 判断值的类型后进行强制类型转换
                    textValue = null;
                    if (value instanceof Integer) {
                        cell.setCellValue((Integer) value);
                    } else if (value instanceof Float) {
                        textValue = String.valueOf((Float) value);
                        cell.setCellValue(textValue);
                    } else if (value instanceof Double) {
                        textValue = String.valueOf((Double) value);
                        cell.setCellValue(textValue);
                    } else if (value instanceof Long) {
                        cell.setCellValue((Long) value);
                    }
                    if (value instanceof Boolean) {
                        textValue = "是";
                        if (!(Boolean) value) {
                            textValue = "否";
                        }
                    } else if (value instanceof Date) {
                        textValue = sdf.format((Date) value);
                    } else {
                        // 其它数据类型都当作字符串简单处理
                        if (value != null) {
                            textValue = value.toString();
                        }
                    }
                    if (textValue != null) {
                        matcher = p.matcher(textValue);
                        if (matcher.matches()) {
                            // 是数字当作double处理
                            cell.setCellValue(Double.parseDouble(textValue));
                        } else {
                            richString = new HSSFRichTextString(textValue);
                            cell.setCellValue(richString);
                        }
                    }
                } catch (SecurityException e) {
                    e.printStackTrace();
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                } finally {
                    // 清理资源
                }
            }
        }
        if (workbook != null) {
            FileOutputStream outputStream = new FileOutputStream(getExportPath() + File.separator + title + ".xls");
//            FileOutputStream outputStream = new FileOutputStream("D:\\daochu\\"+title+".xls");
            workbook.write(outputStream);
            outputStream.close();
        }
    }

    /**
     * 校验文件合法性      校验规则: 内容是否为空以及后缀名是否正确
     *
     * @param file 待校验文件
     * @return
     */
    public static Boolean checkFileXlsx(MultipartFile file) {
        if (file != null && !file.isEmpty()) {//检查文件是否为空
            //检查文件是否是excel类型文件
            String fileName = file.getOriginalFilename();
            if (!org.springframework.util.StringUtils.endsWithIgnoreCase(fileName, "xls") && !org.springframework.util.StringUtils.endsWithIgnoreCase(fileName, "xlsx")) {
                return false;
            }
        } else {
            return false;
        }
        return true;
    }

    /**
     * 根据excel版本返回对应的poi解析对象
     *
     * @param fileName 文件名称
     * @param is       文件输入流
     * @return
     */
    public static Workbook judegExcelEdition(String fileName, InputStream is) throws Exception {
        if (org.springframework.util.StringUtils.endsWithIgnoreCase(fileName, "xlsx")) {
            return new XSSFWorkbook(is);
        } else {
            return new HSSFWorkbook(is);
        }
    }

    /**
     * 获取单元格的值
     *
     * @param cell
     * @return
     */
    private static String getValue(Cell cell) {
        try {
            if (cell == null) {
                return "";
            }
            int type = CellFormat.ultimateType(cell);

            if (type == Cell.CELL_TYPE_STRING) {
                if (cell.getStringCellValue() != null) {
                    return cell.getStringCellValue().trim();
                }
                return "";
            }
            if (type == Cell.CELL_TYPE_BOOLEAN) {
                return String.valueOf(cell.getBooleanCellValue());
            } else if (type == Cell.CELL_TYPE_NUMERIC) {
                double cellValue = cell.getNumericCellValue();
                DecimalFormat df = new DecimalFormat("#.00");
                return df.format(cellValue);
            } else if (type == Cell.CELL_TYPE_BLANK) {
                return "";
            } else {
                return cell.getStringCellValue().trim();
            }
        } catch (Exception e) {
            throw new BusinessException("文件导入失败!");
        }
    }

    /**
     * 根据传入参数将读取到的excel数据保存至对应的Vo集合中并返回
     *
     * @param workbook poi解析对象
     * @param rowNum   开始读取的行数(如果表格数据不是从第一行开始的,需调用getRowNum()方法获取开始读取行数)
     * @return
     */
    public static <T> List<T> readExcel(Workbook workbook, Class<T> c, int rowNum, String templateType) {
        List<T> list = new ArrayList<T>();
        // 读取表
        try {
            Sheet Sheet = workbook.getSheetAt(0);
            int cellnum = 0;
            if (c.getSimpleName().equals("ExcelTempQueryVo")) {
                cellnum = 1;
            }
            // 获取每行
            for (; rowNum <= Sheet.getLastRowNum(); rowNum++) {
                Row row = Sheet.getRow(rowNum);
                if (row != null && (row.getCell(cellnum) != null && row.getCell(cellnum).getCellType() != Cell.CELL_TYPE_BLANK
                        && org.springframework.util.StringUtils.hasText(row.getCell(cellnum).toString()))) {//判断这行记录是否存在
                    //获取每一行
                    //反射获取实例
                    T t = null;
                    t = c.newInstance();
                    //获取私有变量
                    Field[] fields = c.getDeclaredFields();
                    int columnLen = fields.length;
//                    if (PolicyCopyMainService.TEMPLATE_BATCH_IMPORT.equals(templateType)) {
//                        columnLen--;
//                    }
                    //循环赋值
                    for (int i = 0; i < columnLen; i++) {
                        fields[i].setAccessible(true);
                        String value = getValue(row.getCell(i));
                        if (!PolicyCopyMainService.TEMPLATE_BATCH_IMPORT_INSUREDLIST.equals(templateType)) {
                            if (i == columnLen-1){
                                if (null != value && !"".equals(value) && value.length() > 20 ){
                                    throw new BusinessException("导入数据「项目」字段过长!请确认!");
                                }
                            }
                        }
                        if (PolicyCopyMainService.TEMPLATE_BATCH_IMPORT.equals(templateType)) {
                            //处理批改类型与证件类型
                            value = getString(i, value);
                            //处理性别
                            if (i == 3 && org.springframework.util.StringUtils.hasText(value)) {
                                if (org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(value, "1", "1-男", "男") || value.startsWith("1")) {
                                    value = "1";
                                } else if (org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(value, "2", "2-女", "女") || value.startsWith("2")) {
                                    value = "2";
                                } else if (org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(value, "0", "0-未知", "未知") || value.startsWith("0")) {
                                    value = "0";
                                } else {
                                    throw new BusinessException(MessageFormat.format("申报人员{0}性别信息错误", getValue(row.getCell(2))));
                                }
                            }
                        }

                        if (PolicyCopyMainService.TEMPLATE_BATCH_IMPORT_TY.equals(templateType)) {
                            //处理批改类型与证件类型
                            value = getString(i, value);
                            //处理性别
                            if (i == 3 && org.springframework.util.StringUtils.hasText(value)) {
                                if (org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(value, "1", "1-男", "男") || value.startsWith("1")) {
                                    value = "1";
                                } else if (org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(value, "2", "2-女", "女") || value.startsWith("2")) {
                                    value = "2";
                                } else if (org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(value, "0", "0-未知", "未知") || value.startsWith("0")) {
                                    value = "0";
                                } else {
                                    throw new BusinessException(MessageFormat.format("申报人员{0}性别信息错误", getValue(row.getCell(2))));
                                }
                            }
                            //处理省级代码，市级代码，县区代码
                            if ((i == 13|| i == 14 || i == 15) && org.springframework.util.StringUtils.hasText(value)) {
                                    value = (value.split("-"))[0];
                            }
                        }

                        if (PolicyCopyMainService.TEMPLATE_BATCH_IMPORT_INSUREDLIST.equals(templateType)) {
                            //处理批改类型与证件类型
                            value = getString(i, value);
                            //处理性别
                            if (i == 2 && org.springframework.util.StringUtils.hasText(value)) {
                                if (org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(value, "1", "1-男", "男") || value.startsWith("1")) {
                                    value = "1";
                                } else if (org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(value, "2", "2-女", "女") || value.startsWith("2")) {
                                    value = "2";
                                } else if (org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(value, "0", "0-未知", "未知") || value.startsWith("0")) {
                                    value = "0";
                                } else {
                                    throw new BusinessException(MessageFormat.format("被保人{0}性别信息错误", getValue(row.getCell(2))));
                                }
                            }
                        }
                        fields[i].set(t, value);
                    }
                    list.add(t);
                }
            }
            if (CollectionUtils.isEmpty(list)) { //如果为空
                throw new BusinessException("导入数据为空!请确认!");
            }
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessage());
        } catch (Exception e) {
            throw new BusinessException(MessageFormat.format("文件导入失败!异常原因:{0}", e.getMessage()));
        }
        return list;
    }

    private static String getString(int i, String value) {
        if ((i == 0 || i == 5) && org.springframework.util.StringUtils.hasText(value)) {
            if (org.springframework.util.StringUtils.hasText(value)) {
                value = (value.split("-"))[0];
            }
        }
        return value;
    }
}
