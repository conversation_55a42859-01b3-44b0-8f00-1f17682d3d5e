package ins.platform.utils;

import ins.framework.exception.BusinessException;
import ins.framework.web.util.RequestUtils;
import ins.platform.common.SysUser;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by sino on 2019/9/5.
 */
public class JwtAuthHelper {
    public static final String CLAIM_KEY_USERCODE = "userCode";
    public static final String CLAIM_KEY_USERNAME = "userName";
    public static final String CLAIM_KEY_CREATED = "created";
    public static final String CLAIM_KEY_COMCODE = "companyCode";
    public static final String CLAIM_KEY_COMCNAME = "comCName";
    public static final String CLAIM_KEY_LOGINTIME = "loginTime";
    //Modify By zhoutaoyu 登录时返回所属公司名称、代码以及用户职级,供前端分页查询时展示使用 20210222
    public static final String CLAIM_KEY_TEAMMANAGER = "teamManager";
    public static final String CLAIM_KEY_OUTERCODE= "outerCode";
    public static final String CLAIM_KEY_USERIND= "userInd";
    public static final String CLAIM_KEY_DEPARTMENT= "department";

    @Value("${jwt.secret:arch6Secret}")
    public String secret;
    @Value("${jwt.header:Authorization}")
    public String header;
    @Value("${jwt.expiration:86400}")
    public Long expiration;
    @Value("${jwt.tokenHead:Arch6WithCloud}")
    public String tokenHead;

    public SysUser getSysUser(String authToken) {
        SysUser user = new SysUser();
        Claims claims = this.getClaimsFromToken(authToken);
        if (claims != null) {
            user.setUserCode((String) claims.get(CLAIM_KEY_USERCODE));
            user.setUserCname((String) claims.get(CLAIM_KEY_USERNAME));
            user.setCompanyCode((String) claims.get(CLAIM_KEY_COMCODE));
            user.setComCName((String) claims.get(CLAIM_KEY_COMCNAME));
            user.setTeamManager((String) claims.get(CLAIM_KEY_TEAMMANAGER));
            user.setOuterCode((String) claims.get(CLAIM_KEY_OUTERCODE));
            user.setUserInd((String) claims.get(CLAIM_KEY_USERIND));
            user.setDepartment((String) claims.get(CLAIM_KEY_DEPARTMENT));
            // user.setLoginTime(new Date(((Long)claims.get(CLAIM_KEY_LOGINTIME)).longValue()));
        }
        return user;
    }

    public String getUserCode() {
        HttpServletRequest req = RequestUtils.getHttpServletRequest();
        String authHeader = req.getHeader(header);
        String userCode = this.getUserCodeFromToken(authHeader);
        return userCode;
    }

    public String getUserCodeFromToken(String token) {
        String userCode = null;
        final Claims claims = getClaimsFromToken(token);
        if (claims != null) {
            userCode = (String) claims.get(CLAIM_KEY_USERCODE);
        }
        return userCode;
    }

    public String getComCodeFromToken(String token) {
        String comcode = null;
        final Claims claims = getClaimsFromToken(token);
        if (claims != null) {
            comcode = (String) claims.get(CLAIM_KEY_COMCODE);
        }
        return comcode;
    }

    public Date getCreatedDateFromToken(String token) {
        Date created = null;
        final Claims claims = getClaimsFromToken(token);
        if (claims != null) {
            created = new Date((Long) claims.get(CLAIM_KEY_CREATED));
        }
        return created;
    }

    public Date getLoginTimeFromToken(String token) {
        Date created = null;
        final Claims claims = getClaimsFromToken(token);
        if (claims != null) {
            created = new Date((Long) claims.get(CLAIM_KEY_LOGINTIME));
        }
        return created;
    }

    public Date getExpirationDateFromToken(String token) {
        Date expiration = null;
        final Claims claims = getClaimsFromToken(token);
        if (claims != null) {
            expiration = claims.getExpiration();
        }
        return expiration;
    }

    public Object getAttribute(String token, String attribute) {
        Object result = null;
        Claims claims = this.getClaimsFromToken(token);
        if (claims != null) {
            result = claims.get(attribute);
        }
        return result;
    }

    private Claims getClaimsFromToken(String token) {
        Claims claims;
        try {
            claims = Jwts.parser()
                    .setSigningKey(secret)
                    .parseClaimsJws(tokenAdapt(token))
                    .getBody();
        } catch (Exception ex) {
            throw new BusinessException("解析token异常", true);
        }
        return claims;
    }

    private Date generateExpirationDate() {
        return new Date(System.currentTimeMillis() + expiration * 1000);
    }

    private Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }

    private Boolean isCreatedBeforeLastPasswordReset(Date created, Date lastPasswordReset) {
        return (lastPasswordReset != null && created.before(lastPasswordReset));
    }

    public String generateToken(SysUser user) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_KEY_USERNAME, user.getUserCname());
        claims.put(CLAIM_KEY_USERCODE, user.getUserCode());
        claims.put(CLAIM_KEY_LOGINTIME, user.getLoginTime());
        claims.put(CLAIM_KEY_COMCODE, user.getCompanyCode());
        claims.put(CLAIM_KEY_COMCNAME, user.getComCName());
        //Modify By zhoutaoyu 登录时返回所属公司名称、代码以及用户职级,供前端分页查询时展示使用 20210222
        claims.put(CLAIM_KEY_TEAMMANAGER, user.getTeamManager());
        claims.put(CLAIM_KEY_OUTERCODE, user.getOuterCode());
        claims.put(CLAIM_KEY_USERIND, user.getUserInd());
        claims.put(CLAIM_KEY_DEPARTMENT, user.getDepartment());
        claims.put(CLAIM_KEY_CREATED, new Date());
        return generateToken(claims);
    }

    private String generateToken(Map<String, Object> claims) {
        return Jwts.builder()
                .setClaims(claims)
                .setExpiration(generateExpirationDate())
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }

    /**
     * 判断token是否失效，失效的token不可续约
     *
     * @param token
     * @return
     */
    public boolean canTokenBeRefreshed(String token) {
        return !isTokenExpired(token);
    }

    public String refreshToken(String token) {
        String refreshedToken;
        final Claims claims = getClaimsFromToken(token);
        if (claims != null) {
            claims.put(CLAIM_KEY_CREATED, new Date());
        }
        refreshedToken = generateToken(claims);
        return refreshedToken;
    }

    public String tokenAdapt(String token) {
        String newToken = token;
        if (token.startsWith(tokenHead)) {
            newToken = token.substring(tokenHead.length()+1);
        }
        return newToken;
    }

}
