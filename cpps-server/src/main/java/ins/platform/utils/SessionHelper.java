package ins.platform.utils;

import ins.platform.common.SysUser;

/**
 * Created by sino on 2019/7/12.
 */
public class SessionHelper {

    /** ThreadLocalStorage中用户token的名称 */
    private static final String THREAD_LOCAL_USER_TOKEN_KEY = "_USER_TOKEN_";
    /** ThreadLocalStorage中登录用户对象的名称 */
    private static final String THREAD_LOCAL_LOGIN_USER_KEY = "_LOGIN_USER_";

    /**
     * 获取登录用户对象
     */
    public static SysUser getLoginUser() {
        return (SysUser) ThreadLocalStorage.get(THREAD_LOCAL_LOGIN_USER_KEY);
    }

    /**
     * 获取登录用户Token
     */
    public static String getAuthToken() {
        return (String) ThreadLocalStorage.get(THREAD_LOCAL_USER_TOKEN_KEY);
    }

    /**
     * 设置当前线程绑定的用户token（注：业务程序不应该调用该方法，只能由SessionFilter设置）
     */
    public static void setThreadLocalUserToken(String authToken, SysUser user) {
        ThreadLocalStorage.set(THREAD_LOCAL_USER_TOKEN_KEY, authToken);
        ThreadLocalStorage.set(THREAD_LOCAL_LOGIN_USER_KEY, user);
    }

    /**
     * 清除当前线程绑定的用户token（注：业务程序不应该调用该方法，只能由SessionFilter设置）
     */
    public static void removeThreadLocalUserToken() {
        ThreadLocalStorage.remove(THREAD_LOCAL_USER_TOKEN_KEY);
        ThreadLocalStorage.remove(THREAD_LOCAL_LOGIN_USER_KEY);
    }

}
