package ins.platform.utils;

import com.sinosoft.power.api.SaaPowerApi;
import ins.platform.common.SysUser;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by sino on 2019/9/11.
 */
public class UserPermitDataHelper {

    public static Set<String> getUserPermitCom() {
        SysUser user = SessionHelper.getLoginUser();
        Set<String> permitComs = SaaPowerApi.getInstance().getPowerService().selectUserPermitCom(user.getUserCode());
        if (permitComs == null) {
            permitComs = new HashSet<>();
        }
        // 默认可操作归属机构数据
        permitComs.add(user.getCompanyCode());
        return permitComs;
    }

}
