#开发环境
server:
  port: 8001

application:
  showExceptionStackTrace: true
spring:
  datasource: #数据源配置
    driver-class-name: oracle.jdbc.driver.OracleDriver
    url: *************************************************
    username: qdgzpt
    password: QDG_zpt963
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      maximum-pool-size: 20
      minimum-idle: 10

#配置2级缓存的第2级
redis:
  host: localhost
  port: 6379
  password:
  mode: single

logging:
  level:
    ins:
      platform: debug
      pay: debug

framework:
  swagger:
    enabled: true #使用默认配置类
    title: API接口文档
    description: Api Documentation
    version: 1.0.0
    terms-of-service-url: http://localhost
    use-default-response-messages: false
    contact:
      name: ypic
      url: http:///localhost
      email: <EMAIL>

### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl:
  job:
    accessToken:
    executor:
      appname: cpps-test
      ip:
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
      port: 9999
    admin:
      addresses: http://**********:8888/xxl-job-admin/

##
policy:
   channel:
      #调用服务平台,获取待同步保单号的URL
      queryPolicyEmployer: http://***********:8002/sp/resources/importPolicy/queryPolicyEmployer
      #调用服务平台,同步保单的URL
      queryPolicyNoEmployer: http://***********:8002/sp/resources/importPolicy/queryPolicyNoEmployer
      #调用服务平台,获取保单批改报价与批改承保风险的URL(传不同type)
      endorCommonInQua: http://***********:8002/sp/resources/endorCommonIncludeQuotaService/endorCommonInQua
     #调用理赔接口,查询被保险人赔案信息URL
      queryClaims4Insured: http://**********:6011/claim/servlet/mobileInfoServlet
     #调用服务平台,获取保险平台PDF的URL
      callSP4PolicyVoucher: http://***********:8002/sp/resources/epolicyCommonService/ploicyVoucher4GZPT
     #调用服务平台,获取电子批单PDF的URL
      callSP4EndorNo: http://***********:8002/sp/resources/epolicyEndorService/endorCommon
      #调用服务平台,结算数据路径
      settall: http://***********:8002/sp/resources/endorCommonIncludeQuotaService/synchronizePaymentData

jwt:
  secret: as3d4srg5e5w1df12e

administrator:
  usercode: 000000,beifenguanli

rsa:
  privateKey: MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIAoDuGsSIlIXvzema5SdzlnuBXBjWhDcAzQrcZY/yMEjIIk3omGFcDiUF8yK3L7DJzDz0ac17KOe/L8+88yiTSi4q97jczBdBPhU+C1wSSxrR9F1m8ubPy8B16DC6151AT59T848n4W4G1Yzm5merY7vSFVBpQqq+ddi6jYUK3zAgMBAAECgYApwONYUzEuyQ1phXy8dtGxOkGXlP/lQFN5WWPEg6pcVlcQ78IxPCZKOXHGEiplw7VQPduCPgdofVOUzyebUfOxX27n1nG1gad4X+CVoDUsqwg9enSJpextwJwjwdWrD3jk7FcsejzidKv5jVbYzida1z4doiMznFilqxsyMFJpAQJBAPJfFb3xGN6RY4Xs6UsVCXMs7lsw8Jdtu12sddo2Ky+jDpjLIK82WF5VfwcD4ra30/naC0jGfXNb8IcdnxHbOtECQQCHXNv0XJVYOiPZCm/5RBMvSYNGs6QQQhCVMkiyHuwGCx5o4nXOAsf+ATd82kxSbtlzLSu4yxkekBuf1921I4WDAkAVlx7KssVyXiUtcvM9OvyChAgfm3ZzJtU//PLqo1Spg67zMXUR5pdsn9UW/OOfPzTk1uWWcAgQ1KnSf3MCFjNhAkBUn4XWp4aqCHbeufq0n41K069hJO8dRt/tFemCiXhOcucI9QJmzzBOKF6TDDoIksoYihW6SiYjCD5s2Fv/YPyXAkAbU7UrjmaNSg5vjKszVGRwbmv6xI0QVmjBTBnw5/azO1x6ld+XXe/XSkAa+7lyOltdrhk1tZgHUoHamd/pSqZo
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCT7SS4goWpdX2bWEHz2hfJXjnVxfk61R6ZaVD8zewIRCjQLKJ47wYdJtz8xg/Iik7AsOttrlvUHX5ARCQgl9n13W4keLaU7A4A1n6zpfniRdVrnKQ1CxDBVRUZ1Kubgghg3gXkvG6fjVP7ygdKvkwXBpyOVa0IK7FfBQu3ahJOhwIDAQAB
  request:
    # 前端也要做对应的设置
    excluded: /api/auth/getImage,/api/policycopymain/uploadExcelBySearch,/api/policycopymain/downLoadTemplate,/api/policycopymain/downloadBatchQueryDeclarationTemplate,/api/policycopymain/downLoadTemplateTy,/api/declarationUpload/resolveExcel,/api/policycopymain/batchQueryDeclarationTemplate,/api/policyVoucher/getPolicyVoucherPdf
# 申报确认成功后, 直接生成批单
declaration:
  flag: 1
