#开发环境
server:
  port: 8001

application:
  showExceptionStackTrace: true
spring:
  datasource: #数据源配置
    driver-class-name: oracle.jdbc.driver.OracleDriver
    url: ************************************************
    username: qdgzptysc
    password: QD_gzYSC23
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10

#配置2级缓存的第2级
redis:
  host: localhost
  port: 6379
  password:
  mode: single

logging:
  level:
    ins:
      platform: debug
      pay: debug

framework:
  swagger:
    enabled: true #使用默认配置类
    title: API接口文档
    description: Api Documentation
    version: 1.0.0
    terms-of-service-url: http://localhost
    use-default-response-messages: false
    contact:
      name: ypic
      url: http:///localhost
      email: <EMAIL>

### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl:
  job:
    accessToken:
    executor:
      appname: cpps-ysc
      ip:
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
      port: 9999
    admin:
      addresses: http://**********:8888/xxl-job-admin/

##
policy:
  channel:
    #调用服务平台,获取待同步保单号的URL
    queryPolicyEmployer: http://**********:9001/sp/resources/importPolicy/queryPolicyEmployer
    #调用服务平台,同步保单的URL
    queryPolicyNoEmployer: http://**********:9001/sp/resources/importPolicy/queryPolicyNoEmployer
    #调用服务平台,获取保单批改报价与批改承保风险的URL(传不同type)
    endorCommonInQua: http://**********:9001/sp/resources/endorCommonIncludeQuotaService/endorCommonInQua
    #调用理赔接口,查询被保险人赔案信息URL
    queryClaims4Insured: http://**********:9051/claim/servlet/mobileInfoServlet
    #调用服务平台,获取保险平台PDF的URL
    callSP4PolicyVoucher: http://**********:9001/sp/resources/epolicyCommonService/ploicyVoucher4GZPT
    #调用服务平台,获取电子批单PDF的URL
    callSP4EndorNo: http://**********:9001/sp/resources/epolicyEndorService/endorCommon
    #调用服务平台,结算数据路径
    settall: http://**********:9001/sp/resources/endorCommonIncludeQuotaService/synchronizePaymentData