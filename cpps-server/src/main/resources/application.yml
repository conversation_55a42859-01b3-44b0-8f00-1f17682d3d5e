spring:  
  application:
    name: channel-server
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  messages:
    basename: i18n/defalultMessages,i18n/messages #指定message的basename，多个以逗号分隔，如果不加包名的话，默认从classpath路径开始，默认: messages
    encoding: UTF-8 #设定Message bundles的编码，默认: UTF-8
  
mybatis: #MyBatis配置
  mapper-locations: classpath*:mapper/**/*Dao.xml
  executor-type: reuse
  configuration:
    # 将下划线格式列自动映射成驼峰格式属性名
    mapUnderscoreToCamelCase: true
    jdbc-type-for-null: NULL
    cache-enabled: true
    default-executor-type: reuse

j2cache:
  config-location: j2cache.properties
  L1:
    provider_class: none
  l2-cache-open: false #是否开启两级缓存框架的第二级
  open-spring-cache: true
  serialization: fst #json
  
#配置2级缓存的第1级  
caffeine:
  region:
    default: 1000,1h
    
filter:
  session:
#    excluded-url: /socketServer,/v2,/swagger-resources,/webjars/springfox-swagger-ui,/swagger-ui.html,/monitoring,/api/auth/login,/api/auth/getImage,/api/auth/casLogin,/api/auth/logout,/oauth/token,/gateway,/gppaymentno/channel,/cash/callback/notify
    excluded-url: /socketServer,/monitoring,/api/auth/login,/api/auth/getImage,/api/auth/casLogin,/api/auth/logout,/oauth/token,/gateway,/gppaymentno/channel,/cash/callback/notify

# 供运维人员监控使用
management:
  trace:
    http:
      enabled: false
  endpoint:
    health:
      show-details: always
  endpoints:
    shutdown:
      enable: false
    configprops:
      enable: false
    trace:
      enable: false
    web:
      exposure:
        include: "health,prometheus,httptrace"
        exclude: "trace,shutdown,configprops"
  server:
    port: 8099 # 为了方便运维统计数据，请大家务必用这个端口。如果有端口冲突请联系运维修改监控监听端口。
    servlet:
      context-path: /
    ssl:
      enabled: false
