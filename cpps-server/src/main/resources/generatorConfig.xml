<?xml version="1.0" encoding="UTF-8"?>  
<!DOCTYPE generatorConfiguration  
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"  
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
	<!-- 引入配置文件 -->
	<!-- <properties resource="mybatis-gen.properties" /> -->

	<!-- 指定数据库连接驱动jara地址 -->
	<classPathEntry location="E:\Repository\mysql\mysql-connector-java\8.0.15\mysql-connector-java-8.0.15.jar" />

	<!-- 一个数据库一个context -->
	<context id="sqlserverTables">
		<!-- 生成的pojo，将implements Serializable -->
		<plugin type="org.mybatis.generator.plugins.SerializablePlugin"></plugin>

		<!-- 注释 -->
		<commentGenerator>
			<property name="suppressAllComments" value="true" /><!-- 是否取消注释 -->
			<!-- <property name="suppressDate" value="true" /> 是否生成注释代时间戳 -->
		</commentGenerator>

		<!-- 数据库链接URL、用户名、密码 -->
		<jdbcConnection driverClass="com.mysql.cj.jdbc.Driver" 
			connectionURL="***********************************************************************************************************************************************************************************"
			userId="root" password="admin">
		</jdbcConnection>

		<!-- 类型转换 -->
		<javaTypeResolver>
			<!-- 默认false，把JDBC DECIMAL 和 NUMERIC 类型解析为 Integer true，把JDBC DECIMAL 
				和 NUMERIC 类型解析为java.math.BigDecimal -->
			<property name="forceBigDecimals" value="false" />
		</javaTypeResolver>

		<!-- 生成model模型，对应的包路径，以及文件存放路径(targetProject)，targetProject可以指定具体的路径,如./src/main/java， 
			也可以使用“MAVEN”来自动生成，这样生成的代码会在target/generatord-source目录下 -->
		<javaModelGenerator
			targetPackage="xxx.platform"
			targetProject="./src/main/java">
			<!-- 是否在当前路径下新加一层schema,eg：fase路径com.oop.eksp.user.model， true:com.oop.eksp.user.model.[schemaName] -->
			<property name="enableSubPackages" value="false" />
			<!-- 从数据库返回的值被清理前后的空格 -->
			<property name="trimStrings" value="true" />
		</javaModelGenerator>

		<!--对应的mapper.xml文件 -->
		<sqlMapGenerator
			targetPackage="xxx.platform"
			targetProject="./src/main/java">
			<property name="enableSubPackages" value="false" />
		</sqlMapGenerator>

		<!-- 对应的Mapper接口类文件 -->
		<javaClientGenerator type="XMLMAPPER"
			targetPackage="xxx.platform"
			targetProject="./src/main/java">
			<property name="enableSubPackages" value="false" />
		</javaClientGenerator>


		<!-- 列出要生成代码的所有表，这里配置的是不生成Example文件 -->
		<!-- schema即为数据库名 tableName为对应的数据库表 domainObjectName是要生成的实体类 enable*ByExample 
			是否生成 example类 -->
		<table tableName="demo_robot_job2" 
			domainObjectName="DemoRobotJob2"  enableCountByExample="false"
			enableUpdateByExample="true" enableDeleteByExample="true"
			enableSelectByExample="true" selectByExampleQueryId="true">
			<!-- 忽略列，不生成bean 字段 <ignoreColumn column="FRED" /> -->
			<!-- 指定列的java数据类型 <columnOverride column="LONG_VARCHAR_FIELD" jdbcType="VARCHAR" 
				/> -->
			<!-- 用于指定生成实体类时是否使用实际的列名作为实体类的属性名。false是 Camel Case风格 -->
			<property name="useActualColumnNames" value="false" />
		</table>
	</context>
</generatorConfiguration>    