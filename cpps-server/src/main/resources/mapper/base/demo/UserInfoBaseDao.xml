<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.demo.dao.UserInfoDao">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.demo.po.UserInfo">
		<id column="idnum" property="idnum" />
		<result column="user_code" property="userCode" />
		<result column="user_name" property="userName" />
		<result column="parent_name" property="parentName" />
		<result column="age" property="age" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		idnum,
		user_code,
		user_name,
		parent_name,
		age
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="idnum != null and idnum != ''" >
			and idnum = #{idnum}
		</if>
		<if test="userCode != null and userCode != ''" >
			and user_code = #{userCode}
		</if>
		<if test="userName != null and userName != ''" >
			and user_name = #{userName}
		</if>
		<if test="parentName != null and parentName != ''" >
			and parent_name = #{parentName}
		</if>
		<if test="age != null and age != ''" >
			and age = #{age}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from user_info
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from user_info
		where idnum = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from user_info
		where idnum in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.demo.po.UserInfo">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from user_info
		where idnum = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from user_info
		where idnum in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" useGeneratedKeys="true" keyProperty="idnum" parameterType="ins.channel.demo.po.UserInfo">
		insert into user_info (
			idnum,
			user_code,
			user_name,
			parent_name,
			age
		) values (
			#{idnum},
			#{userCode},
			#{userName},
			#{parentName},
			#{age}
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="idnum" parameterType="ins.channel.demo.po.UserInfo">
		insert into user_info
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="idnum != null" >
				idnum,
			</if>
			<if test="userCode != null" >
				user_code,
			</if>
			<if test="userName != null" >
				user_name,
			</if>
			<if test="parentName != null" >
				parent_name,
			</if>
			<if test="age != null" >
				age
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="idnum != null" >
				#{idnum},
			</if>
			<if test="userCode != null" >
				#{userCode},
			</if>
			<if test="userName != null" >
				#{userName},
			</if>
			<if test="parentName != null" >
				#{parentName},
			</if>
			<if test="age != null" >
				#{age}
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" useGeneratedKeys="true" keyProperty="idnum" parameterType="ins.channel.demo.po.UserInfo">
		update user_info 
		<set>
			<if test="userCode != null" >
				user_code=#{userCode},
			</if>
			<if test="userName != null" >
				user_name=#{userName},
			</if>
			<if test="parentName != null" >
				parent_name=#{parentName},
			</if>
			<if test="age != null" >
				age=#{age},
			</if>
		</set>
		where idnum = #{idnum }	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" useGeneratedKeys="true" keyProperty="idnum" parameterType="ins.channel.demo.po.UserInfo">
		update user_info set
			user_code=#{userCode},
			user_name=#{userName},
			parent_name=#{parentName},
			age=#{age},
		where idnum = #{idnum}	</update>
</mapper>
