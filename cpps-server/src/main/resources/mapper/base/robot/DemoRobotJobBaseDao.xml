<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.robot.dao.DemoRobotJobDao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.robot.po.DemoRobotJob">
		<id column="id" property="id" />
		<result column="robot_id" property="robotId" />
		<result column="start_time" property="startTime" />
		<result column="end_time" property="endTime" />
		<result column="walk_count" property="walkCount" />
		<result column="consume_energy" property="consumeEnergy" />
		<result column="job_content" property="jobContent" />
		<result column="job_image" property="jobImage" />
		<result column="com_code" property="companyCode" />
		<result column="version" property="version" />
		<result column="Insert_Time_For_His" property="insertTimeForHis" />
		<result column="Operate_Time_For_His" property="operateTimeForHis" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		id,
		robot_id,
		start_time,
		end_time,
		walk_count,
		consume_energy,
		job_content,
		job_image,
		com_code,
		version,
		Insert_Time_For_His,
		Operate_Time_For_His
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and id = #{id}
		</if>
		<if test="robotId != null and robotId != ''" >
			and robot_id = #{robotId}
		</if>
		<if test="startTime != null and startTime != ''" >
			and start_time = #{startTime}
		</if>
		<if test="endTime != null and endTime != ''" >
			and end_time = #{endTime}
		</if>
		<if test="walkCount != null and walkCount != ''" >
			and walk_count = #{walkCount}
		</if>
		<if test="consumeEnergy != null and consumeEnergy != ''" >
			and consume_energy = #{consumeEnergy}
		</if>
		<if test="jobContent != null and jobContent != ''" >
			and job_content = #{jobContent}
		</if>
		<if test="jobImage != null and jobImage != ''" >
			and job_image = #{jobImage}
		</if>
		<if test="companyCode != null and companyCode != ''" >
			and com_code = #{companyCode}
		</if>
		<if test="version != null and version != ''" >
			and version = #{version}
		</if>
		<if test="insertTimeForHis != null and insertTimeForHis != ''" >
			and Insert_Time_For_His = #{insertTimeForHis}
		</if>
		<if test="operateTimeForHis != null and operateTimeForHis != ''" >
			and Operate_Time_For_His = #{operateTimeForHis}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from demo_robot_job
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from demo_robot_job
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from demo_robot_job
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.robot.po.DemoRobotJob">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from demo_robot_job
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from demo_robot_job
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="ins.channel.robot.po.DemoRobotJob">
		insert into demo_robot_job (
			id,
			robot_id,
			start_time,
			end_time,
			walk_count,
			consume_energy,
			job_content,
			job_image,
			com_code,
			version,
			Operate_Time_For_His
		) values (
			#{id},
			#{robotId},
			#{startTime},
			#{endTime},
			#{walkCount},
			#{consumeEnergy},
			#{jobContent},
			#{jobImage},
			#{companyCode},
			1,
			<include refid="mybatis.common.Base_Current" />
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="ins.channel.robot.po.DemoRobotJob">
		insert into demo_robot_job
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="robotId != null" >
				robot_id,
			</if>
			<if test="startTime != null" >
				start_time,
			</if>
			<if test="endTime != null" >
				end_time,
			</if>
			<if test="walkCount != null" >
				walk_count,
			</if>
			<if test="consumeEnergy != null" >
				consume_energy,
			</if>
			<if test="jobContent != null" >
				job_content,
			</if>
			<if test="jobImage != null" >
				job_image,
			</if>
			<if test="companyCode != null" >
				com_code,
			</if>
			version,
			Operate_Time_For_His
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="robotId != null" >
				#{robotId},
			</if>
			<if test="startTime != null" >
				#{startTime},
			</if>
			<if test="endTime != null" >
				#{endTime},
			</if>
			<if test="walkCount != null" >
				#{walkCount},
			</if>
			<if test="consumeEnergy != null" >
				#{consumeEnergy},
			</if>
			<if test="jobContent != null" >
				#{jobContent},
			</if>
			<if test="jobImage != null" >
				#{jobImage},
			</if>
			<if test="companyCode != null" >
				#{companyCode},
			</if>
			1,
			<include refid="mybatis.common.Base_Current" />
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" useGeneratedKeys="true" keyProperty="id" parameterType="ins.channel.robot.po.DemoRobotJob">
		update demo_robot_job 
		<set>
			<if test="robotId != null" >
				robot_id=#{robotId},
			</if>
			<if test="startTime != null" >
				start_time=#{startTime},
			</if>
			<if test="endTime != null" >
				end_time=#{endTime},
			</if>
			<if test="walkCount != null" >
				walk_count=#{walkCount},
			</if>
			<if test="consumeEnergy != null" >
				consume_energy=#{consumeEnergy},
			</if>
			<if test="jobContent != null" >
				job_content=#{jobContent},
			</if>
			<if test="jobImage != null" >
				job_image=#{jobImage},
			</if>
			<if test="companyCode != null" >
				com_code=#{companyCode},
			</if>
			version = version + 1,
			Operate_Time_For_His = <include refid="mybatis.common.Base_Current" />,
		</set>
		where id = #{id } and version = #{version}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" useGeneratedKeys="true" keyProperty="id" parameterType="ins.channel.robot.po.DemoRobotJob">
		update demo_robot_job set
			robot_id=#{robotId},
			start_time=#{startTime},
			end_time=#{endTime},
			walk_count=#{walkCount},
			consume_energy=#{consumeEnergy},
			job_content=#{jobContent},
			job_image=#{jobImage},
			com_code=#{companyCode},
			version = version + 1,
			Operate_Time_For_His = <include refid="mybatis.common.Base_Current" />,
		where id = #{id} and version = #{version}
	</update>
</mapper>
