<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- ===========通过ins-framework-mybatis工具自动生成，请勿手工修改！========= -->
<!-- ===========本配置文件中定义的节点可在自定义配置文件中直接使用！============== -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.robot.dao.DemoRobotMain2Dao">
	<!-- 默认开启二级缓存,使用两级缓存来处理 -->
	<cache type="ins.framework.mybatis.cache.decorators.DefaultCache"/>
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="ins.channel.robot.po.DemoRobotMain2">
		<id column="id" property="id" />
		<result column="robot_sn" property="robotSn" />
		<result column="robot_height" property="robotHeight" />
		<result column="nickname" property="nickname" />
		<result column="recharge_count" property="rechargeCount" />
		<result column="manufacture_name" property="manufactureName" />
		<result column="manufacture_date" property="manufactureDate" />
		<result column="com_code" property="companyCode" />
		<result column="version" property="version" />
		<result column="Insert_Time_For_His" property="insertTimeForHis" />
		<result column="Operate_Time_For_His" property="operateTimeForHis" />
	</resultMap>
	
	<!-- 通用查询结果列-->
	<sql id="Base_Column_List"> 
		id,
		robot_sn,
		robot_height,
		nickname,
		recharge_count,
		manufacture_name,
		manufacture_date,
		com_code,
		version,
		Insert_Time_For_His,
		Operate_Time_For_His
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null and id != ''" >
			and id = #{id}
		</if>
		<if test="robotSn != null and robotSn != ''" >
			and robot_sn = #{robotSn}
		</if>
		<if test="robotHeight != null and robotHeight != ''" >
			and robot_height = #{robotHeight}
		</if>
		<if test="nickname != null and nickname != ''" >
			and nickname = #{nickname}
		</if>
		<if test="rechargeCount != null and rechargeCount != ''" >
			and recharge_count = #{rechargeCount}
		</if>
		<if test="manufactureName != null and manufactureName != ''" >
			and manufacture_name = #{manufactureName}
		</if>
		<if test="manufactureDate != null and manufactureDate != ''" >
			and manufacture_date = #{manufactureDate}
		</if>
		<if test="companyCode != null and companyCode != ''" >
			and com_code = #{companyCode}
		</if>
		<if test="version != null and version != ''" >
			and version = #{version}
		</if>
		<if test="insertTimeForHis != null and insertTimeForHis != ''" >
			and Insert_Time_For_His = #{insertTimeForHis}
		</if>
		<if test="operateTimeForHis != null and operateTimeForHis != ''" >
			and Operate_Time_For_His = #{operateTimeForHis}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from demo_robot_main2
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>
 
	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from demo_robot_main2
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from demo_robot_main2
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="ins.channel.robot.po.DemoRobotMain2">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from demo_robot_main2
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from demo_robot_main2
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="ins.channel.robot.po.DemoRobotMain2">
		insert into demo_robot_main2 (
			id,
			robot_sn,
			robot_height,
			nickname,
			recharge_count,
			manufacture_name,
			manufacture_date,
			com_code,
			version,
			Operate_Time_For_His
		) values (
			#{id},
			#{robotSn},
			#{robotHeight},
			#{nickname},
			#{rechargeCount},
			#{manufactureName},
			#{manufactureDate},
			#{companyCode},
			1,
			<include refid="mybatis.common.Base_Current" />
		)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="ins.channel.robot.po.DemoRobotMain2">
		insert into demo_robot_main2
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="robotSn != null" >
				robot_sn,
			</if>
			<if test="robotHeight != null" >
				robot_height,
			</if>
			<if test="nickname != null" >
				nickname,
			</if>
			<if test="rechargeCount != null" >
				recharge_count,
			</if>
			<if test="manufactureName != null" >
				manufacture_name,
			</if>
			<if test="manufactureDate != null" >
				manufacture_date,
			</if>
			<if test="companyCode != null" >
				com_code,
			</if>
			version,
			Operate_Time_For_His
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="robotSn != null" >
				#{robotSn},
			</if>
			<if test="robotHeight != null" >
				#{robotHeight},
			</if>
			<if test="nickname != null" >
				#{nickname},
			</if>
			<if test="rechargeCount != null" >
				#{rechargeCount},
			</if>
			<if test="manufactureName != null" >
				#{manufactureName},
			</if>
			<if test="manufactureDate != null" >
				#{manufactureDate},
			</if>
			<if test="companyCode != null" >
				#{companyCode},
			</if>
			1,
			<include refid="mybatis.common.Base_Current" />
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" useGeneratedKeys="true" keyProperty="id" parameterType="ins.channel.robot.po.DemoRobotMain2">
		update demo_robot_main2 
		<set>
			<if test="robotSn != null" >
				robot_sn=#{robotSn},
			</if>
			<if test="robotHeight != null" >
				robot_height=#{robotHeight},
			</if>
			<if test="nickname != null" >
				nickname=#{nickname},
			</if>
			<if test="rechargeCount != null" >
				recharge_count=#{rechargeCount},
			</if>
			<if test="manufactureName != null" >
				manufacture_name=#{manufactureName},
			</if>
			<if test="manufactureDate != null" >
				manufacture_date=#{manufactureDate},
			</if>
			<if test="companyCode != null" >
				com_code=#{companyCode},
			</if>
			version = version + 1,
			Operate_Time_For_His = <include refid="mybatis.common.Base_Current" />,
		</set>
		where id = #{id } and version = #{version}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" useGeneratedKeys="true" keyProperty="id" parameterType="ins.channel.robot.po.DemoRobotMain2">
		update demo_robot_main2 set
			robot_sn=#{robotSn},
			robot_height=#{robotHeight},
			nickname=#{nickname},
			recharge_count=#{rechargeCount},
			manufacture_name=#{manufactureName},
			manufacture_date=#{manufactureDate},
			com_code=#{companyCode},
			version = version + 1,
			Operate_Time_For_His = <include refid="mybatis.common.Base_Current" />,
		where id = #{id} and version = #{version}
	</update>
</mapper>
