<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.demo.dao.UserInfoDao">
	<!-- 请在下方添加自定义配置-->

	<select id="selectByAge" resultType="ins.channel.demo.po.UserInfo">
		select
			<include refid="Base_Column_List" />
		from user_info
		where age = #{age}
	</select>

	<select id="selectAll" resultType="ins.channel.demo.po.UserInfo">
		select
			<include refid="Base_Column_List" />
		from ${tableName}
	</select>
  
</mapper>
