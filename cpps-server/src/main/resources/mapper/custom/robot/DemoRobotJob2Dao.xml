<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.robot.dao.DemoRobotJob2Dao">
	<!-- 开启二级缓存,在SQL中使用flushCache、useCache来刷新和使用缓存 -->
	<cache-ref  namespace="ins.channel.robot.dao.DemoRobotJob2Dao"/>
	<!-- 请在下方添加自定义配置--> 
  	<select id="selectByRobotId" resultMap="BaseResultMap" parameterType="map" flushCache="false" useCache="true">
		select
			<include refid="Base_Column_List"/>
		from demo_robot_job2
		where robot_id=#{param1}
	</select>
	<!-- 按主键删除一条记录 -->
	<delete id="deleteByRobotIds" parameterType="map" flushCache="true">
		delete from demo_robot_job2
		where robot_id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>
	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage2" resultMap="BaseResultMap" parameterType="ins.channel.robot.po.DemoRobotJob">
		<include refid="Base_Select_By_Entity" />
	</select>
</mapper>
