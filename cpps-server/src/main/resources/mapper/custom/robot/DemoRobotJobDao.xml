<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ================可直接使用Base配置文件中定义的节点！================ -->
<!-- ============================================================== -->
<mapper namespace="ins.channel.robot.dao.DemoRobotJobDao">
  <cache-ref  namespace="ins.channel.robot.dao.DemoRobotJobDao"/>
  <!-- 请在下方添加自定义配置-->

	<select id="selectByRobotId" resultMap="BaseResultMap" parameterType="map" flushCache="false" useCache="true">
		select
			<include refid="Base_Column_List"/>
		from demo_robot_job
		where robot_id=#{param1}
	</select>
	<!-- 按主键删除一条记录 -->
	<delete id="deleteByRobotIds" parameterType="map" flushCache="true">
		delete from demo_robot_job
		where robot_id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>
</mapper>