--- # 这个为yaml的分割线，如果一个工程中有多个jar包需要部署，可以用这个符号分割。
# appType用于确定服务类型,当前分为java、nginx
appType: java
# nameSpacePrefix用于设置namespaces 前缀，各环境实际namespace将为<nameSpacePrefix>-<env>
# 例：ins-channel-pre-prod
nameSpacePrefix: "ins-core-channel"
# appName用于确定service名以及deployment名，集群内服务名将为：appName.nameSpacePrefix-<env name>.svc.cluster.local
# 例：ins-phone-fraud.ins-channel-pre-prod.svc.cluster.local
appName: "ins-channel-server-uat"
# listenPorts用于确定服务将要暴露的集群内端口，非nodeport端口,可以配置多个
listenPorts:
  - 8080

# jarFile用于确定程序启动入口jar包来源，将用于build镜像并启动程序，配置内容为mvn build后jar包相对代码根目录的路径 
# 本工程的结构为
# channel-parent 这一层为工程的名称，当前目录
# |-channel-server 子module
# |-channel-gateway 子module
# |-...
jarFile: "channel-server/target/channel-server-1.0-SNAPSHOT-exec.jar"
# javaRunOptions用于确定java程序运行时所需添加的参数，可以配置多个
javaRunOptions:
  - "-Xmx2048m"
  - "-Djava.security.egd=file:/dev/./urandom"

--- # 这个为yaml的分割线，如果一个工程中有多个jar包需要部署，可以用这个符号分割。
# appType用于确定服务类型,当前分为java、nginx
appType: java
# nameSpacePrefix用于设置namespaces 前缀，各环境实际namespace将为<nameSpacePrefix>-<env>
# 例：ins-channel-pre-prod
nameSpacePrefix: "ins-core-channel"
# appName用于确定service名以及deployment名，集群内服务名将为：appName.nameSpacePrefix-<env name>.svc.cluster.local
# 例：ins-phone-fraud.ins-channel-pre-prod.svc.cluster.local
appName: "ins-channel-gateway-uat"
# listenPorts用于确定服务将要暴露的集群内端口，非nodeport端口,可以配置多个
listenPorts:
  - 8080

# jarFile用于确定程序启动入口jar包来源，将用于build镜像并启动程序，配置内容为mvn build后jar包相对代码根目录的路径 
# 本工程的结构为
# channel-parent 这一层为工程的名称，当前目录
# |-channel-server 子module
# |-channel-gateway 子module
# |-...
jarFile: "channel-gateway/target/channel-gateway-1.0-SNAPSHOT.jar"
# javaRunOptions用于确定java程序运行时所需添加的参数，可以配置多个
javaRunOptions:
  - "-Xmx2048m"
  - "-Djava.security.egd=file:/dev/./urandom"
