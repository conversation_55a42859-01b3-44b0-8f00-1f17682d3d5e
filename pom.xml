<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sinosoft</groupId>
    <artifactId>cpps-parent</artifactId>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>cpps-server</module>
        <module>cpps-power</module>
        <module>cpps-persistence</module>
        <module>cpps-framework</module>
        <module>cpps-gateway</module>
    </modules>

    <packaging>pom</packaging>

    <parent>
        <groupId>ins.framework</groupId>
        <artifactId>ins-framework-parent</artifactId>
        <version>6.2.3</version>
    </parent>

    <properties>
        <ins-framework.version>6.2.3</ins-framework.version>
        <ins-platform-plugins.version>6.2.3</ins-platform-plugins.version>
        <skip_maven_test>true</skip_maven_test>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 内部 -->
            <dependency>
                <groupId>com.sinosoft</groupId>
                <artifactId>cpps-power</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinosoft</groupId>
                <artifactId>cpps-persistence</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinosoft</groupId>
                <artifactId>cpps-framework</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <!-- Web类型 -->
            <dependency>
                <groupId>ins.framework</groupId>
                <artifactId>ins-framework-web</artifactId>
                <version>${ins-framework.version}</version>
            </dependency>
            <!-- +DB类型 -->
            <dependency>
                <groupId>ins.framework</groupId>
                <artifactId>ins-framework-mybatis</artifactId>
                <version>${ins-framework.version}</version>
            </dependency>
            <!-- 代码生成工具 -->
            <dependency>
                <groupId>ins.framework</groupId>
                <artifactId>ins-framework-mybatis-generator</artifactId>
                <version>${ins-framework.version}</version>
            </dependency>

            <!-- tk -->
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>2.0.4</version>
            </dependency>

            <!-- pagehelper -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.2.10</version>
            </dependency>

            <!-- okhttp -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>3.12.1</version>
            </dependency>

            <!-- commons -->
            <dependency>
                <groupId>ma.glasnost.orika</groupId>
                <artifactId>orika-core</artifactId>
                <version>1.5.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 设定除中央仓库(repo1.maven.org/maven2/)外的其他仓库,按设定顺序进行查找. -->
    <repositories>
        <!-- 如有Nexus私服, 取消注释并指向正确的服务器地址. -->
        <repository>
            <id>jsptz-nexus</id>
            <name>Team
                Nexus Repository</name>
            <url>http://repo.jsptz.com/nexus/content/groups/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <!-- 设定使用Release插件发布的仓库服务器 如有Nexus私服, 取消注释并指向正确的服务器地址. -->
    <distributionManagement>
        <repository>
            <id>jsptz-nexus</id>
            <name>Team Nexus Release Repository</name>
            <url>http://repo.jsptz.com/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>jsptz-nexus-snapshot</id>
            <name>Team Nexus Snapshot Repository</name>
            <url>http://repo.jsptz.com/nexus/content/repositories/snapshots</url>
            <uniqueVersion>false</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

    <!--<repositories>
        <repository>
            <id>azjd-central</id>
            <name>azjd-central</name>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <url>http://nexus.azcn.com/repository/fchx-group/</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>azjd-plugin</id>
            <name>azjd-plugin</name>
            <url>http://nexus.azcn.com/repository/fchx-group/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    -->
    <!-- 插件配置 -->
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <addMavenDescriptor>true</addMavenDescriptor>
                        <index>true</index>
                        <manifest>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>${skip_maven_test}</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>${skip_maven_deploy}</skip>
                </configuration>
            </plugin>
            <!-- install插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>${skip_maven_install}</skip>
                </configuration>
            </plugin>
            <!-- git 流程管理 -->
            <plugin>
                <groupId>com.amashchenko.maven.plugin</groupId>
                <artifactId>gitflow-maven-plugin</artifactId>
                <version>1.11.0</version>
                <configuration>
                    <installProject>true</installProject>
                    <verbose>true</verbose>
                    <keepBranch>true</keepBranch>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>